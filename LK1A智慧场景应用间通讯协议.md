# 协议修改记录

| 版本   | 日期        | 编写者     | 变更内容                         |
|------|-----------|---------|------------------------------|
| V1.0 | 2024-3-14 | 朱时伟     | 初版                           |
| V1.1 | 2024-8-22 | 徐丽丽，钟文祥 | 新增场景魔方打开智慧场景编辑界面10007        |
| V1.2 | 2024-11-5 | 饶昱琳     | 新增10007返回参数：条件匹配情况，动作匹配情况    |
| V1.3 | 2025-3-25 | 徐丽丽     | 新增10002返回参数originName原始场景名   |
| V1.4 | 2025-3-26 | 徐丽丽，钟文祥 | 新增10008通过语义中间层转换，并打开智慧场景编辑界面 |

[TOC]

# 1 通用参数

* 包名：com.dfl.smartscene
* 服务Action名：com.dfl.smartscene.actionProtocol

## 1.1通用请求参数

```json
{
  "protocolId": 10001,
  "requestAuthor": "com.dfl.xxxx",
  "messageType": "dispatch",
  "requestCode": "10001",
  "versionName": "V0.7.0",
  "data": {}
}
```

| 参数名           | 类型     | 说明                                                  |
|---------------|--------|-----------------------------------------------------|
| protocolId    | Int    | 具体的协议ID                                             |
| requestAuthor | String | 发起请求的应用的包名                                          |
| messageType   | string | 消息类型：request:请求的消息response:响应的消息dispatch:监听并主动下发的消息 |
| requestCode   | string | 请求码，用于区分同一接口多次调用                                    |
| versionName   | string | 请求端应用版本号                                            |
| data          | object | 自定义数据类型                                             |

## 1.2通用响应参数

```json
{
  "protocolId": 10001,
  "requestAuthor": "com.dfl.xxxx",
  "messageType": "dispatch",
  "requestCode": "10001",
  "versionName": "V0.7.0",
  "data": {
    "result": 0
  }
}
```

| 参数名           | 类型     | 说明                                                  |
|---------------|--------|-----------------------------------------------------|
| protocolId    | Int    | 具体的协议ID                                             |
| requestAuthor | String | 发起请求的应用的包名                                          |
| messageType   | string | 消息类型：request:请求的消息response:响应的消息dispatch:监听并主动下发的消息 |
| requestCode   | string | 请求码，用于区分同一接口多次调用                                    |
| versionName   | string | 请求端应用版本号                                            |
| data          | object | 自定义数据类型                                             |
| result        | int    | 执行结果，0成功，1失败                                        |

# 2 场景变更通知-10001

* protocolId:**10001**

* 类型:**dispatch**
* 功能描述：新增场景,修改场景,删除场景会通知变更
* 请求参数说明：参考通用请求参数
* 返回参数说明：参考通用响应参数

# 3 获取场景名称列表-10002

* protocolId:**10002**
* 类型:**request**

* 功能描述：获取用户场景名称和场景id列表

* 请求参数说明：参考通用请求参数

* 返回参数说明：

  | 参数字段   | 类型       | 说明               |
        | ---------- | ---------- | ------------------ |
  | sceneId    | String     | 场景id             |
  | nameList   | String列表 | 语言需要中文场景名 |
  | originName | String     | 原始场景名         |

* 返回参数示例：

  ```json
  {
    "data": [
  		{"sceneId":"123","nameList":["场景十二","场景一二"],"originName":"场景12"},  
  		{"sceneId":"456","nameList":["hij","hij"],"originName":"hij"}
   ]
  }
  ```

# 4 执行场景-10003

* protocolId:**10003**

* 类型:**request**

* 功能描述：根据场景id执行场景,场景id通过10002获取；有返回时表示场景开始执行

* 请求参数说明：

  | 参数字段 | 类型   | 说明   |
        | -------- | ------ | ------ |
  | sceneId  | String | 场景id |

* 请求参数示例

  ```json
  {
   "data": {
     "sceneId":"123"
   } 
  }
  ```

* 响应参数说明：参考通用响应

# 5 获取场景列表数据信息-10004

* protocolId:**10004**
* 类型:**dispatch**
* 功能描述：下发自动执行场景的数量与手动执行场景的数量，两个数据

* 请求参数说明：参考通用请求

* 响应参数说明：

  | 参数字段    | 类型 | 说明             |
        | ----------- | ---- | ---------------- |
  | autoCount   | int  | 自动执行场景数量 |
  | manualCount | int  | 手动执行场景数量 |

* 响应参数示例：

  ```json
  {
    "data": {
  		"autoCount":1,
  		"manualCount":2
  	}
  }
  ```

# 6 控制场景自动执行的监听-10005

* protocolId:**10005**

* 类型:**request**

* 功能描述：打开或关闭所有场景自动执行

* 请求参数说明

  | 参数字段 | 类型 | 说明                                             |
        | -------- | ---- | ------------------------------------------------ |
  | state    | int  | 0表示关闭场景自动执行监听，1表示打开场景自动监听 |

* 请求参数示例：

  ```json
  {
    "data": {
  		"state":0 
    }
  }
  ```

* 响应参数说明：参考通用响应

# 7 试运行场景-10006

* protocolId:**10006**

* 类型:**request**

* 功能描述：将试运行场景的JSON数据发送过来执行

* 请求参数说明

  | 参数字段  | 类型   | 说明                         |
        | --------- | ------ | ---------------------------- |
  | sceneJson | string | 场景的JSON,具体属性见7.1说明 |

* 请求参数示例

  ```json
  {
    "sceneJson":{
      "autoExeFlag": 0,
      "conditions": [{
        "category": "全车门锁",
        "desc": "解锁",
        "id": 0,
        "input": [{
          "desc": null,
          "name": "status",
          "type": "INT32",
          "value": "0"
        }],
        "skillId": 1538
      }],
      "edgeCondition": {
        "category": "时间点",
        "desc": "09:00",
        "id": 0,
        "input": [{
          "desc": null,
          "name": "hour",
          "type": "INT32",
          "value": "9"
        }, {
          "desc": null,
          "name": "minute",
          "type": "INT32",
          "value": "0"
        }, {
          "desc": null,
          "name": "second",
          "type": "INT32",
          "value": "0"
        }],
        "skillId": 65286
      },
      "executeFrequency": 0,
      "imgPath": "",
      "scenarioDesc": "",
      "scenarioId": "VEHICLE_1743132377319",
      "scenarioName": "场景三",
      "scenarioTimeStamp": 943891682400,
      "secondAskeFlag": 1,
      "sequence": [{
        "action": {
          "category": "空调温度",
          "desc": " 20℃",
          "input": [{
            "desc": null,
            "name": "temp",
            "type": "DOUBLE",
            "value": "20.0"
          }],
          "skillId": 258
        },
        "delay": 0,
        "id": 0,
        "type": 1
      }],
      "version": 66
    }
  }
  ```

* 返回参数说明：

  | 参数字段 | 类型 | 说明                                                         |
        | -------- | ---- | ------------------------------------------------------------ |
  | result   | int  | 0->调用场景成功，1->调用场景失败，2->开始执行场景，3->场景执行结束(无论场景动作成功失败都会结束) |

* 返回参数示例

  ```json
  {
   "data":{
      "result":0
    } 
  }
  ```

## 7.1请求参数sceneJson

```plaintext
public class ScenarioInfo{
private String scenarioId;
private String scenarioName;
private String scenarioDesc;
private int version;
private int autoExeFlag;
private int secondAskeFlag;
private long scenarioTimeStamp;
private List<ScenarioInfo.Sequence> sequence;
}

public static class Sequence{
        private int id;
        private int type;
        private int delay;
        private ScenarioInfo.Action action;
        private int id;
        private int type;
        private int delay;
        private ScenarioInfo.Action action;
}
public static class Action {
        private String desc;
        private int skillId;
        private String category;
        private List<InputArgInfo> input;
}
public class InputArgInfo {
    private String name;
    private ArgType type;
    private String value;
    private String desc;
}
public enum ArgType{
    UNUSED(0),
    UINT8(1),
    UINT16(2),
    UINT32(3),
    UINT64(4),
    INT8(5),
    INT16(6),
    INT32(7),
    INT64(8),
    FLOAT(9),
    DOUBLE(10),
    STRING(11);
}
```

# 8 打开智慧场景编辑界面-10007

* protocolId:**10007**

* 类型:**request**

* 功能说明：有返回时表示智慧场景打开了编辑界面。会校验skillId是否可用，ScenarioInfo数据格式是否符合。

* 请求参数说明

  | 参数字段         | 类型   | 说明                         |
        | ---------------- | ------ | ---------------------------- |
  | scenarioInfoJson | string | 场景的JSON,具体属性见7.1说明 |

* 请求参数示例

  ```json
  {
    "scenarioInfoJson":{
      "autoExeFlag": 0,
      "conditions": [
          {
              "category": "空气质量",
              "desc": "差",
              "id": 0,
              "input": [
                  {
                      "desc": null,
                      "name": "status",
                      "type": "INT32",
                      "value": "0"
                  }
              ],
              "skillId": 89
          }
      ],
      "edgeCondition": {
          "category": "时间点",
          "desc": "09:00",
          "id": 0,
          "input": [
              {
                  "desc": "",
                  "name": "hour",
                  "type": "INT32",
                  "value": "09"
              },
              {
                  "desc": "",
                  "name": "minute",
                  "type": "INT32",
                  "value": "00"
              },
              {
                  "desc": "",
                  "name": "second",
                  "type": "INT32",
                  "value": "0"
              }
          ],
          "skillId": 65286
      },
      "imgPath": "",
      "scenarioDesc": "",
      "scenarioId": "VEHICLE_1723825036319",
      "scenarioName": "场景一人人",
      "scenarioTimeStamp": 0,
      "secondAskeFlag": 1,
      "sequence": [
          {
              "action": {
                  "category": "播放多媒体",
                  "desc": "QQ音乐",
                  "input": [],
                  "skillId": 88
              },
              "delay": 0,
              "id": 0,
              "type": 1
          },
          {
              "action": {
                  "category": "关闭多媒体",
                  "desc": "关闭多媒体",
                  "input": [],
                  "skillId": 41
              },
              "delay": 0,
              "id": 1,
              "type": 1
          }
      ],
      "version": 30
  	}
  }
  ```

* 响应参数说明

  | 参数字段       | 类型 | 说明                                                     |
        | -------------- | ---- | -------------------------------------------------------- |
  | result         | int  | 0成功，1失败                                             |
  | conditionMatch | int  | -1其他(没有动作或条件时回传),0不支持,1部分支持,2全部支持 |
  | sequenceMatch  | int  | -1其他(没有动作或条件时回传),0不支持,1部分支持,2全部支持 |

* 响应参数示例

  ```json
  {
    "data": {
  	"result":0 
  	"conditionMatch":2 
  	"sequenceMatch":2 
  	}
  }
  ```

# 9 通过语义中间层转换，并打开智慧场景编辑界面-10008

* protocolId:**10008**

* 类型:**request**

* 功能说明：通过语义中间层转换校验，并打开智慧场景编辑界面。有返回时表示智慧场景打开了编辑界面。转换skillvalue成skillId，
  校验skillId是否可用，IMScenarioInfo数据格式是否符合。

* 请求参数说明：

  | 参数字段         | 类型   | 说明                                                  |
        | ---------------- | ------ | ----------------------------------------------------- |
  | scenarioInfoJson | string | 场景的语义IMScenarioInfo JSON字符串,具体属性见9.1说明 |

* 请求参数示例：

  ```json
  {
    "scenarioInfoJson":{
      {
        "scenarioId": "VEHICLE_1730771137560",
        "scenarioName": "高温自动空调",
        "version": 0,
        "autoExeFlag": 0,
        "secondAskeFlag": 0,
        "scenarioTimeStamp": 1730771137560,
        "executeFrequency": 0,
        "edgeCondition": {
          "skillValue": "condition.env.InsideTempNotifyGreater",
          "category": "车内温度大于",
          "desc": "30℃",
          "id": 0,
          "input": [{
            "name": "InsideTemp",
            "type": "DOUBLE",
            "value": "30"
          }],
          "params": null
        },
        "conditions": [{
          "skillValue": "condition.lock.CarLockStatus",
          "category": "全车门锁",
          "desc": "上锁",
          "id": 0,
          "input": [{
            "name": "status",
            "type": "INT32",
            "value": "1"
          }],
          "params": null
        }],
        "sequence": [{
          "id": 0,
          "type": 1,
          "action": {
            "skillValue": "action.ac.ACSwitch",
            "category": "空调开关",
            "desc": "打开",
            "input": [{
              "name": "hvacswstate",
              "type": "INT32",
              "value": "0"
            }],
            "params": null
          }
        }, {
          "id": 1,
          "type": 1,
          "action": {
            "skillValue": "action.ac.ACTemperature",
            "category": "空调温度",
            "desc": "%d℃",
            "input": [{
              "name": "temp",
              "type": "DOUBLE",
              "value": "26.5"
            }],
            "params": null
          }
        }, {
          "id": 2,
          "type": 1,
          "action": {
            "skillValue": "action.navi.NaviRequest",
            "category": "发起导航",
            "desc": "去%d",
            "input": [{
              "name": "json",
              "type": "STRING",
              "value": ""
            }],
            "params": [{
              "index": 0,
              "arg": "天安门"
            }, {
              "index": 1,
              "arg": "故宫"
            }]
          }
        }]
      }
    }
  }
  ```

* 响应参数说明

  | 参数字段       | 类型              | 说明                                                     |
        | -------------- | ----------------- | -------------------------------------------------------- |
  | result         | int               | 0成功，1失败                                             |
  | failReason     | string            | 服务失败的原因，服务成功时为空字符串                     |
  | conditionMatch | int               | -1其他(没有动作或条件时回传),0不支持,1部分支持,2全部支持 |
  | sequenceMatch  | int               | -1其他(没有动作或条件时回传),0不支持,1部分支持,2全部支持 |
  | resList        | List`<IMResItem>` | 每个能力对应的结果                                       |

  IMResItem参数说明

  | 参数字段   | 类型   | 说明                                 |
        | ---------- | ------ | ------------------------------------ |
  | skillValue | string | 传入的能力语义,根据语义表格          |
  | status     | int    | 能力转换后的状态 0成功，1失败，2超时 |
  | failReason | string | 转化失败的原因，当0成功时伟空字符串  |

* 响应参数示例

  ```json
  {
    "data": {
      "result":0,
      "failReason":"",
      "conditionMatch":0,
      "sequenceMatch":0,
      "resList":[{
        "skillValue":"action.navi.NaviRequest",
        "status":1,
        "failReason":"请求超时"
      }]
    }
  }
  ```

## 9.1 10008相关数据实体

```kotlin
data class IMScenarioInfo(
    /**场景id格式，VEHICLE_+时间戳System.currentTimeMillis()*/
    private var scenarioId: String? = null,
    /**场景名称*/
    private var scenarioName: String? = null,
    /**场景更新次数，初始0修改+1*/
    private var version: Int = 0,
    /**自动执行,0自动执行，1不自动执行*/
    private var autoExeFlag: Int = 0,
    /**自动执行前询问，0询问，1不询问*/
    private var secondAskeFlag: Int = 0,
    /**场景更新时间戳*/
    private var scenarioTimeStamp: Long = 0,
    /**自动执行前频率，2a新字段*/
    private var executeFrequency: Int = 0,
    /**触发条件*/
    private var edgeCondition: IMCondition? = null,
    /**状态条件列表*/
    private var conditions: List<IMCondition>? = null,
    /**动作列表*/
    private var sequence: List<IMSequence>? = null
)
data class IMCondition(
    /**能力语义*/
    private var skillValue: String? = null,
    /**能力类别描述，对应ui标题*/
    private var category: String? = null,
    /**能力描述，对应ui副标题*/
    private var desc: String? = null,
    /**能力顺序，从0开始*/
    private var id: Int = 0,
    /**能力参数列表，没有参数要传空列表*/
    private var input: List<IMInputArgInfo> = emptyList(),
    /**输入中间层额外参数*/
    private var params: List<IMParams>? = null
)

data class IMInputArgInfo(
    /**参数名*/
    private var name: String? = null,
    /**参数值类别*/
    private var type: IMArgType? = null,
    /**参数值*/
    private var value: String? = null
)

data class IMSequence(
    /**动作排序*/
    private var id: Int = 0,
    /**ccm预留参数，填1*/
    private var type: Int = 1,
    /**动作类*/
    private var action: IMAction? = null
)

data class IMAction(
    /**能力语义*/
    private var skillValue: String? = null,
    /**能力类别描述，对应ui标题*/
    private var category: String? = null,
    /**能力描述，对应ui副标题*/
    private var desc: String? = null,
    /**能力参数列表，没有参数要传空列表*/
    private var input: List<IMInputArgInfo> = emptyList(),
    /**输入中间层额外参数*/
    private var params: List<IMParams>? = null
)

/**第三方协议需要用到的参数*/
data class IMParams(
    /**第几个参数 0,1,2,3,4...，按顺序*/
    private var index: Int = 0,
    /**参数值*/
    private var arg: String? = null
)

enum class IMArgType {
    UNUSED(0),
    UINT8(1),
    UINT16(2),
    UINT32(3),
    UINT64(4),
    INT8(5),
    INT16(6),
    INT32(7),
    INT64(8),
    FLOAT(9),
    DOUBLE(10),
    STRING(11);
}
```

