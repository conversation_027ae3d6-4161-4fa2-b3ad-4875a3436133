/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 *
 *  * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 *  * All Rights Reserved.
 *  *
 *  * Redistribution and use in source and binary forms, with or without
 *  * modification, are NOT permitted except as agreed by
 *  * iAuto (Shanghai) Co., Ltd.
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 *
 * @file msgcenter_manifest.cpp
 * @brief msgcenter_manifest.cpp
 *
 *
 */

package ara.ivi.msgcenter.ics.msgcenter.v1;
import ics.com.runtime.ComTransportManager;
import ics.com.runtime.ComTransportManifest;

public class msgcenter_manifest {
    public msgcenter_manifest(){}

    public static void OnInit(){
        ComTransportManifest manifest = new ComTransportManifest();
        manifest.serviceInfo = "msgcenter_1_0";
        manifest.e2eManifest = "";
        manifest.permissionManifest = "";
        manifest.events = new String[]{};
        manifest.fields = new String[]{};
        manifest.methods = new String[]{"showMessage"};
        manifest.bindingName = "isomeip";
        manifest.deploymentManifest ="{\n" +
        "    \"services\" :\n" +
        "    [\n" +
        "        {\n" +
        "            \"short_name\" : \"msgcenter_1_0\",\n" +
        "            \"service\" : 180,\n" +
        "            \"major_version\" : 1,\n" +
        "            \"minor_version\" : 0,\n" +
        "            \"events\":\n" +
        "            [\n" +
        "            ],\n" +
        "            \"fields\":\n" +
        "            [\n" +
        "            ],\n" +
        "            \"methods\":\n" +
        "            [\n" +
        "                {\n" +
        "                    \"short_name\": \"showMessage\",\n" +
        "                    \"method\": 8193,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                }\n" +
        "            ],\n" +
        "            \"eventgroups\":\n" +
        "            [\n" +
        "            ]\n" +
        "        }\n" +
        "    ],\n" +
        "    \"provided_instances\" :\n" +
        "    [\n" +
        "        {\n" +
        "            \"service\" : 180,\n" +
        "            \"major_version\" : 1,\n" +
        "            \"short_name\" : \"MsgCenter_1_SomeipProvided\",\n" +
        "            \"instance\" : 1,\n" +
        "            \"udp_port\" : 0,\n" +
        "            \"tcp_port\" : 30501,\n" +
        "            \"tcp_reuse\" : true,\n" +
        "            \"udp_reuse\" : false,\n" +
        "            \"method_attribute\" :\n" +
        "            [\n" +
        "            ],\n" +
        "            \"event_attribute\" :\n" +
        "            [\n" +
        "            ],\n" +
        "            \"offer_time_reference\" : \"MsgCenter_SomeipServerServiceInstanceConfig\",\n" +
        "            \"ethernet_reference\" : \"IVIunicastEndpoint\",\n" +
        "            \"tcp_tls_flag\" : false,\n" +
        "            \"udp_tls_flag\" : false,\n" +
        "            \"provided_eventgroups\" :\n" +
        "            [\n" +
        "            ]\n" +
        "        }\n" +
        "    ],\n" +
        "    \"required_instances\" :\n" +
        "    [\n" +
        "    ],\n" +
        "    \"sd_server_offer_times\" :\n" +
        "    [\n" +
        "        {\n" +
        "            \"sd_time_name\" : \"MsgCenter_SomeipServerServiceInstanceConfig\",\n" +
        "            \"initial_delay_min\" : 10,\n" +
        "            \"initial_delay_max\" : 50,\n" +
        "            \"repetitions_base_delay\" : 100,\n" +
        "            \"repetitions_max\" : 5,\n" +
        "            \"ttl\" : 3,\n" +
        "            \"cyclic_offer_delay\" : 2000,\n" +
        "            \"request_response_delay_min\" : 10,\n" +
        "            \"request_response_delay_max\" : 20\n" +
        "        }\n" +
        "    ],\n" +
        "    \"sd_client_find_times\" :\n" +
        "    [\n" +
        "    ],\n" +
        "    \"sd_client_subscribe_times\" :\n" +
        "    [\n" +
        "    ],\n" +
        "    \"sd_server_subscribe_times\" :\n" +
        "    [\n" +
        "    ],\n" +
        "    \"network_endpoints\" :\n" +
        "    [\n" +
        "        {\n" +
        "            \"network_id\" : \"IVIunicastEndpoint\",\n" +
        "            \"ip_addr\" : \"************\",\n" +
        "            \"ip_addr_type\" : 4,\n" +
        "            \"subnet_mask\" : \"*************\",\n" +
        "            \"ttl\" : 3,\n" +
        "            \"multicast_ip_addr\" : \"**********\",\n" +
        "            \"multicast_port\" : 30490\n" +
        "        }\n" +
        "      ]\n" +
        "  }";

        ComTransportManager.instance().loadManifest("msgcenter_1_0",manifest);
    }
}
