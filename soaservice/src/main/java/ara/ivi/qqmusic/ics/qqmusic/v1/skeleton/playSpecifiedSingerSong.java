/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 *
 *  * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 *  * All Rights Reserved.
 *  *
 *  * Redistribution and use in source and binary forms, with or without
 *  * modification, are NOT permitted except as agreed by
 *  * iAuto (Shanghai) Co., Ltd.
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 *
 * @file playSpecifiedSingerSong.java
 * @brief playSpecifiedSingerSong.java
 *
 *
 */

package ara.ivi.qqmusic.ics.qqmusic.v1.skeleton;
import ics.com.runtime.Skeleton;
import ics.com.runtime.ErrorCode;
import ics.com.runtime.utils.concurrent.Future;
import ics.com.serialization.common.Structure;
import ics.com.serialization.common.Wrapper;
import ics.com.runtime.ComSkeletonInstance;
import ics.com.runtime.ComSkeletonRequestCallback;
import ics.com.runtime.SessionTag;
import ics.com.runtime.utils.FieldOrderAnnotation;
import ara.ivi.qqmusic.ics.qqmusic.v1.TransformPropsObj;

public class playSpecifiedSingerSong {
    private ComSkeletonInstance instance;
    private String name;
    private int idx;

    public playSpecifiedSingerSong(String name, int idx, ComSkeletonInstance ins) {
        this.name = name;
        this.idx = idx;
        this.instance = ins;
    }

    public void RegMethodProcess(playSpecifiedSingerSongHandle callback) {
        this.instance.setMethodRequestCallback(this.idx, new ComSkeletonRequestCallback() {
            @Override
            public void onCall(byte[] payload, SessionTag tag) {
                playSpecifiedSingerSongInput input = (playSpecifiedSingerSongInput) Skeleton.executeRRMethodAsyncRequest(idx, instance, payload, TransformPropsObj.instance().kDefaultSomeipTransformationProps, playSpecifiedSingerSongInput.class);
                Future<playSpecifiedSingerSongOutput> future =  callback.OnCall(input.singer, input.song);
                Skeleton.sendMethodResponse(future, idx, instance, TransformPropsObj.instance().kDefaultSomeipTransformationProps, tag);
            }

            @Override
            public void onError(ErrorCode errorCode, SessionTag tag) {
                //error handle
                callback.OnError(errorCode);
            }
        });
    }
    public static class playSpecifiedSingerSongOutput implements Structure {
        @FieldOrderAnnotation(order = 1)
        public Byte resultCode;// uint8_t data: please convert by function: Byte.toUnsignedInt or Byte.toUnsignedLong

        public playSpecifiedSingerSongOutput(Byte resultCode) {
            this.resultCode = resultCode;
        }
    }
    public static class playSpecifiedSingerSongInput implements Wrapper {
        @FieldOrderAnnotation(order = 1)
        public String singer;
        @FieldOrderAnnotation(order = 2)
        public String song;

        public playSpecifiedSingerSongInput(String singer, String song) {
            this.singer = singer;
            this.song = song;
        }
    }
    public interface playSpecifiedSingerSongHandle {
        public Future<playSpecifiedSingerSongOutput> OnCall(String singer, String song);
        public void OnError(ErrorCode errorCode);
    }
}

