/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 *
 *  * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 *  * All Rights Reserved.
 *  *
 *  * Redistribution and use in source and binary forms, with or without
 *  * modification, are NOT permitted except as agreed by
 *  * iAuto (Shanghai) Co., Ltd.
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 *
 * @file setSmartAromatherapyMachineFlavor.java
 * @brief setSmartAromatherapyMachineFlavor.java
 *
 *
 */

package ara.ivi.carctrl.ics.carctrl.v1.skeleton;
import ics.com.runtime.Skeleton;
import ics.com.runtime.ErrorCode;
import ics.com.serialization.common.Wrapper;
import ics.com.runtime.ComSkeletonInstance;
import ics.com.runtime.ComSkeletonRequestCallback;
import ics.com.runtime.SessionTag;
import ics.com.runtime.utils.FieldOrderAnnotation;
import ara.ivi.carctrl.ics.carctrl.v1.TransformPropsObj;

public class setSmartAromatherapyMachineFlavor {
    private ComSkeletonInstance instance;
    private String name;
    private int idx;

    public setSmartAromatherapyMachineFlavor(String name, int idx, ComSkeletonInstance ins) {
        this.name = name;
        this.idx = idx;
        this.instance = ins;
    }

    public void RegMethodProcess(setSmartAromatherapyMachineFlavorHandle callback) {
        this.instance.setMethodRequestCallback(this.idx, new ComSkeletonRequestCallback() {
            @Override
            public void onCall(byte[] payload, SessionTag tag) {
                setSmartAromatherapyMachineFlavorInput input = (setSmartAromatherapyMachineFlavorInput) Skeleton.executeRRMethodAsyncRequest(idx, instance, payload, TransformPropsObj.instance().kDefaultSomeipTransformationProps, setSmartAromatherapyMachineFlavorInput.class);
                callback.OnCall(input.flavor);
            }

            @Override
            public void onError(ErrorCode errorCode, SessionTag tag) {
                //error handle
                callback.OnError(errorCode);
            }
        });
    }
    public static class setSmartAromatherapyMachineFlavorInput implements Wrapper {
        @FieldOrderAnnotation(order = 1)
        public Byte flavor;// uint8_t data: please convert by function: Byte.toUnsignedInt or Byte.toUnsignedLong

        public setSmartAromatherapyMachineFlavorInput(Byte flavor) {
            this.flavor = flavor;
        }
    }
    public interface setSmartAromatherapyMachineFlavorHandle {
        public void OnCall(Byte flavor);
        public void OnError(ErrorCode errorCode);
    }
}

