/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 *
 *  * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 *  * All Rights Reserved.
 *  *
 *  * Redistribution and use in source and binary forms, with or without
 *  * modification, are NOT permitted except as agreed by
 *  * iAuto (Shanghai) Co., Ltd.
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 *
 * @file setSmartAtmosphereLightBrightness.java
 * @brief setSmartAtmosphereLightBrightness.java
 *
 *
 */

package ara.ivi.carctrl.ics.carctrl.v1.skeleton;
import ics.com.runtime.Skeleton;
import ics.com.runtime.ErrorCode;
import ics.com.serialization.common.Wrapper;
import ics.com.runtime.ComSkeletonInstance;
import ics.com.runtime.ComSkeletonRequestCallback;
import ics.com.runtime.SessionTag;
import ics.com.runtime.utils.FieldOrderAnnotation;
import ara.ivi.carctrl.ics.carctrl.v1.TransformPropsObj;

public class setSmartAtmosphereLightBrightness {
    private ComSkeletonInstance instance;
    private String name;
    private int idx;

    public setSmartAtmosphereLightBrightness(String name, int idx, ComSkeletonInstance ins) {
        this.name = name;
        this.idx = idx;
        this.instance = ins;
    }

    public void RegMethodProcess(setSmartAtmosphereLightBrightnessHandle callback) {
        this.instance.setMethodRequestCallback(this.idx, new ComSkeletonRequestCallback() {
            @Override
            public void onCall(byte[] payload, SessionTag tag) {
                setSmartAtmosphereLightBrightnessInput input = (setSmartAtmosphereLightBrightnessInput) Skeleton.executeRRMethodAsyncRequest(idx, instance, payload, TransformPropsObj.instance().kDefaultSomeipTransformationProps, setSmartAtmosphereLightBrightnessInput.class);
                callback.OnCall(input.brightness);
            }

            @Override
            public void onError(ErrorCode errorCode, SessionTag tag) {
                //error handle
                callback.OnError(errorCode);
            }
        });
    }
    public static class setSmartAtmosphereLightBrightnessInput implements Wrapper {
        @FieldOrderAnnotation(order = 1)
        public Byte brightness;// uint8_t data: please convert by function: Byte.toUnsignedInt or Byte.toUnsignedLong

        public setSmartAtmosphereLightBrightnessInput(Byte brightness) {
            this.brightness = brightness;
        }
    }
    public interface setSmartAtmosphereLightBrightnessHandle {
        public void OnCall(Byte brightness);
        public void OnError(ErrorCode errorCode);
    }
}

