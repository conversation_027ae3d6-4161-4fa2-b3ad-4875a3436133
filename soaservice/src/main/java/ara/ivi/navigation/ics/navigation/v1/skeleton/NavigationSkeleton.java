/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 *
 *  * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 *  * All Rights Reserved.
 *  *
 *  * Redistribution and use in source and binary forms, with or without
 *  * modification, are NOT permitted except as agreed by
 *  * iAuto (Shanghai) Co., Ltd.
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 *
 * @file NavigationSkeleton.java
 * @brief NavigationSkeleton.java
 *
 *
 */

package ara.ivi.navigation.ics.navigation.v1.skeleton;
import android.os.Looper;
import ics.com.runtime.ComSkeletonInstance;
import ics.com.runtime.ComSkeletonStateChangedCallback;
import ics.com.runtime.InstanceHandle;

public class NavigationSkeleton {
    private ComSkeletonInstance instance;
    public ara.ivi.navigation.ics.navigation.v1.skeleton.getSpeedLimit getSpeedLimit;
    public ara.ivi.navigation.ics.navigation.v1.skeleton.getlaneLine getlaneLine;
    public ara.ivi.navigation.ics.navigation.v1.skeleton.getlaneLevel getlaneLevel;
    public ara.ivi.navigation.ics.navigation.v1.skeleton.getNviInfo getNviInfo;
    public ara.ivi.navigation.ics.navigation.v1.skeleton.onNviToFavorite onNviToFavorite;
    public ara.ivi.navigation.ics.navigation.v1.skeleton.setRouteStrategy setRouteStrategy;
    public ara.ivi.navigation.ics.navigation.v1.skeleton.requestNavi requestNavi;
    public ara.ivi.navigation.ics.navigation.v1.skeleton.onNviToCharger onNviToCharger;
    public ara.ivi.navigation.ics.navigation.v1.skeleton.requestNaviWithRoutePoint requestNaviWithRoutePoint;

    public NavigationSkeleton(String instanceId, Looper loop) {
        this.instance = new ComSkeletonInstance(new InstanceHandle("navigation_1_0", instanceId), loop);
        this.getSpeedLimit = new ara.ivi.navigation.ics.navigation.v1.skeleton.getSpeedLimit("getSpeedLimit", this.instance.getMethodIndex("getSpeedLimit"), this.instance);
        this.getlaneLine = new ara.ivi.navigation.ics.navigation.v1.skeleton.getlaneLine("getlaneLine", this.instance.getMethodIndex("getlaneLine"), this.instance);
        this.getlaneLevel = new ara.ivi.navigation.ics.navigation.v1.skeleton.getlaneLevel("getlaneLevel", this.instance.getMethodIndex("getlaneLevel"), this.instance);
        this.getNviInfo = new ara.ivi.navigation.ics.navigation.v1.skeleton.getNviInfo("getNviInfo", this.instance.getMethodIndex("getNviInfo"), this.instance);
        this.onNviToFavorite = new ara.ivi.navigation.ics.navigation.v1.skeleton.onNviToFavorite("onNviToFavorite", this.instance.getMethodIndex("onNviToFavorite"), this.instance);
        this.setRouteStrategy = new ara.ivi.navigation.ics.navigation.v1.skeleton.setRouteStrategy("setRouteStrategy", this.instance.getMethodIndex("setRouteStrategy"), this.instance);
        this.requestNavi = new ara.ivi.navigation.ics.navigation.v1.skeleton.requestNavi("requestNavi", this.instance.getMethodIndex("requestNavi"), this.instance);
        this.onNviToCharger = new ara.ivi.navigation.ics.navigation.v1.skeleton.onNviToCharger("onNviToCharger", this.instance.getMethodIndex("onNviToCharger"), this.instance);
        this.requestNaviWithRoutePoint = new ara.ivi.navigation.ics.navigation.v1.skeleton.requestNaviWithRoutePoint("requestNaviWithRoutePoint", this.instance.getMethodIndex("requestNaviWithRoutePoint"), this.instance);
    }

    // API OfferService
    public void OfferService() {
        this.instance.start();
    }

    // API StopOfferService
    public void StopOfferService() {
        this.instance.stop();
    }

    // API SetStateChangedCallback
    public void SetStateChangedCallback(ComSkeletonStateChangedCallback callback) {
        this.instance.setStateChangedCallback(callback);
    }
}

