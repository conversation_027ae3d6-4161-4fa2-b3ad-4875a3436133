/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 *
 *  * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 *  * All Rights Reserved.
 *  *
 *  * Redistribution and use in source and binary forms, with or without
 *  * modification, are NOT permitted except as agreed by
 *  * iAuto (Shanghai) Co., Ltd.
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 *
 * @file SoundEffectSkeleton.java
 * @brief SoundEffectSkeleton.java
 *
 *
 */

package ara.ivi.soundeffect.ics.soundeffect.v1.skeleton;
import android.os.Looper;
import ics.com.runtime.ComSkeletonInstance;
import ics.com.runtime.ComSkeletonStateChangedCallback;
import ics.com.runtime.InstanceHandle;

public class SoundEffectSkeleton {
    private ComSkeletonInstance instance;
    public ara.ivi.soundeffect.ics.soundeffect.v1.skeleton.setCustomSound setCustomSound;
    public ara.ivi.soundeffect.ics.soundeffect.v1.skeleton.setRoar setRoar;
    public ara.ivi.soundeffect.ics.soundeffect.v1.skeleton.setSoundTheme setSoundTheme;
    public ara.ivi.soundeffect.ics.soundeffect.v1.skeleton.setSoundBearing setSoundBearing;
    public ara.ivi.soundeffect.ics.soundeffect.v1.skeleton.playTricksterSound playTricksterSound;
    public ara.ivi.soundeffect.ics.soundeffect.v1.skeleton.playCarExteriorSound playCarExteriorSound;
    public ara.ivi.soundeffect.ics.soundeffect.v1.skeleton.setImmersiveSoundMode setImmersiveSoundMode;
    public ara.ivi.soundeffect.ics.soundeffect.v1.skeleton.setSoundWaveVolumeType setSoundWaveVolumeType;
    public ara.ivi.soundeffect.ics.soundeffect.v1.skeleton.setSoundWaveSwitch setSoundWaveSwitch;

    public SoundEffectSkeleton(String instanceId, Looper loop) {
        this.instance = new ComSkeletonInstance(new InstanceHandle("soundeffect_1_0", instanceId), loop);
        this.setCustomSound = new setCustomSound("setCustomSound", this.instance.getMethodIndex("setCustomSound"), this.instance);
        this.setRoar = new setRoar("setRoar", this.instance.getMethodIndex("setRoar"), this.instance);
        this.setSoundTheme = new setSoundTheme("setSoundTheme", this.instance.getMethodIndex("setSoundTheme"), this.instance);
        this.setSoundBearing = new setSoundBearing("setSoundBearing", this.instance.getMethodIndex("setSoundBearing"), this.instance);
        this.playTricksterSound = new playTricksterSound("playTricksterSound", this.instance.getMethodIndex("playTricksterSound"), this.instance);
        this.playCarExteriorSound = new playCarExteriorSound("playCarExteriorSound", this.instance.getMethodIndex("playCarExteriorSound"), this.instance);
        this.setImmersiveSoundMode = new setImmersiveSoundMode("setImmersiveSoundMode", this.instance.getMethodIndex("setImmersiveSoundMode"), this.instance);
        this.setSoundWaveVolumeType = new setSoundWaveVolumeType("setSoundWaveVolumeType", this.instance.getMethodIndex("setSoundWaveVolumeType"), this.instance);
        this.setSoundWaveSwitch = new setSoundWaveSwitch("setSoundWaveSwitch", this.instance.getMethodIndex("setSoundWaveSwitch"), this.instance);
    }

    // API OfferService
    public void OfferService() {
        this.instance.start();
    }

    // API StopOfferService
    public void StopOfferService() {
        this.instance.stop();
    }

    // API SetStateChangedCallback
    public void SetStateChangedCallback(ComSkeletonStateChangedCallback callback) {
        this.instance.setStateChangedCallback(callback);
    }
}

