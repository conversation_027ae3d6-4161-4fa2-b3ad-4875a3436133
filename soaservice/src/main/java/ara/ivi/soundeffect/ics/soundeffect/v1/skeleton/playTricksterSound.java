/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 *
 *  * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 *  * All Rights Reserved.
 *  *
 *  * Redistribution and use in source and binary forms, with or without
 *  * modification, are NOT permitted except as agreed by
 *  * iAuto (Shanghai) Co., Ltd.
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 *
 * @file playTricksterSound.java
 * @brief playTricksterSound.java
 *
 *
 */

package ara.ivi.soundeffect.ics.soundeffect.v1.skeleton;
import ics.com.runtime.Skeleton;
import ics.com.runtime.ErrorCode;
import ics.com.runtime.utils.concurrent.Future;
import ics.com.serialization.common.Structure;
import ics.com.serialization.common.Wrapper;
import ics.com.runtime.ComSkeletonInstance;
import ics.com.runtime.ComSkeletonRequestCallback;
import ics.com.runtime.SessionTag;
import ics.com.runtime.utils.FieldOrderAnnotation;
import ara.ivi.soundeffect.ics.soundeffect.v1.TransformPropsObj;

public class playTricksterSound {
    private ComSkeletonInstance instance;
    private String name;
    private int idx;

    public playTricksterSound(String name, int idx, ComSkeletonInstance ins) {
        this.name = name;
        this.idx = idx;
        this.instance = ins;
    }

    public void RegMethodProcess(playTricksterSoundHandle callback) {
        this.instance.setMethodRequestCallback(this.idx, new ComSkeletonRequestCallback() {
            @Override
            public void onCall(byte[] payload, SessionTag tag) {
                playTricksterSoundInput input = (playTricksterSoundInput) Skeleton.executeRRMethodAsyncRequest(idx, instance, payload, TransformPropsObj.instance().kDefaultSomeipTransformationProps, playTricksterSoundInput.class);
                callback.OnCall(input.position, input.sound);
            }

            @Override
            public void onError(ErrorCode errorCode, SessionTag tag) {
                //error handle
                callback.OnError(errorCode);
            }
        });
    }
    public static class playTricksterSoundInput implements Wrapper {
        @FieldOrderAnnotation(order = 1)
        public Byte position;// uint8_t data: please convert by function: Byte.toUnsignedInt or Byte.toUnsignedLong
        @FieldOrderAnnotation(order = 2)
        public Byte sound;// uint8_t data: please convert by function: Byte.toUnsignedInt or Byte.toUnsignedLong

        public playTricksterSoundInput(Byte position, Byte sound) {
            this.position = position;
            this.sound = sound;
        }
    }
    public interface playTricksterSoundHandle {
        public void OnCall(Byte position, Byte sound);
        public void OnError(ErrorCode errorCode);
    }
}

