/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 *
 *  * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 *  * All Rights Reserved.
 *  *
 *  * Redistribution and use in source and binary forms, with or without
 *  * modification, are NOT permitted except as agreed by
 *  * iAuto (Shanghai) Co., Ltd.
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 *
 * @file sysset_manifest.cpp
 * @brief sysset_manifest.cpp
 *
 *
 */

package ara.ivi.sysset.ics.sysset.v1;
import ics.com.runtime.ComTransportManager;
import ics.com.runtime.ComTransportManifest;

public class sysset_manifest {
    public sysset_manifest(){}

    public static void OnInit(){
        ComTransportManifest manifest = new ComTransportManifest();
        manifest.serviceInfo = "sysset_1_0";
        manifest.e2eManifest = "";
        manifest.permissionManifest = "";
        manifest.events = new String[]{"notifyDayStatus"};
        manifest.fields = new String[]{};
        manifest.methods = new String[]{"getBluetoothConnectionStatus","getBluetoothSwitchStatus","getHotspotConnectionStatus","getHotspotSwitchStatus","getWLANConnectionStatus","getWLANSwitchStatus","getMobileNetworkSwitchStatus","getMobileNetworkConnectionStatus","setBluetoothSwitchStatus","setDayAndNightMode","setHotspotSwitchStatus","setScreenBrightness","setThemeWallpaper","setVolume","setWLANSwitchStatus","setMobileNetworkSwitchStatus","setScreenText","setMuteStatus","setOutCarVolume"};
        manifest.bindingName = "isomeip";
        manifest.deploymentManifest = "{\n" +
                "    \"services\" :\n" +
                "    [\n" +
                "        {\n" +
                "            \"short_name\" : \"sysset_1_0\",\n" +
                "            \"service\" : 166,\n" +
                "            \"major_version\" : 1,\n" +
                "            \"minor_version\" : 0,\n" +
                "            \"events\":\n" +
                "            [\n" +
                "                {\n" +
                "                    \"short_name\" : \"notifyDayStatus\",\n" +
                "                    \"event\" : 1,\n" +
                "                    \"communication_type\" : \"TCP\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"fields\":\n" +
                "            [\n" +
                "            ],\n" +
                "            \"methods\":\n" +
                "            [\n" +
                "                {\n" +
                "                    \"short_name\": \"getBluetoothConnectionStatus\",\n" +
                "                    \"method\": 1,\n" +
                "                    \"fire_and_forget\" : false,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"getBluetoothSwitchStatus\",\n" +
        "                    \"method\": 2,\n" +
        "                    \"fire_and_forget\" : false,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"getHotspotConnectionStatus\",\n" +
        "                    \"method\": 3,\n" +
        "                    \"fire_and_forget\" : false,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"getHotspotSwitchStatus\",\n" +
        "                    \"method\": 4,\n" +
        "                    \"fire_and_forget\" : false,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"getWLANConnectionStatus\",\n" +
        "                    \"method\": 7,\n" +
        "                    \"fire_and_forget\" : false,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"getWLANSwitchStatus\",\n" +
        "                    \"method\": 8,\n" +
        "                    \"fire_and_forget\" : false,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"getMobileNetworkSwitchStatus\",\n" +
        "                    \"method\": 9,\n" +
        "                    \"fire_and_forget\" : false,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"getMobileNetworkConnectionStatus\",\n" +
        "                    \"method\": 16,\n" +
        "                    \"fire_and_forget\" : false,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setBluetoothSwitchStatus\",\n" +
        "                    \"method\": 8193,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setDayAndNightMode\",\n" +
        "                    \"method\": 8194,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setHotspotSwitchStatus\",\n" +
        "                    \"method\": 8195,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setScreenBrightness\",\n" +
        "                    \"method\": 8196,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setThemeWallpaper\",\n" +
        "                    \"method\": 8198,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setVolume\",\n" +
        "                    \"method\": 8199,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setWLANSwitchStatus\",\n" +
        "                    \"method\": 8200,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setMobileNetworkSwitchStatus\",\n" +
        "                    \"method\": 8201,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setScreenText\",\n" +
        "                    \"method\": 8210,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setMuteStatus\",\n" +
        "                    \"method\": 8211,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"short_name\": \"setOutCarVolume\",\n" +
                "                    \"method\": 8212,\n" +
                "                    \"fire_and_forget\" : true,\n" +
                "                    \"communication_type\" : \"TCP\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"eventgroups\":\n" +
                "            [\n" +
                "                {\n" +
                "                    \"short_name\" : \"SysSetEG\",\n" +
                "                    \"eventgroup\" : 1,\n" +
                "                    \"events\" : [1]\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ],\n" +
                "    \"provided_instances\" :\n" +
                "    [\n" +
                "        {\n" +
                "            \"service\" : 166,\n" +
                "            \"major_version\" : 1,\n" +
                "            \"short_name\" : \"SysSet_1_SomeipProvided\",\n" +
                "            \"instance\" : 1,\n" +
        "            \"udp_port\" : 0,\n" +
        "            \"tcp_port\" : 30501,\n" +
        "            \"tcp_reuse\" : true,\n" +
        "            \"udp_reuse\" : false,\n" +
        "            \"method_attribute\" :\n" +
        "            [\n" +
                "            ],\n" +
                "            \"event_attribute\" :\n" +
                "            [\n" +
                "            ],\n" +
                "            \"offer_time_reference\" : \"SysSet_SomeipServerServiceInstanceConfig\",\n" +
                "            \"ethernet_reference\" : \"IVIunicastEndpoint\",\n" +
                "            \"tcp_tls_flag\" : false,\n" +
                "            \"udp_tls_flag\" : false,\n" +
                "            \"provided_eventgroups\" :\n" +
                "            [\n" +
                "                {\n" +
                "                    \"eventgroup\" : 1,\n" +
                "                    \"multicast_addr_ip4\" : \"**********\",\n" +
                "                    \"multicast_port\" : 30491,\n" +
                "                    \"threshold\" : 0\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ],\n" +
                "    \"required_instances\" :\n" +
                "    [\n" +
                "    ],\n" +
                "    \"sd_server_offer_times\" :\n" +
                "    [\n" +
                "        {\n" +
                "            \"sd_time_name\" : \"SysSet_SomeipServerServiceInstanceConfig\",\n" +
        "            \"initial_delay_min\" : 10,\n" +
        "            \"initial_delay_max\" : 50,\n" +
        "            \"repetitions_base_delay\" : 100,\n" +
        "            \"repetitions_max\" : 5,\n" +
        "            \"ttl\" : 3,\n" +
        "            \"cyclic_offer_delay\" : 2000,\n" +
        "            \"request_response_delay_min\" : 10,\n" +
        "            \"request_response_delay_max\" : 20\n" +
        "        }\n" +
        "    ],\n" +
        "    \"sd_client_find_times\" :\n" +
        "    [\n" +
        "    ],\n" +
        "    \"sd_client_subscribe_times\" :\n" +
        "    [\n" +
        "    ],\n" +
        "    \"sd_server_subscribe_times\" :\n" +
        "    [\n" +
        "    ],\n" +
        "    \"network_endpoints\" :\n" +
        "    [\n" +
        "        {\n" +
        "            \"network_id\" : \"IVIunicastEndpoint\",\n" +
        "            \"ip_addr\" : \"************\",\n" +
        "            \"ip_addr_type\" : 4,\n" +
        "            \"subnet_mask\" : \"*************\",\n" +
        "            \"ttl\" : 3,\n" +
        "            \"multicast_ip_addr\" : \"**********\",\n" +
        "            \"multicast_port\" : 30490\n" +
        "        }\n" +
        "      ]\n" +
        "  }";

        ComTransportManager.instance().loadManifest("sysset_1_0",manifest);
    }
}
