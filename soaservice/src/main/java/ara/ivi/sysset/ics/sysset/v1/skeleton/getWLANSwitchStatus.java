/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 *
 *  * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 *  * All Rights Reserved.
 *  *
 *  * Redistribution and use in source and binary forms, with or without
 *  * modification, are NOT permitted except as agreed by
 *  * iAuto (Shanghai) Co., Ltd.
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 *
 * @file getWLANSwitchStatus.java
 * @brief getWLANSwitchStatus.java
 *
 *
 */

package ara.ivi.sysset.ics.sysset.v1.skeleton;
import ics.com.runtime.Skeleton;
import ics.com.runtime.ErrorCode;
import ics.com.runtime.utils.concurrent.Future;
import ics.com.serialization.common.Structure;
import ics.com.runtime.ComSkeletonInstance;
import ics.com.runtime.ComSkeletonRequestCallback;
import ics.com.runtime.SessionTag;
import ics.com.runtime.utils.FieldOrderAnnotation;
import ara.ivi.sysset.ics.sysset.v1.TransformPropsObj;

public class getWLANSwitchStatus {
    private ComSkeletonInstance instance;
    private String name;
    private int idx;

    public getWLANSwitchStatus(String name, int idx, ComSkeletonInstance ins) {
        this.name = name;
        this.idx = idx;
        this.instance = ins;
    }

    public void RegMethodProcess(getWLANSwitchStatusHandle callback) {
        this.instance.setMethodRequestCallback(this.idx, new ComSkeletonRequestCallback() {
            @Override
            public void onCall(byte[] payload, SessionTag tag) {
                Future<getWLANSwitchStatusOutput> future =  callback.OnCall();
                Skeleton.sendMethodResponse(future, idx, instance, TransformPropsObj.instance().kDefaultSomeipTransformationProps, tag);
            }

            @Override
            public void onError(ErrorCode errorCode, SessionTag tag) {
                //error handle
                callback.OnError(errorCode);
            }
        });
    }
    public static class getWLANSwitchStatusOutput implements Structure {
        @FieldOrderAnnotation(order = 1)
        public ara.ivi.sysset.ics.SwitchStatus switchStatus;

        public getWLANSwitchStatusOutput(ara.ivi.sysset.ics.SwitchStatus switchStatus) {
            this.switchStatus = switchStatus;
        }
    }
    public interface getWLANSwitchStatusHandle {
        public Future<getWLANSwitchStatusOutput> OnCall();
        public void OnError(ErrorCode errorCode);
    }
}

