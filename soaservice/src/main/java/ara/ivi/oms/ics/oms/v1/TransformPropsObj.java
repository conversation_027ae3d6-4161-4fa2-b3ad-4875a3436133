/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 *
 *  * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 *  * All Rights Reserved.
 *  *
 *  * Redistribution and use in source and binary forms, with or without
 *  * modification, are NOT permitted except as agreed by
 *  * iAuto (Shanghai) Co., Ltd.
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 *
 * @file TransformPropsObj.java
 * @brief TransformPropsObj.java
 *
 *
 */

package ara.ivi.oms.ics.oms.v1;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import ics.com.serialization.api.TransformProperty;
import ics.com.serialization.common.Alignment;
import ics.com.serialization.common.LengthFieldSize;
import ics.com.serialization.common.UnionTypeFieldSize;
import ics.com.serialization.someip.SomeIPTransformProperty;

public class TransformPropsObj {
    private static TransformPropsObj instance = new TransformPropsObj();

    public TransformProperty kDefaultSomeipTransformationProps;

    public static TransformPropsObj instance() {
        return instance;
    }

    public TransformPropsObj() {
        this.kDefaultSomeipTransformationProps = new SomeIPTransformProperty();
        this.kDefaultSomeipTransformationProps.setAlignment(Alignment.ALIGNMENT_8BIT);
        this.kDefaultSomeipTransformationProps.setByteOrder(ByteOrder.BIG_ENDIAN);
        this.kDefaultSomeipTransformationProps.setImplementsLegacyString(false);
        this.kDefaultSomeipTransformationProps.setDynamicLengthFieldSize(false);
        this.kDefaultSomeipTransformationProps.setSessionHandling(false);
        this.kDefaultSomeipTransformationProps.setArrayLengthFieldSize(LengthFieldSize.LENGTH_FIELD_SIZE_32BIT);
        this.kDefaultSomeipTransformationProps.setStringLengthFieldSize(LengthFieldSize.LENGTH_FIELD_SIZE_32BIT);
        this.kDefaultSomeipTransformationProps.setStructLengthFieldSize(LengthFieldSize.LENGTH_FIELD_SIZE_0BIT);
        this.kDefaultSomeipTransformationProps.setUnionLengthFieldSize(LengthFieldSize.LENGTH_FIELD_SIZE_32BIT);
        this.kDefaultSomeipTransformationProps.setUnionTypeFieldSize(UnionTypeFieldSize.UNION_TYPE_FIELD_SIZE_32BIT);
        this.kDefaultSomeipTransformationProps.setStringEncoding(StandardCharsets.UTF_8);
    }
}
