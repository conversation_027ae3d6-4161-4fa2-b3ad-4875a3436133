/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 *
 *  * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 *  * All Rights Reserved.
 *  *
 *  * Redistribution and use in source and binary forms, with or without
 *  * modification, are NOT permitted except as agreed by
 *  * iAuto (Shanghai) Co., Ltd.
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 *
 * @file OMSSkeleton.java
 * @brief OMSSkeleton.java
 *
 *
 */

package ara.ivi.oms.ics.oms.v1.skeleton;
import android.os.Looper;
import ics.com.runtime.ComSkeletonInstance;
import ics.com.runtime.ComSkeletonStateChangedCallback;
import ics.com.runtime.InstanceHandle;

public class OMSSkeleton {
    private ComSkeletonInstance instance;
    public ara.ivi.oms.ics.oms.v1.skeleton.notifyIfExactSeatExistChild notifyIfExactSeatExistChild;
    public ara.ivi.oms.ics.oms.v1.skeleton.notifyIfExactSeatExistPassenger notifyIfExactSeatExistPassenger;
    public ara.ivi.oms.ics.oms.v1.skeleton.notifyIfExactPassengerGender notifyIfExactPassengerGender;
    public ara.ivi.oms.ics.oms.v1.skeleton.notifyExactSeatPassengerBehavior notifyExactSeatPassengerBehavior;
    public ara.ivi.oms.ics.oms.v1.skeleton.checkIfExactSeatExistChild checkIfExactSeatExistChild;
    public ara.ivi.oms.ics.oms.v1.skeleton.checkIfExactSeatExistPassenger checkIfExactSeatExistPassenger;
    public ara.ivi.oms.ics.oms.v1.skeleton.checkExactPassengerGender checkExactPassengerGender;
    public ara.ivi.oms.ics.oms.v1.skeleton.checkExactSeatPassengerBehavior checkExactSeatPassengerBehavior;

    public OMSSkeleton(String instanceId, Looper loop) {
        this.instance = new ComSkeletonInstance(new InstanceHandle("oms_1_0", instanceId), loop);
        this.notifyIfExactSeatExistChild = new notifyIfExactSeatExistChild("notifyIfExactSeatExistChild", this.instance.getEventIndex("notifyIfExactSeatExistChild"), this.instance);
        this.notifyIfExactSeatExistPassenger = new notifyIfExactSeatExistPassenger("notifyIfExactSeatExistPassenger", this.instance.getEventIndex("notifyIfExactSeatExistPassenger"), this.instance);
        this.notifyIfExactPassengerGender = new notifyIfExactPassengerGender("notifyIfExactPassengerGender", this.instance.getEventIndex("notifyIfExactPassengerGender"), this.instance);
        this.notifyExactSeatPassengerBehavior = new notifyExactSeatPassengerBehavior("notifyExactSeatPassengerBehavior", this.instance.getEventIndex("notifyExactSeatPassengerBehavior"), this.instance);
        this.checkIfExactSeatExistChild = new checkIfExactSeatExistChild("checkIfExactSeatExistChild", this.instance.getMethodIndex("checkIfExactSeatExistChild"), this.instance);
        this.checkIfExactSeatExistPassenger = new checkIfExactSeatExistPassenger("checkIfExactSeatExistPassenger", this.instance.getMethodIndex("checkIfExactSeatExistPassenger"), this.instance);
        this.checkExactPassengerGender = new checkExactPassengerGender("checkExactPassengerGender", this.instance.getMethodIndex("checkExactPassengerGender"), this.instance);
        this.checkExactSeatPassengerBehavior = new checkExactSeatPassengerBehavior("checkExactSeatPassengerBehavior", this.instance.getMethodIndex("checkExactSeatPassengerBehavior"), this.instance);
    }

    // API OfferService
    public void OfferService() {
        this.instance.start();
    }

    // API StopOfferService
    public void StopOfferService() {
        this.instance.stop();
    }

    // API SetStateChangedCallback
    public void SetStateChangedCallback(ComSkeletonStateChangedCallback callback) {
        this.instance.setStateChangedCallback(callback);
    }
}

