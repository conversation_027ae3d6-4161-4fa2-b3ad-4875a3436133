/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 *
 *  * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 *  * All Rights Reserved.
 *  *
 *  * Redistribution and use in source and binary forms, with or without
 *  * modification, are NOT permitted except as agreed by
 *  * iAuto (Shanghai) Co., Ltd.
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 *
 * @file getHolidayInfo.java
 * @brief getHolidayInfo.java
 *
 *
 */

package ara.ivi.iviinf.ics.iviinf.v1.skeleton;
import ics.com.runtime.Skeleton;
import ics.com.runtime.ErrorCode;
import ics.com.runtime.utils.concurrent.Future;
import ics.com.serialization.common.Structure;
import ics.com.serialization.common.Wrapper;
import ics.com.runtime.ComSkeletonInstance;
import ics.com.runtime.ComSkeletonRequestCallback;
import ics.com.runtime.SessionTag;
import ics.com.runtime.utils.FieldOrderAnnotation;
import ara.ivi.iviinf.ics.iviinf.v1.TransformPropsObj;

public class getHolidayInfo {
    private ComSkeletonInstance instance;
    private String name;
    private int idx;

    public getHolidayInfo(String name, int idx, ComSkeletonInstance ins) {
        this.name = name;
        this.idx = idx;
        this.instance = ins;
    }

    public void RegMethodProcess(getHolidayInfoHandle callback) {
        this.instance.setMethodRequestCallback(this.idx, new ComSkeletonRequestCallback() {
            @Override
            public void onCall(byte[] payload, SessionTag tag) {
                getHolidayInfoInput input = (getHolidayInfoInput) Skeleton.executeRRMethodAsyncRequest(idx, instance, payload, TransformPropsObj.instance().kDefaultSomeipTransformationProps, getHolidayInfoInput.class);
                Future<getHolidayInfoOutput> future =  callback.OnCall(input.date);
                Skeleton.sendMethodResponse(future, idx, instance, TransformPropsObj.instance().kDefaultSomeipTransformationProps, tag);
            }

            @Override
            public void onError(ErrorCode errorCode, SessionTag tag) {
                //error handle
                callback.OnError(errorCode);
            }
        });
    }
    public static class getHolidayInfoOutput implements Structure {
        @FieldOrderAnnotation(order = 1)
        public ara.ivi.iviinf.ics.HolidayInfo status;

        public getHolidayInfoOutput(ara.ivi.iviinf.ics.HolidayInfo status) {
            this.status = status;
        }
    }
    public static class getHolidayInfoInput implements Wrapper {
        @FieldOrderAnnotation(order = 1)
        public String date;

        public getHolidayInfoInput(String date) {
            this.date = date;
        }
    }
    public interface getHolidayInfoHandle {
        public Future<getHolidayInfoOutput> OnCall(String date);
        public void OnError(ErrorCode errorCode);
    }
}

