/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 *
 *  * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 *  * All Rights Reserved.
 *  *
 *  * Redistribution and use in source and binary forms, with or without
 *  * modification, are NOT permitted except as agreed by
 *  * iAuto (Shanghai) Co., Ltd.
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 *
 * @file notifyDistanceToPositionWithType.java
 * @brief notifyDistanceToPositionWithType.java
 *
 *
 */

package ara.ivi.iviinf.ics.iviinf.v1.skeleton;
import ics.com.runtime.ErrorCode;
import ics.com.serialization.common.Wrapper;
import ics.com.runtime.ComSkeletonInstance;
import ics.com.runtime.Skeleton;
import ara.ivi.iviinf.ics.iviinf.v1.TransformPropsObj;

public class notifyDistanceToPositionWithType {
    private ComSkeletonInstance instance;
    private String name;
    private int idx;

    public notifyDistanceToPositionWithType(String name,int idx,ComSkeletonInstance ins) {
        this.name = name;
        this.idx = idx;
        this.instance = ins;
    }

    // API Event Send
    public int Send(ics.IVIInf_notifyDistanceToPositionWithType_param_t data){
        notifyDistanceToPositionWithTypeObject tmp = new notifyDistanceToPositionWithTypeObject(data);
        return Skeleton.sendEventNotify(this.idx, this.instance, TransformPropsObj.instance().kDefaultSomeipTransformationProps, tmp);
    }

    public static class notifyDistanceToPositionWithTypeObject implements Wrapper {
        public ics.IVIInf_notifyDistanceToPositionWithType_param_t data;

        public notifyDistanceToPositionWithTypeObject(ics.IVIInf_notifyDistanceToPositionWithType_param_t data) {
            this.data = data;
        }
    }
}

