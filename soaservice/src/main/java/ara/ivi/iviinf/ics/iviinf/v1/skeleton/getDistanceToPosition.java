/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 *
 *  * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 *  * All Rights Reserved.
 *  *
 *  * Redistribution and use in source and binary forms, with or without
 *  * modification, are NOT permitted except as agreed by
 *  * iAuto (Shanghai) Co., Ltd.
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 *
 * @file getDistanceToPosition.java
 * @brief getDistanceToPosition.java
 *
 *
 */

package ara.ivi.iviinf.ics.iviinf.v1.skeleton;
import ics.com.runtime.Skeleton;
import ics.com.runtime.ErrorCode;
import ics.com.runtime.utils.concurrent.Future;
import ics.com.serialization.common.Structure;
import ics.com.serialization.common.Wrapper;
import ics.com.runtime.ComSkeletonInstance;
import ics.com.runtime.ComSkeletonRequestCallback;
import ics.com.runtime.SessionTag;
import ics.com.runtime.utils.FieldOrderAnnotation;
import ara.ivi.iviinf.ics.iviinf.v1.TransformPropsObj;

public class getDistanceToPosition {
    private ComSkeletonInstance instance;
    private String name;
    private int idx;

    public getDistanceToPosition(String name, int idx, ComSkeletonInstance ins) {
        this.name = name;
        this.idx = idx;
        this.instance = ins;
    }

    public void RegMethodProcess(getDistanceToPositionHandle callback) {
        this.instance.setMethodRequestCallback(this.idx, new ComSkeletonRequestCallback() {
            @Override
            public void onCall(byte[] payload, SessionTag tag) {
                getDistanceToPositionInput input = (getDistanceToPositionInput) Skeleton.executeRRMethodAsyncRequest(idx, instance, payload, TransformPropsObj.instance().kDefaultSomeipTransformationProps, getDistanceToPositionInput.class);
                Future<getDistanceToPositionOutput> future =  callback.OnCall(input.latitude, input.longitude);
                Skeleton.sendMethodResponse(future, idx, instance, TransformPropsObj.instance().kDefaultSomeipTransformationProps, tag);
            }

            @Override
            public void onError(ErrorCode errorCode, SessionTag tag) {
                //error handle
                callback.OnError(errorCode);
            }
        });
    }
    public static class getDistanceToPositionOutput implements Structure {
        @FieldOrderAnnotation(order = 1)
        public Integer distance;// uint32_t data: please convert by function: Integer.toUnsignedInt or Integer.toUnsignedLong

        public getDistanceToPositionOutput(Integer distance) {
            this.distance = distance;
        }
    }
    public static class getDistanceToPositionInput implements Wrapper {
        @FieldOrderAnnotation(order = 1)
        public Double latitude;
        @FieldOrderAnnotation(order = 2)
        public Double longitude;

        public getDistanceToPositionInput(Double latitude, Double longitude) {
            this.latitude = latitude;
            this.longitude = longitude;
        }
    }
    public interface getDistanceToPositionHandle {
        public Future<getDistanceToPositionOutput> OnCall(Double latitude, Double longitude);
        public void OnError(ErrorCode errorCode);
    }
}

