/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 *
 *  * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 *  * All Rights Reserved.
 *  *
 *  * Redistribution and use in source and binary forms, with or without
 *  * modification, are NOT permitted except as agreed by
 *  * iAuto (Shanghai) Co., Ltd.
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 *
 * @file iviinf_manifest.cpp
 * @brief iviinf_manifest.cpp
 *
 *
 */

package ara.ivi.iviinf.ics.iviinf.v1;
import ics.com.runtime.ComTransportManager;
import ics.com.runtime.ComTransportManifest;

public class iviinf_manifest {
    public iviinf_manifest(){}

    public static void OnInit(){
        ComTransportManifest manifest = new ComTransportManifest();
        manifest.serviceInfo = "iviinf_1_0";
        manifest.e2eManifest = "";
        manifest.permissionManifest = "";
        manifest.events = new String[]{"notifyDistanceToPosition","notifyDistanceToPositionWithType"};
        manifest.fields = new String[]{};
        manifest.methods = new String[]{"getCurrentDate","getHolidayInfo","getDistanceToPosition"};
        manifest.bindingName = "isomeip";
        manifest.deploymentManifest ="{\n" +
        "    \"services\" :\n" +
        "    [\n" +
        "        {\n" +
        "            \"short_name\" : \"iviinf_1_0\",\n" +
        "            \"service\" : 160,\n" +
        "            \"major_version\" : 1,\n" +
        "            \"minor_version\" : 0,\n" +
        "            \"events\":\n" +
        "            [\n" +
        "                {\n" +
        "                    \"short_name\" : \"notifyDistanceToPosition\",\n" +
        "                    \"event\" : 1,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\" : \"notifyDistanceToPositionWithType\",\n" +
        "                    \"event\" : 2,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                }\n" +
        "            ],\n" +
        "            \"fields\":\n" +
        "            [\n" +
        "            ],\n" +
        "            \"methods\":\n" +
        "            [\n" +
        "                {\n" +
        "                    \"short_name\": \"getCurrentDate\",\n" +
        "                    \"method\": 4,\n" +
        "                    \"fire_and_forget\" : false,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"getHolidayInfo\",\n" +
        "                    \"method\": 5,\n" +
        "                    \"fire_and_forget\" : false,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"getDistanceToPosition\",\n" +
        "                    \"method\": 8,\n" +
        "                    \"fire_and_forget\" : false,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                }\n" +
        "            ],\n" +
        "            \"eventgroups\":\n" +
        "            [\n" +
        "                {\n" +
        "                    \"short_name\" : \"IVIInfEG\",\n" +
        "                    \"eventgroup\" : 1,\n" +
        "                    \"events\" : [1,2]\n" +
        "                }\n" +
        "            ]\n" +
        "        }\n" +
        "    ],\n" +
        "    \"provided_instances\" :\n" +
        "    [\n" +
        "        {\n" +
        "            \"service\" : 160,\n" +
        "            \"major_version\" : 1,\n" +
        "            \"short_name\" : \"IVIInf_1_SomeipProvided\",\n" +
        "            \"instance\" : 1,\n" +
        "            \"udp_port\" : 0,\n" +
        "            \"tcp_port\" : 30501,\n" +
        "            \"tcp_reuse\" : true,\n" +
        "            \"udp_reuse\" : false,\n" +
        "            \"method_attribute\" :\n" +
        "            [\n" +
        "            ],\n" +
        "            \"event_attribute\" :\n" +
        "            [\n" +
        "            ],\n" +
        "            \"offer_time_reference\" : \"IVIInf_SomeipServerServiceInstanceConfig\",\n" +
        "            \"ethernet_reference\" : \"IVIunicastEndpoint\",\n" +
        "            \"tcp_tls_flag\" : false,\n" +
        "            \"udp_tls_flag\" : false,\n" +
        "            \"provided_eventgroups\" :\n" +
        "            [\n" +
        "                {\n" +
        "                    \"eventgroup\" : 1,\n" +
        "                    \"multicast_addr_ip4\" : \"**********\",\n" +
        "                    \"multicast_port\" : 30491,\n" +
        "                    \"threshold\" : 0\n" +
        "                }\n" +
        "            ]\n" +
        "        }\n" +
        "    ],\n" +
        "    \"required_instances\" :\n" +
        "    [\n" +
        "    ],\n" +
        "    \"sd_server_offer_times\" :\n" +
        "    [\n" +
        "        {\n" +
        "            \"sd_time_name\" : \"IVIInf_SomeipServerServiceInstanceConfig\",\n" +
        "            \"initial_delay_min\" : 10,\n" +
        "            \"initial_delay_max\" : 50,\n" +
        "            \"repetitions_base_delay\" : 100,\n" +
        "            \"repetitions_max\" : 5,\n" +
        "            \"ttl\" : 3,\n" +
        "            \"cyclic_offer_delay\" : 2000,\n" +
        "            \"request_response_delay_min\" : 10,\n" +
        "            \"request_response_delay_max\" : 20\n" +
        "        }\n" +
        "    ],\n" +
        "    \"sd_client_find_times\" :\n" +
        "    [\n" +
        "    ],\n" +
        "    \"sd_client_subscribe_times\" :\n" +
        "    [\n" +
        "    ],\n" +
        "    \"sd_server_subscribe_times\" :\n" +
        "    [\n" +
        "    ],\n" +
        "    \"network_endpoints\" :\n" +
        "    [\n" +
        "        {\n" +
        "            \"network_id\" : \"IVIunicastEndpoint\",\n" +
        "            \"ip_addr\" : \"************\",\n" +
        "            \"ip_addr_type\" : 4,\n" +
        "            \"subnet_mask\" : \"*************\",\n" +
        "            \"ttl\" : 3,\n" +
        "            \"multicast_ip_addr\" : \"**********\",\n" +
        "            \"multicast_port\" : 30490\n" +
        "        }\n" +
        "      ]\n" +
        "  }";

        ComTransportManager.instance().loadManifest("iviinf_1_0",manifest);
    }
}
