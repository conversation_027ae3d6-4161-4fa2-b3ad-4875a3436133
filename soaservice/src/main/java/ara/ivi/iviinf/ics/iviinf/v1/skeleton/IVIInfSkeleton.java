/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 *
 *  * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 *  * All Rights Reserved.
 *  *
 *  * Redistribution and use in source and binary forms, with or without
 *  * modification, are NOT permitted except as agreed by
 *  * iAuto (Shanghai) Co., Ltd.
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 *
 * @file IVIInfSkeleton.java
 * @brief IVIInfSkeleton.java
 *
 *
 */

package ara.ivi.iviinf.ics.iviinf.v1.skeleton;
import android.os.Looper;
import ics.com.runtime.ComSkeletonInstance;
import ics.com.runtime.ComSkeletonStateChangedCallback;
import ics.com.runtime.InstanceHandle;

public class IVIInfSkeleton {
    private ComSkeletonInstance instance;
    public ara.ivi.iviinf.ics.iviinf.v1.skeleton.notifyDistanceToPosition notifyDistanceToPosition;
    public ara.ivi.iviinf.ics.iviinf.v1.skeleton.notifyDistanceToPositionWithType notifyDistanceToPositionWithType;
    public ara.ivi.iviinf.ics.iviinf.v1.skeleton.getCurrentDate getCurrentDate;
    public ara.ivi.iviinf.ics.iviinf.v1.skeleton.getHolidayInfo getHolidayInfo;
    public ara.ivi.iviinf.ics.iviinf.v1.skeleton.getDistanceToPosition getDistanceToPosition;

    public IVIInfSkeleton(String instanceId, Looper loop) {
        this.instance = new ComSkeletonInstance(new InstanceHandle("iviinf_1_0", instanceId), loop);
        this.notifyDistanceToPosition = new notifyDistanceToPosition("notifyDistanceToPosition", this.instance.getEventIndex("notifyDistanceToPosition"), this.instance);
        this.notifyDistanceToPositionWithType = new notifyDistanceToPositionWithType("notifyDistanceToPositionWithType", this.instance.getEventIndex("notifyDistanceToPositionWithType"), this.instance);
        this.getCurrentDate = new getCurrentDate("getCurrentDate", this.instance.getMethodIndex("getCurrentDate"), this.instance);
        this.getHolidayInfo = new getHolidayInfo("getHolidayInfo", this.instance.getMethodIndex("getHolidayInfo"), this.instance);
        this.getDistanceToPosition = new getDistanceToPosition("getDistanceToPosition", this.instance.getMethodIndex("getDistanceToPosition"), this.instance);
    }

    // API OfferService
    public void OfferService() {
        this.instance.start();
    }

    // API StopOfferService
    public void StopOfferService() {
        this.instance.stop();
    }

    // API SetStateChangedCallback
    public void SetStateChangedCallback(ComSkeletonStateChangedCallback callback) {
        this.instance.setStateChangedCallback(callback);
    }
}

