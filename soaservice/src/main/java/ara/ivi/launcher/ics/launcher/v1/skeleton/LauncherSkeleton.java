/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 *
 *  * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 *  * All Rights Reserved.
 *  *
 *  * Redistribution and use in source and binary forms, with or without
 *  * modification, are NOT permitted except as agreed by
 *  * iAuto (Shanghai) Co., Ltd.
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 *
 * @file LauncherSkeleton.java
 * @brief LauncherSkeleton.java
 *
 *
 */

package ara.ivi.launcher.ics.launcher.v1.skeleton;
import android.os.Looper;
import ics.com.runtime.ComSkeletonInstance;
import ics.com.runtime.ComSkeletonStateChangedCallback;
import ics.com.runtime.InstanceHandle;

public class LauncherSkeleton {
    private ComSkeletonInstance instance;
    public ara.ivi.launcher.ics.launcher.v1.skeleton.backDAHome backDAHome;
    public ara.ivi.launcher.ics.launcher.v1.skeleton.openApplication openApplication;
    public ara.ivi.launcher.ics.launcher.v1.skeleton.setScreenStatus setScreenStatus;
    public ara.ivi.launcher.ics.launcher.v1.skeleton.setIVIScreenOnOffStatus setIVIScreenOnOffStatus;

    public LauncherSkeleton(String instanceId, Looper loop) {
        this.instance = new ComSkeletonInstance(new InstanceHandle("launcher_1_0", instanceId), loop);
        this.backDAHome = new backDAHome("backDAHome", this.instance.getMethodIndex("backDAHome"), this.instance);
        this.openApplication = new openApplication("openApplication", this.instance.getMethodIndex("openApplication"), this.instance);
        this.setScreenStatus = new setScreenStatus("setScreenStatus", this.instance.getMethodIndex("setScreenStatus"), this.instance);
        this.setIVIScreenOnOffStatus = new setIVIScreenOnOffStatus("setIVIScreenOnOffStatus", this.instance.getMethodIndex("setIVIScreenOnOffStatus"), this.instance);
    }

    // API OfferService
    public void OfferService() {
        this.instance.start();
    }

    // API StopOfferService
    public void StopOfferService() {
        this.instance.stop();
    }

    // API SetStateChangedCallback
    public void SetStateChangedCallback(ComSkeletonStateChangedCallback callback) {
        this.instance.setStateChangedCallback(callback);
    }
}

