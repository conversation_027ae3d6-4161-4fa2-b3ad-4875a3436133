/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 *
 *  * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 *  * All Rights Reserved.
 *  *
 *  * Redistribution and use in source and binary forms, with or without
 *  * modification, are NOT permitted except as agreed by
 *  * iAuto (Shanghai) Co., Ltd.
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 *
 * @file EnergyCenterSkeleton.java
 * @brief EnergyCenterSkeleton.java
 *
 *
 */

package ara.ivi.energycenter.ics.energycenter.v1.skeleton;
import android.os.Looper;
import ics.com.runtime.ComSkeletonInstance;
import ics.com.runtime.ComSkeletonStateChangedCallback;
import ics.com.runtime.InstanceHandle;

public class EnergyCenterSkeleton {
    private ComSkeletonInstance instance;
    public ara.ivi.energycenter.ics.energycenter.v1.skeleton.setInRoadPreheatingSwitchStatus setInRoadPreheatingSwitchStatus;
    public ara.ivi.energycenter.ics.energycenter.v1.skeleton.setExternalChargeSwitchStatus setExternalChargeSwitchStatus;
    public ara.ivi.energycenter.ics.energycenter.v1.skeleton.setEnduranceCalculationStandardType setEnduranceCalculationStandardType;

    public EnergyCenterSkeleton(String instanceId, Looper loop) {
        this.instance = new ComSkeletonInstance(new InstanceHandle("energycenter_1_0", instanceId), loop);
        this.setInRoadPreheatingSwitchStatus = new ara.ivi.energycenter.ics.energycenter.v1.skeleton.setInRoadPreheatingSwitchStatus("setInRoadPreheatingSwitchStatus", this.instance.getMethodIndex("setInRoadPreheatingSwitchStatus"), this.instance);
        this.setExternalChargeSwitchStatus = new ara.ivi.energycenter.ics.energycenter.v1.skeleton.setExternalChargeSwitchStatus("setExternalChargeSwitchStatus", this.instance.getMethodIndex("setExternalChargeSwitchStatus"), this.instance);
        this.setEnduranceCalculationStandardType = new ara.ivi.energycenter.ics.energycenter.v1.skeleton.setEnduranceCalculationStandardType("setEnduranceCalculationStandardType", this.instance.getMethodIndex("setEnduranceCalculationStandardType"), this.instance);
    }

    // API OfferService
    public void OfferService() {
        this.instance.start();
    }

    // API StopOfferService
    public void StopOfferService() {
        this.instance.stop();
    }

    // API SetStateChangedCallback
    public void SetStateChangedCallback(ComSkeletonStateChangedCallback callback) {
        this.instance.setStateChangedCallback(callback);
    }
}

