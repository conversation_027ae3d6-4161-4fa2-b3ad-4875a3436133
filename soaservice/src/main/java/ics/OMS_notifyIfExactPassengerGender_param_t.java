/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 *
 *  * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 *  * All Rights Reserved.
 *  *
 *  * Redistribution and use in source and binary forms, with or without
 *  * modification, are NOT permitted except as agreed by
 *  * iAuto (Shanghai) Co., Ltd.
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 *
 * @file OMS_notifyIfExactPassengerGender_param_t.java
 * @brief 
 *
 *
 */

package ics;
import java.io.Serializable;

import ics.com.serialization.common.Structure;
import ics.com.runtime.utils.FieldOrderAnnotation;

public class OMS_notifyIfExactPassengerGender_param_t implements Structure, Serializable {
    @FieldOrderAnnotation(order = 1)
    public Byte position;// uint8_t data: please convert by function: Byte.toUnsignedInt or Byte.toUnsignedLong
    @FieldOrderAnnotation(order = 2)
    public Byte gender;// uint8_t data: please convert by function: Byte.toUnsignedInt or Byte.toUnsignedLong

    public OMS_notifyIfExactPassengerGender_param_t() {
        this.position = new Byte((byte)0);
        this.gender = new Byte((byte)0);
    }

    public OMS_notifyIfExactPassengerGender_param_t( Byte position, Byte gender ) {
            this.position = position;
            this.gender = gender;
    }
}
