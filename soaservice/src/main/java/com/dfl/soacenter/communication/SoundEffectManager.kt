package com.dfl.soacenter.communication

import com.dfl.androhermas.constant.ServiceConstant
import com.dfl.androhermas.remote.client.ConnectionClient
import com.dfl.android.common.base.BaseSoaManager
import com.dfl.android.common.util.CustomApiUtils
import com.dfl.android.common.util.GsonUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.api.base.Constants
import com.dfl.api.da.setting.ISettingCarSound
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.entity.GetVoiceResponseData
import com.dfl.soacenter.entity.OutSideSoundRequestData
import com.dfl.soacenter.entity.PlayPrankMusicRequestData
import com.dfl.soacenter.entity.RequestSoaBaseBean
import com.dfl.soacenter.entity.ResponseSoaBaseBean
import com.dfl.soacenter.entity.SoundEffectRequestData
import com.dfl.soacenter.entity.SoundSpaceResponse
import com.dfl.soacenter.entity.SoundSpaceResponseData
import com.dfl.soacenter.entity.UseVoiceRequestData
import com.dfl.soacenter.entity.VoiceData
import com.dfl.soacenter.entity.VoiceVolumeRequestData
import com.google.gson.JsonParseException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.json.JSONException
import java.util.concurrent.CopyOnWriteArrayList
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/01/12
 * desc : 声音空间通信管理
 * version: 1.0
 */
object SoundEffectManager : BaseSoaManager() {
    object ProtocolType {
        /**
         * 获取沉浸音效id
         */
        const val PROTOCOL_ID_GET_SOUND_EFFECT = 1010

        /**
         * 设置沉浸音效
         */
        const val PROTOCOL_ID_SOUND_EFFECT = 1015

        /**
         * 获取声浪列表 目前测试数据id 0，1，2
         */
        const val PROTOCOL_ID_GET_VOICE_VOLUME = 1018

        /**
         * 使用/不使用声浪
         */
        const val PROTOCOL_ID_USE_VOICE_VOLUME = 1021

        /**
         * 设置声浪音量
         */
        const val PROTOCOL_ID_VOICE_VOLUME = 1025

        /**
         * 获取整蛊音id
         */
        const val PROTOCOL_ID_GET_PRANK_MUSIC = 1029

        /**
         * 设置整蛊音
         */
        const val PROTOCOL_ID_PLAY_PRANK_MUSIC = 1032
        const val PROTOCOL_ID_PLAY_OUTSIDE_SOUND = 1080
    }

    @Volatile
    private var mISettingCarSound: ISettingCarSound? = null
    private var mVoiceList: CopyOnWriteArrayList<VoiceData> = CopyOnWriteArrayList()


    override fun init() {
        TAG = SoaConstants.SOA_TAG.plus("SoundEffectManager")
        mClient = ConnectionClient.Builder(CommonUtils.getApp(), ServiceConstant.Package.PACKAGE_SPORTS_CAR_SOUND)
                .setReconnectMode(ConnectionClient.CONNECT_MODE_NOT_RECONNECT)
                .setThreadPool(1, 1)
                .build()
        CoroutineScope(Dispatchers.IO).launch {
            initCustomApi()
        }
    }

    private suspend fun initCustomApi(): Boolean = initCustomApi(
        Constants.DFL_DA_SETTING_SETTINGCARSOUND_SERVICE,
        mISettingCarSound,
        { if (it is ISettingCarSound) it else null },
        { mISettingCarSound = it }
    )

    /**
     * 仅用于单元测试
     * @param req 请求体
     */
    suspend fun testRequestAction(req: String, method: String): SoundSpaceResponse<SoundSpaceResponseData>? {
        return suspendCoroutine {
            queryToSendMessage(req, method, { json ->
                try {
                    val response = GsonUtils.fromJson<SoundSpaceResponse<SoundSpaceResponseData>>(
                        json,
                        GsonUtils.type(SoundSpaceResponse::class.java, SoundSpaceResponseData::class.java)
                    )
                    it.resume(response)
                } catch (e: Exception) {
                    if (e is JsonParseException || e is JSONException) {
                        CommonLogUtils.logE(TAG, "$method json转换失败,json=$json,e=$e")
                    }
                    it.resume(null)
                    e.printStackTrace()
                }
            }, {
                it.resume(null)
            })
        }

    }

    /**
     * 发起声音空间-动作通讯
     * @param req 请求体
     * @param method 方法名
     */
    private fun requestAction(req: String, method: String) {
        queryToSendMessage(req, method) {}
    }

    /**
     * 声音空间获取音效id
     * @param soundType 音效类型 1沉浸 2整蛊 3声浪
     */
    private suspend fun requestGetSoundId(soundType: Int): GetVoiceResponseData? {
        return suspendCoroutine { continuation ->
            val protocolId = when (soundType) {
                0 -> ProtocolType.PROTOCOL_ID_GET_SOUND_EFFECT
                1 -> ProtocolType.PROTOCOL_ID_GET_PRANK_MUSIC
                else -> ProtocolType.PROTOCOL_ID_GET_VOICE_VOLUME
            }
            val bean = RequestSoaBaseBean(
                protocolId = protocolId,
                messageType = SoaConstants.MESSAGE_TYPE_REQUEST,
                data = SoundEffectRequestData(soundType.toString())
            )
            queryToSendMessage(GsonUtils.toJson(bean), "requestGetSoundId", {
                val res = GsonUtils.fromJson<ResponseSoaBaseBean<GetVoiceResponseData>>(
                    it,
                    GsonUtils.type(ResponseSoaBaseBean::class.java, GetVoiceResponseData::class.java)
                )
                continuation.resume(res.data)
            }, {
                continuation.resume(null)
            })
        }

    }

    /**
     * 声音空间设置沉浸音效
     * @param soundId 音效Id 自然原声:1,清透人声:2,沉浸观影:3,嗨翻现场:4,音乐厅:5,轻柔伴音:6
     */
    fun requestSoundEffect(soundId: String) {
        val data = SoundEffectRequestData(soundId)
        val bean = RequestSoaBaseBean(
            protocolId = ProtocolType.PROTOCOL_ID_SOUND_EFFECT,
            messageType = SoaConstants.MESSAGE_TYPE_REQUEST,
            data = data
        )
        requestAction(GsonUtils.toJson(bean), "requestSoundEffect")
    }

    /**
     * 设置整蛊音,转换ccm参数为应用间通讯所需
     * 通讯整蛊音id 34:喵 35:鹦鹉 36:海豚 37:猫猫祟祟 38:奇幻夏⽇ 39:绿茵狂欢
     * @param id 整蛊音id
     * @param position 播放的位置 1主驾2副驾3左后4右后
     */
    fun requestPlayPrankMusic(id: Int, position: String) {
        val data = PlayPrankMusicRequestData(id.toString(), position)
        val bean = RequestSoaBaseBean(
            protocolId = ProtocolType.PROTOCOL_ID_PLAY_PRANK_MUSIC,
            messageType = SoaConstants.MESSAGE_TYPE_REQUEST,
            data = data
        )
        requestAction(GsonUtils.toJson(bean), "requestPlayPrankMusic id=$id position=$position")
    }

    /**
     * 设置声浪音量
     * @param volume 1低2中3高
     */
    fun requestVoiceVolume(volume: String) {
        val data = VoiceVolumeRequestData(volume)
        val bean = RequestSoaBaseBean(
            protocolId = ProtocolType.PROTOCOL_ID_VOICE_VOLUME,
            messageType = SoaConstants.MESSAGE_TYPE_REQUEST,
            data = data
        )
        requestAction(GsonUtils.toJson(bean), "requestVoiceVolume volume=$volume")
    }

    /**
     * @param sound ccm声浪 1GTR，2Fairlady，3赛博朋克
     * @param use 取消使用0，使用1
     */
    fun requestUseVoice(sound: Int, use: String) {
        CoroutineScope(Dispatchers.IO).launch {
            //0513声音空间更新协议目前写死三个声浪
            //if (mVoiceList.isEmpty()) {
            //    CommonLogUtils.logI(TAG, "start requestGetSoundId")
            //    val result = async { requestGetSoundId(3) }.await() ?: return@launch
            //    CommonLogUtils.logI(TAG, "end requestGetSoundId")
            //    mVoiceList.clear()
            //    mVoiceList.addAll(result.roars)
            //}
            //if (mVoiceList.size <= soundIndex) return@launch
            //val data = UseVoiceRequestData(mVoiceList[soundIndex].id, use)
            val goodsId = when (sound) {
                1 -> UseVoiceRequestData.SOUND_GTR
                2 -> UseVoiceRequestData.SOUND_FAIRLADY
                else -> UseVoiceRequestData.SOUND_CYBERPUNK
            }
            val data = UseVoiceRequestData(goodsId, use)
            val bean = RequestSoaBaseBean(
                protocolId = ProtocolType.PROTOCOL_ID_USE_VOICE_VOLUME,
                messageType = SoaConstants.MESSAGE_TYPE_REQUEST,
                data = data
            )
            requestAction(GsonUtils.toJson(bean), "requestUseVoice")
        }

    }

    /**
     * 设置声浪开关
     * @param type 0关闭 1打开
     */
    fun requestSetVoiceSwitch(type: Int) {
        CustomApiUtils.requestCustomApi({
            if (initCustomApi()) {
                mISettingCarSound?.sportsCarSoundType = type
            }
        }, {
            it.printStackTrace()
            CommonLogUtils.logE(TAG, "setIVIScreenOnOffStatus error,  $it")
        })
    }

    /**
     * 设置声浪开关
     * @param duration 车外大喇叭持续时间 0关闭
     */
    fun requestOutSideSound(duration: Int) {
        val data = OutSideSoundRequestData(duration)
        val bean = RequestSoaBaseBean(
            protocolId = ProtocolType.PROTOCOL_ID_PLAY_OUTSIDE_SOUND,
            messageType = SoaConstants.MESSAGE_TYPE_REQUEST,
            data = data
        )
        requestAction(GsonUtils.toJson(bean), "requestOutSideSound duration=$duration")
    }
}