package com.dfl.soacenter.communication

import ara.ivi.sysset.ics.ConnectionStatus
import ara.ivi.sysset.ics.SwitchStatus
import ara.ivi.sysset.ics.sysset.v1.skeleton.getBluetoothConnectionStatus
import ara.ivi.sysset.ics.sysset.v1.skeleton.getBluetoothSwitchStatus
import ara.ivi.sysset.ics.sysset.v1.skeleton.getHotspotConnectionStatus
import ara.ivi.sysset.ics.sysset.v1.skeleton.getHotspotSwitchStatus
import ara.ivi.sysset.ics.sysset.v1.skeleton.getMobileNetworkSwitchStatus
import ara.ivi.sysset.ics.sysset.v1.skeleton.getWLANConnectionStatus
import ara.ivi.sysset.ics.sysset.v1.skeleton.getWLANSwitchStatus
import com.dfl.androhermas.constant.ServiceConstant
import com.dfl.androhermas.remote.client.ConnectionClient
import com.dfl.android.common.base.BaseSoaManager
import com.dfl.android.common.util.GsonUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.entity.RequestSoaBaseBean
import com.dfl.soacenter.entity.SystemSettingResponseBean
import com.dfl.soacenter.entity.ThemeDataBean
import com.dfl.soacenter.entity.ValueDataBean
import com.dfl.soacenter.entity.VolumeDataBean
import com.google.gson.JsonParseException
import ics.com.serialization.common.Structure
import org.json.JSONException
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2024/01/10
 * desc: 系统设置的应用间通讯管理器
 * version:1.0
 */
object SoaSystemSettingManager : BaseSoaManager() {

    object ProtocolType {

        /**
         * 设置日夜模式
         * 0 自动 1 白天 2 黑夜
         */
        const val SET_DAY_AND_NIGHT_MODE = 1028

        /**
         * 设置音量
         * type范围:1通话 2语音 3导航 4多媒体 5蓝牙音乐 6车外音 7铃声音量
         * value 范围:0-40
         */
        const val SET_VOLUME = 1039

        /**
         * 设置静音
         * 0关 1开
         */
        const val SET_MUTE_STATUS = 1040

        /**
         * 设置中控亮度
         * value 范围:1-21
         */
        const val SET_SCREEN_BRIGHTNESS = 1041

        /**
         * 设置壁纸
         * value 范围:1-9
         */
        const val SET_THEME_WALLPAPER = 1042

        /**
         * 设置蓝牙开关
         * 0关 1开
         */
        const val SET_BLUETOOTH_SWITCH_STATUS = 1043

        /**
         * 设置热点开关
         * 0关 1开
         */
        const val SET_HOTSPOT_SWITCH_STATUS = 1044

        /**
         * 设置wifi开关
         * 0关 1开
         */
        const val SET_WLAN_SWITCH_STATUS = 1045

        /**
         * 设置移动网络开关
         * 0关 1开
         */
        const val SET_MOBILE_NETWORK_SWITCH_STATUS = 1046

        /**
         * 获取蓝牙开关状态
         * 0关 1开
         */
        const val GET_BLUETOOTH_SWITCH_STATUS = 1047

        /**
         * 获取蓝牙连接状态
         * 0 未连接 1 已连接
         */
        const val GET_BLUETOOTH_CONNECTION_STATUS = 1048

        /**
         * 获取热点开关状态
         * 0关 1开
         */
        const val GET_HOTSPOT_SWITCH_STATUS = 1049

        /**
         * 获取热点连接状态
         * 0 未连接 1 已连接
         */
        const val GET_HOTSPOT_CONNECTION_STATUS = 1050

        /**
         *获取 wifi 开关状态
         * 0关 1开
         */
        const val GET_WLAN_SWITCH_STATUS = 1051

        /**
         * 获取 wifi 连接状态
         * 0 未连接 1 已连接
         */
        const val GET_WLAN_CONNECTION_STATUS = 1052

        /**
         * 获取移动网络开关状态
         * 0关 1开
         */
        const val GET_MOBILE_NETWORK_SWITCH_STATUS = 1053
    }

    override fun init() {
        TAG = SoaConstants.SOA_TAG.plus("SystemSettingManager")
        mClient = ConnectionClient.Builder(CommonUtils.getApp(), ServiceConstant.Package.PACKAGE_SYSTEM_SETTINGS)
                .setReconnectMode(ConnectionClient.CONNECT_MODE_NOT_RECONNECT)
                .setThreadPool(1, 1)
                .build()
    }

    /**
     * 请求系统设置相关动作 的应用间通讯
     * @param req 请求体
     */
    private fun requestAction(req: String, method: String) {
        queryToSendMessage(req, method) { json ->
            try {
                val bean = GsonUtils.fromJson(json, SystemSettingResponseBean::class.java)
                //通信成功且json格式正确
                if (bean?.statusCode == SystemSettingResponseBean.STATUS_CODE_FAIL) {
                    CommonLogUtils.logE(TAG, "$method 失败 json=$bean")
                } else {
                    CommonLogUtils.logI(TAG, "$method 成功 json=$bean")
                }
            } catch (e: Exception) {
                if (e is JsonParseException || e is JSONException) {
                    CommonLogUtils.logE(TAG, "$method json转换失败,json=$json,e=$e")
                }
                e.printStackTrace()
            }
        }
    }

    /**
     * 仅用于单元测试
     * @param req 请求体
     * @return SystemSettingResponseBean 或 null
     */
    suspend fun testRequestAction(req: String, method: String): SystemSettingResponseBean? {
        return suspendCoroutine {
            queryToSendMessage(req, method, { json ->
                try {
                    val response = GsonUtils.fromJson(json, SystemSettingResponseBean::class.java)
                    if (response?.statusCode == SystemSettingResponseBean.STATUS_CODE_FAIL) {
                        CommonLogUtils.logE(TAG, "$method 失败,json=$json")
                    }
                    it.resume(response)
                } catch (e: Exception) {
                    if (e is JsonParseException || e is JSONException) {
                        CommonLogUtils.logE(TAG, "$method json转换失败,json=$json,e=$e")
                    }
                    it.resume(null)
                    e.printStackTrace()
                }
            }, {
                it.resume(null)
            })
        }
    }

    /**
     * 请求系统设置相关条件 的应用间通讯
     * @param req json格式请求体
     * @param method 方法名
     * @return T? 实现Structure接口返回结果类型 或 null
     */
    private suspend inline fun <reified T : Structure> requestCondition(req: String, method: String): T? {
        return suspendCoroutine {
            queryToSendMessage(req, method) { json ->
                it.resume(getStatus(json, method) as? T)
            }
        }
    }

    /**
     * 根据响应体创建对应类型
     * @param json 响应体
     * @param method 方法名
     * @return 实现Structure接口返回结果类型 或 null
     */
    private fun getStatus(json: String, method: String): Structure? {
        try {
            val response = GsonUtils.fromJson(json, SystemSettingResponseBean::class.java)
            if (response?.data != null && response.statusCode == SystemSettingResponseBean.STATUS_CODE_SUCCESS) {
                return when (response.protocolId) {
                    ProtocolType.GET_BLUETOOTH_CONNECTION_STATUS -> {
                        if (response.data.value == ValueDataBean.OFF_OR_UNCONNECTED) {
                            getBluetoothConnectionStatus.getBluetoothConnectionStatusOutput(ConnectionStatus.NO_CONNECTED)
                        } else {
                            getBluetoothConnectionStatus.getBluetoothConnectionStatusOutput(ConnectionStatus.CONNECTED)
                        }
                    }

                    ProtocolType.GET_BLUETOOTH_SWITCH_STATUS -> {
                        if (response.data.value == ValueDataBean.OFF_OR_UNCONNECTED) {
                            getBluetoothSwitchStatus.getBluetoothSwitchStatusOutput(SwitchStatus.OFF)
                        } else {
                            getBluetoothSwitchStatus.getBluetoothSwitchStatusOutput(SwitchStatus.ON)
                        }
                    }

                    ProtocolType.GET_HOTSPOT_CONNECTION_STATUS -> {
                        if (response.data.value == ValueDataBean.OFF_OR_UNCONNECTED) {
                            getHotspotConnectionStatus.getHotspotConnectionStatusOutput(ConnectionStatus.NO_CONNECTED)
                        } else {
                            getHotspotConnectionStatus.getHotspotConnectionStatusOutput(ConnectionStatus.CONNECTED)
                        }
                    }

                    ProtocolType.GET_HOTSPOT_SWITCH_STATUS -> {
                        if (response.data.value == ValueDataBean.OFF_OR_UNCONNECTED) {
                            getHotspotSwitchStatus.getHotspotSwitchStatusOutput(SwitchStatus.OFF)
                        } else {
                            getHotspotSwitchStatus.getHotspotSwitchStatusOutput(SwitchStatus.ON)
                        }
                    }

                    ProtocolType.GET_WLAN_CONNECTION_STATUS -> {
                        if (response.data.value == ValueDataBean.OFF_OR_UNCONNECTED) {
                            getWLANConnectionStatus.getWLANConnectionStatusOutput(ConnectionStatus.NO_CONNECTED)
                        } else {
                            getWLANConnectionStatus.getWLANConnectionStatusOutput(ConnectionStatus.CONNECTED)
                        }
                    }

                    ProtocolType.GET_WLAN_SWITCH_STATUS -> {
                        if (response.data.value == ValueDataBean.OFF_OR_UNCONNECTED) {
                            getWLANSwitchStatus.getWLANSwitchStatusOutput(SwitchStatus.OFF)
                        } else {
                            getWLANSwitchStatus.getWLANSwitchStatusOutput(SwitchStatus.ON)
                        }
                    }

                    ProtocolType.GET_MOBILE_NETWORK_SWITCH_STATUS -> {
                        if (response.data.value == ValueDataBean.OFF_OR_UNCONNECTED) {
                            getMobileNetworkSwitchStatus.getMobileNetworkSwitchStatusOutput(SwitchStatus.OFF)
                        } else {
                            getMobileNetworkSwitchStatus.getMobileNetworkSwitchStatusOutput(SwitchStatus.ON)
                        }
                    }

                    else -> {
                        CommonLogUtils.logE(TAG, "$method 错误的protocolId, json=$json")
                        null
                    }
                }
            } else {
                CommonLogUtils.logE(TAG, "$method 响应数据为空或响应失败, json=$json")
                return null
            }
        } catch (e: Exception) {
            if (e is JsonParseException || e is JSONException) {
                CommonLogUtils.logE(TAG, "$method json转换失败,json=$json,e=$e")
            }
            e.printStackTrace()
            return null
        }
    }

    /**
     * 设置日夜模式
     * @param themeType 0 自动 1 白天 2 黑夜
     */
    fun setDayAndNightMode(themeType: Int) {
        val bean = RequestSoaBaseBean(protocolId = ProtocolType.SET_DAY_AND_NIGHT_MODE, data = ThemeDataBean(themeType))
        requestAction(GsonUtils.toJson(bean), "setDayAndNightMode")
    }


    /**
     *设置音量
     * @param type VolumeDataBean常量 1通话 2语音 3导航 4多媒体 5蓝牙音乐
     * @param value 范围:0-40
     */
    fun setVolume(type: Int, value: Int) {
        val bean = RequestSoaBaseBean(protocolId = ProtocolType.SET_VOLUME, data = VolumeDataBean(type, value))
        requestAction(GsonUtils.toJson(bean), "setVolume")
    }

    /**
     * 设置静音
     * @param value 0关 1开
     */
    fun setMuteStatus(value: Int) {
        val bean = RequestSoaBaseBean(protocolId = ProtocolType.SET_MUTE_STATUS, data = ValueDataBean(value))
        requestAction(GsonUtils.toJson(bean), "setMuteStatus")
    }

    /**
     * 设置壁纸
     * @param value 范围:1-9
     */
    fun setThemeWallpaper(value: Int) {
        val bean = RequestSoaBaseBean(protocolId = ProtocolType.SET_THEME_WALLPAPER, data = ValueDataBean(value))
        requestAction(GsonUtils.toJson(bean), "设置壁纸")
    }

    /**
     * 设置中控亮度
     * @param value 范围:1-21
     */
    fun setScreenBrightness(value: Int) {
        val bean = RequestSoaBaseBean(protocolId = ProtocolType.SET_SCREEN_BRIGHTNESS, data = ValueDataBean(value))
        requestAction(GsonUtils.toJson(bean), "setScreenBrightness")
    }

    /**
     * 设置蓝牙开关
     * @param value 0关 1开
     */
    fun setBluetoothSwitchStatus(value: Int) {
        val bean = RequestSoaBaseBean(
            protocolId = ProtocolType.SET_BLUETOOTH_SWITCH_STATUS,
            data = ValueDataBean(value)
        )
        requestAction(GsonUtils.toJson(bean), "setBluetoothSwitchStatus")
    }

    /**
     * 设置热点开关
     * @param value 0关 1开
     */
    fun setHotspotSwitchStatus(value: Int) {
        val bean = RequestSoaBaseBean(protocolId = ProtocolType.SET_HOTSPOT_SWITCH_STATUS, data = ValueDataBean(value))
        requestAction(GsonUtils.toJson(bean), "setHotspotSwitchStatus")
    }

    /**
     * 设置wifi开关
     * @param value 0关 1开
     */
    fun setWLANSwitchStatus(value: Int) {
        val bean = RequestSoaBaseBean(protocolId = ProtocolType.SET_WLAN_SWITCH_STATUS, data = ValueDataBean(value))
        requestAction(GsonUtils.toJson(bean), "setWLANSwitchStatus")
    }

    /**
     * 设置移动网络开关
     * @param value 0关 1开
     */
    fun setMobileNetworkSwitchStatus(value: Int) {
        val bean = RequestSoaBaseBean(
            protocolId = ProtocolType.SET_MOBILE_NETWORK_SWITCH_STATUS,
            data = ValueDataBean(value)
        )
        requestAction(GsonUtils.toJson(bean), "setMobileNetworkSwitchStatus")
    }

    /**
     * 获取蓝牙开关状态
     * @return value 0关 1开 异常超时null
     */
    suspend fun getBluetoothSwitchStatus(): getBluetoothSwitchStatus.getBluetoothSwitchStatusOutput? {
        val bean = RequestSoaBaseBean(protocolId = ProtocolType.GET_BLUETOOTH_SWITCH_STATUS, data = null)
        return requestCondition(GsonUtils.toJson(bean), "getBluetoothSwitchStatus")
    }

    /**
     * 获取蓝牙连接状态
     * @return value 0 未连接 1 已连接 异常超时null
     */
    suspend fun getBluetoothConnectionStatus(): getBluetoothConnectionStatus.getBluetoothConnectionStatusOutput? {
        val bean = RequestSoaBaseBean(protocolId = ProtocolType.GET_BLUETOOTH_CONNECTION_STATUS, data = null)
        return requestCondition(GsonUtils.toJson(bean), "getBluetoothConnectionStatus")
    }

    /**
     * 获取热点连接状态
     * @return value 0 未连接 1 已连接 异常超时null
     */
    suspend fun getHotspotConnectionStatus(): getHotspotConnectionStatus.getHotspotConnectionStatusOutput? {
        val bean = RequestSoaBaseBean(protocolId = ProtocolType.GET_HOTSPOT_CONNECTION_STATUS, data = null)
        return requestCondition(GsonUtils.toJson(bean), "getHotspotConnectionStatus")
    }

    /**
     * 获取热点开关状态
     * @return value 0关 1开 异常超时null
     */
    suspend fun getHotspotSwitchStatus(): getHotspotSwitchStatus.getHotspotSwitchStatusOutput? {
        val bean = RequestSoaBaseBean(protocolId = ProtocolType.GET_HOTSPOT_SWITCH_STATUS, data = null)
        return requestCondition(GsonUtils.toJson(bean), "getHotspotSwitchStatus")
    }

    /**
     * 获取wifi开关状态
     * @return value 0关 1开 异常超时null
     */
    suspend fun getWLANSwitchStatus(): getWLANSwitchStatus.getWLANSwitchStatusOutput? {
        val bean = RequestSoaBaseBean(protocolId = ProtocolType.GET_WLAN_SWITCH_STATUS, data = null)
        return requestCondition(GsonUtils.toJson(bean), "getWLANSwitchStatus")
    }

    /**
     * 获取wifi连接状态
     * @return value 0 未连接 1 已连接 异常超时null
     */
    suspend fun getWLANConnectionStatus(): getWLANConnectionStatus.getWLANConnectionStatusOutput? {
        val bean = RequestSoaBaseBean(protocolId = ProtocolType.GET_WLAN_CONNECTION_STATUS, data = null)
        return requestCondition(GsonUtils.toJson(bean), "getWLANConnectionStatus")
    }

    /**
     * 获取移动网络开关状态
     * @return value 0关 1开 异常超时null
     */
    suspend fun getMobileNetworkSwitchStatus(): getMobileNetworkSwitchStatus.getMobileNetworkSwitchStatusOutput? {
        val bean = RequestSoaBaseBean(protocolId = ProtocolType.GET_MOBILE_NETWORK_SWITCH_STATUS, data = null)
        return requestCondition(GsonUtils.toJson(bean), "getMobileNetworkSwitchStatus")
    }
}