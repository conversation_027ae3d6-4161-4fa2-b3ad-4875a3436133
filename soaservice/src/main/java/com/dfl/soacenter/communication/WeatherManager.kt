package com.dfl.soacenter.communication

import ara.ivi.weather.ics.weather.v1.skeleton.getAirQualityStatus
import ara.ivi.weather.ics.weather.v1.skeleton.getOutdoorTemperature
import ara.ivi.weather.ics.weather.v1.skeleton.getWeatherStatus
import com.dfl.androhermas.constant.ServiceConstant
import com.dfl.androhermas.remote.client.ConnectionClient
import com.dfl.android.common.base.BaseSoaManager
import com.dfl.android.common.util.GsonUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.soacenter.R
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.entity.ReqWeatherBean
import com.dfl.soacenter.entity.RequestSoaBaseBean
import com.dfl.soacenter.entity.ResWeatherBean
import com.dfl.soacenter.entity.ResponseSoaBaseBean
import com.dfl.soacenter.entity.WeatherSoaBean
import com.google.gson.Gson
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 *Created by 钟文祥 on 2024/1/17.
 *Describer:天气 应用间通信管理
 */
object WeatherManager : BaseSoaManager() {

    private object ProtocolType {
        /**获取城市天气*/
        const val WEATHER_CITY_WEATHER = 10001

        /**空气质量差*/
        const val AIR_QUALITY_BAD = 0

        /**空气质量中 */
        const val AIR_QUALITY_MIDDLE = 1

        /** 空气质量优良*/
        const val AIR_QUALITY_GOOD = 2

        /** 空气质量其他*/
        const val AIR_QUALITY_OTHERS = 3


        /** 晴天*/
        const val WEATHER_SUNNY = 1

        /**阴天*/
        const val WEATHER_CLOUDY = 2

        /**雨天*/
        const val WEATHER_RAINY = 3

        /** 雪天*/
        const val WEATHER_SNOWY = 4

        /** 雾天*/
        const val WEATHER_FOGGY = 5

        /** 雾霾 */
        const val WEATHER_HAZY = 6

        /** 沙尘*/
        const val WEATHER_SANDDUST = 7

        /** 其他*/
        const val WEATHER_OTHERS = 8
    }

    override fun init() {
        TAG = SoaConstants.SOA_TAG.plus("WeatherManager")
        mClient = ConnectionClient.Builder(
            CommonUtils.getApp(), ServiceConstant.Package.PACKAGE_WEATHER
        )
                .setReconnectMode(ConnectionClient.CONNECT_MODE_NOT_RECONNECT)
                .setThreadPool(1, 1)
                .build()
    }

    /**获取空气质量，转为ccm需要的数据*/
    fun getAirQualityStatus(aqi: Int?): getAirQualityStatus.getAirQualityStatusOutput {
        var status = ProtocolType.AIR_QUALITY_OTHERS
        if (aqi != null) {
            status = if (aqi in (0..50)) {
                ProtocolType.AIR_QUALITY_GOOD
            } else if (aqi in (51..100)) {
                ProtocolType.AIR_QUALITY_MIDDLE
            } else {
                ProtocolType.AIR_QUALITY_BAD
            }
        }
        return getAirQualityStatus.getAirQualityStatusOutput(status.toByte())
    }

    fun getAirQualityDec(aqi: Int?): String? {
        val aqiStr = aqi?.let {
            if (aqi in (0..50)) {
                CommonUtils.getApp().getString(R.string.scene_soa_text_edit_condition_air_excellent)
            } else if (aqi in (51..100)) {
                CommonUtils.getApp().getString(R.string.scene_soa_text_edit_condition_air_good)
            } else if (aqi in (101..200)) {
                CommonUtils.getApp().getString(R.string.scene_soa_text_edit_condition_air_polluted_lightly)
            } else if (aqi in (201..300)) {
                CommonUtils.getApp().getString(R.string.scene_soa_text_edit_condition_air_polluted_moderately)
            } else if (aqi in (301..400)) {
                CommonUtils.getApp().getString(R.string.scene_soa_text_edit_condition_air_polluted_heavily)
            } else if (aqi in (401..500)) {
                CommonUtils.getApp().getString(R.string.scene_soa_text_edit_condition_air_polluted_severely)
            } else if (aqi > 500) {
                CommonUtils.getApp().getString(R.string.scene_soa_text_edit_condition_air_polluted_explosion)
            } else {
                ""
            }
        }
        return aqiStr
    }


    /**室外温度 ,转为ccm需要的数据*/
    fun getOutdoorTemperature(bean: WeatherSoaBean?): getOutdoorTemperature.getOutdoorTemperatureOutput {
        return getOutdoorTemperature.getOutdoorTemperatureOutput((bean?.temp ?: "-100").toByte())
    }

    /**天气状态 ,转为ccm需要的数据*/
    fun getWeatherStatus(bean: WeatherSoaBean?): getWeatherStatus.getWeatherStatusOutput {
        val status: Int = if (bean == null) {
            ProtocolType.WEATHER_OTHERS
        } else {
            when (bean.conditionNum?.toInt()) {
                0 -> ProtocolType.WEATHER_SUNNY
                1, 2, 55 -> ProtocolType.WEATHER_CLOUDY
                in 3..10 -> ProtocolType.WEATHER_RAINY
                in 14..17 -> ProtocolType.WEATHER_SNOWY
                18 -> ProtocolType.WEATHER_FOGGY
                53 -> ProtocolType.WEATHER_HAZY
                20, 29, 30 -> ProtocolType.WEATHER_SANDDUST
                else -> ProtocolType.WEATHER_OTHERS
            }
        }
        return getWeatherStatus.getWeatherStatusOutput(status.toByte())
    }

    /**获取城市天气*/
    suspend fun getCityWeather(req: ReqWeatherBean?): WeatherSoaBean? {
        return suspendCoroutine { continuation ->
            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = ProtocolType.WEATHER_CITY_WEATHER, data = req
                )
            )
            val method = "getCityWeather"
            queryToSendMessage(json, method, { result ->
                val res = Gson().fromJson<ResponseSoaBaseBean<ResWeatherBean>>(
                    result, GsonUtils.type(
                        ResponseSoaBaseBean::class.java, ResWeatherBean::class.java
                    )
                )
                if (res.data?.isSuccess() == true) {
                    val resBean = GsonUtils.fromJson(res.data?.weatherInfo, WeatherSoaBean::class.java)
                    continuation.resume(resBean)
                } else {
                    continuation.resume(null)
                }
            }, {
                continuation.resume(null)
            })
        }
    }

}