package com.dfl.soacenter.communication

import com.dfl.androhermas.constant.ServiceConstant
import com.dfl.androhermas.remote.client.ConnectionClient
import com.dfl.android.common.base.BaseSoaManager
import com.dfl.android.common.util.GsonUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.entity.EnduranceCalculationRequestBean
import com.dfl.soacenter.entity.EnergyCenterRequestDataBean
import com.dfl.soacenter.entity.EnergyCenterResponseDataBean
import com.dfl.soacenter.entity.RequestSoaBaseBean
import com.dfl.soacenter.entity.ResponseSoaBaseBean
import com.google.gson.JsonParseException
import org.json.JSONException
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/01/11
 * desc : 与能源中心应用间通信管理
 * version: 1.0
 */

object EnergyCenterManager : BaseSoaManager() {

    /**
     * 协议id类
     */
    object ProtocolType {
        /**
         *对外放电protocolId
         */
        const val DISCHARGE_SWITCH = 100001

        /**
         *在途预热protocolId
         */
        const val PREHEAT_SWITCH = 100002

        /**设置续航模式*/
        const val SET_ENDURANCE_CALCULATION_TYPE = 1000013
    }

    override fun init() {
        TAG = SoaConstants.SOA_TAG.plus("EnergyCenterManager")
        mClient = ConnectionClient.Builder(CommonUtils.getApp(), ServiceConstant.Package.PACKAGE_ENERGY_CENTER)
                .setReconnectMode(ConnectionClient.CONNECT_MODE_NOT_RECONNECT)
                .setThreadPool(1, 1)
                .build()
    }

    /**
     * 仅用于单元测试
     * @param req 请求体
     */
    suspend fun testRequestAction(req: String, method: String): RequestSoaBaseBean<EnergyCenterResponseDataBean>? {
        return suspendCoroutine {
            queryToSendMessage(req, method, { json ->
                try {
                    val response = GsonUtils.fromJson<RequestSoaBaseBean<EnergyCenterResponseDataBean>>(
                        json,
                        GsonUtils.type(ResponseSoaBaseBean::class.java, EnergyCenterResponseDataBean::class.java)
                    )
                    it.resume(response)
                } catch (e: Exception) {
                    if (e is JsonParseException || e is JSONException) {
                        CommonLogUtils.logE(TAG, "$method json转换失败,json=$json,e=$e")
                    }
                    it.resume(null)
                    e.printStackTrace()
                }
            }, {
                it.resume(null)
            })
        }
    }

    /**
     * 发起能源中心-动作请求
     * @param req 请求体
     * @param method 方法名
     */
    private fun requestAction(req: String, method: String) {
        queryToSendMessage(req, method) { json ->
            try {
                val bean = GsonUtils.fromJson<ResponseSoaBaseBean<EnergyCenterResponseDataBean>>(
                    json,
                    GsonUtils.type(ResponseSoaBaseBean::class.java, EnergyCenterResponseDataBean::class.java)
                )
                if (bean?.data?.resultCode == EnergyCenterResponseDataBean.RESULT_CODE_FAIL) {
                    CommonLogUtils.logE(TAG, "$method 当前不支持设置或返回结果为空, json=$json")
                } else {
                    CommonLogUtils.logI(TAG, "$method 成功, json=$json")
                }
            } catch (e: Exception) {
                if (e is JsonParseException || e is JSONException) {
                    CommonLogUtils.logE(TAG, "$method json转换失败,json=$json,e=$e")
                }
                e.printStackTrace()
            }
        }
    }

    /**
     * 能源中心对外放电通信
     * @param isOpen true打开false关闭
     */
    fun requestDischargeSwitch(isOpen: Boolean) {
        val operaType = if (isOpen) {
            EnergyCenterRequestDataBean.OPERA_TYPE_OPEN
        } else {
            EnergyCenterRequestDataBean.OPERA_TYPE_CLOSE
        }
        val data = EnergyCenterRequestDataBean(
            EnergyCenterRequestDataBean.ACTION_TYPE_DISCHARGE_SWITCH, operaType
        )
        val bean = RequestSoaBaseBean(
            protocolId = ProtocolType.DISCHARGE_SWITCH, messageType = SoaConstants.MESSAGE_TYPE_REQUEST, data = data
        )
        requestAction(GsonUtils.toJson(bean), "requestDischargeSwitch")
    }

    /**
     * 能源中心在途预热通信
     * @param isOpen true打开false关闭
     */
    fun requestPreheatSwitch(isOpen: Boolean) {
        val operaType = if (isOpen) {
            EnergyCenterRequestDataBean.OPERA_TYPE_OPEN
        } else {
            EnergyCenterRequestDataBean.OPERA_TYPE_CLOSE
        }
        val data = EnergyCenterRequestDataBean(
            EnergyCenterRequestDataBean.ACTION_TYPE_PREHEAT_SWITCH, operaType
        )
        val bean = RequestSoaBaseBean(
            protocolId = ProtocolType.PREHEAT_SWITCH, messageType = SoaConstants.MESSAGE_TYPE_REQUEST, data = data
        )
        requestAction(GsonUtils.toJson(bean), "requestPreheatSwitch")
    }

    /**
     * 能源中心设置续航模式
     * @param mode 1标准 2动态
     */
    fun requestEnduranceCalculation(mode: Int?) {
        if (mode == null) return
        val data = EnduranceCalculationRequestBean(mode)
        val bean = RequestSoaBaseBean(
            protocolId = ProtocolType.SET_ENDURANCE_CALCULATION_TYPE,
            messageType = SoaConstants.MESSAGE_TYPE_REQUEST,
            data = data
        )
        requestAction(GsonUtils.toJson(bean), "requestEnduranceCalculation")
    }
}