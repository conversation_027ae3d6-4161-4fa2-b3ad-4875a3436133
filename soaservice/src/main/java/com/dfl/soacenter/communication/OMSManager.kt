package com.dfl.soacenter.communication

import com.dfl.androhermas.remote.client.ConnectionClient
import com.dfl.android.common.base.BaseSoaManager
import com.dfl.android.common.util.GsonUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.entity.OmsChildPositionList
import com.dfl.soacenter.entity.OmsFaceEntryList
import com.dfl.soacenter.entity.OmsLeftoverProblem
import com.dfl.soacenter.entity.OmsPassengerBehaviourList
import com.dfl.soacenter.entity.OmsPassengerDistribution
import com.dfl.soacenter.entity.OmsPassengerEmoticonList
import com.dfl.soacenter.entity.OmsPassengerGenderAgeList
import com.dfl.soacenter.entity.OmsPassengerSleepList
import com.dfl.soacenter.entity.OmsXunFeiEnable
import com.dfl.soacenter.entity.RequestSoaBaseBean
import com.dfl.soacenter.entity.ResponseSoaBaseBean
import com.google.gson.Gson
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 *Created by 钟文祥 on 2024/11/4.
 *Describer: OMS 应用间通信管理  状态条件
 */
object OMSManager : BaseSoaManager() {
    private object ProtocolType {
        /**讯飞是否可用*/
        const val XUNFEI_ENABLE = 10000

        /**遗留*/
        const val LEFTOVER = 10001

        /**儿童位置*/
        const val CHILD_POSITION = 10002

        /**乘客行为*/
        const val PASSENGER_BEHAVIOUR = 10003

        /**乘客睡眠*/
        const val PASSENGER_SLEEP = 10004

        /**人像录入状态*/
        const val FACE_ENTRY = 10005

        /**乘客分布*/
        const val PASSENGER_DISTRIBUTION = 10006

        /**乘客表情*/
        const val PASSENGER_EMOTICON = 10007

        /**乘客性别年龄*/
        const val PASSENGER_GENDER_AGE = 10010
    }

    override fun init() {
        TAG = SoaConstants.SOA_TAG.plus("OMSManager")
        mClient = ConnectionClient.Builder(CommonUtils.getApp(), "com.dfl.oms")
                .setReconnectMode(ConnectionClient.CONNECT_MODE_NOT_RECONNECT).setThreadPool(1, 1).build()
    }


    /**1.1查询讯飞服务是否可用*/
    suspend fun isXunFeiEnable(): OmsXunFeiEnable? {
        return suspendCoroutine { continuation ->
            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = ProtocolType.XUNFEI_ENABLE, data = null
                )
            )
            val method = "isXunFeiEnable"
            queryToSendMessage(json, method, actionSuccess = { result ->
                val res = Gson().fromJson<ResponseSoaBaseBean<OmsXunFeiEnable>>(
                    result, GsonUtils.type(
                        ResponseSoaBaseBean::class.java, OmsXunFeiEnable::class.java
                    )
                )
                continuation.resume(res.data)
            }, actionFail = {
                continuation.resume(null)
            })
        }
    }

    /**1.2查询遗留儿童、宠物、物品*/
    suspend fun getLeftoverProblem(): OmsLeftoverProblem? {
        return suspendCoroutine { continuation ->
            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = ProtocolType.LEFTOVER, data = null
                )
            )
            val method = "getLeftoverProblem"
            queryToSendMessage(json, method, actionSuccess = { result ->
                val res = Gson().fromJson<ResponseSoaBaseBean<OmsLeftoverProblem>>(
                    result, GsonUtils.type(
                        ResponseSoaBaseBean::class.java, OmsLeftoverProblem::class.java
                    )
                )
                continuation.resume(res.data)
            }, actionFail = {
                continuation.resume(null)
            })
        }
    }

    /**1.3获取儿童位置集合*/
    suspend fun getChildPositionList(): OmsChildPositionList? {
        return suspendCoroutine { continuation ->
            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = ProtocolType.CHILD_POSITION, data = null
                )
            )
            val method = "getChildPositionList"
            queryToSendMessage(json, method, actionSuccess = { result ->
                val res = Gson().fromJson<ResponseSoaBaseBean<OmsChildPositionList>>(
                    result, GsonUtils.type(
                        ResponseSoaBaseBean::class.java, OmsChildPositionList::class.java
                    )
                )
                continuation.resume(res.data)
            }, actionFail = {
                continuation.resume(null)
            })
        }
    }


    /**1.4查询乘客行为*/
    suspend fun getPassengerBehaviourList(): OmsPassengerBehaviourList? {
        return suspendCoroutine { continuation ->
            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = ProtocolType.PASSENGER_BEHAVIOUR, data = null
                )
            )
            val method = "getPassengerBehaviourList"
            queryToSendMessage(json, method, actionSuccess = { result ->
                val res = Gson().fromJson<ResponseSoaBaseBean<OmsPassengerBehaviourList>>(
                    result, GsonUtils.type(
                        ResponseSoaBaseBean::class.java, OmsPassengerBehaviourList::class.java
                    )
                )
                continuation.resume(res.data)
            }, actionFail = {
                continuation.resume(null)
            })
        }
    }

    /**1.5查询乘客睡眠*/
    suspend fun getPassengerSleepList(): OmsPassengerSleepList? {
        return suspendCoroutine { continuation ->
            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = ProtocolType.PASSENGER_SLEEP, data = null
                )
            )
            val method = "getPassengerSleepList"
            queryToSendMessage(json, method, actionSuccess = { result ->
                val res = Gson().fromJson<ResponseSoaBaseBean<OmsPassengerSleepList>>(
                    result, GsonUtils.type(
                        ResponseSoaBaseBean::class.java, OmsPassengerSleepList::class.java
                    )
                )
                continuation.resume(res.data)
            }, actionFail = {
                continuation.resume(null)
            })
        }
    }

    /**1.6查询人脸录入状态*/
    suspend fun getFaceEntryList(): OmsFaceEntryList? {
        return suspendCoroutine { continuation ->
            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = ProtocolType.FACE_ENTRY, data = null
                )
            )
            val method = "getFaceEntryList"
            queryToSendMessage(json, method, actionSuccess = { result ->
                val res = Gson().fromJson<ResponseSoaBaseBean<OmsFaceEntryList>>(
                    result, GsonUtils.type(
                        ResponseSoaBaseBean::class.java, OmsFaceEntryList::class.java
                    )
                )
                continuation.resume(res.data)
            }, actionFail = {
                continuation.resume(null)
            })
        }
    }

    /**1.7查询乘客分布状态*/
    suspend fun getPassengerDistribution(): OmsPassengerDistribution? {
        return suspendCoroutine { continuation ->
            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = ProtocolType.PASSENGER_DISTRIBUTION, data = null
                )
            )
            val method = "getPassengerDistribution"
            queryToSendMessage(json, method, actionSuccess = { result ->
                val res = Gson().fromJson<ResponseSoaBaseBean<OmsPassengerDistribution>>(
                    result, GsonUtils.type(
                        ResponseSoaBaseBean::class.java, OmsPassengerDistribution::class.java
                    )
                )
                continuation.resume(res.data)
            }, actionFail = {
                continuation.resume(null)
            })
        }
    }

    //测试
    fun getPassengerDistribution2() {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.PASSENGER_DISTRIBUTION,
                messageType = SoaConstants.MESSAGE_TYPE_DISPATCH,
                data = null
            )
        )
        val method = "getPassengerDistribution"
        queryToSendMessage(json, method, actionSuccess = { result ->
            val res = Gson().fromJson<ResponseSoaBaseBean<OmsPassengerDistribution>>(
                result, GsonUtils.type(
                    ResponseSoaBaseBean::class.java, OmsPassengerDistribution::class.java
                )
            )
        }, actionFail = {})
    }


    /**1.8查询乘客表情*/
    suspend fun getPassengerEmoticon(): OmsPassengerEmoticonList? {
        return suspendCoroutine { continuation ->
            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = ProtocolType.PASSENGER_EMOTICON, data = null
                )
            )
            val method = "getPassengerEmoticon"
            queryToSendMessage(json, method, actionSuccess = { result ->
                val res = Gson().fromJson<ResponseSoaBaseBean<OmsPassengerEmoticonList>>(
                    result, GsonUtils.type(
                        ResponseSoaBaseBean::class.java, OmsPassengerEmoticonList::class.java
                    )
                )
                continuation.resume(res.data)
            }, actionFail = {
                continuation.resume(null)
            })
        }
    }

    /**1.11 主动查询性别年龄*/
    suspend fun getPassengerGenderAge(): OmsPassengerGenderAgeList? {
        return suspendCoroutine { continuation ->
            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = ProtocolType.PASSENGER_GENDER_AGE, data = null
                )
            )
            val method = "getPassengerGenderAge"
            queryToSendMessage(json, method, actionSuccess = { result ->
                val res = Gson().fromJson<ResponseSoaBaseBean<OmsPassengerGenderAgeList>>(
                    result, GsonUtils.type(
                        ResponseSoaBaseBean::class.java, OmsPassengerGenderAgeList::class.java
                    )
                )
                continuation.resume(res.data)
            }, actionFail = {
                continuation.resume(null)
            })
        }
    }
}