package com.dfl.soacenter.communication

import ara.ivi.navigation.ics.Strategy
import com.dfl.androhermas.constant.ServiceConstant
import com.dfl.androhermas.remote.client.ConnectionClient
import com.dfl.android.common.base.BaseSoaManager
import com.dfl.android.common.util.GsonUtils
import com.dfl.android.common.util.JsonUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.entity.CurCityInfo
import com.dfl.soacenter.entity.CurPositionInfo
import com.dfl.soacenter.entity.KeywordBean
import com.dfl.soacenter.entity.LaneLineInfo
import com.dfl.soacenter.entity.NaviRouteStrategy
import com.dfl.soacenter.entity.NviInfo
import com.dfl.soacenter.entity.PoiItem
import com.dfl.soacenter.entity.PolygonBound
import com.dfl.soacenter.entity.ReqFavoriteList
import com.dfl.soacenter.entity.ReqNavi
import com.dfl.soacenter.entity.ReqPlanRoute
import com.dfl.soacenter.entity.RequestSoaBaseBean
import com.dfl.soacenter.entity.ResDistance
import com.dfl.soacenter.entity.ResFavoriteListBean
import com.dfl.soacenter.entity.ResPlanRoute
import com.dfl.soacenter.entity.ResponseSoaBaseBean
import com.dfl.soacenter.entity.RoadInfo
import com.dfl.soacenter.entity.RoutePoi
import com.dfl.soacenter.entity.SearchMapAddressBean
import com.dfl.soacenter.entity.SpeedLimitInfo
import com.google.gson.Gson
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 *Created by 钟文祥 on 2024/1/15.
 *Describer: 导航 应用间通信管理
 */
object NaviManager : BaseSoaManager() {

    private object ProtocolType {
        /**获取剩余时间和距离*/
        const val NVI_INFO = 30006

        /**设置路线偏好*/
        const val NAVI_ROUTE_STRATEGY = 50007

        /**获取收藏点列表*/
        const val NAVI_FAVORITE_LIST = 50005

        /**获取车位当前城市的信息*/
        const val NAVI_CURRENT_CITY_INFO = 30015

        /**查询当前位置*/
        const val NAVI_CURRENT_POSITION_INFO = 20003

        /**获取道路信息*/
        const val NAVI_ROAD_INFO = 30014

        /**获取限速信息*/
        const val NAVI_SPEED_LIMIT = 40014

        /**道路线*/
        const val NAVI_LAND_LINE = 30013

        /**规划路线*/
        const val NAVI_PLAN_ROUTE = 30001

        /**开始导航 停止导航*/
        const val NAVI_START_STOP = 30003

        const val NAVI_DISTANCE = 10005

        /**关键字搜索*/
        const val NAVI_KEY_WORDS_SEARCH = 20006
    }

    override fun init() {
        TAG = SoaConstants.SOA_TAG.plus("NaviManager")
        mClient = ConnectionClient.Builder(CommonUtils.getApp(), ServiceConstant.Package.PACKAGE_NAVI)
                .setReconnectMode(ConnectionClient.CONNECT_MODE_NOT_RECONNECT)
                .setThreadPool(1, 1)
                .build()
    }

    /**当前位置到目标位置的直线距离*/
    suspend fun getDistance(req: PolygonBound): ResDistance? {
        return suspendCoroutine { continuation ->
            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = ProtocolType.NAVI_DISTANCE, data = req
                )
            )
            val method = "getDistance"
            queryToSendMessage(json, method, { result ->
                val res = Gson().fromJson<ResponseSoaBaseBean<ResDistance>>(
                    result, GsonUtils.type(
                        ResponseSoaBaseBean::class.java, ResDistance::class.java
                    )
                )
                val bean = res.data
                if (bean?.isSuccess() == true) {
                    continuation.resume(bean)
                } else {
                    continuation.resume(null)
                }
            }, {
                continuation.resume(null)
            })
        }
    }

    /**获取剩余时间和距离*/
    suspend fun getNviInfo(): NviInfo? {
        return suspendCoroutine { continuation ->
            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = ProtocolType.NVI_INFO, data = null
                )
            )
            val method = "getNviInfo"
            queryToSendMessage(json, method, { result ->
                val jsonObject = JsonUtils.getJSONObject(result, "data", null)
                if (jsonObject.has("remainDistance") && jsonObject.has("remainTime")) {
                    val distance = jsonObject.getLong("remainDistance")
                    val time = jsonObject.getLong("remainTime")
                    continuation.resume(NviInfo(distance, time))
                } else {
                    continuation.resume(null)
                }
            }, {
                continuation.resume(null)
            })
        }
    }

    /**设置路线偏好*/
    fun setRouteStrategy(strategy: Strategy) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.NAVI_ROUTE_STRATEGY,
                data = NaviRouteStrategy(NaviRouteStrategy.convert(strategy))
            )
        )

        val method = "setRouteStrategy"
        actionToSendMessage(json, method)
    }

    /**获取收藏点列表 0:普通收藏点 1:家 2:公司3:全部*/
    suspend fun getFavoriteList(operaType: Int): ResFavoriteListBean? {
        return suspendCancellableCoroutine { continuation ->
            val job = CoroutineScope(Dispatchers.IO).launch {
                val json = GsonUtils.toJson(
                    RequestSoaBaseBean(
                        protocolId = ProtocolType.NAVI_FAVORITE_LIST, data = ReqFavoriteList(operaType)
                    )
                )
                val method = "getFavoriteList"
                queryToSendMessage(json, method, { result ->
                    val res = Gson().fromJson<ResponseSoaBaseBean<ResFavoriteListBean>>(
                        result, GsonUtils.type(
                            ResponseSoaBaseBean::class.java, ResFavoriteListBean::class.java
                        )
                    )
                    val bean = res.data
                    if (bean?.isSuccess() == true) {
                        bean.msgStr = bean.covert()
                        continuation.resume(bean)
                    } else {
                        continuation.resume(null)
                    }
                }, {
                    continuation.resume(null)
                })
            }
            // 当外部协程取消时，取消内部协程
            continuation.invokeOnCancellation {
                CommonLogUtils.logI(TAG, "getFavoriteList取消内部协程")
                job.cancel()
            }
        }
    }

    /**向导航 查询关键字 */
    suspend fun searchKey2Navi(key: String): List<PoiItem>? {
        return suspendCancellableCoroutine { continuation ->
            val job = CoroutineScope(Dispatchers.IO).launch {
                val json = GsonUtils.toJson(
                    RequestSoaBaseBean(
                        protocolId = ProtocolType.NAVI_KEY_WORDS_SEARCH, data = KeywordBean(key)
                    )
                )
                val method = "searchKey2Navi"
                queryToSendMessage(json, method, actionSuccess = { result ->
                    val res = Gson().fromJson<ResponseSoaBaseBean<SearchMapAddressBean>>(
                        result, GsonUtils.type(
                            ResponseSoaBaseBean::class.java, SearchMapAddressBean::class.java
                        )
                    )
                    val bean = res.data
                    if (bean?.isSuccess() == true) {
                        continuation.resume(bean.Pois)

                    } else {
                        continuation.resume(null)
                    }
                }, actionFail = {
                    continuation.resume(null)
                })
            }
            // 当外部协程取消时，取消内部协程
            continuation.invokeOnCancellation {
                CommonLogUtils.logI(TAG, "searchKey2Navi取消内部协程")
                job.cancel()
            }
        }

    }

    /**获取车位当前城市的信息*/
    suspend fun getCurrentCityInfo(): CurCityInfo? {
        return suspendCoroutine { continuation ->
            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = ProtocolType.NAVI_CURRENT_CITY_INFO, data = null
                )
            )
            val method = "getCurrentCityInfo"
            queryToSendMessage(json, method, { result ->
                val res = Gson().fromJson<ResponseSoaBaseBean<CurCityInfo>>(
                    result, GsonUtils.type(
                        ResponseSoaBaseBean::class.java, CurCityInfo::class.java
                    )
                )
                if (res.data?.isSuccess() == true) {
                    continuation.resume(res.data)
                } else {
                    continuation.resume(null)
                }
            }, {
                continuation.resume(null)
            })
        }
    }

    /**获取当前位置信息*/
    suspend fun getCurrentPosition(): CurPositionInfo? {
        return suspendCoroutine { continuation ->
            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = ProtocolType.NAVI_CURRENT_POSITION_INFO, data = null
                )
            )
            val method = "getCurrentPosition"
            queryToSendMessage(json, method, { result ->
                val res = Gson().fromJson<ResponseSoaBaseBean<CurPositionInfo>>(
                    result, GsonUtils.type(
                        ResponseSoaBaseBean::class.java, CurPositionInfo::class.java
                    )
                )
                if (res.data?.isSuccess() == true) {
                    continuation.resume(res.data)
                } else {
                    continuation.resume(null)
                }
            }, {
                continuation.resume(null)
            })
        }
    }

    /**获取道路信息*/
    suspend fun getRoadInfo(): RoadInfo? {
        return suspendCoroutine { continuation ->
            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = ProtocolType.NAVI_ROAD_INFO, data = null
                )
            )
            val method = "getRoadInfo"
            queryToSendMessage(json, method, { result ->
                val res = Gson().fromJson<ResponseSoaBaseBean<RoadInfo>>(
                    result, GsonUtils.type(
                        ResponseSoaBaseBean::class.java, RoadInfo::class.java
                    )
                )
                if (res.data?.isSuccess() == true) {
                    continuation.resume(res.data)
                } else {
                    continuation.resume(null)
                }
            }, {
                continuation.resume(null)
            })
        }
    }

    suspend fun getLaneLineInfo(): LaneLineInfo? {
        return suspendCoroutine { continuation ->
            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = ProtocolType.NAVI_LAND_LINE, data = null
                )
            )
            val method = "getLaneLineInfo"
            queryToSendMessage(json, method, { result ->
                val res = Gson().fromJson<ResponseSoaBaseBean<LaneLineInfo>>(
                    result, GsonUtils.type(
                        ResponseSoaBaseBean::class.java, LaneLineInfo::class.java
                    )
                )
                continuation.resume(res.data)
            }, {
                continuation.resume(null)
            })
        }

    }

    /**获取限速*/
    suspend fun getSpeedLimitInfo(): SpeedLimitInfo? {
        return suspendCoroutine { continuation ->
            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = ProtocolType.NAVI_SPEED_LIMIT, data = null
                )
            )
            val method = "getSpeedLimitInfo"
            queryToSendMessage(json, method, { result ->
                val res = Gson().fromJson<ResponseSoaBaseBean<SpeedLimitInfo>>(
                    result, GsonUtils.type(
                        ResponseSoaBaseBean::class.java, SpeedLimitInfo::class.java
                    )
                )
                if (res.data?.isSuccess() == true) {
                    continuation.resume(res.data)
                } else {
                    continuation.resume(null)
                }
            }, {
                continuation.resume(null)
            })
        }
    }

    /**路线规划*/
    suspend fun planRoute(endPoi: RoutePoi): ResPlanRoute? {
        return planRoute(ReqPlanRoute(endPoi = endPoi))
    }

    /**路线规划*/
    suspend fun planRoute(reqData: ReqPlanRoute?): ResPlanRoute? {
        return suspendCoroutine { continuation ->
            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = ProtocolType.NAVI_PLAN_ROUTE, data = reqData
                )
            )
            val method = "planRoute"
            queryToSendMessage(json, method, actionSuccess = { result ->
                val res = Gson().fromJson<ResponseSoaBaseBean<ResPlanRoute>>(
                    result, GsonUtils.type(
                        ResponseSoaBaseBean::class.java, ResPlanRoute::class.java
                    )
                )
                if (res.data?.isSuccess() == true) {
                    continuation.resume(res.data)
                } else {
                    continuation.resume(null)
                }
            }, {
                continuation.resume(null)
            })

        }
    }

    fun startNavi() {
        startStopNavi(0)
    }

    fun stopNavi() {
        startStopNavi(1)
    }

    /**开始导航，停止导航*/
    private fun startStopNavi(actionType: Int) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.NAVI_START_STOP, data = ReqNavi(actionType)
            )
        )
        val method = "startStopNavi"
        actionToSendMessage(json, method)
    }


    /**高德坐标 两点间距离*/
    fun distance(lat1: Double, lon1: Double, lat2: Double, lon2: Double): Double {

        val lon1Rad = Math.toRadians(lon1)
        val lat1Rad = Math.toRadians(lat1)
        val lon2Rad = Math.toRadians(lon2)
        val lat2Rad = Math.toRadians(lat2)

        val earthRadius = 6371393.0  // 6371393.0  //6371250// 6371393.0 // 地球半径 (以米为单位)
        val latDiff = lat2Rad - lat1Rad
        val lonDiff = lon2Rad - lon1Rad

        val a =
            Math.sin(latDiff / 2) * Math.sin(latDiff / 2) + Math.cos(lat1Rad) * Math.cos(lat2Rad) * Math.sin(lonDiff / 2) * Math.sin(
                lonDiff / 2
            )
        val c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
        val distance = earthRadius * c
        return distance
    }
}