package com.dfl.soacenter.communication

import com.dfl.androhermas.constant.ServiceConstant
import com.dfl.androhermas.remote.client.ConnectionClient
import com.dfl.android.common.base.BaseSoaManager
import com.dfl.android.common.util.GeneralInfoUtils
import com.dfl.android.common.util.GsonUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.soacenter.R
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.entity.AutoRunSceneInfo
import com.dfl.soacenter.entity.MessageCenterBean
import com.dfl.soacenter.entity.RequestSoaBaseBean

/**
 * author : zhushiwei
 * e-mail :
 * time : 2023/01/04
 * desc :消息中心管理
 * version: 1.0
 */
object MessageCenterManager : BaseSoaManager() {

    object ProtocolType {
        /**
         * 发送通知卡片的协议
         * 24.10.09根据Launcher UX2.0转移到消息中心
         */
        const val PROTOCOL_NOTIFY = 7003

        /**
         * 发送消息的协议
         */
        const val PROTOCOL_SEND_MSG = 1001
    }

    override fun init() {
        TAG = SoaConstants.SOA_TAG.plus("MessageCenterManager")
        mClient = ConnectionClient.Builder(
            CommonUtils.getApp(), ServiceConstant.Package.PACKAGE_MESSAGE_CENTER
        )
                .setReconnectMode(ConnectionClient.CONNECT_MODE_NOT_RECONNECT)
                .setThreadPool(1, 1)
                .build()
    }


    fun sendMessage(sceneName: String, message: String) {
        val list = ArrayList<MessageCenterBean>()
        list.add(
            MessageCenterBean(
                appName = CommonUtils.getApp().getString(R.string.scene_soa_app_name),
                title = sceneName,
                content = message,
                releaseTime = System.currentTimeMillis(),
                showPosition = arrayListOf(1, 2)
            )
        )
        val bean = RequestSoaBaseBean(
            protocolId = ProtocolType.PROTOCOL_SEND_MSG,
            requestAuthor = GeneralInfoUtils.getPackageName(),
            data = list
        )
        val json = GsonUtils.toJson(bean)
        queryToSendMessage(json, "sendMessage") {}
    }

    /**
     * 发送通知卡片操作信息
     */
    fun sendAutoRunInfo(autoRunSceneInfo: AutoRunSceneInfo, onSuccess: (String) -> Unit, onFail: () -> Unit) {
        val generalRequestMessage = RequestSoaBaseBean(
            protocolId = ProtocolType.PROTOCOL_NOTIFY,
            requestAuthor = GeneralInfoUtils.getPackageName(),
            data = autoRunSceneInfo
        )
        val message = GsonUtils.toJson(generalRequestMessage)
        queryToSendMessage(message, "sendAutoRunInfo", onSuccess, onFail)
    }

}