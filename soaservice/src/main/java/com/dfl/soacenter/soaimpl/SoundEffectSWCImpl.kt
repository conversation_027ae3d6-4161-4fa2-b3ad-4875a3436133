package com.dfl.soacenter.soaimpl

import android.os.Looper
import ara.ivi.soundeffect.ics.soundeffect.v1.skeleton.SoundEffectSkeleton
import ara.ivi.soundeffect.ics.soundeffect.v1.skeleton.playCarExteriorSound
import ara.ivi.soundeffect.ics.soundeffect.v1.skeleton.playTricksterSound
import ara.ivi.soundeffect.ics.soundeffect.v1.skeleton.setCustomSound
import ara.ivi.soundeffect.ics.soundeffect.v1.skeleton.setImmersiveSoundMode
import ara.ivi.soundeffect.ics.soundeffect.v1.skeleton.setRoar
import ara.ivi.soundeffect.ics.soundeffect.v1.skeleton.setSoundBearing
import ara.ivi.soundeffect.ics.soundeffect.v1.skeleton.setSoundTheme
import ara.ivi.soundeffect.ics.soundeffect.v1.skeleton.setSoundWaveSwitch
import ara.ivi.soundeffect.ics.soundeffect.v1.skeleton.setSoundWaveVolumeType
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.communication.SoundEffectManager
import ics.com.runtime.ErrorCode
import ics.com.runtime.utils.concurrent.Future
import ics.com.runtime.utils.concurrent.Promise

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2023/12/19
 * desc: 声音空间的SOA
 * version:1.0
 */
class SoundEffectSWCImpl(instanceId: String, loop: Looper) : SoundEffectSkeleton(instanceId, loop) {
    companion object {
        private const val TAG = SoaConstants.SOA_TAG.plus("SoundEffectSWCImpl")
    }

    /**
     * 注册监听
     */
    fun registerCallBack() {
        SetStateChangedCallback { i: Int -> CommonLogUtils.logI(TAG, "setStateChangedCallback==>$i") }
        //使用指定按键音音效
        setCustomSound.RegMethodProcess(object : setCustomSound.setCustomSoundHandle {
            override fun OnCall(sound: Byte?): Future<setCustomSound.setCustomSoundOutput> {
                CommonLogUtils.logI(TAG, "OnCall->setCustomSound:$sound")
                return Promise()
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setCustomSound:${errorCode.message}")
            }

        })
        //使用指定声浪
        setRoar.RegMethodProcess(object : setRoar.setRoarHandle {
            override fun OnCall(roar: Byte): Future<setRoar.setRoarOutput> {
                //roar:1GTR，2Fairlady，3赛博朋克
                CommonLogUtils.logI(TAG, "OnCall->setRoar:$roar")
                //目前0GTR，1Fairlady，2赛博朋克
                SoundEffectManager.requestUseVoice(roar.toInt(), "1")
                return Promise()
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setRoar:${errorCode.message}")
            }

        })
        //使用指定音效
        setSoundTheme.RegMethodProcess(object : setSoundTheme.setSoundThemeHandle {
            override fun OnCall(soundTheme: Byte?): Future<setSoundTheme.setSoundThemeOutput> {
                CommonLogUtils.logI(TAG, "OnCall->soundTheme:$soundTheme")
                SoundEffectManager.requestSoundEffect(soundTheme.toString())
                val result = Promise<setSoundTheme.setSoundThemeOutput>()
                result.setValue(ara.ivi.soundeffect.ics.soundeffect.v1.skeleton.setSoundTheme.setSoundThemeOutput(1))
                return result
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setSoundTheme:${errorCode.message}")
            }

        })
        //使用指定的按键生效喇叭方位
        setSoundBearing.RegMethodProcess(object : setSoundBearing.setSoundBearingHandle {
            override fun OnCall(bearing: Byte?): Future<setSoundBearing.setSoundBearingOutput> {
                CommonLogUtils.logI(TAG, "OnCall->setSoundBearing:$bearing")
                return Promise()
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setSoundBearing:${errorCode.message}")
            }

        })
        //播放整蛊音
        playTricksterSound.RegMethodProcess(object : playTricksterSound.playTricksterSoundHandle {
            override fun OnCall(position: Byte, sound: Byte) {
                //ccm整蛊音id 1:喵 2:鹦鹉 3:海豚 4:猫猫祟祟 5:奇幻夏⽇ 6:绿茵狂欢
                CommonLogUtils.logI(TAG, "OnCall->playTricksterSound:position:$position,sound:$sound")
                //通讯整蛊音id 34:喵 35:鹦鹉 36:海豚 37:猫猫祟祟 38:奇幻夏⽇ 39:绿茵狂欢
                SoundEffectManager.requestPlayPrankMusic(sound + 33, position.toString())
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->playTricksterSound:${errorCode.message}")
            }

        })
        //播放车外音
        playCarExteriorSound.RegMethodProcess(object : playCarExteriorSound.playCarExteriorSoundHandle {
            override fun OnCall(sound: Byte?) {
                CommonLogUtils.logI(TAG, "OnCall->playCarExteriorSound:$sound")
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->playCarExteriorSound:${errorCode.message}")
            }

        })
        //设置沉浸音效
        setImmersiveSoundMode.RegMethodProcess(object : setImmersiveSoundMode.setImmersiveSoundModeHandle {
            override fun OnCall(mode: Byte) {
                CommonLogUtils.logI(TAG, "OnCall->setImmersiveSoundMode:$mode")
                SoundEffectManager.requestSoundEffect(mode.toString())
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setImmersiveSoundMode:${errorCode.message}")
            }

        })

        //设置声浪音量
        setSoundWaveVolumeType.RegMethodProcess(object : setSoundWaveVolumeType.setSoundWaveVolumeTypeHandle {
            override fun OnCall(type: Byte) {
                CommonLogUtils.logI(TAG, "OnCall->setSoundWaveVolumeType:$type")
                SoundEffectManager.requestVoiceVolume(type.toString())
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setSoundWaveVolumeType:${errorCode.message}")
            }

        })

        //动感声浪开关
        setSoundWaveSwitch.RegMethodProcess(object : setSoundWaveSwitch.setSoundWaveSwitchHandle {
            override fun OnCall(status: Byte?) {
                CommonLogUtils.logI(TAG, "OnCall->setSoundWaveSwitch:$status")
                //关闭:0 , 打开:1
                if (status != null) {
                    SoundEffectManager.requestSetVoiceSwitch(status.toInt())
                }
            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logE(TAG, "OnError->setSoundWaveSwitch:${errorCode?.message}")
            }

        })
    }
}