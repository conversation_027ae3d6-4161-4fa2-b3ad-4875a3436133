package com.dfl.soacenter.soaimpl

import android.os.Looper
import ara.ivi.navigation.ics.FavoriteType
import ara.ivi.navigation.ics.Strategy
import ara.ivi.navigation.ics.navigation.v1.skeleton.NavigationSkeleton
import ara.ivi.navigation.ics.navigation.v1.skeleton.getNviInfo
import ara.ivi.navigation.ics.navigation.v1.skeleton.getSpeedLimit
import ara.ivi.navigation.ics.navigation.v1.skeleton.getlaneLevel
import ara.ivi.navigation.ics.navigation.v1.skeleton.getlaneLine
import ara.ivi.navigation.ics.navigation.v1.skeleton.onNviToCharger
import ara.ivi.navigation.ics.navigation.v1.skeleton.onNviToFavorite
import ara.ivi.navigation.ics.navigation.v1.skeleton.requestNavi
import ara.ivi.navigation.ics.navigation.v1.skeleton.requestNaviWithRoutePoint
import ara.ivi.navigation.ics.navigation.v1.skeleton.setRouteStrategy
import com.dfl.android.common.util.EncodeUtils
import com.dfl.android.common.util.GsonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.communication.NaviManager
import com.dfl.soacenter.entity.PoiItem
import com.dfl.soacenter.entity.ReqPlanRoute
import com.dfl.soacenter.entity.RoutePoi
import ics.com.runtime.ErrorCode
import ics.com.runtime.utils.concurrent.Future
import ics.com.runtime.utils.concurrent.Promise
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.launch

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2023/12/19
 * desc: 导航的SOA
 * version:1.0
 * update : 钟文祥
 */
class NaviSWCImpl(instanceId: String, loop: Looper) : NavigationSkeleton(instanceId, loop) {
    companion object {
        private const val TAG = SoaConstants.SOA_TAG.plus("NaviSWCImpl")
    }

    /**
     * 注册监听
     */
    fun registerCallBack() {
        SetStateChangedCallback { i: Int ->
            CommonLogUtils.logI(
                TAG, "setStateChangedCallback==>$i"
            )
        }
        //获取剩余时间和距离
        getNviInfo.RegMethodProcess(object : getNviInfo.getNviInfoHandle {
            override fun OnCall(): Future<getNviInfo.getNviInfoOutput> {
                CommonLogUtils.logI(TAG, "OnCall->getNviInfo")
                val promise = Promise<getNviInfo.getNviInfoOutput>()
                CoroutineScope(Dispatchers.IO).launch {
                    val value = async {
                        NaviManager.getNviInfo()
                    }.await()

                    val distance = if (value != null) value.distance?.toDouble() else 100000000.0
                    val seconds = if (value != null) value.time else 86400

                    promise.setValue(
                        ara.ivi.navigation.ics.navigation.v1.skeleton.getNviInfo.getNviInfoOutput(
                            distance, seconds!! / 3600L, seconds % 3600L / 60L
                        )
                    )
                    CommonLogUtils.logD(
                        TAG,
                        "getNviInfo distance:" + distance + " ,h:" + seconds / 3600L + " ，m:" + seconds % 3600L / 60L
                    )
                }
                return promise
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->getNviInfo:${errorCode.message}")
            }
        })

        //设置路线偏好
        setRouteStrategy.RegMethodProcess(object : setRouteStrategy.setRouteStrategyHandle {
            override fun OnCall(strategy: Strategy) {
                CommonLogUtils.logI(TAG, "OnCall->setRouteStrategy:$strategy")
                NaviManager.setRouteStrategy(strategy)
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setRouteStrategy:${errorCode.message}")
            }
        })

        //路口车道线等级
        getlaneLevel.RegMethodProcess(object : getlaneLevel.getlaneLevelHandle {
            override fun OnCall(): Future<getlaneLevel.getlaneLevelOutput> {
                CommonLogUtils.logI(TAG, "OnCall->getlaneLevel")
                val promise = Promise<getlaneLevel.getlaneLevelOutput>()
                CoroutineScope(Dispatchers.IO).launch {
                    val value = async {
                        NaviManager.getRoadInfo()
                    }.await()

                    promise.setValue(
                        ara.ivi.navigation.ics.navigation.v1.skeleton.getlaneLevel.getlaneLevelOutput(
                            value?.roadClass?.toLong()
                        )
                    )
                    CommonLogUtils.logI(TAG, "getlaneLevel 等级:" + value?.roadClass?.toLong())
                }
                return promise
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->getlaneLevel:${errorCode.message}")
            }

        })
        //路口车道线
        getlaneLine.RegMethodProcess(object : getlaneLine.getlaneLineHandle {
            override fun OnCall(): Future<getlaneLine.getlaneLineOutput> {
                CommonLogUtils.logI(TAG, "OnCall->getlaneLine")
                val promise = Promise<getlaneLine.getlaneLineOutput>()
                CoroutineScope(Dispatchers.IO).launch {
                    val value = async {
                        NaviManager.getLaneLineInfo()
                    }.await()

                    promise.setValue(
                        ara.ivi.navigation.ics.navigation.v1.skeleton.getlaneLine.getlaneLineOutput(
                            value?.frontLane.toString()
                        )
                    )
                    CommonLogUtils.logI(TAG, "getlaneLine 道路线:" + value?.frontLane.toString())
                }
                return promise
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->getlaneLevel:${errorCode.message}")
            }

        })
        //路口车速限制
        getSpeedLimit.RegMethodProcess(object : getSpeedLimit.getSpeedLimitHandle {
            override fun OnCall(): Future<getSpeedLimit.getSpeedLimitOutput> {
                CommonLogUtils.logI(TAG, "OnCall->getSpeedLimit")
                val promise = Promise<getSpeedLimit.getSpeedLimitOutput>()
                CoroutineScope(Dispatchers.IO).launch {
                    val value = async {
                        NaviManager.getSpeedLimitInfo()
                    }.await()

                    promise.setValue(
                        ara.ivi.navigation.ics.navigation.v1.skeleton.getSpeedLimit.getSpeedLimitOutput(
                            value?.speed?.toLong()
                        )
                    )
                    CommonLogUtils.logI(TAG, "getSpeedLimit 限速:" + value?.speed?.toLong())
                }
                return promise
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->getlaneLevel:${errorCode.message}")
            }

        })


        //去家 和 去公司
        onNviToFavorite.RegMethodProcess(object : onNviToFavorite.onNviToFavoriteHandle {
            override fun OnCall(favoriteType: FavoriteType) {
                CommonLogUtils.logI(TAG, "OnCall->onNviToFavorite:$favoriteType")
                CoroutineScope(Dispatchers.IO).launch {
                    //1.获取家还是公司
                    val favoriteListBean = async {
                        NaviManager.getFavoriteList(favoriteType.value)
                    }.await()
                    val favoriteItemInfo = favoriteListBean?.protocolFavPoiInfos?.get(0)
                    if (favoriteItemInfo == null) {
                        CommonLogUtils.logE(
                            TAG, "startStopNavi 错误，没有收藏地：" + if (favoriteType.value == 1) "回家" else "公司"
                        )
                        return@launch
                    }

                    //2.发起规划
                    val resPlanRoute = async {
                        NaviManager.planRoute(favoriteItemInfo.toRoutePoi())
                    }.await()
                    if (resPlanRoute == null) {
                        CommonLogUtils.logE(TAG, "planRoute 错误，找不到目的地")
                        return@launch
                    }
                    //3.开始导航
                    if (resPlanRoute.count == 0) {
                        CommonLogUtils.logE(
                            TAG,
                            "startStopNavi 错误，目的地未能规划路线,目的地：${favoriteListBean.protocolFavPoiInfos.get(0).favoriteName}"
                        )
                        return@launch
                    }
                    NaviManager.startNavi()

                }
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->onNviToFavorite:${errorCode.message}")
            }
        })

        //导航到某地 string json,导航目的地的POI
        requestNavi.RegMethodProcess(object : requestNavi.requestNaviHandle {
            override fun OnCall(json: String) {
                CommonLogUtils.logI(TAG, "OnCall->requestNavi1:$json")
                var decodeJson = ""
                try {
                    decodeJson = String(EncodeUtils.base64Decode(json))
                } catch (e: IllegalArgumentException) {
                    CommonLogUtils.logE(TAG, "json格式错误不是base64加密，不执行动作")
                    return
                }
                CommonLogUtils.logI(TAG, "OnCall->requestNavi2:$decodeJson")
                CoroutineScope(Dispatchers.IO).launch {
                    //2.发起规划
                    val resPlanRoute = async {
                        NaviManager.planRoute(GsonUtils.fromJson(decodeJson, ReqPlanRoute::class.java))
                    }.await()
                    if (resPlanRoute == null) {
                        CommonLogUtils.logE(TAG, "planRoute 错误，找不到目的地")
                        return@launch
                    }
                    //3.开始导航
                    if (resPlanRoute.count == 0) {
                        CommonLogUtils.logE(
                            TAG, "startStopNavi 错误，目的地未能规划路线,json：${decodeJson} "
                        )
                        return@launch
                    }
                    NaviManager.startNavi()
                }
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->requestNavi:${errorCode.message}")
            }
        })

        /**
         * 导航到推荐的充电站
         */
        onNviToCharger.RegMethodProcess(object : onNviToCharger.onNviToChargerHandle {
            override fun OnCall(type: Byte?) {
                CommonLogUtils.logI(TAG, "OnCall->onNviToCharger:$type")
                CoroutineScope(Dispatchers.IO).launch {
                    if (type?.toInt() == 1) { //去充电
                        //1.获取周边充电站
                        val searchChargeList = async {
                            NaviManager.searchKey2Navi("充电站")
                        }.await()

                        var endPoiInfo: PoiItem? = null
                        run breaking@{
                            searchChargeList?.forEach { poiItem ->
                                if (poiItem.name != null && poiItem.point?.lat != 0.toDouble() && poiItem.point?.lon != 0.toDouble()) {
                                    endPoiInfo = poiItem
                                    return@breaking
                                }
                            }
                        }
                        if (endPoiInfo == null) {
                            CommonLogUtils.logE(TAG, "onNviToCharger 去充电错误，搜索不到充电站")
                            return@launch
                        }
                        val endRouteBean = RoutePoi(
                            endPoiInfo?.name,
                            endPoiInfo?.addr,
                            endPoiInfo?.point?.lon ?: 0.0,
                            endPoiInfo?.point?.lat ?: 0.0,
                            endPoiInfo?.typeCode,
                            endPoiInfo?.adCode,
                            endPoiInfo?.pid
                        )
                        val planRoute = ReqPlanRoute(endPoi = endRouteBean, midPois = null)

                        //2.发起规划
                        val resPlanRoute = async {
                            NaviManager.planRoute(planRoute)
                        }.await()
                        if (resPlanRoute == null) {
                            CommonLogUtils.logE(TAG, "onNviToCharger planRoute规划错误，找不到目的地")
                            return@launch
                        }
                        //3.开始导航
                        if (resPlanRoute.count == 0) {
                            CommonLogUtils.logE(
                                TAG, "onNviToCharger 错误，去充电目的地未能规划路线,目的地：${
                                    endPoiInfo?.name + " " + endPoiInfo?.addr
                                }"
                            )
                            return@launch
                        }
                        NaviManager.startNavi()

                    }

                }
            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logE(TAG, "OnError->onNviToCharger:${errorCode?.message}")
            }

        })

        //路选规划特殊点：回家/公司+途径点
        requestNaviWithRoutePoint.RegMethodProcess(object : requestNaviWithRoutePoint.requestNaviWithRoutePointHandle {
            override fun OnCall(type: Byte?, routePoint: String?) {
                CommonLogUtils.logI(TAG, "OnCall->requestNaviWithRoutePoint type:$type,routePoint:$routePoint")

                //途经点实体
                var midJson = ""
                try {
                    midJson = String(EncodeUtils.base64Decode(routePoint))
                } catch (e: IllegalArgumentException) {
                    CommonLogUtils.logE(TAG, "requestNaviWithRoutePoint json格式错误不是base64加密，不执行动作")
                    return
                }
                CommonLogUtils.logI(TAG, "OnCall->requestNaviWithRoutePoint:$midJson")
                val midRouteBean = GsonUtils.fromJson(midJson, RoutePoi::class.java)

                CoroutineScope(Dispatchers.IO).launch {
                    //1.获取家还是公司
                    val favoriteListBean = async {
                        NaviManager.getFavoriteList(type?.toInt()!!)
                    }.await()
                    val favoriteItemInfo = favoriteListBean?.protocolFavPoiInfos?.get(0)
                    if (favoriteItemInfo == null) {
                        CommonLogUtils.logE(
                            TAG,
                            "requestNaviWithRoutePoint 错误，没有收藏地：" + if (type?.toInt()!! == 1) "回家" else "公司"
                        )
                        return@launch
                    }

                    //2.生成规划实体
                    val midPois = if (midRouteBean != null) {
                        arrayListOf(midRouteBean)
                    } else {
                        null
                    }
                    val planRoute = ReqPlanRoute(endPoi = favoriteItemInfo.toRoutePoi(), midPois = midPois)


                    //3.发起规划
                    val resPlanRoute = async {
                        NaviManager.planRoute(planRoute)
                    }.await()
                    if (resPlanRoute == null) {
                        CommonLogUtils.logE(TAG, "requestNaviWithRoutePoint 错误，找不到目的地")
                        return@launch
                    }
                    if (resPlanRoute.count == 0) {
                        CommonLogUtils.logE(
                            TAG, "requestNaviWithRoutePoint 错误，目的地未能规划路线,目的地：${
                                favoriteListBean.protocolFavPoiInfos.get(0).favoriteName
                            } ,途经点：${midRouteBean.name}"
                        )
                        return@launch
                    }
                    //4.开始导航
                    NaviManager.startNavi()

                }
            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logE(TAG, "OnError->requestNaviWithRoutePoint:${errorCode?.message}")

            }

        })
    }
}