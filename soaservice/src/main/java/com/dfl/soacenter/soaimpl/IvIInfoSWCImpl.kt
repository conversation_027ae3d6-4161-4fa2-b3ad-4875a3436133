package com.dfl.soacenter.soaimpl

import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import androidx.annotation.RequiresApi
import ara.ivi.iviinf.ics.HolidayInfo
import ara.ivi.iviinf.ics.iviinf.v1.skeleton.IVIInfSkeleton
import ara.ivi.iviinf.ics.iviinf.v1.skeleton.getCurrentDate.getCurrentDateHandle
import ara.ivi.iviinf.ics.iviinf.v1.skeleton.getCurrentDate.getCurrentDateOutput
import ara.ivi.iviinf.ics.iviinf.v1.skeleton.getDistanceToPosition
import ara.ivi.iviinf.ics.iviinf.v1.skeleton.getHolidayInfo.getHolidayInfoHandle
import ara.ivi.iviinf.ics.iviinf.v1.skeleton.getHolidayInfo.getHolidayInfoOutput
import com.dfl.android.common.util.CommonToastUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.soacenter.R
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.communication.NaviManager
import com.dfl.soacenter.entity.HolidayBean
import com.dfl.soacenter.network.NetWorkHelp
import com.dfl.soacenter.network.NetWorkHelp.getHolidayData
import ics.IVIInf_notifyDistanceToPositionWithType_param_t
import ics.com.runtime.ErrorCode
import ics.com.runtime.utils.concurrent.Future
import ics.com.runtime.utils.concurrent.Promise
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeoutOrNull
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale


/**
 * zhushiwei
 * 地理围栏，节假日判断，时间区间（对应soa表格里的【智慧场景】一项）
 * version: 1.0
 * update : 钟文祥
 */
class IvIInfoSWCImpl(instanceId: String, loop: Looper) : IVIInfSkeleton(instanceId, loop) {

    companion object {
        private const val TAG = SoaConstants.SOA_TAG.plus("IvIInfoSWCImpl")

        /**
         * 查询天气预报等的超时时间
         */
        private const val QUERY_TIME_OUT = 5 * 1000L

        /**
         * 关闭多媒体的广播
         */
        private const val ACTION_PAUSE_MEDIA = "com.dfl.smartscene.soa.action.PauseMedia"
        private val dateStampFormat = SimpleDateFormat("yyyy-MM-dd", Locale.CHINA)

    }


    private var mHolidayBean: HolidayBean? = null
    private var mHolidayInfo = HolidayInfo.WORKDAY

    /**
     * 注册监听
     */
    fun registerCallBack() {
        SetStateChangedCallback { i: Int ->
            CommonLogUtils.logI(
                TAG, "setStateChangedCallback==>$i"
            )
        }
        registerCurrentDate()
        registerHolidayInfo()
        registerDistanceToPosition()
    }

    //发送消息给ccm的队列，要求每隔一秒才执行
    private var notifyHandler: Handler? = null

    /** 将距离更新至CCM中*/
    fun notifyToCCM(latitude: Double, longitude: Double, distance: Int, type: Int) {
        if (notifyHandler == null) {
            notifyHandler = object : Handler(Looper.getMainLooper()) {
                @RequiresApi(Build.VERSION_CODES.TIRAMISU)
                override fun handleMessage(msg: Message) {
                    when (msg.what) {
                        1 -> {
                            val value = msg.data.getSerializable(
                                "data"
                            ) as IVIInf_notifyDistanceToPositionWithType_param_t

                            notifyDistanceToPositionWithType.Send(value)
                            Thread.sleep(1000)  //ccm处理时间是几十ms级别的，1s比较稳妥
                        }
                    }
                }
            }
        }

        // 使用Message发送任务
        val msg = Message.obtain()
        msg.what = 1 // 消息类型，自定义
        val bundle = Bundle()
        bundle.putSerializable(
            "data", IVIInf_notifyDistanceToPositionWithType_param_t(
                latitude, longitude, distance, type.toByte()
            )
        ) // 添加数据到Bundle中
        msg.data = bundle
        notifyHandler!!.sendMessage(msg)// 将Bundle设置到Message中

    }


    /**
     * 注册两点间的距离位置计算服务
     */
    private fun registerDistanceToPosition() {
        getDistanceToPosition.RegMethodProcess(object : getDistanceToPosition.getDistanceToPositionHandle {
            override fun OnCall(
                latitude: Double, longitude: Double
            ): Future<getDistanceToPosition.getDistanceToPositionOutput> {
                CommonLogUtils.logI(TAG, "OnCall->getDistanceToPosition:$latitude,$longitude")
                val promise = Promise<getDistanceToPosition.getDistanceToPositionOutput>()
                CoroutineScope(Dispatchers.IO).launch {
                    val cPostion = async {
                        NaviManager.getCurrentPosition()
                    }.await()
                    cPostion?.let {
                        val distance = NaviManager.distance(it.lat!!, it.lon!!, latitude, longitude)
                        CommonLogUtils.logI(TAG, "distance:$distance")
                        promise.setValue(
                            ara.ivi.iviinf.ics.iviinf.v1.skeleton.getDistanceToPosition.getDistanceToPositionOutput(
                                distance.toInt()
                            )
                        )
                    }
                }
                return promise
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "onError->getDistanceToPosition:" + errorCode.message)
            }
        })
    }

    private fun registerHolidayInfo() {
        getHolidayInfo.RegMethodProcess(object : getHolidayInfoHandle {
            override fun OnCall(date: String): Future<getHolidayInfoOutput> {
                CommonLogUtils.logI(TAG, "OnCall->getHolidayInfo:$date")
                //1通过API判断当前是否是周末，2然后获取节假日判断是否是节假日或者是调休日，3最后再集成
                getHolidayStatus()
                //3集成，根据上面两步确定具体是周末节假日还是工作日，分别传HOLIDAY或者WORKDAY
                CommonLogUtils.logI(TAG, "查询到的节假日=====>$mHolidayInfo")
                val promise = Promise<getHolidayInfoOutput>()
                promise.setValue(getHolidayInfoOutput(mHolidayInfo))
                return promise
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "onError->getHolidayInfo:" + errorCode.message)
            }
        })
    }

    private fun registerCurrentDate() {
        getCurrentDate.RegMethodProcess(object : getCurrentDateHandle {
            override fun OnCall(): Future<getCurrentDateOutput> { //返回毫秒
                CommonLogUtils.logI(TAG, "OnCall->getCurrentDate")
                val promise = Promise<getCurrentDateOutput>()
                promise.setValue(getCurrentDateOutput(System.currentTimeMillis()))
                return promise
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "onError->getCurrentDate:" + errorCode.message)
            }
        })
    }


    private fun getHolidayStatus() {
        mHolidayBean = NetWorkHelp.getHoliday()
        if (mHolidayBean == null) {
            runBlocking {
                val result = withTimeoutOrNull(QUERY_TIME_OUT) {
                    val job = CoroutineScope(Dispatchers.IO).launch {
                        mHolidayBean = getHolidayData()
                        dealHolidayData()
                    }
                    job.join()
                }
                if (result == null) {
                    CommonToastUtils.show(
                        CommonUtils.getApp().getString(R.string.scene_soa_toast_query_weather_error)
                    )
                }
            }
        } else {
            dealHolidayData()
        }
    }

    private fun isWeekend(date: Date): Boolean {
        val calendar = Calendar.getInstance()
        calendar.time = date
        val week = calendar.get(Calendar.DAY_OF_WEEK)
        return week == Calendar.SATURDAY || week == Calendar.SUNDAY
    }

    private fun dealHolidayData() {
        CommonLogUtils.logE(TAG, "节假日===>$mHolidayBean")
        //先判断当天是不是周日
        val isWeek = isWeekend(Date())
        //如果是需要判断是不是调休日，如果不是需要判断是不是节假日(1表示休息日0工作日)
        val today = dateStampFormat.format(System.currentTimeMillis())
        var index: Int
        if (isWeek) { //是周末，需要判断下是不是调休日
            index = 1
            mHolidayBean?.rows?.legalWorkdays?.forEach {
                if (it == today) {
                    index = 0
                }
            }
        } else { //不是周末需要判断下是不是节假日
            index = 0
            mHolidayBean?.rows?.legalHolidays?.forEach {
                if (it == today) {
                    index = 1
                }
            }
        }
        mHolidayInfo = if (index == 1) HolidayInfo.HOLIDAY else HolidayInfo.WORKDAY
    }

}