package com.dfl.soacenter.soaimpl

import android.os.Looper
import ara.ivi.weather.ics.weather.v1.skeleton.*
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.communication.NaviManager
import com.dfl.soacenter.communication.TTSManager
import com.dfl.soacenter.communication.WeatherManager
import com.dfl.soacenter.entity.ReqWeatherBean
import ics.com.runtime.ErrorCode
import ics.com.runtime.utils.concurrent.Future
import ics.com.runtime.utils.concurrent.Promise
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.launch

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2023/12/19
 * desc: 天气SOA服务的实现方
 * version:1.0
 * update : 钟文祥
 */
class WeatherSWCImpl(instanceId: String, loop: Looper) : WeatherSkeleton(instanceId, loop) {
    companion object {
        private const val TAG = SoaConstants.SOA_TAG.plus("WeatherSWCImpl")

        /**查询天气预报等的超时时间*/
        //		private const val QUERY_TIME_OUT = 5 * 1000L

    }


    /**
     * 注册SOA回调的监听
     */
    fun registerCallBack() {
        SetStateChangedCallback { i: Int ->
            CommonLogUtils.logI(TAG, "setStateChangedCallback==>$i")
        }
        //注册天气状态监听
        registerWeatherStatus()
        //注册室外温度监听
        registerOutdoorTemperature()
        //注册空气质量监听
        registerAirQuality()

        setCurrentWeatherBroadcast.RegMethodProcess(object :
            setCurrentWeatherBroadcast.setCurrentWeatherBroadcastHandle {
            override fun OnCall() {
                CoroutineScope(Dispatchers.IO).launch {
                    val bean = async {
                        WeatherManager.getCityWeather(ReqWeatherBean("", "", ""))
                    }.await()

                    val content = bean?.let {
                        "${it.cityName}${it.county},今天${it.condition},气温${it.minTem}到${it.maxTem}度,空气质量指数${it.pm25},${
                            WeatherManager.getAirQualityDec(
                                it.pm25?.toInt()
                            )
                        }," + if (it.condition?.contains(
                                    "雨"
                                ) == true
                        ) "出门记得带伞哦" else ""
                    }
                    CommonLogUtils.logI(TAG, "OnCall->setCurrentWeatherBroadcast,天气播报：${content}")
                    TTSManager.getInstance().speakContent(content)
                }
            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logE(
                    TAG, "[scene][method][setCurrentWeatherBroadcast]:onError..." + errorCode?.message
                )
            }
        })
    }

    //注册空气质量监听
    private fun registerAirQuality() {
        getAirQualityStatus.RegMethodProcess(object : getAirQualityStatus.getAirQualityStatusHandle {
            override fun OnCall(): Future<getAirQualityStatus.getAirQualityStatusOutput> {
                CommonLogUtils.logI(TAG, "OnCall->getAirQualityStatus")
                val promise = Promise<getAirQualityStatus.getAirQualityStatusOutput>()

                CoroutineScope(Dispatchers.IO).launch {
                    val city = async {
                        NaviManager.getCurrentCityInfo()
                    }.await()
                    val bean = async {
                        WeatherManager.getCityWeather(ReqWeatherBean(city?.province, city?.cityName, city?.townName))
                    }.await()
                    promise.setValue(WeatherManager.getAirQualityStatus(bean?.pm25?.toInt()))
                }
                return promise
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(
                    TAG, "[scene][method][AirQualityStatus]:onError..." + errorCode.message
                )
            }
        })
    }

    //注册室外温度监听
    private fun registerOutdoorTemperature() {
        getOutdoorTemperature.RegMethodProcess(object : getOutdoorTemperature.getOutdoorTemperatureHandle {
            override fun OnCall(): Future<getOutdoorTemperature.getOutdoorTemperatureOutput> {
                CommonLogUtils.logI(TAG, "OnCall->getOutdoorTemperature")
                val promise = Promise<getOutdoorTemperature.getOutdoorTemperatureOutput>()

                CoroutineScope(Dispatchers.IO).launch {
                    val city = async {
                        NaviManager.getCurrentCityInfo()
                    }.await()
                    val bean = async {
                        WeatherManager.getCityWeather(ReqWeatherBean(city?.province, city?.cityName, city?.townName))
                    }.await()
                    promise.setValue(WeatherManager.getOutdoorTemperature(bean))
                }
                return promise
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(
                    TAG, "[scene][method][OutdoorTemperature]:onError..." + errorCode.message
                )
            }
        })
    }

    //注册天气状态监听
    private fun registerWeatherStatus() {
        getWeatherStatus.RegMethodProcess(object : getWeatherStatus.getWeatherStatusHandle {
            override fun OnCall(): Future<getWeatherStatus.getWeatherStatusOutput> {
                CommonLogUtils.logI(TAG, "OnCall->getWeatherStatus")
                val promise = Promise<getWeatherStatus.getWeatherStatusOutput>()

                CoroutineScope(Dispatchers.IO).launch {
                    val bean = async {
                        WeatherManager.getCityWeather(
                            ReqWeatherBean("", "", "")
                        )
                    }.await()
                    promise.setValue(WeatherManager.getWeatherStatus(bean))
                }
                return promise
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(
                    TAG, "[scene][method][WeatherStatus]:onError..." + errorCode.message
                )
            }
        })
    }


}