package com.dfl.soacenter.soaimpl

import android.os.Looper
import ara.ivi.voice.ics.voice.v1.skeleton.VoiceSkeleton
import ara.ivi.voice.ics.voice.v1.skeleton.setOuterSpeaker
import ara.ivi.voice.ics.voice.v1.skeleton.setTTSBroadcastContent
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.communication.SoundEffectManager
import com.dfl.soacenter.communication.TTSManager
import ics.com.runtime.ErrorCode
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2024/01/17
 * desc:
 * version:1.0
 */
class VoiceSWCImpl(instanceId: String, loop: Looper) : VoiceSkeleton(instanceId, loop) {

    companion object {
        private const val TAG = SoaConstants.SOA_TAG.plus("VoiceSWCImpl")
    }

    /**
     * 注册监听
     */
    fun registerCallBack() {
        SetStateChangedCallback { i: Int -> CommonLogUtils.logI(TAG, "setStateChangedCallback==>$i") }
        setTTSBroadcastContent.RegMethodProcess(object : setTTSBroadcastContent.setTTSBroadcastContentHandle {
            override fun OnCall(content: String?) {
                CommonLogUtils.logI(TAG, "OnCall->setTTSBroadcastContent:$content")
                CoroutineScope(Dispatchers.Default).launch {
                    TTSManager.getInstance().speakContent(content)
                }
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "Error-setTTSBroadcastContent:${errorCode.message}")
            }

        })

        /**
         * 设置大喇叭实时喊话
         */
        setOuterSpeaker.RegMethodProcess(object : setOuterSpeaker.setOuterSpeakerHandle {
            override fun OnCall(mode: Byte?) {
                CommonLogUtils.logI(TAG, "OnCall->setOuterSpeaker:$mode")
                mode?.let {
                    SoundEffectManager.requestOutSideSound(it.toInt())
                }
            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logI(TAG, "Error-setOuterSpeaker:${errorCode?.message}")
            }

        })
    }
}