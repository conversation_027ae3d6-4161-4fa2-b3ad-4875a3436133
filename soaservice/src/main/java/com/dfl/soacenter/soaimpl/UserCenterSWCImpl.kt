package com.dfl.soacenter.soaimpl

import android.os.Looper
import ara.ivi.personalcenter.ics.personalcenter.v1.skeleton.PersonalCenterSkeleton
import ara.ivi.personalcenter.ics.personalcenter.v1.skeleton.setJourneyAnnouncement
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.soacenter.R
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.communication.SoaUserCenterManager
import com.dfl.soacenter.communication.TTSManager
import ics.com.runtime.ErrorCode
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2023/12/19
 * desc: 用户中心的SOA
 * version:1.0
 */
class UserCenterSWCImpl(instanceId: String, loop: Looper) : PersonalCenterSkeleton(instanceId, loop) {
    companion object {
        private const val TAG = SoaConstants.SOA_TAG.plus("UserCenterSWCImpl")
    }

    /**
     * 注册监听
     */
    fun registerCallBack() {
        SetStateChangedCallback { i: Int -> CommonLogUtils.logI(TAG, "setStateChangedCallback==>$i") }
        //行程播报
        setJourneyAnnouncement.RegMethodProcess(object : setJourneyAnnouncement.setJourneyAnnouncementHandle {
            override fun OnCall(journeyInfo: String) {
                CommonLogUtils.logI(TAG, "OnCall->setJourneyAnnouncement:$journeyInfo")
                CoroutineScope(Dispatchers.IO).launch {
                    val scheduleList = async { SoaUserCenterManager.querySchedule() }.await()
                    val message: String
                    val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA)
                    val outputFormat = SimpleDateFormat("HH点mm分", Locale.CHINA)
                    if (scheduleList == null || scheduleList.rows.size == 0) {
                        message = CommonUtils.getString(R.string.scene_soa_text_action_trip_broadcast_no_schedule)
                    } else {
                        val schedule = scheduleList.rows[0]
                        val time = dateFormat.parse(schedule.departureTime ?: "")
                        var timeString = outputFormat.format(time ?: Date())
                        if (timeString.endsWith("00分")) {
                            timeString = timeString.slice(0..2)
                        }
                        val scheduleString = timeString + schedule.note
                        message = if (scheduleList.rows.size == 1) {
                            CommonUtils.getString(
                                R.string.scene_soa_text_action_trip_broadcast_one_schedule,
                                scheduleString
                            )
                        } else {
                            CommonUtils.getString(
                                R.string.scene_soa_text_action_trip_broadcast_more_schedule,
                                scheduleList.rows.size,
                                scheduleString
                            )
                        }
                    }
                    CommonLogUtils.logI(TAG, "setJourneyAnnouncement message: $message")
                    TTSManager.getInstance().speakContent(message)
                }
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setJourneyAnnouncement:${errorCode.message}")
            }

        })
    }
}