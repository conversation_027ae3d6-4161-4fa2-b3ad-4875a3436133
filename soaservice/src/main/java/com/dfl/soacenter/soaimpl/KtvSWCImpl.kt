package com.dfl.soacenter.soaimpl

import android.os.Looper
import ara.ivi.ktv.ics.ktv.v1.skeleton.KTVSkeleton
import ara.ivi.ktv.ics.ktv.v1.skeleton.playKSong
import ara.ivi.ktv.ics.ktv.v1.skeleton.playKSongList
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.soacenter.SoaConstants
import ics.com.runtime.ErrorCode

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2023/12/19
 * desc: ktv的SOA
 * version:1.0
 */
class KtvSWCImpl(instanceId: String, loop: Looper) : KTVSkeleton(instanceId, loop) {
    companion object {
        private const val TAG = SoaConstants.SOA_TAG.plus("BltMusicSWCImpl")
    }

    /**
     * 注册监听
     */
    fun registerCallBack() {
        SetStateChangedCallback { i: Int -> CommonLogUtils.logI(TAG, "setStateChangedCallback==>$i") }
        //K歌指定歌曲
        playKSong.RegMethodProcess(object : playKSong.playKSongHandle {
            override fun OnCall(song: String) {
                CommonLogUtils.logI(TAG, "OnCall->playKSong:$song")

            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->playKSong:${errorCode.message}")
            }

        })
        //K歌指定歌曲列表
        playKSongList.RegMethodProcess(object : playKSongList.playKSongListHandle {
            override fun OnCall(songList: String) {
                CommonLogUtils.logI(TAG, "OnCall->playKSongList:$songList")
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->playKSongList:${errorCode.message}")

            }

        })

    }
}