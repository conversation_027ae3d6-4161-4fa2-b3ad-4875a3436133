package com.dfl.soacenter.soaimpl

import android.content.Intent
import android.os.Looper
import ara.ivi.launcher.ics.launcher.v1.skeleton.*
import com.dfl.android.common.util.ActivityUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.communication.SoaLauncherManager
import ics.com.runtime.ErrorCode

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2023/12/19
 * desc: launcher的SOA
 * version:1.0
 */
class LauncherSWCImpl(instanceId: String, loop: Looper) : LauncherSkeleton(instanceId, loop) {
    companion object {
        private const val TAG = SoaConstants.SOA_TAG.plus("LauncherSWCImpl")
    }

    /**
     * 注册监听
     */
    fun registerCallBack() {
        SetStateChangedCallback { i: Int -> CommonLogUtils.logI(TAG, "setStateChangedCallback==>$i") }
        registerPrimaryAppCallback()
        registerOtherCallback()
    }

    /**
     * 注册原生app的监听回调
     */
    private fun registerPrimaryAppCallback() {
        //打开应用
        openApplication.RegMethodProcess(object : openApplication.openApplicationHandle {
            override fun OnCall(status: Byte, type: Byte) {
                CommonLogUtils.logI(TAG, "OnCall->openApplication:$status,type:$type")
                SoaLauncherManager.openAPP(type.toInt())
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->openApplication:${errorCode.message}")
            }

        })
        //设置屏幕保护
        setScreenStatus.RegMethodProcess(object : setScreenStatus.setScreenStatusHandle {
            override fun OnCall(status: Byte) {
                //打开 : 0,关闭 : 1
                CommonLogUtils.logI(TAG, "OnCall->setScreenStatus:$status")
                SoaLauncherManager.setScreenStatus(status.toInt())
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setScreenStatus:${errorCode.message}")
            }
        })
        //设置熄屏
        setIVIScreenOnOffStatus.RegMethodProcess(object : setIVIScreenOnOffStatus.setIVIScreenOnOffStatusHandle {
            override fun OnCall(status: Byte) {
                //打开 : 0,关闭 : 1
                CommonLogUtils.logI(TAG, "OnCall->setIVIScreenOnOffStatus:$status")
                SoaLauncherManager.setIVIScreenOnOffStatus(1, status.toInt())
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setIVIScreenOnOffStatus:${errorCode.message}")
            }
        })
    }


    /**
     * 注册其他非打开应用的监听
     */
    private fun registerOtherCallback() {
        backDAHome.RegMethodProcess(object : backDAHome.backDAHomeHandle {
            override fun OnCall() {
                CommonLogUtils.logI(TAG, "OnCall->backDAHome")
                backToLauncherHome()
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->backDAHome:${errorCode.message}")
            }

        })
    }

    /**
     * 返回桌面
     */
    fun backToLauncherHome() {
        val homeIntent = Intent(Intent.ACTION_MAIN)
        homeIntent.addCategory(Intent.CATEGORY_HOME)
        ActivityUtils.startActivity(homeIntent)
    }
}