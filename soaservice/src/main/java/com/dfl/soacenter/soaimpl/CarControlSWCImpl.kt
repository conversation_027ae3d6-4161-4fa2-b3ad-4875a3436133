package com.dfl.soacenter.soaimpl

import android.os.Looper
import ara.ivi.carctrl.ics.AromatherapMode
import ara.ivi.carctrl.ics.AromatherapyIntensity
import ara.ivi.carctrl.ics.AtmosphereLightMode
import ara.ivi.carctrl.ics.Position
import ara.ivi.carctrl.ics.PositionMode
import ara.ivi.carctrl.ics.Status
import ara.ivi.carctrl.ics.SwStatus
import ara.ivi.carctrl.ics.ThemeId
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.CarCtrlSkeleton
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.getChildSeatStatus
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.getInCarAirQualityStatus
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setAiBackSeats
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setAirPurifierSwitchStatus
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setAromatherapyMachineConcentrationStatus
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setAromatherapyMachineDispersingMode
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setAromatherapyMachineSwitchStatus
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setAromatherapyMachineWorkingTime
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setAtmosphereLampColorTheme
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setAtmosphereLightBrightness
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setAtmosphereLightColor
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setAtmosphereLightMode
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setAtmosphereLightSOASignalType
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setAtmosphereLightSceneLightingEffectStatus
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setAtmosphereLightStatus
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setChildSeatHeatingStatus
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setChildSeatVentilationStatus
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setDrivingMode
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setEnergyRecoveryMode
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setHudColorTheme
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setHudMode
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setHudStatus
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setInsideLampCustomized
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setInsideLampTheme
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setRearFogLampStatus
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setSayHiHeadlightSignalType
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setSeatMemoryMode
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setSmartAromatherapyMachineConcentration
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setSmartAromatherapyMachineDispersingMode
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setSmartAromatherapyMachineFlavor
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setSmartAromatherapyMachineSwitchStatus
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setSmartAtmosphereLightBrightness
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setSmartAtmosphereLightColor
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setSmartAtmosphereLightMode
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setSmartAtmosphereLightSwitchStatus
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setStarRingLightStatus
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setWirelessCharger
import ara.ivi.carctrl.ics.carctrl.v1.skeleton.setheadlightSOASignalType
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.communication.CarControlManager
import com.dfl.soacenter.entity.CarControlSetStatus
import com.dfl.soacenter.entity.ReqActionAIOTBean2
import com.dfl.soacenter.entity.ReqSeatMemoryMode
import ics.com.runtime.ErrorCode
import ics.com.runtime.utils.concurrent.Future
import ics.com.runtime.utils.concurrent.Promise
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.launch

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2023/12/19
 * desc: 车控的SOA
 * version:1.0
 * update : 钟文祥
 */
class CarControlSWCImpl(instanceId: String, loop: Looper) : CarCtrlSkeleton(instanceId, loop) {
    companion object {
        private const val TAG = SoaConstants.SOA_TAG.plus("CarControlSWCImpl")
    }

    /**
     * 注册监听
     */
    fun registerCallBack() {
        SetStateChangedCallback { i: Int -> CommonLogUtils.logI(TAG, "setStateChangedCallback==>$i") }
        registerAirPurifier() //空气净化器开关
        registerAromatherapy() //香氛开关
        registerVehicle() //注册与车辆相关动作
        registerChildSeat() //儿童座椅加热设置、通风、已离座、已入座
        registerSmartAtmosphereLight() //炫彩氛围灯开关、亮度、模式、颜色
        registerSmartAromatherapyMachine() //等离子香氛开关、模式、浓度、香型
        registerSeat()    //设置座椅记忆
        registerAtmosphere() //设置氛围灯颜色主题 \开关\模式\亮度\颜色\效果状态
        registerLight() //雾灯模式控制 \触发打招呼灯语（大灯）\设置氛围灯SOA灯语 \触发大灯SOA灯语
    }

    /**
     * 注册aiot-空气净化器相关动作
     */
    private fun registerAirPurifier() {
        //空气净化器开关
        setAirPurifierSwitchStatus.RegMethodProcess(object :
            setAirPurifierSwitchStatus.setAirPurifierSwitchStatusHandle {
            override fun OnCall(status: Status) {
                CommonLogUtils.logI(TAG, "OnCall->setAirPurifierSwitchStatus:$status")
                //				CarControlManager.actionAIOT(ReqActionAIOTBean.AirPurifierSwitch(status.value == 1))
                CarControlManager.actionAIOT2(
                    ReqActionAIOTBean2.init(
                        operation = if (status.value == 1) "SET" else "CLOSE", name = "空气净化器"
                    )
                )
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setAirPurifierSwitchStatus:${errorCode.message}")
            }

        })
        //获取车内空气质量
        getInCarAirQualityStatus.RegMethodProcess(object : getInCarAirQualityStatus.getInCarAirQualityStatusHandle {
            override fun OnCall(): Future<getInCarAirQualityStatus.getInCarAirQualityStatusOutput> {
                CommonLogUtils.logI(TAG, "OnCall->getInCarAirQualityStatus")

                val result = Promise<getInCarAirQualityStatus.getInCarAirQualityStatusOutput>()
                CoroutineScope(Dispatchers.IO).launch {
                    val res = async {
                        CarControlManager.getActionAIOT2(
                            ReqActionAIOTBean2.init(
                                operation = "QUERY", name = "空气净化器", mode = "空气质量"
                            )
                        )
                    }.await()
                    val airQuality = if (res != null) when (res.message) {
                        "优" -> 0
                        "良好" -> 1
                        "差" -> 2
                        else -> -1
                    } else -1
                    result.setValue(
                        ara.ivi.carctrl.ics.carctrl.v1.skeleton.getInCarAirQualityStatus.getInCarAirQualityStatusOutput(
                            airQuality.toByte()
                        )
                    )
                }

                return result
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->getInCarAirQualityStatus:${errorCode.message}")

            }
        })
    }

    /**
     * 注册香氛机相关动作
     */
    private fun registerAromatherapy() {
        //香氛开关
        setAromatherapyMachineSwitchStatus.RegMethodProcess(object :
            setAromatherapyMachineSwitchStatus.setAromatherapyMachineSwitchStatusHandle {
            override fun OnCall(status: Status) {
                CommonLogUtils.logI(TAG, "OnCall->setAromatherapyMachineSwitchStatus:$status")
                //				CarControlManager.actionAIOT(ReqActionAIOTBean.AromatherapyMachineSwitch(status.value == 1))
                CarControlManager.actionAIOT2(
                    ReqActionAIOTBean2.init(
                        operation = if (status.value == 1) "SET" else "CLOSE", name = "香氛"
                    )
                )
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setAromatherapyMachineSwitchStatus:${errorCode.message}")
            }

        })
        //香氛机工作模式
        setAromatherapyMachineDispersingMode.RegMethodProcess(object :
            setAromatherapyMachineDispersingMode.setAromatherapyMachineDispersingModeHandle {
            override fun OnCall(aromatherapyMode: AromatherapMode) {
                CommonLogUtils.logI(TAG, "OnCall->setAromatherapyMachineDispersingMode:$aromatherapyMode")
                //				CarControlManager.actionAIOT(ReqActionAIOTBean.AromatherapyMachineMode(aromatherapyMode))
                CarControlManager.actionAIOT2(
                    ReqActionAIOTBean2.init(
                        operation = "SET",
                        name = "香氛",
                        mode = "散香模式",
                        modeValue = if (aromatherapyMode == AromatherapMode.CONTINUOUS) "持续模式" else "间隔模式"
                    )
                )
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setAromatherapyMachineDispersingMode:${errorCode.message}")
            }

        })
        //香氛机连续工作时间
        setAromatherapyMachineWorkingTime.RegMethodProcess(object :
            setAromatherapyMachineWorkingTime.setAromatherapyMachineWorkingTimeHandle {
            override fun OnCall(time: Short) {
                CommonLogUtils.logI(TAG, "OnCall->setAromatherapyMachineWorkingTime:$time")
                //				CarControlManager.actionAIOT(ReqActionAIOTBean.AromatherapyMachineWorkingTime(time.toInt()))
                CarControlManager.actionAIOT2(
                    ReqActionAIOTBean2.init(
                        operation = "SET", name = "香氛", mode = "使用时长", minute = time.toString(), hour = ""
                    )
                )
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setAromatherapyMachineWorkingTime:${errorCode.message}")
            }

        })
        //香氛机浓度
        setAromatherapyMachineConcentrationStatus.RegMethodProcess(object :
            setAromatherapyMachineConcentrationStatus.setAromatherapyMachineConcentrationStatusHandle {
            override fun OnCall(status: AromatherapyIntensity) {
                CommonLogUtils.logI(TAG, "OnCall->setAromatherapyMachineConcentrationStatus:$status")
                //				CarControlManager.actionAIOT(ReqActionAIOTBean.AromatherapyMachineConcentration(status))
                CarControlManager.actionAIOT2(
                    ReqActionAIOTBean2.init(
                        operation = "SET",
                        name = "香氛",
                        mode = "香氛浓度",
                        modeValue = if (status == AromatherapyIntensity.STRONG_RICH) "强" else "弱"
                    )
                )
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setAromatherapyMachineConcentrationStatus:${errorCode.message}")
            }

        })

    }

    /**
     * 注册与车辆相关动作
     */
    private fun registerVehicle() {

        /**
         * ccm驾驶模式：舒适 : 1, 运动: 2, 标准: 3,  自定义 : 4,  AI专属 : 5
         */
        setDrivingMode.RegMethodProcess(object : setDrivingMode.setDrivingModeHandle {
            override fun OnCall(mode: Byte) {
                CommonLogUtils.logI(TAG, "OnCall->setDrivingMode:$mode")
                CarControlManager.setDrivingMode(
                    CarControlSetStatus(
                        value = when (mode.toInt()) {
                            1 -> 1
                            2 -> 3
                            3 -> 2
                            4 -> 5
                            5 -> 4
                            else -> 0
                        }
                    )
                )
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setDrivingMode:${errorCode.message}")
            }
        })
        //能量回收机制
        setEnergyRecoveryMode.RegMethodProcess(object : setEnergyRecoveryMode.setEnergyRecoveryModeHandle {
            override fun OnCall(mode: Byte) {
                CommonLogUtils.logI(TAG, "OnCall->setEnergyRecoveryMode:$mode")
                CarControlManager.setEnergyRecoveryMode(mode)
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setEnergyRecoveryMode:${errorCode.message}")
            }
        })
    }

    /**注册aiot-儿童座椅相关动作*/
    private fun registerChildSeat() {
        //儿童座椅加热设置
        setChildSeatHeatingStatus.RegMethodProcess(object : setChildSeatHeatingStatus.setChildSeatHeatingStatusHandle {
            override fun OnCall(swStatus: SwStatus) {
                CommonLogUtils.logI(TAG, "OnCall->setChildSeatHeatingStatus:$swStatus")
                //				CarControlManager.setChildStatus(1, swStatus)
                CarControlManager.actionAIOT2(
                    ReqActionAIOTBean2.init(
                        operation = "SET", name = "智能儿童座椅", mode = "座椅加热", modeValue = when (swStatus) {
                            SwStatus.OFF -> ReqActionAIOTBean2.OFF
                            SwStatus.LOW -> ReqActionAIOTBean2.WEAK
                            SwStatus.MIDDLE -> ReqActionAIOTBean2.MEDIUM
                            SwStatus.HIGH -> ReqActionAIOTBean2.STRONG
                        }
                    )
                )
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setChildSeatHeatingStatus:${errorCode.message}")
            }
        })
        //儿童座椅通风设置
        setChildSeatVentilationStatus.RegMethodProcess(object :
            setChildSeatVentilationStatus.setChildSeatVentilationStatusHandle {
            override fun OnCall(swStatus: SwStatus) {
                CommonLogUtils.logI(TAG, "OnCall->setChildSeatVentilationStatus:$swStatus")
                //				CarControlManager.setChildStatus(0, swStatus)
                CarControlManager.actionAIOT2(
                    ReqActionAIOTBean2.init(
                        operation = "SET", name = "智能儿童座椅", mode = "座椅通风", modeValue = when (swStatus) {
                            SwStatus.OFF -> ReqActionAIOTBean2.OFF
                            SwStatus.LOW -> ReqActionAIOTBean2.WEAK
                            SwStatus.MIDDLE -> ReqActionAIOTBean2.MEDIUM
                            SwStatus.HIGH -> ReqActionAIOTBean2.STRONG
                        }
                    )
                )
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setChildSeatVentilationStatus:${errorCode.message}")
            }

        })
        //儿童座椅检测儿童已离座、已入座
        getChildSeatStatus.RegMethodProcess(object : getChildSeatStatus.getChildSeatStatusHandle {
            override fun OnCall(): Future<getChildSeatStatus.getChildSeatStatusOutput> {
                CommonLogUtils.logI(TAG, "OnCall->getChildSeatStatus")

                val result = Promise<getChildSeatStatus.getChildSeatStatusOutput>()
                CoroutineScope(Dispatchers.IO).launch {
                    val res = async {
                        CarControlManager.getChildSeatStatus()
                    }.await()
                    val value = if (res != null) res.value?.toByte() else -1
                    result.setValue(
                        ara.ivi.carctrl.ics.carctrl.v1.skeleton.getChildSeatStatus.getChildSeatStatusOutput(
                            value
                        )
                    )
                }
                return result
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->getChildSeatStatus:${errorCode.message}")
            }

        })
    }

    /** 注册座椅相关动作 */
    private fun registerSeat() {
        //设置座椅记忆
        setSeatMemoryMode.RegMethodProcess(object : setSeatMemoryMode.setSeatMemoryModeHandle {
            override fun OnCall(position: Position, positionMode: PositionMode) {
                CommonLogUtils.logI(TAG, "OnCall->setSeatMemoryMode:position=$position,positionMode=$positionMode")
                CarControlManager.setSeatMemoryMode(ReqSeatMemoryMode(position.value, positionMode.value))
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setSeatMemoryMode:${errorCode.message}")
            }
        })
    }

    /**注册aiot炫彩氛围灯相关动作*/
    private fun registerSmartAtmosphereLight() {
        //设置炫彩氛围灯开关
        setSmartAtmosphereLightSwitchStatus.RegMethodProcess(object :
            setSmartAtmosphereLightSwitchStatus.setSmartAtmosphereLightSwitchStatusHandle {
            override fun OnCall(status: Byte?) {
                CommonLogUtils.logI(TAG, "OnCall->setSmartAtmosphereLightSwitchStatus:$status")
                //1:打开  0:关闭
            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logE(TAG, "OnError->setSmartAtmosphereLightSwitchStatus:${errorCode?.message}")
            }
        })
        //设置炫彩氛围灯亮度
        setSmartAtmosphereLightBrightness.RegMethodProcess(object :
            setSmartAtmosphereLightBrightness.setSmartAtmosphereLightBrightnessHandle {
            override fun OnCall(brightness: Byte?) {
                CommonLogUtils.logI(TAG, "OnCall->setSmartAtmosphereLightBrightness:$brightness")
                //[1, 5] 共5档
            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logE(TAG, "OnError->setSmartAtmosphereLightBrightness:${errorCode?.message}")
            }

        })
        //设置炫彩氛围灯模式
        setSmartAtmosphereLightMode.RegMethodProcess(object :
            setSmartAtmosphereLightMode.setSmartAtmosphereLightModeHandle {
            override fun OnCall(mode: Byte?) {
                CommonLogUtils.logI(TAG, "OnCall->setSmartAtmosphereLightMode:$mode")
                // 1 常亮 2 呼吸 3 音乐律动
            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logE(TAG, "OnError->setSmartAtmosphereLightMode:${errorCode?.message}")
            }

        })
        //设置炫彩氛围灯颜色
        setSmartAtmosphereLightColor.RegMethodProcess(object :
            setSmartAtmosphereLightColor.setSmartAtmosphereLightColorHandle {
            override fun OnCall(themeId: Byte?, detailedColor: Byte?) {
                CommonLogUtils.logI(
                    TAG, "OnCall->setSmartAtmosphereLightColor themeId:$themeId,detailedColor:$detailedColor"
                )
                //int themeId：1 活力橙 2 静谧蓝 3 迷幻紫 4 魅力红
                // int detailedColor：直接给color值（32色）
            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logE(TAG, "OnError->setSmartAtmosphereLightColor:${errorCode?.message}")
            }

        })

    }

    /**注册aiot等离子香氛相关动作*/
    private fun registerSmartAromatherapyMachine() {
        //设置等离子香氛开关
        setSmartAromatherapyMachineSwitchStatus.RegMethodProcess(object :
            setSmartAromatherapyMachineSwitchStatus.setSmartAromatherapyMachineSwitchStatusHandle {
            override fun OnCall(status: Byte?) {
                CommonLogUtils.logI(TAG, "OnCall->setSmartAromatherapyMachineSwitchStatus:$status")
                //1:打开  0:关闭
            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logE(TAG, "OnError->setSmartAromatherapyMachineSwitchStatus:${errorCode?.message}")
            }
        })
        //设置等离子香氛浓度
        setSmartAromatherapyMachineConcentration.RegMethodProcess(object :
            setSmartAromatherapyMachineConcentration.setSmartAromatherapyMachineConcentrationHandle {
            override fun OnCall(status: Byte?) {
                CommonLogUtils.logI(TAG, "OnCall->setSmartAromatherapyMachineConcentration:$status")
                //1 清新 2 浓香 3 淡香
            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logE(TAG, "OnError->setSmartAromatherapyMachineConcentration:${errorCode?.message}")
            }

        })
        //设置等离子香氛模式
        setSmartAromatherapyMachineDispersingMode.RegMethodProcess(object :
            setSmartAromatherapyMachineDispersingMode.setSmartAromatherapyMachineDispersingModeHandle {
            override fun OnCall(mode: Byte?) {
                CommonLogUtils.logI(TAG, "OnCall->setSmartAromatherapyMachineDispersingMode:$mode")
                //1 持续 2 间隔
            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logE(TAG, "OnError->setSmartAromatherapyMachineDispersingMode:${errorCode?.message}")
            }

        })
        //设置等离子香氛香型
        setSmartAromatherapyMachineFlavor.RegMethodProcess(object :
            setSmartAromatherapyMachineFlavor.setSmartAromatherapyMachineFlavorHandle {
            override fun OnCall(flavor: Byte?) {
                CommonLogUtils.logI(TAG, "OnCall->setSmartAromatherapyMachineFlavor:$flavor")
                //1 东方 2 松香 3 茶韵
            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logE(TAG, "OnError->setSmartAromatherapyMachineFlavor:${errorCode?.message}")
            }

        })
    }

    /** 注册氛围灯相关动作*/
    private fun registerAtmosphere() {
        //设置氛围灯颜色主题
        setAtmosphereLampColorTheme.RegMethodProcess(object :
            setAtmosphereLampColorTheme.setAtmosphereLampColorThemeHandle {
            override fun OnCall(themeId: ThemeId) {
                CommonLogUtils.logI(TAG, "OnCall->setAtmosphereLampColorTheme:$themeId")
                //0：暖阳，1：海风，2：星光 3：森林，4：白昼，5：晚风 6：自定义
                CarControlManager.setAtmosphereLampColorTheme(themeId) //里面有-1
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setAtmosphereLampColorTheme:${errorCode.message}")
            }

        })
        //设置氛围灯开关
        setAtmosphereLightStatus.RegMethodProcess(object : setAtmosphereLightStatus.setAtmosphereLightStatusHandle {
            override fun OnCall(status: Status) {
                CommonLogUtils.logI(TAG, "OnCall->setAtmosphereLightStatus:$status")
                //1:打开  2:关闭
                CarControlManager.setAtmosphereLightStatus(status)
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setAtmosphereLightStatus:${errorCode.message}")
            }

        })
        //设置氛围灯模式
        setAtmosphereLightMode.RegMethodProcess(object : setAtmosphereLightMode.setAtmosphereLightModeHandle {
            override fun OnCall(atmosphereLightMode: AtmosphereLightMode) {
                CommonLogUtils.logI(TAG, "OnCall->setAtmosphereLightMode:$atmosphereLightMode")
                //0关闭1常亮2呼吸3律动4声浪
                CarControlManager.setAtmosphereLightMode(
                    when (atmosphereLightMode.value) {
                        3 -> 4
                        4 -> 3
                        else -> atmosphereLightMode.value
                    }
                )
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setAtmosphereLightMode:${errorCode.message}")
            }

        })
        //设置氛围灯亮度
        setAtmosphereLightBrightness.RegMethodProcess(object :
            setAtmosphereLightBrightness.setAtmosphereLightBrightnessHandle {
            override fun OnCall(brightness: Short) { //brightness:[1-8]
                CommonLogUtils.logI(TAG, "OnCall->setAtmosphereLightBrightness:$brightness")
                //0-7
                CarControlManager.setAtmosphereLightBrightness(brightness.toInt())
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setAtmosphereLightBrightness:${errorCode.message}")
            }

        })

        //设置氛围灯自定义颜色
        setAtmosphereLightColor.RegMethodProcess(object : setAtmosphereLightColor.setAtmosphereLightColorHandle {
            override fun OnCall(color: Long) {
                CommonLogUtils.logI(TAG, "OnCall->setAtmosphereLightColor:$color")
                CarControlManager.setAtmosphereLightColor(color)
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setAtmosphereLightColor:${errorCode.message}")
            }

        })

        //设置氛围灯场景功能联动状态
        setAtmosphereLightSceneLightingEffectStatus.RegMethodProcess(object :
            setAtmosphereLightSceneLightingEffectStatus.setAtmosphereLightSceneLightingEffectStatusHandle {
            override fun OnCall(mode: Byte, status: Status) {
                CommonLogUtils.logI(
                    TAG, "OnCall->setAtmosphereLightSceneLightingEffectStatus:mode=$mode,status=$status"
                )
                //0 关闭 1 打开  / 1 语音唤醒 2 KTV人声律动 3 驾驶模式切换 4 空调升降温 5 开门预警  6上电启动
                CarControlManager.setAtmosphereLightSceneLightingEffectStatus(
                    status.value, when (mode.toInt()) {
                        3 -> 1
                        4 -> 2
                        1 -> 3
                        2 -> 4
                        5 -> 5
                        6 -> 6
                        else -> 0
                    }
                )
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setAtmosphereLightSceneLightingEffectStatus:${errorCode.message}")
            }

        })
    }

    /** 注册灯光相关动作*/
    private fun registerLight() {
        //雾灯模式控制
        setRearFogLampStatus.RegMethodProcess(object : setRearFogLampStatus.setRearFogLampStatusHandle {
            override fun OnCall(status: Status) {
                CommonLogUtils.logI(TAG, "OnCall->setRearFogLampStatus:status=$status")
                //0 关闭 1打开
                CarControlManager.setRearFogLampStatus(status)
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setRearFogLampStatus:${errorCode.message}")
            }
        })
        //触发打招呼灯语（大灯）
        setSayHiHeadlightSignalType.RegMethodProcess(object :
            setSayHiHeadlightSignalType.setSayHiHeadlightSignalTypeHandle {
            override fun OnCall(type: Byte) {
                CommonLogUtils.logI(TAG, "OnCall->setSayHiHeadlightSignalType:type=$type")
                // 5 Nissan 6 Hi 7 叮叮 8 喵喵 9 钵
                CarControlManager.setSayHiHeadlightSignalType(type.toInt() + 4)
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setSayHiHeadlightSignalType:${errorCode.message}")
            }
        })
        //设置氛围灯SOA主题灯效
        setAtmosphereLightSOASignalType.RegMethodProcess(object :
            setAtmosphereLightSOASignalType.setAtmosphereLightSOASignalTypeHandle {
            override fun OnCall(type: Byte, status: Byte) {
                CommonLogUtils.logI(TAG, "OnCall->setAtmosphereLightSOASignalType:status=$status,type=$type")
                // 0停止 1开始     春节主题 : 1, 元旦主题 : 2,  生日快乐 : 3,  Nissan主题: 4,  浪漫主题: 5
                CarControlManager.setCarInSoa(
                    status.toInt(), when (type.toInt()) {
                        3 -> 1
                        4 -> 2
                        1 -> 3
                        2 -> 4
                        5 -> 5
                        else -> 0
                    }
                )
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setAtmosphereLightSOASignalType:${errorCode.message}")
            }
        })
        //触发大灯SOA灯语
        setheadlightSOASignalType.RegMethodProcess(object : setheadlightSOASignalType.setheadlightSOASignalTypeHandle {
            override fun OnCall(type: Byte, status: Byte) {
                CommonLogUtils.logI(TAG, "OnCall->setheadlightSOASignalType:status=$status,type=$type")
                //1:打开  2：关闭  /  1:春节 2:元旦 3:生日 4：Nissan主题 5:浪漫
                CarControlManager.setCarOutSoa(
                    when (status.toInt()) {
                        0 -> 2
                        else -> 1
                    }, type.toInt()
                )
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setheadlightSOASignalType:${errorCode.message}")
            }
        })

        //室内灯主题
        setInsideLampTheme.RegMethodProcess(object : setInsideLampTheme.setInsideLampThemeHandle {
            override fun OnCall(theme: Byte) {
                CommonLogUtils.logI(TAG, "OnCall->setInsideLampTheme:status=$theme")
                //1-温馨，2-冷白，3-标准，4-自然  改为 1护眼 2舒适 3清冷 4清亮
                CarControlManager.setInsideLampTheme(theme.toInt())
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setInsideLampTheme:${errorCode.message}")
            }
        })

        //设置星环灯状态
        setStarRingLightStatus.RegMethodProcess(object : setStarRingLightStatus.setStarRingLightStatusHandle {
            override fun OnCall(status: Byte) {
                CommonLogUtils.logI(TAG, "OnCall->setStarRingLightStatus:status=$status")
                //0 关闭 1 打开
                CarControlManager.setStarRingLightStatus(status.toInt())
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setStarRingLightStatus:${errorCode.message}")
            }
        })

        //设置室内灯自定义
        setInsideLampCustomized.RegMethodProcess(object : setInsideLampCustomized.setInsideLampCustomizedHandle {
            override fun OnCall(brightness: Byte, temperature: Byte) {
                CommonLogUtils.logI(
                    TAG, "OnCall->setInsideLampCustomized:brightness=$brightness, temperature=$temperature"
                )
                //brightness temperature均为[0,100],默认50
                CarControlManager.setReadLampCustomSet(brightness.toInt(), temperature.toInt(), 0)
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setInsideLampCustomized:${errorCode.message}")
            }

        })

        /**
         * 设置无线充电状态
         */
        setWirelessCharger.RegMethodProcess(object : setWirelessCharger.setWirelessChargerHandle {
            override fun OnCall(status: Byte?) {
                CommonLogUtils.logI(TAG, "OnCall->setWirelessCharger:status=$status")
                status?.let {
                    CarControlManager.setWirelessCharger(it.toInt())
                }
            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logE(TAG, "OnError->setWirelessCharger:${errorCode?.message}")
            }
        })

        /**
         * 设置后排尊享
         */
        setAiBackSeats.RegMethodProcess(object : setAiBackSeats.setAiBackSeatsHandle {
            override fun OnCall(status: Byte?) {
                CommonLogUtils.logI(TAG, "OnCall->setAiBackSeats:status=$status")
                status?.let {
                    CarControlManager.setSeatMemoryMode(ReqSeatMemoryMode(2, 6))
                }

            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logE(TAG, "OnError->setAiBackSeats:${errorCode?.message}")
            }
        })

        //设置hud开关
        setHudStatus.RegMethodProcess(object : setHudStatus.setHudStatusHandle {
            override fun OnCall(status: Byte?) {
                TODO("Not yet implemented")
            }

            override fun OnError(errorCode: ErrorCode?) {
                TODO("Not yet implemented")
            }
        })

        //设置hud模式
        setHudMode.RegMethodProcess(object : setHudMode.setHudModeHandle {
            override fun OnCall(mode: Byte?) {
                TODO("Not yet implemented")
            }

            override fun OnError(errorCode: ErrorCode?) {
                TODO("Not yet implemented")
            }

        })

        //设置hud颜色主题
        setHudColorTheme.RegMethodProcess(object : setHudColorTheme.setHudColorThemeHandle {
            override fun OnCall(theme: Byte?) {
                TODO("Not yet implemented")
            }

            override fun OnError(errorCode: ErrorCode?) {
                TODO("Not yet implemented")
            }

        })
    }

    //测试的
    //	CarControlManager.actionAIOT(ReqActionAIOTBean.AirPurifierSwitch(true))
    //	CarControlManager.actionAIOT(ReqActionAIOTBean.AromatherapyMachineSwitch(true))
    //	CarControlManager.actionAIOT(ReqActionAIOTBean.AromatherapyMachineMode(AromatherapMode.CONTINUOUS))
    //	CarControlManager.actionAIOT(ReqActionAIOTBean.AromatherapyMachineWorkingTime(5))
    //	CarControlManager.actionAIOT(
    //	ReqActionAIOTBean.AromatherapyMachineConcentration(
    //	AromatherapyIntensity.WEAK_SIMPLE
    //	)
    //	)
    //	CarControlManager.setDrivingMode(CarControlSetStatus(value = 1))
    //	CarControlManager.setEnergyRecoveryMode(1)
    //	CarControlManager.setChildStatus(1, SwStatus.MIDDLE)
    //	CarControlManager.setChildStatus(0, SwStatus.MIDDLE)
    //	CarControlManager.actionNeckPillow(ReqNeckPillowAndLumbarActionBean(switch = 1))
    //	CarControlManager.actionNeckPillow(ReqNeckPillowAndLumbarActionBean(massageStrength = 2))
    //	CarControlManager.actionNeckPillow(ReqNeckPillowAndLumbarActionBean(massageType = 1))
    //	CarControlManager.actionNeckPillow(ReqNeckPillowAndLumbarActionBean(massageTime = 2))
    //	CarControlManager.actionNeckPillow(ReqNeckPillowAndLumbarActionBean(massageTemp = 1))
    //	CarControlManager.actionLumbar(ReqNeckPillowAndLumbarActionBean(switch = 1))
    //	CarControlManager.actionLumbar(ReqNeckPillowAndLumbarActionBean(massageTemp = 1))
    //	CarControlManager.actionLumbar(ReqNeckPillowAndLumbarActionBean(massageType = 1))
    //	CarControlManager.actionLumbar(ReqNeckPillowAndLumbarActionBean(massageStrength = 2))
    //	CarControlManager.actionLumbar(ReqNeckPillowAndLumbarActionBean(massageTime = 10))
    //	CarControlManager.setSeatMemoryMode(ReqSeatMemoryMode(1, 1))
    //	CarControlManager.setAtmosphereLampColorTheme(ThemeId.STAR_LIGHT)
    //	CarControlManager.setAtmosphereLightStatus(Status.ON)
    //	CarControlManager.setAtmosphereLightMode(1)
    //	CarControlManager.setAtmosphereLightBrightness(2)
    //	CarControlManager.setAtmosphereLightColor(2)
    //	CarControlManager.setAtmosphereLightSceneLightingEffectStatus(1, 1)
    //	CarControlManager.setRearFogLampStatus(Status.ON)
    //	CarControlManager.setSayHiHeadlightSignalType(2)
    //	CarControlManager.setCarInSoa(2)
    //	CarControlManager.setCarOutSoa(2)
    //	CarControlManager.actionRefrigerator(ReqRefrigeratorActionBean(switch = 1))
    //	CarControlManager.actionRefrigerator(ReqRefrigeratorActionBean(temp = 1.0))
    //	CarControlManager.actionRefrigerator(ReqRefrigeratorActionBean(mode = 2))

}