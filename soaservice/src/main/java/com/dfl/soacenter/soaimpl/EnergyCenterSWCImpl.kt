package com.dfl.soacenter.soaimpl

import android.os.Looper
import ara.ivi.energycenter.ics.energycenter.v1.skeleton.EnergyCenterSkeleton
import ara.ivi.energycenter.ics.energycenter.v1.skeleton.setEnduranceCalculationStandardType
import ara.ivi.energycenter.ics.energycenter.v1.skeleton.setExternalChargeSwitchStatus
import ara.ivi.energycenter.ics.energycenter.v1.skeleton.setInRoadPreheatingSwitchStatus
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.communication.EnergyCenterManager
import ics.com.runtime.ErrorCode

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2023/12/19
 * desc: 能源中心的SOA
 * version:1.0
 */
class EnergyCenterSWCImpl(instanceId: String, loop: Looper) : EnergyCenterSkeleton(instanceId, loop) {
    companion object {
        private const val TAG = SoaConstants.SOA_TAG.plus("EnergyCenterSWCImpl")
    }

    /**
     * 注册监听
     */
    fun registerCallBack() {
        SetStateChangedCallback { i: Int -> CommonLogUtils.logI(TAG, "setStateChangedCallback==>$i") }
        //能源中心对外放电通信
        setExternalChargeSwitchStatus.RegMethodProcess(object :
            setExternalChargeSwitchStatus.setExternalChargeSwitchStatusHandle {
            override fun OnCall(status: Byte) {
                CommonLogUtils.logI(TAG, "OnCall->setExternalChargeSwitchStatus:$status")
                EnergyCenterManager.requestDischargeSwitch(status.toInt() == 1)
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setExternalChargeSwitchStatus:${errorCode.message}")

            }

        })
        //能源中心在途预热通信
        setInRoadPreheatingSwitchStatus.RegMethodProcess(object :
            setInRoadPreheatingSwitchStatus.setInRoadPreheatingSwitchStatusHandle {
            override fun OnCall(status: Byte) {
                CommonLogUtils.logI(TAG, "OnCall->setInRoadPreheatingSwitchStatus:$status")
                EnergyCenterManager.requestPreheatSwitch(status.toInt() == 1)
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setInRoadPreheatingSwitchStatus:${errorCode.message}")
            }

        })
        //续航模式
        setEnduranceCalculationStandardType.RegMethodProcess(object :
            setEnduranceCalculationStandardType.setEnduranceCalculationStandardTypeHandle {
            override fun OnCall(type: Byte?) {
                CommonLogUtils.logI(TAG, "OnCall->setEnduranceCalculationStandardType:$type")
                EnergyCenterManager.requestEnduranceCalculation(type?.toInt())
            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logE(TAG, "OnError->setInRoadPreheatingSwitchStatus:${errorCode?.message}")
            }

        })
    }
}