package com.dfl.soacenter.soaimpl

import android.content.Context
import android.content.Intent
import android.os.Looper
import ara.ivi.media.ics.MusicPlayMode
import ara.ivi.media.ics.media.v1.skeleton.*
import com.dfl.android.common.customapi.ConfigManager
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.communication.MediaManager
import com.dfl.soacenter.communication.QQMusicManager
import ics.com.runtime.ErrorCode
import ics.com.runtime.utils.concurrent.Future
import ics.com.runtime.utils.concurrent.Promise

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2023/12/19
 * desc: 多媒体中间件的SOA
 * version:1.0
 * update : 钟文祥
 */
class MediaSWCImpl(var context: Context, instanceId: String, loop: Looper) : MediaSkeleton(instanceId, loop) {
    companion object {
        private const val TAG = SoaConstants.SOA_TAG.plus("MediaSWCImpl")

        /**航盛 播放蓝牙音乐的广播*/
        private const val ACTION_OPEN_BLUE = "com.dfl.bluetooth_play"

        /**航盛 播放usb音乐的广播*/
        private const val ACTION_OPEN_USB = "com.dfl.usb_play"

        /**
         * 关闭多媒体的广播
         */
        private const val ACTION_PAUSE_MEDIA = "com.dfl.smartscene.soa.action.PauseMedia"
    }

    /** 注册监听 */
    fun registerCallBack() {
        SetStateChangedCallback { i: Int ->
            CommonLogUtils.logI(
                TAG, "setStateChangedCallback==>$i"
            )
        }
        //播放音乐
        playMusic.RegMethodProcess(object : playMusic.playMusicHandle {
            override fun OnCall(app: Byte?) {
                CommonLogUtils.logI(TAG, "OnCall->playMusic:$app")
                // 1 qq音乐 2蓝牙音乐 3 usb音乐
                when (app?.toInt()) {
                    1 -> {
                        QQMusicManager.openQQMusic()
                    }

                    2 -> {
                        val intent: Intent? = context.packageManager.getLaunchIntentForPackage("com.dfl.bluetooth")
                        intent?.putExtra("isAutoPlay", true)
                        context.startActivity(intent)
                        if (ConfigManager.getCarSystemType() == "hsae") { //8155 航盛
                            CommonUtils.getApp().sendBroadcast(Intent(ACTION_OPEN_BLUE))
                        } else {
                            val intent = Intent(ACTION_OPEN_BLUE)
                            intent.setPackage("com.dfl.bluetooth")
                            CommonUtils.getApp().sendBroadcast(intent)
                        }
                    }

                    else -> {
                        val intent: Intent? = context.packageManager.getLaunchIntentForPackage("com.dfl.usb")
                        intent?.putExtra("isAutoPlay", true)
                        context.startActivity(intent)
                        if (ConfigManager.getCarSystemType() == "hsae") { //8155 航盛
                            CommonUtils.getApp().sendBroadcast(Intent(ACTION_OPEN_USB))
                        } else {
                            val intent = Intent(ACTION_OPEN_USB)
                            intent.setPackage("com.dfl.usb")
                            CommonUtils.getApp().sendBroadcast(intent)
                        }

                    }
                }
            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logE(TAG, "OnError->playMusic:${errorCode?.message}")
            }
        })
        //播放模式切换
        switchPlayMode.RegMethodProcess(object : switchPlayMode.switchPlayModeHandle {
            override fun OnCall(mode: MusicPlayMode): Future<switchPlayMode.switchPlayModeOutput> {
                //1 顺序播放 2 随机播放 3 单曲循环
                CommonLogUtils.logI(TAG, "OnCall->switchPlayMode:$mode")

                //QQMusicManager.setQQMusicPlayMode(mode)
                MediaManager.switchPlayMode(mode)
                val result = Promise<switchPlayMode.switchPlayModeOutput>()
                result.setValue(
                    ara.ivi.media.ics.media.v1.skeleton.switchPlayMode.switchPlayModeOutput(
                        1
                    )
                )
                return result
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->switchPlayMode:${errorCode.message}")
            }
        })

        //暂停/播放
        setMediaStatus.RegMethodProcess(object : setMediaStatus.setMediaStatusHandle {
            override fun OnCall(status: Byte?): Future<Void> {
                CommonLogUtils.logI(TAG, "OnCall->switchPlayMode:$status")
                //0 暂停 1 播放
                status?.let { MediaManager.setMediaStatus(it.toInt()) }
                return Promise()
            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logE(TAG, "OnError->setMediaStatus:${errorCode?.message}")
            }

        })

        //上一首下一首
        switchMediaSource.RegMethodProcess(object : switchMediaSource.switchMediaSourceHandle {
            override fun OnCall(switchMediaSource: Byte?): Future<Void> {
                CommonLogUtils.logI(TAG, "OnCall->switchMediaSource:$switchMediaSource")
                //1  上一首 2 下一首
                switchMediaSource?.toInt()?.let { MediaManager.switchMediaSource(it) }
                return Promise()
            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logE(TAG, "OnError->switchMediaSource:${errorCode?.message}")
            }

        })

        //关闭多媒体
        closeMedia.RegMethodProcess(object : closeMedia.closeMediaHandle {
            override fun OnCall(): Future<Void> {
                CommonLogUtils.logI(TAG, "OnCall->closeMedia")
                //关闭多媒体
                //1.暂停多媒体  2.发送退出当前界面的广播
                MediaManager.setMediaStatus(0)
                CommonUtils.getApp().sendBroadcast(Intent(ACTION_PAUSE_MEDIA))
                return Promise()
            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logE(TAG, "OnError->closeMedia:${errorCode?.message}")
            }

        })
    }


}