package com.dfl.soacenter.soaimpl

import android.os.*
import androidx.annotation.RequiresApi
import ara.ivi.sysset.ics.DayMode
import ara.ivi.sysset.ics.SwitchStatus
import ara.ivi.sysset.ics.sysset.v1.skeleton.*
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.communication.SoaSystemSettingManager
import com.dfl.soacenter.entity.ThemeDataBean
import com.dfl.soacenter.entity.VolumeDataBean
import ics.com.runtime.ErrorCode
import ics.com.runtime.utils.concurrent.Future
import ics.com.runtime.utils.concurrent.Promise
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.launch

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2023/12/19
 * desc: 系统设置的SOA
 * version:1.0
 */
class SystemSettingSWCImpl(instanceId: String, loop: Looper) : SysSetSkeleton(instanceId, loop) {
    companion object {
        private const val TAG = SoaConstants.SOA_TAG.plus("SystemSettingSWCImpl")
    }

    //触发条件的handler,用于限制间隔1s发送ccm数据
    private var notifyHandler: Handler? = null

    /**
     * 注册监听
     */
    fun registerCallBack() {
        SetStateChangedCallback { i: Int -> CommonLogUtils.logI(TAG, "setStateChangedCallback==>$i") }
        //获取蓝牙连接状态
        getBluetoothConnectionStatus.RegMethodProcess(object :
            getBluetoothConnectionStatus.getBluetoothConnectionStatusHandle {
            override fun OnCall(): Future<getBluetoothConnectionStatus.getBluetoothConnectionStatusOutput> {
                CommonLogUtils.logI(TAG, "OnCall->getBluetoothConnectionStatus")
                val promise = Promise<getBluetoothConnectionStatus.getBluetoothConnectionStatusOutput>()
                CoroutineScope(Dispatchers.IO).launch {
                    val status = async { SoaSystemSettingManager.getBluetoothConnectionStatus() }.await()
                    promise.setValue(status)
                }
                return promise
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->getBluetoothConnectionStatus:${errorCode.message}")
            }

        })
        //获取蓝牙开关状态
        getBluetoothSwitchStatus.RegMethodProcess(object : getBluetoothSwitchStatus.getBluetoothSwitchStatusHandle {
            override fun OnCall(): Future<getBluetoothSwitchStatus.getBluetoothSwitchStatusOutput> {
                CommonLogUtils.logI(TAG, "OnCall->getBluetoothSwitchStatus")
                val promise = Promise<getBluetoothSwitchStatus.getBluetoothSwitchStatusOutput>()
                CoroutineScope(Dispatchers.IO).launch {
                    val status = async { SoaSystemSettingManager.getBluetoothSwitchStatus() }.await()
                    promise.setValue(status)
                }
                return promise
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->getBluetoothSwitchStatus:${errorCode.message}")
            }

        })
        //获取热点连接状态
        getHotspotConnectionStatus.RegMethodProcess(object :
            getHotspotConnectionStatus.getHotspotConnectionStatusHandle {
            override fun OnCall(): Future<getHotspotConnectionStatus.getHotspotConnectionStatusOutput> {
                CommonLogUtils.logI(TAG, "OnCall->getHotspotConnectionStatus")
                val promise = Promise<getHotspotConnectionStatus.getHotspotConnectionStatusOutput>()
                CoroutineScope(Dispatchers.IO).launch {
                    val status = async { SoaSystemSettingManager.getHotspotConnectionStatus() }.await()
                    promise.setValue(status)
                }
                return promise
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->getHotspotConnectionStatus:${errorCode.message}")
            }

        })
        //获取热点开关状态
        getHotspotSwitchStatus.RegMethodProcess(object : getHotspotSwitchStatus.getHotspotSwitchStatusHandle {
            override fun OnCall(): Future<getHotspotSwitchStatus.getHotspotSwitchStatusOutput> {
                CommonLogUtils.logI(TAG, "OnCall->getHotspotSwitchStatus")
                val promise = Promise<getHotspotSwitchStatus.getHotspotSwitchStatusOutput>()
                CoroutineScope(Dispatchers.IO).launch {
                    val status = async { SoaSystemSettingManager.getHotspotSwitchStatus() }.await()
                    promise.setValue(status)
                }
                return promise
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->getHotspotSwitchStatus:${errorCode.message}")
            }

        })
        //获取wifi连接状态
        getWLANConnectionStatus.RegMethodProcess(object : getWLANConnectionStatus.getWLANConnectionStatusHandle {
            override fun OnCall(): Future<getWLANConnectionStatus.getWLANConnectionStatusOutput> {
                CommonLogUtils.logI(TAG, "OnCall->getWLANConnectionStatus")
                val promise = Promise<getWLANConnectionStatus.getWLANConnectionStatusOutput>()
                CoroutineScope(Dispatchers.IO).launch {
                    val status = async { SoaSystemSettingManager.getWLANConnectionStatus() }.await()
                    promise.setValue(status)
                }
                return promise
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->getWLANConnectionStatus:${errorCode.message}")
            }

        })
        //获取wifi开关状态
        getWLANSwitchStatus.RegMethodProcess(object : getWLANSwitchStatus.getWLANSwitchStatusHandle {
            override fun OnCall(): Future<getWLANSwitchStatus.getWLANSwitchStatusOutput> {
                CommonLogUtils.logI(TAG, "OnCall->getWLANSwitchStatus")
                val promise = Promise<getWLANSwitchStatus.getWLANSwitchStatusOutput>()
                CoroutineScope(Dispatchers.IO).launch {
                    val status = async { SoaSystemSettingManager.getWLANSwitchStatus() }.await()
                    promise.setValue(status)
                }
                return promise
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->getWLANSwitchStatus:${errorCode.message}")
            }

        })
        //获取移动网络开关状态
        getMobileNetworkSwitchStatus.RegMethodProcess(object :
            getMobileNetworkSwitchStatus.getMobileNetworkSwitchStatusHandle {
            override fun OnCall(): Future<getMobileNetworkSwitchStatus.getMobileNetworkSwitchStatusOutput> {
                CommonLogUtils.logI(TAG, "OnCall->getMobileNetworkSwitchStatus")
                val promise = Promise<getMobileNetworkSwitchStatus.getMobileNetworkSwitchStatusOutput>()
                CoroutineScope(Dispatchers.IO).launch {
                    val status = async { SoaSystemSettingManager.getMobileNetworkSwitchStatus() }.await()
                    promise.setValue(status)
                }
                return promise
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->getMobileNetworkSwitchStatus:${errorCode.message}")
            }

        })
        //获取移动网络连接状态
        getMobileNetworkConnectionStatus.RegMethodProcess(object :
            getMobileNetworkConnectionStatus.getMobileNetworkConnectionStatusHandle {
            override fun OnCall(): Future<getMobileNetworkConnectionStatus.getMobileNetworkConnectionStatusOutput> {
                CommonLogUtils.logI(TAG, "OnCall->getMobileNetworkConnectionStatus")
                val promise = Promise<getMobileNetworkConnectionStatus.getMobileNetworkConnectionStatusOutput>()
                return promise
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->getMobileNetworkConnectionStatus:${errorCode.message}")
            }

        })
        //设置蓝牙开关
        setBluetoothSwitchStatus.RegMethodProcess(object : setBluetoothSwitchStatus.setBluetoothSwitchStatusHandle {
            override fun OnCall(switchStatus: SwitchStatus) {
                CommonLogUtils.logI(TAG, "OnCall->setBluetoothSwitchStatus:$switchStatus")
                //cmm值与通讯一致
                SoaSystemSettingManager.setBluetoothSwitchStatus(switchStatus.value)
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setBluetoothSwitchStatus:${errorCode.message}")
            }

        })
        //设置热点开关
        setHotspotSwitchStatus.RegMethodProcess(object : setHotspotSwitchStatus.setHotspotSwitchStatusHandle {
            override fun OnCall(switchStatus: SwitchStatus) {
                CommonLogUtils.logI(TAG, "OnCall->setHotspotSwitchStatus:$switchStatus")
                SoaSystemSettingManager.setHotspotSwitchStatus(switchStatus.value)
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setHotspotSwitchStatus:${errorCode.message}")
            }

        })
        //设置wifi开关
        setWLANSwitchStatus.RegMethodProcess(object : setWLANSwitchStatus.setWLANSwitchStatusHandle {
            override fun OnCall(switchStatus: SwitchStatus) {
                CommonLogUtils.logI(TAG, "OnCall->setWLANSwitchStatus:$switchStatus")
                SoaSystemSettingManager.setWLANSwitchStatus(switchStatus.value)

            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setWLANSwitchStatus:${errorCode.message}")
            }

        })
        //设置移动网络开关
        setMobileNetworkSwitchStatus.RegMethodProcess(object :
            setMobileNetworkSwitchStatus.setMobileNetworkSwitchStatusHandle {
            override fun OnCall(status: SwitchStatus) {
                CommonLogUtils.logI(TAG, "OnCall->setMobileNetworkSwitchStatus:$status")
                SoaSystemSettingManager.setMobileNetworkSwitchStatus(status.value)
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setMobileNetworkSwitchStatus:${errorCode.message}")
            }

        })
        //设置日夜模式
        setDayAndNightMode.RegMethodProcess(object : setDayAndNightMode.setDayAndNightModeHandle {
            override fun OnCall(dayMode: DayMode) {
                CommonLogUtils.logI(TAG, "OnCall->setDayAndNightMode:$dayMode")
                //ccm枚举值转换为系统设置应用间通信值
                val dayValue = when (dayMode) {
                    DayMode.DAY -> ThemeDataBean.DAY
                    DayMode.NIGHT -> ThemeDataBean.NIGHT
                    DayMode.AUTO -> ThemeDataBean.AUTO
                }
                SoaSystemSettingManager.setDayAndNightMode(dayValue)
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setDayAndNightMode:${errorCode.message}")
            }

        })
        //设置中控亮度
        setScreenBrightness.RegMethodProcess(object : setScreenBrightness.setScreenBrightnessHandle {
            override fun OnCall(percent: Int) {
                CommonLogUtils.logI(TAG, "OnCall->setScreenBrightness:$percent")
                SoaSystemSettingManager.setScreenBrightness(percent)
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setScreenBrightness:${errorCode.message}")
            }

        })

        //设置壁纸
        setThemeWallpaper.RegMethodProcess(object : setThemeWallpaper.setThemeWallpaperHandle {
            override fun OnCall(type: Byte) {
                CommonLogUtils.logI(TAG, "OnCall->setThemeWallpaper:$type")
                SoaSystemSettingManager.setThemeWallpaper(type.toInt())
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setThemeWallpaper:${errorCode.message}")
            }

        })
        //设置音量
        setVolume.RegMethodProcess(object : setVolume.setVolumeHandle {
            override fun OnCall(volumeType: Byte, per: Int?) {
                CommonLogUtils.logI(TAG, "OnCall->setVolume:volumeType=$volumeType,per=$per")
                SoaSystemSettingManager.setVolume(volumeType.toInt(), per ?: 0)
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setVolume:${errorCode.message}")
            }

        })
        //设置屏幕文本 oem
        setScreenText.RegMethodProcess(object : setScreenText.setScreenTextHandle {
            override fun OnCall(text: String) {
                CommonLogUtils.logI(TAG, "OnCall->setScreenText:$text")
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setScreenText:${errorCode.message}")
            }

        })
        //设置静音
        setMuteStatus.RegMethodProcess(object : setMuteStatus.setMuteStatusHandle {
            override fun OnCall(status: Byte) {
                CommonLogUtils.logI(TAG, "OnCall->setMuteStatus:$status")
                SoaSystemSettingManager.setMuteStatus(status.toInt())
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setMuteStatus:${errorCode.message}")
            }

        })
        //设置车外音已废弃,在setVolume type=6为车外音
        setOutCarVolume.RegMethodProcess(object : setOutCarVolume.setOutCarVolumeHandle {
            override fun OnCall(volume: Byte) {
                CommonLogUtils.logI(TAG, "OnCall->setOutCarVolume:$volume")
                SoaSystemSettingManager.setVolume(VolumeDataBean.VOLUME_CAR_OUT, volume.toInt())
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->setOutCarVolume:${errorCode.message}")
            }

        })

    }

    /**
     * 触发条件，黎明0日出1白天2薄暮3日落4黑夜5
     */
    fun notifyDayStatusToCCM(dayStatus: Int) {
        if (notifyHandler == null) {
            notifyHandler = object : Handler(Looper.getMainLooper()) {
                @RequiresApi(Build.VERSION_CODES.TIRAMISU)
                override fun handleMessage(msg: Message) {
                    when (msg.what) {
                        1 -> {
                            val value = msg.data.getByte("dayStatus")
                            CommonLogUtils.logI(TAG, "OnCall->notifyDayStatusToCCM $value")
                            notifyDayStatus.Send(value)
                            //ccm处理时间是几十ms级别的，1s比较稳妥
                            Thread.sleep(1000)
                        }
                    }
                }
            }
        }
        // 使用Message发送任务
        val msg = Message.obtain()
        // 消息类型，自定义
        msg.what = 1
        //必须使用bundle存储此时数据，否则数据不准确
        val bundle = Bundle()
        bundle.putByte("dayStatus", dayStatus.toByte())
        msg.data = bundle
        notifyHandler?.sendMessage(msg) // 将Bundle设置到Message中
    }
}