package com.dfl.soacenter.network.api

import com.dfl.soacenter.network.NetConstant
import kotlin.reflect.KProperty

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/10/17
 * desc :
 * version: 1.0
 */
object NetworkService {
    val api by Service()


    class Service {
        operator fun getValue(thisRef: Any, property: KProperty<*>): ApiService {
            return ApiFactory.createService(
                NetConstant.FINAL_HOLIDAY_BASE_URL, ApiService::class.java
            )
        }
    }
}