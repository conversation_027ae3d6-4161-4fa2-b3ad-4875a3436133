package com.dfl.soacenter.network.api

import android.util.SparseArray
import com.dfl.android.common.BuildConfig
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.soacenter.SoaConstants
import com.google.gson.GsonBuilder
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/10/17
 * desc :后台接口请求
 * version: 1.0
 */
object ApiFactory {
    private val urls: SparseArray<String> = SparseArray()
    private var customHeaders: Map<String, String>? = null
    private val retrofits: SparseArray<Retrofit> = SparseArray()
    private const val CONNECT_TIMEOUT = 60L
    private const val READ_TIMEOUT = 60L
    private const val WRITE_TIMEOUT = 10L
    private val TAG = SoaConstants.SOA_TAG.plus("ApiFactory")

    fun <T> createService(
        baseUrl: String,
        clazz: Class<T>,
        newHeaders: Map<String, String>? = null
    ): T {
        val indexOfValue = urls.indexOfValue(baseUrl)
        val retrofit = when {
            newHeaders != customHeaders -> {
                if (indexOfValue >= 0) {
                    urls.remove(indexOfValue)
                    retrofits.remove(indexOfValue)
                }
                customHeaders = newHeaders
                getRetrofit(baseUrl, newClient(newHeaders))
            }
            indexOfValue >= 0 -> {
                retrofits.get(indexOfValue)
            }
            else -> {
                getRetrofit(baseUrl, newClient())
            }
        }
        return retrofit.create(clazz)
    }

    private fun getRetrofit(baseUrl: String, newClient: OkHttpClient): Retrofit =
        Retrofit.Builder().run {
            baseUrl(baseUrl)
            client(newClient)
            addConverterFactory(
                GsonConverterFactory.create(
                    GsonBuilder().serializeNulls().create()
                )
            )
            val build = build()
            val index = urls.size()
            urls.append(index, baseUrl)
            retrofits.append(index, build)
            build
        }


    private fun newClient(headers: Map<String, String>? = null) =
        OkHttpClient.Builder().apply {
            retryOnConnectionFailure(true)
            connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
            readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
            writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
            addInterceptor(
                HttpLoggingInterceptor(HttpLog()).setLevel(
                    HttpLoggingInterceptor.Level.BODY
                )
            )
            headers?.let {
                addInterceptor(Interceptor { chain ->
                    val original: Request = chain.request()
                    val requestBuilder: Request.Builder = original.newBuilder()
                    for ((key, value) in it) {
                        requestBuilder.addHeader(key, value)
                    }
                    val request: Request = requestBuilder.build()
                    chain.proceed(request)
                })
            }

        }.build()

    class HttpLog : HttpLoggingInterceptor.Logger {
        override fun log(message: String) {
            if (BuildConfig.DEBUG) {
                CommonLogUtils.logE(TAG, "request=httpLog===>$message")
            }
        }
    }
}