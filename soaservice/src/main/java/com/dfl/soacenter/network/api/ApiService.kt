package com.dfl.soacenter.network.api

import com.dfl.soacenter.network.NetConstant
import com.dfl.soacenter.entity.ResultData
import retrofit2.http.*

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/10/17
 * desc :
 * version: 1.0
 */
interface ApiService {
	@Headers(
		"Content-Type: application/json",
		"api: " + NetConstant.HOLIDAY_API,
		"appid: " + NetConstant.FINAL_HOLIDAY_APP_ID
	)
	@POST("/{path}")
	suspend fun getHolidayInfo(
		@Path("path") path: String?,
		@HeaderMap headerMap: Map<String, String>?,
		@Body map: Map<String, String>?
	): ResultData<String>
}