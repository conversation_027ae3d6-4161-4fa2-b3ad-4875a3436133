package com.dfl.soacenter

import android.content.Context
import android.database.ContentObserver
import android.os.Handler
import android.provider.Settings
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.commonlib.log.CommonLogUtils

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2025/04/02
 * desc : 监听系统设置自适应功能
 * version: 1.0
 */
class DayNightContentObserver(handler: Handler?, private val mContext: Context) : ContentObserver(handler) {
    private val TAG = GlobalConstant.GLOBAL_TAG + this::class.simpleName
    //报错
    //init {
    //    judgeNotifyCMM()
    //}

    companion object {
        const val SETTINGS_WALLPAPER_TIME_INTERVAL: String = "day_status"
    }

    enum class DisplayTimeInterval(val value: Int) {
        DAWN(0), //---＞黎明 SUNRISE -1h to SUNRISE
        SUNRISE(1), //---＞日出 SUNRISE to SUNRISE + 1h
        DAY(2), //--＞白天 SUNRISE + 1h to SUNSET - 1h
        DUSK(3), //---＞薄基 SUNSET -1h to SUNSET
        SUNSET(4), //---> 日落## SUNSET to SUNSET + 1h
        NIGHT(5) //- --＞黑夜 SUNSET +1 to SUNRISE -1h
    }

    interface OnChangeListener {
        fun onChange(display: Int)
    }

    var onChangeListener: OnChangeListener? = null

    /**获取系统设置自适应value*/
    fun getDisplayTimeInterval(): Int {
        var display = -1
        try {
            display = Settings.System.getInt(
                mContext.contentResolver,
                SETTINGS_WALLPAPER_TIME_INTERVAL
            )
            CommonLogUtils.logI(TAG, "系统设置自适应:$display")
        } catch (e: Settings.SettingNotFoundException) {
            CommonLogUtils.logE(TAG, "day_status get failed")
            e.printStackTrace()
        }
        return display
    }

    override fun onChange(selfChange: Boolean) {
        val display = getDisplayTimeInterval()
        onChangeListener?.onChange(display)
    }
}