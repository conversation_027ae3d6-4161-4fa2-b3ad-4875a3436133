package com.dfl.soacenter.entity

import ara.ivi.navigation.ics.Strategy
import java.io.Serializable

/**
 *Created by 钟文祥 on 2024/1/15.
 *Describer:
 */
class NaviSoaBean {}


/**导航 路线偏好 */
data class NaviRouteStrategy(
    val preference: Int
) {
    companion object {

        fun convert(strategy: Strategy): Int {
            //输出
            //偏好组合 ⾼德推荐:0  躲避拥堵:1  少收费:2 不⾛⾼速:3  ⾼速优先:4  速度
            //最快:5  ⼤路优先:6  躲避拥堵+少收费:7躲避拥堵+不⾛⾼速:8
            //躲避拥堵+⾼速优先:9少收费+不⾛⾼速:10  躲避拥堵+少收费+不⾛⾼速:11
            //躲避拥堵+速度最快:12  躲避拥堵+⼤路优先:13

            return when (strategy) {
                Strategy.RECOMMEND -> 0
                Strategy.AVOID_CONGESTION -> 1
                Strategy.LESS_MONEY -> 2
                Strategy.NO_HIGH_SPEED -> 3
                Strategy.HIGH_SPEED_PRIORITY -> 4
                Strategy.ROAD_PRIORITY -> 5
                Strategy.FASTEST -> 6
            }
        }
    }
}

/**请求收藏点列表*/
data class ReqFavoriteList(
    val operaType: Int //0:普通收藏点 1:家 2:公司3:全部
)

/**响应收藏点列表 实体*/
data class ResFavoriteListBean(
    val resultCode: Int?, //10000:请求成功 10001:⾮法参数  10040:未设置家和公司 10041:⽆任何收藏点,未找到结果
    val favoriteType: Int?, //0：普通收藏点 1：家2：公司 3：全部
    val protocolFavPoiInfos: MutableList<FavoriteItemInfo>?, //收藏点集合
    var msgStr: String?
) {
    fun covert(): String? {
        return when (resultCode) {
            10000 -> "请求成功"
            10001 -> "⾮法参数"
            10040 -> "未设置家和公司"
            10041 -> "⽆任何收藏点,未找到结果"
            else -> ""
        }
    }

    fun isSuccess(): Boolean {
        return resultCode == 10000 || resultCode == 10041 || resultCode == 10040
    }

}

/**收藏点*/
data class FavoriteItemInfo(
    val favoriteName: String?, //收藏点名称
    val favoriteAddress: String?, //收藏点地址
    val lon: Double?, //经度
    val lat: Double?, //纬度
    val itemId: String? //收藏点唯⼀码

) {
    fun toRoutePoi(): RoutePoi {
        return RoutePoi(
            name = favoriteName, address = favoriteAddress, lon = lon, lat = lat,
            //typeCode = itemId,
            //adCode = itemId,
            poiId = itemId
        )
    }
}

/**当前城市信息*/
data class CurCityInfo(
    var resultCode: Int?, //10000 成功 10001 未找到城市信息
    var province: String?, //省
    var cityName: String?, //市
    var townName: String?, //区/县
    var longitude: Double?, //经纬
    var latitude: Double? //经纬

) {
    fun isSuccess(): Boolean {
        return resultCode == 10000
    }
}

/**巡航信息*/
data class NviInfo(
    var distance: Long?, var time: Long?
)

/**当前位置信息*/
data class CurPositionInfo(
    var resultCode: Int?, //10000 成功 10050 未找到对应结果
    var cityName: String?, //城市名
    var address: String?, //地址信息
    var name: String?, //当前位置名
    var lon: Double?, var lat: Double?, var cityCode: Int?, //当前位置城市编码
    var poiId: String? //poi Id
) {
    fun isSuccess(): Boolean {
        return resultCode == 10000
    }
}

/**道路信息*/
data class RoadInfo(
    var resultCode: Int?, //10000 成功 10001 ⾮法参数 10052︓不在巡航状态
    var roadName: String?, //道路名称
    var roadClass: Int? //道路等级
) {
    fun isSuccess(): Boolean {
        return resultCode == 10000
    }
}

/**限速*/
data class SpeedLimitInfo(
    var resultCode: Int?, //10000 成功  其他-⻅错误表
    var speed: Int? //限速信息
) {
    fun isSuccess(): Boolean {
        return resultCode == 10000
    }
}

/**道路线*/
data class LaneLineInfo(
    var frontLane: List<Int>?, //⻋道线前景ID集合
    var backLane: List<Int>? //⻋道线背景ID集合
)

/**发起路线规划*/
data class ReqPlanRoute(
    var startPoi: RoutePoi? = null, //起点 缺省：当前位置
    var endPoi: RoutePoi?, //终点
    var midPois: List<RoutePoi>? = null, //途经点
    var routePref: Int? = null //偏好组合 缺省：当前导航当前的pref ⾼德推荐:1 躲避拥堵:2 少收费:3不⾛⾼速:4 ⾼速优先:5
    //速度最快:6 ⼤路优先:7 躲避拥堵+少收费:8躲避拥堵+不⾛⾼速:9 躲避拥堵+⾼速优先:10少收费+不⾛⾼速:11 躲避拥堵+少收费+不⾛⾼
    //速:12 躲避拥堵+速度最快:13 躲避拥堵+⼤路优先:14
)

data class ResPlanRoute(
    var resultCode: Int?, //10000成功 10013起点不在⽀持范围内 10014 终点不在⽀持范围内 10015途经点不在⽀持范围 内 10016 路线请求失败，请稍后重试 10002 规划失败，所在城市未下载离线导航数据
    var count: Int?, //规划出的路线个数
    var pathData: List<RouteInfo>?, //路径规划结果信息列表
    var start: RoutePoi?, //起点信息
    var end: RoutePoi?, //终点信息
    var mid: List<RoutePoi>?, //途径点列表信息
) {
    fun isSuccess(): Boolean {
        return resultCode == 10000
    }
}

/**路线的导航点信息*/
data class RoutePoi(
    var name: String?, //导航点名字
    var address: String?, //地址
    var lon: Double?, //
    var lat: Double?, //
    var typeCode: String? = null, //POI类型编码
    var adCode: String? = null, //城市编码
    var poiId: String? = null, //Poi ID
    var childPois: List<RouteChildPoi>? = null, //⼦Poi集合 起点、途径点不需要设置此字段，终点绘制终点区域需要
    var polygonBounds: List<PolygonBound>? = null, //终点区域集合 起点、途径点不需要设置此字段，终点绘制终点区域需要，
    var isEnable: Boolean? = true  //是否能用
)

data class RouteChildPoi(
    var name: String?, //导航点名字
    var childType: Int?, //⼦POI类型
    var lon: Double?, //
    var lat: Double?,
)

data class KeywordBean(
    val keyWord: String //搜索的关键字
)

data class PolygonBound(
    var lat: Double?,
    var lon: Double?, //

)

/**路线信息*/
data class RouteInfo(
    var method: String?, //路线标签
    var time: Double?, //路径总时间,单位为秒
    var distance: Double?, //路径总距离,单位为⽶
    var trafficLights: Double?, //红绿灯数
    var tolls: Double?, //收费信息 单位元
    var cityData: List<CityInfo>? //途径城市信息
)

data class CityInfo(
    var cityName: String?, //城市名称
    var cityAdcode: Int?, //城市编码
    var belongedProvince: Int?, //省编码
    var pinyin: String?, //城市拼⾳
    var initial: String?, //城市缩写
    var cityX: Double?, //城市坐标X
    var cityY: Double?, //城市坐标Y
    var cityLevel: Int? //城市编码

)

/**开始导航，停⽌导航*/
data class ReqNavi(
    var actionType: Int //0：开始导航,1：停⽌导航
)

/**我的场景位置*/
data class MyScenePosition(
    var lat: Double, //
    var lon: Double, //
    var distance: Int, //
    var type: Int = 0 //1到达 2离开
) : Serializable

//获取当前位置距离目标位置的直线距离
data class ResDistance(
    var resultCode: Int, var distance: Double
) {
    fun isSuccess(): Boolean {
        return resultCode == 10000
    }
}

data class SearchMapAddressBean(
    val resultCode: Int, val Pois: List<PoiItem>
) : Serializable {
    fun isSuccess(): Boolean {
        return resultCode == 10000
    }
}


data class PoiItem(
    val adCode: String,
    val addr: String,
    val category: String,
    val childPois: List<PoiItem>?,
    val childType: Int,
    val cityCode: String,
    val cityName: String,
    val deepInfo: String,
    val dis: String,
    val distToVia: Int,
    val distance: String,
    val district: String,
    val districtAdcode: Int,
    val etaToVia: Int,
    val floorNo: String,
    val historyType: Int,
    val id: String,
    val industry: String,
    val labelType: Int,
    val mChargeData: List<MChargeData>?,
    val mGasInfos: List<SearchGasInfo>?,
    val name: String?,
    val parent: String,
    val pid: String,
    val poiExtra: PoiExtra?,
    val poiPolygonBounds: List<PoiPolygonBound>?,
    val poiTag: String,
    val point: PointX?,
    val price: String,
    val shortName: String,
    val telPhone: String,
    val time: Int,
    val toll: Int,
    val type: Int,
    val typeCode: String,
    val typecode: String,
    val vehicleChargeLeft: Int,
    val viaLevel: Int,
    val workTime: String
) : Serializable


data class SearchGasInfo(
    val price: String, val type: String, val unit: String
) : Serializable


data class MChargeData(
    val business: String,
    val chargeInfo: List<ChargeInfo>,
    val charge_src_name: String,
    val chargingPrice: List<ChargingPrice>,
    val cscf: String,
    val currentChargingPrice: CurrentChargingPrice,
    val national_standard: String,
    val num_fast: Int,
    val num_slow: Int,
    val pay_type: String,
    val price_parking: String,
    val src_id: String,
    val src_type: String
) : Serializable

data class PoiPolygonBound(
    val lat: Double, val lon: Double, val m_X: Int, val m_Y: Int
) : Serializable

data class PointX(
    val lat: Double, val lon: Double, val m_X: Int, val m_Y: Int
) : Serializable

data class PoiExtra(
    val entranceList: List<Entrance>
) : Serializable

data class Entrance(
    val lat: Double, val lon: Double, val m_X: Int, val m_Y: Int
) : Serializable

data class ChargeInfo(
    val charge_Plugs_Info: List<ChargePlugsInfo>,
    val max_vol: Int,
    val min_vol: Int,
    val plugstype: String,
    val vol_type: String
) : Serializable

data class ChargingPrice(
    val ele_price: String, val ser_price: String, val time: String
) : Serializable

data class CurrentChargingPrice(
    val ele_price: String, val ser_price: String, val time: String
) : Serializable

data class ChargePlugsInfo(
    val brand_desc: String,
    val concur: Int,
    val conpower: Int,
    val convol: Int,
    val fastcur: Int,
    val fastpower: Int,
    val fastvol: Int,
    val speed_type: Int,
    val vol: Int,
    val vol_type: String
) : Serializable



