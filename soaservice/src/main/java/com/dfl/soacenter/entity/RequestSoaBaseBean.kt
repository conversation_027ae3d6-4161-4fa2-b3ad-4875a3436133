package com.dfl.soacenter.entity

import com.dfl.android.common.util.GeneralInfoUtils
import com.dfl.soacenter.SoaConstants
import java.io.Serializable

/**
 *Created by 钟文祥 on 2024/1/10.
 *Describer: soa通信协议数据请求实体基类
 */
data class RequestSoaBaseBean<T>(
    /**具体的协议ID*/
    var protocolId: Int,
    /**请求码*/
    var requestCode: String = GeneralInfoUtils.getRequestCode(),
    /**应答码  应答回来与请求码需要一致*/
    var responseCode: String? = null,
    /**协议版本号，本次请求协议的版本号*/
    var versionName: String = GeneralInfoUtils.getVersionName(),
    /**发送方标志 本应用包名*/
    var requestAuthor: String = GeneralInfoUtils.getRequestAuthor(),
    /**消息类型 : request：请求的消息 response：响应的消息 dispatch：主动透出的消息*/
    var messageType: String = SoaConstants.MESSAGE_TYPE_REQUEST,
    /**具体请求数据*/
    var data: T?
) : Serializable
