package com.dfl.soacenter.entity


/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/01/11
 * desc : 能源中心通信请求响应Bean
 * version: 1.0
 * @property actionType 操作类型 供电开关：1 智能预热开关：2
 * @property operaType 关闭 ：0开启 ：1
 * @constructor 能源中心通用请求data
 */
data class EnergyCenterRequestDataBean(
    val actionType: Int = 1,
    val operaType: Int = 1,
) {
    companion object {
        /**
         * 对外放电开关
         */
        const val ACTION_TYPE_DISCHARGE_SWITCH = 4

        /**
         * 在途预热开关
         */
        const val ACTION_TYPE_PREHEAT_SWITCH = 2

        /**
         * 关闭
         */
        const val OPERA_TYPE_CLOSE = 0

        /**
         * 打开
         */
        const val OPERA_TYPE_OPEN = 1
    }
}

/**
 * @property resultCode 1当前值和设置值一致 2当前不支持设置 3执行设置
 * @constructor 能源中心通用响应data
 */
data class EnergyCenterResponseDataBean(
    val resultCode: Int = 2
) {
    companion object {
        /**
         * 当前不支持设置
         */
        const val RESULT_CODE_FAIL = 2
    }
}

/**
 * @property mode 1标准 2动态
 * @constructor 能源中心设置续航模式
 */
data class EnduranceCalculationRequestBean(
    val mode: Int = STANDARD_MODE
) {
    companion object {
        /**标准*/
        const val STANDARD_MODE = 1

        /**动态*/
        const val DYNAMIC_MODE = 2
    }
}