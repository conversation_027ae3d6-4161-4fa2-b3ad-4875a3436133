package com.dfl.soacenter.entity

/**
 *Created by 钟文祥 on 2024/1/17.
 *Describer:
 */
data class WeatherSoaBean(
	var province: String?,    //省
	var cityName: String?,    //市
	var county: String?,    //区
	var aqiValue: String?,    //优良差
	var condition: String?, //晴阴
	var conditionNum: String?, //晴阴数
	var maxTem: String?,    //温度最大
	var minTem: String?,    //温度最小
	var temp: String?,    //当前温度
	var pm25: String?,
	var weatherNum: String?, //天气数
	var windDir: String?,    //风描述
	var windLevel: String?,  //风等级
	var future: FutureBean?, //未来
)


/**
 * aqiValue : 优
 * cityName : 深圳市
 * condition : 阴
 * conditionNum : 2
 * county : 福田区
 * future : {"day2":{"condition":"雷阵雨","conditionNum":"4","maxTem":"30","minTem":"26","predictDate":"2022-06-18","weatherNum":"4","windDir":"西南风","windLevel":"3-4"},"day3":{"condition":"雷阵雨","conditionNum":"4","maxTem":"30","minTem":"27","predictDate":"2022-06-19","weatherNum":"4","windDir":"西南风","windLevel":"3-4"},"day4":{"condition":"小雨","conditionNum":"7","maxTem":"31","minTem":"27","predictDate":"2022-06-20","weatherNum":"7","windDir":"微风","windLevel":"2"},"day5":{"condition":"阵雨","conditionNum":"3","maxTem":"31","minTem":"27","predictDate":"2022-06-21","weatherNum":"3","windDir":"微风","windLevel":"1"},"day6":{"condition":"多云","conditionNum":"1","maxTem":"32","minTem":"27","predictDate":"2022-06-22","weatherNum":"1","windDir":"微风","windLevel":"1"}}
 * maxTem : 31
 * minTem : 26
 * pm25 : 7
 * province : 广东省
 * temp : 29
 * weatherNum : 2
 * windDir : 北风
 * windLevel : 6
 */

data class FutureBean(
	val day2: WeatherBean?,
	val day3: WeatherBean?,
	val day4: WeatherBean?,
	val day5: WeatherBean?,
	val day6: WeatherBean?
)

/**未来2-6天天气*/
data class WeatherBean(
	val condition: String?,
	val conditionNum: String?,
	val maxTem: String?,
	val minTem: String?,
	val predictDate: String?,
	val weatherNum: String?,
	val windDir: String?,
	val windLevel: String?,
)

/**请求参数*/
data class ReqWeatherBean(
	val province: String?, val cityName: String?, val townName: String?
)

/**
 * {
"province":"广东省",
"cityName":"深圳市",
"townName":"福田区"
}
 * */


/**返回参数*/
data class ResWeatherBean(
	var resultCode: Int?, var weatherInfo: String?

) {
	fun isSuccess(): Boolean {
		return resultCode == 10000
	}
}