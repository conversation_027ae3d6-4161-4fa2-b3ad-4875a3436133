package com.dfl.soacenter.entity

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/02/19
 * desc : 用户中心协议通信实体基类
 * version: 1.0
 */
/**
 * @property startTime 开始时间yyyy-MM-dd HH:mm:ss
 * @property endTime 结束时间yyyy-MM-dd HH:mm:ss
 * @constructor 用户中心查询时间段行程
 */
data class UserCenterRequestSchedule(
	val startTime: String,
	val endTime: String
)


/**
 * @property protocolId 协议ID
 * @property responseCode 应答码 应答回来与请求码需要一致
 * @property versionName 协议版本号，本次请求协议的版本号
 * @property result 1成功其他失败
 * @property message 响应消息,失败原因
 * @property data 响应体
 * @constructor 用户中心通信响应
 */
data class UserCenterResponse<T>(
	val protocolId: Int?,
	val responseCode: String?,
	val versionName: String?,
	val result: Int?,
	val message: String?,
	var data: T?
) {
	companion object {
		const val SUCCESS = 1
	}
}

/**
 * @property rows 用户行程列表
 */
data class UserCenterResponseSchedule(
	val rows: ArrayList<UserCenterSchedule> = arrayListOf()
)

/**
 * @property arrivePlaceDetail 到达地点详情
 * @property arrivePlaceLat 到达地点纬度
 * @property arrivePlaceLng 到达地点经度
 * @property arrivePlaceSimple 目的地简称
 * @property arriveTime 计划到达时间，yyyy-MM-dd HH:mm:ss
 * @property carStatus 车辆位置与出发位置是否一致,1一致
 * @property createTime 创建时间，yyyy-MM-dd HH:mm:ss
 * @property departurePlaceDetail 出发地详细地址
 * @property departurePlaceLat 出发地纬度
 * @property departurePlaceLng 出发地经度
 * @property departurePlaceSimple 出发地简称
 * @property departureTime 计划出发时间，yyyy-MM-dd HH:mm:ss
 * @property eventName 行程名称
 * @property havcStatus 是否预约空调
 * @property id 行程id
 * @property inProgress 是否进行中 ，1是
 * @property isApproach 是否临期，1是
 * @property isEnable 是否可用，1是
 * @property isFinish 是否结束，1是
 * @property isRemind 是否已提醒，1是
 * @property note 行程备注内容
 * @constructor 个人中心用户行程
 */
data class UserCenterSchedule(
	val arrivePlaceDetail: String?,
	val arrivePlaceLat: String?,
	val arrivePlaceLng: String?,
	val arrivePlaceSimple: String?,
	val arriveTime: String?,
	val carStatus: String?,
	val createTime: String?,
	val departurePlaceDetail: String?,
	val departurePlaceLat: String?,
	val departurePlaceLng: String?,
	val departurePlaceSimple: String?,
	val departureTime: String?,
	val eventName: String?,
	val havcStatus: String?,
	val id: Int?,
	val inProgress: Int?,
	val isApproach: Int?,
	val isEnable: Int?,
	val isFinish: Int?,
	val isRemind: Int?,
	val note: String?
)

data class UserCenterRequestAddSchedule(
	val cpType: Int = 1,
	val arrivePlace: String = "东风日产",
	val arrivePlaceLat: String = "39.96",
	val arrivePlaceLng: String = "116.31",
	val departureTime: String = "2024-02-21 19:30:00",
	val arriveTime: String = "2024-02-21 20:00:00",
	val note: String = "出发去东风日产3",
	val arrivePlaceDetail: String = "东风日产",
	val arrivePlaceSimple: String = "东风日产",

	)
