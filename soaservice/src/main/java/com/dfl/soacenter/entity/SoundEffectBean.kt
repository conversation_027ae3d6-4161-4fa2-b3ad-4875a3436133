package com.dfl.soacenter.entity

/**
 * @param result 1成功其余失败
 * @param msg 提示信息
 * @param protocolId 协议号
 * @param responseCode 与请求requestCode匹配
 * @param data 返回体
 * @constructor 声音空间响应体
 */

data class SoundSpaceResponse<T>(
    val result: Int = 1, val msg: String = "", val protocolId: Int?, val responseCode: String = "", val data: T? = null
) {
    companion object {
        const val RESPONSE_RESULT_SUCCESS = 1
    }
}

/**
 * @property id 音效,整蛊音唯一id,声浪音量无该值
 * @property status 0使用失败 1使用成功 2音效在使用/设置播放位置失败 3未购买 4取消失败 5取消成功 6声浪已取消 7声浪未购买
 * @property trial 0非试用商品 1试用商品
 * @constructor 声音空间通用响应data
 */
data class SoundSpaceResponseData(
    val id: String? = "",
    val status: String = "0",
    val trial: String = "0"
)

/**
 * @property id 音效唯一id
 * @constructor 声音空间请求data
 */
data class SoundEffectRequestData(
    val id: String
)

/**
 * @param id 34:喵 35:鹦鹉 36:海豚 37:猫猫祟祟 38:奇幻夏⽇ 39:绿茵狂欢
 * @param position 播放的位置 1主驾2副驾3左后4右后
 * @constructor 设置整蛊音请求体data
 */
data class PlayPrankMusicRequestData(
    val id: String, val position: String
)

/**
 * @property volume 声浪音量 1低2中3高
 * @constructor 设置声浪音量请求体data
 */
data class VoiceVolumeRequestData(
    val volume: String
)

/**
 * @param id 声浪id
 * @param use 取消使用0，使用1
 */
data class UseVoiceRequestData(
    val id: String, val use: String
) {
    companion object {
        const val SOUND_GTR = "soundwaveGTR"
        const val SOUND_FAIRLADY = "soundwave370Z"
        const val SOUND_CYBERPUNK = "0"
    }
}

/**
 * @param id 声浪id
 * @param use 取消使用0，使用1
 */
data class GetVoiceResponseData(
    val roars: List<VoiceData>
)

/**
 * @param id 声浪id
 * @param name 声浪名称
 * @param desc 声浪简介
 * @param payStatus 未支付0，已支付1
 * @param used 未使用0，使用中1
 * @param trial 非试用0，试用1
 */
data class VoiceData(
    val id: String = "",
    val name: String = "",
    val desc: String = "",
    val payStatus: String = "",
    val used: String = "",
    val trial: String = ""
)

/**
 * @property duration 车外大喇叭持续时间
 * @constructor 设置声浪音量请求体data
 */
data class OutSideSoundRequestData(
    val duration: Int
)