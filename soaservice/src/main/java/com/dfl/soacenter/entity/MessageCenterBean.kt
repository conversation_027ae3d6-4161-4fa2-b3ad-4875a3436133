package com.dfl.soacenter.entity

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/12/24
 * desc : 消息中心应用间通信相关
 * version: 1.0
 */

/**发送消息中心消息data*/
data class MessageCenterBean(
    val appName: String, //应用名称
    val title: String, //通知标题
    val content: String, //内容
    val releaseTime: Long, //通知发送时间戳
    val tabId: Int? = 3, //3:智慧场景
    val label: String? = null, //内容标签/否
    val details: String? = null, //消息详情/否
    val isValid: Boolean? = null, //通知是否有效/否
    val validTime: Long? = null, //通知有效时间/否
    val btnName: String? = null, //按钮名称/否
    val fragmentId: Int? = null, //fragment对应ID/否
    val showPosition: ArrayList<Int>? = null //显示位置，0应用内显示，1、下啦通知，2、横幅通知，3、系统弹框/否
)

/**消息通知响应*/
data class MessageCenterResponse<T>(
    /**消息中心响应码*/
    val result: Int? = null,
    /**消息中心响应文言*/
    val msg: String? = null,
) : ResponseSoaBaseBean<T>() {
    override fun toString(): String {
        return super.toString() + "result=$result, msg=$msg"
    }

    companion object {
        const val SUCCESS = 1
        const val NO_PERMISSION = 106
    }
}

/**
 * 通知卡片信息
 */
data class AutoRunSceneInfo(
    /**
     * 1显示通知卡片，2隐藏通知卡片
     */
    val operaType: Int,
    /**
     * 场景名称
     */
    val name: String,
    /**
     * 场景id
     */
    val id: String
)