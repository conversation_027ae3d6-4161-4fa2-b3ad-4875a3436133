package com.dfl.android.common.base

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.lifecycle.ViewModelProvider

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/20
 * desc   :基于ViewDataBinding的DialogFragment基类
 * version: 1.0
 */
abstract class MVVMDialogFragment<V : ViewDataBinding, VM : BaseViewModel> : BaseDialogFragment() {
    protected lateinit var mViewDataBinding: V
    var mViewModel: VM? = null

    fun MVVMFragment() {}

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        super.onCreateView(inflater, container, savedInstanceState)
        mViewDataBinding = DataBindingUtil.inflate(inflater, getLayoutId(), container, false)
        return mViewDataBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        initViewModel()
        initDataBinding()
        super.onViewCreated(view, savedInstanceState)
    }

    protected abstract fun getBindingVariable(): Int

    protected abstract fun getViewModelClass(): Class<VM>

    private fun initViewModel() {
        val owner = activity
        mViewModel = if (owner != null) {
            ViewModelProvider(this).get(getViewModelClass())
        } else {
            getViewModelClass().newInstance()
        }
    }


    private fun initDataBinding() {
        if (getBindingVariable() > 0) {
            mViewDataBinding.setVariable(getBindingVariable(), mViewModel)
            mViewDataBinding.executePendingBindings()
            mViewDataBinding.lifecycleOwner = this
        }
    }
}