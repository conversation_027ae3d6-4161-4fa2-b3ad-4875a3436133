package com.dfl.android.common.util.constant;

import com.dfl.android.commonlib.log.CommonLogUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/08/31
 * desc : 特殊的能力skillId的inputArg值为空列表,不能为null
 * version: 1.0
 */
public final class NoInputArgSkillIdConstant {
    /**
     * 工作日
     */
    public static final int SKILL_ID_STATUS_TIME_WORKDAY_ID = 0xFF03;

    /**
     * 节假日
     */
    public static final int SKILL_ID_STATUS_TIME_HOLIDAY_ID = 0xFF04;

    /**
     * 关闭多媒体
     */
    public static final int SKILL_ID_ACTION_MEDIA_CLOSE = 0x29;

    /**
     * 关闭应用
     */
    public static final int SKILL_ID_ACTION_APP_CLOSE_APP = 0x6a;

    /**
     * 打开并播放QQ音乐
     */
    public static final int SKILL_ID_ACTION_OPEN_QQ_MUSIC = 0x58;
    /**
     * 打开并播放蓝牙音乐
     */
    public static final int SKILL_ID_ACTION_OPEN_BLUE_MUSIC = 0x59;

    /**
     * 打开并播放USB音乐
     */
    public static final int SKILL_ID_ACTION_OPEN_USB_MUSIC = 0x5a;

    /**
     * 天气预报
     */
    public static final int SKILL_ID_ACTION_APP_WEATHER_REPORT = 0x71;
    /**
     * 车窗透气模式打开
     */
    public static final int SKILL_ID_ACTION_DOOR_WINDOW_VENTILATE_OPEN = 0x31f;

    /**
     * 车窗通风
     */
    public static final int SKILL_ID_ACTION_DOOR_WINDOW_IN_FRESH_AIR = 0x306;
    private static ArrayList<Integer> skills = new ArrayList<>();

    /**特殊的能力skillId的inputArg值为空列表,不能为null*/
    public static ArrayList<Integer> getSpecialSkillList() {
        if (skills.isEmpty()) {
            ArrayList<Integer> skillList = new ArrayList<>();
            Field[] fields = NoInputArgSkillIdConstant.class.getDeclaredFields();
            for (Field field : fields) {
                if (field.getType() == int.class) {
                    try {
                        skillList.add(field.getInt(null));
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    }
                }
            }
            CommonLogUtils.logD("Object", skillList.toString());
            skills = skillList;
        }
        return skills;
    }
}
