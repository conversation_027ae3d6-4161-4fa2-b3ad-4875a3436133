package com.dfl.android.common.util

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentTransaction
import com.dfl.android.common.R

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/19
 * desc   :
 * version: 1.0
 */
object FragmentUtils {

    inline fun switchFragment(
        manager: FragmentManager,
        containerViewId: Int,
        tags: Array<String>,
        visibleTag: String,
        isShow: Boolean = true, //是否初始化并显示出来
        block: (String) -> Fragment?
    ) {
        val transaction = manager.beginTransaction()
        for (tag in tags) {
            var fragment = manager.findFragmentByTag(tag)
            if (fragment == null) {
                if (visibleTag == tag) {
                    fragment = block.invoke(tag)
                    fragment ?: continue
                    transaction.add(containerViewId, fragment, tag)
                    if (isShow) transaction.show(fragment)
                    else transaction.hide(fragment)
                }
            } else {
                if (visibleTag == tag) {
                    if (isShow) transaction.show(fragment)
                    else transaction.hide(fragment)
                } else {
                    transaction.hide(fragment)
                }
            }
        }
        transaction.commitAllowingStateLoss()
    }


    fun addFragment(activity: FragmentActivity, viewId: Int, toFragment: Fragment) {
        val ft: FragmentTransaction = activity.supportFragmentManager.beginTransaction()
        ft.addToBackStack(null); //这里将我们的Fragment加入到返回栈
        //退出时间设为10ms 防止内存泄露
        ft.setCustomAnimations(R.anim.scene_fragment_enter_ani, R.anim.scene_fragment_exit_ani)
        ft.add(viewId, toFragment);
        ft.commit();
    }

}