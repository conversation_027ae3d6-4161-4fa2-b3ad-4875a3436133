package com.dfl.android.common.base;

//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//


import android.app.UiModeManager;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Color;
import android.os.Bundle;
import android.view.WindowManager;

import androidx.annotation.ColorRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.appcompat.app.NightAppCompatDelegateImpl;

import com.dfl.android.common.global.GlobalConstant;
import com.dfl.android.common.util.BarUtils;
import com.dfl.android.commonlib.CommonUtils;
import com.dfl.android.commonlib.log.CommonLogUtils;
import com.dfl.common.nightmode.NightModeManager;

import me.jessyan.autosize.AutoSizeCompat;

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2024/3/27
 * desc: Activity基类
 * version:1.1
 */
public abstract class BaseActivity extends AppCompatActivity {
    private final String TAG = GlobalConstant.GLOBAL_TAG + this.getClass().getSimpleName();

    public BaseActivity() {
    }

    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        CommonLogUtils.logI(TAG, this.getClass().getSimpleName() + " onCreate");
        //状态栏背景设置
        transparentStatusBar();
        //启动activity动画
        startTransition();
        this.initView();
        this.initData();
    }

    @Override
    protected void onStart() {
        super.onStart();
        CommonLogUtils.logI(TAG, this.getClass().getSimpleName() + " onStart");
    }

    @Override
    protected void onResume() {
        super.onResume();
        UiModeManager uiModeManager = (UiModeManager) getSystemService(Context.UI_MODE_SERVICE);
        int modeApp = CommonUtils.getApp().getResources().getConfiguration().uiMode;
        int modeActivity = getResources().getConfiguration().uiMode;
        CommonLogUtils.logI(TAG, this.getClass().getSimpleName() + " onResume，modeActivity=" + modeActivity + " modeApp=" + modeApp + " isNight=" + uiModeManager.getNightMode());
    }

    @Override
    protected void onPause() {
        super.onPause();
        CommonLogUtils.logI(TAG, this.getClass().getSimpleName() + " onPause");
    }

    @Override
    protected void onStop() {
        super.onStop();
        CommonLogUtils.logI(TAG, this.getClass().getSimpleName() + " onStop");
    }

    @Override
    protected void onRestart() {
        super.onRestart();
        CommonLogUtils.logI(TAG, this.getClass().getSimpleName() + " onRestart");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        CommonLogUtils.logI(TAG, this.getClass().getSimpleName() + " onDestroy");
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        //状态栏和导航栏由应用设置深浅模式，不再跟随系统主题切换
        //int mUiMode = newConfig.uiMode & Configuration.UI_MODE_NIGHT_MASK;
        //深色浅色模式切换再次设置density适配
        AutoSizeCompat.autoConvertDensityOfGlobal(getResources());
        //通知控件深色浅色模式切换
        NightModeManager.get().notifyUpdateSkin(this);
        //发广播给dialog
        //Intent intent = new Intent("THEME_CHANGED");
        //intent.putExtra("mUiMode", mUiMode);
        //sendBroadcast(intent);
        //activity更新导航栏和状态栏深浅
        //BarUtils.setStatusAndNaviBarStyle(getWindow(), mUiMode);
    }

    @NonNull
    @Override
    public AppCompatDelegate getDelegate() {
        return NightAppCompatDelegateImpl.get(this, this);
    }

    @Override
    public void finish() {
        //退出activity动画，会影响组件库入场退场动画，注释
        //overridePendingTransition(0, R.anim.scene_activity_exit_ani);
        super.finish();
    }

    /**
     * 将状态栏设置为透明色
     */
    protected void transparentStatusBar() {
        BarUtils.transparentStatusBar(this);
    }

    /**
     * 导航栏背景设置
     */
    @Deprecated
    protected void setNaviBarColor(@ColorRes int color) {
        getWindow().clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        getWindow().setNavigationBarColor(getColor(color));
    }

    protected void startTransition() {
        //会影响组件库入场退场动画，注释
        //overridePendingTransition(R.anim.scene_activity_enter_ani, R.anim.scene_activity_exit_no);
    }

    protected abstract void initView();

    protected abstract void initData();

    @Override
    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        //        int mUiMode = getResources().getConfiguration().uiMode & Configuration.UI_MODE_NIGHT_MASK;
        //        BarUtils.setStatusAndNaviBarStyle(getWindow(), mUiMode);
        getWindow().setStatusBarColor(Color.TRANSPARENT);
    }
}

