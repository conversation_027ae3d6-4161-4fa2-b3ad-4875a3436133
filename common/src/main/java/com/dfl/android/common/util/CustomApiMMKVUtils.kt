package com.dfl.android.common.util

import com.tencent.mmkv.MMKV

/**
 * author : wswang
 * e-mail : <EMAIL>
 * time   : 2022/08/04
 * desc   :缓存通用配置数据
 * version: 1.0
 */
class CustomApiMMKVUtils private constructor() {
    private var mmkv: MMKV? = null

    init {
        init()
    }

    private object InstanceHolder {
        val instance = CustomApiMMKVUtils()
    }

    private fun init() {
        mmkv = MMKV.defaultMMKV()
    }

    fun saveVehicleConfig(vehicleConfig: String?) {
        mmkv?.putString(VEHICLE_CONFIG, vehicleConfig)
    }

    fun saveVehicleConfigSeat(vehicleConfigSeat: String?) {
        mmkv?.putString(VEHICLE_CONFIG_SEAT, vehicleConfigSeat)
    }

    val vehicleConfig: String?
        get() = mmkv?.getString(VEHICLE_CONFIG, "")

    val vehicleConfigSeat: String?
        get() = mmkv?.getString(VEHICLE_CONFIG_SEAT, "")


    companion object {
        /**
         * 车辆配置信息
         */
        private const val VEHICLE_CONFIG = "vehicle_config"

        /**
         * 是否支持座椅放平
         */
        private const val VEHICLE_CONFIG_SEAT = "vehicle_config_seat"

        /**
         * daid的key值
         */
        private const val KV_DAID = "kv_daid"

        /**
         * vin的key值
         */
        private const val KV_VIN = "kv_vin"

        val instance: CustomApiMMKVUtils
            get() = InstanceHolder.instance


    }

    fun saveDaid(daid: String?) {
        mmkv?.putString(
            KV_DAID, daid
        )
    }

    fun getDaid(): String? {
        return mmkv?.getString(KV_DAID, "")
    }

    fun saveVin(vin: String?) {
        mmkv?.putString(KV_VIN, vin)
    }

    fun getVin(): String? {
        return mmkv?.getString(KV_VIN, "")
    }

}