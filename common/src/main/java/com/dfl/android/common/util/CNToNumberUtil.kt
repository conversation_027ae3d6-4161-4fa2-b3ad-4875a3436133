package com.dfl.android.common.util


/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/06/02
 * desc   :
 * version: 1.0
 */
object CNToNumberUtil {
    val numberMap: HashMap<Char, Int>
    val digitMap: HashMap<Char, Int>

    init {
        numberMap = getCNToNumberMap()
        digitMap = getCNToDigitMap()
    }

    fun test(defaultSceneNamePrefix: String): Int {
        val arr = arrayOf(
            "场景", "场景零十", "场景二百零零一", "场景二千零一零", "场景二号", "场景二百号",
            "场景三百零一", "场景一千零一", "场景二百", "场景十一百", "场景十一", "场景二百一十零", "场景一",
            "场景三", "场景十", "场景一十", "场景十零", "场景二百一", "场景二千零零一", "change"
        )
        return toNumber(defaultSceneNamePrefix, arr.size) { index ->
            arr[index]
        }
    }

    fun test2(defaultSceneNamePrefix: String): Int {
        val arr = 1 until 10000
        val sceneNameList = arrayOfNulls<String>(arr.count())
        for (i in arr) {
            sceneNameList[i - 1] = "$defaultSceneNamePrefix${NumberToCNUtil.toChineseLower(i)}"
        }
        return toNumber(defaultSceneNamePrefix, sceneNameList.size) { index ->
            sceneNameList[index] ?: ""
        }
    }

    /**
     * 解析场景名字并返回一个未被占用的默认名称，默认名称以中文小写数字递增，如场景一、场景二…… 以此类推。
     * 举例：如果在场景列表中已有场景一、场景二，则此处默认名称为场景三； 如果在场景列表中已有场景一、场景三、场景四，
     * 则此处默认名称为场景二。
     * 本方法最高解析到九千九百九十九。
     * @return 场景的数字编号
     *
     * tips: 06/06和刘落恺刘工沟通后，确认“10”的显示方式为“十”而不是“一十”
     *
     * 所使用的测试用例：
     * "场景", "场景零十", "场景二百零零一", "场景二千零一零", "场景二号", "场景二百号",
     * "场景三百零一", "场景一千零一", "场景二百", "场景十一百", "场景十一", "场景二百一十零", "场景一",
     * "场景三", "场景十", "场景一十", "场景十零", "场景二百一", "场景二千零零一", "change"
     */
    inline fun toNumber(defaultSceneNamePrefix: String, count: Int, block: (index: Int) -> String): Int {
        val array = arrayOfNulls<Boolean>(count + 1)
        var sceneName: String
        for (i in 0 until count) {
            sceneName = block.invoke(i)
            if (sceneName.isNotEmpty() &&
                    sceneName.startsWith(defaultSceneNamePrefix) &&
                    sceneName != defaultSceneNamePrefix
            ) {
                val numberString = sceneName.substring(defaultSceneNamePrefix.length)
                //                LogUtils.i(tag, sceneName)

                var total = 0           // 解析时会一边把解析出来的数字累加暂存在这里
                var digit = -1          // 上次解析出来的位数
                var expectDigit = -1    // 期望的位数，比如一百后边应该要跟十位或者零
                var expectMaxDigit = -1 // 期望的最大位数，比如可以是一千零一也可以是一千零一十
                // 从左边起始位置进行解析时，位数的大小会随着解析递减，如一千一百一十一（千百十递减）
                var maxDigit = 1000     // 当前解析出来的最大位数
                var tempNumber: Int?    // 当前解析出来的数字
                var tempDigit: Int?     // 当前解析出来的位数
                var index = 0           // 中文字符串索引
                var zeroBefore = false  // 是否存在中文零
                while (index < numberString.length) {
                    // 得到本次number，并根据number设置期望的digit
                    tempNumber = numberMap[numberString[index]]
                    if (tempNumber == null) { //
                        expectDigit = 10
                    } else if (tempNumber == 0) {
                        if (expectDigit == 1) { // “场景二百一十零”，"场景十零" ”二百零零一“
                            //                            LogUtils.w(tag, "场景二百一十零 场景十零 二百零零一")
                            total = -1
                            break
                        } else {
                            if (zeroBefore) { // "场景二百零零一"，"场景二千零零一"
                                //                                LogUtils.w(tag, "场景二百零零一 场景二千零零一")
                                total = -1
                                break
                            }
                            zeroBefore = true
                            expectMaxDigit = digit / 10
                        }
                    } else {
                        expectMaxDigit = if (digit > 0) {
                            digit / 10
                        } else {
                            1000
                        }
                    }
                    // 得到本次digit
                    if (expectMaxDigit >= 1 || expectDigit > 1) {
                        if (numberString.length > index + 1) {
                            if (tempNumber == 0) {
                                tempDigit = expectMaxDigit
                            } else if (tempNumber != null) {
                                index++
                                tempDigit = digitMap[numberString[index]]
                                if (total == 0 && tempNumber == 1 && tempDigit == 10) { // "场景一十"
                                    //                                    LogUtils.w(tag, "场景一十")
                                    total = -1
                                    break
                                }
                            } else {
                                tempDigit = digitMap[numberString[index]]
                                if (tempDigit == 10) { // "场景十一"
                                    tempNumber = 1
                                }
                            }
                        } else if (total == 0) {
                            if (tempNumber != null) { // “场景一”
                                tempDigit = 1
                            } else { // “场景十”
                                tempDigit = digitMap[numberString[index]]
                                if (tempDigit == 10) {
                                    tempNumber = 1
                                }
                            }
                        } else {
                            if (tempNumber != null) { // "场景十一"
                                tempDigit = 1
                            } else { // "场景二百号"
                                tempDigit = null
                            }
                        }
                    } else { // “场景零十”
                        tempDigit = null
                    }
                    if (tempDigit == null || // “场景二百一号”，“场景二百一”，“场景二号”，“场景二百号”
                            tempNumber == null) { // “场景”，“场景壹”
                        //                        LogUtils.w(tag, "场景二百一号 场景二百一 场景 场景壹 场景二号 场景二百号")
                        total = -1
                        break
                    }
                    if (tempDigit > maxDigit || // "场景十一百"
                            (expectDigit > 0 && expectDigit != tempDigit && !zeroBefore)) { // "场景二百一"
                        //                        LogUtils.w(tag, "场景十一百 场景二百一")
                        total = -1
                        break
                    }
                    maxDigit = Math.min(maxDigit, tempDigit)
                    // 根据新的number和digit计算total
                    total += tempNumber * tempDigit
                    // 推测下次需要给出的digit
                    expectDigit = tempDigit / 10
                    expectMaxDigit = -1
                    digit = tempDigit
                    zeroBefore = tempNumber == 0
                    index++
                }
                total -= 1
                if (total >= 0 && total < array.size) {
                    array[total] = true
                }
                //                if (total >= 0) {
                //                    LogUtils.w(tag, "场景${total + 1}")
                //                }
            }
        }
        var i = 1
        for (b in array) {
            if (b == null) {
                break
            }
            i++
        }
        if (i > 9999) {
            i = 0 // 极端情况下才会出现的“场景零”
        }
        return i
    }

    private fun getCNToNumberMap(): HashMap<Char, Int> {
        val cnToNumberMap = HashMap<Char, Int>()
        with(cnToNumberMap) {
            put('零', 0)
            put('一', 1)
            put('二', 2)
            put('三', 3)
            put('四', 4)
            put('五', 5)
            put('六', 6)
            put('七', 7)
            put('八', 8)
            put('九', 9)
        }
        return cnToNumberMap
    }

    private fun getCNToDigitMap(): HashMap<Char, Int> {
        val cnToDigitMap = HashMap<Char, Int>()
        with(cnToDigitMap) {
            put('十', 10)
            put('百', 100)
            put('千', 1000)
        }
        return cnToDigitMap
    }
}