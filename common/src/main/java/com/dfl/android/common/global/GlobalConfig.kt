package com.dfl.android.common.global

/**
 * author:zhus<PERSON><PERSON>
 * e-mail:zhushi<PERSON>@dfl.com.cn
 * time: 2023/06/01
 * desc: 全局共用的常量，场景社区相关常量
 * version:1.0
 */
class GlobalConfig {
    companion object {
        /**
         * 场景数量上限
         */
        const val SCENE_COUNT_MAX = 30

        /**
         * 动作数量上限
         */
        const val SCENE_ACTION_COUNT_MAX = 30

        /** 网络超时 10s  */
        const val NET_TIME_OUT = 10

        /**是否切换商用环境*/
        const val IS_BUSINESS = false

        /** 社区场景API服务 测试  */
        private const val TEST_COMMUNITY_URL = "https://vitappzh.venucia.com/"
        private const val TEST_COMMUNITY_APPID = "app7gLEvuYzYKlQASLjLdJC75ZWX0e7mhaN"
        private const val TEST_COMMUNITY_APP_KEY =
            "OEQqMRUpc7H3YlouQ4JhKVRbO4vOVxzw8qYxkStrNGcAr2m2fbDsRQx1rObiwihGjLLVFX0CcwzQWif77kwuPvhILwg17I5rIfnyJMBkLrEGbMGXtweTuYUZit4FEAk8"
        private const val TEST_COMMUNITY_PUBLIC_KEY =
            "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEWHGyO08M/LcvGxqLZn2BFss2gcqNSgKa+692WJvspXj9UPK/gbraBr+uwPVrCqFwB4cETJoCX4dcoQ8J+CECIA=="
        private const val TEST_COMMUNITY_PRIVATE_KEY =
            "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgCBSkl8HnIyY8V/5CeR/WMbfUODleDcefNuwnnLBMNfmgCgYIKoEcz1UBgi2hRANCAAQWBqSNEuQxu8qJgZMzl2BTxtEdpwu8b9qJkpZ//lBejLavKo8sHz/ZQqnFXzyo14uOozGSNORfArCF3LK6kkHh"

        /** 社区场景API服务 商用 灰度  接口文档上  */
        private const val BUSINESS_COMMUNITY_URL = "https://nvitapp.venucia.com/"
        private const val BUSINESS_COMMUNITY_APPID = "apptfiltdVH95DRQWTU7J5SCB9D98riNRUm"
        private const val BUSINESS_COMMUNITY_APP_KEY =
            "H0YMUqQkn5AtxnPhgN1XYKPNbM96KUqtVAu7V8kXrFeyaP6NSKfmroYmrh9YnCReFlvlS2On3wAds5HFs347kqqlu4eHl71uP3Q26RykoxndHm06royX8i2WX8ZYiave"
        private const val BUSINESS_COMMUNITY_PUBLIC_KEY =
            "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEDbpfnjMAlrwwSYfq40WUAz/0zjMMuXfjJWc42qdK7uqVAwvuehfyUXii08pVRi/StWtkS+KK7/pEwnfVGuETMQ=="
        private const val BUSINESS_COMMUNITY_PRIVATE_KEY =
            "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQg1sPWJiXyqSweeTkB01IUHuEGv9LleCH/NiEEw6gvjPmgCgYIKoEcz1UBgi2hRANCAAQB7Qmi+ZxGeLgTygP/J+HRoxLXX/9ucZWr8oPHhzJqE74055kYxadkaXcDABEv/WJNfP7fpzk0/soBvGptOjQe"

        /** 社区场景API服务 最终  */
        var FINAL_COMMUNITY_URL = if (IS_BUSINESS) BUSINESS_COMMUNITY_URL else TEST_COMMUNITY_URL
        private var FINAL_COMMUNITY_APPID = if (IS_BUSINESS) BUSINESS_COMMUNITY_APPID else TEST_COMMUNITY_APPID
        private var FINAL_COMMUNITY_APP_KEY = if (IS_BUSINESS) BUSINESS_COMMUNITY_APP_KEY else TEST_COMMUNITY_APP_KEY
        private var FINAL_COMMUNITY_PUBLIC_KEY = if (IS_BUSINESS) BUSINESS_COMMUNITY_PUBLIC_KEY else TEST_COMMUNITY_PUBLIC_KEY
        private var FINAL_COMMUNITY_PRIVATE_KEY = if (IS_BUSINESS) BUSINESS_COMMUNITY_PRIVATE_KEY else TEST_COMMUNITY_PRIVATE_KEY


        /**请求每页的数量*/
        const val REQ_PAGE_SIZE = 15

        /**轮播图请求每页的数量*/
        const val REQ_PAGE_SIZE_4_BANNER = 5

        fun getAPPID(): String {
            return FINAL_COMMUNITY_APPID
        }

        fun getAPPKEY(): String {
            return FINAL_COMMUNITY_APP_KEY
        }

        fun getPUBLIC_KEY(): String {
            return FINAL_COMMUNITY_PUBLIC_KEY
        }

        fun getPRIVATE_KEY(): String {
            return FINAL_COMMUNITY_PRIVATE_KEY
        }

        /**友盟sdk appkey*/
        const val UMENG_APP_KEY = "67fc80e579267e0210382468"
    }
}