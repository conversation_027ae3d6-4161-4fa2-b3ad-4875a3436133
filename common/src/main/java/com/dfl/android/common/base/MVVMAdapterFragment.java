package com.dfl.android.common.base;

//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.ViewModelProvider;

import com.dfl.android.common.global.GlobalLiveEventConstants;
import com.dfl.android.common.util.MatchableAdapter;
import com.dfl.android.commonlib.log.CommonLogUtils;
import com.iauto.scenarioadapter.ScenarioInfo;
import com.jeremyliao.liveeventbus.LiveEventBus;

/**
 * author : raoyulin
 * e-mail : <EMAIL>
 * time : 2025/04/09
 * desc : 包含MatchableAdapter的fragment基类。通过泛型参数T来适配不同类型的Adapter。
 * version: 1.0
 */
public abstract class MVVMAdapterFragment<T extends MatchableAdapter<?>, V extends ViewDataBinding,
        VM extends BaseViewModel> extends BaseFragment {
    public VM mViewModel;
    protected V mViewDataBinding;

    public MVVMAdapterFragment() {
    }

    // 抽象方法，子类需要提供自己的 Adapter
    protected abstract T getAdapter();

    @Nullable
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        this.mViewDataBinding = DataBindingUtil.inflate(inflater, this.getLayoutId(), container, false);
        return this.mViewDataBinding.getRoot();
    }

    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        this.initViewModel();
        this.initDataBinding();
        super.onViewCreated(view, savedInstanceState);
    }

    protected void initParams() {
    }

    protected void initView(View view) {
    }

    protected void initData() {
    }

    protected abstract int getBindingVariable();

    protected abstract Class<VM> getViewModelClass();

    private void initViewModel() {
        this.mViewModel = (new ViewModelProvider(this)).get(this.getViewModelClass());
    }

    private void initDataBinding() {
        if (this.getBindingVariable() > 0) {
            this.mViewDataBinding.setVariable(this.getBindingVariable(), this.mViewModel);
            this.mViewDataBinding.executePendingBindings();
            this.mViewDataBinding.setLifecycleOwner(this);
        }

    }

    /**
     * 在数据加载完成后，根据标题匹配并获取模拟点击的位置。
     */
    protected int getSimulateClickPosition(ScenarioInfo.Condition preCondition, ScenarioInfo.Sequence preSequence) {
        int targetPosition = -1;
        if (preCondition != null && preCondition.getCategory() != null && preCondition.getSkillId() != -1) {
            String targetTitle = preCondition.getCategory();
            T adapter = getAdapter();

            if (adapter != null) {
                int position = adapter.findPositionByTitle(targetTitle);

                if (position != -1) {
                    View view = getView();
                    if (view != null) {
                        targetPosition = position;
                    }
                } else {
                    CommonLogUtils.logI(TAG, "编辑条件卡片，没找到可以自动打开的下一级弹框 targetTitle: " + targetTitle);
                    //                    return -1;
                }
            }
        } else if (preSequence != null && preSequence.getAction().getCategory() != null && preSequence.getAction().getSkillId() != -1) {
            String targetTitle = preSequence.getAction().getCategory();
            T adapter = getAdapter();

            if (adapter != null) {
                int position = adapter.findPositionByTitle(targetTitle);

                if (position != -1) {
                    View view = getView();
                    if (view != null) {
                        targetPosition = position;
                    }
                } else {
                    CommonLogUtils.logI(TAG, "编辑动作卡片，没找到可以自动打开的下一级弹框 targetTitle: " + targetTitle);
                    //                    return -1;
                }
            }
        }
        if (targetPosition != -1) { //找到下一级菜单，发消息通知隐藏上一级菜单
            LiveEventBus.get(GlobalLiveEventConstants.KEY_EDIT_DIALOG_DISAPPEAR_TOGETHER).post(0);
        } else { //找到下一级菜单，发消息通知上一级菜单 还原回去
            LiveEventBus.get(GlobalLiveEventConstants.KEY_EDIT_DIALOG_DISAPPEAR_TOGETHER).post(-1);
        }
        return targetPosition;
    }
}

