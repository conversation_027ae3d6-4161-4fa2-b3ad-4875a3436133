package com.dfl.android.common.customapi

import com.dfl.android.common.BuildConfig
import com.dfl.android.common.base.BaseSoaManager
import com.dfl.android.common.customapi.constant.ApiConstant
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.util.CustomApiUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.api.base.Constants
import com.dfl.api.base.appcommon.*
import com.jeremyliao.liveeventbus.LiveEventBus
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine


/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2022/10/14
 * desc:车辆共通类customApi接口,可获取车型基本信息，车辆行驶状态信息
 * version:1.0
 */
interface AppCommonManagerCallback {
    fun onInitResult(result: Boolean)
    fun onApiSTRModeStatusChanged(strMode: Int?)
}

object AppCommonManager : BaseSoaManager() {
    /**
     * 车辆状态回调
     */
    private val carStatusCallback = CarStatusCallback()

    /**
     * 通用管理器回调
     */
    var appCommonManagerCallback: AppCommonManagerCallback? = null

    /**
     * 通用管理器对象
     */
    @Volatile
    private var mAppCommon: IAppCommon? = null

    /**
     * 车机DaID
     */
    private var mDaId = "-1"

    /**
     * 缓存车辆挡位值
     */
    private var mCarGear = ApiConstant.GEAR_TYPE_UNKNOWN
    override fun init() {
        TAG = GlobalConstant.GLOBAL_TAG.plus("AppCommonManager")
        CoroutineScope(Dispatchers.IO).launch {
            initCustomApi()
        }
    }

    /**
     * 初始化车辆管理器
     */
    fun init(callback: AppCommonManagerCallback) {
        appCommonManagerCallback = callback
        init()
    }

    private suspend fun initCustomApi(): Boolean {
        return initCustomApi(
            Constants.DFL_BASE_APPCOMMON_APPCOMMON_SERVICE,
            mAppCommon,
            { if (it is IAppCommon) it else null },
            {
                mAppCommon = it
                if (it != null) {
                    mAppCommon?.registerCommonCarStatusCallback(carStatusCallback)
                    //mCarGear = mAppCommon?.carGear ?: ApiConstant.GEAR_TYPE_PARKING
                    appCommonManagerCallback?.onInitResult(true)
                } else {
                    appCommonManagerCallback?.onInitResult(false)
                }
            }
        )
    }

    /**
     * 获取车机的DAID
     */
    suspend fun getDAId(): String {
        return suspendCoroutine { continuation ->
            CustomApiUtils.requestCustomApi({
                if (initCustomApi()) {
                    mDaId = mAppCommon?.daId ?: ""
                    CommonLogUtils.logI(TAG, "获取daid成功： $mDaId")
                }
                continuation.resume(mDaId)
            }, {
                mDaId = "-1"
                CommonLogUtils.logE(TAG, "获取daid失败：" + it.message)
                continuation.resume(mDaId)
            })
        }
    }

    /**
     * 获取车辆挡位状态
     * -1： 无法获取
     * 0：Parking - P
     * 1：Reverse - R
     * 2：Neutral - N
     * 3：Drive - D
     */
    suspend fun getCarGear(): Int {
        return suspendCoroutine { continuation ->
            CustomApiUtils.requestCustomApi({
                if (initCustomApi()) {
                    mCarGear = mAppCommon?.carGear ?: ApiConstant.GEAR_TYPE_PARKING
                    CommonLogUtils.logI(TAG, "getCarGear $mCarGear")
                }
                if (BuildConfig.DEBUG) {
                    mCarGear = ApiConstant.GEAR_TYPE_PARKING
                }
                continuation.resume(mCarGear)
            }, {
                mCarGear = ApiConstant.GEAR_TYPE_UNKNOWN
                CommonLogUtils.logE(TAG, "获取CarGear失败：" + it.message)
                continuation.resume(mCarGear)
            })
        }
    }

    fun getCarGearName(): String {
        val gear = when (mCarGear) {
            0 -> "P挡"
            1 -> "R挡"
            2 -> "N挡"
            3 -> "D挡"
            else -> "null"
        }
        return gear
    }

    class CarStatusCallback : ICommonCarStatusCallback {
        override fun onVehicleWarningInfoChange(p0: WarningInfo?) {

        }

        override fun onTotalRangeInfoChange(p0: RangeInfo?) {

        }

        override fun onIllStatusChange(b: Boolean) {

        }

        override fun onTurnIndicateStatusChange(i: Int) {

        }

        override fun onScreenStatus(b: Boolean) {

        }

        override fun onAutoACCStatusChange(b: Boolean) {

        }

        override fun onDriveStatusChanged(i: Int) {

        }

        override fun onVehicleStatusChange(i: Int) {

        }

        override fun onVehicleStatusTypeChange(i: Int, b: Boolean) {

        }

        override fun onEcuErrorStateChange(i: Int, i1: Int) {

        }

        override fun onCanIdErrorStateChange(i: Int, i1: Int) {

        }

        override fun onAlertWarningInfo(i: Int, i1: Int) {

        }

        override fun onPowerModeStatusChange(b: Boolean) {

        }



        override fun onPkbStatusChange(p0: Int) {

        }

        override fun onCarSpeedChange(speed: Float) {

        }

        override fun onCarGearChange(gear: Int) {
            CommonLogUtils.logI(TAG, "onCarGearChange gear:$gear")
            mCarGear = gear
            LiveEventBus.get(GlobalLiveEventConstants.KEY_CAR_GEAR_CHANGE, Int::class.java).post(gear)
        }

        override fun onReverseStatusChange(p0: Boolean) {

        }

        override fun onMeterSpeedChange(p0: Float) {
        }

        override fun onOdographChange(p0: Int) {

        }

        override fun onFuelDrivingRange(p0: Int) {
        }

        override fun onPercentRemainFuel(p0: Int) {
        }

        override fun onRemainFuel(p0: Float) {

        }

        override fun onPercentRemainPower(p0: Int) {
        }

        override fun onPowerDrivingRange(p0: Int) {
        }

        override fun onAccStatus(p0: Boolean) {
        }

        /**
         * @param state true IGN on；false IGN off
         */
        override fun onIGNStatus(state: Boolean) {
            CommonLogUtils.logE(TAG, "onIGNStatus==>:$state")
        }


        override fun onDriveMode(p0: Int) {
            CommonLogUtils.logI(TAG, "onSTRModeStatusChange==>:$p0")
            appCommonManagerCallback?.onApiSTRModeStatusChanged(p0)
        }



        override fun onEnduranceCalculationStandardTypeChange(p0: Int) {
        }

    }

}