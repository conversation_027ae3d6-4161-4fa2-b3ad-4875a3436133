package com.dfl.android.common.base

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import androidx.viewbinding.ViewBinding
import com.dfl.android.common.util.getVmClazz
import com.dfl.android.common.util.inflateBindingWithGeneric
import com.dfl.android.commonlib.log.CommonLogUtils

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2022/07/18
 * desc: fragment的基类
 * version:1.0
 */
abstract class BaseVBFragment<VM : BaseViewModel, VB : ViewBinding> : BaseFragment() {

    //该类绑定的 ViewBinding
    lateinit var mViewBind: VB
    lateinit var mViewModel: VM

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mViewModel = createViewModel()
        initView(savedInstanceState)
        createObserver()
    }

    /**
     * 创建viewModel
     */
    private fun createViewModel(): VM {
        return ViewModelProvider(this).get(getVmClazz(this))
    }

    /**
     * 初始化view
     */
    abstract fun initView(savedInstanceState: Bundle?)

    /**
     * 创建观察者
     */
    abstract fun createObserver()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        CommonLogUtils.logI(TAG, this::class.simpleName + " onCreateView")
        mViewBind = inflateBindingWithGeneric(inflater, container, false)
        return mViewBind.root
    }

    override fun initData() {}
    override fun initParams() {}
    override fun initView(var1: View?) {}
    override fun getLayoutId(): Int {
        return -1
    }
    /**
     * 获取当前类绑定的泛型ViewModel-clazz
     */
    //    @Suppress("UNCHECKED_CAST")
    //    fun <VM> getVmClazz(obj: Any): VM {
    //        return (obj.javaClass.genericSuperclass as ParameterizedType).actualTypeArguments[0] as VM
    //    }
}