package com.dfl.android.common.base

import android.annotation.SuppressLint
import android.content.DialogInterface
import android.content.DialogInterface.OnDismissListener
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.viewbinding.ViewBinding
import com.dfl.android.common.util.inflateBindingWithGeneric2
import com.dfl.android.commonlib.KeyboardUtils
import com.dfl.android.commonlib.log.CommonLogUtils

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2022/07/18
 * desc: 基于ViewBinding的dialogFragment的基类
 * version:1.0
 */
abstract class BaseVBDialogFragment<VB : ViewBinding> : BaseDialogFragment() {

    //该类绑定的 ViewBinding
    protected lateinit var mVB: VB

    /**
     * 用于监听弹框是否隐藏
     */
    private var mDismissListener: OnDismissListener? = null


    @SuppressLint("ClickableViewAccessibility")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        view.setOnTouchListener { _, _ ->
            KeyboardUtils.hideSoftInput(view)
            return@setOnTouchListener false
        }
        initView(savedInstanceState)
    }

    fun setDismissListener(dismissListener: OnDismissListener) {
        mDismissListener = dismissListener
    }

    /**
     * 初始化view
     */
    abstract fun initView(savedInstanceState: Bundle?)

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        CommonLogUtils.logI(TAG, this::class.simpleName + " onCreateView")
        mVB = inflateBindingWithGeneric2(inflater, container, false)
        return mVB.root
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        mDismissListener?.onDismiss(dialog)
    }

    /**
     * 使用viewBinding不需要，不能调用父类onCreateView方法
     */
    override fun getLayoutId(): Int {
        return -1;
    }
}