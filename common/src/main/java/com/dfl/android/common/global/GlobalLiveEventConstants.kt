package com.dfl.android.common.global

/**
 * author:zhus<PERSON><PERSON>
 * e-mail:zhus<PERSON><PERSON>@dfl.com.cn
 * time: 2023/03/31
 * desc:
 * version:1.0
 */
class GlobalLiveEventConstants {
    companion object {

        const val KEY_STR_MODE_CHANGE: String = "str_mode_status_changed"

        /**
         * 系统设置隐私状态变化
         */
        const val KEY_SYSTEM_SETTING_PRIVATE_STATE_CHANGED = "key_system_setting_private_state_changed"

        /**
         * 权限状态监听
         */
        const val KEY_SYSTEM_SETTING_PERMISSION_STATE = "key_system_setting_permission_state"

        /**
         * 车挡位变更通知
         */
        const val KEY_CAR_GEAR_CHANGE = "key_car_gear_change"

        /**
         * 香氛机连接状态通知
         */
        const val KEY_FRAGRANCE_LINK_STATE = "key_fragrance_link_state"

        /**
         * 香氛机设置状态监听
         */
        const val KEY_FRAGRANCE_SET_STATE = "key_fragrance_set_state"

        /**
         * 香氛机浓度监听
         */
        const val KEY_FRAGRANCE_POTENCY_STATE_LISTENER = "key_fragrance_potency_listener"

        /**
         * 香氛机开关状态监听
         */
        const val KEY_FRAGRANCE_SWITCH_STATE = "key_fragrance_switch_state"

        /**
         * 场景是否可手动或自动执行的消息设置
         */
        const val KEY_SCENE_EXECUTE_STATUS = "key_scene_execute_status"

        /**
         * 空调、香氛弹窗状态通知（打开/关闭、打开失败）
         */
        const val KEY_SLEEP_AIOT_DIALOG_STATE = "key_sleep_aiot_dialog_state"

        /**
         * 地址搜索结果回调
         */
        const val KEY_SEARCH_ADDRESS_RESULT_CALLBACK = "key_search_address_result_callback"

        /**获取数据失败回调*/
        const val KEY_GET_DATA_ERROR_CALLBACK = "key_get_data_error_callback"

        /**
         * 隐私协议变更的消息 true同意
         */
        const val KEY_PROTOCOL_STATUS = "key_protocol_status"

        /**
         * 打开自在场景结果状态的回调
         */
        const val KEY_OPEN_COMFORT_SCENE_STATE = "key_open_comfort_scene_state"

        /**
         * 在自在主页监听临停模式判断回调
         */
        const val KEY_OPEN_SCENE_PARK_STATE_LISTENER = "key_open_scene_park_state_listener"

        /**
         * 场景添加成功的事件
         */
        const val KEY_SCENE_ADD_SUCCESS = "key_scene_add_success"

        /**
         * 场景修改成功的事件
         */
        const val KEY_SCENE_MODIFY_SUCCESS = "key_scene_modify_success"

        /**
         * 场景删除成功的事件
         */
        const val KEY_SCENE_DELETE_SUCCESS = "key_scene_delete_success"

        /**
         * 更新场景列表的数据事件 //0是有数据，1是没有数据 ,2异常
         */
        const val KEY_GET_SCENE_LIST = "key_get_scene_list"

        /**
         * 更新发现列表的数据事件
         */
        const val KEY_GET_DISCOVER_LIST = "key_get_discover_list"

        /**
         * 更新能力列表的数据事件
         */
        const val KEY_GET_SKILL_LIST = "key_get_skill_list"

        /**
         * 场景开始执行的事件
         */
        const val KEY_SCENE_START = "key_scene_start"

        /**
         * 场景结束执行的事件
         */
        const val KEY_SCENE_END = "key_scene_end"

        /**
         * 场景试用结束执行的事件
         */
        const val KEY_TRY_SCENE_END = "key_try_scene_end"

        /**
         * 场景设置自动执行标志成功的事件
         */
        const val KEY_SCENE_AUTO_SUCCESS = "key_scene_auto_success"

        /**
         * 场景数据发生变化接口
         */
        const val KEY_SCENE_CHANGED = "key_scene_changed"

        /**
         * 展厅模式-试运行场景状态监听
         */
        const val KEY_PLAY_MODE_TRY_RUN_SCENE_LISTENER = "key_play_mode_try_run_scene_listener"

        /**
         * 车控智能设备数据变化监听
         */
        const val CAR_CONTROL_SMART_DEVICE_STATE = "car_control_smart_device_state"

        /**
         * 场景执行记录
         */
        const val KEY_SCENE_RUN_RECORD = "key_scene_run_record"

        /**
         * CCM连接状态变化监听
         */
        const val KEY_CCM_CONNECT_STATE = "key_ccm_connect_state"

        /**
         * 多媒体搜索结果回调
         */
        const val KEY_MEDIA_SEARCH_RESULT_CALLBACK = "key_media_search_result_callback"

        /**
         * 删除所有用户场景成功
         */
        const val KEY_SCENE_DELETE_ALL_SUCCESS = "key_scene_delete_all_success"

        /**社区场景获取数据回调*/
        const val KEY_COMMUNITY_GET_VALUE_CALLBACK = "key_community_get_value_callback"

        /**发布场景fragment回退  post:1或2  表示层数*/
        const val KEY_POST_FRAGMENT_BACK = "key_post_fragment_back";

        /**社区场景修改成功 - 点赞*/
        const val KEY_COMMUNITY_LIKE_SUCCESS_UPDATE_UI = "key_community_like_success_update_ui"

        /**社区场景修改成功 - 下载*/
        const val KEY_COMMUNITY_DOWNLOAD_SUCCESS_UPDATE_UI = "key_community_download_success_update_ui"

        /**社区场景下载*/
        const val KEY_COMMUNITY_DOWNLOAD_FAIL = "key_community_download_fail"


        /**社区场景修改成功 - 关注 */
        const val KEY_COMMUNITY_FOCUS_SUCCESS_UPDATE_UI = "key_community_focus_success_update_ui"

        /**社区场景 取消下载*/
        const val KEY_COMMUNITY_DOWNLOAD_CANCEL = "key_community_download_cancel"

        /**
         *通知个人中心用户已登陆情况 的数据变化，第一次获取用户数据和用户登录不会post
         */
        const val KEY_USER_CENTER_GET_USERID_LOGIN_STATUS = "key_user_center_user_data_change"

        /**
         *监听个人中心App用户登录状态,返回值boolean
         * true退出登录，false登录，第一次获取到用户数据
         */
        const val KEY_USER_CENTER_USER_IS_LOGIN_OUT_STATUS = "key_user_center_user_is_login_out_status"

        const val KEY_USER_CENTER_DELETE_SCENE = "key_user_center_delete_scene"

        /**SceneEditContainerDialogFragment与下一级能力类弹框消失而消失的通知*/
        const val KEY_EDIT_DIALOG_DISAPPEAR_TOGETHER = "key_edit_dialog_disappear_together"
    }
}