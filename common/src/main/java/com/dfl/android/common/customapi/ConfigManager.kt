package com.dfl.android.common.customapi

import android.text.TextUtils
import com.dfl.android.common.BuildConfig
import com.dfl.android.common.util.CustomApiMMKVUtils
import com.dfl.android.common.util.PropReflectUtil
import com.dfl.android.commonlib.log.CommonLogUtils

/**
 * author:zhus<PERSON><PERSON>
 * e-mail:<EMAIL>
 * time: 2023/03/06
 * desc: 系统属性反射类，通过反射可用以获取高低配信息，daid，vin
 * version:1.0
 */
object ConfigManager {

    /**车项目产品类型*/
    private const val KEY_PRODUCT_MODEL = "ro.hardware.product.model" // =62324E/LKIA/631/PK1B/LK2A
    private var mCarProductModel: String? = ""

    /**车机系统类型*/
    private const val KEY_SYSTEM_TYPE = "ro.hardware.systemtype" //=hsae/lanyou/megatronix
    private var mCarSystemType: String? = ""

    /**
     * 系统设置属性的常量值，车型配置属性,2023-08-21更换属性名的值
     */
    private const val KEY_VEHICLE_CONFIG = "ro.hardware.carconfig"
    //    LK1A:   0 entry  1 BSG  2 UPR
    //    LK2A:   1 010, 2 020, 3 030, 4 040,5 050 ,6 060
    //    PK1B：1 510,2 520,3 530&610,4 620，5 110,6 120&220，7 130&230, 8 240
    //    9 760&750，10 950，11 550，12 650，13 860
    //    PK1B 5-8是混动车，其余为纯电车

    /**
     * 座椅属性值
     */
    private const val KEY_SEAT_CONFIG = "persist.eol.drmemoryseat"
    //    private const val KEY_VEHICLE_CONFIG="persist.dfl.vehicle.config"

    /**
     * daid的常量值 20240821更改常量名
     */
    private const val KEY_VEHICLE_DAID = "ro.hardware.daid"

    /**vin的常量值*/
    private const val KEY_VEHICLE_VIN = "ro.hardware.vin"

    private const val TAG = "DFLScene-ConfigManager"

    /**
     * 高配属性值
     */
    private const val CONFIG_HIGH = "2"

    /**
     * 低配属性值
     */
    private const val CONFIG_LOW = "1"

    /**
     * 支持座椅放平
     */
    private const val CONFIG_SUPPORT_SEAT = "2"

    /**
     * 不支持座椅放平
     */
    private const val CONFIG_NOT_SUPPORT_SEAT = "1"


    /**
     * 高低配配置
     */
    @Deprecated("不再通过customai区分配置，根据ccm能力下发区分配置")
    private var mVehicleConfig: String? = ""

    /**
     * 座椅配置值
     */
    @Deprecated("未集成自在，已不需要使用")
    private var mSeatConfig: String? = ""

    /**
     * daid
     */
    private var mDaid: String? = ""

    /**vin*/
    private var mVin: String? = ""

    /**获取车机系统类型 =hsae/lanyou/megatronix*/
    fun getCarSystemType(): String? {
        if (TextUtils.isEmpty(mCarSystemType)) {
            mCarSystemType = PropReflectUtil.getProp(KEY_SYSTEM_TYPE)
        }
        return mCarSystemType
    }

    /**获取车项目产品类型  62324E/LKIA/631/PK1B/LK2A*/
    fun getCarProductModel(): String? {
        if (TextUtils.isEmpty(mCarProductModel)) {
            mCarProductModel = PropReflectUtil.getProp(KEY_PRODUCT_MODEL)
            CommonLogUtils.logI(TAG, "车项目产品类型为：$mCarProductModel")
        }
        return mCarProductModel
    }

    fun isLK1A(): Boolean {
        return getCarProductModel().equals("LK1A", true)
    }

    fun isLK2A(): Boolean {
        return getCarProductModel().equals("LK2A", true)
    }

    fun isPK1B(): Boolean {
        return getCarProductModel().equals("PK1B", true)
    }

    /**
     * 判断车机是否为高配置车型
     * @return 默认返回false低配，高配则返回true
     */
    @Deprecated("不再通过customai区分配置，根据ccm能力下发区分配置")
    fun isHighConfig(): Boolean {
        //1.如果没有设置过配置值，则需要去拿一次值
        if (TextUtils.isEmpty(mVehicleConfig)) {
            CommonLogUtils.logD(TAG, "systemProp-get")
            val vehicleConfig = PropReflectUtil.getProp(KEY_VEHICLE_CONFIG)
            CommonLogUtils.logI(TAG, "systemProp=$vehicleConfig")
            //debug版本默认高配
            if (BuildConfig.DEBUG) {
                return true
            }
            if (CONFIG_HIGH == vehicleConfig) {
                saveVehicleConfig2MMKV(vehicleConfig)
                return true
            }
            if (CONFIG_LOW == vehicleConfig) {
                saveVehicleConfig2MMKV(vehicleConfig)
                return false
            }
            //2.如果从系统属性中拿到的值是错误的，则需要从MMKV中拿缓存的值
            val config4MMKV = CustomApiMMKVUtils.instance.vehicleConfig
            CommonLogUtils.logD(TAG, "systemProp4MMKV=$config4MMKV")
            if (CONFIG_HIGH == config4MMKV) {
                return true
            }
            if (CONFIG_LOW == config4MMKV) {
                return false
            }
            //测试环境返回true,高配
            //            if(BuildConfig.TestEnv){
            //                return true
            //            }
            //3.如果MMKV中也没，则只能返回默认的低配了
            if (BuildConfig.DEBUG) {
                return true
            }
            return false
        } else {
            return CONFIG_HIGH == mVehicleConfig
        }
    }

    /**
     * 是否支持座椅放平
     */
    @Deprecated("未集成自在，已不需要使用")
    fun isSupportSeat(): Boolean {
        //1.如果没有设置过配置值，则需要去拿一次值
        if (TextUtils.isEmpty(mSeatConfig)) {
            val vehicleConfig = PropReflectUtil.getProp(KEY_SEAT_CONFIG)
            CommonLogUtils.logD(TAG, "drmemoryseat=$vehicleConfig")
            //debug版本默认高配
            if (BuildConfig.DEBUG) {
                return true
            }
            if (CONFIG_SUPPORT_SEAT == vehicleConfig) {
                saveVehicleSeatConfig2MMKV(vehicleConfig)
                return true
            }
            if (CONFIG_NOT_SUPPORT_SEAT == vehicleConfig) {
                saveVehicleSeatConfig2MMKV(vehicleConfig)
                return false
            }
            //2.如果从系统属性中拿到的值是错误的，则需要从MMKV中拿缓存的值
            val config4MMKV = CustomApiMMKVUtils.instance.vehicleConfigSeat
            CommonLogUtils.logD(TAG, "drmemoryseat=$config4MMKV")
            if (CONFIG_SUPPORT_SEAT == config4MMKV) {
                return true
            }
            //3.如果MMKV中也没，则只能返回默认的低配了
            if (BuildConfig.DEBUG) {
                return true
            }
            return false
        } else {
            return CONFIG_SUPPORT_SEAT == mSeatConfig
        }
    }

    @Deprecated("不再通过customai区分配置，根据ccm能力下发区分配置")
    private fun saveVehicleConfig2MMKV(vehicleConfig: String?) {
        //只要正确的值才进行保存
        mVehicleConfig = vehicleConfig
        //获取一次就保存一次
        CustomApiMMKVUtils.instance.saveVehicleConfig(vehicleConfig)
    }

    @Deprecated("未集成自在，已不需要使用")
    private fun saveVehicleSeatConfig2MMKV(vehicleConfig: String?) {
        //只要正确的值才进行保存
        mSeatConfig = vehicleConfig
        //获取一次就保存一次
        CustomApiMMKVUtils.instance.saveVehicleConfigSeat(vehicleConfig)
    }

    fun getDaid(): String? {
        //1.如果没有设置过配置值，则需要去拿一次值
        if (TextUtils.isEmpty(mDaid)) {
            val vehicleConfig = PropReflectUtil.getProp(KEY_VEHICLE_DAID)
            CommonLogUtils.logI(TAG, "systemProp=$vehicleConfig")
            if (TextUtils.isEmpty(vehicleConfig)) {
                //2.如果从系统属性中拿到的值是错误的，则需要从MMKV中拿缓存的值
                val config4MMKV = CustomApiMMKVUtils.instance.getDaid()
                CommonLogUtils.logD(TAG, "systemProp4MMKV=$config4MMKV")
                mDaid = config4MMKV
            } else {
                saveDaid2MMKV(vehicleConfig)
            }
        }
        CommonLogUtils.logI(TAG, "getDaid:$mDaid")
        return mDaid
    }

    private fun saveDaid2MMKV(daid: String?) {
        //只要正确的值才进行保存
        mDaid = daid
        //获取一次就保存一次
        CustomApiMMKVUtils.instance.saveDaid(daid)
    }

    /**是否是航盛系统*/
    fun isHase(): Boolean {
        return getCarSystemType().equals("hsae", true)
    }

    /**通过系统反射拿到vin码*/
    fun getVin(): String? {
        //1.如果没有设置过配置值，则需要去拿一次值
        if (TextUtils.isEmpty(mVin)) {
            val vehicleConfig = PropReflectUtil.getProp(KEY_VEHICLE_VIN)
            CommonLogUtils.logI(TAG, "getVin systemProp=$vehicleConfig")
            if (TextUtils.isEmpty(vehicleConfig)) {
                //2.如果从系统属性中拿到的值是错误的，则需要从MMKV中拿缓存的值
                val config4MMKV = CustomApiMMKVUtils.instance.getVin()
                CommonLogUtils.logD(TAG, "getVin systemProp4MMKV=$config4MMKV")
                mVin = config4MMKV
            } else {
                saveVin2MMKV(vehicleConfig)
            }
        }
        return mVin
    }

    private fun saveVin2MMKV(vin: String?) {
        //只要正确的值才进行保存
        mVin = vin
        //获取一次就保存一次
        CustomApiMMKVUtils.instance.saveVin(vin)
    }
}