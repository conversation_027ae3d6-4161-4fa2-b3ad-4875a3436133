package com.dfl.android.common.base;

//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.dfl.android.common.global.GlobalConstant;
import com.dfl.android.commonlib.log.CommonLogUtils;

public abstract class BaseFragment extends Fragment {
    protected final String TAG = GlobalConstant.GLOBAL_TAG + this.getClass().getSimpleName();
    protected Context context;

    public BaseFragment() {
    }

    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        CommonLogUtils.logI(TAG, this.getClass().getSimpleName() + " onAttach");
        this.context = context;
    }

    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        CommonLogUtils.logI(TAG, this.getClass().getSimpleName() + " onCreate");
        this.initParams();
    }

    @Nullable
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
            @Nullable Bundle savedInstanceState) {
        CommonLogUtils.logI(TAG, this.getClass().getSimpleName() + " onCreateView");
        return inflater.inflate(this.getLayoutId(), container, false);
    }

    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        CommonLogUtils.logI(TAG, this.getClass().getSimpleName() + " onViewCreated");
        this.initData();
        this.initView(view);
    }

    @Override
    public void onViewStateRestored(@Nullable Bundle savedInstanceState) {
        super.onViewStateRestored(savedInstanceState);
        CommonLogUtils.logI(TAG, this.getClass().getSimpleName() + " onViewStateRestored");
    }

    @Override
    public void onStart() {
        super.onStart();
        CommonLogUtils.logI(TAG, this.getClass().getSimpleName() + " onStart");
    }

    @Override
    public void onResume() {
        super.onResume();
        CommonLogUtils.logI(TAG, this.getClass().getSimpleName() + " onResume");
    }

    @Override
    public void onPause() {
        super.onPause();
        CommonLogUtils.logI(TAG, this.getClass().getSimpleName() + " onPause");
    }

    @Override
    public void onStop() {
        super.onStop();
        CommonLogUtils.logI(TAG, this.getClass().getSimpleName() + " onStop");
    }

    public void onDestroyView() {
        super.onDestroyView();
        CommonLogUtils.logI(TAG, this.getClass().getSimpleName() + " onDestroyView");
    }

    public void onDestroy() {
        super.onDestroy();
        CommonLogUtils.logI(TAG, this.getClass().getSimpleName() + " onDestroy");
    }

    public void onDetach() {
        super.onDetach();
        CommonLogUtils.logI(TAG, this.getClass().getSimpleName() + " onDetach");
        this.context = null;
    }

    protected abstract void initParams();

    protected abstract void initView(View var1);

    protected abstract int getLayoutId();

    protected abstract void initData();

}
