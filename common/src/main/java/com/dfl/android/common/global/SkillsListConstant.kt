package com.dfl.android.common.global

/**
 *Created by 钟文祥 on 2024/7/25.
 *Describer: 能力id 集合
 */
class SkillsListConstant {
    companion object {

        //  一、触发条件
        //1.1、时间

        /**时间点*/
        const val SKILLS_ID_TRIGGER_TIME_TIME_POINT = 0xFF06
        const val INPUT_ARG_TRIGGER_TIME_HOUR_NAME = "hour"
        const val INPUT_ARG_TRIGGER_TIME_MINUTE_NAME = "minute"
        const val INPUT_ARG_TRIGGER_TIME_SECOND_NAME = "second"

        /**日出日落*/
        const val SKILL_ID_TRIGGER_TIME_DAY_STATUS = 0x729

        //1.2、环境
        /** 室内环境温度高于*/
        const val SKILLS_ID_TRIGGER_ENVIRONMENT_INNER_TEMPERATURE_HIGHER = 0x126

        /**室内环境温度低于*/
        const val SKILLS_ID_TRIGGER_ENVIRONMENT_INNER_TEMPERATURE_LOW = 0x127

        /**室外环境温度高于*/
        const val SKILLS_ID_TRIGGER_ENVIRONMENT_OUTSIDE_TEMPERATURE_HIGHER = 0x128

        /**室外环境温度低于*/
        const val SKILLS_ID_TRIGGER_ENVIRONMENT_OUTSIDE_TEMPERATURE_LOW = 0x129

        /**室内温度*/
        const val INPUT_ARG_TRIGGER_ENVIRONMENT_OUTSIDE_NAME = "OutsideTemp"

        /**室外温度*/
        const val INPUT_ARG_TRIGGER_ENVIRONMENT_INSIDE_TEMP = "InsideTemp"


        //1.3、位置
        /** 车辆到达  车辆离开  【车辆距离特定点的距离小于，车辆距离特定点的距离大于】*/
        const val SKILL_ID_TRIGGER_LOCATION_VEHICLE_ARRIVAL = 0x7C // 0x5
        const val SKILL_ID_TRIGGER_LOCATION_VEHICLE_DEPARTURE = 0x7B // 0x4

        /** 车辆到达  车辆离开  【位置 、 半径】*/
        const val INPUT_ARG_TRIGGER_LOCATION_LATITUDE = "latitude"
        const val INPUT_ARG_TRIGGER_LOCATION_LONGITUDE = "longitude"
        const val INPUT_ARG_TRIGGER_LOCATION_DISTANCE = "distance"


        //     1.4、驾驶
        /** 续航  大于、小于*/
        const val SKILL_ID_TRIGGER_DRIVE_BATTERY_LIFE_MORE = 0x839
        const val SKILL_ID_TRIGGER_DRIVE_BATTERY_LIFE_LESS = 0x83a

        /**电池电量  大于、小于*/
        const val SKILL_ID_TRIGGER_DRIVE_BATTERY_POWER_MORE = 0x81c
        const val SKILL_ID_TRIGGER_DRIVE_BATTERY_POWER_LESS = 0x81d

        /**车速 大于、小于*/
        const val SKILL_ID_TRIGGER_DRIVE_SPEED_MORE = 0xa06
        const val SKILL_ID_TRIGGER_DRIVE_SPEED_LESS = 0xa07

        /**驾驶时长 大于 小于*/
        const val SKILL_ID_TRIGGER_DRIVE_TIME_MORE = 0xa1d
        const val SKILL_ID_TRIGGER_DRIVE_TIME_LESS = 0xa1e

        /**驾驶里程 */
        const val SKILL_ID_TRIGGER_DRIVE_DISTANCE_MORE = 0xa17
        const val SKILL_ID_TRIGGER_DRIVE_DISTANCE_LESS = 0xa18

        /** 挡位 */
        const val SKILL_ID_TRIGGER_DRIVE_GEAR = 0x87f

        /** 续航里程*/
        const val INPUT_ARG_TRIGGER_DISTANCE_TO_EMPTY = "distanceToEmpty"

        /** 驾驶时长 */
        const val INPUT_ARG_TRIGGER_TRAVEL_TIME_STATE = "travelTimeState"

        /** 驾驶里程*/
        const val INPUT_ARG_TRIGGER_TRAVEL_DISTANCE_STATE = "travelDistanceState"

        /** 挡位*/
        const val INPUT_ARG_TRIGGER_TRAVEL_GEAR_STATE = "gearState"

        /** 车速*/
        const val INPUT_ARG_TRIGGER_TRAVEL_VEHICLE_SPEED = "vehicleSpeed"

        /** 电池电量 */
        const val INPUT_ARG_TRIGGER_TRAVEL_HV_BAT_SOC = "hvBatSOC"

        /**
         *  驾驶模式切换（舒适/激情）
         * 舒适 : 1, 运动: 2, 标准: 3,  自定义 : 4,  AI专属 : 5
         * */
        const val SKILL_ID_ACTION_DRIVE_MODE = 0x882
        const val INPUT_ARG_ACTION_DRIVE_MODE = "mode"

        /**
         * 前雨刮自动灵敏度设置（1-5挡）
         * 最低 : 1,低 : 2,中 : 3,高 : 4,最高 : 5,
         */
        const val SKILL_ID_ACTION_WIPER_SENSITIVITY = 0x615
        const val INPUT_ARG_ACTION_WIPER_SENSITIVITY = "period"
        //     1.5、车门

        /**全车门锁 0解锁1上锁*/
        const val SKILL_ID_TRIGGER_DOOR_ALL_DOOR_LOCK = 0x62d

        /**
         * 主驾、副驾、左后、右后车门打开/关闭状态
         */
        const val SKILL_ID_TRIGGER_DOOR_ALL_STATE = 0x605

        /** 左侧儿童锁,0解锁1上锁*/
        const val SKILL_ID_TRIGGER_DOOR_LEFT_CHILD_LOCK = 0x620

        /** 右侧儿童锁,0解锁1上锁*/
        const val SKILL_ID_TRIGGER_DOOR_RIGHT_CHILD_LOCK = 0x623

        /**任意车门打开 todo xll 正式输出后修正*/
        const val SKILL_ID_TRIGGER_DOOR_ANY_OPEN = 0x999

        /**
         * 1主驾2副驾3左后4右后
         */
        const val INPUT_ARG_TRIGGER_DOOR_DOOR_ID = "doorID"
        const val INPUT_ARG_TRIGGER_DOOR_STATUS_1 = "doorStatus1"
        const val INPUT_ARG_TRIGGER_DOOR_STATUS_2 = "doorStatus2"
        const val INPUT_ARG_TRIGGER_DOOR_STATUS_3 = "doorStatus3"
        const val INPUT_ARG_TRIGGER_DOOR_STATUS_4 = "doorStatus4"


        //     1.5、座椅

        /**
         * 主驾座椅占位状态反馈0x414
         * 离座0就座1
         */
        const val SKILL_ID_TRIGGER_SEAT_MAIN_DRIVER_TRIGGER_STATE = 0x414

        /**
         * 副驾座椅占位状态反馈0x416
         * 离座0就座1
         */
        const val SKILL_ID_TRIGGER_SEAT_COPILOT_TRIGGER_STATE = 0x416

        /**
         * 主驾安全带系上/解开状态反馈0x41b
         * 系上0解开2
         */
        const val SKILL_ID_TRIGGER_SEAT_MAIN_DRIVER_TRIGGER_SAFE_BELT = 0x41b

        /**
         * 副驾安全带系上/解开状态反馈0x41c
         * 系上0解开2
         */
        const val SKILL_ID_TRIGGER_SEAT_COPILOT_TRIGGER_SAFE_BELT = 0x41c

        /**
         *后排左安全带系上/解开状态反馈
         * 系上0解开2
         */
        const val SKILL_ID_TRIGGER_SEAT_BACK_LEFT_SAFE_BELT = 0x438

        /**
         *后排中安全带系上/解开状态反馈
         * 系上0解开2
         */
        const val SKILL_ID_TRIGGER_SEAT_BACK_MIDDLE_SAFE_BELT = 0x43a

        /**
         * 后排右安全带系上/解开状态反馈
         * 系上0解开2
         */
        const val SKILL_ID_TRIGGER_SEAT_BACK_RIGHT_SAFE_BELT = 0x439
        const val INPUT_ARG_TRIGGER_SEAT_IS_OCCUPIED = "isOccupied"
        const val INPUT_ARG_TRIGGER_SEAT_BELT_STATE = "beltState"

        //1.6灯光
        //远光灯
        const val SKILL_ID_TRIGGER_FAR_LIGHT = 0x268
        const val INPUT_ARG_TRIGGER_FAR_LIGHT = "status"

        //近光灯
        const val SKILL_ID_TRIGGER_NEAR_LIGHT = 0x269
        const val INPUT_ARG_TRIGGER_NEAR_LIGHT = "status"

        //后雾灯
        const val SKILL_ID_TRIGGER_FOG_LIGHT = 0x270
        const val INPUT_ARG_TRIGGER_FOG_LIGHT = "status"

        //1.6、充放电
        //充电
        const val SKILL_ID_TRIGGER_CHARGE = 0x891
        const val INPUT_ARG_TRIGGER_CHARGE = "status"

        //预计充电结束时间 （分 10/20/30/40/50/60分钟）
        const val SKILL_ID_TRIGGER_CHARGE_END_TIME = 0x893
        const val INPUT_ARG_TRIGGER_CHARGE_END_TIME = "time"

        //放电
        const val SKILL_ID_TRIGGER_DISCHARGE = 0x894
        const val INPUT_ARG_TRIGGER_DISCHARGE = "status"

        //     1.7、座舱感知 (触发)
        //检测位置是否有儿童
        const val SKILL_ID_TRIGGER_OMS_POSITION_CHILDREN = 0x7D

        //检测位置是否有人
        const val SKILL_ID_TRIGGER_OMS_POSITION_SOMEONE = 0x7E

        //检测位置性别
        const val SKILL_ID_TRIGGER_OMS_POSITION_GENDER = 0x7F

        //检测位置行为
        const val SKILL_ID_TRIGGER_OMS_POSITION_BEHAVIOR = 0x80

        const val INPUT_ARG_POSITION = "position" //驾驶位0、副驾驶1、后排左2、后排中3、后排右4、三排左5、三排中6、三排右7

        //oms触发条件
        const val INPUT_ARG_TRIGGER_SCSTATUS = "status" //有1、无0
        const val INPUT_ARG_TRIGGER_SCGENDER = "gender" // 0 男性 1 女性
        const val INPUT_ARG_TRIGGER_SCEXPRESSION = "expression" //0 自然 1 开心 2 悲伤 3 愤怒 4 惊讶 5 害怕 6 厌恶 7 负面
        const val INPUT_ARG_TRIGGER_SCBEHAVIOR = "behavior" //0 打电话  1 未打电话  2 抽烟  3 未抽烟 4 喝水  5 未喝水


        //oms状态条件
        const val INPUT_ARG_STATUS_SCSTATUS = "scStatus" //有1、无0
        const val INPUT_ARG_STATUS_SCGENDER = "scGender" // 0 男性 1 女性
        const val INPUT_ARG_STATUS_SCEXPRESSION = "scExpression" //0 自然 1 开心 2 悲伤 3 愤怒 4 惊讶 5 害怕 6 厌恶 7 负面
        const val INPUT_ARG_STATUS_SCBEHAVIOR = "scBehavior" //0 打电话  1 未打电话  2 抽烟  3 未抽烟 4 喝水  5 未喝水


        //  二、状态条件
        //     2.1、时间

        /** 时间区间 */
        const val SKILL_ID_STATUS_TIME_INTERVAL_ID = 0xFF05

        /**  周几*/
        const val SKILL_ID_STATUS_TIME_WHICH_DAY_ID = 0xFF02

        /** 工作日*/
        const val SKILL_ID_STATUS_TIME_WORKDAY_ID = 0xFF03

        /** 节假日 */
        const val SKILL_ID_STATUS_TIME_HOLIDAY_ID = 0xFF04

        /** 某日  */
        const val SKILL_ID_STATUS_TIME_SOME_DAY_ID = 0xFF00

        /** 周一*/
        const val SKILL_ID_STATUS_TIME_WHICH_DAY_ONE_ID = 0x01

        /**周二*/
        const val SKILL_ID_STATUS_TIME_WHICH_DAY_TWO_ID = 0x02

        /** 周三*/
        const val SKILL_ID_STATUS_TIME_WHICH_DAY_THREE_ID = 0x04

        /** 周四*/
        const val SKILL_ID_STATUS_TIME_WHICH_DAY_FOUR_ID = 0x08

        /** 周五*/
        const val SKILL_ID_STATUS_TIME_WHICH_DAY_FIVE_ID = 0x10

        /**周六 */
        const val SKILL_ID_STATUS_TIME_WHICH_DAY_SIX_ID = 0x20

        /** 周日 */
        const val SKILL_ID_STATUS_TIME_WHICH_DAY_SEVEN_ID = 0x40

        /**每天  */
        const val SKILL_ID_STATUS_TIME_EVERY_DAY_ID = 0x7F

        const val INPUT_ARG_STATUS_TIME_DAY = "day"
        const val INPUT_ARG_STATUS_TIME_START_HOUR = "start_hour"
        const val INPUT_ARG_STATUS_TIME_START_MINUTE = "start_minute"
        const val INPUT_ARG_STATUS_TIME_START_SECOND = "start_second"
        const val INPUT_ARG_STATUS_TIME_STOP_HOUR = "stop_hour"
        const val INPUT_ARG_STATUS_TIME_STOP_MINUTE = "stop_minute"
        const val INPUT_ARG_STATUS_TIME_STOP_SECOND = "stop_second"

        //     2.2、环境

        /** 车外天气 有雨*/
        const val SKILL_ID_STATUS_ENVIRONMENT_CAR_OUTSIDE_WEATHER_RAIN = 0x14f

        /** 车外天气 无雨 */
        const val SKILL_ID_STATUS_ENVIRONMENT_CAR_OUTSIDE_WEATHER_NO_RAIN = 0x14e

        /** 天气预报 */
        const val SKILL_ID_STATUS_ENVIRONMENT_WEATHER = 0x6b

        /**  空气质量 */
        const val SKILL_ID_STATUS_ENVIRONMENT_AIR_QUALITY = 0x6f

        /**车内湿度 小于 */
        const val SKILL_ID_STATUS_ENVIRONMENT_HUMIDITY_IN_CAR_LESS = 0x14b

        /** 车内湿度 大于 */
        const val SKILL_ID_STATUS_ENVIRONMENT_HUMIDITY_IN_CAR_MORE = 0x14a

        /** 车外天气InputName*/
        const val INPUT_ARG_STATUS_ENVIRONMENT_WIPER_GEA_VALUE = "RainSizeSts"

        /** 车内湿度InputName */
        const val INPUT_ARG_STATUS_ENVIRONMENT_HUMIDITY_VALUE = "Humidityvalue"


        //     2.3、位置
        /** 车辆位于 不位于 【获取车辆距离特定点的距离小于、获取车辆距离特定点的距离大于】*/
        const val SKILL_ID_STATUS_LOCATION_LOCATED_AT = 0x3
        const val SKILL_ID_STATUS_LOCATION_LOCATED_AT_NOT = 0x2

        //     2.4、导航
        /**导航预计到达时间*/
        const val SKILL_ID_STATUS_NAVIGATION_EXPECTED_ARRIVAL_TIME = 0x61

        /**导航预计到达距离*/
        const val SKILL_ID_STATUS_NAVIGATION_EXPECTED_ARRIVAL_DISTANCE = 0x5f

        /**时*/
        const val INPUT_ARG_STATUS_NAVIGATION_H = "h"

        /**分*/
        const val INPUT_ARG_STATUS_NAVIGATION_MIN = "min"

        //2.5、门窗

        /**全车门锁 */
        const val SKILL_ID_STATUS_DOOR_ALL_DOOR_LOCK = 0x602

        /**后背门（低配）,行李箱 */
        const val SKILL_ID_STATUS_DOOR_DOOR_BACK = 0x60a

        /** 电动后背门（高配） */
        const val SKILL_ID_STATUS_DOOR_ELECTRIC_DOOR_BACK = 0x60d

        /**左侧儿童锁 */
        const val SKILL_ID_STATUS_DOOR_LEFT_CHILD_LOCK = 0x61e

        /** 右侧儿童锁*/
        const val SKILL_ID_STATUS_DOOR_RIGHT_CHILD_LOCK = 0x621

        /** 主驾车窗 */
        const val SKILL_ID_STATUS_DOOR_WINDOW_DRIVER_MAIN = 0x307

        /** 副驾车窗  */
        const val SKILL_ID_STATUS_DOOR_WINDOW_DRIVER_CHIEF = 0x30d

        /**左后车窗 */
        const val SKILL_ID_STATUS_DOOR_WINDOW_BACK_LEFT = 0x313

        /** 右后车窗 */
        const val SKILL_ID_STATUS_DOOR_WINDOW_BACK_RIGHT = 0x319

        //     2.6、座椅
        /** 主驾座椅占位状态反馈*/
        const val SKILL_ID_STATUS_SEAT_MAIN_DRIVER_STATUS_STATE = 0x413

        /**副驾座椅占位状态反馈 */
        const val SKILL_ID_STATUS_SEAT_COPILOT_STATUS_STATE = 0x415

        /**主驾安全带系上/解开状态反馈*/
        const val SKILL_ID_STATUS_SEAT_MAIN_DRIVER_STATUS_SAFE_BELT = 0x41d

        /** 副驾安全带系上/解开状态反馈 */
        const val SKILL_ID_STATUS_SEAT_COPILOT_STATUS_SAFE_BELT = 0x41e

        /** 后排左安全带系上/解开状态反馈 */
        const val SKILL_ID_STATUS_SEAT_BACK_LEFT_STATUS_SAFE_BELT = 0x43b

        /**后排中安全带系上/解开状态反馈 */
        const val SKILL_ID_STATUS_SEAT_BACK_MIDDLE_STATUS_SAFE_BELT = 0x43d

        /**后排右安全带系上/解开状态反馈 */
        const val SKILL_ID_STATUS_SEAT_BACK_RIGHT_STATUS_SAFE_BELT = 0x43c

        //     2.7、连接
        /**蓝牙开关状态，0-关闭，1-打开*/
        const val SKILL_ID_STATUS_LINK_BLUETOOTH_SWITCH = 0x715

        /**蓝牙连接状态，0-未连接，1-已连接*/
        const val SKILL_ID_STATUS_LINK_BLUETOOTH_CONNECTION = 0x716

        /** 热点开关状态，0-关闭，1-打开  */
        const val SKILL_ID_STATUS_LINK_HOTSPOT_SWITCH = 0x717

        /** 热点连接状态，0-未连接，1-已连接 */
        const val SKILL_ID_STATUS_LINK_HOTSPOT_CONNECTION = 0x718

        /**  WIFI开关状态，0-关闭，1-打开 */
        const val SKILL_ID_STATUS_LINK_WLAN_SWITCH = 0x719

        /** WIFI连接状态，0-未连接，1-已连接  */
        const val SKILL_ID_STATUS_LINK_WLAN_CONNECTION = 0x71a

        /** 移动网络打开/关闭状态 */
        const val SKILL_ID_STATUS_LINK_MOBILE_SWITCH = 0x71b

        /** 连接状态的参数名称 */
        const val INPUT_ARG_STATUS_LINK_CONNECTION_STATUS = "connectionStatus"


        // 2.8、智能设备

        /** 空气质量 (空气净化器) */
        const val SKILL_ID_STATUS_SMART_DEVICE_AIR_QUALITY = 0xb06

        /** 乘坐状态 (儿童座椅)*/
        const val SKILL_ID_STATUS_SMART_DEVICE_RIDING_STATUS = 0xb1d

        const val INPUT_ARG_STATUS_SMART_AIR_QUALITY = "airQuality"

        /**
         * 关闭0打开1
         * 暂停:0,播放:1
         * 解锁0上锁1
         * 空座0就坐1
         * 儿童锁 解锁0上锁1
         * 电动后背门 关闭1打开2停止3
         * 香薰浓度 浓郁1淡雅2
         * 按摩温度 关闭0低1中2高3
         * 空气质量 差0中1优2
         * 炫彩氛围灯开关设置 关: 1, 开 : 2
         * 等离子香氛开关设置 关: 0, 开 : 1
         * 等离子香氛浓度设置 清新:1, 浓香 :2,淡香 : 3
         */
        const val INPUT_ARG_STATUS = "status"


        //     2.9、其他
        /**左前车轮 */
        const val SKILL_ID_STATUS_OTHER_TIRE_PRESSURE_LEFT_FRONT_WHEEL = 0x519

        /** 右前车轮  */
        const val SKILL_ID_STATUS_OTHER_TIRE_PRESSURE_RIGHT_FRONT_WHEEL = 0x51b

        /** 左后车轮 */
        const val SKILL_ID_STATUS_OTHER_TIRE_PRESSURE_LEFT_AFTER_WHEEL = 0x51a

        /** 右后车轮 */
        const val SKILL_ID_STATUS_OTHER_TIRE_PRESSURE_RIGHT_AFTER_WHEEL = 0x51c

        const val INPUT_ARG_STATUS_OTHER_TIRE_PRESSURE_LOW_STATUS = "tirePressureLowStatus"

        //     2.10、座舱感知 (状态)
        //检测位置是否有儿童
        const val SKILL_ID_STATUS_OMS_POSITION_CHILDREN = 0x77

        //检测位置是否有人
        const val SKILL_ID_STATUS_OMS_POSITION_SOMEONE = 0x78

        //检测位置性别
        const val SKILL_ID_STATUS_OMS_POSITION_GENDER = 0x79

        //        //检测位置表情
        //        const val SKILL_ID_STATUS_OMS_POSITION_EXPRESSION = 0x7B

        //检测位置行为
        const val SKILL_ID_STATUS_OMS_POSITION_BEHAVIOR = 0x7A

        //2.11灯光
        //远光灯
        const val SKILL_ID_STATUS_FAR_LIGHT = 0x20d
        const val INPUT_ARG_STATUS_FAR_LIGHT = "lampSts"

        //近光灯
        const val SKILL_ID_STATUS_NEAR_LIGHT = 0x20a
        const val INPUT_ARG_STATUS_NEAR_LIGHT = "lampSts"

        //后雾灯
        const val SKILL_ID_STATUS_FOG_LIGHT = 0x211
        const val INPUT_ARG_STATUS_FOG_LIGHT = "lampSts"


        //  三、动作
        //     3.1、推荐
        /**延时*/
        const val SKILL_ID_ACTION_RECOMMEND_DELAY_ID = 0xFF07

        //     3.2、空调
        /**空调开关 */
        const val SKILL_ID_ACTION_AIR_CONDITIONER_OPEN = 0x100

        /**空调温度 （单区） */
        const val SKILL_ID_ACTION_AIR_CONDITIONER_TEMPERATURE = 0x102

        /** 空调温度 （双区的主驾驶） */
        const val SKILL_ID_ACTION_AIR_CONDITIONER_TEMPERATURE_TWO_LEFT = 0x166

        /** 空调温度 （双区的副驾驶） */
        const val SKILL_ID_ACTION_AIR_CONDITIONER_TEMPERATURE_TWO_RIGHT = 0x167

        /** 空调风量*/
        const val SKILL_ID_ACTION_AIR_CONDITIONER_AIR_VOLUME = 0x103

        /** 吹风模式 */
        const val SKILL_ID_ACTION_AIR_CONDITIONER_BLOW_MODE = 0x104

        /** 循环模式*/
        const val SKILL_ID_ACTION_AIR_CONDITIONER_INNER_LOOP = 0x109

        /** 前除雾打开 */
        const val SKILL_ID_ACTION_AIR_CONDITIONER_FRONT_DEFOG_OPEN = 0x105

        /**后除雾打开*/
        const val SKILL_ID_ACTION_AIR_CONDITIONER_AFTER_DEFOG_OPEN = 0x107

        /** auto模式开闭*/
        const val SKILL_ID_ACTION_AIR_CONDITIONER_AUTO_MODE_OPEN = 0x10c

        /**制冷制热开闭 */
        const val SKILL_ID_ACTION_AIR_CONDITIONER_COOLING_HEATING_OPEN = 0x10e

        /** 送风打开*/
        const val SKILL_ID_ACTION_AIR_CONDITIONER_BLOWER_OPEN = 0x110

        /** 极速制冷模式 */
        const val SKILL_ID_ACTION_AIR_MAX_COOL = 0x161

        /**极速制热模式 */
        const val SKILL_ID_ACTION_AIR_MAX_HEAT = 0x160

        /**同步*/
        const val SKILL_ID_ACTION_AIR_SYNC = 0x16f

        /**节能模式*/
        const val SKILL_ID_ACTION_AIR_ENERGY_CONSERVATION = 0x169

        /**自干燥*/
        const val SKILL_ID_ACTION_AIR_SELF_DESICCATION = 0x168


        /**冰箱同步Input Name*/
        const val INPUT_ARG_ACTION_AIR_SYNC = "ACSyncModeSw"

        /**节能模式Input Name*/
        const val INPUT_ARG_ACTION_AIR_ENERGY_CONSERVATION = "AirECOModetSw"

        /**自干燥Input Name*/
        const val INPUT_ARG_ACTION_AIR_SELF_DESICCATION = "AirDelayBlowtSw"


        /**空调开关Input Name */
        const val INPUT_ARG_ACTION_AIR_CONDITIONER_SWITCH_NAME = "hvacswstate"

        /**前除雾开闭Input Name */
        const val INPUT_ARG_ACTION_AIR_CONDITIONER_FRONT_DEFOG_NAME = "defrostswState"

        /**后除雾开闭Input Name*/
        const val INPUT_ARG_ACTION_AIR_CONDITIONER_AFTER_DEFOG_NAME = "RedefrostSwstate"

        /** AUTO Input Name */
        const val INPUT_ARG_ACTION_AIR_CONDITIONER_AUTO_NAME = "AutoSwstate"

        /**制冷制热开闭Input Name*/
        const val INPUT_ARG_ACTION_AIR_CONDITIONER_COOL_HEAT_NAME = "ACSwstate"

        /**鼓风Input Name */
        const val INPUT_ARG_ACTION_AIR_CONDITIONER_BLOW_NAME = "fanonlySW"

        /**
         * 循环Input Name
         */
        const val INPUT_ARG_ACTION_AIR_CONDITIONER_LOOP_NAME = "recirc"

        /**
         * 温度设置Input Name
         */
        const val INPUT_ARG_ACTION_AIR_CONDITIONER_TEMPERATURE_NAME = "temp"

        /**
         * 风量挡位Input Name
         */
        const val INPUT_ARG_ACTION_AIR_CONDITIONER_AIR_VOLUME_NAME = "speedgear"

        /**
         * 吹风模式Input Name
         */
        const val INPUT_ARG_ACTION_AIR_CONDITIONER_BLOW_MODE_NAME = "blowmode"

        /**
         * 极速制热模式Input Name
         */
        const val INPUT_ARG_ACTION_AIR_MAX_HEAT_NAME = "AirMaxHeatSw"

        /**
         * 极速制冷模式Input Name
         */
        const val INPUT_ARG_ACTION_AIR_MAX_COOL_NAME = "AirMaxCoolSw"

        /**
         * 温度设置Input Default Value
         */
        const val INPUT_ARG_ACTION_AIR_CONDITIONER_TEMPERATURE_DEFAULT_VALUE = "20"

        /**
         * 风量挡位Input Default Value
         */
        const val INPUT_ARG_ACTION_AIR_CONDITIONER_AIR_VOLUME_DEFAULT_VALUE = "0"

        /**
         * 吹风模式Input Default Value
         */
        const val INPUT_ARG_ACTION_AIR_CONDITIONER_BLOW_MODE_DEFAULT_VALUE = "0"

        /**
         * 默认值为0
         */
        const val INPUT_ARG_ACTION_AIR_CONDITIONER_DEFAULT_VALUE = "0"


        //     3.3、门窗
        /**
         * 左前车窗开度
         */
        const val SKILL_ID_ACTION_DOOR_WINDOW_DRIVER_MAIN_PERCENT = 0x300

        /**
         * 右前车窗开度
         */
        const val SKILL_ID_ACTION_DOOR_WINDOW_DRIVER_CHIEF_PERCENT = 0x301

        /**
         * 左后车窗开度
         */
        const val SKILL_ID_ACTION_DOOR_WINDOW_BACK_LEFT_PERCENT = 0x302

        /**
         * 右后车窗开度
         */
        const val SKILL_ID_ACTION_DOOR_WINDOW_BACK_RIGHT_PERCENT = 0x303

        /**
         * 所有车窗，默认关闭
         */
        const val SKILL_ID_ACTION_DOOR_WINDOW_ALL_OPEN = 0x304

        /**
         * 车窗透气模式打开
         */
        const val SKILL_ID_ACTION_DOOR_WINDOW_VENTILATE_OPEN = 0x31f

        /**
         * 车窗通风
         */
        const val SKILL_ID_ACTION_DOOR_WINDOW_IN_FRESH_AIR = 0x306

        /**天窗*/
        const val SKILL_ID_ACTION_DOOR_WINDOW_SKYLIGHT = 0x324
        const val INPUT_ARG_ACTION_DOOR_WINDOW_SKYLIGHT = "level"

        /**电动遮阳帘*/
        const val SKILL_ID_ACTION_DOOR_WINDOW_ELECTRIC_SUNSHADE_CURTAIN = 0x326
        const val INPUT_ARG_ACTION_DOOR_WINDOW_ELECTRIC_SUNSHADE_CURTAIN = "positon"

        /**普通后背门,行李箱解锁 */
        const val SKILL_ID_ACTION_DOOR_BACK_DOOR_NORMAL_LOCK = 0x609

        /**
         * 电动后背门 开闭
         */
        const val SKILL_ID_ACTION_DOOR_BACK_DOOR_ELECTRIC_LOCK = 0x60b

        /**
         * 左侧儿童锁
         */
        const val SKILL_ID_ACTION_DOOR_LEFT_CHILD_LOCK = 0x62a

        /**
         * 右侧儿童锁
         */
        const val SKILL_ID_ACTION_DOOR_RIGHT_CHILD_LOCK = 0x62b

        const val INPUT_ARG_ACTION_DOOR_LEVEL = "level"


        //     3.4、灯光

        /**
         * 大灯打招呼灯语
         */
        const val SKILL_ID_ACTION_LIGHT_LAMP_LANGUAGE_MODE_ID = 0x220

        /**
         * 大灯主题soa灯语
         */
        const val SKILL_ID_ACTION_LIGHT_SOA = 0x223

        /**
         * 大灯场景灯效功能联动
         */
        const val SKILL_ID_ACTION_LIGHT_SCENE_EFFECT = 0x221

        /**
         * 雾灯
         */
        const val SKILL_ID_ACTION_LIGHT_FOG_LIGHT_ID = 0x206

        /**
         * 星环灯状态
         */
        const val SKILL_ID_ACTION_LIGHT_STAR_LIGHT_STATUS = 0x266

        /**
         * 氛围灯开关
         */
        const val SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_SWITCH_ID = 0x218

        /**
         * 氛围灯模式
         */
        const val SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_MODE_ID = 0x219

        /**
         * 氛围灯亮度
         */
        const val SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_LIGHT_ID = 0x21a

        /**
         * 氛围灯颜色主题
         */
        const val SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_COLOR_THEME_ID = 0x217

        /**
         * 氛围灯自定义颜色
         */
        const val SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_COLOR_ID = 0x21b

        /**
         * 氛围灯主题soa灯语
         */
        const val SKILL_ID_ACTION_LIGHT_AMBIENT_SOA = 0x222

        /**
         * 氛围灯场景灯语
         */
        const val SKILL_ID_ACTION_LIGHT_SCENE_EFFECT_ID = 0x202

        /**
         * 室内灯 开关
         */
        const val SKILL_ID_ACTION_LIGHT_READ_LIGHT_SWITCH = 0x264

        /**
         * 室内灯 （阅读灯） 主题
         */
        const val SKILL_ID_ACTION_LIGHT_READ_LIGHT_MODE = 0x265

        /**
         * 室内灯 （阅读灯） 自定义
         */
        const val SKILL_ID_ACTION_LIGHT_READ_LIGHT_CUSTOM = 0x267


        /**
         * 尾灯自定义灯语
         */
        const val SKILL_ID_ACTION_LIGHT_DIY = 0x209

        /**
         *  SOA灯语 春节1元旦2生日快乐3Nissan4浪漫5
         *  播放控制 上一首1下一首2 , 暂停0播放1
         *  声浪音量 低1中2高3
         */
        const val INPUT_ARG_TYPE = "type"
        const val INPUT_ARG_ACTION_LIGHT_DIY_LAMPEFFECT = "lampEffect"
        const val INPUT_ARG_ACTION_LIGHT_SCENE_EFFECT_SCENEID = "sceneID"
        const val INPUT_ARG_ACTION_LIGHT_SCENE_EFFECT_SCENEEFFECTSETSTS = "sceneEffectSetSts"
        const val INPUT_ARG_ACTION_LIGHT_LAMP_LANGUAGE_MODE_NAME = "signal"


        /**
         * 雾灯
         */
        const val INPUT_ARG_ACTION_LIGHT_READ_LIGHT_MODE_NAME = "lightMode"

        /**
         * 室内灯主题
         */
        const val INPUT_ARG_ACTION_LIGHT_READ_LIGHT_THEME_NAME = "theme"

        /**
         * 氛围灯开关
         */
        const val INPUT_ARG_ACTION_LIGHT_AMBIENT_LIGHT_MODE_NAME = "atmosphereLightMode"

        /**
         * 氛围灯模式
         */
        const val INPUT_ARG_ACTION_LIGHT_AMBIENT_LIGHT_BRIGHTNESS_NAME = "brightness"

        /**色温*/
        const val INPUT_ARG_ACTION_LIGHT_AMBIENT_LIGHT_TEMPERATURE = "colour"

        /**
         * 氛围灯颜色自定义
         */
        const val INPUT_ARG_ACTION_LIGHT_AMBIENT_LIGHT_COLOR_NAME = "color"

        /**
         * 氛围灯颜色
         */
        const val INPUT_ARG_ACTION_LIGHT_AMBIENT_LIGHT_COLOR_THEME_NAME = "themeId"

        /**炫彩氛围灯颜色*/
        const val INPUT_ARG_ACTION_COLORFUL_AMBIENT_LIGHT_COLOR_DETAILED_COLOR_NAME = "detailedColor"

        /**等离子香氛香型*/
        const val INPUT_ARG_ACTION_SMART_PLASMA_FRAGRANCE_FLAVOR_SMART_NAME = "flavor"

        /**
         * 氛围灯颜色主题 暖阳1海风2星光3森林4白昼5晚风6
         * 播放模式 列表1随机2单曲3
         * 沉浸音效 自然原声:1,动感激情:2,身临其境:3,轻松舒适:4,高保真原声:5,影院模式:6,重低音增强:7
         * 颈枕按摩模式 振动1捶打2揉捏3
         * 座椅按摩 重1 中2 轻3
         */
        const val INPUT_ARG_MODE = "mode"

        /**
         * 大灯组合模式控制（模拟大灯组合开关挡位）
         */
        const val SKILL_ID_ACTION_EXTERIOR_LIGHT_MODE_CONTROL = 0x205
        const val INPUT_ARG_EXTERIOR_LIGHT_MODE_CONTROL = "lampMode"

        /**
         * 大灯高度挡位设置（低/中/标准/高）
         */
        const val SKILL_ID_ACTION_EXTERIOR_LIGHT_HEIGHT = 0x207
        const val INPUT_ARG_EXTERIOR_LIGHT_MODE_HEIGHT = "frLampLevel"

        /**
         * 近光灯开启/关闭状态反馈
         */
        const val SKILL_ID_STATUS_EXTERIOR_LIGHT_LOW_STATUS = 0x20a

        /**
         * 远光灯开启/关闭状态反馈
         */
        const val SKILL_ID_STATUS_EXTERIOR_LIGHT_HIGH_STATUS = 0x20d

        /**
         * 后雾灯开启/关闭状态反馈
         */
        const val SKILL_ID_STATUS_EXTERIOR_LIGHT_FOG_LAMP_STATUS = 0x211

        /**
         * 设置大灯音乐律动
         *  打开 : 0 , 关闭 : 1
         *  鼓点 : 1 , 频谱 : 2
         */
        const val SKILL_ID_ACTION_EXTERIOR_LIGHT_MUSIC_EFFECT = 0x271
        const val INPUT_ARG_EXTERIOR_LIGHT_MUSIC_EFFECT_STATUS = "status"
        const val INPUT_ARG_EXTERIOR_LIGHT_MUSIC_EFFECT_EFFECT = "effect"


        //     3.5、座椅

        /**
         * 主驾座椅记忆设置
         */
        const val SKILL_ID_ACTION_SEAT_MAIN_DRIVER_SEAT_MEMORY_SETTING = 0x409

        /**
         * 副驾座椅记忆设置
         */
        const val SKILL_ID_ACTION_SEAT_COPILOT_SEAT_MEMORY_SETTING = 0x40a

        /**
         * 座椅记忆设置
         */
        const val INPUT_ARG_ACTION_SEAT_POSITION_MODE = "positionMode"

        /**
         * 主驾座椅加热
         */
        const val SKILL_ID_ACTION_SEAT_MAIN_DRIVER_SEAT_HEATING_SETTING = 0x405

        /**
         * 副驾座椅加热
         */
        const val SKILL_ID_ACTION_SEAT_COPILOT_SEAT_HEATING_SETTING = 0x407

        /**
         * 座椅加热
         */
        const val INPUT_ARG_ACTION_SEAT_HEAT_LEVEL = "heatLevel"

        /**后排左座椅加热*/
        const val SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_HEATING = 0x441

        /**后排右座椅加热*/
        const val SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_HEATING = 0x442

        /**
         * 主驾座椅通风
         */
        const val SKILL_ID_ACTION_SEAT_MAIN_DRIVER_SEAT_AIR_SETTING = 0x406

        /**
         * 副驾座椅通风
         */
        const val SKILL_ID_ACTION_SEAT_COPILOT_SEAT_AIR_SETTING = 0x408

        /**后排左座椅通风*/
        const val SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_AIR = 0x443

        /**后排右座椅通风*/
        const val SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_AIR = 0x444

        /**
         * 座椅通风
         */
        const val INPUT_ARG_ACTION_SEAT_VENT_LEVEL = "ventLevel"

        /**
         * 主驾座椅按摩 开关
         * 关闭0打开1
         */
        const val SKILL_ID_ACTION_SEAT_LEFT_SEAT_MASSAGE_SWITCH = 0x42f

        /**
         * 主驾座椅按摩 力度
         * 重1 中2 轻3
         */
        const val SKILL_ID_ACTION_SEAT_LEFT_SEAT_MASSAGE_INTENSITY = 0x430

        /**
         * 主驾座椅按摩 模式
         * 全身波浪:1,全身猫步:2,肩部敲击:3,肩部单排:4,背部波浪:5,背部脉冲:6,腰部波浪:7,腰部脉冲:8,臀腿波浪:9,臀腿脉冲:10
         */
        const val SKILL_ID_ACTION_SEAT_LEFT_SEAT_MASSAGE_MODE = 0x433

        /**
         * 副驾座椅按摩 开关
         */
        const val SKILL_ID_ACTION_SEAT_RIGHT_SEAT_MASSAGE_SWITCH = 0x431

        /**
         * 副驾座椅按摩 力度
         */
        const val SKILL_ID_ACTION_SEAT_RIGHT_SEAT_MASSAGE_INTENSITY = 0x432

        /**
         * 副驾座椅按摩 模式
         * 全身波浪:1,全身猫步:2,肩部敲击:3,肩部单排:4,背部波浪:5,背部脉冲:6,腰部波浪:7,腰部脉冲:8,臀腿波浪:9,臀腿脉冲:10
         */
        const val SKILL_ID_ACTION_SEAT_RIGHT_SEAT_MASSAGE_MODE = 0x434

        /**后排左按摩 关闭*/
        const val SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_MASSAGE_OFF = 0x449

        /**后排左按摩 力度*/
        const val SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_MASSAGE_INTENSITY = 0x445

        /**后排右按摩 关闭*/
        const val SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_MASSAGE_OFF = 0x450

        /**后排右按摩 力度*/
        const val SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_MASSAGE_INTENSITY = 0x446

        /**后排左按摩模式*/
        const val SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_MASSAGE_MODE = 0x447

        /**后排右按摩模式*/
        const val SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_MASSAGE_MODE = 0x448


        /**
         * 座椅按摩 关闭0打开1
         */
        const val INPUT_ARG_ACTION_SEAT_MASSAGE_SW = "sw"

        /**
         * 座椅按摩 力度
         */
        const val INPUT_ARG_ACTION_SEAT_MASSAGE_INTENSITY = "massageIntensity"

        /**
         * 座椅按摩 模式
         */
        const val INPUT_ARG_ACTION_SEAT_MASSAGE_MODE = "massageMode"

        /**
         * 主驾一键复位
         */
        const val SKILL_ID_ACTION_SEAT_LEFT_SEAT_RESET = 0x435

        /**
         * 主副驾一键复位参数
         */
        const val INPUT_ARG_ACTION_SEAT_RESET = "SASReq"

        /**
         * 副驾一键复位
         */
        const val SKILL_ID_ACTION_SEAT_RIGHT_SEAT_RESET = 0x436

        /**
         * 后排尊享
         */
        const val SKILL_ID_ACTION_SEAT_AI_BACK_SEAT = 0x437

        /**
         * 后排尊享参数
         */
        const val INPUT_ARG_ACTION_AI_BACK_SEAT = "status"

        /**后排左座椅一键折叠*/
        const val SKILL_ID_ACTION_SEAT_AI_BACK_LEFT_SEAT_FOLD = 0x43e

        /**后排右座椅一键折叠*/
        const val SKILL_ID_ACTION_SEAT_AI_BACK_RIGHT_SEAT_FOLD = 0x43f

        /**零重力座椅*/
        const val SKILL_ID_ACTION_SEAT_AI_ZERO_G_SEAT = 0x440

        //------------冰箱---------------
        /**冰箱开关 打开0关闭1*/
        const val SKILL_ID_ACTION_FRIDGE_SWITCH = 0x16a
        const val INPUT_ARG_ACTION_FRIDGE_SWITCH = "ICESw"

        /**冰箱模式 热1冷2*/
        const val SKILL_ID_ACTION_FRIDGE_MODE = 0x16b
        const val INPUT_ARG_ACTION_FRIDGE_MODE = "ICEcoolheatstatus"

        /**制热温度*/
        const val SKILL_ID_ACTION_FRIDGE_COOLING_TEMP = 0x16c

        /**制冷温度*/
        const val SKILL_ID_ACTION_FRIDGE_HEATING_TEMP = 0x16d
        const val INPUT_ARG_TEMP = "temp"

        /**冰箱便捷场景 关闭:0,快速冷藏:1,饮品冰镇:2,蔬果解封:3,快速加热:4,热奶保温:5*/
        const val SKILL_ID_ACTION_FRIDGE_CONVENIENCE_MODE = 0x170

        /**离车工作时间*/
        const val SKILL_ID_ACTION_FRIDGE_DEPARTURE_WORKING_TIME = 0x171
        const val INPUT_ARG_TIME = "time"

        /**离车工作关闭*/
        const val SKILL_ID_ACTION_FRIDGE_DEPARTURE_WORKING_OFF = 0x172
        const val INPUT_ARG_SWITCH = "switch"

        /**冰箱节能模式 打开0关闭1*/
        const val SKILL_ID_ACTION_FRIDGE_ECO_MODE = 0x173

        //     3.5、应用

        /** 导航至家或公司*/
        const val SKILL_ID_ACTION_NAV_HOME_AND_COMPANY = 0x62
        const val INPUT_ARG_NAV_HOME_COMPANY = "favoriteType"

        /**导航去某地*/
        const val SKILL_ID_ACTION_NAV_GO_SOME_WHERE = 0x64

        /**去充电*/
        const val SKILL_ID_ACTION_NAV_GO_CHARGE = 0x81
        const val INPUT_ARG_NAV_GO_CHARGE = "type"

        // TODO: 钟文祥，修改此能力id
        /**回家回公司去充电+途经点*/
        const val SKILL_ID_ACTION_NAV_GO_SELECT_AND_MID = 0x810
        const val INPUT_ARG_ACTION_NAV_GO_SELECT_AND_MID = "routePoint"

        /**
         * 参数名称，导航至特定地址
         */
        const val INPUT_ARG_ACTION_APP_GO_KEYWORD = "keyWord"

        /**路线偏好设置 */
        const val SKILL_ID_ACTION_NAV_ROUTE = 0x63
        const val INPUT_ARG_ACTION_NAV_ROUTE = "strategy"


        //打开多媒体
        /**播放多媒体*/
        const val SKILL_ID_ACTION_OPEN_MEDIA = 0x50

        /**关闭多媒体*/
        const val SKILL_ID_ACTION_MEDIA_CLOSE = 0x29

        //播放模式
        /**QQ音乐播放模式切换*/
        const val SKILL_ID_ACTION_MEDIA_PLAY_MODE = 0x56

        //QQ音乐播放特定曲风歌单
        const val SKILL_ID_ACTION_MEDIA_QQ_SONG_STYLE = 0x53

        /**
         * 流行:1,中国风:2,民谣:3,摇滚:4,轻音乐:5,电子:6,爵士:7,乡村:8
         */
        const val INPUT_ARG_ACTION_MEDIA_GENRE = "genre"

        //QQ音乐播放指定歌单
        const val SKILL_ID_ACTION_MEDIA_PLAY_LIST = 0x54


        //QQ音乐播放特定心情歌单
        const val SKILL_ID_ACTION_MEDIA_MOOD = 0x51
        const val INPUT_ARG_ACTION_MEDIA_MOOD = "mood"


        //QQ音乐播放特定年代歌单
        const val SKILL_ID_ACTION_MEDIA_YEARS = 0x52
        const val INPUT_ARG_ACTION_MEDIA_ERA = "era"


        //QQ音乐播放指定歌手的歌曲
        const val SKILL_ID_ACTION_MEDIA_PLAY_SINGER = 0x57
        const val INPUT_ARG_ACTION_MEDIA_SINGER = "singer"


        //QQ音乐播放指定歌曲
        const val SKILL_ID_ACTION_MEDIA_PLAY_SONG_APPOINT = 0x55
        const val INPUT_ARG_ACTION_MEDIA_NAME = "name"


        // 暂停/播放多媒体
        const val SKILL_ID_ACTION_MEDIA_CONTROL = 0x31

        //多媒体控制-/上一首/下一首
        const val SKILL_ID_ACTION_MEDIA_CONTROL2 = 0x7

        /**
         * 小尼回复
         */
        const val SKILL_ID_ACTION_APP_VPA_REPLY = 0x69

        /**
         * 天气预报
         */
        const val SKILL_ID_ACTION_APP_WEATHER_REPORT = 0x71

        /**
         * 行程播报
         */
        const val SKILL_ID_ACTION_APP_TRAVEL_REPORT = 0x75

        /**
         * 消息通知,string
         */
        const val SKILL_ID_ACTION_APP_NOTIFY = 0x74

        /**
         * 消息通知InputName
         */
        const val INPUT_ARG_ACTION_APP_NOTIFY = "message"

        /**
         * 播放整蛊音
         */
        const val SKILL_ID_ACTION_APP_PLAY_PRANK_MUSIC = 0x67

        /**
         *播放整蛊音,播放车外音 声音inputName,int,range[0,10]
         * 整蛊音:打嗝:1, ⼑⽚划玻璃:2,地板缝的蛐蛐叫:3,绵⻓的屁:4,⽪座椅的屁:5, 敲⻔:6
         */
        const val INPUT_ARG_ACTION_APP_SOUND = "sound"

        /**
         *播放整蛊音 位置inputName,int,range[0,10]
         * 主驾 : 1, 副驾 : 2, 左后 : 3, 右后:4
         */
        const val INPUT_ARG_ACTION_APP_POSITION = "position"

        /**
         * 沉浸音效
         */
        const val SKILL_ID_ACTION_APP_SOUND_EFFECT = 0x70a

        /**
         * 动感声浪开关 关闭0打开1
         */
        const val SKILL_ID_ACTION_APP_VOICE_SWITCH = 0x727

        /**
         * 声浪音量 低1中2高3
         */
        const val SKILL_ID_ACTION_APP_VOICE_VOLUME = 0x724

        /**
         * 使用指定声浪
         */
        const val SKILL_ID_ACTION_APP_USE_VOICE = 0x3f

        /**
         * 使用指定声浪
         */
        const val INPUT_ARG_ACTION_APP_USE_VOICE = "roar"

        /**大喇叭实时喊话 关闭 10～60s*/
        const val SKILL_ID_ACTION_APP_OUT_SPEAKER = 0x730


        /**
         * 在途预热开关,0关闭1打开
         */
        const val SKILL_ID_ACTION_APP_PREHEAT_SWITCH = 0x72

        /**
         * 对外放电开关,0关闭1打开
         */
        const val SKILL_ID_ACTION_APP_DISCHARGE_SWITCH = 0x73

        /**设置续航模式 CLTC：1，WLTP：2*/
        const val SKILL_ID_ACTION_APP_ENDURANCE_MODE = 0x734

        /**
         * vpa小尼回复内容
         */
        const val INPUT_ARG_ACTION_APP_VPA_REPLY = "content"

        /**
         * 行程播报内容
         */
        const val INPUT_ARG_ACTION_APP_TRAVEL_REPORT_JOURNEY_INFO = "journeyInfo"

        /**
         * 打开应用,0关闭 1打开
         * 车主指南:1 , 蓝牙电话:2 ,   天气:3,  消息中心:4, 能源中心:5,  小尼空间:6, 车辆控制:7 ,空调:8,  导航:9 ,
         * 在线音乐（QQ音乐）:10,   酷狗唱唱:11,  蓝牙音乐:12,  USB音视频:13,   系统设置:14, 声音空间:15,  应用中心:16,
         * 爱奇艺:17,  优酷:18,  哔哩哔哩:19,  云听:20,  喜马拉雅:21,  宝宝巴士:22,  口袋故事 :23 , 凤凰FM:24,   乐听头条:25
         */
        const val SKILL_ID_ACTION_APP_OPEN_APP = 0x76

        /**
         * 关闭应用
         */
        const val SKILL_ID_ACTION_APP_CLOSE_APP = 0x6a

        /**
         * VPA发音人
         */
        const val SKILL_ID_ACTION_APP_VPA_SPEAKER = 0x1d

        /** 输入播放歌曲 */
        const val INPUT_ARG_ACTION_APP_TYPE_SONG = 0

        /**
         * 输入歌手
         */
        const val INPUT_ARG_ACTION_APP_TYPE_SINGER = 1
        const val INPUT_ARG_ACTION_APP_OPEN_MEDIA = "app"


        //     3.6、系统设置

        /**
         * 蓝牙
         */
        const val SKILL_ID_ACTION_SYSTEM_BLUETOOTH_ID = 0x707

        /**
         * 热点
         */
        const val SKILL_ID_ACTION_SYSTEM_HOT_SPOT_ID = 0x708

        /**
         * WLAN
         */
        const val SKILL_ID_ACTION_SYSTEM_WLAN_ID = 0x709

        /**
         * 移动网络
         */
        const val SKILL_ID_ACTION_SYSTEM_MOBILE = 0x720

        /**
         * 音量-多媒体
         */
        const val SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_MEDIA = 0x702

        /**
         * 音量-语音
         */
        const val SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_ASSISTANT = 0x701

        /**
         * 音量-导航
         */
        const val SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_NAVI = 0x700

        /**
         * 音量-通话
         */
        const val SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_CALL = 0x703

        /**
         * 音量-蓝牙音乐
         */
        const val SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_BLUETOOTH = 0x71d

        /**
         * 音量-车外扬声器音量
         */
        const val SKILL_ID_ACTION_SYSTEM_CAT_OUT_VOLUME = 0x71e

        /**
         * 音量-铃声音量
         */
        const val SKILL_ID_ACTION_SYSTEM_RING_VOLUME = 0x71f

        /**
         * 静音
         */
        const val SKILL_ID_ACTION_SYSTEM_MUTE = 0x723

        /**
         * 中控屏幕亮度
         */
        const val SKILL_ID_ACTION_SYSTEM_CENTRAL_SCREEN_BRIGHTNESS = 0x704

        /**
         * 白天黑夜模式 白天 : 1,夜晚 : 2,自动:3
         */
        const val SKILL_ID_ACTION_SYSTEM_SCREEN_MODE = 0x705

        /**
         * 设置无线充电
         * 关闭:0 , 打开:1
         */
        const val SKILL_ID_ACTION_OTHER_WIRELESS_CHARGER = 0x728

        /**
         * 蓝牙,热点,wlan InputName
         */
        const val INPUT_ARG_ACTION_SYSTEM_SWITCH_STATUS = "switchStatus"

        /**
         * 音量设置大小,中控屏屏幕亮度InputName range[0,100]
         */
        const val INPUT_ARG_ACTION_SYSTEM_PER = "per"

        /**
         * 白天黑夜模式切换 InputName
         */
        const val INPUT_ARG_ACTION_SYSTEM_DAT_MODE = "dayMode"


        //     3.7、智能设备

        /**炫彩氛围灯开关*/
        const val SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_SWITCH = 0xb22

        /**炫彩氛围灯亮度*/
        const val SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_BRIGHTNESS = 0xb23

        /**炫彩氛围灯模式*/
        const val SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_MODE = 0xb24

        /**炫彩氛围灯颜色 主题*/
        const val SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_COLOR_THEME = 0xb25

        /**炫彩氛围灯颜色 自定义*/
        @Deprecated("之前说要加的，现在能力列表没有加，并且炫彩氛围灯和等离子香氛 没有上")
        const val SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_COLOR_CUSTOMIZE = 0xb26

        /**等离子香氛开关*/
        const val SKILL_ID_ACTION_SMART_PLASMA_FRAGRANCE_SWITCH = 0xb1e

        /**等离子香氛浓度*/
        const val SKILL_ID_ACTION_SMART_PLASMA_FRAGRANCE_CONCENTRATION = 0xb1f

        /**等离子香氛模式*/
        const val SKILL_ID_ACTION_SMART_PLASMA_FRAGRANCE_MODE = 0xb20

        /**等离子香氛香型*/
        const val SKILL_ID_ACTION_SMART_PLASMA_FRAGRANCE_FLAVOR = 0xb21

        /**
         * 香氛机开关
         */
        const val SKILL_ID_ACTION_SMART_AROMA_DIFFUSER_SWITCH_ID = 0xb01

        /**
         * 香氛模式
         */
        const val SKILL_ID_ACTION_SMART_FRAGRANCE_MODE_ID = 0xb1c

        /**
         * 香氛散香浓度
         */
        const val SKILL_ID_ACTION_SMART_FRAGRANCE_STALL_ID = 0xb02

        /**
         * 香氛散香时长
         */
        const val SKILL_ID_ACTION_SMART_FRAGRANCE_DURATION_ID = 0xb03

        /**
         * 空气净化器开关
         */
        const val SKILL_ID_ACTION_SMART_AIR_PURIFIER_ID = 0xb04

        /**
         * 空气质量同步
         */
        const val SKILL_ID_ACTION_SMART_AIR_QUALITY_SYNC_ID = 0xb05

        /**
         * 儿童座椅通风
         */
        const val SKILL_ID_ACTION_SMART_CHILD_SEAT_BLOW_ID = 0xb0d

        /**
         * 儿童座椅加热
         */
        const val SKILL_ID_ACTION_SMART_CHILD_SEAT_HEAT_ID = 0xb0e

        /**
         * 按摩颈枕开关
         */
        const val SKILL_ID_ACTION_SMART_MASSAGE_NECK_PILLOW_SWITCH_ID = 0xb0f

        /**
         * 按摩颈枕加热
         */
        const val SKILL_ID_ACTION_SMART_MASSAGE_NECK_PILLOW_HEAT_ID = 0xb19

        /**
         * 颈枕按摩力度
         */
        const val SKILL_ID_ACTION_SMART_NECK_PILLOW_MASSAGE_STRENGTH_ID = 0xb11

        /**
         * 颈枕按摩时间
         */
        const val SKILL_ID_ACTION_SMART_NECK_PILLOW_MASSAGE_TIME_ID = 0xb12

        /**
         * 颈枕按摩模式
         */
        const val SKILL_ID_ACTION_SMART_NECK_PILLOW_MASSAGE_MODE_ID = 0xb10

        /**
         * 按摩腰靠开关
         */
        const val SKILL_ID_ACTION_SMART_MASSAGE_CUSHION_SWITCH_ID = 0xb13

        /**
         * 按摩腰靠加热
         */
        const val SKILL_ID_ACTION_SMART_MASSAGE_CUSHION_HEAT_ID = 0xb14

        /**
         * 腰靠按摩模式
         */
        const val SKILL_ID_ACTION_SMART_MASSAGE_PART_ID = 0xb15

        /**
         * 腰靠按摩力度
         */
        const val SKILL_ID_ACTION_SMART_MASSAGE_CUSHION_STRENGTH_ID = 0xb1a

        /**
         * 腰靠按摩时间
         */
        const val SKILL_ID_ACTION_SMART_MASSAGE_CUSHION_TIME_ID = 0xb1b

        /**
         * 香氛模式
         */
        const val INPUT_ARG_ACTION_SMART_FRAGRANCE_MODE = "aromatherapyMode"

        /**
         * 香薰散香时长
         */
        const val INPUT_ARG_ACTION_SMART_FRAGRANCE_DURATION_NAME = "time"

        /**
         * 儿童座椅
         */
        const val INPUT_ARG_ACTION_SMART_CHILD_SEAT_DEVICE_NAME = "swStatus"

        /**
         * 颈枕按摩力度
         */
        const val INPUT_ARG_ACTION_SMART_NECK_PILLOW_MASSAGE_STRENGTH_NAME = "intensity"

        //     3.8、其他

        /**
         * 后视镜开闭
         */
        const val SKILL_ID_ACTION_OTHER_REARVIEW_MIRROR_SWITCH_ID = 0x500

        /**
         * 后视镜加热打开/关闭
         */
        const val SKILL_ID_ACTION_OTHER_REARVIEW_MIRROR_HEATING_OPEN_ID = 0x501

        /**
         * 屏幕保护 打开0关闭1
         */
        const val SKILL_ID_ACTION_OTHER_SCREEN_PROTECT = 0x721

        /**
         * 熄屏 打开0关闭1
         */
        const val SKILL_ID_ACTION_OTHER_SCREEN_OFF = 0x725

        /**
         * 后视镜开闭
         */
        const val INPUT_ARG_ACTION_OTHER_REARVIEW_MIRROR_SWITCH_NAME = "foldStatus"

        /**
         * 后视镜加热开闭
         */
        const val INPUT_ARG_ACTION_OTHER_REARVIEW_MIRROR_HEATING_NAME = "MirrorHeatSWstate"
    }
}