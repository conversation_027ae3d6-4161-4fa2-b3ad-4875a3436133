package com.dfl.android.common.base

import android.os.Bundle
import androidx.lifecycle.ViewModelProvider
import androidx.viewbinding.ViewBinding
import com.dfl.android.common.util.getVmClazz
import com.dfl.android.common.util.inflateBindingWithGeneric

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2022/07/18
 * desc: 基于viewBinding的基类Activity
 * version:1.1
 */
abstract class BaseVBActivity<VM : BaseViewModel, VB : ViewBinding> : BaseActivity() {
    protected lateinit var mViewModel: VM
    protected lateinit var mViewBind: VB


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mViewBind = inflateBindingWithGeneric(layoutInflater)
        val root = mViewBind.root
        setContentView(root)
        mViewModel = createViewModel()
        initView(savedInstanceState)
        createObserver()
    }

    abstract fun initView(savedInstanceState: Bundle?)
    override fun initView() {}
    override fun initData() {}

    /**
     * 创建viewModel
     */
    private fun createViewModel(): VM {
        return ViewModelProvider(this).get(getVmClazz(this))
    }

    /**
     * 创建LiveData数据观察者
     */
    abstract fun createObserver()

    /**
     * 获取当前类绑定的泛型ViewModel-clazz
     */
    //    @Suppress("UNCHECKED_CAST")
    //    fun <VM> getVmClazz(obj: Any): VM {
    //        return (obj.javaClass.genericSuperclass as ParameterizedType).actualTypeArguments[0] as VM
    //    }

}