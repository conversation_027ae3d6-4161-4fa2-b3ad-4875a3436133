package com.dfl.android.common.util

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/06/02
 * desc   :
 * version: 1.0
 */
object NumberToCNUtil {
    //num 表示数字对应的中文，lower表示小写，upper表示大写
    private val num_lower = arrayOf("零", "一", "二", "三", "四", "五", "六", "七", "八", "九")
    private val num_upper = arrayOf("零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖")

    //unit 表示单位对应的中文，lower表示小写，upper表示大写
    private val unit_lower = arrayOf("", "十", "百", "千")
    private val unit_upper = arrayOf("", "拾", "佰", "仟")
    private val unit_common = arrayOf("", "万", "亿", "兆", "京", "垓", "秭", "穰", "沟", "涧", "正", "载")

    //允许的数据类型格式
    private val promissTypes: List<String> = listOf(
        "INTEGER",
        "INT",
        "LONG",
        "DECIMAL",
        "FLOAT",
        "DOUBLE",
        "STRING",
        "BYTE",
        "TYPE",
        "SHORT"
    )

    /**
     * 数字转化为小写的汉字
     *
     * @param num 将要转化的数字
     * @return
     */
    fun toChineseLower(num: Any): String {
        return format(num, num_lower, unit_lower)
    }

    /**
     * 数字转化为大写的汉字
     *
     * @param num 将要转化的数字
     * @return
     */
    fun toChineseUpper(num: Any): String {
        return format(num, num_upper, unit_upper)
    }

    /**
     * 格式化数字
     *
     * @param num      原数字
     * @param numArray 数字大小写数组
     * @param unit     单位权值
     * @return
     */
    private fun format(num: Any, numArray: Array<String>, unit: Array<String>): String {
        if (!promissTypes.contains(num.javaClass.simpleName.toUpperCase())) {
            throw RuntimeException("不支持的格式类型")
        }
        //获取整数部分
        val intnum = getInt(num.toString())
        //获取小数部分
        val decimal = getFraction(num.toString())
        //格式化整数部分
        var result = formatIntPart(intnum, numArray, unit)
        if ("" != decimal) { //小数部分不为空
            //格式化小数
            result += "点" + formatFractionalPart(decimal, numArray)
        }
        return result
    }

    /**
     * 格式化整数部分
     *
     * @param num      整数部分
     * @param numArray 数字大小写数组
     * @return
     */
    private fun formatIntPart(num: String, numArray: Array<String>, unit: Array<String>): String {

        //按4位分割成不同的组（不足四位的前面补0）
        val intnums = split2IntArray(num)
        var zero = false
        val sb = StringBuilder()
        for (i in intnums.indices) {
            //格式化当前4位
            val r = formatInt(intnums[i]!!, numArray, unit)
            if ("" == r) { //
                if (i + 1 == intnums.size) {
                    sb.append(numArray[0]) //结果中追加“零”
                } else {
                    zero = true
                }
            } else { //当前4位格式化结果不为空（即不为0）
                if (zero || i > 0 && intnums[i]!! < 1000) { //如果前4位为0，当前4位不为0
                    sb.append(numArray[0]) //结果中追加“零”
                }
                sb.append(r)
                sb.append(unit_common[intnums.size - 1 - i]) //在结果中添加权值
                zero = false
            }
        }
        var numberString = sb.toString()
        if (numberString.startsWith("一十")) {
            numberString = numberString.substring(1)
        }
        return numberString
    }

    /**
     * 格式化小数部分
     *
     * @param decimal  小数部分
     * @param numArray 数字大小写数组
     * @return
     */
    private fun formatFractionalPart(decimal: String, numArray: Array<String>): String {
        val `val` = decimal.toCharArray()
        val sb = StringBuilder()
        for (c in `val`) {
            val n = (c.toString() + "").toInt()
            sb.append(numArray[n])
        }
        return sb.toString()
    }
    //拆分整数和小数的方法在下面-----------------
    /**
     * 获取整数部分
     *
     * @param num
     * @return
     */
    private fun getInt(num: String): String {
        //检查格式
        checkNum(num)
        val `val` = num.toCharArray()
        val sb = StringBuilder()
        var t: Int
        var s = 0
        for (c in `val`) {
            if (c == '.') {
                break
            }
            t = (c.toString() + "").toInt(16)
            if (s + t == 0) {
                continue
            }
            sb.append(t)
            s += t
        }
        return if (sb.length == 0) "0" else sb.toString()
    }

    /**
     * 获取小数部分
     *
     * @param num
     * @return
     */
    private fun getFraction(num: String): String {
        val i = num.lastIndexOf(".")
        if (num.indexOf(".") != i) {
            throw RuntimeException("数字格式不正确，最多只能有一位小数点！")
        }
        var fraction = ""
        if (i >= 0) {
            fraction = getInt(StringBuffer(num).reverse().toString())
            if ("0" == fraction) {
                return ""
            }
        }
        return StringBuffer(fraction).reverse().toString()
    }

    /**
     * 检查数字格式
     *
     * @param num
     */
    private fun checkNum(num: String) {
        if (num.indexOf(".") != num.lastIndexOf(".")) {
            throw RuntimeException("数字[$num]格式不正确!")
        }
        if (num.indexOf("-") != num.lastIndexOf("-") || num.lastIndexOf("-") > 0) {
            throw RuntimeException("数字[$num]格式不正确！")
        }
        if (num.indexOf("+") != num.lastIndexOf("+")) {
            throw RuntimeException("数字[$num]格式不正确！")
        }
        if (num.indexOf("+") != num.lastIndexOf("+")) {
            throw RuntimeException("数字[$num]格式不正确！")
        }
        if (num.replace("[\\d|\\.|\\-|\\+]".toRegex(), "").length > 0) {
            throw RuntimeException("数字[$num]格式不正确！")
        }
    }

    /**
     * 分割数字，每4位一组
     *
     * @param num
     * @return
     */
    private fun split2IntArray(num: String): Array<Int?> {
        var num = num
        val prev = num.substring(0, num.length % 4)
        val stuff = num.substring(num.length % 4)
        if ("" != prev) {
            num = String.format("%04d", Integer.valueOf(prev)) + stuff
        }
        val ints = arrayOfNulls<Int>(num.length / 4)
        var idx = 0
        var i = 0
        while (i < num.length) {
            val n = num.substring(i, i + 4)
            ints[idx++] = Integer.valueOf(n)
            i += 4
        }
        return ints
    }

    /**
     * 格式化4位整数
     *
     * @param num
     * @param numArray
     * @return
     */
    private fun formatInt(num: Int, numArray: Array<String>, unit: Array<String>): String {
        val `val` = num.toString().toCharArray()
        val len = `val`.size
        val sb = StringBuilder()
        var isZero = false
        for (i in 0 until len) {
            val n = (`val`[i].toString() + "").toInt() //获取当前位的数值
            isZero = if (n == 0) {
                true
            } else {
                if (isZero) {
                    sb.append(numArray[(`val`[i - 1].toString() + "").toInt()])
                }
                sb.append(numArray[n])
                sb.append(unit[len - 1 - i])
                false
            }
        }
        return sb.toString()
    }

    @JvmStatic
    fun main(args: Array<String>) {
        val s: Short = 10
        val b: Byte = 10
        val c = 'A'
        val nums = arrayOf<Any>(
            s,
            b,
            c,
            0,
            1001,
            100100001L,
            21.0,
            205.23f,
            205.23,
            "01000010",
            "1000000100105.0123",
            ".142",
            "20.00",
            "1..2",
            true
        )
        println("将任意数字转化为汉字(包括整数、小数以及各种类型的数字)")
        println("--------------------------------------------")
        for (num in nums) {
            try {
                print("[" + num.javaClass.simpleName + "]" + num)
                var i = 0
                while (i < 25 - (num.toString() + num.javaClass.simpleName).length) {
                    print("\t")
                    i += 4
                }
                //调用转化为小写和大写
                print(" format:" + toChineseLower(num))
                println("【" + toChineseUpper(num) + "】")
            } catch (e: Exception) {
                println(" 错误信息：" + e.message)
            }
        }
    }

    /***
     * 是否能string转成long
     * return : true: 返回本身，false：返回 null
     */
    @JvmStatic
    fun isToLong(longNum: String?): String? {
        longNum?.let {
            try {
                it?.toLong()
                return it
            } catch (ex: Exception) {
                return null
            }
        }
        return null
    }

    /**
     * 把超过千,万数字四舍五入转换为1千 1.1千格式
     */
    @JvmStatic
    fun formatOverString(num: Int?): String {
        return if (num == null) return ""
        else if (num >= 10000) {
            String.format("%.1f", num.toDouble() / 10000.0)
                .dropLastWhile { it == '0' }
                .dropLastWhile { it == '.' }
                .plus("万")
        } else if (num >= 1000) {
            String.format("%.1f", num.toDouble() / 1000.0)
                .dropLastWhile { it == '0' }
                .dropLastWhile { it == '.' }
                .plus("千")
        } else {
            num.toString()
        }
    }
}