package com.dfl.android.common.util


/**
 * author:z<PERSON><PERSON><PERSON>
 * e-mail:z<PERSON><PERSON><PERSON>@dfl.com.cn
 * time: 2023/12/11
 * desc: 通信获取常用信息的工具
 * version:1.0
 */
class GeneralInfoUtils private constructor() {
    companion object {

        /**
         * 获取请求码，
         * 包名+时间戳
         */
        fun getRequestCode(): String {
            return getRequestAuthor().plus(System.currentTimeMillis())
        }

        /**
         * 获取版本名称
         */
        fun getVersionName(): String {
            return AppUtils.getAppVersionName()
        }

        /**
         * 获取当前进程名
         */
        fun getRequestAuthor(): String {
            return ProcessUtils.getCurrentProcessName()
        }

        /**获取应用包名，用于消息中心*/
        fun getPackageName(): String {
            return AppUtils.getAppPackageName()
        }
    }
}