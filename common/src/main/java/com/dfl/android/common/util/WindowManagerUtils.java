/*
 * Copyright (c) 2021.
 * All Rights Reserved by Software.
 * -
 * -
 *
 * You may not use, copy, distribute, modify, transmit in any form this file.
 * except in compliance with ThunderSoft in writing by applicable law.
 *
 * Date last modified date: 2021/11/15.
 * NAME.
 * VOYAH
 */

package com.dfl.android.common.util;

import android.app.Activity;
import android.graphics.Color;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.appcompat.app.AppCompatActivity;


/**
 * Created with Android Studio.
 *
 * <AUTHOR>
 * @data 2021/8/12 12:50
 */
public class WindowManagerUtils {
    public static void hideStatusBar(AppCompatActivity activity) {
        activity.getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);
    }

    /**
     * hideNavigationBar.
     *
     * @param activity Activity
     */
    public static void hideNavigationBar(Activity activity) {
        View decorView = activity.getWindow().getDecorView();
        int option = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION | View.SYSTEM_UI_FLAG_IMMERSIVE;
        decorView.setSystemUiVisibility(option);
    }

    public static void showStatusBar(AppCompatActivity activity) {
        activity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
    }

    /**
     * 设置透明状态栏.
     *
     * @param activity Activity
     */
    public static void showTransparentStatusBar(Activity activity) {
        Window window = activity.getWindow();
        window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
        window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        window.setStatusBarColor(Color.TRANSPARENT);
    }

    public static void showNavigationBar(Activity activity) {
        activity.getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_VISIBLE);
    }

}
