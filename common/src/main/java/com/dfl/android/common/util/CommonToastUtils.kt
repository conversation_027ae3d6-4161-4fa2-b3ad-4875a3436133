package com.dfl.android.common.util

import android.widget.Toast
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.dfl.android.commonlib.CommonUtils
import com.google.gson.Gson
import com.dfl.android.commonlib.toast.CommonToastUtils as CommonLibToastUtils

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/11/21
 * desc : toast显示,对commonlib.toast.CommonToastUtils再一次分装，方便之后替换
 * version: 1.0
 */

class ToastBean(var type: Int, var text: String, var icon: Int) {
    override fun toString(): String {
        return "ToastBean{type=[$type', text='$text', icon='$icon'}"
    }
}

object CommonToastUtils {
    private const val DEBOUNCING_DEFAULT_VALUE: Long = 2000

    /**
     * 短时间显示toast
     */
    @JvmStatic
    fun show(@StringRes resId: Int) {
        val string = StringUtils.getString(resId)
        if (!DebouncingUtils.isValid(string, DEBOUNCING_DEFAULT_VALUE)) { //防抖动
            return
        }
        CommonUtils.runOnUiThread {
            Toast.makeText(CommonUtils.getApp(), string, Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 短时间显示toast
     */
    @JvmStatic
    fun show(string: String) {
        if (!DebouncingUtils.isValid(string, DEBOUNCING_DEFAULT_VALUE)) { //防抖动
            return
        }
        CommonUtils.runOnUiThread {
            Toast.makeText(CommonUtils.getApp(), string, Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 打开toast动效动画
     * commonLib v1.3之后使用动画通过windowManager实现
     */
    fun openAnimate(open: Boolean) {
        CommonLibToastUtils.openAnimate(true)
    }

    /**
     * 长时间显示toast
     */
    fun showLong(string: String) {
        if (!DebouncingUtils.isValid(string, DEBOUNCING_DEFAULT_VALUE)) { //防抖动
            return
        }
        CommonUtils.runOnUiThread {
            Toast.makeText(CommonUtils.getApp(), string, Toast.LENGTH_LONG).show()
        }
    }

    /**
     * 长时间显示toast
     */
    fun showLong(@StringRes resId: Int) {
        CommonUtils.runOnUiThread {
            Toast.makeText(CommonUtils.getApp(), resId, Toast.LENGTH_LONG).show()
        }
    }

    /**
     * 显示加载状态toast，不带关闭按钮
     */
    fun showLoadingToast(@StringRes resId: Int) {
        CommonLibToastUtils.showLoadingToast(resId)
    }

    /**
     * 取消加载状态toast
     */
    fun cancelLoadingToast() {
        CommonLibToastUtils.cancel()
    }


    fun showWithIcon(@DrawableRes iconResId: Int, msg: String) {
        CommonUtils.runOnUiThread {
            Toast.makeText(
                CommonUtils.getApp(), Gson().toJson(ToastBean(1, msg, iconResId)), Toast.LENGTH_SHORT
            ).show()
        }
    }

    fun showLongWithIcon(@DrawableRes iconResId: Int, msg: String) {
        CommonUtils.runOnUiThread {
            Toast.makeText(
                CommonUtils.getApp(), Gson().toJson(ToastBean(1, msg, iconResId)), Toast.LENGTH_LONG
            ).show()
        }
    }
}