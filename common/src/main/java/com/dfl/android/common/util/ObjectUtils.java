package com.dfl.android.common.util;

import android.os.Build;
import android.util.SparseArray;
import android.util.SparseBooleanArray;
import android.util.SparseIntArray;
import android.util.SparseLongArray;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.collection.LongSparseArray;
import androidx.collection.SimpleArrayMap;

import com.dfl.android.common.util.constant.NoInputArgSkillIdConstant;
import com.iauto.scenarioadapter.ArgType;
import com.iauto.scenarioadapter.InputArgInfo;
import com.iauto.scenarioadapter.ScenarioInfo;

import java.lang.reflect.Array;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 *     author: Blankj
 *     blog  : http://blankj.com
 *     time  : 2017/12/24
 *     desc  : utils about object
 * </pre>
 */
public final class ObjectUtils {

    private ObjectUtils() {
        throw new UnsupportedOperationException("u can't instantiate me...");
    }

    /**
     * Return whether object is empty.
     *
     * @param obj The object.
     * @return {@code true}: yes<br>{@code false}: no
     */
    public static boolean isEmpty(final Object obj) {
        if (obj == null) {
            return true;
        }
        if (obj.getClass().isArray() && Array.getLength(obj) == 0) {
            return true;
        }
        if (obj instanceof CharSequence && obj.toString().length() == 0) {
            return true;
        }
        if (obj instanceof Collection && ((Collection) obj).isEmpty()) {
            return true;
        }
        if (obj instanceof Map && ((Map) obj).isEmpty()) {
            return true;
        }
        if (obj instanceof SimpleArrayMap && ((SimpleArrayMap) obj).isEmpty()) {
            return true;
        }
        if (obj instanceof SparseArray && ((SparseArray) obj).size() == 0) {
            return true;
        }
        if (obj instanceof SparseBooleanArray && ((SparseBooleanArray) obj).size() == 0) {
            return true;
        }
        if (obj instanceof SparseIntArray && ((SparseIntArray) obj).size() == 0) {
            return true;
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
            if (obj instanceof SparseLongArray && ((SparseLongArray) obj).size() == 0) {
                return true;
            }
        }
        if (obj instanceof LongSparseArray && ((LongSparseArray) obj).size() == 0) {
            return true;
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            if (obj instanceof android.util.LongSparseArray
                    && ((android.util.LongSparseArray) obj).size() == 0) {
                return true;
            }
        }
        return false;
    }

    public static boolean isEmpty(final CharSequence obj) {
        return obj == null || obj.toString().length() == 0;
    }

    public static boolean isEmpty(final Collection obj) {
        return obj == null || obj.isEmpty();
    }

    public static boolean isEmpty(final Map obj) {
        return obj == null || obj.isEmpty();
    }

    public static boolean isEmpty(final SimpleArrayMap obj) {
        return obj == null || obj.isEmpty();
    }

    public static boolean isEmpty(final SparseArray obj) {
        return obj == null || obj.size() == 0;
    }

    public static boolean isEmpty(final SparseBooleanArray obj) {
        return obj == null || obj.size() == 0;
    }

    public static boolean isEmpty(final SparseIntArray obj) {
        return obj == null || obj.size() == 0;
    }

    public static boolean isEmpty(final LongSparseArray obj) {
        return obj == null || obj.size() == 0;
    }

    @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR2)
    public static boolean isEmpty(final SparseLongArray obj) {
        return obj == null || obj.size() == 0;
    }

    @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN)
    public static boolean isEmpty(final android.util.LongSparseArray obj) {
        return obj == null || obj.size() == 0;
    }

    /**
     * Return whether object is not empty.
     *
     * @param obj The object.
     * @return {@code true}: yes<br>{@code false}: no
     */
    public static boolean isNotEmpty(final Object obj) {
        return !isEmpty(obj);
    }


    public static boolean isNotEmpty(final CharSequence obj) {
        return !isEmpty(obj);
    }

    public static boolean isNotEmpty(final Collection obj) {
        return !isEmpty(obj);
    }

    public static boolean isNotEmpty(final Map obj) {
        return !isEmpty(obj);
    }

    public static boolean isNotEmpty(final SimpleArrayMap obj) {
        return !isEmpty(obj);
    }

    public static boolean isNotEmpty(final SparseArray obj) {
        return !isEmpty(obj);
    }

    public static boolean isNotEmpty(final SparseBooleanArray obj) {
        return !isEmpty(obj);
    }

    public static boolean isNotEmpty(final SparseIntArray obj) {
        return !isEmpty(obj);
    }

    public static boolean isNotEmpty(final LongSparseArray obj) {
        return !isEmpty(obj);
    }

    @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR2)
    public static boolean isNotEmpty(final SparseLongArray obj) {
        return !isEmpty(obj);
    }

    @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN)
    public static boolean isNotEmpty(final android.util.LongSparseArray obj) {
        return !isEmpty(obj);
    }

    /**
     * Return whether object1 is equals to object2.
     *
     * @param o1 The first object.
     * @param o2 The second object.
     * @return {@code true}: yes<br>{@code false}: no
     */
    public static boolean equals(final Object o1, final Object o2) {
        return o1 == o2 || (o1 != null && o1.equals(o2));
    }

    /**
     * Returns 0 if the arguments are identical and {@code
     * c.compare(a, b)} otherwise.
     * Consequently, if both arguments are {@code null} 0
     * is returned.
     */
    public static <T> int compare(T a, T b, @NonNull Comparator<? super T> c) {
        return (a == b) ? 0 : c.compare(a, b);
    }

    /**
     * Checks that the specified object reference is not {@code null}.
     */
    public static <T> T requireNonNull(T obj) {
        if (obj == null) throw new NullPointerException();
        return obj;
    }

    /**
     * Checks that the specified object reference is not {@code null} and
     * throws a customized {@link NullPointerException} if it is.
     */
    public static <T> T requireNonNull(T obj, String ifNullTip) {
        if (obj == null) throw new NullPointerException(ifNullTip);
        return obj;
    }

    /**
     * Require the objects are not null.
     *
     * @param objects The object.
     * @throws NullPointerException if any object is null in objects
     */
    public static void requireNonNulls(final Object... objects) {
        if (objects == null) throw new NullPointerException();
        for (Object object : objects) {
            if (object == null) throw new NullPointerException();
        }
    }

    /**
     * Return the nonnull object or default object.
     *
     * @param object The object.
     * @param defaultObject The default object to use with the object is null.
     * @param <T> The value type.
     * @return the nonnull object or default object
     */
    public static <T> T getOrDefault(final T object, final T defaultObject) {
        if (object == null) {
            return defaultObject;
        }
        return object;
    }

    /**
     * Returns the result of calling {@code toString} for a non-{@code
     * null} argument and {@code "null"} for a {@code null} argument.
     */
    public static String toString(Object obj) {
        return String.valueOf(obj);
    }

    /**
     * Returns the result of calling {@code toString} on the first
     * argument if the first argument is not {@code null} and returns
     * the second argument otherwise.
     */
    public static String toString(Object o, String nullDefault) {
        return (o != null) ? o.toString() : nullDefault;
    }

    /**
     * Return the hash code of object.
     *
     * @param o The object.
     * @return the hash code of object
     */
    public static int hashCode(final Object o) {
        return o != null ? o.hashCode() : 0;
    }

    /**
     * Return the hash code of objects.
     */
    public static int hashCodes(Object... values) {
        return Arrays.hashCode(values);
    }

    public static boolean isInputArgValueError(String s, ArgType type) {
        if (type != ArgType.STRING) {
            //数字类型字符串，带正负号，带小数点
            return !StringUtils.isNumeric(s, true, true);
        }
        //字符串类型不判断空字符串情况,623指定歌手的歌曲有一个inputArg为空字符串
        return s == null;
    }

    /**
     * 拓展isEmpty()用来判断ccm数据及其属性不为空,不需要检验数据是否有效
     * 防止发生空指针,数组越界,空字符串类型转化异常
     * 非空判断自身;数组及其成员不为空;
     * 非空判断InputArgInfo type和value不为空,如果是InputArgInfo为空的能力,不进行判断
     * 非空判断Sequence和Condition InputArgInfo列表
     *
     * @param obj Condition,Sequence,ScenarioInfo,Action及其Array和Collection集合
     * @return true数据为空或者列表为空表
     */
    public static boolean checkScenarioDataIsEmpty(Object obj) {
        //自身是否为空
        if (isEmpty(obj)) return true;
        //数组判断其中元素是否为空
        if (obj.getClass().isArray()) {
            for (int i = 0; i < Array.getLength(obj); i++) {
                if (checkScenarioDataIsEmpty(Array.get(obj, i))) {
                    return true;
                }
            }
        }
        //列表判断其中元素是否为空
        if (obj instanceof Collection) {
            for (Object o : (Collection<?>) obj) {
                if (checkScenarioDataIsEmpty(o)) {
                    return true;
                }
            }
        }
        //InputArgInfo type和value不为空
        if (obj instanceof InputArgInfo) {
            InputArgInfo arg = (InputArgInfo) obj;
            return isEmpty(arg.getType()) || isInputArgValueError(arg.getValue(), arg.getType());
        }
        //Action判断List<InputArgInfo>
        if (obj instanceof ScenarioInfo.Action) {
            ScenarioInfo.Action action = (ScenarioInfo.Action) obj;
            //如果是InputArgInfo为空的能力,不进行判断
            if (NoInputArgSkillIdConstant.getSpecialSkillList().contains(action.getSkillId())) {
                return false;
            }
            return checkScenarioDataIsEmpty(action.getInput());
        }
        //Sequence判断Action
        if (obj instanceof ScenarioInfo.Sequence) {
            return checkScenarioDataIsEmpty(((ScenarioInfo.Sequence) obj).getAction());
        }
        //Condition判断List<InputArgInfo>
        if (obj instanceof ScenarioInfo.Condition) {
            ScenarioInfo.Condition condition = (ScenarioInfo.Condition) obj;
            //如果是InputArgInfo为空的能力,不进行判断
            if (NoInputArgSkillIdConstant.getSpecialSkillList().contains(condition.getSkillId())) {
                return false;
            }
            return checkScenarioDataIsEmpty(condition.getInput());
        }
        //ScenarioInfo判断condition,List<Condition> ,List<Sequence>
        if (obj instanceof ScenarioInfo) {
            ScenarioInfo scenario = (ScenarioInfo) obj;
            //有状态条件若触发或状态无效则无效数据
            return checkScenarioDataIsEmpty(scenario.getConditions())
                    || checkScenarioDataIsEmpty(scenario.getEdgeCondition())
                    || checkScenarioDataIsEmpty(scenario.getSequence());
        }
        return false;
    }

    /**
     * 拓展isEmpty()用来判断ccm数据及其属性不为空,且检验数据是否有效
     * 防止发生空指针,数组越界,空字符串类型转化异常
     * 非空判断自身;数组及其成员不为空;
     * InputArgInfo type和value不为空,如果是InputArgInfo为空的能力,不判断InputArgInfo
     * Action字段都不为空
     * Sequence判断Action不为空
     * Condition除了id所有字段不为空
     * ScenarioInfo除了scenarioDesc,version,imgPath不为空
     *
     * @param obj Condition,Sequence,ScenarioInfo,InputArgInfo,Action及其Array和Collection集合
     * @return true数据为空 或列表为空表 或无效
     */
    public static boolean checkScenarioDataIsInvalid(Object obj) {
        //自身是否为空
        if (isEmpty(obj)) return true;
        //数组判断其中元素是否为空
        if (obj.getClass().isArray()) {
            for (int i = 0; i < Array.getLength(obj); i++) {
                if (checkScenarioDataIsInvalid(Array.get(obj, i))) {
                    return true;
                }
            }
        }
        //列表判断其中元素是否为空
        if (obj instanceof Collection) {
            for (Object o : (Collection<?>) obj) {
                if (checkScenarioDataIsInvalid(o)) {
                    return true;
                }
            }
        }
        //InputArgInfo type和value不为空
        if (obj instanceof InputArgInfo) {
            InputArgInfo arg = (InputArgInfo) obj;
            return isEmpty(arg.getType()) || isInputArgValueError(arg.getValue(), arg.getType());
        }
        //Action字段都不为空
        if (obj instanceof ScenarioInfo.Action) {
            ScenarioInfo.Action action = (ScenarioInfo.Action) obj;
            if (isEmpty(action.getSkillId()) || isEmpty(action.getDesc()) || isEmpty(action.getCategory())) {
                return true;
            }
            //如果是InputArgInfo为空的能力,只判断InputArgInfo是否为空
            if (NoInputArgSkillIdConstant.getSpecialSkillList().contains(action.getSkillId())) {
                return action.getInput() == null;
            }
            return checkScenarioDataIsInvalid(action.getInput());
        }
        //Sequence判断Action不为空
        if (obj instanceof ScenarioInfo.Sequence) {
            return checkScenarioDataIsInvalid(((ScenarioInfo.Sequence) obj).getAction());
        }
        //Condition除了id所有字段不为空
        if (obj instanceof ScenarioInfo.Condition) {
            ScenarioInfo.Condition condition = (ScenarioInfo.Condition) obj;
            if (isEmpty(condition.getSkillId()) || isEmpty(condition.getCategory()) || isEmpty(condition.getDesc())) {
                return true;
            }
            //如果是InputArgInfo为空的能力,不判断InputArgInfo
            if (NoInputArgSkillIdConstant.getSpecialSkillList().contains(condition.getSkillId())) {
                return condition.getInput() == null;
            }
            return checkScenarioDataIsInvalid(condition.getInput());
        }
        //ScenarioInfo除了scenarioDesc,version,imgPath不为空
        if (obj instanceof ScenarioInfo) {
            ScenarioInfo scenario = (ScenarioInfo) obj;
            //动作一定不为空
            if (isEmpty(scenario.getScenarioId()) || isEmpty(scenario.getScenarioName())
                    || isEmpty(scenario.getSecondAskeFlag()) || isEmpty(scenario.getAutoExeFlag())
                    || isEmpty(scenario.getScenarioTimeStamp()) || checkScenarioDataIsInvalid(scenario.getSequence())) {
                return true;
            }
            boolean triggerIsEmpty = checkScenarioDataIsInvalid(scenario.getEdgeCondition());
            boolean conditionIsEmpty = checkScenarioDataIsInvalid(scenario.getConditions());
            //有触发条件且触发条件无效判断为无效数据
            if (isNotEmpty(scenario.getEdgeCondition()) && triggerIsEmpty) {
                return true;
            }
            //有状态条件若触发或状态无效则无效数据
            return isNotEmpty(scenario.getConditions()) && (triggerIsEmpty || conditionIsEmpty);
        }
        return false;
    }

    /**
     * 判断preData在dataList中存在后,查找preData.getInput().get(0)对应valueList的位置
     * 非空判断preData和dataList和valueList
     * 如果是InputArgInfo为空的能力不进行判断,返回-1
     *
     * @param preData 之前选中的值,Condition或者Sequence
     * @param dataList 对话框条件列表
     * @param valueList 对话框选项值
     * @return -1没有找到
     */
    public static <T, K> int findPreIndexWithInput(T preData, List<T> dataList, List<K> valueList) {
        return findPreIndexWithMultiInput(preData, dataList, valueList, 0);
    }

    /**
     * 判断preData在dataList中存在后,查找preData.getInput().get(compareIndex)在valueList位置
     * 非空判断preData和dataList和valueList
     * 如果是InputArgInfo为空的能力不进行判断,返回-1
     *
     * @param preData 之前选中的值,Condition或者Sequence
     * @param dataList 对话框条件列表
     * @param valueList 对话框选项值
     * @param compareIndex preData中InputArgList多个值,指定要比较的值
     * @return -1没有找到
     */
    public static <T, K> int findPreIndexWithMultiInput(T preData, List<T> dataList, List<K> valueList,
                                                        int compareIndex) {
        int index = -1;
        String value;
        //非空判断,如果是InputArgInfo为空的能力,不进行判断
        if (ObjectUtils.checkScenarioDataIsEmpty(preData)
                || ObjectUtils.checkScenarioDataIsEmpty(dataList)
                || ObjectUtils.checkScenarioDataIsEmpty(valueList)) {
            return -1;
        }
        for (int i = 0; i < dataList.size(); i++) {
            int preSkillId;
            int skillId;
            if (preData instanceof ScenarioInfo.Condition) {
                ScenarioInfo.Condition condition = (ScenarioInfo.Condition) preData;
                preSkillId = condition.getSkillId();
                skillId = ((ScenarioInfo.Condition) dataList.get(i)).getSkillId();
            } else if (preData instanceof ScenarioInfo.Sequence) {
                ScenarioInfo.Sequence sequence = (ScenarioInfo.Sequence) preData;
                preSkillId = sequence.getAction().getSkillId();
                skillId = ((ScenarioInfo.Sequence) dataList.get(i)).getAction().getSkillId();
            } else {
                //错误类型,不进行比较
                break;
            }
            //skillId相同,且不是InputArg值为空的能力
            if (skillId != -1 && preSkillId == skillId && !NoInputArgSkillIdConstant.getSpecialSkillList().contains(skillId)) {
                //再比较能力id对应的InputArg,找到index
                if (preData instanceof ScenarioInfo.Condition) {
                    ScenarioInfo.Condition condition = (ScenarioInfo.Condition) preData;
                    //索引越界结束循环返回-1
                    if (condition.getInput().size() <= compareIndex) {
                        break;
                    }
                    value = condition.getInput().get(compareIndex).getValue();
                } else {
                    ScenarioInfo.Sequence sequence = (ScenarioInfo.Sequence) preData;
                    //索引越界结束循环返回-1
                    if (sequence.getAction().getInput().size() <= compareIndex) {
                        break;
                    }
                    value = sequence.getAction().getInput().get(compareIndex).getValue();
                }
                for (int j = 0; j < valueList.size(); j++) {
                    if (value.equals(valueList.get(j).toString())) {
                        index = j;
                        break;
                    }
                }
            }
        }
        return index;
    }

    /**
     * 从dataList找到和preData.skillId相同且InputArg相同,未找到会返回最后一次skillId相同的索引
     * 非空判断ccm数据preData和dataList,
     * 如果是InputArgInfo为空的能力,不判断InputArg相同
     *
     * @param preData 之前选中的值,Condition或者Sequence
     * @param dataList 对话框动作或者条件列表
     * @return -1没有找到
     */
    public static <T> int findPreIndex(T preData, List<T> dataList) {
        int index = -1;
        //非空判断
        if (checkScenarioDataIsEmpty(preData)
                || checkScenarioDataIsEmpty(dataList)) {
            return -1;
        }
        for (int i = 0; i < dataList.size(); i++) {
            if (preData instanceof ScenarioInfo.Condition) {
                ScenarioInfo.Condition c = ((ScenarioInfo.Condition) dataList.get(i));
                if (((ScenarioInfo.Condition) preData).getSkillId() == c.getSkillId()) {
                    index = i;
                    if (isNotEmpty(((ScenarioInfo.Condition) preData).getInput())
                            && !((ScenarioInfo.Condition) preData).getInput().get(0).getValue().equals(c.getInput().get(0).getValue())) {
                        //如果InputArgList不为空,返回判断InputArg的值相等的索引,场景:同一个skillId,不同value值对应不同选项
                        continue;
                    }
                    break;
                }
            } else if (preData instanceof ScenarioInfo.Sequence) {
                ScenarioInfo.Sequence s = ((ScenarioInfo.Sequence) dataList.get(i));
                if (((ScenarioInfo.Sequence) preData).getAction().getSkillId() == s.getAction().getSkillId()) {
                    index = i;
                    if (isNotEmpty(((ScenarioInfo.Sequence) preData).getAction().getInput())
                            && !((ScenarioInfo.Sequence) preData).getAction().getInput().get(0).getValue().equals(s.getAction().getInput().get(0).getValue())) {
                        //如果InputArgList不为空,返回判断InputArg的值相等的索引,场景:同一个skillId,不同value值对应不同选项
                        continue;
                    }
                    break;
                }
            }
        }

        return index;
    }
}
