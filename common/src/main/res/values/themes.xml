<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!--    dialog 的 style-->
    <style name="Theme.Dialog.TimePickerDialogTheme" parent="Theme.AppCompat.DayNight.Dialog">
        <item name="android:windowIsTranslucent">true</item>
        <!--必须设置透明,使用组件库背景-->
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <!--是否模糊-->
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:fitsSystemWindows">false</item>
    </style>
    <!--    dialogFragment 的 style-->
    <style name="Theme.AppCompat.Light.Dialog.dialogTheme" parent="Theme.AppCompat.DayNight.Dialog">
        <item name="android:windowIsTranslucent">true</item>
        <!--必须设置透明,使用组件库背景-->
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <!--是否模糊-->
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:fitsSystemWindows">false</item>
    </style>
</resources>