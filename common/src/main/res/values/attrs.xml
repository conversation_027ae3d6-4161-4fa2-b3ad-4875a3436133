<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 正则表达式编辑框 -->
    <declare-styleable name="RegexEditText">
        <!-- 正则输入限制 -->
        <attr name="inputRegex" format="string" />
        <!-- 常用正则类型 -->
        <attr name="regexType" >
            <!-- 手机号（只能以 1 开头）-->
            <enum name="mobile" value="0x01" />
            <!-- 中文（普通的中文字符）-->
            <enum name="chinese" value="0x02" />
            <!-- 英文（大写和小写的英文）-->
            <enum name="english" value="0x03" />
            <!-- 数字（只允许输入纯数字）-->
            <enum name="number" value="0x04" />
            <!-- 整数（非 0 开头的数字）-->
            <enum name="count" value="0x05" />
            <!-- 用户名（中文、英文、数字）-->
            <enum name="name" value="0x06" />
            <!-- 非空格字符 -->
            <enum name="nonnull" value="0x07" />
        </attr>
    </declare-styleable>

</resources>