// Top-level build file where you can add configuration options common to all sub-projects/modules.
apply from: "version.gradle"
buildscript {
    ext.room_version = '2.5.1'
    ext.kotlin_version = '1.8.0'
    repositories {
        maven {
            allowInsecureProtocol = true
            url "https://dntcdevcloud.dongfeng-nissan.com.cn/nexus/repository/maven-public/"
        }
        maven {
            allowInsecureProtocol = true
            url "https://devcloud.szlanyou.com/nexus/repository/maven-public/"
        }
        maven { url "https://repo1.maven.org/maven2/" }
        google()

        //        maven {
        //            allowInsecureProtocol = true
        //            url "http://**************:8081/repository/maven_android/"
        //        }
        //        mavenLocal()
        //        mavenCentral()
        //        google()
        //        maven { url 'https://jitpack.io' }
        //        jcenter()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    // 可能加快 Android Studio 编译的办法
    tasks.withType(JavaCompile) {
        //使在一个单独的守护进程编译
        options.fork = true
        //增量编译
        options.incremental = true
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
