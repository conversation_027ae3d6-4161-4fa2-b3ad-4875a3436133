dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
//        maven {
//            allowInsecureProtocol = true
//            url 'http://maven.aliyun.com/nexus/content/groups/public/'
//        }
        maven {
            allowInsecureProtocol = true
            url "https://dntcdevcloud.dongfeng-nissan.com.cn/nexus/repository/maven-public/"
        }
        maven {
            allowInsecureProtocol = true
            url "https://devcloud.szlanyou.com/nexus/repository/maven-public/"
        }

//        maven {
//            allowInsecureProtocol = true
//            url "http://172.26.157.113:8081/repository/maven_android/"
//        }

//        mavenLocal()
//        mavenCentral()
//        google()
        jcenter()
        maven { url 'https://jitpack.io' }
    }
}
rootProject.name = "SmartScene"
include ':app'
include ':common'
include ':soaservice'
//include ':smartindicator'
