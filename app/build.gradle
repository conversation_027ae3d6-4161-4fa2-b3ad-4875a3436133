plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-kapt'
}
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-parcelize'


android {
    compileSdkVersion rootProject.compileSdk

    defaultConfig {
        applicationId "com.dfl.smartscene"
        minSdk rootProject.minSdk
        targetSdk rootProject.targetSdk
        versionCode rootProject.versionCode
        // 版本号格式为：V0.x.y_分支号_commit号_时间_其他说明项（缺省）
        //日期格式为yyyymmdd
        //以正式SOP稳定版本为V1.0.0_mmm_mmm_mmm_mmm，在SOP之前都以V0.x.y_mmm_mmm_mmm_mmm为版本号，其中每次SOP前正式发版x++;正式发版前小改动y++
        versionName "V" + rootProject.versionName + "_" + getGitBranch() + "_" + getGitCommitId() + "_" + getBuildTime()
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        // Enable MultiDex support
        multiDexEnabled true

        javaCompileOptions {
            annotationProcessorOptions {
                arguments = ["room.schemaLocation": "$projectDir/schemas".toString()]
            }
        }
        resConfigs "en", "zh-rCN"
    }

    compileOptions {
        incremental = true //开启增量编译
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = '11'
    }
    signingConfigs {
        os_app_sign {
            storeFile file('../keystore/platform.jks')
            storePassword "lyos123"
            keyAlias "lyos"
            keyPassword "lyos123"
            v2SigningEnabled true
        }
        os_app_sign_626 {
            storeFile file('../keystore/platform.keystore')
            storePassword "android"
            keyAlias "1"
            keyPassword "android"
            v2SigningEnabled true
        }
    }

    buildTypes {
        release {
            ndk {
                //                        abiFilters "arm64-v8a","armeabi-v7a","x86"
                abiFilters "arm64-v8a"
            }
            //            debuggable true
            crunchPngs true //crunchPng优化, 不加快构建
            minifyEnabled true
            zipAlignEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.os_app_sign_626
            buildConfigField "boolean", "NO_CCM", 'false'
            buildConfigField "boolean", "TestEnv", 'false'
            // 添加平台相关属性
            buildConfigField "String", "platform", '"ccs5"'
            buildConfigField "String", "platform_ccs5", '"ccs5"'
            buildConfigField "String", "platform_ccu", '"ccu"'
            buildConfigField "String", "platform_phone", '"phone"'

        }
        debug {
            ndk {
                abiFilters "arm64-v8a"
            }
            minifyEnabled false

            zipAlignEnabled false
            shrinkResources false
            crunchPngs false //关闭crunchPng优化, 以加快构建

            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.os_app_sign_626
            buildConfigField "boolean", "NO_CCM", 'true'
            buildConfigField "boolean", "TestEnv", 'true'
            // 添加平台相关属性
            buildConfigField "String", "platform", '"ccs5"'
            buildConfigField "String", "platform_ccs5", '"ccs5"'
            buildConfigField "String", "platform_ccu", '"ccu"'
            buildConfigField "String", "platform_phone", '"phone"'
        }
    }
    //
    //    packagingOptions {
    //        pickFirst 'lib/*/libshadowhook.so'
    //    }
    android.applicationVariants.all { variant ->
        variant.outputs.all {
            if (variant.buildType.name == "release") {
                outputFileName = "DFL_SmartScene_${defaultConfig.versionName}_Release.apk"
            } else {
                outputFileName = "DFL_SmartScene_${defaultConfig.versionName}_Debug.apk"
            }
        }
    }

    //开启dataBinding
    buildFeatures {
        dataBinding = true
        viewBinding = true
        aidl = true
    }
    lintOptions {
        abortOnError false
    }
    configurations {
        all {
            exclude group: 'com.google.guava', module: 'listenablefuture'
        }
    }


    //跳过Lint和Test相关的task, 以加速编译
    if (isDebug()) {
        gradle.taskGraph.whenReady {
            tasks.each { task ->
                if (task.name.contains("Test") || task.name.contains("Lint")) {
                    task.enabled = false
                }
            }
        }
    }

    //如果是构建debug包, 则禁用 "png cruncher" (默认cruncherEnabled=true, 禁用以加速构建)
    def enableCruncher = { ->
        return !isDebug()
    }

    aaptOptions {
        //禁用cruncher, 以加速编译
        cruncherEnabled = enableCruncher()
        cruncherProcesses = 0
    }

}

//是否是执行Debug相关task (通用函数, 可供子module调用)
def isDebug() {
    def taskNames = gradle.startParameter.taskNames
    for (tn in taskNames) {
        if ((tn.contains("install") || tn.contains("assemble")) && tn.contains("Debug")) {
            return true
        }
    }
    return false
}

static def getBuildTime() {
    def date = new Date()
    //yy年 MM月 dd日 HH小时 mm分钟 ss秒
    def formattedDate = date.format('yyyyMMddHHmm')
    return formattedDate
}


static def isGitProject() {
    Process process = "git rev-list --count HEAD".execute()
    process.waitFor()
    if (process.getText()) {
        return true
    } else {
        return false
    }
}

// git rev-parse HEAD
static def getGitCommitId() {
    if (isGitProject()) {
        // 获取short commit id
        Process process = "git rev-parse --short HEAD".execute()
        process.waitFor()
        return process.getText().trim()
    }
    return 0
}

static def getGitBranch() {
    if (isGitProject()) {
        // 获取short commit id
        Process process = "git rev-parse --abbrev-ref HEAD".execute()
        process.waitFor()
        return process.getText().trim()
    }
    return "NULL"
}

dependencies {

    // MultiDex support
    implementation 'androidx.multidex:multidex:2.0.1'

    //埋点
    implementation 'com.szlanyou.iov:lyDaEventTrackThirdyParty:0.0.1-alpha6'
    compileOnly 'com.szlanyou.iov:lyDaEventTrackThirdyPartyReinforce:0.0.1-alpha6'
    implementation 'org.bouncycastle:bcprov-jdk15to18:1.68'

    //ccm soa回调，api依赖common库
    implementation project(path: ':soaservice')
    // soa dependence，序列化
    implementation 'com.google.protobuf:protoc:3.5.1'
    implementation('com.google.protobuf:protobuf-java:3.5.1') {
        exclude group: 'com.google.protobuf', module: 'protobuf-java'
    }
    implementation 'com.google.protobuf:protobuf-java-util:3.5.1'

    //base基类和util
    implementation project(path: ':common')

    //tcu通讯工具
    implementation files('libs/libProtocolService-623-1.7.3.jar')

    //customapi
    releaseCompileOnly files("../common/libs/customapi_${customapi_version}.jar")

        implementation 'com.szlanyou.mvvm:mvvmLibrary:1.0.0'
    // https://github.com/greenrobot/EventBus
    implementation 'org.greenrobot:eventbus:3.2.0'

    // text view
    implementation 'com.ruffian.library:RWidgetHelper-AndroidX:0.0.9'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    //rv
    implementation "androidx.recyclerview:recyclerview:$recyclerviewVersion"
    implementation 'com.github.CymChad:BaseRecyclerViewAdapterHelper:3.0.7'
    //rv动效    https://github.com/wasabeef/recyclerview-animators
    implementation 'jp.wasabeef:recyclerview-animators:4.0.2'
    // room
    implementation "androidx.room:room-runtime:$roomVersion"
    kapt "androidx.room:room-compiler:$roomVersion"
    implementation "androidx.room:room-ktx:$roomVersion"
    // 添加 Room 的 RxJava2 支持
    implementation "androidx.room:room-rxjava2:$roomVersion"
    
    annotationProcessor("androidx.room:room-compiler:$roomVersion") {
        exclude module: 'guava'
    }
    //rxjava
    implementation "io.reactivex.rxjava2:rxjava:$rxjava2Version"
    implementation "io.reactivex.rxjava2:rxandroid:$rxandroidVersion"

    //用以拖曳实现
    implementation 'androidx.recyclerview:recyclerview-selection:1.0.0'
    //圆角图片
    implementation 'com.makeramen:roundedimageview:2.3.0'
    //智能刷新  https://github.com/scwang90/SmartRefreshLayout
    implementation 'com.scwang.smartrefresh:SmartRefreshLayout:1.1.3'
    implementation 'com.scwang.smartrefresh:SmartRefreshHeader:1.1.3'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'

    implementation  "com.scwang.smart:refresh-layout-kernel:$smartRefreshVersion"      //核心必须依赖
    implementation  "com.scwang.smart:refresh-header-classics:$smartRefreshVersion"    //经典刷新头
    implementation  "com.scwang.smart:refresh-footer-classics:$smartRefreshVersion"    //经典加载

    //内存泄漏查找工具
    //    debugImplementation "com.squareup.leakcanary:leakcanary-android:$leakcanaryVersion"
    //    releaseImplementation 'com.squareup.leakcanary:leakcanary-android-release:2.11'

    //字符串转换器 和retrofit版本一致
    implementation "com.squareup.retrofit2:converter-scalars:$retrofitVersion"
    //rxjava
    implementation "com.squareup.retrofit2:adapter-rxjava2:$retrofitVersion"
    //rxjava
    implementation "io.reactivex.rxjava2:rxjava:2.2.16"
    implementation 'io.reactivex.rxjava2:rxandroid:2.1.1'

    //轮播组件 https://github.com/YunShiTiger/BannerViewPager
    //轮播  https://github.com/zhpanvip/BannerViewPager/blob/master/README_CN.md
    //    implementation 'com.github.zhpanvip:bannerviewpager:3.5.6'
    implementation 'com.github.zhpanvip:viewpagerindicator:1.2.1'

    //可见即可说库
    implementation 'com.dfl.utils:voice-helper:0.1.7'
    //lottie动画
    implementation "com.airbnb.android:lottie:$lottieVersion"

    //测试相关依赖
    implementation 'androidx.tracing:tracing:1.1.0'
    androidTestImplementation "androidx.test:core:${rootProject.coreVersion}"
    androidTestImplementation "androidx.test:rules:${rootProject.ruleVersion}"
    androidTestImplementation "androidx.test:core-ktx:${rootProject.coreVersion}"
    androidTestImplementation "androidx.test.ext:junit:${rootProject.extJUnitVersion}"
    androidTestImplementation "androidx.test.ext:junit-ktx:${rootProject.extJUnitVersion}"
    androidTestImplementation "androidx.test:runner:${rootProject.runnerVersion}"
    androidTestImplementation "androidx.test.espresso:espresso-core:${rootProject.espressoVersion}"
    androidTestImplementation "androidx.test.espresso:espresso-contrib:${rootProject.espressoVersion}"
    testImplementation "androidx.test:core:${rootProject.coreVersion}"
    testImplementation "androidx.test.ext:junit:${rootProject.extJUnitVersion}"
    testImplementation "junit:junit:${rootProject.junitVersion}"
    testImplementation "org.robolectric:robolectric:${rootProject.robolectricVersion}"
    testImplementation "androidx.test.espresso:espresso-core:${rootProject.espressoVersion}"
    testImplementation "androidx.test.espresso:espresso-intents:${rootProject.espressoVersion}"
    testImplementation "androidx.test.ext:truth:${rootProject.extTruthVersion}"

    //codelocator 仅debug不能release! 查看ui布局 https://zhuanlan.zhihu.com/p/657471126?utm_id=0
    debugImplementation "com.bytedance.tools.codelocator:codelocator-core:2.0.3"

    //=====吉大网络安全证书SDK  start====
    implementation files('libs/bcmail-jdk15to18-1.68.jar')
    implementation files('libs/bcpkix-jdk15to18-1.68.jar')
    //    implementation files('libs/bcprov-jdk15to18-1.68.jar')
    //    implementation files('libs/gson-2.10.1.jar')
    implementation files('libs/jit-lApi.jar')

    implementation files('libs/JITOSAuthSupport1.0.37.3.1.jar')
    implementation files('libs/JITOSCertExtSupport1.0.37.3.1.jar')
    implementation files('libs/JITOSCertSupport1.0.37.3.1.jar')
    implementation files('libs/JITOSCommonSupport1.0.37.3.1.jar')
    implementation files('libs/JITOSCryptoSupport1.0.37.3.1.jar')
    implementation files('libs/JITOSLanguageSupport1.0.37.3.1.jar')
    implementation files('libs/JITOSLicSupport1.0.37.3.1.jar')
    implementation files('libs/JITOSLogSupport1.0.37.3.1.jar')
    implementation files('libs/JITOSNetSupport1.0.37.3.1.jar')
    implementation files('libs/JITOSParentSupport1.0.37.3.1.jar')
    implementation files('libs/JITOSSupport1.0.37.3.1.jar')
    implementation files('libs/jit-pkitool-3.3.1.jar')
    implementation files('libs/JITPKIToolExtAndroid1.0.37.3.1.jar')
    //=====吉大网络安全证书SDK  end====
    //    implementation project(path: ':dapki')

    //    implementation project(path: ':smartindicator')
    //    用于解析skill json
    implementation "com.fasterxml.jackson.core:jackson-core:${rootProject.jacksonVersion}"
    implementation "com.fasterxml.jackson.core:jackson-databind:${rootProject.jacksonVersion}"
    implementation "com.fasterxml.jackson.core:jackson-annotations:${rootProject.jacksonVersion}"


    compileOnly files('libs\\framework.jar')
}

