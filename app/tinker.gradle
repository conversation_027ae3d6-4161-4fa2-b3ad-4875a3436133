def gitSha() {
    try {
        String gitRev = 'git rev-parse --short HEAD'.execute(null, project.rootDir).text.trim()
        if (gitRev == null) {
            throw new GradleException("can't get git rev, you should add git to system path or just input test value, such as 'testTinkerId'")
        }
        return gitRev
    } catch (Exception e) {
        throw new GradleException("can't get git rev, you should add git to system path or just input test value, such as 'testTinkerId'")
    }
}

def bakPath = file("${buildDir}/bakApk/")
ext {
    // 调试阶段可以设置为 false，避免一直生成基准包
    tinkerEnabled = false
    // 基准包路径
    tinkerOldApkPath = "${bakPath}/newscene-v1.2.2-2023-07-27-DflPatch_1.2.2_20230727115627_release-20230727115627.apk"
    // 混淆映射文件(没开启混淆，则随便配置一个不存在的文件即可)
    tinkerApplyMappingPath = "${bakPath}/newscene-v1.2.2-2023-07-27-DflPatch_1.2.2_20230727115627_release-20230727115627-mapping.txt"
    // 资源文件(如果有改资源文件，务必配置)
    tinkerApplyResourcePath = "${bakPath}/newscene-v1.2.2-2023-07-27-DflPatch_1.2.2_20230727115627_release-20230727115627-R.txt"
    // 使用了product flavor 则用这个来配合配置基准包的路径
    tinkerBuildFlavorDirectory = "${bakPath}/"
}
android {
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    packagingOptions {
        exclude "/META-INF/**"
    }
}
def getOldApkPath() {
    return hasProperty("OLD_APK") ? OLD_APK : ext.tinkerOldApkPath
}
def getApplyMappingPath() {
    return hasProperty("APPLY_MAPPING") ? APPLY_MAPPING : ext.tinkerApplyMappingPath
}
def getApplyResourceMappingPath() {
    return hasProperty("APPLY_RESOURCE") ? APPLY_RESOURCE : ext.tinkerApplyResourcePath
}
def buildWithTinker() {
    return hasProperty("TINKER_ENABLE") ? Boolean.parseBoolean(TINKER_ENABLE) : ext.tinkerEnabled
}
def getTinkerBuildFlavorDirectory() {
    return ext.tinkerBuildFlavorDirectory
}
if (buildWithTinker()) {
    //apply tinker插件
    apply plugin: 'com.tencent.tinker.patch'
    // 全局信息相关的配置项
    tinkerPatch {
        removeLoaderForAllDex = true
        oldApk = getOldApkPath()            // 基准apk包的路径，必须输入，否则会报错。
        ignoreWarning = true                // 是否忽略有风险的补丁包。这里选择不忽略，当补丁包风险时会中断编译。
        useSign = true                      // 是否忽略有风险的补丁包。这里选择不忽略，当补丁包风险时会中断编译。
        tinkerEnable = buildWithTinker()    // 是否打开tinker的功能。
        // 编译相关的配置项
        buildConfig {
            // 可选参数；在编译新的apk时候，我们希望通过保持旧apk的proguard混淆方式，从而减少补丁包的大小。这个只是推荐设置，不设置applyMapping也不会影响任何的assemble编译。
            applyMapping = getApplyMappingPath()
            // 可选参数；在编译新的apk时候，我们希望通过旧apk的R.txt文件保持ResId的分配，这样不仅可以减少补丁包的大小，同时也避免由于ResId改变导致remote view异常。
            applyResourceMapping = getApplyResourceMappingPath()
            // 在运行过程中，我们需要验证基准apk包的tinkerId是否等于补丁包的tinkerId。这个是决定补丁包能运行在哪些基准包上面。
            tinkerId = versionName + "_" + patchVersion
            // 如果我们有多个dex,编译补丁时可能会由于类的移动导致变更增多。若打开keepDexApply模式，补丁包将根据基准包的类分布来编译。
            keepDexApply = false
            // 是否使用加固模式，仅仅将变更的类合成补丁。注意，这种模式仅仅可以用于加固应用中。
            isProtectedApp = false
            // 是否支持新增非export的Activity（1.9.0版本开始才有的新功能）
            supportHotplugComponent = false
        }
        // dex相关的配置项
        dex {
            // 只能是'raw'或者'jar'。 对于'raw'模式，我们将会保持输入dex的格式。对于'jar'模式，我们将会把输入dex重新压缩封装到jar。如果你的minSdkVersion小于14，你必须选择‘jar’模式，而且它更省存储空间，但是验证md5时比'raw'模式耗时。默认我们并不会去校验md5,一般情况下选择jar模式即可。
            dexMode = "jar"
            // 需要处理dex路径，支持*、?通配符，必须使用'/'分割。路径是相对安装包的，例如assets/...
            pattern = ["classes*.dex",
                       "assets/secondary-dex-?.jar"]
            loader = [
                    // 定义哪些类在加载补丁包的时候会用到。这些类是通过Tinker无法修改的类，也是一定要放在main dex的类。
                    // 如果你自定义了TinkerLoader，需要将它以及它引用的所有类也加入loader中；
                    // 其他一些你不希望被更改的类，例如Sample中的BaseBuildInfo类。这里需要注意的是，这些类的直接引用类也需要加入到loader中。或者你需要将这个类变成非preverify。
                    "tinker.sample.android.app.BaseBuildInfo"
            ]
        }
        //  lib相关的配置项
        lib {
            // 需要处理lib路径，支持*、?通配符，必须使用'/'分割。与dex.pattern一致, 路径是相对安装包的，例如assets/...
            pattern = ["lib/*/*.so"]
        }
        // res相关的配置项
        res {
            // 需要处理res路径，支持*、?通配符，必须使用'/'分割。与dex.pattern一致, 路径是相对安装包的，例如assets/...，务必注意的是，只有满足pattern的资源才会放到合成后的资源包。
            pattern = ["res/*", "assets/*", "resources.arsc", "AndroidManifest.xml"]
            // 支持*、?通配符，必须使用'/'分割。若满足ignoreChange的pattern，在编译时会忽略该文件的新增、删除与修改。 最极端的情况，ignoreChange与上面的pattern一致，即会完全忽略所有资源的修改。
            ignoreChange = ["assets/sample_meta.txt"]
            // 对于修改的资源，如果大于largeModSize，我们将使用bsdiff算法。这可以降低补丁包的大小，但是会增加合成时的复杂度。默认大小为100kb
            largeModSize = 100
        }
        // 用于生成补丁包中的'package_meta.txt'文件
        packageConfig {
            configField("patchMessage", "tinker is sample to use")
            configField("platform", "all")
            configField("patchVersion", "1.0")
        }
        // 7zip路径配置项，执行前提是useSign为true
        sevenZip {
            zipArtifact = "com.tencent.mm:SevenZip:1.1.10"
//            zipArtifact = "com.tencent.mm:SevenZip:1.2.17"
        }
    }
    //是否配置了多渠道
    boolean hasFlavors = false
    def date = new Date().format("yyyyMMddHHmmss",TimeZone.getTimeZone("GMT+08:00:00"))
    android.applicationVariants.all { variant ->
        def taskName = variant.name
        tasks.all {
            if ("assemble${taskName.capitalize()}".equalsIgnoreCase(it.name)) {
                it.doLast {
                    copy {
                        //官方的写法有误，恒为false，这里需要重新判断
                        if (!hasFlavors) {
                            List<String> flavors = new ArrayList<>();
                            project.android.productFlavors.each { flavor ->
                                println("flavor.name = " + flavor.name)
                                flavors.add(flavor.name)
                            }
                            println("flavors 数量 = " + flavors.size())
                            hasFlavors = flavors.size() > 0
                        }
                        println("tasks.all hasFlavors = " + hasFlavors)
                        def outputFileName = null
                        def fileNamePrefix = null
                        def destPath = hasFlavors ? file("${bakPath}/${variant.flavorName}") : bakPath
                        println("destPath = " + destPath)
                        if (variant.metaClass.hasProperty(variant, 'packageApplicationProvider')) {
                            def packageAndroidArtifact = variant.packageApplicationProvider.get()
                            if (packageAndroidArtifact != null) {
                                try {
                                    println("try")
                                    outputFileName = variant.outputs.first().apkData.outputFileName
                                    fileNamePrefix = outputFileName.substring(0, outputFileName.length() - 4) + "-" + date
                                    println("fileNamePrefix = " + fileNamePrefix)
                                    println("outputFileName = " + outputFileName)
                                    from new File(packageAndroidArtifact.outputDirectory.getAsFile().get(), variant.outputs.first().apkData.outputFileName)
                                } catch (Exception e) {
                                    try {
                                        println("catch try")
                                        outputFileName = variant.outputs.first().apkData.outputFileName
                                        fileNamePrefix = outputFileName.substring(0, outputFileName.length() - 4) + "-" + date
                                        println("fileNamePrefix = " + fileNamePrefix)
                                        println("outputFileName = " + outputFileName)
                                        from new File(packageAndroidArtifact.outputDirectory, variant.outputs.first().apkData.outputFileName)
                                    } catch (Exception ignored) {
                                        println("catch catch")
                                        from variant.outputs.first().outputFile
                                    }
                                }
                            } else {
                                from variant.outputs.first().mainOutputFile.outputFile
                            }
                        } else {
                            from variant.outputs.first().outputFile
                        }
                        into destPath
                        rename { String fileName ->
                            fileName.replace("${outputFileName}", "${fileNamePrefix}.apk")
                        }
                        def dirName = variant.dirName
                        println("taskName = " + taskName)
                        println("hasFlavors = " + hasFlavors)
                        if (hasFlavors) {
                            dirName = taskName
                        }
                        println("dirName = " + dirName)
                        from "${buildDir}/outputs/mapping/${dirName}/mapping.txt"
                        into destPath
                        rename { String fileName ->
                            fileName.replace("mapping.txt", "${fileNamePrefix}-mapping.txt")
                        }
                        from "${buildDir}/intermediates/symbols/${dirName}/R.txt"
                        from "${buildDir}/intermediates/symbol_list/${dirName}/R.txt"
                        from "${buildDir}/intermediates/runtime_symbol_list/${dirName}/R.txt"
                        into destPath
                        rename { String fileName ->
                            fileName.replace("R.txt", "${fileNamePrefix}-R.txt")
                        }
                    }
                }
            }
        }
    }
    project.afterEvaluate {
        //sample use for build all flavor for one time
        if (hasFlavors) {
            task(tinkerPatchAllFlavorRelease) {
                group = 'tinker'
                def originOldPath = getTinkerBuildFlavorDirectory()
                for (String flavor : flavors) {
                    def tinkerTask = tasks.getByName("tinkerPatch${flavor.capitalize()}Release")
                    dependsOn tinkerTask
                    def preAssembleTask = tasks.getByName("process${flavor.capitalize()}ReleaseManifest")
                    preAssembleTask.doFirst {
                        String flavorName = preAssembleTask.name.substring(7, 8).toLowerCase() + preAssembleTask.name.substring(8, preAssembleTask.name.length() - 15)
                        project.tinkerPatch.oldApk = "${originOldPath}/${flavorName}/${project.name}-${flavorName}-release.apk"
                        project.tinkerPatch.buildConfig.applyMapping = "${originOldPath}/${flavorName}/${project.name}-${flavorName}-release-mapping.txt"
                        project.tinkerPatch.buildConfig.applyResourceMapping = "${originOldPath}/${flavorName}/${project.name}-${flavorName}-release-R.txt"
                    }
                }
            }
            task(tinkerPatchAllFlavorDebug) {
                group = 'tinker'
                def originOldPath = getTinkerBuildFlavorDirectory()
                for (String flavor : flavors) {
                    def tinkerTask = tasks.getByName("tinkerPatch${flavor.capitalize()}Debug")
                    dependsOn tinkerTask
                    def preAssembleTask = tasks.getByName("process${flavor.capitalize()}DebugManifest")
                    preAssembleTask.doFirst {
                        String flavorName = preAssembleTask.name.substring(7, 8).toLowerCase() + preAssembleTask.name.substring(8, preAssembleTask.name.length() - 13)
                        project.tinkerPatch.oldApk = "${originOldPath}/${flavorName}/${project.name}-${flavorName}-debug.apk"
                        project.tinkerPatch.buildConfig.applyMapping = "${originOldPath}/${flavorName}/${project.name}-${flavorName}-debug-mapping.txt"
                        project.tinkerPatch.buildConfig.applyResourceMapping = "${originOldPath}/${flavorName}/${project.name}-${flavorName}-debug-R.txt"
                    }
                }
            }
        }
    }
}
task sortPublicTxt() {
    doLast {
        File originalFile = project.file("public.txt")
        File sortedFile = project.file("public_sort.txt")
        List<String> sortedLines = new ArrayList<>()
        originalFile.eachLine {
            sortedLines.add(it)
        }
        Collections.sort(sortedLines)
        sortedFile.delete()
        sortedLines.each {
            sortedFile.append("${it}\n")
        }
    }
}