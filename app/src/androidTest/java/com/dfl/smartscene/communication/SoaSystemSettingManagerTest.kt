package com.dfl.smartscene.communication

import com.dfl.android.common.util.GsonUtils
import com.dfl.soacenter.communication.SoaSystemSettingManager
import com.dfl.soacenter.entity.RequestSoaBaseBean
import com.dfl.soacenter.entity.SystemSettingResponseBean
import com.dfl.soacenter.entity.ValueDataBean
import com.dfl.soacenter.entity.VolumeDataBean
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import org.junit.Assert
import org.junit.Before
import org.junit.Test

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/02/22
 * desc : 系统设置
 * version: 1.0
 */
class SoaSystemSettingManagerTest {
    @Before
    fun init() {
        SoaSystemSettingManager.init()
    }

    /**
     * 系统设置设置静音
     */
    @Test
    fun systemSettingSetMute() {
        val reqOpen = RequestSoaBaseBean(
            protocolId = SoaSystemSettingManager.ProtocolType.SET_MUTE_STATUS,
            data = ValueDataBean(1)
        )
        val reqClose = RequestSoaBaseBean(
            protocolId = SoaSystemSettingManager.ProtocolType.SET_MUTE_STATUS,
            data = ValueDataBean(0)
        )
        runBlocking {
            Assert.assertEquals(
                SystemSettingResponseBean.STATUS_CODE_SUCCESS,
                async {
                    SoaSystemSettingManager.testRequestAction(
                        GsonUtils.toJson(reqOpen),
                        "systemSettingSetMute open"
                    )
                }.await()?.statusCode
            )
            Assert.assertEquals(
                async {
                    SoaSystemSettingManager.testRequestAction(
                        GsonUtils.toJson(reqClose),
                        "systemSettingSetMute close"
                    )
                }.await()?.statusCode,
                SystemSettingResponseBean.STATUS_CODE_SUCCESS
            )
        }
    }

    /**
     * 系统设置设置音量
     */
    @Test
    fun systemSettingSetVolume() {
        val reqBluetooth = RequestSoaBaseBean(
            protocolId = SoaSystemSettingManager.ProtocolType.SET_VOLUME,
            data = VolumeDataBean(VolumeDataBean.VOLUME_BLUETOOTH, 0)
        )
        val reqAssistant = RequestSoaBaseBean(
            protocolId = SoaSystemSettingManager.ProtocolType.SET_VOLUME,
            data = VolumeDataBean(VolumeDataBean.VOLUME_ASSISTANT, 0)
        )
        val reqCall = RequestSoaBaseBean(
            protocolId = SoaSystemSettingManager.ProtocolType.SET_VOLUME,
            data = VolumeDataBean(VolumeDataBean.VOLUME_CALL, 0)
        )
        val reqMedia = RequestSoaBaseBean(
            protocolId = SoaSystemSettingManager.ProtocolType.SET_VOLUME,
            data = VolumeDataBean(VolumeDataBean.VOLUME_MEDIA, 0)
        )
        val reqNavi = RequestSoaBaseBean(
            protocolId = SoaSystemSettingManager.ProtocolType.SET_VOLUME,
            data = VolumeDataBean(VolumeDataBean.VOLUME_NAVI, 0)
        )
        runBlocking {
            Assert.assertEquals(
                SystemSettingResponseBean.STATUS_CODE_SUCCESS,
                async {
                    SoaSystemSettingManager.testRequestAction(
                        GsonUtils.toJson(reqBluetooth),
                        "systemSettingSetVolume Bluetooth"
                    )
                }.await()?.statusCode
            )
            Assert.assertEquals(
                SystemSettingResponseBean.STATUS_CODE_SUCCESS,
                async {
                    SoaSystemSettingManager.testRequestAction(
                        GsonUtils.toJson(reqAssistant),
                        "systemSettingSetVolume Assistant"
                    )
                }.await()?.statusCode
            )
            Assert.assertEquals(
                SystemSettingResponseBean.STATUS_CODE_SUCCESS,
                async {
                    SoaSystemSettingManager.testRequestAction(
                        GsonUtils.toJson(reqCall),
                        "systemSettingSetVolume Call"
                    )
                }.await()?.statusCode
            )
            Assert.assertEquals(
                SystemSettingResponseBean.STATUS_CODE_SUCCESS,
                async {
                    SoaSystemSettingManager.testRequestAction(
                        GsonUtils.toJson(reqMedia),
                        "systemSettingSetVolume Media"
                    )
                }.await()?.statusCode
            )
            Assert.assertEquals(
                SystemSettingResponseBean.STATUS_CODE_SUCCESS,
                async {
                    SoaSystemSettingManager.testRequestAction(
                        GsonUtils.toJson(reqNavi),
                        "systemSettingSetVolume Navi"
                    )
                }.await()?.statusCode
            )
        }
    }

    /**
     * 系统设置设置壁纸
     */
    @Test
    fun systemSettingSetThemeWallpaper() {
        val req1 = RequestSoaBaseBean(
            protocolId = SoaSystemSettingManager.ProtocolType.SET_THEME_WALLPAPER,
            data = ValueDataBean(1)
        )
        val req2 = RequestSoaBaseBean(
            protocolId = SoaSystemSettingManager.ProtocolType.SET_THEME_WALLPAPER,
            data = ValueDataBean(9)
        )
        runBlocking {
            Assert.assertEquals(
                async {
                    SoaSystemSettingManager.testRequestAction(
                        GsonUtils.toJson(req1),
                        "systemSettingSetThemeWallpaper 1"
                    )
                }.await()?.statusCode,
                SystemSettingResponseBean.STATUS_CODE_SUCCESS
            )
            Assert.assertEquals(
                async {
                    SoaSystemSettingManager.testRequestAction(
                        GsonUtils.toJson(req2),
                        "systemSettingSetThemeWallpaper 9"
                    )
                }.await()?.statusCode,
                SystemSettingResponseBean.STATUS_CODE_SUCCESS
            )
        }
    }

    /**
     * 系统设置设置中控亮度
     */
    @Test
    fun systemSettingSetScreenBrightness() {
        val reqMin = RequestSoaBaseBean(
            protocolId = SoaSystemSettingManager.ProtocolType.SET_SCREEN_BRIGHTNESS,
            data = ValueDataBean(1)
        )
        val reqMax = RequestSoaBaseBean(
            protocolId = SoaSystemSettingManager.ProtocolType.SET_SCREEN_BRIGHTNESS,
            data = ValueDataBean(21)
        )
        runBlocking {
            Assert.assertEquals(
                SystemSettingResponseBean.STATUS_CODE_SUCCESS,
                async {
                    SoaSystemSettingManager.testRequestAction(
                        GsonUtils.toJson(reqMin),
                        "systemSettingSetScreenBrightness 1"
                    )
                }.await()?.statusCode
            )
            Assert.assertEquals(
                SystemSettingResponseBean.STATUS_CODE_SUCCESS,
                async {
                    SoaSystemSettingManager.testRequestAction(
                        GsonUtils.toJson(reqMax),
                        "systemSettingSetScreenBrightness 21"
                    )
                }.await()?.statusCode
            )
        }
    }

    /**
     * 系统设置日夜模式
     */
    @Test
    fun systemSettingSetDayNight() {
        val reqDay = RequestSoaBaseBean(
            protocolId = SoaSystemSettingManager.ProtocolType.SET_DAY_AND_NIGHT_MODE,
            data = ValueDataBean(0)
        )
        //val reqNight = RequestSoaBaseBean(
        //	protocolId = SystemSettingManager.ProtocolType.SET_DAY_AND_NIGHT_MODE,
        //	data = ValueDataBean(2)
        //)
        runBlocking {
            Assert.assertEquals(
                SystemSettingResponseBean.STATUS_CODE_SUCCESS,
                async {
                    SoaSystemSettingManager.testRequestAction(
                        GsonUtils.toJson(reqDay),
                        "systemSettingSetDayNight day"
                    )
                }.await()?.statusCode
            )
            //Assert.assertEquals(
            //	SystemSettingResponseBean.STATUS_CODE_SUCCESS,
            //	async {
            //		SystemSettingManager.testRequestAction(
            //			GsonUtils.toJson(reqNight),
            //			"systemSettingSetDayNight night"
            //		)
            //	}.await()?.statusCode
            //)
        }
    }

    /**
     * 系统设置蓝牙开关
     */
    @Test
    fun systemSettingSetBluetoothSwitch() {
        val reqOpen = RequestSoaBaseBean(
            protocolId = SoaSystemSettingManager.ProtocolType.SET_BLUETOOTH_SWITCH_STATUS,
            data = ValueDataBean(1)
        )
        val reqClose = RequestSoaBaseBean(
            protocolId = SoaSystemSettingManager.ProtocolType.SET_BLUETOOTH_SWITCH_STATUS,
            data = ValueDataBean(0)
        )
        runBlocking {
            Assert.assertEquals(
                SystemSettingResponseBean.STATUS_CODE_SUCCESS,
                async {
                    SoaSystemSettingManager.testRequestAction(
                        GsonUtils.toJson(reqOpen),
                        "systemSettingSetBluetoothSwitch open"
                    )
                }.await()?.statusCode
            )
            Assert.assertEquals(
                SystemSettingResponseBean.STATUS_CODE_SUCCESS,
                async {
                    SoaSystemSettingManager.testRequestAction(
                        GsonUtils.toJson(reqClose),
                        "systemSettingSetBluetoothSwitch close"
                    )
                }.await()?.statusCode
            )
        }
    }

    /**
     * 系统设置热点开关
     */
    @Test
    fun systemSettingSetHotspotSwitch() {
        val reqOpen = RequestSoaBaseBean(
            protocolId = SoaSystemSettingManager.ProtocolType.SET_HOTSPOT_SWITCH_STATUS,
            data = ValueDataBean(1)
        )
        val reqClose = RequestSoaBaseBean(
            protocolId = SoaSystemSettingManager.ProtocolType.SET_HOTSPOT_SWITCH_STATUS,
            data = ValueDataBean(0)
        )
        runBlocking {
            Assert.assertEquals(
                SystemSettingResponseBean.STATUS_CODE_SUCCESS,
                async {
                    SoaSystemSettingManager.testRequestAction(
                        GsonUtils.toJson(reqOpen),
                        "systemSettingSetHotspotSwitch open"
                    )
                }.await()?.statusCode,
            )
            Assert.assertEquals(
                SystemSettingResponseBean.STATUS_CODE_SUCCESS,
                async {
                    SoaSystemSettingManager.testRequestAction(
                        GsonUtils.toJson(reqClose),
                        "systemSettingSetHotspotSwitch close"
                    )
                }.await()?.statusCode,
            )
        }
    }

    /**
     * 系统设置wifi开关
     */
    @Test
    fun systemSettingSetWLANSwitch() {
        val reqOpen = RequestSoaBaseBean(
            protocolId = SoaSystemSettingManager.ProtocolType.SET_WLAN_SWITCH_STATUS,
            data = ValueDataBean(1)
        )
        val reqClose = RequestSoaBaseBean(
            protocolId = SoaSystemSettingManager.ProtocolType.SET_WLAN_SWITCH_STATUS,
            data = ValueDataBean(0)
        )
        runBlocking {
            Assert.assertEquals(
                SystemSettingResponseBean.STATUS_CODE_SUCCESS,
                async {
                    SoaSystemSettingManager.testRequestAction(
                        GsonUtils.toJson(reqOpen),
                        "systemSettingSetWLANSwitch open"
                    )
                }.await()?.statusCode
            )
            Assert.assertEquals(
                SystemSettingResponseBean.STATUS_CODE_SUCCESS,
                async {
                    SoaSystemSettingManager.testRequestAction(
                        GsonUtils.toJson(reqClose),
                        "systemSettingSetWLANSwitch close"
                    )
                }.await()?.statusCode
            )
        }
    }

    /**
     * 系统设置移动网络开关
     */
    @Test
    fun systemSettingSetMobileSwitch() {
        val reqOpen = RequestSoaBaseBean(
            protocolId = SoaSystemSettingManager.ProtocolType.SET_MOBILE_NETWORK_SWITCH_STATUS,
            data = ValueDataBean(1)
        )
        val reqClose = RequestSoaBaseBean(
            protocolId = SoaSystemSettingManager.ProtocolType.SET_MOBILE_NETWORK_SWITCH_STATUS,
            data = ValueDataBean(0)
        )
        runBlocking {
            Assert.assertEquals(
                SystemSettingResponseBean.STATUS_CODE_SUCCESS,
                async {
                    SoaSystemSettingManager.testRequestAction(
                        GsonUtils.toJson(reqOpen),
                        "systemSettingSetMobileSwitch open"
                    )
                }.await()?.statusCode
            )
            Assert.assertEquals(
                SystemSettingResponseBean.STATUS_CODE_SUCCESS,
                async {
                    SoaSystemSettingManager.testRequestAction(
                        GsonUtils.toJson(reqClose),
                        "systemSettingSetMobileSwitch close"
                    )
                }.await()?.statusCode
            )
        }
    }

    /**
     * 系统设置蓝牙连接状态
     */
    @Test
    fun systemSettingBluetoothConnection() {
        runBlocking {
            Assert.assertNotNull(async { SoaSystemSettingManager.getBluetoothConnectionStatus() }.await()?.connectionStatus)
        }
    }

    /**
     * 系统设置热点连接状态
     */
    @Test
    fun systemSettingHotspotConnection() {
        runBlocking {
            Assert.assertNotNull(async { SoaSystemSettingManager.getHotspotConnectionStatus() }.await()?.connectionStatus)
        }
    }

    /**
     * 系统设置wifi连接状态
     */
    @Test
    fun systemSettingWLANConnectionStatus() {
        runBlocking {
            Assert.assertNotNull(async { SoaSystemSettingManager.getWLANConnectionStatus() }.await()?.connectionStatus)
        }
    }

    /**
     * 系统设置蓝牙开关状态
     */
    @Test
    fun systemSettingBluetoothSwitch() {
        runBlocking {
            Assert.assertNotNull(async { SoaSystemSettingManager.getBluetoothSwitchStatus() }.await()?.switchStatus)
        }
    }

    /**
     * 系统设置热点开关状态
     */
    @Test
    fun systemSettingHotspotSwitch() {
        runBlocking {
            Assert.assertNotNull(async { SoaSystemSettingManager.getHotspotSwitchStatus() }.await()?.switchStatus)
        }
    }

    /**
     * 系统设置wifi开关状态
     */
    @Test
    fun systemSettingWLANSwitch() {
        runBlocking {
            Assert.assertNotNull(async { SoaSystemSettingManager.getWLANSwitchStatus() }.await()?.switchStatus)
        }
    }

    /**
     * 系统设置移动网络开关状态
     */
    @Test
    fun systemSettingMobileNetworkSwitch() {
        runBlocking {
            Assert.assertNotNull(async { SoaSystemSettingManager.getMobileNetworkSwitchStatus() }.await()?.switchStatus)
        }
    }
}