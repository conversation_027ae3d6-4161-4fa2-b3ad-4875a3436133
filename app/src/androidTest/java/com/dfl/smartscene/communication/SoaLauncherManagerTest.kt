package com.dfl.smartscene.communication

import com.dfl.soacenter.communication.CarControlManager
import com.dfl.soacenter.communication.SoaLauncherManager
import org.junit.Assert
import org.junit.Before
import org.junit.Test

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/02/27
 * desc : 打开其他应用测试
 * version: 1.0
 */
class SoaLauncherManagerTest {
	@Before
	fun init() {
		CarControlManager.init()
	}

	/**
	 * 测试打开应用
	 */
	@Test
	fun openAppTest() {
		Assert.assertTrue(SoaLauncherManager.openAPP(14))
		Thread.sleep(1000)
		Assert.assertTrue(SoaLauncherManager.openAPP(8))
		Thread.sleep(2000)
	}
}