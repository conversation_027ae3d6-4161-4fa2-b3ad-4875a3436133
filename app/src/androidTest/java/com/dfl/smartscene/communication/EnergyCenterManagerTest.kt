package com.dfl.smartscene.communication

import com.dfl.android.common.util.GsonUtils
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.communication.EnergyCenterManager
import com.dfl.soacenter.entity.EnergyCenterRequestDataBean
import com.dfl.soacenter.entity.EnergyCenterResponseDataBean
import com.dfl.soacenter.entity.RequestSoaBaseBean
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import org.junit.Assert
import org.junit.Before
import org.junit.Test

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/02/22
 * desc : 能源中心测试
 * version: 1.0
 */
class EnergyCenterManagerTest {
    @Before
    fun init() {
        EnergyCenterManager.init()
    }

    /**
     * 能源中心对外放电通信
     */
    @Test
    fun energyCenterDischargeSwitch() {
        val reqOpen = RequestSoaBaseBean(
            protocolId = EnergyCenterManager.ProtocolType.DISCHARGE_SWITCH,
            messageType = SoaConstants.MESSAGE_TYPE_REQUEST,
            data = EnergyCenterRequestDataBean(
                EnergyCenterRequestDataBean.ACTION_TYPE_DISCHARGE_SWITCH,
                EnergyCenterRequestDataBean.OPERA_TYPE_OPEN
            )
        )
        val reqClose = RequestSoaBaseBean(
            protocolId = EnergyCenterManager.ProtocolType.DISCHARGE_SWITCH,
            messageType = SoaConstants.MESSAGE_TYPE_REQUEST,
            data = EnergyCenterRequestDataBean(
                EnergyCenterRequestDataBean.ACTION_TYPE_DISCHARGE_SWITCH,
                EnergyCenterRequestDataBean.OPERA_TYPE_CLOSE
            )
        )
        runBlocking {
            Assert.assertNotEquals(
                EnergyCenterResponseDataBean.RESULT_CODE_FAIL,
                async {
                    EnergyCenterManager.testRequestAction(
                        GsonUtils.toJson(reqOpen),
                        "energyCenterDischargeSwitch open"
                    )
                }.await()?.data?.resultCode
            )
            Assert.assertNotEquals(
                EnergyCenterResponseDataBean.RESULT_CODE_FAIL,
                async {
                    EnergyCenterManager.testRequestAction(
                        GsonUtils.toJson(reqClose),
                        "energyCenterDischargeSwitch close"
                    )
                }.await()?.data?.resultCode
            )
        }
    }

    /**
     * 能源中心在途预热通信
     */
    @Test
    fun energyCenterPreheatSwitch() {
        val reqOpen = RequestSoaBaseBean(
            protocolId = EnergyCenterManager.ProtocolType.PREHEAT_SWITCH,
            messageType = SoaConstants.MESSAGE_TYPE_REQUEST,
            data = EnergyCenterRequestDataBean(
                EnergyCenterRequestDataBean.ACTION_TYPE_PREHEAT_SWITCH,
                EnergyCenterRequestDataBean.OPERA_TYPE_OPEN
            )
        )
        val reqClose = RequestSoaBaseBean(
            protocolId = EnergyCenterManager.ProtocolType.PREHEAT_SWITCH,
            messageType = SoaConstants.MESSAGE_TYPE_REQUEST,
            data = EnergyCenterRequestDataBean(
                EnergyCenterRequestDataBean.ACTION_TYPE_PREHEAT_SWITCH,
                EnergyCenterRequestDataBean.OPERA_TYPE_CLOSE
            )
        )
        runBlocking {
            Assert.assertNotEquals(
                EnergyCenterResponseDataBean.RESULT_CODE_FAIL,
                async {
                    EnergyCenterManager.testRequestAction(
                        GsonUtils.toJson(reqOpen),
                        "energyCenterDischargeSwitch open"
                    )
                }.await()?.data?.resultCode
            )
            Assert.assertNotEquals(
                EnergyCenterResponseDataBean.RESULT_CODE_FAIL,
                async {
                    EnergyCenterManager.testRequestAction(
                        GsonUtils.toJson(reqClose),
                        "energyCenterDischargeSwitch close"
                    )
                }.await()?.data?.resultCode
            )
        }
    }
}