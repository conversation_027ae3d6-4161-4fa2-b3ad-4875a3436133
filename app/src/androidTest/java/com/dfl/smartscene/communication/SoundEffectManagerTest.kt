package com.dfl.smartscene.communication

import com.dfl.android.common.util.GsonUtils
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.communication.SoundEffectManager
import com.dfl.soacenter.entity.*
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import org.junit.Assert
import org.junit.Before
import org.junit.Test

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/02/22
 * desc : 声音空间
 * version: 1.0
 */
class SoundEffectManagerTest {
    @Before
    fun init() {
        SoundEffectManager.init()
    }

    /**
     *声音空间设置沉浸音效
     */
    @Test
    fun soundCenterSoundEffect() {
        val req1 = RequestSoaBaseBean(
            protocolId = SoundEffectManager.ProtocolType.PROTOCOL_ID_SOUND_EFFECT,
            messageType = SoaConstants.MESSAGE_TYPE_REQUEST,
            data = SoundEffectRequestData("1")
        )
        val req5 = RequestSoaBaseBean(
            protocolId = SoundEffectManager.ProtocolType.PROTOCOL_ID_SOUND_EFFECT,
            messageType = SoaConstants.MESSAGE_TYPE_REQUEST,
            data = SoundEffectRequestData("5")
        )
        runBlocking {
            Assert.assertEquals(
                SoundSpaceResponse.RESPONSE_RESULT_SUCCESS,
                async {
                    SoundEffectManager.testRequestAction(
                        GsonUtils.toJson(req1),
                        "soundCenterSoundEffect 1"
                    )
                }.await()?.result
            )
            Assert.assertEquals(
                SoundSpaceResponse.RESPONSE_RESULT_SUCCESS,
                async {
                    SoundEffectManager.testRequestAction(
                        GsonUtils.toJson(req5),
                        "soundCenterSoundEffect 5"
                    )
                }.await()?.result
            )
        }
    }

    /**
     * 播放整蛊音
     */
    @Test
    fun playPrankMusic() {
        val req1 = RequestSoaBaseBean(
            protocolId = SoundEffectManager.ProtocolType.PROTOCOL_ID_PLAY_PRANK_MUSIC,
            messageType = SoaConstants.MESSAGE_TYPE_REQUEST,
            data = PlayPrankMusicRequestData("34", "1")
        )
        val req2 = RequestSoaBaseBean(
            protocolId = 1029,
            messageType = SoaConstants.MESSAGE_TYPE_REQUEST,
            data = PlayPrankMusicRequestData("1", "1")
        )
        runBlocking {
            SoundEffectManager.testRequestAction(GsonUtils.toJson(req2), "获取整蛊音")
            Assert.assertEquals(
                SoundSpaceResponse.RESPONSE_RESULT_SUCCESS,
                async {
                    SoundEffectManager.testRequestAction(GsonUtils.toJson(req1), "playPrankMusic 1 1")
                }.await()?.result
            )
        }
    }

    /**
     * 设置声浪音量
     */
    @Test
    fun setVoiceVolume() {
        val req1 = RequestSoaBaseBean(
            protocolId = SoundEffectManager.ProtocolType.PROTOCOL_ID_VOICE_VOLUME,
            messageType = SoaConstants.MESSAGE_TYPE_REQUEST,
            data = VoiceVolumeRequestData("1")
        )
        runBlocking {
            Assert.assertEquals(
                SoundSpaceResponse.RESPONSE_RESULT_SUCCESS,
                async {
                    SoundEffectManager.testRequestAction(GsonUtils.toJson(req1), "setVoiceVolume 1")
                }.await()?.result
            )
        }
    }
}