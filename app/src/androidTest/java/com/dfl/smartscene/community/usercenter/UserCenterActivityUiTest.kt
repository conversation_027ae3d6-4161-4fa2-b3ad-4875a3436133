package com.dfl.smartscene.community.usercenter

import android.content.Intent
import android.os.Bundle
import androidx.test.core.app.ActivityScenario
import androidx.test.core.app.ApplicationProvider
import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.action.ViewActions
import androidx.test.espresso.assertion.ViewAssertions
import androidx.test.espresso.assertion.ViewAssertions.matches
import androidx.test.espresso.contrib.RecyclerViewActions
import androidx.test.espresso.matcher.ViewMatchers.*
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.android.commonlib.CommonUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.community.reponse.CommunitySceneInfo
import com.dfl.smartscene.bean.community.reponse.CommunityUserInfo
import com.iauto.scenarioadapter.ScenarioInfo
import org.hamcrest.Matchers.not
import org.junit.Assert.*
import org.junit.Test
import org.junit.runner.RunWith

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2023/11/16
 * desc :测试启动，空数据情况，跳转，布局
 * version: 1.2
 */
@RunWith(AndroidJUnit4::class)
class UserCenterActivityUiTest {
    /**
     *跳转到场景详情测试
     */
    @Test
    fun goToSceneDetailActivity() {
        val communityUserInfo = initCommunityUserInfo()
        ActivityScenario.launch<UserCenterActivity>(UserCenterActivity::class.java).use { scenario ->
            scenario.onActivity { activity ->
                activity.mViewModel.convertToUiState(communityUserInfo)
            }
            onView(withId(R.id.tv_title)).check(ViewAssertions.doesNotExist())
            onView(withId(R.id.tv_user_name)).check(matches(withText("追逐的少年")))
            onView(withId(R.id.rv_user_uploads_list)).perform(
                RecyclerViewActions.actionOnItemAtPosition<BaseViewHolder>(
                    4, ViewActions.click()
                )
            )
            onView(withId(R.id.tv_title)).check(matches(isDisplayed()))
        }
    }

    /**
     * 使用当前用户数据Intent启动
     */
    @Test
    fun startWithCurrentUserIntent() {
        val communityUserInfo = initCommunityUserInfo()
        val intent = Intent(ApplicationProvider.getApplicationContext(), UserCenterActivity::class.java)
        val bundle = Bundle()
        bundle.putString(UserCenterActivity.EXTRA_VISITED_USER_ID, communityUserInfo.sceneList[0].uuid)
        bundle.putInt(UserCenterActivity.EXTRA_IS_OFFICIAL, 0)
        intent.putExtra(UserCenterActivity.EXTRA_BUNDLE, bundle)
        ActivityScenario.launch<UserCenterActivity>(intent).use { scenario ->
            scenario.onActivity { activity ->
                activity.mViewModel.convertToUiState(communityUserInfo)
            }
            //判断 显示的是我的上传场景,不显示空上传场景
            onView(withId(R.id.tv_user_name)).check(matches(withText("追逐的少年")))
            onView(withId(R.id.tv_user_uploads_num)).check(
                matches(
                    withText(
                        CommonUtils.getString(R.string.scene_text_user_center_my_uploads_num) + " (" + communityUserInfo.userUploadNum + ")"
                    )
                )
            )
            onView(withId(R.id.group_empty_upload_show)).check(matches(not(isDisplayed())))
            scenario.onActivity { activity ->
                //rv子item间距判断
                val userInfoItem1 = activity.mViewBind.rvUserinfoList.layoutManager?.findViewByPosition(0)
                val userInfoItem2 = activity.mViewBind.rvUserinfoList.layoutManager?.findViewByPosition(1)
                if (userInfoItem1 != null && userInfoItem2 != null) {
                    assertEquals(
                        activity.resources.getDimension(R.dimen.scene_gap_user_center_userinfo),
                        userInfoItem2.x - userInfoItem1.x - userInfoItem1.width
                    )
                } else {
                    throw IllegalArgumentException("rv的item不应该为空")
                }
                val userUploadItem1 = activity.mViewBind.rvUserUploadsList.layoutManager?.findViewByPosition(0)
                val userUploadItem2 = activity.mViewBind.rvUserUploadsList.layoutManager?.findViewByPosition(1)
                val userUploadItem3 = activity.mViewBind.rvUserUploadsList.layoutManager?.findViewByPosition(3)
                if (userUploadItem1 != null && userUploadItem2 != null && userUploadItem3 != null) {
                    assertEquals(
                        activity.resources.getDimension(R.dimen.scene_column_gap_user_center_upload),
                        userUploadItem2.x - userUploadItem1.x - userUploadItem1.width
                    )
                    assertEquals(
                        activity.resources.getDimension(R.dimen.scene_row_gap_user_center_upload),
                        userUploadItem3.y - userUploadItem1.y - userUploadItem1.height
                    )
                } else {
                    throw IllegalArgumentException("rv的item不应该为空")
                }
            }
        }
    }

    /**
     * 使用其他用户数据intent启动
     */
    @Test
    fun startWithOtherUserIntent() {
        val communityUserInfo = initOtherCommunityUserInfo()
        val intent = Intent(ApplicationProvider.getApplicationContext(), UserCenterActivity::class.java)
        val bundle = Bundle()
        bundle.putString(UserCenterActivity.EXTRA_VISITED_USER_ID, communityUserInfo.sceneList[0].uuid)
        bundle.putInt(UserCenterActivity.EXTRA_IS_OFFICIAL, 0)
        intent.putExtra(UserCenterActivity.EXTRA_BUNDLE, bundle)
        ActivityScenario.launch<UserCenterActivity>(intent).use {
            it.onActivity { activity ->
                activity.mViewModel.convertToUiState(communityUserInfo)
            }
            onView(withId(R.id.tv_user_name)).check(matches(withText("单休也算休")))
            onView(withId(R.id.tv_user_uploads_num)).check(
                matches(
                    withText(
                        CommonUtils.getString(R.string.scene_text_user_center_other_uploads_num) + " (" + communityUserInfo.userUploadNum + ")"
                    )
                )
            )
        }
    }

    /**
     *使用空上传列表启动
     *
     */
    @Test
    fun startWithEmptyUploadIntent() {
        val communityUserInfo = initEmptyUploadUserInfo()
        val intent = Intent(ApplicationProvider.getApplicationContext(), UserCenterActivity::class.java)
        val bundle = Bundle()
        bundle.putString(UserCenterActivity.EXTRA_VISITED_USER_ID, "")
        bundle.putInt(UserCenterActivity.EXTRA_IS_OFFICIAL, 0)
        intent.putExtra(UserCenterActivity.EXTRA_BUNDLE, bundle)
        ActivityScenario.launch<UserCenterActivity>(intent).use {
            it.onActivity { activity ->
                activity.mViewModel.convertToUiState(communityUserInfo)
            }
            onView(withId(R.id.tv_user_name)).check(matches(withText("我一个场景不上传")))
            onView(withId(R.id.iv_upload_empty)).check(matches(isDisplayed()))
            onView(withId(R.id.rv_user_uploads_list)).check(matches(not(isDisplayed())))
            onView(withId(R.id.tv_user_uploads_num)).check(
                matches(
                    withText(
                        CommonUtils.getString(R.string.scene_text_user_center_other_uploads_num) + " (0)"
                    )
                )
            )
        }
    }

    private fun initIntent(isOfficial: Int, visitedUserId: String): Intent {
        val bundle = Bundle()
        val intent = Intent(ApplicationProvider.getApplicationContext(), UserCenterActivity::class.java)
        bundle.putInt(UserCenterActivity.EXTRA_IS_OFFICIAL, isOfficial)
        bundle.putString(UserCenterActivity.EXTRA_VISITED_USER_ID, visitedUserId)
        intent.putExtra(UserCenterActivity.EXTRA_BUNDLE, bundle)
        return intent
    }

    private fun initCommunityUserInfo(): CommunityUserInfo {
        val sceneInfo = ScenarioInfo(
            "userid_123", "开心周末", "每周六周日驾驶时，播放音乐", 0, 0, 0, 0L, null, null, null, null
        )
        val list = CommunitySceneInfo.getBannerList(4)
        return CommunityUserInfo(
            "https://img2.baidu.com/it/u=**********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500",
            2,
            999,
            111,
            "999",
            "追逐的少年",
            23,
            list.size,
            list
        )
    }

    private fun initEmptyUploadUserInfo(): CommunityUserInfo {
        return CommunityUserInfo(
            "https://img2.baidu.com/it/u=**********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500",
            2,
            999,
            111,
            "999",
            "追逐的少年",
            23,
            0,
            emptyList()
        )
    }

    private fun initOtherCommunityUserInfo(): CommunityUserInfo {
        val list = CommunitySceneInfo.getBannerList(4)
        return CommunityUserInfo(
            "https://img2.baidu.com/it/u=**********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500",
            0,
            999,
            111,
            "999",
            "追逐的少年",
            23,
            list.size,
            list
        )
    }
}