<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.dfl.smartscene">

    <application
        android:name=".application.SceneApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/scene_icon_launcher"
        android:label="@string/scene_app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/Theme.SceneControl"
        tools:replace="android:label"
        tools:targetApi="s">
        <activity
            android:name=".community.search.SearchActivity"
            android:configChanges="uiMode|keyboard|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="landscape"
            android:taskAffinity="com.dfl.smartscene" />
        <activity
            android:name=".community.usercenter.UserCenterActivity"
            android:configChanges="uiMode|keyboard|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="landscape"
            android:taskAffinity="com.dfl.smartscene" />
        <activity
            android:name=".ui.splash.SplashActivity"
            android:configChanges="uiMode|keyboard|keyboardHidden|orientation"
            android:exported="true"
            android:launchMode="standard"
            android:taskAffinity="com.dfl.smartscene"
            android:theme="@style/Theme.SceneControl.SplashTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ui.main.MainActivity"
            android:configChanges="uiMode|keyboard|keyboardHidden|orientation"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="landscape"
            android:taskAffinity="com.dfl.smartscene" />
        <activity
            android:name=".ui.main.discover.detail.SceneDetailActivity"
            android:configChanges="uiMode|keyboard|keyboardHidden|orientation"
            android:screenOrientation="landscape"
            android:taskAffinity="com.dfl.smartscene" />
        <activity
            android:name=".ui.edit.SceneNewEditActivity"
            android:configChanges="uiMode|keyboard|keyboardHidden|orientation"
            android:launchMode="singleTask"
            android:screenOrientation="landscape"
            android:taskAffinity="com.dfl.smartscene"
            android:theme="@style/Theme.SceneControl.Translucent" />

        <service
            android:name=".service.SceneWidgetService"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.dfl.smartscene.sceneWidgetService" />
                <action android:name="com.dfl.smartscene.changeDefaultVin" />
            </intent-filter>
        </service>

        <service
            android:name=".service.SmartSceneProtocolService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.dfl.smartscene.actionProtocol" />
            </intent-filter>
        </service>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.common.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/common_provider_paths" />
        </provider>

        <receiver
            android:name=".receiver.LauncherBroadcastReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.dfl.launcher.smartScene" />
            </intent-filter>
        </receiver>


        <meta-data
            android:name="design_width_in_dp"
            android:value="1920" />
        <meta-data
            android:name="design_height_in_dp"
            android:value="720" />
    </application>

    <!--  避免tts引擎无法绑定的问题-->
    <queries>
        <intent>
            <action android:name="android.intent.action.TTS_SERVICE" />
        </intent>
    </queries>
</manifest>