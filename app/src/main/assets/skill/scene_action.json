[{"category": "延时", "desc": "#second#秒", "input": [{"name": "second", "type": "uint32", "desc": "range:[0,86399]", "method": "range", "value": "0_86399", "valueMap": {}}], "permission": "USER", "skillId": "0xFF07", "skillValue": "action.common.Delay", "type": "ACTION", "config": "1"}, {"category": "播放控制", "desc": "#type#", "input": [{"name": "type", "type": "int", "desc": "enum:上一首 : 1,下一首 : 2", "method": "enum", "value": "1_2", "valueMap": {"1": "上一首", "2": "下一首"}}], "permission": "USER", "skillId": "0x7", "skillValue": "action.media.SkipControl", "type": "ACTION", "config": "1"}, {"category": "关闭多媒体", "desc": "关闭多媒体", "input": [], "permission": "USER", "skillId": "0x29", "skillValue": "action.media.CloseControl", "type": "ACTION", "config": "1"}, {"category": "播放内置音源", "desc": "#soundSource#", "input": [{"name": "soundSource", "type": "int", "desc": "enum:音乐 : 1,听书 : 2,电台 : 3,USB音乐 : 4,蓝牙 : 5,KTV : 6,USB视频 : 7", "method": "enum", "value": "1_2_3_4_5_6_7", "valueMap": {"1": "音乐", "2": "听书", "3": "电台", "4": "USB音乐", "5": "蓝牙", "6": "KTV", "7": "USB视频"}}], "permission": "USER", "skillId": "0x2d", "skillValue": "action.media.PlayMedia", "type": "ACTION", "config": "1"}, {"category": "播放控制", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:暂停:0,播放:1 ", "method": "enum", "value": "0_1", "valueMap": {"0": "暂停", "1": "播放"}}], "permission": "USER", "skillId": "0x31", "skillValue": "action.media.PlayControl", "type": "ACTION", "config": "1"}, {"category": "动感声浪模式", "desc": "#roar#", "input": [{"name": "roar", "type": "int", "desc": "enum:GTR R35 : 1,<PERSON><PERSON><PERSON> Z : 2,赛博朋克 : 3", "method": "enum", "value": "1_2_3", "valueMap": {"1": "GTR R35", "2": "Fairlady Z", "3": "赛博朋克"}}], "permission": "USER", "skillId": "0x3f", "skillValue": "action.soundspace.EnginesRoarEffect", "type": "ACTION", "config": "1"}, {"category": "播放多媒体", "desc": "#app#", "input": [{"name": "app", "type": "int", "desc": "enum:qq音乐 : 1,蓝牙音乐 : 2,usb音乐 : 3", "method": "enum", "value": "1_2_3", "valueMap": {"1": "qq音乐", "2": "蓝牙音乐", "3": "usb音乐"}}], "permission": "USER", "skillId": "0x50", "skillValue": "action.media.PlayMusic", "type": "ACTION", "config": "1"}, {"category": "播放QQ音乐", "desc": "#mood#心情", "input": [{"name": "mood", "type": "int", "desc": "enum: 快乐:1,伤感 : 2,安静 : 3,励志 : 4,治愈 : 5,思念 : 6,甜蜜 : 7,寂寞 : 8 , 宣泄 : 9", "method": "enum", "value": "1_2_3_4_5_6_7_8_9", "valueMap": {"1": "快乐", "2": "伤感", "3": "安静", "4": "励志", "5": "治愈", "6": "思念", "7": "甜蜜", "8": "寂寞", "9": "宣泄"}}], "permission": "USER", "skillId": "0x51", "skillValue": "action.media.PlaySpecificMood", "type": "ACTION", "config": "1"}, {"category": "播放QQ音乐", "desc": "#era#", "input": [{"name": "era", "type": "int", "desc": "enum: 00年代:1, 90年代 : 2, 80年代 : 3, 70年代 : 4", "method": "enum", "value": "1_2_3_4", "valueMap": {"1": "00年代", "2": "90年代", "3": "80年代", "4": "70年代"}}], "permission": "USER", "skillId": "0x52", "skillValue": "action.media.PlaySpecificEra", "type": "ACTION", "config": "1"}, {"category": "播放QQ音乐", "desc": "#genre#曲风", "input": [{"name": "genre", "type": "int", "desc": "enum:流行:1,中国风:2,民谣:3,摇滚:4,轻音乐:5,电子:6,爵士:7,乡村:8", "method": "enum", "value": "1_2_3_4_5_6_7_8", "valueMap": {"1": "流行", "2": "中国风", "3": "民谣", "4": "摇滚", "5": "轻音乐", "6": "电子", "7": "爵士", "8": "乡村"}}], "permission": "USER", "skillId": "0x53", "skillValue": "action.media.PlaySpecificGenre", "type": "ACTION", "config": "1"}, {"category": "播放QQ音乐", "desc": "#type#歌单", "input": [{"name": "type", "type": "int", "desc": "enum:最近播放:1,我喜欢:2,下载歌曲:3,每日推荐:4,新歌速递:5,热门儿歌:6", "method": "enum", "value": "1_2_3_4_5_6", "valueMap": {"1": "最近播放", "2": "我喜欢", "3": "下载歌曲", "4": "每日推荐", "5": "新歌速递", "6": "热门儿歌"}}], "permission": "USER", "skillId": "0x54", "skillValue": "action.media.PlaySongList", "type": "ACTION", "config": "1"}, {"category": "播放QQ音乐", "desc": "#name#", "input": [{"name": "name", "type": "string", "desc": "searchSong", "method": "third<PERSON><PERSON>y", "value": "", "valueMap": {}}], "permission": "USER", "skillId": "0x55", "skillValue": "action.media.PlaySong", "type": "ACTION", "config": "1", "params": [{"index": 0, "arg": "歌曲名称（搜索默认选中第一个）"}]}, {"category": "播放模式", "desc": "#mode#", "input": [{"name": "mode", "type": "int", "desc": "enum:顺序播放 : 1,随机播放 : 2,单曲循环 : 3", "method": "enum", "value": "1_2_3", "valueMap": {"1": "顺序播放", "2": "随机播放", "3": "单曲循环"}}], "permission": "USER", "skillId": "0x56", "skillValue": "action.media.MusicPlayMode", "type": "ACTION", "config": "1"}, {"category": "播放QQ音乐", "desc": "#singer#", "input": [{"name": "singer", "type": "string", "desc": "searchSinger", "method": "third<PERSON><PERSON>y", "value": "", "valueMap": {}}], "permission": "USER", "skillId": "0x57", "skillValue": "action.media.PlaySingerSong", "type": "ACTION", "config": "1", "params": [{"index": 0, "arg": "歌手名称（搜索默认选中第一个）"}]}, {"category": "播放模式", "desc": "#mode#", "input": [{"name": "mode", "type": "int", "desc": "enum:列表循环 : 1,随机播放 : 2,单曲循环 : 3", "method": "enum", "value": "1_2_3", "valueMap": {"1": "列表循环", "2": "随机播放", "3": "单曲循环"}}], "permission": "USER", "skillId": "0x5b", "skillValue": "action.media.USBPlayMode", "type": "ACTION", "config": "1"}, {"category": "发起导航", "desc": "#favoriteType#", "input": [{"name": "favoriteType", "type": "int", "desc": "enum:回家:1,回公司:2", "method": "enum", "value": "1_2", "valueMap": {"1": "回家", "2": "回公司"}}], "permission": "USER", "skillId": "0x62", "skillValue": "action.navi.NaviToFavorite", "type": "ACTION", "config": "1"}, {"category": "导航路线偏好", "desc": "#strategy#", "input": [{"name": "strategy", "type": "int", "desc": "enum:高德推荐 : 0,躲避拥堵 : 2,避免收费 : 4,不走高速 : 8,高速优先 : 16,速度最快 : 32,大路优先 : 64", "method": "enum", "value": "0_2_4_8_16_32_64", "valueMap": {"0": "高德推荐", "2": "躲避拥堵", "4": "避免收费", "8": "不走高速", "16": "高速优先", "32": "速度最快", "64": "大路优先"}}], "permission": "USER", "skillId": "0x63", "skillValue": "action.navi.SetRouteStrategy", "type": "ACTION", "config": "1"}, {"category": "发起导航", "desc": "#json#", "input": [{"name": "json", "type": "string", "desc": "searchAddress", "method": "third<PERSON><PERSON>y", "value": "", "valueMap": {}}], "permission": "USER", "skillId": "0x64", "skillValue": "action.navi.NaviRequest", "type": "ACTION", "config": "1", "params": [{"index": 0, "arg": "目的地名称（搜索默认选中第一个）"}, {"index": 1, "arg": "途径点名称（搜索默认选中第一个）"}]}, {"category": "趣味彩蛋", "desc": "#position# #sound#", "input": [{"name": "position", "type": "int", "desc": "enum:主驾 : 1, 副驾 : 2, 左后 : 3, 右后:4", "method": "enum", "value": "1_2_3_4", "valueMap": {"1": "主驾", "2": "副驾", "3": "左后", "4": "右后"}}, {"name": "sound", "type": "int", "desc": "enum: 奶萌小猫:1, 调皮鹦鹉:2,灵动海豚:3,猫猫祟祟:4,奇幻夏日:5, 绿茵狂欢:6", "method": "enum", "value": "1_2_3_4_5_6", "valueMap": {"1": "奶萌小猫", "2": "调皮鹦鹉", "3": "灵动海豚", "4": "猫猫祟祟", "5": "奇幻夏日", "6": "绿茵狂欢"}}], "permission": "USER", "skillId": "0x67", "skillValue": "action.soundspace.PlayTricksterSound", "type": "ACTION", "config": "1"}, {"category": "小尼回复", "desc": "#content#", "input": [{"name": "content", "type": "string", "desc": "range:", "method": "value2Desc", "value": "", "valueMap": {}}], "permission": "USER", "skillId": "0x69", "skillValue": "action.voice.PlaySpeechTTS", "type": "ACTION", "config": "1"}, {"category": "关闭应用", "desc": "关闭应用", "input": [], "permission": "USER", "skillId": "0x6a", "skillValue": "action.launcher.BackHome", "type": "ACTION", "config": "1"}, {"category": "天气播报", "desc": "天气播报", "input": [], "permission": "USER", "skillId": "0x71", "skillValue": "action.weather.PlayWeatherInfo", "type": "ACTION", "config": "1"}, {"category": "智能预热", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:关闭:0,打开:1", "method": "enum", "value": "0_1", "valueMap": {"0": "关闭", "1": "打开"}}], "permission": "USER", "skillId": "0x72", "skillValue": "action.energy.InRoadPreheatingSwitch", "type": "ACTION", "config": "1"}, {"category": "对外自由供电", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:关闭:0,打开:1", "method": "enum", "value": "0_1", "valueMap": {"0": "关闭", "1": "打开"}}], "permission": "USER", "skillId": "0x73", "skillValue": "action.energy.DischargeSwitch", "type": "ACTION", "config": "1"}, {"category": "消息通知", "desc": "#message#", "input": [{"name": "message", "type": "string", "desc": "enum:", "method": "value2Desc", "value": "", "valueMap": {}}], "permission": "USER", "skillId": "0x74", "skillValue": "action.message.ShowMessage", "type": "ACTION", "config": "1"}, {"category": "行程播报", "desc": "行程播报", "input": [{"name": "journeyInfo", "type": "string", "desc": "", "method": "", "value": "", "valueMap": {}}], "permission": "USER", "skillId": "0x75", "skillValue": "action.user.PlayJourneyInfo", "type": "ACTION", "config": "1"}, {"category": "#status#", "desc": "#type#", "input": [{"name": "status", "type": "int", "desc": "enum:关闭 : 0,打开应用 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "关闭", "1": "打开应用"}}, {"name": "type", "type": "int", "desc": "enum: 车主指南:1 , 蓝牙电话:2 ,   天气:3,  消息中心:4, 能源中心:5,  小尼助手:6, 车辆控制:7 ,空调:8, 蓝牙音乐:12,  USB音视频:13,   系统设置:14, 声音空间:15,  应用中心:16", "method": "enum", "value": "1_2_3_4_5_6_7_8_12_13_14_15_16", "valueMap": {"1": "车主指南", "2": "蓝牙电话", "3": "天气", "4": "消息中心", "5": "能源中心", "6": "小尼助手", "7": "车辆控制", "8": "空调", "12": "蓝牙音乐", "13": "USB音视频", "14": "系统设置", "15": "声音空间", "16": "应用中心"}}], "permission": "USER", "skillId": "0x76", "skillValue": "action.launcher.OpenApplication", "type": "ACTION", "config": "1"}, {"category": "发起导航", "desc": "#type#", "input": [{"name": "type", "type": "int", "desc": "enum:去充电:1", "method": "enum", "value": "1", "valueMap": {"1": "去充电"}}], "permission": "USER", "skillId": "0x81", "skillValue": "action.navi.NaviToFavoriteOther", "type": "ACTION", "config": "1"}, {"category": "空调开关", "desc": "#hvacswstate#", "input": [{"name": "hvacswstate", "type": "int", "desc": "enum:打开:0,关闭:1", "method": "enum", "value": "0_1", "valueMap": {"0": "打开", "1": "关闭"}}], "permission": "USER", "skillId": "0x100", "skillValue": "action.ac.ACSwitch", "type": "ACTION", "config": "1"}, {"category": "空调温度", "desc": "#temp#℃", "input": [{"name": "temp", "type": "double", "desc": "range:[16,32]", "method": "range", "value": "16_32", "valueMap": {}}], "permission": "USER", "skillId": "0x102", "skillValue": "action.ac.ACTemperature;action.ac.ACTemperatureRF;action.ac.ACTemperatureLF", "type": "ACTION", "config": "6"}, {"category": "空调风量", "desc": "#speedgear#挡", "input": [{"name": "speedgear", "type": "int", "desc": "range:[0,7]", "method": "range", "value": "0_7", "valueMap": {}}], "permission": "USER", "skillId": "0x103", "skillValue": "action.ac.FanSpeedLevel", "type": "ACTION", "config": "1"}, {"category": "吹风模式", "desc": "#blowmode#", "input": [{"name": "blowmode", "type": "int", "desc": "enum:吹面 : 0,吹面吹脚 : 1,吹脚 : 2,吹脚除霜 : 3", "method": "enum", "value": "0_1_2_3", "valueMap": {"0": "吹面", "1": "吹面吹脚", "2": "吹脚", "3": "吹脚除霜"}}], "permission": "USER", "skillId": "0x104", "skillValue": "action.ac.FanMode", "type": "ACTION", "config": "1"}, {"category": "前除雾", "desc": "#defrostswState#", "input": [{"name": "defrostswState", "type": "int", "desc": "enum:打开:0,关闭:1", "method": "enum", "value": "0_1", "valueMap": {"0": "打开", "1": "关闭"}}], "permission": "USER", "skillId": "0x105", "skillValue": "action.ac.FrontDefrostSwitch", "type": "ACTION", "config": "1"}, {"category": "后除雾", "desc": "#RedefrostSwstate#", "input": [{"name": "RedefrostSwstate", "type": "int", "desc": "enum:打开:0,关闭:1", "method": "enum", "value": "0_1", "valueMap": {"0": "打开", "1": "关闭"}}], "permission": "USER", "skillId": "0x107", "skillValue": "action.ac.RearDefrostSwitch", "type": "ACTION", "config": "1"}, {"category": "循环模式", "desc": "#recirc#", "input": [{"name": "recirc", "type": "int", "desc": "enum:内循环 :0,外循环 : 1,智能循环 : 2", "method": "enum", "value": "0_1_2", "valueMap": {"0": "内循环", "1": "外循环", "2": "智能循环"}}], "permission": "USER", "skillId": "0x109", "skillValue": "action.ac.CycleMode", "type": "ACTION", "config": "1"}, {"category": "AUTO", "desc": "#AutoSwstate#", "input": [{"name": "AutoSwstate", "type": "int", "desc": "enum:打开:0,关闭:1", "method": "enum", "value": "0_1", "valueMap": {"0": "打开", "1": "关闭"}}], "permission": "USER", "skillId": "0x10c", "skillValue": "action.ac.AutoMode", "type": "ACTION", "config": "1"}, {"category": "冷热", "desc": "#ACSwstate#", "input": [{"name": "ACSwstate", "type": "int", "desc": "enum:打开:0,关闭:1", "method": "enum", "value": "0_1", "valueMap": {"0": "打开", "1": "关闭"}}], "permission": "USER", "skillId": "0x10e", "skillValue": "action.ac.CompressorSwitch", "type": "ACTION", "config": "1"}, {"category": "送风", "desc": "#fanonlySW#", "input": [{"name": "fanonlySW", "type": "int", "desc": "enum:打开:0,关闭:1", "method": "enum", "value": "0_1", "valueMap": {"0": "打开", "1": "关闭"}}], "permission": "USER", "skillId": "0x110", "skillValue": "action.ac.FanOnlySwitch", "type": "ACTION", "config": "1"}, {"category": "极速制热", "desc": "#AirMaxHeatSw#", "input": [{"name": "AirMaxHeatSw", "type": "int", "desc": "enum:打开: 0,关闭 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "打开", "1": "关闭"}}], "permission": "USER", "skillId": "0x160", "skillValue": "action.ac.QuickHeatingMode", "type": "ACTION", "config": "1"}, {"category": "极速制冷", "desc": "#AirMaxCoolSw#", "input": [{"name": "AirMaxCoolSw", "type": "int", "desc": "enum:打开: 0,关闭 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "打开", "1": "关闭"}}], "permission": "USER", "skillId": "0x161", "skillValue": "action.ac.QuickCoolingMode", "type": "ACTION", "config": "1"}, {"category": "空调温度", "desc": "主驾 #temp#℃", "input": [{"name": "temp", "type": "double", "desc": "range:[16,32]", "method": "range", "value": "16_32", "valueMap": {}}], "permission": "USER", "skillId": "0x166", "skillValue": "action.ac.ACTemperature;action.ac.ACTemperatureLF", "type": "ACTION", "config": "2"}, {"category": "副驾温度", "desc": "副驾 #temp#℃", "input": [{"name": "temp", "type": "double", "desc": "range:[16,32]", "method": "range", "value": "16_32", "valueMap": {}}], "permission": "USER", "skillId": "0x167", "skillValue": "action.ac.ACTemperatureRF", "type": "ACTION", "config": "2"}, {"category": "自干燥模式", "desc": "#AirDelayBlowtSw#", "input": [{"name": "AirDelayBlowtSw", "type": "int", "desc": "enum:打开:0,关闭:1", "method": "enum", "value": "0_1", "valueMap": {"0": "打开", "1": "关闭"}}], "permission": "USER", "skillId": "0x168", "skillValue": "action.ac.DryModeSwitch", "type": "ACTION", "config": "1"}, {"category": "节能模式", "desc": "#AirECOModetSw#", "input": [{"name": "AirECOModetSw", "type": "int", "desc": "enum:打开:0,关闭:1", "method": "enum", "value": "0_1", "valueMap": {"0": "打开", "1": "关闭"}}], "permission": "USER", "skillId": "0x169", "skillValue": "action.ac.ECOModeSwitch", "type": "ACTION", "config": "1"}, {"category": "冰箱开关", "desc": "#ICESw#", "input": [{"name": "ICESw", "type": "int", "desc": "enum:打开:0,关闭:1", "method": "enum", "value": "0_1", "valueMap": {"0": "打开", "1": "关闭"}}], "permission": "USER", "skillId": "0x16a", "skillValue": "action.ac.FridgeSwitch", "type": "ACTION", "config": "4"}, {"category": "冰箱模式冷热模式", "desc": "#ICEcoolheatstatus#", "input": [{"name": "ICEcoolheatstatus", "type": "int", "desc": "enum:制冷:1,制热:2", "method": "enum", "value": "1_2", "valueMap": {"1": "制冷", "2": "制热"}}], "permission": "USER", "skillId": "0x16b", "skillValue": "action.ac.FridgeMode", "type": "ACTION", "config": "4"}, {"category": "冰箱制冷温度调节", "desc": "#temp#℃", "input": [{"name": "temp", "type": "int", "desc": "range:[-6,10]", "method": "range", "value": "-6_10", "valueMap": {}}], "permission": "USER", "skillId": "0x16c", "skillValue": "action.ac.FridgeCoolingTemp", "type": "ACTION", "config": "4"}, {"category": "冰箱制热温度调节", "desc": "#temp#℃", "input": [{"name": "temp", "type": "int", "desc": "range:[35,55]", "method": "range", "value": "35_55", "valueMap": {}}], "permission": "USER", "skillId": "0x16d", "skillValue": "action.ac.FridgeHeatingTemp", "type": "ACTION", "config": "4"}, {"category": "同步", "desc": "#ACSyncModeSw#", "input": [{"name": "ACSyncModeSw", "type": "uint8", "desc": "enum:打开:0,关闭:1", "method": "enum", "value": "0_1", "valueMap": {"0": "打开", "1": "关闭"}}], "permission": "USER", "skillId": "0x16f", "skillValue": "action.ac.ACSyncMode", "type": "ACTION", "config": "2"}, {"category": "冰箱快捷场景", "desc": "#mode#", "input": [{"name": "mode", "type": "uint8", "desc": "enum:关闭:0,快速冷藏:1,饮品冰镇:2,蔬果解封:3,快速加热:4,热奶保温:5", "method": "enum", "value": "0_1_2_3_4_5", "valueMap": {"0": "关闭", "1": "快速冷藏", "2": "饮品冰镇", "3": "蔬果解封", "4": "快速加热", "5": "热奶保温"}}], "permission": "USER", "skillId": "0x170", "skillValue": "action.ac.FridgeConvenientMode", "type": "ACTION", "config": "4"}, {"category": "离车持续工作打开", "desc": "#time#小时", "input": [{"name": "time", "type": "uint8", "desc": "range:[1,24]", "method": "range", "value": "1_24", "valueMap": {}}], "permission": "USER", "skillId": "0x171", "skillValue": "action.ac.FridgeDepartureWorkingTime", "type": "ACTION", "config": "4"}, {"category": "离车持续工作", "desc": "#switch#", "input": [{"name": "switch", "type": "uint8", "desc": "enum:关闭:1", "method": "enum", "value": "1", "valueMap": {"1": "关闭"}}], "permission": "USER", "skillId": "0x172", "skillValue": "action.ac.FridgeDepartureWorkingOff", "type": "ACTION", "config": "4"}, {"category": "节能模式", "desc": "#mode#", "input": [{"name": "mode", "type": "uint8", "desc": "enum:打开:0,关闭:1", "method": "enum", "value": "0_1", "valueMap": {"0": "打开", "1": "关闭"}}], "permission": "USER", "skillId": "0x173", "skillValue": "action.ac.FridgeECOMode", "type": "ACTION", "config": "4"}, {"category": "氛围灯功能联动", "desc": "#mode# #status#", "input": [{"name": "mode", "type": "int", "desc": "enum:语音唤醒 : 1,人声联动 : 2, 驾驶模式切换 : 3,空调升降温 : 4,  开门预警 : 5，上电启动 : 6", "method": "enum", "value": "1_2_3_4_5_6", "valueMap": {"1": "语音唤醒", "2": "人声联动", "3": "驾驶模式切换", "4": "空调升降温", "5": "开门预警", "6": "上电启动"}}, {"name": "status", "type": "int", "desc": "enum:关闭 : 0,打开 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "关闭", "1": "打开"}}], "permission": "USER", "skillId": "0x202", "skillValue": "action.light.AtmosphereLightSceneEffect", "type": "ACTION", "config": "2"}, {"category": "大灯控制", "desc": "#lampMode#", "input": [{"name": "lampMode", "type": "int", "desc": "enum:关闭 : 0,自动 : 1,位置灯 : 2,近光灯 : 3", "method": "enum", "value": "0_1_2_3", "valueMap": {"0": "关闭", "1": "自动", "2": "位置灯", "3": "近光灯"}}], "permission": "USER", "skillId": "0x205", "skillValue": "action.light.HeadLightMode", "type": "ACTION", "config": "1"}, {"category": "后雾灯", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:关闭 : 00,打开 : 01", "method": "enum", "value": "00_01", "valueMap": {"00": "关闭", "01": "打开"}}], "permission": "USER", "skillId": "0x206", "skillValue": "action.light.FogLightSwitch", "type": "ACTION", "config": "1"}, {"category": "大灯高度", "desc": "#frLampLevel#", "input": [{"name": "frLampLevel", "type": "int", "desc": "enum:低 : 4,中 : 3,标准 : 2,高 : 1", "method": "enum", "value": "4_3_2_1", "valueMap": {"4": "低", "3": "中", "2": "标准", "1": "高"}}], "permission": "USER", "skillId": "0x207", "skillValue": "action.light.HeadLightLevel", "type": "ACTION", "config": "1"}, {"category": "尾灯自定义灯语", "desc": "#lampEffect#", "input": [{"name": "lampEffect", "type": "int", "desc": "enum: 灯语1 : 27 , 灯语2 : 28", "method": "enum", "value": "27_28", "valueMap": {"27": "灯语1", "28": "灯语2"}}], "permission": "USER", "skillId": "0x209", "skillValue": "action.light.RearLampSignalEffect", "type": "ACTION", "config": "3"}, {"category": "氛围灯颜色", "desc": "#themeId#", "input": [{"name": "themeId", "type": "int", "desc": "enum:暖阳 : 1,海风 : 2,星光 : 3,森林: 4,白昼 : 5,晚风 : 6", "method": "enum", "value": "1_2_3_4_5_6", "valueMap": {"1": "暖阳", "2": "海风", "3": "星光", "4": "森林", "5": "白昼", "6": "晚风"}}], "permission": "USER", "skillId": "0x217", "skillValue": "action.light.AtmosphereLightTheme", "type": "ACTION", "config": "2"}, {"category": "氛围灯开关", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:关闭 : 0,打开 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "关闭", "1": "打开"}}], "permission": "USER", "skillId": "0x218", "skillValue": "action.light.AtmosphereLightSwitch", "type": "ACTION", "config": "2"}, {"category": "氛围灯模式", "desc": "#atmosphereLightMode#", "input": [{"name": "atmosphereLightMode", "type": "int", "desc": "enum:关闭:0,常亮:1,呼吸:2,音乐律动:3,声浪:4", "method": "enum", "value": "0_1_2_3_4", "valueMap": {"0": "关闭", "1": "常亮", "2": "呼吸", "3": "音乐律动", "4": "声浪"}}], "permission": "USER", "skillId": "0x219", "skillValue": "action.light.AtmosphereLightMode", "type": "ACTION", "config": "2"}, {"category": "氛围灯亮度", "desc": "#brightness#级", "input": [{"name": "brightness", "type": "int", "desc": "range:[0,7] desc需要加1", "method": "enum", "value": "0_7", "valueMap": {"0": "1", "1": "2", "2": "3", "3": "4", "4": "5", "5": "6", "6": "7", "7": "8"}}], "permission": "USER", "skillId": "0x21a", "skillValue": "action.light.AtmosphereLightBriLevel", "type": "ACTION", "config": "2"}, {"category": "氛围灯颜色", "desc": "自定义", "input": [{"name": "color", "type": "int", "desc": "range:", "method": "value2Desc", "value": "", "valueMap": {}}], "permission": "USER", "skillId": "0x21b", "skillValue": "action.light.AtmosphereLightColor", "type": "ACTION", "config": "2"}, {"category": "大灯打招呼", "desc": "#type#", "input": [{"name": "type", "type": "int", "desc": "enum: <PERSON> : 1, <PERSON> : 2,  叮叮 : 3, 喵喵: 4, 钵 : 5", "method": "enum", "value": "1_2_3_4_5", "valueMap": {"1": "Nissan", "2": "Hi", "3": "叮叮", "4": "喵喵", "5": "钵"}}], "permission": "USER", "skillId": "0x220", "skillValue": "action.light.HeadLightWelcomeMode", "type": "ACTION", "config": "1"}, {"category": "大灯功能联动", "desc": "#sceneID# #sceneEffectSetSts#", "input": [{"name": "sceneID", "type": "int", "desc": "enum: 迎宾解锁 : 1, 欢送上锁 : 2,  快速充电 : 3, 慢速充电: 4, 久停提醒 : 5, 短停驻车 : 6,  行李厢提醒 : 7", "method": "enum", "value": "1_2_3_4_5_6_7", "valueMap": {"1": "迎宾解锁", "2": "欢送上锁", "3": "快速充电", "4": "慢速充电", "5": "久停提醒", "6": "短停驻车", "7": "行李厢提醒"}}, {"name": "sceneEffectSetSts", "type": "int", "desc": "enum: 打开 : 1 , 关闭 : 2", "method": "enum", "value": "1_2", "valueMap": {"1": "打开", "2": "关闭"}}], "permission": "USER", "skillId": "0x221", "skillValue": "action.light.HeadLightSceneEffect", "type": "ACTION", "config": "1"}, {"category": "大灯灯光秀", "desc": "#type# #status#", "input": [{"name": "type", "type": "int", "desc": "enum: 春节主题 : 1, 元旦主题 : 2,  生日快乐 : 3,  <PERSON>主题: 4,  浪漫主题: 5", "method": "enum", "value": "1_2_3_4_5", "valueMap": {"1": "春节主题", "2": "元旦主题", "3": "生日快乐", "4": "Nissan主题", "5": "浪漫主题"}}, {"name": "status", "type": "int", "desc": "enum:   停止 : 0 ,播放 : 1 ", "method": "enum", "value": "0_1", "valueMap": {"0": "停止", "1": "播放"}}], "permission": "USER", "skillId": "0x223", "skillValue": "action.light.HeadLightSOASignal", "type": "ACTION", "config": "1"}, {"category": "阅读灯开关", "desc": "#lightMode#", "input": [{"name": "lightMode", "type": "int", "desc": "enum: 全关 : 0 , 全开 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "全关", "1": "全开"}}], "permission": "USER", "skillId": "0x264", "skillValue": "action.light.InternalLightSwitch", "type": "ACTION", "config": "1"}, {"category": "阅读灯调节", "desc": "主题 #theme#", "input": [{"name": "theme", "type": "int", "desc": "enum: 护眼 : 1 , 舒适 : 2, 清冷 : 3, 清亮 : 4", "method": "enum", "value": "1_2_3_4", "valueMap": {"1": "护眼", "2": "舒适", "3": "清冷", "4": "清亮"}}], "permission": "USER", "skillId": "0x265", "skillValue": "action.light.InternalLightTheme", "type": "ACTION", "config": "1"}, {"category": "星环灯", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum: 关闭 : 0 , 打开 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "关闭", "1": "打开"}}], "permission": "USER", "skillId": "0x266", "skillValue": "action.light.StarRingLightStatus", "type": "ACTION", "config": "1"}, {"category": "阅读灯调节", "desc": "亮度 #brightness#% 色温 #colour#%", "input": [{"name": "brightness", "type": "int", "desc": "range:[1,100]", "method": "range", "value": "1_100", "valueMap": {}}, {"name": "colour", "type": "int", "desc": "range:[1,100]", "method": "range", "value": "1_100", "valueMap": {}}], "permission": "USER", "skillId": "0x267", "skillValue": "action.light.InsideLampCustomized", "type": "ACTION", "config": "1"}, {"category": "大灯音乐律动", "desc": "#status# #effect#", "input": [{"name": "status", "type": "uint8", "desc": "enum: 打开 : 1 , 关闭 : 2", "method": "enum", "value": "1_2", "valueMap": {"1": "打开", "2": "关闭"}}, {"name": "effect", "type": "uint8", "desc": "enum: 鼓点 : 1 , 频谱 : 2", "method": "enum", "value": "1_2", "valueMap": {"1": "鼓点", "2": "频谱"}}], "permission": "USER", "skillId": "0x271", "skillValue": "action.light.HeadLightMusicLamp", "type": "ACTION", "config": "1"}, {"category": "主驾车窗", "desc": "#level#", "input": [{"name": "level", "type": "int", "desc": "enum:全开:0,关闭10%:1,关闭20%:2,关闭30%:3,关闭40%:4,关闭50%:5,关闭60%:6,关闭70%:7,关闭80%:8,关闭90%:9,全关:10", "method": "enum", "value": "0_1_2_3_4_5_6_7_8_9_10", "valueMap": {"0": "全开", "1": "关闭10%", "2": "关闭20%", "3": "关闭30%", "4": "关闭40%", "5": "关闭50%", "6": "关闭60%", "7": "关闭70%", "8": "关闭80%", "9": "关闭90%", "10": "全关"}}], "permission": "USER", "skillId": "0x300", "skillValue": "action.window.LFWindowPosition", "type": "ACTION", "config": "1"}, {"category": "副驾车窗", "desc": "#level#", "input": [{"name": "level", "type": "int", "desc": "enum:全开:0,关闭10%:1,关闭20%:2,关闭30%:3,关闭40%:4,关闭50%:5,关闭60%:6,关闭70%:7,关闭80%:8,关闭90%:9,全关:10", "method": "enum", "value": "0_1_2_3_4_5_6_7_8_9_10", "valueMap": {"0": "全开", "1": "关闭10%", "2": "关闭20%", "3": "关闭30%", "4": "关闭40%", "5": "关闭50%", "6": "关闭60%", "7": "关闭70%", "8": "关闭80%", "9": "关闭90%", "10": "全关"}}], "permission": "USER", "skillId": "0x301", "skillValue": "action.window.RFWindowPosition", "type": "ACTION", "config": "1"}, {"category": "左后车窗", "desc": "#level#", "input": [{"name": "level", "type": "int", "desc": "enum:全开:0,关闭10%:1,关闭20%:2,关闭30%:3,关闭40%:4,关闭50%:5,关闭60%:6,关闭70%:7,关闭80%:8,关闭90%:9,全关:10", "method": "enum", "value": "0_1_2_3_4_5_6_7_8_9_10", "valueMap": {"0": "全开", "1": "关闭10%", "2": "关闭20%", "3": "关闭30%", "4": "关闭40%", "5": "关闭50%", "6": "关闭60%", "7": "关闭70%", "8": "关闭80%", "9": "关闭90%", "10": "全关"}}], "permission": "USER", "skillId": "0x302", "skillValue": "action.window.LRWindowPosition", "type": "ACTION", "config": "1"}, {"category": "右后车窗", "desc": "#level#", "input": [{"name": "level", "type": "int", "desc": "enum:全开:0,关闭10%:1,关闭20%:2,关闭30%:3,关闭40%:4,关闭50%:5,关闭60%:6,关闭70%:7,关闭80%:8,关闭90%:9,全关:10", "method": "enum", "value": "0_1_2_3_4_5_6_7_8_9_10", "valueMap": {"0": "全开", "1": "关闭10%", "2": "关闭20%", "3": "关闭30%", "4": "关闭40%", "5": "关闭50%", "6": "关闭60%", "7": "关闭70%", "8": "关闭80%", "9": "关闭90%", "10": "全关"}}], "permission": "USER", "skillId": "0x303", "skillValue": "action.window.RRWindowPosition", "type": "ACTION", "config": "1"}, {"category": "所有车窗", "desc": "#level#", "input": [{"name": "level", "type": "int", "desc": "enum:全开:0,全关:10", "method": "enum", "value": "0_10", "valueMap": {"0": "全开", "10": "全关"}}], "permission": "USER", "skillId": "0x304", "skillValue": "action.window.WindowPosition", "type": "ACTION", "config": "1"}, {"category": "车窗通风", "desc": "打开", "input": [], "permission": "USER", "skillId": "0x306", "skillValue": "action.window.WindowVentModeSwitch", "type": "ACTION", "config": "1"}, {"category": "车窗透气", "desc": "打开", "input": [], "permission": "USER", "skillId": "0x31f", "skillValue": "action.window.WindowBreatModeSwitch", "type": "ACTION", "config": "1"}, {"category": "天窗d%", "desc": "天窗d%", "input": [{"name": "level", "type": "int", "desc": "enum:全关:0x1,全开 :0x21", "method": "enum", "value": "1_33", "valueMap": {"1": "全关", "33": "全开"}}], "permission": "USER", "skillId": "0x324", "skillValue": "action.skylight.SkylightMove", "type": "ACTION", "config": "2"}, {"category": "电动遮阳帘", "desc": "#positon#", "input": [{"name": "positon", "type": "uint8", "desc": "enum:关闭:1,打开:33", "method": "enum", "value": "1_33", "valueMap": {"1": "关闭", "33": "打开"}}], "permission": "USER", "skillId": "0x326", "skillValue": "action.window.ElectricSunshadeCurtain", "type": "ACTION", "config": "1"}, {"category": "主驾座椅加热", "desc": "#heatLevel#", "input": [{"name": "heatLevel", "type": "int", "desc": "enum:关闭:0,1挡:1,2挡:2,3挡:3", "method": "enum", "value": "0_1_2_3", "valueMap": {"0": "关闭", "1": "1挡", "2": "2挡", "3": "3挡"}}], "permission": "USER", "skillId": "0x405", "skillValue": "action.seat.LFSeatHeatLevel", "type": "ACTION", "config": "2"}, {"category": "主驾座椅通风", "desc": "#ventLevel#", "input": [{"name": "ventLevel", "type": "int", "desc": "enum:关闭:0,1挡:1,2挡:2,3挡:3", "method": "enum", "value": "0_1_2_3", "valueMap": {"0": "关闭", "1": "1挡", "2": "2挡", "3": "3挡"}}], "permission": "USER", "skillId": "0x406", "skillValue": "action.seat.LFSeatVentLevel", "type": "ACTION", "config": "2"}, {"category": "副驾座椅加热", "desc": "#heatLevel#", "input": [{"name": "heatLevel", "type": "int", "desc": "enum:关闭:0,1挡:1,2挡:2,3挡:3", "method": "enum", "value": "0_1_2_3", "valueMap": {"0": "关闭", "1": "1挡", "2": "2挡", "3": "3挡"}}], "permission": "USER", "skillId": "0x407", "skillValue": "action.seat.RFSeatHeatLevel", "type": "ACTION", "config": "2"}, {"category": "副驾座椅通风", "desc": "#ventLevel#", "input": [{"name": "ventLevel", "type": "int", "desc": "enum:关闭:0,1挡:1,2挡:2,3挡:3", "method": "enum", "value": "0_1_2_3", "valueMap": {"0": "关闭", "1": "1挡", "2": "2挡", "3": "3挡"}}], "permission": "USER", "skillId": "0x408", "skillValue": "action.seat.RFSeatVentLevel", "type": "ACTION", "config": "2"}, {"category": "主驾座椅记忆", "desc": "#positionMode#", "input": [{"name": "positionMode", "type": "int", "desc": "enum:驾驶 : 1,休息 : 3,备用 : 4", "method": "enum", "value": "1_3_4", "valueMap": {"1": "驾驶", "3": "休息", "4": "备用"}}], "permission": "USER", "skillId": "0x409", "skillValue": "action.seat.LFSeatMemoryMode", "type": "ACTION", "config": "2"}, {"category": "副驾座椅记忆", "desc": "#positionMode#", "input": [{"name": "positionMode", "type": "int", "desc": "enum:常用 : 1,休息 : 3,备用 : 4", "method": "enum", "value": "1_3_4", "valueMap": {"1": "常用", "3": "休息", "4": "备用"}}], "permission": "USER", "skillId": "0x40a", "skillValue": "action.seat.RFSeatMemoryMode", "type": "ACTION", "config": "3"}, {"category": "主驾座椅按摩", "desc": "#sw#", "input": [{"name": "sw", "type": "int", "desc": "enum:关闭:0", "method": "enum", "value": "0", "valueMap": {"0": "关闭"}}], "permission": "USER", "skillId": "0x42f", "skillValue": "action.seat.LFSeatMassageSwitch", "type": "ACTION", "config": "2"}, {"category": "主驾座椅按摩", "desc": "#massageIntensity#", "input": [{"name": "massageIntensity", "type": "int", "desc": "enum:重:1,中:2,轻:3", "method": "enum", "value": "1_2_3", "valueMap": {"1": "重", "2": "中", "3": "轻"}}], "permission": "USER", "skillId": "0x430", "skillValue": "action.seat.LFSeatMassageIntensity", "type": "ACTION", "config": "2"}, {"category": "副驾座椅按摩", "desc": "#sw#", "input": [{"name": "sw", "type": "int", "desc": "enum:关闭:0", "method": "enum", "value": "0", "valueMap": {"0": "关闭"}}], "permission": "USER", "skillId": "0x431", "skillValue": "action.seat.RFSeatMassageSwitch", "type": "ACTION", "config": "2"}, {"category": "副驾座椅按摩", "desc": "#massageIntensity#", "input": [{"name": "massageIntensity", "type": "int", "desc": "enum:重:1,中:2,轻:3", "method": "enum", "value": "1_2_3", "valueMap": {"1": "重", "2": "中", "3": "轻"}}], "permission": "USER", "skillId": "0x432", "skillValue": "action.seat.RFSeatMassageIntensity", "type": "ACTION", "config": "2"}, {"category": "主驾按摩模式", "desc": "#massageMode#", "input": [{"name": "massageMode", "type": "uint8", "desc": "enum:全身波浪:1,全身猫步:2,肩部敲击:3,肩部单排:4,背部波浪:5,背部脉冲:6,腰部波浪:7,腰部脉冲:8,臀腿波浪:9,臀腿脉冲:10", "method": "enum", "value": "1_2_3_4_5_6_7_8_9_10", "valueMap": {"1": "全身波浪", "2": "全身猫步", "3": "肩部敲击", "4": "肩部单排", "5": "背部波浪", "6": "背部脉冲", "7": "腰部波浪", "8": "腰部脉冲", "9": "臀腿波浪", "10": "臀腿脉冲"}}], "permission": "USER", "skillId": "0x433", "skillValue": "action.seat.LFSeatMassageMode", "type": "ACTION", "config": "2"}, {"category": "副驾按摩模式", "desc": "#massageMode#", "input": [{"name": "massageMode", "type": "uint8", "desc": "enum:全身波浪:1,全身猫步:2,肩部敲击:3,肩部单排:4,背部波浪:5,背部脉冲:6,腰部波浪:7,腰部脉冲:8,臀腿波浪:9,臀腿脉冲:10", "method": "enum", "value": "1_2_3_4_5_6_7_8_9_10", "valueMap": {"1": "全身波浪", "2": "全身猫步", "3": "肩部敲击", "4": "肩部单排", "5": "背部波浪", "6": "背部脉冲", "7": "腰部波浪", "8": "腰部脉冲", "9": "臀腿波浪", "10": "臀腿脉冲"}}], "permission": "USER", "skillId": "0x434", "skillValue": "action.seat.RFSeatMassageMode", "type": "ACTION", "config": "2"}, {"category": "主驾一键复位", "desc": "点击#SASReq#", "input": [{"name": "SASReq", "type": "uint8", "desc": "enum:打开:7", "method": "enum", "value": "7", "valueMap": {"7": "打开"}}], "permission": "USER", "skillId": "0x435", "skillValue": "action.seat.LFSeatReset", "type": "ACTION", "config": "2"}, {"category": "副驾一键复位", "desc": "点击#SASReq#", "input": [{"name": "SASReq", "type": "uint8", "desc": "enum:打开:7", "method": "enum", "value": "7", "valueMap": {"7": "打开"}}], "permission": "USER", "skillId": "0x436", "skillValue": "action.seat.RFSeatReset", "type": "ACTION", "config": "3"}, {"category": "后排尊享", "desc": "点击#status#", "input": [{"name": "status", "type": "uint8", "desc": "enum:打开:1", "method": "enum", "value": "1", "valueMap": {"1": "打开"}}], "permission": "USER", "skillId": "0x437", "skillValue": "action.seat.BackRowModeOn", "type": "ACTION", "config": "3"}, {"category": "后排左座椅一键折叠", "desc": "#status#", "input": [{"name": "status", "type": "uint8", "desc": "enum:打开:1", "method": "enum", "value": "1", "valueMap": {"1": "打开"}}], "permission": "USER", "skillId": "0x43e", "skillValue": "action.seat.BackLFSeatFold", "type": "ACTION", "config": "1"}, {"category": "后排右座椅一键折叠", "desc": "#status#", "input": [{"name": "status", "type": "uint8", "desc": "enum:打开:1", "method": "enum", "value": "1", "valueMap": {"1": "打开"}}], "permission": "USER", "skillId": "0x43f", "skillValue": "action.seat.BackRFSeatFold", "type": "ACTION", "config": "1"}, {"category": "零重力座椅", "desc": "#status#", "input": [{"name": "status", "type": "uint8", "desc": "enum:展开:1,复位:2", "method": "enum", "value": "1_2", "valueMap": {"1": "展开", "2": "复位"}}], "permission": "USER", "skillId": "0x440", "skillValue": "action.seat.ZeroGSeatStatus", "type": "ACTION", "config": "1"}, {"category": "后排左座椅加热", "desc": "#status#", "input": [{"name": "status", "type": "uint8", "desc": "enum:关闭:0,1档:1,2档:0,3档", "method": "enum", "value": "0_1_0", "valueMap": {"0": "2档", "1": "1档"}}], "permission": "USER", "skillId": "0x441", "skillValue": "action.seat.BackLFSeatHeatLevel", "type": "ACTION", "config": "1"}, {"category": "后排右座椅加热", "desc": "#status#", "input": [{"name": "status", "type": "uint8", "desc": "enum:关闭:0,1档:1,2档:0,3档", "method": "enum", "value": "0_1_0", "valueMap": {"0": "2档", "1": "1档"}}], "permission": "USER", "skillId": "0x442", "skillValue": "action.seat.BackRFSeatHeatLevel", "type": "ACTION", "config": "1"}, {"category": "后排左座椅通风", "desc": "#ventLevel#", "input": [{"name": "ventLevel", "type": "int", "desc": "enum:关闭:0,1挡:1,2挡:2,3挡:3", "method": "enum", "value": "0_1_2_3", "valueMap": {"0": "关闭", "1": "1挡", "2": "2挡", "3": "3挡"}}], "permission": "USER", "skillId": "0x443", "skillValue": "action.seat.BackLFSeatVentLevel", "type": "ACTION", "config": "2"}, {"category": "后排右座椅通风", "desc": "#ventLevel#", "input": [{"name": "ventLevel", "type": "int", "desc": "enum:关闭:0,1挡:1,2挡:2,3挡:3", "method": "enum", "value": "0_1_2_3", "valueMap": {"0": "关闭", "1": "1挡", "2": "2挡", "3": "3挡"}}], "permission": "USER", "skillId": "0x444", "skillValue": "action.seat.BackRFSeatVentLevel", "type": "ACTION", "config": "2"}, {"category": "后排左座椅按摩", "desc": "#massageIntensity#", "input": [{"name": "massageIntensity", "type": "int", "desc": "enum:重:1,中:2,轻:3", "method": "enum", "value": "1_2_3", "valueMap": {"1": "重", "2": "中", "3": "轻"}}], "permission": "USER", "skillId": "0x445", "skillValue": "action.seat.BackLFSeatMassageIntensity", "type": "ACTION", "config": "2"}, {"category": "后排右座椅按摩", "desc": "#massageIntensity#", "input": [{"name": "massageIntensity", "type": "int", "desc": "enum:重:1,中:2,轻:3", "method": "enum", "value": "1_2_3", "valueMap": {"1": "重", "2": "中", "3": "轻"}}], "permission": "USER", "skillId": "0x446", "skillValue": "action.seat.BackRFSeatMassageIntensity", "type": "ACTION", "config": "2"}, {"category": "后排左按摩模式", "desc": "#massageMode#", "input": [{"name": "massageMode", "type": "uint8", "desc": "enum:全身波浪:1,全身猫步:2,肩部敲击:3,肩部单排:4,背部波浪:5,背部脉冲:6,腰部波浪:7,腰部脉冲:8,臀腿波浪:9,臀腿脉冲:10", "method": "enum", "value": "1_2_3_4_5_6_7_8_9_10", "valueMap": {"1": "全身波浪", "2": "全身猫步", "3": "肩部敲击", "4": "肩部单排", "5": "背部波浪", "6": "背部脉冲", "7": "腰部波浪", "8": "腰部脉冲", "9": "臀腿波浪", "10": "臀腿脉冲"}}], "permission": "USER", "skillId": "0x447", "skillValue": "action.seat.BackLFSeatMassageMode", "type": "ACTION", "config": "2"}, {"category": "后排右按摩模式", "desc": "#massageMode#", "input": [{"name": "massageMode", "type": "uint8", "desc": "enum:全身波浪:1,全身猫步:2,肩部敲击:3,肩部单排:4,背部波浪:5,背部脉冲:6,腰部波浪:7,腰部脉冲:8,臀腿波浪:9,臀腿脉冲:10", "method": "enum", "value": "1_2_3_4_5_6_7_8_9_10", "valueMap": {"1": "全身波浪", "2": "全身猫步", "3": "肩部敲击", "4": "肩部单排", "5": "背部波浪", "6": "背部脉冲", "7": "腰部波浪", "8": "腰部脉冲", "9": "臀腿波浪", "10": "臀腿脉冲"}}], "permission": "USER", "skillId": "0x448", "skillValue": "action.seat.BackRFSeatMassageMode", "type": "ACTION", "config": "2"}, {"category": "后排左座椅按摩", "desc": "#sw#", "input": [{"name": "sw", "type": "int", "desc": "enum:关闭:0", "method": "enum", "value": "0", "valueMap": {"0": "关闭"}}], "permission": "USER", "skillId": "0x449", "skillValue": "action.seat.BackLFSeatMassageSwitch", "type": "ACTION", "config": "7"}, {"category": "后排右座椅按摩", "desc": "#sw#", "input": [{"name": "sw", "type": "int", "desc": "enum:关闭:0", "method": "enum", "value": "0", "valueMap": {"0": "关闭"}}], "permission": "USER", "skillId": "0x450", "skillValue": "action.seat.BackRFSeatMassageSwitch", "type": "ACTION", "config": "7"}, {"category": "后视镜控制", "desc": "#foldStatus#", "input": [{"name": "foldStatus", "type": "int", "desc": "enum:折叠 : 0,展开 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "折叠", "1": "展开"}}], "permission": "USER", "skillId": "0x500", "skillValue": "action.mirror.RearMirrorFoldSwitch", "type": "ACTION", "config": "2"}, {"category": "后视镜加热", "desc": "#MirrorHeatSWstate#", "input": [{"name": "MirrorHeatSWstate", "type": "int", "desc": "enum:打开: 0,关闭 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "打开", "1": "关闭"}}], "permission": "USER", "skillId": "0x501", "skillValue": "action.mirror.RearMirrorHeatSwitch", "type": "ACTION", "config": "2"}, {"category": "行李厢", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:打开 : 0", "method": "enum", "value": "0", "valueMap": {"0": "打开"}}], "permission": "USER", "skillId": "0x609", "skillValue": "action.lock.BackDoorSwitch", "type": "ACTION", "config": "5"}, {"category": "电动行李厢", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:关闭 : 0x1,打开 : 0x2", "method": "enum", "value": "1_2", "valueMap": {"1": "关闭", "2": "打开"}}], "permission": "USER", "skillId": "0x60b", "skillValue": "action.lock.BackDoorSwitch", "type": "ACTION", "config": "3", "specials": {"PK1B": {"category": "电动尾门", "desc": "%d", "input": [{"name": "status", "type": "int", "desc": "enum:关闭 : 0x1,打开 : 0x2", "method": "enum", "value": "1_2", "valueMap": {"1": "关闭", "2": "打开"}}], "permission": "USER", "skillId": "0x60b", "skillValue": "action.lock.BackDoorSwitch", "type": "ACTION", "config": "3"}}}, {"category": "自动雨刮灵敏度", "desc": "#period#", "input": [{"name": "period", "type": "int", "desc": "enum:最低 : 1,低 : 2,中 : 3,高 : 4,最高 : 5,", "method": "enum", "value": "1_2_3_4_5", "valueMap": {"1": "最低", "2": "低", "3": "中", "4": "高", "5": "最高"}}], "permission": "USER", "skillId": "0x615", "skillValue": "action.wiper.FrontWiperAutoModeLevel", "type": "ACTION", "config": "1"}, {"category": "左后车门童锁", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:解锁 : 0,上锁 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "解锁", "1": "上锁"}}], "permission": "USER", "skillId": "0x62a", "skillValue": "action.lock.LeftChildLock", "type": "ACTION", "config": "1"}, {"category": "右后车门童锁", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:解锁 : 0,上锁 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "解锁", "1": "上锁"}}], "permission": "USER", "skillId": "0x62b", "skillValue": "action.lock.RightChildLock", "type": "ACTION", "config": "1"}, {"category": "主驾车门", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:关闭 : 1,打开 : 2", "method": "enum", "value": "1_2", "valueMap": {"1": "关闭", "2": "打开"}}], "permission": "USER", "skillId": "0x62e", "skillValue": "action.lock.LFDoorStatus", "type": "ACTION", "config": "3"}, {"category": "副驾车门", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:关闭 : 1,打开 : 2", "method": "enum", "value": "1_2", "valueMap": {"1": "关闭", "2": "打开"}}], "permission": "USER", "skillId": "0x62f", "skillValue": "action.lock.RFDoorStatus", "type": "ACTION", "config": "3"}, {"category": "左后车门", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:关闭 : 1,打开 : 2", "method": "enum", "value": "1_2", "valueMap": {"1": "关闭", "2": "打开"}}], "permission": "USER", "skillId": "0x630", "skillValue": "action.lock.LRDoorStatus", "type": "ACTION", "config": "3"}, {"category": "右后车门", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:关闭 : 1,打开 : 2", "method": "enum", "value": "1_2", "valueMap": {"1": "关闭", "2": "打开"}}], "permission": "USER", "skillId": "0x631", "skillValue": "action.lock.RRDoorStatus", "type": "ACTION", "config": "3"}, {"category": "导航音量", "desc": "#per#", "input": [{"name": "per", "type": "int", "desc": "range:[0,40]", "method": "range", "value": "0_40", "valueMap": {}}], "permission": "USER", "skillId": "0x700", "skillValue": "action.setting.NaviVolume", "type": "ACTION", "config": "1"}, {"category": "语音音量", "desc": "#per#", "input": [{"name": "per", "type": "int", "desc": "range:[0,40]", "method": "range", "value": "0_40", "valueMap": {}}], "permission": "USER", "skillId": "0x701", "skillValue": "action.setting.SpeechVolume", "type": "ACTION", "config": "1"}, {"category": "多媒体音量", "desc": "#per#", "input": [{"name": "per", "type": "int", "desc": "range:[0,40]", "method": "range", "value": "0_40", "valueMap": {}}], "permission": "USER", "skillId": "0x702", "skillValue": "action.setting.MediaVolume", "type": "ACTION", "config": "1"}, {"category": "通话音量", "desc": "#per#", "input": [{"name": "per", "type": "int", "desc": "range:[0,40]", "method": "range", "value": "0_40", "valueMap": {}}], "permission": "USER", "skillId": "0x703", "skillValue": "action.setting.CallVolume", "type": "ACTION", "config": "1"}, {"category": "中控屏亮度", "desc": "#per#", "input": [{"name": "per", "type": "int", "desc": "range:[1,21]", "method": "range", "value": "1_21", "valueMap": {}}], "permission": "USER", "skillId": "0x704", "skillValue": "action.setting.ScreenBrightness", "type": "ACTION", "config": "1"}, {"category": "日夜模式", "desc": "#dayMode#", "input": [{"name": "dayMode", "type": "int", "desc": "enum:白天 : 1,黑夜 : 2,自适应:3", "method": "enum", "value": "1_2_3", "valueMap": {"1": "白天", "2": "黑夜", "3": "自适应"}}], "permission": "USER", "skillId": "0x705", "skillValue": "action.setting.NightMode", "type": "ACTION", "config": "1"}, {"category": "蓝牙", "desc": "#switchStatus#", "input": [{"name": "switchStatus", "type": "int", "desc": "enum:关闭 : 0,打开 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "关闭", "1": "打开"}}], "permission": "USER", "skillId": "0x707", "skillValue": "action.setting.BluetoothSwitch", "type": "ACTION", "config": "1"}, {"category": "热点", "desc": "#switchStatus#", "input": [{"name": "switchStatus", "type": "int", "desc": "enum:关闭 : 0,打开 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "关闭", "1": "打开"}}], "permission": "USER", "skillId": "0x708", "skillValue": "action.setting.HotspotSwitch", "type": "ACTION", "config": "1"}, {"category": "WLAN", "desc": "#switchStatus#", "input": [{"name": "switchStatus", "type": "int", "desc": "enum:关闭 : 0,打开 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "关闭", "1": "打开"}}], "permission": "USER", "skillId": "0x709", "skillValue": "action.setting.WLANSwitch", "type": "ACTION", "config": "1"}, {"category": "沉浸音效", "desc": "#mode#", "input": [{"name": "mode", "type": "int", "desc": "enum:自然原声:1,清透人声:2,沉浸观影:3,嗨翻现场:4,音乐厅:5,轻柔伴音:6,后排VIP:7", "method": "enum", "value": "1_2_3_4_5_6_7", "valueMap": {"1": "自然原声", "2": "清透人声", "3": "沉浸观影", "4": "嗨翻现场", "5": "音乐厅", "6": "轻柔伴音", "7": "后排VIP"}}], "permission": "USER", "skillId": "0x70a", "skillValue": "action.setting.ImmersiveSoundMode", "type": "ACTION", "config": "1"}, {"category": "蓝牙音乐音量", "desc": "#per#", "input": [{"name": "per", "type": "int", "desc": "range:[0,40]", "method": "range", "value": "0_40", "valueMap": {}}], "permission": "USER", "skillId": "0x71d", "skillValue": "action.setting.BluetoothVolume", "type": "ACTION", "config": "1"}, {"category": "车外扬声器音量", "desc": "#per#", "input": [{"name": "per", "type": "int", "desc": "range:[0,15]", "method": "range", "value": "0_15", "valueMap": {}}], "permission": "USER", "skillId": "0x71e", "skillValue": "action.setting.CarExteriorVolume", "type": "ACTION", "config": "1"}, {"category": "电话铃声音量", "desc": "#per#", "input": [{"name": "per", "type": "int", "desc": "range:[0,40]", "method": "range", "value": "0_40", "valueMap": {}}], "permission": "USER", "skillId": "0x71f", "skillValue": "action.setting.RingVolume", "type": "ACTION", "config": "1"}, {"category": "移动网络", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:关闭 : 0,打开 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "关闭", "1": "打开"}}], "permission": "USER", "skillId": "0x720", "skillValue": "action.setting.MobileNetworkSwitch", "type": "ACTION", "config": "1"}, {"category": "屏保模式", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum: 打开 : 0,关闭 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "打开", "1": "关闭"}}], "permission": "USER", "skillId": "0x721", "skillValue": "action.setting.ScreenMode", "type": "ACTION", "config": "1"}, {"category": "静音", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:关闭 : 0,打开 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "关闭", "1": "打开"}}], "permission": "USER", "skillId": "0x723", "skillValue": "action.setting.Mute", "type": "ACTION", "config": "1"}, {"category": "动感声浪音量", "desc": "#type#", "input": [{"name": "type", "type": "int", "desc": "enum:低:1,中:2,高:3", "method": "enum", "value": "1_2_3", "valueMap": {"1": "低", "2": "中", "3": "高"}}], "permission": "USER", "skillId": "0x724", "skillValue": "action.setting.SoundWaveVolume", "type": "ACTION", "config": "1"}, {"category": "熄屏", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum: 打开 : 0,关闭 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "打开", "1": "关闭"}}], "permission": "USER", "skillId": "0x725", "skillValue": "action.setting.ScreenOff", "type": "ACTION", "config": "1"}, {"category": "动感声浪开关", "desc": "#status#", "input": [{"name": "status", "type": "uint8", "desc": "enum: 关闭:0 , 打开:1 ", "method": "enum", "value": "0_1", "valueMap": {"0": "关闭", "1": "打开"}}], "permission": "USER", "skillId": "0x727", "skillValue": "action.setting.SoundWaveSwitch", "type": "ACTION", "config": "1"}, {"category": "无线充电", "desc": "#status#", "input": [{"name": "status", "type": "uint8", "desc": "enum: 关闭:0 , 打开:1 ", "method": "enum", "value": "0_1", "valueMap": {"0": "关闭", "1": "打开"}}], "permission": "USER", "skillId": "0x728", "skillValue": "action.setting.SetWirelessCharger", "type": "ACTION", "config": "1"}, {"category": "大喇叭实时喊话", "desc": "#mode#", "input": [{"name": "mode", "type": "uint8", "desc": "enum:关闭:0,10s:1,20s:2,30s:3,40s:4,50s:5,60s:6", "method": "enum", "value": "0_1_2_3_4_5_6", "valueMap": {"0": "关闭", "1": "10s", "2": "20s", "3": "30s", "4": "40s", "5": "50s", "6": "60s"}}], "permission": "USER", "skillId": "0x730", "skillValue": "action.setting.SetOuterSpeaker", "type": "ACTION", "config": "1"}, {"category": "开关", "desc": "#status#", "input": [{"name": "status", "type": "uint8", "desc": "enum: 关闭:0 , 打开:1 ", "method": "enum", "value": "0_1", "valueMap": {"0": "关闭", "1": "打开"}}], "permission": "USER", "skillId": "0x731", "skillValue": "action.system.HudStatus", "type": "ACTION", "config": "1"}, {"category": "HUD模式", "desc": "#mode#", "input": [{"name": "mode", "type": "uint8", "desc": "enum: 简洁:0 , AR:1 , SR:2 ", "method": "enum", "value": "0_1_2", "valueMap": {"0": "简洁", "1": "AR", "2": "SR"}}], "permission": "USER", "skillId": "0x732", "skillValue": "action.system.HudMode", "type": "ACTION", "config": "1"}, {"category": "颜色主题", "desc": "#status#", "input": [{"name": "status", "type": "uint8", "desc": "enum: 标准:0 , 雪地:1 ", "method": "enum", "value": "0_1", "valueMap": {"0": "标准", "1": "雪地"}}], "permission": "USER", "skillId": "0x733", "skillValue": "action.system.HudColorStatus", "type": "ACTION", "config": "1"}, {"category": "续航模式", "desc": "#mode#", "input": [{"name": "mode", "type": "uint8", "desc": "enum:CLTC:1,WLTP:2", "method": "enum", "value": "1_2", "valueMap": {"1": "CLTC", "2": "WLTP"}}], "permission": "USER", "skillId": "0x734", "skillValue": "action.system.BatteryLifeMode", "type": "ACTION", "config": "1"}, {"category": "驾驶模式", "desc": "#mode#", "input": [{"name": "mode", "type": "int", "desc": "enum: 舒适 : 1, 运动: 2, 标准: 3,  自定义 : 4,  AI专属 : 5", "method": "enum", "value": "1_2_3_4_5", "valueMap": {"1": "舒适", "2": "运动", "3": "标准", "4": "自定义", "5": "AI专属"}}], "permission": "USER", "skillId": "0x882", "skillValue": "action.driving.DrivingMode", "type": "ACTION", "config": "1"}, {"category": "香氛开关", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:关闭 : 0,打开 : 1,", "method": "enum", "value": "0_1", "valueMap": {"0": "关闭", "1": "打开"}}], "permission": "USER", "skillId": "0xb01", "skillValue": "action.aiot.AromatherapySwitch", "type": "ACTION", "config": "0"}, {"category": "香氛浓度", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:浓郁 : 1,淡雅 : 2,", "method": "enum", "value": "1_2", "valueMap": {"1": "浓郁", "2": "淡雅"}}], "permission": "USER", "skillId": "0xb02", "skillValue": "action.aiot.AromatherapyLevel", "type": "ACTION", "config": "0"}, {"category": "香氛时间", "desc": "#time#分钟", "input": [{"name": "time", "type": "int", "desc": "range:[1, 300]", "method": "range", "value": "1_300", "valueMap": {}}], "permission": "USER", "skillId": "0xb03", "skillValue": "action.aiot.AromatherapyDuration", "type": "ACTION", "config": "0"}, {"category": "空气净化器开关", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:关闭 : 0,打开 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "关闭", "1": "打开"}}], "permission": "USER", "skillId": "0xb04", "skillValue": "action.aiot.AirPurifierSwitch", "type": "ACTION", "config": "0"}, {"category": "儿童座椅通风", "desc": "#swStatus#", "input": [{"name": "swStatus", "type": "int", "desc": "enum:关闭:0,低:1,中:2,高:3", "method": "enum", "value": "0_1_2_3", "valueMap": {"0": "关闭", "1": "低", "2": "中", "3": "高"}}], "permission": "USER", "skillId": "0xb0d", "skillValue": "action.aiot.ChildSeatVentLevel", "type": "ACTION", "config": "0"}, {"category": "儿童座椅加热", "desc": "#swStatus#", "input": [{"name": "swStatus", "type": "int", "desc": "enum:关闭:0,低:1,中:2,高:3", "method": "enum", "value": "0_1_2_3", "valueMap": {"0": "关闭", "1": "低", "2": "中", "3": "高"}}], "permission": "USER", "skillId": "0xb0e", "skillValue": "action.aiot.ChildSeatHeatLevel", "type": "ACTION", "config": "0"}, {"category": "香氛模式", "desc": "#aromatherapyMode#", "input": [{"name": "aromatherapyMode", "type": "int", "desc": "enum:持续: 1,间隔 : 2", "method": "enum", "value": "1_2", "valueMap": {"1": "持续", "2": "间隔"}}], "permission": "USER", "skillId": "0xb1c", "skillValue": "action.aiot.AromatherapyMode", "type": "ACTION", "config": "0"}]