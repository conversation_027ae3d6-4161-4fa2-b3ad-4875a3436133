[{"category": "时间点", "desc": "#hour#:#minute#", "input": [{"name": "hour", "type": "uint32", "desc": "range:[0,23]", "method": "range", "value": "0_23", "valueMap": {}}, {"name": "minute", "type": "uint32", "desc": "range:[0,59]", "method": "range", "value": "0_59", "valueMap": {}}, {"name": "second", "type": "uint32", "desc": "range:[0,59]", "method": "range", "value": "0_59", "valueMap": {}}], "permission": "USER", "skillId": "0xFF06", "skillValue": "condition.common.Time", "type": "Trigger Condition", "config": "1"}, {"category": "车辆离开", "desc": "范围半径#distance#m", "input": [{"name": "distance", "type": "uint32", "desc": "range:[100,5000]", "method": "range", "value": "100_5000", "valueMap": {}}, {"name": "latitude", "type": "double", "desc": "searchConditionAddress", "method": "third<PERSON><PERSON>y", "value": "0.0_90.0", "valueMap": {}}, {"name": "longitude", "type": "double", "desc": "range:[0.0,180.0]", "method": "<PERSON><PERSON><PERSON><PERSON>", "value": "0.0_180.0", "valueMap": {}}], "permission": "USER", "skillId": "0x7B", "skillValue": "condition.navi.LeaveDestinationNotify", "type": "Trigger Condition", "config": "1", "params": [{"index": 0, "arg": "特定点名称（搜索默认选中第一个，distance填入InputArg）"}]}, {"category": "车辆到达", "desc": "范围半径#distance#m", "input": [{"name": "distance", "type": "uint32", "desc": "range:[100,5000]", "method": "range", "value": "100_5000", "valueMap": {}}, {"name": "latitude", "type": "double", "desc": "searchConditionAddress", "method": "third<PERSON><PERSON>y", "value": "0.0_90.0", "valueMap": {}}, {"name": "longitude", "type": "double", "desc": "range:[0.0,180.0]", "method": "<PERSON><PERSON><PERSON><PERSON>", "value": "0.0_180.0", "valueMap": {}}], "permission": "USER", "skillId": "0x7C", "skillValue": "condition.navi.ArriveDestinationNotify", "type": "Trigger Condition", "config": "1", "params": [{"index": 0, "arg": "特定点名称（搜索默认选中第一个，distance填入InputArg）"}]}, {"category": "儿童检测", "desc": "#position# #status#", "input": [{"name": "position", "type": "uint8", "desc": "enum:主驾:0,副驾:1,后排左:2,后排中:3,后排右:4,三排左:5,三排中:6,三排右:7", "method": "enum", "value": "0_1_2_3_4_5_6_7", "valueMap": {"0": "主驾", "1": "副驾", "2": "后排左", "3": "后排中", "4": "后排右", "5": "三排左", "6": "三排中", "7": "三排右"}}, {"name": "status", "type": "uint8", "desc": "enum:有儿童:1,无儿童:0", "method": "enum", "value": "1_0", "valueMap": {"1": "有儿童", "0": "无儿童"}}], "permission": "USER", "skillId": "0x7D", "skillValue": "condition.oms.SeatExistChildNotify", "type": "Trigger Condition", "config": "1"}, {"category": "乘客分布检测", "desc": "#position# #status#", "input": [{"name": "position", "type": "uint8", "desc": "enum:主驾:0,副驾:1,后排左:2,后排中:3,后排右:4,三排左:5,三排中:6,三排右:7", "method": "enum", "value": "0_1_2_3_4_5_6_7", "valueMap": {"0": "主驾", "1": "副驾", "2": "后排左", "3": "后排中", "4": "后排右", "5": "三排左", "6": "三排中", "7": "三排右"}}, {"name": "status", "type": "uint8", "desc": "enum: 无人:0,有人:1", "method": "enum", "value": "0_1", "valueMap": {"0": "无人", "1": "有人"}}], "permission": "USER", "skillId": "0x7E", "skillValue": "condition.oms.SeatExistPassengerNotify", "type": "Trigger Condition", "config": "1"}, {"category": "乘客性别检测", "desc": "#position# #gender#", "input": [{"name": "position", "type": "uint8", "desc": "enum:主驾:0,副驾:1,后排左:2,后排中:3,后排右:4,三排左:5,三排中:6,三排右:7", "method": "enum", "value": "0_1_2_3_4_5_6_7", "valueMap": {"0": "主驾", "1": "副驾", "2": "后排左", "3": "后排中", "4": "后排右", "5": "三排左", "6": "三排中", "7": "三排右"}}, {"name": "gender", "type": "uint8", "desc": "enum:男:0,女:1", "method": "enum", "value": "0_1", "valueMap": {"0": "男", "1": "女"}}], "permission": "USER", "skillId": "0x7F", "skillValue": "condition.oms.PassengerGenderNotify", "type": "Trigger Condition", "config": "1"}, {"category": "乘客行为检测", "desc": "#position# #behavior#", "input": [{"name": "position", "type": "uint8", "desc": "enum:主驾:0,副驾:1", "method": "enum", "value": "0_1", "valueMap": {"0": "主驾", "1": "副驾"}}, {"name": "behavior", "type": "uint8", "desc": "enum: 打电话:0", "method": "enum", "value": "0", "valueMap": {"0": "打电话"}}], "permission": "USER", "skillId": "0x80", "skillValue": "condition.oms.PassengerBehaviorNotify", "type": "Trigger Condition", "config": "1"}, {"category": "车内温度", "desc": "大于#InsideTemp#℃", "input": [{"name": "InsideTemp", "type": "double", "desc": "range:[-40,80]", "method": "range", "value": "-40_80", "valueMap": {}}], "permission": "USER", "skillId": "0x126", "skillValue": "condition.env.InsideTempNotifyGreater", "type": "Trigger Condition", "config": "1"}, {"category": "车内温度", "desc": "小于#InsideTemp#℃", "input": [{"name": "InsideTemp", "type": "double", "desc": "range:[-40,80]", "method": "range", "value": "-40_80", "valueMap": {}}], "permission": "USER", "skillId": "0x127", "skillValue": "condition.env.InsideTempNotifyLess", "type": "Trigger Condition", "config": "1"}, {"category": "车外温度", "desc": "大于#OutsideTemp#℃", "input": [{"name": "OutsideTemp", "type": "double", "desc": "range:[-40,80]", "method": "range", "value": "-40_80", "valueMap": {}}], "permission": "USER", "skillId": "0x128", "skillValue": "condition.env.OutsideTempNotifyGreater", "type": "Trigger Condition", "config": "1"}, {"category": "车外温度", "desc": "小于#OutsideTemp#℃", "input": [{"name": "OutsideTemp", "type": "double", "desc": "range:[-40,80]", "method": "range", "value": "-40_80", "valueMap": {}}], "permission": "USER", "skillId": "0x129", "skillValue": "condition.env.OutsideTempNotifyLess", "type": "Trigger Condition", "config": "1"}, {"category": "环境光线", "desc": "大于#AmbientBright#", "input": [{"name": "AmbientBright", "type": "int", "desc": "range:[0,12279]", "method": "range", "value": "0_12279", "valueMap": {}}], "permission": "USER", "skillId": "0x164", "skillValue": "condition.env.AmbientBrightNotifyGreater", "type": "Trigger Condition", "config": "1"}, {"category": "环境光线", "desc": "小于#AmbientBright#", "input": [{"name": "AmbientBright", "type": "int", "desc": "range:[0,12279]", "method": "range", "value": "0_12279", "valueMap": {}}], "permission": "USER", "skillId": "0x165", "skillValue": "condition.env.AmbientBrightNotifyLess", "type": "Trigger Condition", "config": "1"}, {"category": "远光灯", "desc": "#status#", "input": [{"name": "status", "type": "uint8", "desc": "enum:打开 : 1", "method": "enum", "value": "1", "valueMap": {"1": "打开"}}], "permission": "USER", "skillId": "0x268", "skillValue": "condition.light.HighBeamStatusNotify", "type": "Trigger Condition", "config": "1"}, {"category": "近光灯", "desc": "#status#", "input": [{"name": "status", "type": "uint8", "desc": "enum:打开 : 1", "method": "enum", "value": "1", "valueMap": {"1": "打开"}}], "permission": "USER", "skillId": "0x269", "skillValue": "condition.light.DippedBeamStatusNotify", "type": "Trigger Condition", "config": "1"}, {"category": "后雾灯", "desc": "#status#", "input": [{"name": "status", "type": "uint8", "desc": "enum:打开 : 1", "method": "enum", "value": "1", "valueMap": {"1": "打开"}}], "permission": "USER", "skillId": "0x270", "skillValue": "condition.light.FogLightStatusNotify", "type": "Trigger Condition", "config": "1"}, {"category": "主驾乘坐状态", "desc": "#isOccupied#", "input": [{"name": "isOccupied", "type": "int", "desc": "enum:离座:0,就座:1", "method": "enum", "value": "0_1", "valueMap": {"0": "离座", "1": "就座"}}], "permission": "USER", "skillId": "0x414", "skillValue": "condition.seat.LFSeatOccupNotify", "type": "Trigger Condition", "config": "1"}, {"category": "副驾乘坐状态", "desc": "#isOccupied#", "input": [{"name": "isOccupied", "type": "int", "desc": "enum:离座:0,就座:1", "method": "enum", "value": "0_1", "valueMap": {"0": "离座", "1": "就座"}}], "permission": "USER", "skillId": "0x416", "skillValue": "condition.seat.RFSeatOccupNotify", "type": "Trigger Condition", "config": "1"}, {"category": "主驾安全带", "desc": "#beltState#", "input": [{"name": "beltState", "type": "int", "desc": "enum:系上:0,解开:2", "method": "enum", "value": "0_2", "valueMap": {"0": "系上", "2": "解开"}}], "permission": "USER", "skillId": "0x41b", "skillValue": "condition.seat.LFSafetyBeltNotify", "type": "Trigger Condition", "config": "1"}, {"category": "副驾安全带", "desc": "#beltState#", "input": [{"name": "beltState", "type": "int", "desc": "enum:系上:0,解开:2", "method": "enum", "value": "0_2", "valueMap": {"0": "系上", "2": "解开"}}], "permission": "USER", "skillId": "0x41c", "skillValue": "condition.seat.RFSafetyBeltNotify", "type": "Trigger Condition", "config": "1"}, {"category": "后排左安全带", "desc": "#status#", "input": [{"name": "status", "type": "uint8", "desc": "enum:系上:0,解开:2", "method": "enum", "value": "0_2", "valueMap": {"0": "系上", "2": "解开"}}], "permission": "USER", "skillId": "0x438", "skillValue": "condition.seat.BackLFSafetyBeltNotify", "type": "Trigger Condition", "config": "1"}, {"category": "后排右安全带", "desc": "#status#", "input": [{"name": "status", "type": "uint8", "desc": "enum:系上:0,解开:2", "method": "enum", "value": "0_2", "valueMap": {"0": "系上", "2": "解开"}}], "permission": "USER", "skillId": "0x439", "skillValue": "condition.seat.BackRFSafetyBeltNotify", "type": "Trigger Condition", "config": "1"}, {"category": "后排中安全带", "desc": "#status#", "input": [{"name": "status", "type": "uint8", "desc": "enum:系上:0,解开:2", "method": "enum", "value": "0_2", "valueMap": {"0": "系上", "2": "解开"}}], "permission": "USER", "skillId": "0x43a", "skillValue": "condition.seat.BackCenterSafetyBeltNotify", "type": "Trigger Condition", "config": "1"}, {"category": "#doorID#", "desc": "#status#", "input": [{"name": "doorID", "type": "int", "desc": "enum:主驾车门 : 1,左后车门 : 3,副驾车门 : 2,右后车门 : 4", "method": "enum", "value": "1_3_2_4", "valueMap": {"1": "主驾车门", "3": "左后车门", "2": "副驾车门", "4": "右后车门"}}, {"name": "status", "type": "int", "desc": "enum:关闭 : 0,打开 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "关闭", "1": "打开"}}], "permission": "USER", "skillId": "0x605", "skillValue": "condition.lock.DoorLockNotify", "type": "Trigger Condition", "config": "1"}, {"category": "左后车门童锁", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:解锁 : 0,上锁 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "解锁", "1": "上锁"}}], "permission": "USER", "skillId": "0x620", "skillValue": "condition.lock.LeftChildLockNotify", "type": "Trigger Condition", "config": "1"}, {"category": "右后车门童锁", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:解锁 : 0,上锁 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "解锁", "1": "上锁"}}], "permission": "USER", "skillId": "0x623", "skillValue": "condition.lock.RightChildLockNotify", "type": "Trigger Condition", "config": "1"}, {"category": "全车门锁", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:解锁 : 0,上锁 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "解锁", "1": "上锁"}}], "permission": "USER", "skillId": "0x62d", "skillValue": "condition.lock.CarLockStatusNotify", "type": "Trigger Condition", "config": "1"}, {"category": "日出/日落", "desc": "#status#", "input": [{"name": "status", "type": "uint8", "desc": "enum:日出:1,日落:4", "method": "enum", "value": "1_4", "valueMap": {"1": "日出", "4": "日落"}}], "permission": "USER", "skillId": "0x729", "skillValue": "condition.setting.NotifyDayStatus", "type": "Trigger Condition", "config": "1"}, {"category": "电池电量", "desc": "大于#hvBatSOC#%", "input": [{"name": "hvBatSOC", "type": "float", "desc": "range:[0,100]", "method": "range", "value": "0_100", "valueMap": {}}], "permission": "USER", "skillId": "0x81c", "skillValue": "condition.power.HVBatSOCNotifyGreater", "type": "Trigger Condition", "config": "1"}, {"category": "电池电量", "desc": "小于#hvBatSOC#%", "input": [{"name": "hvBatSOC", "type": "float", "desc": "range:[0,100]", "method": "range", "value": "0_100", "valueMap": {}}], "permission": "USER", "skillId": "0x81d", "skillValue": "condition.power.HVBatSOCNotifyLess", "type": "Trigger Condition", "config": "1"}, {"category": "续航里程", "desc": "大于#distanceToEmpty#km", "input": [{"name": "distanceToEmpty", "type": "float", "desc": "range:[0,819]", "method": "range", "value": "0_819", "valueMap": {}}], "permission": "USER", "skillId": "0x839", "skillValue": "condition.power.RangeNotifyGreater", "type": "Trigger Condition", "config": "1"}, {"category": "续航里程", "desc": "小于#distanceToEmpty#km", "input": [{"name": "distanceToEmpty", "type": "float", "desc": "range:[0,819]", "method": "range", "value": "0_819", "valueMap": {}}], "permission": "USER", "skillId": "0x83a", "skillValue": "condition.power.RangeNotifyLess", "type": "Trigger Condition", "config": "1"}, {"category": "挡位", "desc": "#gearState#", "input": [{"name": "gearState", "type": "int", "desc": "enum:P挡:1,R挡:2,N挡:3,D挡:4", "method": "enum", "value": "1_2_3_4", "valueMap": {"1": "P挡", "2": "R挡", "3": "N挡", "4": "D挡"}}], "permission": "USER", "skillId": "0x87f", "skillValue": "condition.driving.GearNotify", "type": "Trigger Condition", "config": "1"}, {"category": "充电", "desc": "#status#", "input": [{"name": "status", "type": "uint8", "desc": "enum: 结束 : 0, 开始 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "结束", "1": "开始"}}], "permission": "USER", "skillId": "0x891", "skillValue": "condition.power.ChargeStatusNotify", "type": "Trigger Condition", "config": "1"}, {"category": "放电", "desc": "#status#", "input": [{"name": "status", "type": "uint8", "desc": "enum: 结束 : 0, 开始 : 9", "method": "enum", "value": "0_9", "valueMap": {"0": "结束", "9": "开始"}}], "permission": "USER", "skillId": "0x894", "skillValue": "condition.power.DischargeStatusNotify", "type": "Trigger Condition", "config": "1"}, {"category": "车速", "desc": "大于#vehicleSpeed#km/h", "input": [{"name": "vehicleSpeed", "type": "double", "desc": "range:[0,200]", "method": "range", "value": "0_200", "valueMap": {}}], "permission": "USER", "skillId": "0xa06", "skillValue": "condition.driving.VehicleSpeedNotifyGreater", "type": "Trigger Condition", "config": "1"}, {"category": "车速", "desc": "小于#vehicleSpeed#km/h", "input": [{"name": "vehicleSpeed", "type": "double", "desc": "range:[0,200]", "method": "range", "value": "0_200", "valueMap": {}}], "permission": "USER", "skillId": "0xa07", "skillValue": "condition.driving.VehicleSpeedNotifyLess", "type": "Trigger Condition", "config": "1"}, {"category": "驾驶里程", "desc": "大于#travelDistanceState#km", "input": [{"name": "travelDistanceState", "type": "int", "desc": "range:[0,999]", "method": "range", "value": "0_999", "valueMap": {}}], "permission": "USER", "skillId": "0xa17", "skillValue": "condition.driving.TravelDistanceNotifyGreater", "type": "Trigger Condition", "config": "1"}, {"category": "驾驶里程", "desc": "小于#travelDistanceState#km", "input": [{"name": "travelDistanceState", "type": "int", "desc": "range:[0,999]", "method": "range", "value": "0_999", "valueMap": {}}], "permission": "USER", "skillId": "0xa18", "skillValue": "condition.driving.TravelDistanceNotifyLess", "type": "Trigger Condition", "config": "1"}, {"category": "驾驶时长", "desc": "大于#travelTimeState#小时", "input": [{"name": "travelTimeState", "type": "float", "desc": "range:[0,24]", "method": "range", "value": "0_24", "valueMap": {}}], "permission": "USER", "skillId": "0xa1d", "skillValue": "condition.driving.TravelTimeNotifyGreater", "type": "Trigger Condition", "config": "1"}, {"category": "驾驶时长", "desc": "小于#travelTimeState#小时", "input": [{"name": "travelTimeState", "type": "float", "desc": "range:[0,24]", "method": "range", "value": "0_24", "valueMap": {}}], "permission": "USER", "skillId": "0xa1e", "skillValue": "condition.driving.TravelTimeNotifyLess", "type": "Trigger Condition", "config": "1"}]