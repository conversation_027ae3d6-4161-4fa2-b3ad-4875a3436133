{"condition": [], "action": [{"skillValue": "action.ac.ACTemperature", "permission": "USER", "type": "ACTION", "6": {"category": "空调温度", "desc": "#temp#℃", "input": [{"name": "temp", "type": "double", "desc": "range:[16,32]", "method": "range", "value": "16_32", "valueMap": {}}], "permission": "USER", "skillId": "0x102", "skillValue": "action.ac.ACTemperature;action.ac.ACTemperatureRF;action.ac.ACTemperatureLF", "type": "ACTION", "config": "6"}, "2": {"category": "空调温度", "desc": "主驾 #temp#℃", "input": [{"name": "temp", "type": "double", "desc": "range:[16,32]", "method": "range", "value": "16_32", "valueMap": {}}], "permission": "USER", "skillId": "0x166", "skillValue": "action.ac.ACTemperature;action.ac.ACTemperatureLF", "type": "ACTION", "config": "2"}}, {"skillValue": "action.ac.ACTemperatureRF", "permission": "USER", "type": "ACTION", "6": {"category": "空调温度", "desc": "#temp#℃", "input": [{"name": "temp", "type": "double", "desc": "range:[16,32]", "method": "range", "value": "16_32", "valueMap": {}}], "permission": "USER", "skillId": "0x102", "skillValue": "action.ac.ACTemperature;action.ac.ACTemperatureRF;action.ac.ACTemperatureLF", "type": "ACTION", "config": "6"}}, {"skillValue": "action.ac.ACTemperatureLF", "permission": "USER", "type": "ACTION", "6": {"category": "空调温度", "desc": "#temp#℃", "input": [{"name": "temp", "type": "double", "desc": "range:[16,32]", "method": "range", "value": "16_32", "valueMap": {}}], "permission": "USER", "skillId": "0x102", "skillValue": "action.ac.ACTemperature;action.ac.ACTemperatureRF;action.ac.ACTemperatureLF", "type": "ACTION", "config": "6"}, "2": {"category": "空调温度", "desc": "主驾 #temp#℃", "input": [{"name": "temp", "type": "double", "desc": "range:[16,32]", "method": "range", "value": "16_32", "valueMap": {}}], "permission": "USER", "skillId": "0x166", "skillValue": "action.ac.ACTemperature;action.ac.ACTemperatureLF", "type": "ACTION", "config": "2"}}, {"skillValue": "action.lock.BackDoorSwitch", "permission": "USER", "type": "ACTION", "5": {"category": "行李厢", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:打开 : 0", "method": "enum", "value": "0", "valueMap": {"0": "打开"}}], "permission": "USER", "skillId": "0x609", "skillValue": "action.lock.BackDoorSwitch", "type": "ACTION", "config": "5"}, "3": {"category": "电动行李厢", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:关闭 : 0x1,打开 : 0x2", "method": "enum", "value": "1_2", "valueMap": {"1": "关闭", "2": "打开"}}], "permission": "USER", "skillId": "0x60b", "skillValue": "action.lock.BackDoorSwitch", "type": "ACTION", "config": "3", "specials": {"PK1B": {"category": "电动尾门", "desc": "%d", "input": [{"name": "status", "type": "int", "desc": "enum:关闭 : 0x1,打开 : 0x2", "method": "enum", "value": "1_2", "valueMap": {"1": "关闭", "2": "打开"}}], "permission": "USER", "skillId": "0x60b", "skillValue": "action.lock.BackDoorSwitch", "type": "ACTION", "config": "3"}}}}]}