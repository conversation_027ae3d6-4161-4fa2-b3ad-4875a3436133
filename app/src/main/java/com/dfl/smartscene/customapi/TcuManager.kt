package com.dfl.smartscene.customapi

import android.os.RemoteException
import android.text.TextUtils
import com.dfl.android.common.customapi.ConfigManager
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.BuildConfig
import com.dfl.android.common.global.GlobalConstant
import com.lanyou.tcu.protocolservicelibrary.ITcuDeviceManager
import com.lanyou.tcu.protocolservicelibrary.ITcuDeviceManager.ITcu4GmoduleStatusListener
import com.lanyou.tcu.protocolservicelibrary.ITcuDeviceManager.ITcuDeviceStatusListener
import com.lanyou.tcu.protocolservicelibrary.ServiceProxy
import com.lanyou.tcu.protocolservicelibrary.ServiceProxy.ServiceProxyListener
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2023/10/16
 * desc: TCU管理器
 * version:1.0
 */
object TcuManager {
    private const val TAG = GlobalConstant.GLOBAL_TAG + "TcuManager"
    private var mServiceProxy: ServiceProxy? = null
    private var mTcuDevice: ITcuDeviceManager? = null
    private var mServiceProxyListener: ServiceProxyListener? = null
    private var mTcuDeviceStatusListener: ITcuDeviceStatusListener? = null
    private var mTcu4GModuleStatusListener: ITcu4GmoduleStatusListener? = null

    /**
     * 网络是否连接,默认连接，避免无法发起请求
     */
    @Volatile
    private var mIsConnect = false

    @JvmStatic
    fun initTcu() {
        mTcuDeviceStatusListener = MyTcuDeviceStatusListener()
        mTcu4GModuleStatusListener = MyTcu4GmoduleStatusListener()
        mServiceProxyListener = object : ServiceProxyListener {
            override fun onServiceConnected(p0: ITcuDeviceManager?) {
                CommonLogUtils.logD(TAG, "TCU:onServiceConnected")
                mTcuDevice = p0
                mTcuDevice?.setTcuDeviceStatusListener(mTcuDeviceStatusListener)
                mTcuDevice?.setTcu4GmoduleStatusListener(mTcu4GModuleStatusListener)
            }

            override fun onServiceDisconnected() {
                CommonLogUtils.logD(TAG, "TCU:onServiceDisconnected")
                //2秒后重连
                CoroutineScope(Dispatchers.IO).launch {
                    delay(2000)
                    mServiceProxy?.rebind(CommonUtils.getApp())
                }
            }

            override fun onServiceConnectError() {
                CommonLogUtils.logD(TAG, "TCU:onServiceConnectError")
                //2秒后重连
                CoroutineScope(Dispatchers.IO).launch {
                    delay(2000)
                    mServiceProxy?.rebind(CommonUtils.getApp())
                }
            }
        }
        CoroutineScope(Dispatchers.IO).launch {
            mServiceProxy = ServiceProxy.getInstance(CommonUtils.getApp(), mServiceProxyListener)
        }
    }

    /**
     * 获取是否连接的结果
     */
    @JvmStatic
    fun isConnect(): Boolean {
        return mIsConnect
    }

    /**
     * TCU注册
     *
     *  developerId     DA开发者代码
     *  deviceSN        DA设备序列号
     *  softwareVersion DA软件版本号
     */
    private fun startRegister() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val deviceSN = ConfigManager.getDaid()
                CommonLogUtils.logD(TAG, "startRegister deviceSN:$deviceSN")
                if (TextUtils.isEmpty(deviceSN)) {
                    return@launch
                }
                mTcuDevice?.startRegist(0x3482, deviceSN, BuildConfig.VERSION_NAME)
            } catch (e: RemoteException) {
                e.printStackTrace()
            }
        }
    }

    /**
     * 获取GPS数据或4G开关
     *
     * @param type 1:GPS数据  2:4G
     */
    private fun getSwitchStatus(type: Int) {
        if (checkService()) {
            try {
                mTcuDevice?.getSwitchStatus(type)
            } catch (e: RemoteException) {
                e.printStackTrace()
            }
        }
    }

    /**
     * 检查服务连接是否正常
     *
     * @return true正常，false异常，需要重连
     */
    private fun checkService(): Boolean {
        try {
            if (mTcuDevice == null || mTcuDevice?.isDeviceReady == false || mTcuDevice?.isDeviceRegisted == false) {
                CommonLogUtils.logE(TAG, "TcuDevice服务连接异常")
                return false
            }
        } catch (e: RemoteException) {
            e.printStackTrace()
        }
        return true
    }

    private class MyTcuDeviceStatusListener : ITcuDeviceStatusListener {
        override fun onTCUdeviceReady() {
            CommonLogUtils.logD(TAG, "onTCUdeviceReady")
            startRegister()
        }

        override fun onTCUdeviceAbnormal(reason: String?) {
            CommonLogUtils.logD(TAG, "onTCUdeviceAbnormal:$reason")
            mIsConnect = false
        }

        override fun onError(errorCode: Int) {
            CommonLogUtils.logD(TAG, "onError:$errorCode")
        }

        override fun onRegisterFail() {
            CommonLogUtils.logD(TAG, "onRegisterFail")
            //注册失败，则重复进行注册
            startRegister()
        }

        /**
         * TCU注册成功回调
         * @param developerId  TCU开发者代码
         * @param deviceSN     TCU设备序列号
         * @param softwareVersion  TCU软件版本号
         * @param hardwareVersion  TCU硬件版本号
         */
        override fun onRegisterSuccess(
            developerId: Int,
            deviceSN: String?,
            softwareVersion: String?,
            hardwareVersion: String?
        ) {
            CommonLogUtils.logD(
                TAG, "onRegisterSuccess:developerId=$developerId," +
                        "deviceSN=$deviceSN,softwareVersion=$softwareVersion,hardwareVersion=$hardwareVersion"
            )
            getSwitchStatus(2)
        }
    }

    private class MyTcu4GmoduleStatusListener : ITcu4GmoduleStatusListener {
        /**
         * 信号强度变化通知
         * @param level 信号强度（取值 0~5），数值越大信号质量越好
         */
        override fun onSignalLevelUpdate(level: Int) {

        }

        /**
         * 网络制式变化通知
         * @param netWorkLevel （2~4）当前网络制式等级
         */
        override fun onNetWorkModeUpdate(p0: Int) {

        }

        override fun onEnableStateChange(p0: Boolean) {

        }

        override fun onDataLinkStateReport(p0: Boolean, p1: Boolean) {

        }

        override fun onNetRegistration(p0: String?, p1: String?) {

        }

        override fun onNetWorkRefreshState(p0: Int) {

        }

        /**
         * TCU 回调流量使用状态结果给 DA
         * 频率：有流量使用时 1Hz，无流量使用时不会有回调。
         * 当前值有效时间：收到回调后的 1S。
         * 说明：当无数据上传下载时，不会有状态回调。
         * @param type    1:4G 流量使用
         * @param status  1:上传  2:下载  3：上传下载
         */
        override fun dataTransportStatus(type: Int, status: Int) {

        }

        /**
         * GPS数据开关状态、4G开关设置状态通知
         * @param type  1:GPS数据   2:4G开关
         * @param status  0:关闭    1:打开
         */
        override fun getSwitchStatusResponse(type: Int, status: Int) {
            CommonLogUtils.logD(TAG, "getSwitchStatusResponse:type=$type,status=$status")
            if (type == 2) {
                mIsConnect = status == 1
            }
        }

        /**
         * GPS数据开关状态、4G开关设置状态通知
         * @param type  1:GPS数据   2:4G开关
         * @param status  0:关闭    1:打开
         */
        override fun setSwitchStatusResponse(type: Int, status: Int) {
            CommonLogUtils.logD(TAG, "setSwitchStatusResponse:type=$type,status=$status")
            if (type == 2) {
                mIsConnect = status == 1
            }
        }

        /**
         * GPS数据开关状态、4G开关设置状态通知
         * @param type  1:GPS数据   2:4G开关
         * @param status  0:关闭    1:打开
         */
        override fun onSwitchStatusUpdate(type: Int, status: Int) {
            CommonLogUtils.logD(TAG, "onSwitchStatusUpdate:type=$type,status=$status")
            if (type == 2) {
                mIsConnect = status == 1
            }
        }

    }

}