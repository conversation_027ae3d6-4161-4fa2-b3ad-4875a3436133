package com.dfl.smartscene.customapi

import com.dfl.android.common.base.BaseSoaManager
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.util.CustomApiUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.api.base.Constants
import com.dfl.api.da.soundeffect.ISound
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


/**
 *<pre>
 *    author:zouzhushi
 *    e-mail:<EMAIL>
 *    time  :2023/03/22
 *    desc  :音效播放类
 *</pre>
 */
object SoundManager : BaseSoaManager() {
    @Volatile
    private var mISound: ISound? = null

    override fun init() {
        TAG = GlobalConstant.GLOBAL_TAG.plus("SoundManager")
        CoroutineScope(Dispatchers.Default).launch {
            initCustomApi()
        }
    }

    /**
     * 初始化customapi，会等待初始化结果
     * @return false初始化失败 true成功
     */
    private suspend fun initCustomApi(): Boolean = initCustomApi(
        Constants.DFL_DA_SOUNDEFFECT_SOUND_SERVICE,
        mISound,
        { if (it is ISound) it else null },
        { mISound = it }
    )

    /**
     * 623专用：
     *@param soundType：1 流程成功声 done 2 流程失败声 failed 3 消息提醒声 notice 4 删除/清空声 delete
     * 5 卡片弹出音效 toast 6 里程/时间（滚轮）设置声 roller 7 键盘输入声（拨号键盘、其它）keyboard 8 DA按键音 pressscreen
     * 9 行车记录仪（拍照、开始/结束录制）photo 10 外设（插入/拔出）
     * LK1A：
     * 10.外设插入  plugin 11外设拔出 unplug 12息屏 DAoff 13开屏 DAon
     */
    fun playSoundEffect(soundType: SoundType) {
        CustomApiUtils.requestCustomApi({
            if (initCustomApi()) {
                mISound?.playCharmSoundEffect(soundType.value)
                CommonLogUtils.logI(TAG, "playCharmSoundEffect type=${soundType.name} value=${soundType.value}")
            }
        }, {
            it.printStackTrace()
            CommonLogUtils.logE(TAG, "playCharmSoundEffect type:$soundType is Error:${it.message}")
        })
    }

    enum class SoundType(var value: Int) {
        DONE(1),
        FAILED(2),
        NOTICE(3),
        DELETE(4),
        TOAST(5),
        ROLLER(6),
        KEYBOARD(7),
        PRESSSCREEN(8),
        PHOTO(9),
        PLUGIN(10),
        UNPLUG(
            11
        ),
        DAOFF(12),
        DAON(13)
    }
}