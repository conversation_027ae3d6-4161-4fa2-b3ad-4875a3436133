package com.dfl.comfort.widget

import android.view.View
import androidx.viewpager2.widget.ViewPager2

/**
 * author:  l<PERSON><PERSON><PERSON>
 * time:    2025/01/08
 * desc:    ViewPager的翻页效果——渐变和大小变化
 * version: 2.0
 */

/**
 * 根据页面的偏移量position来调整页面的透明度和水平偏移
 * 当页面完全在屏幕左侧（position < -1）或完全在屏幕右侧（position > 1）时，透明度为0
 * 当页面在屏幕中间（position == 0）时，透明度为1
 */
class FadePageTransformer : ViewPager2.PageTransformer {
    override fun transformPage(view: View, position: Float) {
        val alpha = 1 - Math.abs(position) //绝对值函数
        view.alpha = alpha
        view.translationX = -view.width * position
    }
}

//class ZoomOutPageTransformer : ViewPager2.PageTransformer {
//    private  val MIN_SCALE = 0.95f
//    private  val MIN_X_SCALE = 0.99f
//    private  val MIN_ALPHA = 0.8f
//    override fun transformPage(view: View, position: Float) {
//        view.apply {
//            val pageWidth = width
//            val pageHeight = height
//            when {
//                position < -1 -> { // [-Infinity,-1)
//                    // This page is way off-screen to the left.
//                    alpha = 0f
//                }
//                position <= 1 -> { // [-1,1]
//                    // Modify the default slide transition to shrink the page as well
//                    val scaleFactor = Math.max(MIN_SCALE, 1 - Math.abs(position))
//                    val scaleFactorX = Math.max(MIN_X_SCALE, 1 - Math.abs(position))
//                    val vertMargin = pageHeight * (1 - scaleFactor) / 2
//                    val horzMargin = pageWidth * (1 - scaleFactor) / 2
//                    translationX = if (position < 0) {
//                        horzMargin - vertMargin / 2
//                    } else {
//                        horzMargin + vertMargin / 2
//                    }
//
//                    // Scale the page down (between MIN_SCALE and 1)
//                    scaleX = scaleFactorX
//                    scaleY = scaleFactor
//
//                    // Fade the page relative to its size.
//                    alpha = (MIN_ALPHA +
//                            (((scaleFactor - MIN_SCALE) / (1 - MIN_SCALE)) * (1 - MIN_ALPHA)))
//                }
//                else -> { // (1,+Infinity]
//                    // This page is way off-screen to the right.
//                    alpha = 0f
//                }
//            }
//        }
//    }
//}
