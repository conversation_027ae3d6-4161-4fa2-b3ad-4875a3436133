package com.dfl.smartscene.widget.edittext;

import android.content.Context;
import android.util.AttributeSet;

import com.dfl.common.nightmode.widget.SkinCompatEditText;

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2023/07/06
 * desc :途径点输入框，已未使用
 * version: 1.0
 */
@Deprecated
public class MidAddressEditText extends SkinCompatEditText {

    public MidAddressEditText(Context context) {
        super(context);

    }

    public MidAddressEditText(Context context, AttributeSet attrs) {
        super(context, attrs);

    }

    public MidAddressEditText(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

    }


    @Override
    public void applySkin() {
        super.applySkin();
        //this.setHighlightColor(ContextCompat.getColor(CommonUtils.getApp(),R.color.select_edit_all_color));
        invalidate();
    }
}
