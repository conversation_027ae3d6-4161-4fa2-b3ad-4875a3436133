package com.dfl.smartscene.widget.seekbar.colorseekbar;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.DashPathEffect;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PathEffect;
import android.graphics.RectF;
import android.graphics.Shader;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.dfl.android.commonlib.CommonUtils;
import com.dfl.smartscene.R;

import me.jessyan.autosize.utils.AutoSizeUtils;


/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/10/09
 * desc :氛围灯自选颜色进度条
 * version: 1.0
 */
public class ColorSeekBar extends View {
    private final int progressHeight = 64;//进度条高度
    private final String[] mColorSeeds =
            CommonUtils.getApp().getResources().getStringArray(R.array.color_seekbar_colors);
    private final Context mContext;
    private final int[] cacheColors = new int[mColorSeeds.length];
    private Paint mBoxPaint;//边框画笔
    private Paint mBgPaint;//背景进度条画笔
    private Paint mLinePaint;//刻度线画笔
    private Paint mMarkPaint;//三角形图标画笔
    private Path mTrianglePath;//三角形的path
    private Paint mExamplePaint;//预览颜色画笔
    private float progressWidth = 320;
    private int leftPadding = 25;//左边距
    private RectF rectF;//颜色背景区域
    private RectF mReviewRect;//预览颜色区域
    private LinearGradient linearGradient;
    private float mPercent = 0;
    private int mColor2 = 0xF22525;
    private OnColorChangeListener onColorChangeListener;

    public ColorSeekBar(Context context) {
        super(context);
        this.mContext = context;
    }

    public ColorSeekBar(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        //初始化颜色
        for (int i = 0; i < mColorSeeds.length; i++) {
            cacheColors[i] = Color.parseColor(mColorSeeds[i]);
        }
    }

    private void init() {
        //初始化边框画笔
        mBoxPaint = new Paint();
        mBoxPaint.setStyle(Paint.Style.STROKE);
        mBoxPaint.setAntiAlias(true);
        mBoxPaint.setStrokeWidth(1);
        mBoxPaint.setColor(ContextCompat.getColor(mContext, R.color.scene_color_scrollbar));

        //初始化背景画笔
        mBgPaint = new Paint();
        mBgPaint.setStyle(Paint.Style.FILL);
        mBgPaint.setShader(getLinearGradient());
        rectF = new RectF(leftPadding, 137, progressWidth + leftPadding,
                137 + progressHeight);
        //初始化刻度线
        mLinePaint = new Paint();
        mLinePaint.setStyle(Paint.Style.FILL);
        mLinePaint.setTextSize(3);
        PathEffect effects = new DashPathEffect(new float[]{5, 5, 5, 5}, 1);
        mLinePaint.setPathEffect(effects);
        mLinePaint.setColor(ContextCompat.getColor(mContext, R.color.scene_primary_color));

        //初始化三角形
        mTrianglePath = new Path();
        mMarkPaint = new Paint();
        mLinePaint.setStyle(Paint.Style.FILL);
        mMarkPaint.setColor(ContextCompat.getColor(mContext, R.color.scene_primary_color));
        //绘制展示样本颜色
        mExamplePaint = new Paint();
        mExamplePaint.setStyle(Paint.Style.FILL);
        mExamplePaint.setAntiAlias(true);
        mReviewRect = new RectF();
    }

    private LinearGradient getLinearGradient() {
        if (linearGradient == null) {

            linearGradient = new LinearGradient(leftPadding, 0, progressWidth + leftPadding
                    , progressHeight, cacheColors, null, Shader.TileMode.CLAMP);
            //根据R文件中的id获取到color
        }
        return linearGradient;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        canvas.drawRoundRect(rectF, 12, 12, mBgPaint);
        drawBgBox(canvas);
        drawLine(canvas);
        drawTriangle(canvas);
        drawReView(canvas);
    }

    private void drawBgBox(Canvas canvas) {
        canvas.drawRoundRect(rectF, 12, 12, mBoxPaint);
    }

    //绘制刻度线
    private void drawLine(Canvas canvas) {
        float x = leftPadding + progressWidth * mPercent;
        canvas.drawLine(x + 1, rectF.top, x + 1, rectF.bottom, mLinePaint);
    }

    //绘制三角形
    private void drawTriangle(Canvas canvas) {
        float height = rectF.top;
        mTrianglePath.reset();
        mTrianglePath.moveTo(progressWidth * mPercent + leftPadding - 12, height - 23);
        mTrianglePath.lineTo(progressWidth * mPercent + leftPadding + 12, height - 23);
        mTrianglePath.lineTo(progressWidth * mPercent + leftPadding, height - 5);
        mTrianglePath.close();
        canvas.drawPath(mTrianglePath, mMarkPaint);
    }

    //绘制预览颜色
    private void drawReView(Canvas canvas) {
        mReviewRect.left = progressWidth * mPercent - 35 + leftPadding;
        mReviewRect.right = progressWidth * mPercent + 35 + leftPadding;
        mReviewRect.bottom = rectF.top - 30;
        mReviewRect.top = rectF.top - 80;
        mExamplePaint.setColor(mColor2);
        canvas.drawRoundRect(mReviewRect, 8, 8, mExamplePaint);
        canvas.drawRoundRect(mReviewRect, 8, 8, mBoxPaint);
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldWidth, int oldHeight) {
        super.onSizeChanged(w, h, oldWidth, oldHeight);
        int mWidth = getWidth();
        progressWidth = mWidth * 0.88f;
        leftPadding = (int) ((mWidth - progressWidth) / 2);
        init();
        //        updateCachedColors();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        int realWidth = startMeasure(widthMeasureSpec);
        int realHeight = startMeasure(heightMeasureSpec);
        setMeasuredDimension(realWidth, realHeight);
    }

    private int startMeasure(int msSpec) {
        int result;
        int mode = MeasureSpec.getMode(msSpec);
        int size = MeasureSpec.getSize(msSpec);
        if (mode == MeasureSpec.EXACTLY) {
            result = size;
        } else {
            result = AutoSizeUtils.dp2px(mContext, 200);
        }
        return result;
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        float x = event.getX();
        float y = event.getY();
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                if (y < rectF.top || y > rectF.bottom) {
                    return false;
                }
                if (x >= leftPadding && x <= leftPadding + progressWidth) {
                    calculatePercent(x);
                    if (onColorChangeListener != null) {
                        onColorChangeListener.onColorChangeListener(getColor());
                    }
                    invalidate();
                }
                break;
            case MotionEvent.ACTION_MOVE:
                if (x < leftPadding) {
                    x = leftPadding;
                } else {
                    x = Math.min(x, leftPadding + progressWidth - 1);
                }
                calculatePercent(x);
                if (onColorChangeListener != null) {
                    onColorChangeListener.onColorChangeListener(getColor());
                }
                invalidate();
                break;
            case MotionEvent.ACTION_CANCEL:
                break;
        }
        return true;
    }

    public void setOnColorChangeListener(OnColorChangeListener listener) {
        this.onColorChangeListener = listener;
    }

    public int getColor() {
        int index = (int) (mPercent * mColorSeeds.length);
        if (index >= cacheColors.length) {
            index = cacheColors.length - 1;
        }
        mColor2 = cacheColors[index];
        return mColor2;
    }

    public void setColor(int value) {
        if (cacheColors == null || cacheColors.length == 0) {
            return;
        }
        int index = -1;
        for (int i = 0; i < cacheColors.length; i++) {
            if (value == cacheColors[i]) {
                index = i;
            }
        }
        mColor2 = value;
        if (index != -1) {
            mPercent = index * 1f / cacheColors.length;
            invalidate();
        }
    }

    private void updateCachedColors() {
        int width = (int) (rectF.width() + leftPadding);
        Bitmap bitmap = Bitmap.createBitmap(width, progressHeight, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        RectF mRectF = new RectF();
        mRectF.top = 0;
        mRectF.bottom = progressHeight;
        mRectF.left = rectF.left;
        mRectF.right = rectF.right;
        canvas.drawRect(mRectF, mBgPaint);
        for (double i = 0; i <= mColorSeeds.length - 1; i++) {
            int w = (int) (((i / mColorSeeds.length) * progressWidth) + leftPadding);
            if (i == mColorSeeds.length - 1)
                w--;
            //            cacheColors.add(bitmap.getPixel(w, 50));
        }
        bitmap.recycle();
        if (mPercent != 0) {//预设置了默认颜色的话需要再设置了颜色集合之后刷新当前预览颜色
            getColor();
        }
    }

    private void calculatePercent(float x) {
        mPercent = (x - leftPadding) / progressWidth;
    }

    public void setPercent(float percent) {
        this.mPercent = percent / mColorSeeds.length;
        getColor();
        invalidate();
    }

    public interface OnColorChangeListener {
        void onColorChangeListener(int color);
    }
}