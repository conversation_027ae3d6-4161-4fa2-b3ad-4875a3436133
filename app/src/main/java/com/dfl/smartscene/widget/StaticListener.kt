package com.dfl.smartscene.widget

import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import kotlin.math.abs

/**
 *Created by 钟文祥 on 2024/8/9.
 *Describer: 用于防止recycleview多次点击
 */
open class StaticListener : OnItemClickListener {
    private var lastTimeMillis: Long = 0
    private val MIN_CLICK_INTERVAL: Long = 1000

    /**间隔超过1秒可以点击*/
    fun isTimeEnabled(): Boolean {
        val currentTimeMillis = System.currentTimeMillis()
        val diff = abs(currentTimeMillis - lastTimeMillis)
        if (diff > MIN_CLICK_INTERVAL) {
            lastTimeMillis = currentTimeMillis
            return true
        }
        return false
    }

    override fun onItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
    }

}