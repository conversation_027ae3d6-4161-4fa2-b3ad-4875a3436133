package com.dfl.smartscene.widget.box;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/10/10
 * desc :我的场景，添加场景背景框特效
 * version: 1.0
 */
public class BackgroundBoxView extends View {
    private FluidColorfulFrameDrawable drawable;

    public BackgroundBoxView(Context context) {
        super(context);
        init();
    }

    public BackgroundBoxView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }
    private void init(){
        drawable=new FluidColorfulFrameDrawable();
        setPadding(5,5,5,5);
        drawable.setCallback(this);
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        drawable.setBounds(0,0,w,h);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        drawable.draw(canvas);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        drawable.cancelFluid();
    }
    public void stopFluid(){
        drawable.cancelFluid();
    }
    public void startFluid(){
        drawable.startFluid();
    }
    @Override
    public void invalidateDrawable(@NonNull Drawable dr) {
        super.invalidateDrawable(dr);
        if (dr==drawable){
            invalidate();
        }
    }

    @Override
    protected void onVisibilityChanged(@NonNull View changedView, int visibility) {
        super.onVisibilityChanged(changedView, visibility);
        if (visibility==View.VISIBLE){
            startFluid();
        }else{
            stopFluid();
        }
    }
}
