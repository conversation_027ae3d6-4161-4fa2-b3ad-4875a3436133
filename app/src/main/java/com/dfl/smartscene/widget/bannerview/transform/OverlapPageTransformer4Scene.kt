/*
Copyright 2017 zhpanvip The BannerViewPager Open Source Project

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
 */
package com.dfl.smartscene.widget.bannerview.transform

import android.os.Build
import android.view.View
import androidx.annotation.RequiresApi
import androidx.viewpager2.widget.ViewPager2
import com.zhpan.indicator.utils.IndicatorUtils
import kotlin.math.abs
import kotlin.math.max

/**
 * @param: gives veiwpager2 orientation
 * Source from：https://github.com/KoderLabs/finite-cover-flow/blob/dev/lib/src/main/java/com/saeed/finiteflow/lib/OverlapSliderTransformer.kt
 */
@RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
class OverlapPageTransformer4Scene(
    private val orientation: Int,  //方向
    private val minScale: Float = 0f, //最小比例
    private val unSelectedItemRotation: Float = 0f, //未选定的item旋转
    private val unSelectedItemAlpha: Float = 0f, //未选定的item透明度
    private val itemGap: Float = 0f //间隙
) : ViewPager2.PageTransformer {

    init {
        require(minScale in 0f..1f) { "minScale value should be between 1.0 to 0.0" }
        require(
            unSelectedItemAlpha in 0f..1f
        ) { "unSelectedItemAlpha value should be between 1.0 to 0.0" }
    }

    private var scalingValue = 0.2f

    override fun transformPage(
        page: View, position: Float
    ) {
        page.apply {
            scalingValue = if (minScale >= 0.8) {
                0.2f
            } else if (minScale >= 0.6) {
                0.3f
            } else {
                0.4f
            }
            elevation = -abs(position)
            val delta = max(1f - abs(position * (1 - 0.5f)), 0.5f)

            if (unSelectedItemRotation != 0f) {
                val rotation = (1 - delta) * if (position > 0) unSelectedItemRotation else -unSelectedItemRotation

                rotationY = rotation
            }

            val scaleDelta = abs(position * scalingValue)
            val scale = max(1f - scaleDelta, minScale)

//            scaleX = scale
//            scaleY = scale

            val dp2px = IndicatorUtils.dp2px((itemGap.toInt() / 2).toFloat())
            when (orientation) {
                ViewPager2.ORIENTATION_HORIZONTAL -> {
                    translationX = position * dp2px + if (position > 0) {
                        (-width * (1f - scale))
                    } else {
                        (width * (1f - scale))
                    }
                }

                ViewPager2.ORIENTATION_VERTICAL -> {
                    translationY = position * dp2px + if (position > 0) {
                        (-width * (1f - scale))
                    } else {
                        (width * (1f - scale))
                    }
                }

                else -> throw IllegalArgumentException(
                    "Gives correct orientation value, ViewPager2.ORIENTATION_HORIZONTAL or ViewPager2.ORIENTATION_VERTICAL"
                )
            }

            if (unSelectedItemAlpha != 1f) {
                alpha = when {
                    position >= -1f && position <= 1f -> { // (0, 1]
                        // page move from right to center.
                        0.5f + ((1 - abs(position)) * 0.5f)
                    }

                    else -> {
                        0.5f / abs(position * position)
                    }
                }
            }

            //===自己写的===

            pivotY = height.toFloat() //底部对齐
            val DEFAULT_CENTER = 0.5f
//            pivotX = width.toFloat() * 4.3f
//            pivotX = width.toFloat() * -3.3f
            if (position < -1) {
                scaleX = minScale
                scaleY = minScale
                pivotX = width.toFloat() * 4.3f
            } else if (position <= 1) {
                if (position < 0) { //[-1,0)
                    val scaleFactor: Float = (1 + position) * (1 - minScale) + minScale
                    scaleX = scaleFactor
                    scaleY = scaleFactor
                    pivotX = width * ((1 - position) * DEFAULT_CENTER) * 4.3f
                } else { //[0,1]
                    val scaleFactor: Float = (1 - position) * (1 - minScale) + minScale
                    scaleX = scaleFactor
                    scaleY = scaleFactor
                    pivotX = width.toFloat() * -3.3f
//                    if (position < 1) {//[0,1)
////                        pivotX = width * position * -3.3f * DEFAULT_CENTER
//                    } else {//[1]
//                        pivotX = width.toFloat() * -3.3f
//                    }
                }
            } else {
                scaleX = minScale
                scaleY = minScale
//                pivotX = 0f
                pivotX = width.toFloat() * -3.3f
            }


//            val DEFAULT_CENTER = 0.5f
//            val DEFAULT_MIN_SCALE = 0.85f
//
//            val pageWidth: Int = getWidth()
//            val pageHeight: Int = getHeight()
//
//            setPivotY(pageHeight.toFloat())
//            setPivotX(pageWidth / 2f)
//            if (position < -1) {
//                // This page is way off-screen to the left.
//                setScaleX(minScale)
//                setScaleY(minScale)
//                setPivotX(pageWidth.toFloat())
//            } else if (position <= 1) {
//                // Modify the default slide transition to shrink the page as well
//                if (position < 0) {
//                    val scaleFactor: Float = (1 + position) * (1 - minScale) + minScale
//                    setScaleX(scaleFactor)
//                    setScaleY(scaleFactor)
//                    setPivotX(pageWidth * (DEFAULT_CENTER + DEFAULT_CENTER * -position))
//                } else {
//                    val scaleFactor: Float = (1 - position) * (1 - minScale) + minScale
//                    setScaleX(scaleFactor)
//                    setScaleY(scaleFactor)
//                    setPivotX(pageWidth * ((1 - position) * DEFAULT_CENTER))
//                }
//            } else {
//                setPivotX(0f)
//                setScaleX(minScale)
//                setScaleY(minScale)
//            }


        }
    }
}