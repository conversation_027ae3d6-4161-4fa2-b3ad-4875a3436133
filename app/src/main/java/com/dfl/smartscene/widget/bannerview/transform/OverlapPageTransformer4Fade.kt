/*
Copyright 2017 zhpanvip The BannerViewPager Open Source Project

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
 */
package com.dfl.smartscene.widget.bannerview.transform

import android.view.View
import androidx.viewpager2.widget.ViewPager2
import com.dfl.smartscene.widget.bannerview.utils.BannerUtils


/**
 * https://blog.csdn.net/weixin_44370506/article/details/127161098
 * @param: gives veiwpager2 orientation
 * Source from：https://github.com/KoderLabs/finite-cover-flow/blob/dev/lib/src/main/java/com/saeed/finiteflow/lib/OverlapSliderTransformer.kt
底部不动，上层滑动的过程中始终在上层
 */
class OverlapPageTransformer4Fade(
) : ViewPager2.PageTransformer {


    private var map = mutableMapOf<Int, Float>()
    private var isLeft = true //是否左滑
    override fun transformPage(
        page: View, position: Float
    ) {
        if (position == 0.0f || position == 1.0f || position == -1.0f) {
            BannerUtils.isLeftSlide = BannerUtils.BannerSlide.STOP
            map.clear()
        }
        if (!map.containsKey(page.hashCode())) {
            map[page.hashCode()] = position
        }
        if (position == map[page.hashCode()]) {
            BannerUtils.isLeftSlide = BannerUtils.BannerSlide.STOP
        } else {
            isLeft = position < map[page.hashCode()]!!
            BannerUtils.isLeftSlide = if (isLeft) BannerUtils.BannerSlide.LEFT else BannerUtils.BannerSlide.RIGHT
        }

//        Log.e(
//            "rtlkjsgf2",
//            "transformPage: " + (if (isLeft) "左" else "右哈哈") + " ,位置:" + position + " ,code:" + map[page.hashCode()] + " ,号：" + page.hashCode() + " ,有：" + map.size
//        )

        if (isLeft) {
            if (position >= 0f) {
                page.translationX = 0f
                page.translationZ = 0f
            } else {
                page.translationX = page.width * -position
                page.translationZ = position
            }
        } else {
            if (position <= 0f) {
                page.translationX = 0f
                page.translationZ = 0f
            } else {
                page.translationX = page.width * -position
                page.translationZ = -position
            }

        }


    }

}