package com.dfl.smartscene.widget.bannerview.shape

import android.graphics.Color

/**
 * @className Shape
 * <AUTHOR>
 * @version 1.0.0
 * @description 图形
 * @createTime 2025/01/14/08:35
 */
abstract class Shape: IShape {

    private var width: Float = 0f
    private var height: Float = 0f

    private var centerX: Float = 0f
    private var centerY: Float = 0f

    private var offsetX: Float = 0f
    private var offsetY: Float = 0f

    private var color: Int = Color.TRANSPARENT

    private var alpha: Float = 1f

    private var allowDraw: Boolean = true

    private var position: Int = 0

    override fun setWidth(width: Float) {
        this.width = width
    }

    override fun getWidth(): Float {
        return width
    }

    override fun setHeight(height: Float) {
        this.height = height
    }

    override fun getHeight(): Float {
        return height
    }

    override fun setCenterX(x: Float) {
        this.centerX = x
    }

    override fun getCenterX(): Float {
        return centerX
    }

    override fun setCenterY(y: Float) {
        this.centerY = y
    }

    override fun getCenterY(): Float {
        return centerY
    }

    override fun setOffsetX(offset: Float) {
        this.offsetX = offset
    }

    override fun getOffsetX(): Float {
        return this.offsetX
    }

    override fun setOffsetY(offset: Float) {
       this.offsetY = offset
    }

    override fun getOffsetY(): Float {
        return offsetY
    }

    override fun setColor(color: Int) {
        this.color = color
    }

    override fun setAlpha(alpha: Float) {
        this.alpha = alpha
    }

    override fun getAlpha(): Float {
        return alpha
    }

    override fun getColor(): Int {
        return color
    }

    override fun isAllowDraw(): Boolean {
        return this.allowDraw
    }

    override fun setAllowDraw(allowDraw: Boolean) {
        this.allowDraw = allowDraw
    }

    override fun setPosition(position: Int){
        this.position = position
    }

    override fun getPosition(): Int{
        return position
    }

    override fun toString(): String {
        return "allowDraw=$allowDraw, position=$position, centerX=$centerX, width=$width, height=$height, offsetX=$offsetX, offsetY=$offsetY, alpha=$alpha"
    }


}