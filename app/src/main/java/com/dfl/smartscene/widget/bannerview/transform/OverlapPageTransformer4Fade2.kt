/*
Copyright 2017 zhpanvip The BannerViewPager Open Source Project

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
 */
package com.dfl.smartscene.widget.bannerview.transform

import android.os.Build
import android.view.View
import androidx.annotation.RequiresApi
import androidx.viewpager2.widget.ViewPager2


/**
 * https://blog.csdn.net/weixin_44370506/article/details/127161098
 * @param: gives veiwpager2 orientation
 * Source from：https://github.com/KoderLabs/finite-cover-flow/blob/dev/lib/src/main/java/com/saeed/finiteflow/lib/OverlapSliderTransformer.kt
    底层不动，上层滑动的过程中，底层会随上下调整
 */
@RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
class OverlapPageTransformer4Fade2(
) : ViewPager2.PageTransformer {


    private var map = mutableMapOf<Int, Float>()
    private var leftMap = mutableMapOf<Int, Boolean>()
    private var isLeft = true //是否左滑
    override fun transformPage(
        page: View, position: Float
    ) {
        if (!map.containsKey(page.hashCode())) {
            map[page.hashCode()] = position
        }
        isLeft = position < map[page.hashCode()]!!
        map[page.hashCode()] = position

//        Log.e("rtlkjsgf", "transformPage: " + (if (isLeft) "左" else "右") + " , " + page.hashCode())

        if (isLeft) {
            if (position >= 0f) {
                page.translationX = 0f
                page.translationZ = 0f
            } else {
                page.translationX = page.width * -position
                page.translationZ = position
            }
//这个透明度设置是可以的
//            if (position > 1 || position < -1) {
//                page.alpha = 0f
//            } else {
//                page.alpha = 0.8f + ((1 - abs(position)) * 0.2f)
//            }

        } else {
            if (position <= 0f) {
                page.translationX = 0f
                page.translationZ = 0f
            } else {
                page.translationX = page.width * -position
                page.translationZ = -position
            }

//            if (-1f < position && position < 1f) {
//                page.alpha = 0.8f + ((1 - abs(position)) * 0.2f)
//            } else {
//                page.alpha = 0f
//            }
        }


    }

}