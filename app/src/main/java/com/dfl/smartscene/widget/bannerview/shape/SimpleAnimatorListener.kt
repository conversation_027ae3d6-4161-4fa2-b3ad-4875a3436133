package com.dfl.smartscene.widget.bannerview.shape

import android.animation.Animator

/**
 * @className SimpleAnimatorListener
 * <AUTHOR>
 * @version 1.0.0
 * @description 动画监听器
 * @createTime 2025/01/16/09:34
 */
abstract class SimpleAnimatorListener: Animator.AnimatorListener{

    override fun onAnimationStart(animation: Animator) {

    }

    override fun onAnimationEnd(animation: Animator) {

    }

    override fun onAnimationCancel(animation: Animator) {

    }

    override fun onAnimationRepeat(animation: Animator) {

    }


}