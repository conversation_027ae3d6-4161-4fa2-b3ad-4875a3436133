package com.dfl.smartscene.widget.bannerview.shape

import android.graphics.Canvas
import android.graphics.Paint

/**
 * @className Indicator
 * <AUTHOR>
 * @version 1.0.0
 * @description 指示器图形
 * @createTime 2025/01/14/08:32
 */
class Indicator : Shape() {


    private var corner: Float = 3f

    private var scrollDx: Float = 0f
    private var scrollRealDx: Float = 0f
    private var scrollDy: Float = 0f

    override fun draw(canvas: Canvas, paint: Paint) {

//        Log.d(TAG,"position=${getPosition()}, centerX=${getCenterX()},isAllowDraw=${isAllowDraw()}, alpha=${getAlpha()}, width=${getWidth()}, height=${getHeight()}, OffsetX=${getOffsetX()}, offsetY=${getOffsetY()}, color=${getColor()}, scrollDx=$scrollDx, scrollY=$scrollDy")
        if (isAllowDraw()) {
            canvas.save()
            paint.reset()
            paint.isAntiAlias = true

            val alphaFloat = getAlpha()
            val alpha: Int = if (alphaFloat.equals(1f)) {
                255
            } else if (alphaFloat.equals(0f)) {
                0
            } else {
                (alphaFloat * 255).toInt()
            }
            paint.color = getColor()
            paint.alpha = alpha

            canvas.translate(getOffsetX() + scrollDx, getOffsetY() + scrollDy)
            canvas.drawRoundRect(0f, 0f, getWidth(), getHeight(), corner, corner, paint)

            canvas.restore()
        }
    }


    fun setRectCorner(corner: Float) {
        this.corner = corner
    }

    fun getRectCorner(): Float {
        return corner
    }


    fun getScrollDx(): Float = scrollDx

    fun setScrollDx(dx: Float) {
        this.scrollDx = dx
    }

    fun getScrollDy(): Float = scrollDy
    fun setScrollDy(dy: Float) {
        this.scrollDy = dy
    }

    fun getScrollRealDx(): Float = scrollRealDx
    fun setScrollRealDx(dx: Float) {
        this.scrollRealDx = dx
    }


    companion object {
        private const val TAG = "SmartIndicator"
    }

}