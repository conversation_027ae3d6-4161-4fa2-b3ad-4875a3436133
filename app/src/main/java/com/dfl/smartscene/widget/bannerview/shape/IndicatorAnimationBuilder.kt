package com.dfl.smartscene.widget.bannerview.shape

import android.animation.ValueAnimator
import android.view.animation.PathInterpolator
/**
 * @className OffsetIndicatorAnimation
 * <AUTHOR>
 * @version 1.0.0
 * @description 翻页符动画构建器
 * @createTime 2025/01/15/09:41
 */
@Deprecated("过时")
object IndicatorAnimationBuilder {

    private var alphaDuration = 100L

    fun buildAlpha(from: Float, to: Float): ValueAnimator {
      return  ValueAnimator.ofFloat(1.0f, 0f).apply {
          interpolator = PathInterpolator(0.33f, 0f, 0.67f, 1f)
          duration = alphaDuration
      }
    }
}