package com.dfl.smartscene.widget.bannerview.shape

import android.animation.Animator
import android.content.Context
import android.util.AttributeSet
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.widget.bannerview.shape.animation.MultiIndicatorAnimaBuilder

/**
 * @className MultiIndicatorView
 * <AUTHOR>
 * @version 1.0.0
 * @description 多点翻页符, 如果重新设置数据集，需要重新设置setCurrentPosition()方法
 * @createTime 2025/01/20/17:36
 */
class MultiIndicatorView : SmartIndicatorBase {


    //组件1
    private var indicatorPrev: Int = -1

    private var indicator0CenterX: Float = 0f
    private var indicator1CenterX: Float = 0f
    private var indicator2CenterX: Float = 0f

    //中心点
    private var indicator3CenterX: Float = 0f
    private var indicator4CenterX: Float = 0f
    private var indicator5CenterX: Float = 0f
    private var indicator6CenterX: Float = 0f


    private var smallWith = 7f
    private var smallHeight = 7f

    //组件3
    private var indicatorNext: Int = -1


    constructor(
        context: Context?,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0
    ) : super(context!!, attrs, defStyleAttr) {
        showNumber = 5
        scrollDistance = shapeMargin
        startScrollPosition = showNumber - 2
        notExceedPointCount = showNumber
    }

    constructor(
        context: Context?,
        attrs: AttributeSet? = null
    ) : this(context!!, attrs, 0)

    constructor(context: Context) : this(context, null)


    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
//        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        if (getPageSize() >= showNumber) {
            var length = showNumber + 1
            val width = shapeMargin * length + shapeMargin + paddingStart + paddingEnd
            val height = getSelectedHeight() * 2 + paddingTop + paddingBottom
            setMeasuredDimension(width.toInt(), height.toInt())
        } else if (getPageSize() in 1 until showNumber) {
            var length = getPageSize()
            val width = shapeMargin * length + shapeMargin + paddingStart + paddingEnd
            val height = getSelectedHeight() * 2 + paddingTop + paddingBottom
            setMeasuredDimension(width.toInt(), height.toInt())
        } else {
            super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        }
//        Log.d(TAG, "onMeasure---width=$measuredWidth, height=$measuredHeight")

        if (getPageSize() >= showNumber) {
            indicatorPrev = getCurrentPosition() - 1
            indicatorNext = getCurrentPosition() + 1

            indicator3CenterX = measuredWidth / 2f

            indicator2CenterX = indicator3CenterX - shapeMargin
            indicator1CenterX = indicator2CenterX - shapeMargin
            indicator0CenterX = indicator1CenterX - shapeMargin

            indicator4CenterX = indicator3CenterX + shapeMargin
            indicator5CenterX = indicator4CenterX + shapeMargin
            indicator6CenterX = indicator5CenterX + shapeMargin

        } else if (getPageSize() == 3) {
            indicator2CenterX = measuredWidth / 2f
            indicator1CenterX = indicator2CenterX - shapeMargin
            indicator3CenterX = indicator2CenterX + shapeMargin

        } else if (getPageSize() == 4) {
            val centerX = measuredWidth / 2f
            indicator2CenterX = centerX - shapeMargin / 2f
            indicator3CenterX = centerX + shapeMargin / 2f
            indicator1CenterX = indicator2CenterX - shapeMargin
            indicator4CenterX = indicator3CenterX + shapeMargin
        } else if (getPageSize() == 2) {
            val centerX = measuredWidth / 2f
            indicator1CenterX = centerX - shapeMargin / 2f
            indicator2CenterX = centerX + shapeMargin / 2f
        } else if (getPageSize() == 1) {
            indicator1CenterX = measuredWidth / 2f
        }

        if (getPageSize() > 0) {
            initIndicators()
//            if (getCurrentPosition() == 0) {
//                mLastPosition = 1
//            } else if(getCurrentPosition() == getCurrentPosition() - 1){
//                mLastPosition = getCurrentPosition() - 1
//            }else{
//                mLastPosition = -1
//            }
            mLastPosition = -1
            toggle(false)
        }
        CommonLogUtils.logI(
            TAG,
            "onMeasure---indicator0CenterX=$indicator0CenterX, indicator1CenterX=$indicator1CenterX" +
                    ", indicator2CenterX=$indicator2CenterX, indicator3CenterX=$indicator3CenterX, indicator4CenterX=$indicator4CenterX, indicator5CenterX=$indicator5CenterX, indicator6CenterX=$indicator6CenterX"
        )
    }

    override fun toggle(isAnimation: Boolean) {
        CommonLogUtils.logI(
            TAG,
            "toggle---isAnimation=$isAnimation, currentPosition=${mLastPosition}, nextPosition=${getCurrentPosition()},pageSize=${getPageSize()}"
        )
        for (i in 0 until shapes.size) {
            val indicator = shapes[i]
            indicator.setAllowDraw(false)
            indicator.setAlpha(0f)
            indicator.setColor(getNormalColor())
            indicator.setWidth(smallWith)
            indicator.setHeight(smallHeight)
        }

        if (isAnimation) {
            resetAnimationStartParams()
            startIndicatorAnimation()
        } else {
            resetParams()
        }
    }

    private fun resetParams() {
        val currentPosition = mLastPosition
        val nextPosition = getCurrentPosition()
//        Log.d(TAG, "resetParams----currentPosition=$currentPosition")

        if (isLoopLastPosition(currentPosition, nextPosition)) { //循环滚动到最后一位时
            resetLoopLastPositionParams(currentPosition, nextPosition)
        } else if(isLoopFirstPosition(currentPosition,nextPosition)){
            resetLoopFirstPositionParams(currentPosition, nextPosition)
        }else {
            if (getPageSize() >= notExceedPointCount) {
                resetParamsExceed(nextPosition)
            } else {
                resetParamsNotExceed(nextPosition)
            }
        }
    }

    private fun resetParamsNotExceed(currentPosition: Int) {
        setPositionParams(
            0,
            getNormalWidth(),
            getNormalHeight(),
            getAlphaMax(),
            indicator1CenterX,
            getNormalColor()
        )
        setPositionParams(
            1,
            getNormalWidth(),
            getNormalHeight(),
            getAlphaMax(),
            indicator2CenterX,
            getNormalColor()
        )
        setPositionParams(
            2,
            getNormalWidth(),
            getNormalHeight(),
            getAlphaMax(),
            indicator3CenterX,
            getNormalColor()
        )
        setPositionParams(
            3,
            getNormalWidth(),
            getNormalHeight(),
            getAlphaMax(),
            indicator4CenterX,
            getNormalColor()
        )

        val centerX = if (currentPosition == 0) {
            indicator1CenterX
        } else if (currentPosition == 1) {
            indicator2CenterX
        } else if (currentPosition == 2) {
            indicator3CenterX
        } else if (currentPosition == 3) {
            indicator4CenterX
        } else {
            indicator4CenterX
        }
        setPositionParams(
            currentPosition,
            getSelectedWidth(),
            getSelectedHeight(),
            getAlphaMax(),
            centerX,
            getSelectedColor()
        )
    }

    private fun resetParamsExceed(currentPosition: Int) {
        resetCurrentPositionParams(currentPosition)
        if (currentPosition >= startScrollPosition && !isLastPosition(currentPosition)) {
            val startPosition = currentPosition - startScrollPosition
            val startRightPosition = startPosition + 1
            val currentLeftPosition = currentPosition - 1
            val endPosition = currentPosition + 1

            setPositionParams(
                currentPosition,
                getSelectedWidth(),
                getSelectedHeight(),
                getAlphaMax(),
                indicator4CenterX,
                getSelectedColor()
            )
            setPositionParams(
                startPosition,
                smallWith,
                smallHeight,
                getAlphaMax(),
                indicator1CenterX,
                getNormalColor()
            )
            setPositionParams(
                startRightPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator2CenterX,
                getNormalColor()
            )
            setPositionParams(
                currentLeftPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator3CenterX,
                getNormalColor()
            )
            setPositionParams(
                endPosition,
                smallWith,
                smallHeight,
                getAlphaMax(),
                indicator5CenterX,
                getNormalColor()
            )

            if (isNotExceedPointCount()) {
                setPositionParams(
                    notExceedPointCount - 1,
                    getNormalWidth(),
                    getNormalHeight(),
                    getAlphaMax(),
                    indicator5CenterX,
                    getNormalColor()
                )
            }
        } else if (isLastPosition(currentPosition) && !isNotExceedPointCount()) {
            val startPosition = currentPosition - startScrollPosition - 1
            val startRightPosition = startPosition + 1
            val currentLeftPosition = currentPosition - 1
            val currentLeftLeftPosition = currentLeftPosition - 1

            setPositionParams(
                currentPosition,
                getSelectedWidth(),
                getSelectedHeight(),
                getAlphaMax(),
                indicator5CenterX,
                getSelectedColor()
            )
            setPositionParams(
                startPosition,
                smallWith,
                smallHeight,
                getAlphaMax(),
                indicator1CenterX,
                getNormalColor()
            )
            setPositionParams(
                startRightPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator2CenterX,
                getNormalColor()
            )
            setPositionParams(
                currentLeftLeftPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator3CenterX,
                getNormalColor()
            )
            setPositionParams(
                currentLeftPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator4CenterX,
                getNormalColor()
            )

            if (isNotExceedPointCount()) {
                setPositionParams(
                    notExceedPointCount - 1,
                    getNormalWidth(),
                    getNormalHeight(),
                    getAlphaMax(),
                    indicator5CenterX,
                    getNormalColor()
                )
            }
        } else {
            if (currentPosition == showNumber - 2) { // 3
                resetRightPositionParams(currentPosition + 1)
                setOtherShowParams(currentPosition - 1, indicator3CenterX)
                setOtherShowParams(currentPosition - 2, indicator2CenterX)
                setOtherShowParams(currentPosition - 3, indicator1CenterX)

                if (isNotExceedPointCount()) {
                    setPositionParams(
                        notExceedPointCount - 1,
                        getNormalWidth(),
                        getNormalHeight(),
                        getAlphaMax(),
                        indicator5CenterX,
                        getNormalColor()
                    )
                }
            } else if (currentPosition == showNumber - 3) { // 2
                resetRightPositionParams(currentPosition + 2)
                setOtherShowParams(currentPosition + 1, indicator4CenterX)
                setOtherShowParams(currentPosition - 1, indicator2CenterX)
                setOtherShowParams(currentPosition - 2, indicator1CenterX)

                if (isNotExceedPointCount()) {
                    setPositionParams(
                        notExceedPointCount - 1,
                        getNormalWidth(),
                        getNormalHeight(),
                        getAlphaMax(),
                        indicator5CenterX,
                        getNormalColor()
                    )
                }
            } else if (currentPosition == showNumber - 4) { // 1
                resetRightPositionParams(currentPosition + 3)
                setOtherShowParams(currentPosition + 2, indicator4CenterX)
                setOtherShowParams(currentPosition + 1, indicator3CenterX)
                setOtherShowParams(currentPosition - 1, indicator1CenterX)

                if (isNotExceedPointCount()) {
                    setPositionParams(
                        notExceedPointCount - 1,
                        getNormalWidth(),
                        getNormalHeight(),
                        getAlphaMax(),
                        indicator5CenterX,
                        getNormalColor()
                    )
                }
            } else if (currentPosition == showNumber - 5) { // 0
                resetRightPositionParams(currentPosition + 4)
                setOtherShowParams(currentPosition + 3, indicator4CenterX)
                setOtherShowParams(currentPosition + 2, indicator3CenterX)
                setOtherShowParams(currentPosition + 1, indicator2CenterX)

                if (isNotExceedPointCount()) {
                    setPositionParams(
                        notExceedPointCount - 1,
                        getNormalWidth(),
                        getNormalHeight(),
                        getAlphaMax(),
                        indicator5CenterX,
                        getNormalColor()
                    )
                }
            } else if (currentPosition == showNumber - 1 && isNotExceedPointCount()) {
                val startPosition = 0
                val startRightPosition = startPosition + 1
                val startRightRightPosition = startPosition + 2
                val currentLeftPosition = currentPosition - 1

                setPositionParams(
                    startPosition,
                    getNormalWidth(),
                    getNormalHeight(),
                    getAlphaMax(),
                    indicator1CenterX,
                    getNormalColor()
                )
                setPositionParams(
                    currentPosition,
                    getSelectedWidth(),
                    getSelectedHeight(),
                    getAlphaMax(),
                    indicator5CenterX,
                    getSelectedColor()
                )
                setPositionParams(
                    currentLeftPosition,
                    getNormalWidth(),
                    getNormalHeight(),
                    getAlphaMax(),
                    indicator4CenterX,
                    getNormalColor()
                )
                setPositionParams(
                    startRightPosition,
                    getNormalWidth(),
                    getNormalHeight(),
                    getAlphaMax(),
                    indicator2CenterX,
                    getNormalColor()
                )
                setPositionParams(
                    startRightRightPosition,
                    getNormalWidth(),
                    getNormalHeight(),
                    getAlphaMax(),
                    indicator3CenterX,
                    getNormalColor()
                )
            }
        }
    }

    private fun resetAnimationStartParams() {

        val currentPosition = mLastPosition
        val nextPosition = getCurrentPosition()

//        Log.d(
//            TAG,
//            "resetAnimationStartParams---currentPosition=$currentPosition, nextPosition=$nextPosition"
//        )

        if (isLoopLastPosition(currentPosition, nextPosition)) { //循环滚动到最后一位时
            resetLoopLastPositionParams(currentPosition, nextPosition)
        } else if(isLoopFirstPosition(currentPosition,nextPosition)){
            resetLoopFirstPositionParams(currentPosition, nextPosition)
        }else {
            if (isAllowScroll()) {
                if (isLeftSlid()) {
                    resetLeftSlidScrollParams(currentPosition, nextPosition)
                } else { //右滑
                    resetRightSlidScrollParams(currentPosition, nextPosition)
                }
            } else { //不可滚动
                if (isLeftSlid()) {
                    resetLeftSlidParams(currentPosition, nextPosition)
                } else {
                    resetRightSlidParams(currentPosition, nextPosition)
                }
            }
        }
    }

    private fun isLoopFirstPosition(currentPosition: Int, nextPosition: Int): Boolean {
        val loopFirstPosition = currentPosition == 0 && isLastPosition(nextPosition)
//        Log.d(TAG,"isLoopFirstPosition----currentPosition=$currentPosition, nextPosition=$nextPosition")
        return loopFirstPosition
    }

    private fun isLoopLastPosition(currentPosition: Int, nextPosition: Int) =
        isLastPosition(currentPosition) && nextPosition == 0


    private fun resetLoopFirstPositionParams(currentPosition: Int, nextPosition: Int) {
        setPositionParams(currentPosition, getSelectedWidth(), getSelectedHeight(), getAlphaMax(), indicator1CenterX, getSelectedColor())

        setPositionParams(1, getNormalWidth(), getNormalHeight(), getAlphaMax(), indicator2CenterX, getNormalColor())
        setPositionParams(2, getNormalWidth(), getNormalHeight(), getAlphaMax(), indicator3CenterX, getNormalColor())
        setPositionParams(3, getNormalWidth(), getNormalHeight(), getAlphaMax(), indicator4CenterX, getNormalColor())
        if(currentPosition >= notExceedPointCount){
            setPositionParams(nextPosition, getNormalWidth(), getNormalHeight(), getAlphaMax(), indicator6CenterX, getNormalColor())
        }else{
            val centerX = if (getPageSize() == 2) {
                indicator2CenterX
            } else if (getPageSize() == 3) {
                indicator3CenterX
            } else if (getPageSize() == 4) {
                indicator4CenterX
            } else {
                indicator5CenterX
            }
            setPositionParams(nextPosition, getNormalWidth(), getNormalHeight(), getAlphaMax(), centerX, getNormalColor())

        }

    }

    private fun resetLoopLastPositionParams(currentPosition: Int, nextPosition: Int) {
        setPositionParams(nextPosition, getNormalWidth(), getNormalHeight(), getAlphaMax(), indicator1CenterX, getNormalColor())

        setPositionParams(1, getNormalWidth(), getNormalHeight(), getAlphaMax(), indicator2CenterX, getNormalColor())
        setPositionParams(2, getNormalWidth(), getNormalHeight(), getAlphaMax(), indicator3CenterX, getNormalColor())
        setPositionParams(3, getNormalWidth(), getNormalHeight(), getAlphaMax(), indicator4CenterX, getNormalColor())
        if(currentPosition >= notExceedPointCount){
            setPositionParams(currentPosition, getNormalWidth(), getNormalHeight(), getAlphaMin(), indicator6CenterX, getNormalColor())
            setShapeAllowDraw(currentPosition,false)
            setPositionParams(4,smallWith, smallHeight, getAlphaMax(), indicator5CenterX, getNormalColor())
        }else{
            val centerX = if (getPageSize() == 2) {
                indicator2CenterX
            } else if (getPageSize() == 3) {
                indicator3CenterX
            } else if (getPageSize() == 4) {
                indicator4CenterX
            } else {
                indicator5CenterX
            }
            setPositionParams(currentPosition, getSelectedWidth(), getSelectedHeight(), getAlphaMax(), centerX, getSelectedColor())

            setPositionParams(4, getNormalWidth(), getNormalHeight(), getAlphaMax(), indicator5CenterX, getNormalColor())
        }

    }

    private fun resetRightSlidParams(currentPosition: Int, nextPosition: Int) {
//        Log.d(
//            TAG,
//            "resetRightSlidParams---currentPosition=$currentPosition, nextPosition=$nextPosition, showNumber=$showNumber"
//        )
        if (getPageSize() >= notExceedPointCount) {
            resetRightSlidParamsExceed(currentPosition, nextPosition)
        } else {
            resetRightSlidParamsNotExceed(currentPosition, nextPosition)
        }
    }

    private fun resetRightSlidParamsNotExceed(currentPosition: Int, nextPosition: Int) {
        setPositionParams(
            0,
            getNormalWidth(),
            getNormalHeight(),
            getAlphaMax(),
            indicator1CenterX,
            getNormalColor()
        )
        setPositionParams(
            1,
            getNormalWidth(),
            getNormalHeight(),
            getAlphaMax(),
            indicator2CenterX,
            getNormalColor()
        )
        setPositionParams(
            2,
            getNormalWidth(),
            getNormalHeight(),
            getAlphaMax(),
            indicator3CenterX,
            getNormalColor()
        )
        setPositionParams(
            3,
            getNormalWidth(),
            getNormalHeight(),
            getAlphaMax(),
            indicator4CenterX,
            getNormalColor()
        )

        val centerX = if (currentPosition == 1) {
            indicator2CenterX
        } else if (currentPosition == 2) {
            indicator3CenterX
        } else if (currentPosition == 3) {
            indicator4CenterX
        } else {
            indicator4CenterX
        }
        setPositionParams(
            currentPosition,
            getSelectedWidth(),
            getSelectedHeight(),
            getAlphaMax(),
            centerX,
            getSelectedColor()
        )
    }

    private fun resetRightSlidParamsExceed(currentPosition: Int, nextPosition: Int) {
        if (currentPosition == showNumber - 1) { //4
            val startPosition = 0
            val startRightPosition = startPosition + 1
            val currentRightPosition = currentPosition + 1
            val nextLeftPosition = nextPosition - 1

            if (!isNotExceedPointCount()) {
                setPositionParams(
                    nextPosition,
                    getNormalWidth(),
                    getNormalHeight(),
                    getAlphaMax(),
                    indicator3CenterX,
                    getNormalColor()
                )
                setPositionParams(
                    nextLeftPosition,
                    getNormalWidth(),
                    getNormalHeight(),
                    getAlphaMax(),
                    indicator2CenterX,
                    getNormalColor()
                )
                setPositionParams(
                    currentPosition,
                    getSelectedWidth(),
                    getSelectedHeight(),
                    getAlphaMax(),
                    indicator4CenterX,
                    getSelectedColor()
                )
                setPositionParams(
                    startPosition,
                    smallWith,
                    smallWith,
                    getAlphaMax(),
                    indicator0CenterX,
                    getNormalColor()
                )
                setPositionParams(
                    startRightPosition,
                    smallWith,
                    smallWith,
                    getAlphaMax(),
                    indicator1CenterX,
                    getNormalColor()
                )
                resetEndPositionParams(currentRightPosition)
            } else {

                setPositionParams(
                    startPosition,
                    getNormalWidth(),
                    getNormalHeight(),
                    getAlphaMax(),
                    indicator1CenterX,
                    getNormalColor()
                )
                setPositionParams(
                    startRightPosition,
                    getNormalWidth(),
                    getNormalHeight(),
                    getAlphaMax(),
                    indicator2CenterX,
                    getNormalColor()
                )
                setPositionParams(
                    currentPosition,
                    getSelectedWidth(),
                    getSelectedHeight(),
                    getAlphaMax(),
                    indicator5CenterX,
                    getSelectedColor()
                )
                setPositionParams(
                    nextPosition,
                    getNormalWidth(),
                    getNormalHeight(),
                    getAlphaMax(),
                    indicator4CenterX,
                    getNormalColor()
                )
                setPositionParams(
                    nextLeftPosition,
                    getNormalWidth(),
                    getNormalHeight(),
                    getAlphaMax(),
                    indicator3CenterX,
                    getNormalColor()
                )
            }

        } else if (currentPosition == showNumber - 2) { // 3
            val startPosition = 0
            val startRightPosition = startPosition + 1
            val endPosition = showNumber - 1
            setPositionParams(
                nextPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator3CenterX,
                getNormalColor()
            )
            setPositionParams(
                currentPosition,
                getSelectedWidth(),
                getSelectedHeight(),
                getAlphaMax(),
                indicator4CenterX,
                getSelectedColor()
            )
            setPositionParams(
                startPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator1CenterX,
                getNormalColor()
            )
            setPositionParams(
                startRightPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator2CenterX,
                getNormalColor()
            )
            resetEndPositionParams(endPosition)

        } else if (currentPosition == showNumber - 3) { // 2
            setPositionParams(
                nextPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator2CenterX,
                getNormalColor()
            )
            setPositionParams(
                currentPosition,
                getSelectedWidth(),
                getSelectedHeight(),
                getAlphaMax(),
                indicator3CenterX,
                getSelectedColor()
            )
            setOtherShowParams(currentPosition + 1, indicator4CenterX)
            resetLeftPositionParams(0)

            val endPosition = showNumber - 1
            resetEndPositionParams(endPosition)
        } else if (currentPosition == showNumber - 4) { // 1
            setPositionParams(
                nextPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator1CenterX,
                getNormalColor()
            )
            setPositionParams(
                currentPosition,
                getSelectedWidth(),
                getSelectedHeight(),
                getAlphaMax(),
                indicator2CenterX,
                getSelectedColor()
            )
            setOtherShowParams(currentPosition + 1, indicator3CenterX)
            setOtherShowParams(currentPosition + 2, indicator4CenterX)

            val endPosition = showNumber - 1
            resetEndPositionParams(endPosition)

        } else if (currentPosition == showNumber - 5) { // 0
            setPositionParams(
                currentPosition,
                getSelectedWidth(),
                getSelectedHeight(),
                getAlphaMax(),
                indicator1CenterX,
                getSelectedColor()
            )
            setOtherShowParams(currentPosition + 1, indicator2CenterX)
            setOtherShowParams(currentPosition + 2, indicator3CenterX)
            setOtherShowParams(currentPosition + 3, indicator4CenterX)

            val endPosition = showNumber - 1
            resetEndPositionParams(endPosition)
        }
    }

    private fun resetLeftSlidParams(currentPosition: Int, nextPosition: Int) {
//        Log.d(
//            TAG,
//            "resetLeftSlidParams---currentPosition=$currentPosition, nextPosition=$nextPosition, notExceedPointCount=$notExceedPointCount"
//        )

        if (getPageSize() >= notExceedPointCount) {
            resetLeftSlidParamsExceed(currentPosition, nextPosition)
        } else {
            resetLeftSlidParamsNotExceed(currentPosition, nextPosition)
        }
    }

    private fun resetLeftSlidParamsNotExceed(currentPosition: Int, nextPosition: Int) {
        setPositionParams(
            0,
            getNormalWidth(),
            getNormalHeight(),
            getAlphaMax(),
            indicator1CenterX,
            getNormalColor()
        )
        setPositionParams(
            1,
            getNormalWidth(),
            getNormalHeight(),
            getAlphaMax(),
            indicator2CenterX,
            getNormalColor()
        )
        setPositionParams(
            2,
            getNormalWidth(),
            getNormalHeight(),
            getAlphaMax(),
            indicator3CenterX,
            getNormalColor()
        )
        setPositionParams(
            3,
            getNormalWidth(),
            getNormalHeight(),
            getAlphaMax(),
            indicator4CenterX,
            getNormalColor()
        )
        val centerX = if (currentPosition == 0) {
            indicator1CenterX
        } else if (currentPosition == 1) {
            indicator2CenterX
        } else if (currentPosition == 2) {
            indicator3CenterX
        } else {
            indicator3CenterX
        }
        setPositionParams(
            currentPosition,
            getSelectedWidth(),
            getSelectedHeight(),
            getAlphaMax(),
            centerX,
            getSelectedColor()
        )
    }

    private fun resetLeftSlidParamsExceed(currentPosition: Int, nextPosition: Int) {
        resetCurrentPositionParams(currentPosition)
        resetNextPositionParams(nextPosition)
        if (currentPosition == 0) {
            val endPosition = showNumber - 1
            val nextRightPosition = nextPosition + 1
            val nextRightRightPosition = nextRightPosition + 1
            setPositionParams(
                currentPosition,
                getSelectedWidth(),
                getSelectedHeight(),
                getAlphaMax(),
                indicator1CenterX,
                getSelectedColor()
            )
            setPositionParams(
                nextPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator2CenterX,
                getNormalColor()
            )
            setPositionParams(
                nextRightPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator3CenterX,
                getNormalColor()
            )
            setPositionParams(
                nextRightRightPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator4CenterX,
                getNormalColor()
            )
            resetEndPositionParams(endPosition)

        } else if (currentPosition == 1) {
            val startPosition = 0
            val endPosition = showNumber - 1
            val nextRightPosition = nextPosition + 1

            setPositionParams(
                startPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator1CenterX,
                getNormalColor()
            )
            setPositionParams(
                currentPosition,
                getSelectedWidth(),
                getSelectedHeight(),
                getAlphaMax(),
                indicator2CenterX,
                getSelectedColor()
            )
            setPositionParams(
                nextPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator3CenterX,
                getNormalColor()
            )
            setPositionParams(
                nextRightPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator4CenterX,
                getNormalColor()
            )
            resetEndPositionParams(endPosition)

        } else if (currentPosition == 2) {
            val startPosition = 0
            val startRightPosition = startPosition + 1
            val endPosition = showNumber - 1
            setPositionParams(
                startPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator1CenterX,
                getNormalColor()
            )
            setPositionParams(
                startRightPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator2CenterX,
                getNormalColor()
            )
            setPositionParams(
                currentPosition,
                getSelectedWidth(),
                getSelectedHeight(),
                getAlphaMax(),
                indicator3CenterX,
                getSelectedColor()
            )
            setPositionParams(
                nextPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator4CenterX,
                getNormalColor()
            )

            resetEndPositionParams(endPosition)

        } else if (currentPosition == 3) {
            val startPosition = 0
            val startRightPosition = startPosition + 1
            val startRightRightPosition = startPosition + 2

            setPositionParams(
                startPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator1CenterX,
                getNormalColor()
            )
            setPositionParams(
                startRightPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator2CenterX,
                getNormalColor()
            )
            setPositionParams(
                startRightRightPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator3CenterX,
                getNormalColor()
            )
            setPositionParams(
                currentPosition,
                getSelectedWidth(),
                getSelectedHeight(),
                getAlphaMax(),
                indicator4CenterX,
                getSelectedColor()
            )

            resetEndPositionParams(nextPosition)

        }
    }

    private fun resetEndPositionParams(position: Int) {
        if (isNotExceedPointCount()) {
            setPositionParams(
                position,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator5CenterX,
                getNormalColor()
            )
        } else {
            setPositionParams(
                position,
                smallWith,
                smallHeight,
                getAlphaMax(),
                indicator5CenterX,
                getNormalColor()
            )
        }
    }

    private fun resetRightSlidScrollParams(currentPosition: Int, nextPosition: Int) {
//        Log.d(
//            TAG,
//            "resetRightSlidScrollParams--currentPosition=$currentPosition, nextPosition=$nextPosition"
//        )
        if (isLastPosition(currentPosition)) {
            val startPosition = mLastPosition - startScrollPosition - 1
            val startRightPosition = startPosition + 1

            val startRightRightPosition = startRightPosition + 1
            setPositionParams(
                nextPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator4CenterX,
                getNormalColor()
            )
            setPositionParams(
                currentPosition,
                getSelectedWidth(),
                getSelectedHeight(),
                getAlphaMax(),
                indicator5CenterX,
                getSelectedColor()
            )
            setPositionParams(
                startRightRightPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator3CenterX,
                getNormalColor()
            )
            setPositionParams(
                startRightPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator2CenterX,
                getNormalColor()
            )
            setPositionParams(
                startPosition,
                smallWith,
                smallHeight,
                getAlphaMax(),
                indicator1CenterX,
                getNormalColor()
            )
        } else {
            val startPosition = currentPosition - startScrollPosition
            val startRightPosition = startPosition + 1
            val startLeftPosition = startPosition - 1
            setPositionParams(
                nextPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator3CenterX,
                getNormalColor()
            )
            setPositionParams(
                currentPosition,
                getSelectedWidth(),
                getSelectedHeight(),
                getAlphaMax(),
                indicator4CenterX,
                getSelectedColor()
            )
            setPositionParams(
                startLeftPosition,
                smallWith,
                smallHeight,
                getAlphaMin(),
                indicator0CenterX,
                getNormalColor()
            )
            setPositionParams(
                startPosition,
                smallWith,
                smallHeight,
                getAlphaMax(),
                indicator1CenterX,
                getNormalColor()
            )
            setPositionParams(
                startRightPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator2CenterX,
                getNormalColor()
            )

            val currentRightPosition = currentPosition + 1
            setPositionParams(
                currentRightPosition,
                smallWith,
                smallHeight,
                getAlphaMax(),
                indicator5CenterX,
                getNormalColor()
            )
        }
    }

    private fun resetLeftSlidScrollParams(currentPosition: Int, nextPosition: Int) {
//        Log.d(
//            TAG,
//            "resetLeftSlidScrollParams--currentPosition=$currentPosition, nextPosition=$nextPosition"
//        )
        val startPosition = currentPosition - startScrollPosition
        val startRightPosition = startPosition + 1
        val currentPrevPosition = currentPosition - 1

        setPositionParams(
            currentPosition,
            getSelectedWidth(),
            getSelectedHeight(),
            1f,
            indicator4CenterX,
            getSelectedColor()
        )
        if (currentPosition == startScrollPosition) {
            setPositionParams(
                startPosition,
                getNormalWidth(),
                getNormalHeight(),
                1f,
                indicator1CenterX,
                getNormalColor()
            )
        } else {
            setPositionParams(
                startPosition,
                smallWith,
                smallHeight,
                1f,
                indicator1CenterX,
                getNormalColor()
            )
        }

        setPositionParams(
            startRightPosition,
            getNormalWidth(),
            getNormalHeight(),
            1f,
            indicator2CenterX,
            getNormalColor()
        )
        setPositionParams(
            currentPrevPosition,
            getNormalWidth(),
            getNormalHeight(),
            1f,
            indicator3CenterX,
            getNormalColor()
        )

        if (isLastPrePosition(currentPosition)) {
            val currentLeftPosition = currentPosition - 1
            setPositionParams(
                currentLeftPosition,
                getNormalWidth(),
                getNormalHeight(),
                getAlphaMax(),
                indicator3CenterX,
                getNormalColor()
            )
            setPositionParams(
                nextPosition,
                getNormalWidth(),
                getNormalHeight(),
                1f,
                indicator5CenterX,
                getNormalColor()
            )
        } else if (isLastPosition(currentPosition)) {
            val startRightRightPosition = startRightPosition + 1
            setPositionParams(
                startRightRightPosition,
                getNormalWidth(),
                getNormalHeight(),
                1f,
                indicator3CenterX,
                getNormalColor()
            )
        } else {
            val nextNextPosition = nextPosition + 1
            setPositionParams(
                nextNextPosition,
                smallWith,
                smallHeight,
                0f,
                indicator6CenterX,
                getNormalColor()
            )
            setPositionParams(
                nextPosition,
                smallWith,
                smallHeight,
                1f,
                indicator5CenterX,
                getNormalColor()
            )
        }
    }

    private fun resetLeftPrevPosition(position: Int) {
        setShapeWidth(position, getNormalWidth())
        setShapeHeight(position, getNormalHeight())
        setShapeAlpha(position, 1f)
        setShapeAllowDraw(position, true)
        setShapeCenterX(position, indicator2CenterX)
//        Log.d(TAG, "resetLeftPrevPosition----shape=${getShape(position)}")
    }

    private fun resetNextRightPosition(position: Int) {
        setShapeWidth(position, smallWith)
        setShapeHeight(position, smallHeight)
        setShapeAlpha(position, 0f)
        setShapeAllowDraw(position, true)
        setShapeCenterX(position, indicator6CenterX)
//        Log.d(TAG, "resetNextRightPosition----shape=${getShape(position)}")
    }

    private fun setOtherShowParams(position: Int, centerX: Float) {
//        Log.d(TAG, "setOtherShowParams---position=$position, centerX=$centerX")
        setShapeAlpha(position, 1f)
        setShapeAllowDraw(position, true)
        setShapeWidth(position, getNormalWidth())
        setShapeHeight(position, getNormalHeight())
        setShapeCenterX(position, centerX)
    }

    private fun resetRightPositionParams(position: Int) {
        setShapeAlpha(position, 1f)
        setShapeAllowDraw(position, true)
        setShapeCenterX(position, indicator5CenterX)
        setShapeWidth(position, smallWith)
        setShapeHeight(position, smallHeight)
//        Log.d(TAG, "resetRightPositionParams---shape=${getShape(position)}")
    }

    private fun resetLeftPositionParams(position: Int) {
        setShapeAlpha(position, 1f)
        setShapeAllowDraw(position, true)
        setShapeCenterX(position, indicator1CenterX)
        if (isAllowScroll()) {
            setShapeWidth(position, smallWith)
            setShapeHeight(position, smallHeight)
            if (isRightSlid()) {
                setShapeAlpha(position, 0f)
            }
        } else {
            setShapeWidth(position, getNormalWidth())
            setShapeHeight(position, getNormalHeight())
        }
//        Log.d(TAG, "resetLeftPositionParams---shape=${getShape(position)}")
    }

    private fun resetNextPositionParams(position: Int) {
        setShapeWidth(position, getNormalWidth())
        setShapeHeight(position, getNormalHeight())
        setShapeAlpha(position, 1f)
        setShapeAllowDraw(position, true)
        if (isAllowScroll()) {
            if (isLeftSlid()) {
                setShapeCenterX(position, indicator5CenterX)
            } else {
                setShapeCenterX(position, indicator4CenterX)
            }
        } else {
            if (isLeftSlid()) {
                if (position == 1) {
                    setShapeCenterX(position, indicator2CenterX)
                } else if (position == 2) {
                    setShapeCenterX(position, indicator3CenterX)
                } else if (position == 3) {
                    setShapeCenterX(position, indicator4CenterX)
                }
            } else {
                if (position == 0) {
                    setShapeCenterX(position, indicator1CenterX)
                } else if (position == 1) {
                    setShapeCenterX(position, indicator2CenterX)
                } else if (position == 2) {
                    setShapeCenterX(position, indicator3CenterX)
                } else if (position == 3) {
                    setShapeCenterX(position, indicator4CenterX)
                }
            }
        }
//        Log.d(TAG, "resetNextPositionParams---shape=${getShape(position)}")
    }

    private fun resetCurrentPositionParams(position: Int) {
        setShapeWidth(position, getSelectedWidth())
        setShapeHeight(position, getSelectedHeight())
        setShapeAlpha(position, 1f)
        setShapeAllowDraw(position, true)
        setShapeColor(position, getSelectedColor())

        if (isAllowScroll()) {
            setShapeCenterX(position, indicator4CenterX)
        } else if (position == 0) {
            setShapeCenterX(position, indicator1CenterX)
        } else if (position == 1) {
            setShapeCenterX(position, indicator2CenterX)
        } else if (position == 2) {
            setShapeCenterX(position, indicator3CenterX)
        } else if (position == 3) {
            setShapeCenterX(position, indicator4CenterX)
        } else {
            setShapeCenterX(position, indicator5CenterX)
        }
//        Log.d(TAG, "resetCurrentPositionParams---shape=${getShape(position)}")
    }


    override fun getAnimations(): MutableList<Animator> {

        val animators = mutableListOf<Animator>()


        val currentPosition = mLastPosition
        val nextPosition = getCurrentPosition()

        CommonLogUtils.logI(
            TAG,
            "getAnimations--currentPosition=$currentPosition, nextPosition=$nextPosition, showNumber=$showNumber"
        )

        if(isLoopLastPosition(currentPosition,nextPosition)){
            animators.addAll(buildLoopLastPositionAnimators(currentPosition, nextPosition))
        }else if(isLoopFirstPosition(currentPosition,nextPosition)){
            animators.addAll(buildLoopFirstPositionAnimators(currentPosition, nextPosition))
        }else{
            if (isAllowScroll()) {
                if (isLeftSlid()) {
                    animators.addAll(buildLeftSlidScrollAnimators(currentPosition, nextPosition))
                } else { //右滑
                    animators.addAll(buildRightSlidScrollAnimators(currentPosition, nextPosition))
                }
            } else {
                if (isRightSlid()) {
                    animators.addAll(buildRightSlidAnimators(currentPosition, nextPosition))
                } else {
                    animators.addAll(buildLeftSlidAnimators(currentPosition, nextPosition))
                }
            }
        }

        return animators
    }

    private fun buildLoopLastPositionAnimators(
        currentPosition: Int,
        nextPosition: Int
    ): Collection<Animator> {
//        Log.d(
//            TAG,
//            "buildLoopLastPositionAnimators----currentPosition=$currentPosition, nextPosition=$nextPosition"
//        )
        val animators = mutableListOf<Animator>()

        animators.addAll(buildNextNotScrollAnimations(nextPosition))
        if(currentPosition < notExceedPointCount){
            animators.add(MultiIndicatorAnimaBuilder.sizeSelectedToNormalNotScroll(getSelectedHeight(), getNormalHeight()) { setShapeHeight(currentPosition, it) })
            animators.add(MultiIndicatorAnimaBuilder.colorNotScroll(getSelectedColor(), getNormalColor()) { setShapeColor(currentPosition, it) })
            animators.add(
                MultiIndicatorAnimaBuilder.sizeSelectedToNormalNotScroll(getSelectedWidth(), getNormalWidth()
            ) { setShapeWidth(currentPosition, it) })
        }
        return animators
    }

    private fun buildLoopFirstPositionAnimators(
        currentPosition: Int,
        nextPosition: Int
    ): Collection<Animator> {
//        Log.d(
//            TAG,
//            "buildLoopFirstPositionAnimators----currentPosition=$currentPosition, nextPosition=$nextPosition"
//        )
        val animators = mutableListOf<Animator>()

        if(getPageSize() > notExceedPointCount){
            animators.add(
                MultiIndicatorAnimaBuilder.sizeSelectedToNormalNotScroll(
                    getSelectedWidth(),
                    smallWith
                ) { setShapeWidth(currentPosition, it) })
            animators.add(
                MultiIndicatorAnimaBuilder.sizeSelectedToNormalNotScroll(
                    getSelectedHeight(),
                   smallHeight
                ) { setShapeHeight(currentPosition, it) })
        }else{
            animators.add(
                MultiIndicatorAnimaBuilder.sizeSelectedToNormalNotScroll(
                    getSelectedWidth(),
                    getNormalWidth()
                ) { setShapeWidth(currentPosition, it) })
            animators.add(
                MultiIndicatorAnimaBuilder.sizeSelectedToNormalNotScroll(
                    getSelectedHeight(),
                    getNormalHeight()
                ) { setShapeHeight(currentPosition, it) })
        }

        animators.add(
            MultiIndicatorAnimaBuilder.colorNotScroll(
                getSelectedColor(),
                getNormalColor()
            ) { setShapeColor(currentPosition, it) })

        animators.addAll(buildNextNotScrollAnimations(nextPosition))
        return animators

    }

    private fun buildLeftSlidAnimators(
        currentPosition: Int,
        nextPosition: Int
    ): Collection<Animator> {
//        Log.d(
//            TAG,
//            "buildLeftSlidAnimators----currentPosition=$currentPosition, nextPosition=$nextPosition"
//        )

        val animators = mutableListOf<Animator>()

        animators.addAll(buildCurrentNotScrollAnimations(currentPosition))
        animators.addAll(buildNextNotScrollAnimations(nextPosition))
        return animators
    }

    private fun buildRightSlidAnimatorsNotExceed(
        currentPosition: Int,
        nextPosition: Int
    ): Collection<Animator> {
        val animators = mutableListOf<Animator>()
        animators.addAll(buildCurrentNotScrollAnimations(currentPosition))
        animators.addAll(buildNextNotScrollAnimations(nextPosition))
        return animators
    }

    private fun buildNextNotScrollAnimations(position: Int): Collection<Animator> {

        val animators = mutableListOf<Animator>()
        if (positionIsValid(position)) {
            animators.add(
                MultiIndicatorAnimaBuilder.sizeSelectedToNormalNotScroll(
                    getNormalWidth(),
                    getSelectedWidth()
                ) { setShapeWidth(position, it) })
            animators.add(
                MultiIndicatorAnimaBuilder.sizeSelectedToNormalNotScroll(
                    getNormalHeight(),
                    getSelectedHeight()
                ) { setShapeHeight(position, it) })
            animators.add(
                MultiIndicatorAnimaBuilder.colorNotScroll(
                    getNormalColor(),
                    getSelectedColor()
                ) { setShapeColor(position, it) })
        }
        return animators
    }

    private fun buildRightSlidAnimatorsExceed(
        currentPosition: Int,
        nextPosition: Int
    ): Collection<Animator> {
        val animators = mutableListOf<Animator>()
        if (currentPosition == showNumber - 1 && !isNotExceedPointCount()) {
            val startPosition = 0
            val startRightPosition = startPosition + 1
            val currentRightPosition = currentPosition + 1
            val nextLeftPosition = nextPosition - 1

            animators.add(
                MultiIndicatorAnimaBuilder.sizeSelectedToNormalNotScroll(
                    getSelectedWidth(),
                    smallWith
                ) { setShapeWidth(currentPosition, it) })
            animators.add(
                MultiIndicatorAnimaBuilder.sizeSelectedToNormal(
                    getSelectedHeight(),
                    smallHeight
                ) { setShapeHeight(currentPosition, it) })
            animators.add(buildColorAnimator(currentPosition, getSelectedColor(), getNormalColor()))

            animators.add(
                MultiIndicatorAnimaBuilder.sizeNormalToSelected(
                    getNormalWidth(),
                    getSelectedWidth()
                ) { setShapeWidth(nextPosition, it) })
            animators.add(
                MultiIndicatorAnimaBuilder.sizeNormalToSelected(
                    getNormalHeight(),
                    getSelectedHeight()
                ) { setShapeHeight(nextPosition, it) })
            animators.add(buildColorAnimator(nextPosition, getNormalColor(), getSelectedColor()))


            animators.add(
                MultiIndicatorAnimaBuilder.alpha(
                    getAlphaMin(),
                    getAlphaMax()
                ) { setShapeAlpha(startPosition, it) })
            animators.add(
                MultiIndicatorAnimaBuilder.sizeNormalToSmall(
                    smallWith,
                    getNormalWidth()
                ) { setShapeWidth(startPosition, it) })
            animators.add(
                MultiIndicatorAnimaBuilder.sizeNormalToSmall(
                    smallHeight,
                    getNormalHeight()
                ) { setShapeHeight(startPosition, it) })

            animators.add(
                MultiIndicatorAnimaBuilder.sizeNormalToSmall(
                    smallWith,
                    getNormalWidth()
                ) { setShapeWidth(startRightPosition, it) })
            animators.add(
                MultiIndicatorAnimaBuilder.sizeNormalToSmall(
                    smallHeight,
                    getNormalHeight()
                ) { setShapeHeight(startRightPosition, it) })

            animators.add(
                MultiIndicatorAnimaBuilder.alpha(
                    getAlphaMax(),
                    getAlphaMin()
                ) { setShapeAlpha(currentRightPosition, it) })

            buildScrollAnimation(startRightPosition)?.let { animators.add(it) }
            buildScrollAnimation(startPosition)?.let { animators.add(it) }
            buildScrollAnimation(currentRightPosition)?.let { animators.add(it) }
            buildScrollAnimation(nextLeftPosition)?.let { animators.add(it) }
            buildScrollAnimation(currentPosition)?.let { animators.add(it) }
            buildScrollAnimation(nextPosition)?.let { animators.add(it) }

        } else {
            animators.addAll(buildCurrentNotScrollAnimations(currentPosition))
            animators.addAll(buildNextNotScrollAnimations(nextPosition))
        }
        return animators
    }

    private fun buildCurrentNotScrollAnimations(position: Int): Collection<Animator> {

        val animators = mutableListOf<Animator>()
        animators.add(
            MultiIndicatorAnimaBuilder.sizeSelectedToNormalNotScroll(
                getSelectedWidth(),
                getNormalWidth()
            ) { setShapeWidth(position, it) })
        animators.add(
            MultiIndicatorAnimaBuilder.sizeSelectedToNormalNotScroll(
                getSelectedHeight(),
                getNormalHeight()
            ) { setShapeHeight(position, it) })
        animators.add(
            MultiIndicatorAnimaBuilder.colorNotScroll(
                getSelectedColor(),
                getNormalColor()
            ) { setShapeColor(position, it) })

        return animators
    }

    private fun buildRightSlidAnimators(
        currentPosition: Int,
        nextPosition: Int
    ): Collection<Animator> {
//        Log.d(
//            TAG,
//            "buildRightSlidAnimators----currentPosition=$currentPosition, nextPosition=$nextPosition"
//        )
        return if (getPageSize() >= notExceedPointCount) {
            buildRightSlidAnimatorsExceed(currentPosition, nextPosition)
        } else {
            buildRightSlidAnimatorsNotExceed(currentPosition, nextPosition)
        }
    }

    private fun buildRightSlidScrollAnimators(
        currentPosition: Int,
        nextPosition: Int
    ): Collection<Animator> {
//        Log.d(
//            TAG,
//            "buildRightSlidScrollAnimators----currentPosition=$currentPosition, nextPosition=$nextPosition"
//        )

        val animators = mutableListOf<Animator>()
        if (isLastPosition(currentPosition)) {
            animators.add(
                MultiIndicatorAnimaBuilder.sizeSelectedToNormalNotScroll(
                    getSelectedWidth(),
                    smallWith
                ) {
                    setShapeWidth(currentPosition, it)
                })
            animators.add(
                MultiIndicatorAnimaBuilder.sizeSelectedToNormalNotScroll(
                    getSelectedHeight(),
                    smallHeight
                ) {
                    setShapeHeight(currentPosition, it)
                })
            animators.add(
                buildColorAnimator(
                    currentPosition,
                    getSelectedColor(),
                    getNormalColor(),
                    50L
                )
            )

            animators.add(
                MultiIndicatorAnimaBuilder.sizeNormalToSelectedNotScroll(
                    getNormalWidth(),
                    getSelectedWidth()
                ) {
                    setShapeWidth(nextPosition, it)
                })
            animators.add(
                MultiIndicatorAnimaBuilder.sizeSelectedToNormalNotScroll(
                    getNormalHeight(),
                    getSelectedHeight()
                ) {
                    setShapeHeight(nextPosition, it)
                })
            animators.add(
                buildColorAnimator(
                    nextPosition,
                    getNormalColor(),
                    getSelectedColor(),
                    50L
                )
            )
        } else {
            animators.add(
                MultiIndicatorAnimaBuilder.sizeSelectedToNormal(
                getSelectedWidth(),
                smallWith
            ) {
                setShapeWidth(currentPosition, it)
            })
            animators.add(
                MultiIndicatorAnimaBuilder.sizeSelectedToNormal(
                    getSelectedHeight(),
                    smallHeight
                ) {
                    setShapeHeight(currentPosition, it)
                })
            animators.add(buildColorAnimator(currentPosition, getSelectedColor(), getNormalColor()))

            animators.add(
                MultiIndicatorAnimaBuilder.sizeNormalToSelected(
                    getNormalWidth(),
                    getSelectedWidth()
                ) {
                    setShapeWidth(nextPosition, it)
                })
            animators.add(
                MultiIndicatorAnimaBuilder.sizeNormalToSelected(
                    getNormalHeight(),
                    getSelectedHeight()
                ) {
                    setShapeHeight(nextPosition, it)
                })
            animators.add(buildColorAnimator(nextPosition, getNormalColor(), getSelectedColor()))

            val startPosition = currentPosition - startScrollPosition
            val startRightPosition = startPosition + 1
            val startLeftPosition = startPosition - 1
            val endPosition = currentPosition + 1

//            animators.add(MultiIndicatorAnimaBuilder.sizeNormalToSmall(smallWith,getNormalWidth()){
//                setShapeWidth(startRightPosition,it)
//            })
//            animators.add(MultiIndicatorAnimaBuilder.sizeNormalToSmall(smallHeight,getNormalHeight()){
//                setShapeHeight(startRightPosition,it)
//            })

            animators.add(MultiIndicatorAnimaBuilder.alpha(smallWith, getNormalWidth()) {
                setShapeWidth(startPosition, it)
            })
            animators.add(MultiIndicatorAnimaBuilder.alpha(smallHeight, getNormalHeight()) {
                setShapeHeight(startPosition, it)
            })

            animators.add(MultiIndicatorAnimaBuilder.alpha(getAlphaMin(), getAlphaMax()) {
                setShapeAlpha(startLeftPosition, it)
            })

            animators.add(MultiIndicatorAnimaBuilder.alpha(getAlphaMax(), getAlphaMin()) {
                setShapeAlpha(endPosition, it)
            })

            buildScrollAnimation(currentPosition)?.let { animators.add(it) }
            buildScrollAnimation(nextPosition)?.let { animators.add(it) }
            buildScrollAnimation(startPosition)?.let { animators.add(it) }
            buildScrollAnimation(startLeftPosition)?.let { animators.add(it) }
            buildScrollAnimation(startRightPosition)?.let { animators.add(it) }
            buildScrollAnimation(endPosition)?.let { animators.add(it) }

        }
        return animators
    }

    private fun buildLeftSlidScrollAnimators(
        currentPosition: Int,
        nextPosition: Int
    ): Collection<Animator> {
//        Log.d(
//            TAG,
//            "buildLeftSlidScrollAnimators---currentPosition=$currentPosition, nextPosition=$nextPosition"
//        )

        val animators = mutableListOf<Animator>()

        val startPosition = mLastPosition - startScrollPosition
        val startRightPosition = startPosition + 1
        val currentLeftPosition = currentPosition - 1
        val endRightPosition = nextPosition + 1

        animators.addAll(buildCurrentPositionAnimation(currentPosition))
        animators.addAll(buildNextPositionAnimation(nextPosition))
        animators.addAll(buildEndRightPositionAnimation(endRightPosition))

        if (!isLastPrePosition(currentPosition) && !isLastPosition(currentPosition)) {
            animators.addAll(buildStartRightPositionAnimation(startRightPosition))
        }

        if (!isLastPrePosition(currentPosition) && !isLastPosition(currentPosition)) {
            animators.add(MultiIndicatorAnimaBuilder.alpha(1f, 0f) {
                setShapeAlpha(
                    startPosition,
                    it
                )
            })
        }
        if (currentPosition == startScrollPosition) {
            animators.add(
                MultiIndicatorAnimaBuilder.sizeNormalToSmall(
                    getNormalWidth(),
                    smallWith
                ) { setShapeWidth(startPosition, it) })
            animators.add(
                MultiIndicatorAnimaBuilder.sizeNormalToSmall(
                    getNormalHeight(),
                    smallHeight
                ) { setShapeHeight(startPosition, it) })
        }

        if (!isLastPrePosition(currentPosition) && !isLastPosition(currentPosition)) {
            buildScrollAnimation(currentPosition)?.let { animators.add(it) }
            buildScrollAnimation(nextPosition)?.let { animators.add(it) }
            buildScrollAnimation(startPosition)?.let { animators.add(it) }
            buildScrollAnimation(startRightPosition)?.let { animators.add(it) }
            buildScrollAnimation(currentLeftPosition)?.let { animators.add(it) }
            buildScrollAnimation(endRightPosition)?.let { animators.add(it) }
        }

        return animators
    }

    private fun buildColorAnimator(position: Int, from: Int, to: Int, duration: Long = 100L) =
        MultiIndicatorAnimaBuilder.color(from, to, duration) {
            setShapeColor(position, it)
        }

    private fun buildStartRightPositionAnimation(position: Int): Collection<Animator> {
        val animators = mutableListOf<Animator>()
//        Log.d(TAG, "buildLeftPrevPositionAnimation--shape=${getShape(position)}")
        val sizeFrom = if (isLeftSlid()) getNormalWidth() else smallWith
        val sizeTo = if (isLeftSlid()) smallWith else getNormalWidth()
        val widthAnimation = MultiIndicatorAnimaBuilder.sizeNormalToSmall(sizeFrom, sizeTo) {
            setShapeWidth(position, it)
        }

        val heightAnimation = MultiIndicatorAnimaBuilder.sizeNormalToSmall(sizeFrom, sizeTo) {
            setShapeHeight(position, it)
        }
        animators.add(widthAnimation)
        animators.add(heightAnimation)
        return animators
    }

    private fun buildStartPositionAnimation(position: Int): Collection<Animator> {
        val animators = mutableListOf<Animator>()
//        Log.d(TAG, "buildPosition1Animation--indicator=${getShape(position)}")

        val alphaAnimation = MultiIndicatorAnimaBuilder.alpha(1f, 0f) {
            setShapeAlpha(position, it)
        }
        animators.add(alphaAnimation)
        return animators
    }

    private fun buildEndRightPositionAnimation(position: Int): Collection<Animator> {
        val animators = mutableListOf<Animator>()
//        Log.d(TAG, "buildNextPrevPositionAnimation--indicator=${getShape(position)}")

        val alphaAnimation = MultiIndicatorAnimaBuilder.alpha(0f, 1f) {
            setShapeAlpha(position, it)
        }
        animators.add(alphaAnimation)
        return animators
    }

    private fun buildNextPositionAnimation(position: Int): Collection<Animator> {
//        Log.d(TAG, "buildNextPositionAnimation---position=$position--shape=${getShape(position)}")
        val animators = mutableListOf<Animator>()

        val widthAnimation = if (isAllowScroll()) {
            MultiIndicatorAnimaBuilder.sizeSmallToSelected(smallWith, getSelectedWidth()) {
                setShapeWidth(position, it)
            }
        } else {
            MultiIndicatorAnimaBuilder.sizeNormalToSelectedNotScroll(
                getNormalWidth(),
                getSelectedWidth()
            ) {
                setShapeWidth(position, it)
            }
        }

        val heightAnimation = if (isAllowScroll()) {
            MultiIndicatorAnimaBuilder.sizeSmallToSelected(smallHeight, getSelectedHeight()) {
                setShapeHeight(position, it)
            }
        } else {
            MultiIndicatorAnimaBuilder.sizeNormalToSelectedNotScroll(
                smallHeight,
                getSelectedHeight()
            ) {
                setShapeHeight(position, it)
            }
        }

        val colorDuration = if (isAllowScroll()) {
            100L
        } else {
            50L
        }
        val colorAnimation =
            MultiIndicatorAnimaBuilder.color(getNormalColor(), getSelectedColor(), colorDuration) {
                setShapeColor(position, it)
            }
        animators.add(widthAnimation)
        animators.add(heightAnimation)
        animators.add(colorAnimation)

        return animators
    }

    private fun buildCurrentPositionAnimation(position: Int): Collection<Animator> {
//        Log.d(TAG, "buildCurrentPositionAnimation--position=$position-shape=${getShape(position)}")
        val animators = mutableListOf<Animator>()
        val widthAnimation = if (isAllowScroll()) {
            MultiIndicatorAnimaBuilder.sizeSelectedToNormal(getSelectedWidth(), getNormalWidth()) {
                setShapeWidth(position, it)
            }
        } else {
            MultiIndicatorAnimaBuilder.sizeSelectedToNormalNotScroll(
                getSelectedWidth(),
                getNormalWidth()
            ) {
                setShapeWidth(position, it)
            }
        }

        val heightAnimation = if (isAllowScroll()) {
            MultiIndicatorAnimaBuilder.sizeSelectedToNormal(
                getSelectedHeight(),
                getNormalHeight()
            ) {
                setShapeHeight(position, it)
            }
        } else {
            MultiIndicatorAnimaBuilder.sizeSelectedToNormalNotScroll(
                getSelectedHeight(),
                getNormalHeight()
            ) {
                setShapeHeight(position, it)
            }
        }
        val colorDuration = if (isAllowScroll()) {
            100L
        } else {
            50L
        }
        val colorAnimation =
            MultiIndicatorAnimaBuilder.color(getSelectedColor(), getNormalColor(), colorDuration) {
                setShapeColor(position, it)
            }

        animators.add(widthAnimation)
        animators.add(heightAnimation)
        animators.add(colorAnimation)

        return animators
    }

    private fun buildScrollAnimation(position: Int): Animator? {
        if (!positionIsValid(position)) {
            return null
        }
        val distance = if (isLeftSlid()) {
            -scrollDistance
        } else {
            scrollDistance
        }
        val startShapeCenterX = getShapeCenterX(position)
        val scrollAnimation = MultiIndicatorAnimaBuilder.scroll(distance) {
            setShapeCenterX(position, startShapeCenterX + it)
        }
        return scrollAnimation
    }

    override fun getAnimationDuration(): Long {
        return 600L
    }

    override fun onCancelAnimation() {

    }

    override fun isAllowScroll(): Boolean {
        return super.isAllowScroll() && getPageSize() > notExceedPointCount
    }
}