package com.dfl.smartscene.widget.bannerview.shape

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import android.view.animation.PathInterpolator
import com.zhpan.indicator.base.BaseIndicatorView


/**
 * @className SmarSceneIndicatorView
 * <AUTHOR>
 * @version 1.0.0
 * @description 带位移的指示器(翻页符-多点翻页-页数超过点数)
 * @createTime 2025/01/09/10:00
 */
@Deprecated("MultiIndicatorView代替")
class ScrollIndicatorView : BaseIndicatorView {

    private var scrollAnimaDuration = IndicatorDefaultConfig.scrollAnimaDuration

    private val mPaint = Paint(Paint.ANTI_ALIAS_FLAG)

    private var pointMargin = 29f
    private var pointSize = 8f
    private var smallPointSize = 7f
    private var scrollDistance = pointMargin


    private var sliderWidth = 26f
    private var sliderHeight = 10f

    private var scrollDx: Float = 0f
    private var scrollRealDx: Float = 0f
    private var scrollDy: Float = 0f

    private var drawSelectedPointColor  = 0
    private var drawSelectedWidth = sliderWidth
    private var drawSelectedHeight = sliderHeight
    private var drawLastSelectedWidth = pointSize
    private var drawLastSelectedHeight = pointSize
    private var drawLastSelectedPointColor = 0

    private var mLastPosition = -1

    private var showNumber = 5
    private var hideNumber = 1
    private var startScrollPosition = 4

    private var animSet: AnimatorSet? = null


    //组件4
    private var startScrollIndicator: Indicator? = null

    private var indicatorPrevPrev: Int = -1

    //组件2
    private var indicatorPrev: Int = -1

    //组件5
    private var indicatorNext: Int = -1

    //组件6
    private var indicatorNextNext: Int = -1

    private var indicators: MutableList<Indicator> = mutableListOf<Indicator>()

    companion object {
        private const val TAG = "ScrollIndicatorView"
    }


    constructor(
        context: Context?,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0
    ) : super(context!!, attrs, defStyleAttr) {
        setNormalColor(IndicatorDefaultConfig.normalColor)
        setCheckedColor(IndicatorDefaultConfig.selectedColor)
        drawSelectedPointColor = getCheckedColor()
        drawLastSelectedPointColor = mIndicatorOptions.normalSliderColor
    }

    constructor(
        context: Context?,
        attrs: AttributeSet? = null
    ) : this(context!!, attrs, 0)

    constructor(context: Context) : this(context, null)

    private var scrollThreshold: Boolean = false

    private fun startIndicatorAnimation() {
        animSet = AnimatorSet()
        val animators = mutableListOf<Animator>()
        animators.addAll(buildSelectedAnimation())
        animators.addAll(buildUnselectedAnimation())


        if (isAllowScroll(getCurrentPosition())) {
            scrollThreshold = true
            if (isLeftSlid(getCurrentPosition(), mLastPosition)) {
                animators.add(buildScrollAnimation(0f, -scrollDistance))

                if (!isLastPosition(getCurrentPosition(), getPageSize())) {
                    if (positionIsValid(indicatorPrevPrev)) {
                        animators.add(buildPrePreAnimation())
                    }
                    if (positionIsValid(indicatorPrev)) {
                        animators.add(buildPreAnimation())
                    }
                }
                if (positionIsValid(indicatorNext)) {
                    animators.add(buildNextAnimation())
                }
            } else {
                if(isRightSlid(getCurrentPosition(), mLastPosition)){
                    animators.add(buildScrollAnimation(0f, scrollDistance))
                }
            }

        } else {
            scrollThreshold = false
        }

        animSet?.playTogether(animators)
        animSet?.start()
    }

    private fun isLastPosition(position: Int, pageSize: Int): Boolean {
        return position == (pageSize - 1)
    }

    private fun buildPrePreAnimation(): Animator {
        return  ValueAnimator.ofFloat(1.0f, 0f).apply {
            interpolator = PathInterpolator(0.33f, 0f, 0.67f, 1f)
            duration = 100L

            addUpdateListener {
                val alpha = it.animatedValue as Float
                if (positionIsValid(indicatorPrevPrev)) {
                    indicators[indicatorPrevPrev].setAlpha(alpha)
                }
            }
        }
    }

    private fun positionIsValid(position: Int) =
        position >= 0 && position < mIndicatorOptions.pageSize

    private fun buildPreAnimation(): Animator {
        return ValueAnimator.ofFloat(8.0f, 7.0f).apply {
            interpolator = PathInterpolator(0.22f, 1f, 0.36f, 1f)
            duration = 600
            addUpdateListener {
                val size = it.animatedValue as Float
                if (positionIsValid(indicatorPrev)) {
                    indicators[indicatorPrev].apply {
                        setWidth(size)
                        setHeight(size)
                    }
                }
            }
        }
    }

    private fun cancelAnimation() {
        animSet?.let {
            if (it.isRunning) {
                it.cancel()
                drawLastSelectedWidth = pointSize
                drawLastSelectedHeight = pointSize
                drawSelectedHeight = sliderHeight
                drawSelectedWidth = sliderWidth
                invalidate()
            }
        }
    }
    private fun buildScrollAnimation(fromValue: Float, toValue: Float): Animator {
//        Log.d(TAG, "buildScrollAnimation---fromValue=$fromValue, toValue=$toValue")
        val pathInterpolator = PathInterpolator(0.22f, 1f, 0.36f, 1f)
        val valueAnimator = ValueAnimator.ofFloat(fromValue, toValue).apply {
            interpolator = pathInterpolator
            duration = scrollAnimaDuration
            addUpdateListener {
                val value = it.animatedValue as Float

                if (isLeftSlid(getCurrentPosition(),mLastPosition)){
                    val number = mIndicatorOptions.currentPosition - startScrollPosition
                    val dx = number * pointMargin

                    if(isAllowScroll(getCurrentPosition())){
                        scrollDx = -dx + value
                        if(toValue.equals(value)) {
                            scrollRealDx = -dx + value
//                            Log.d(TAG,"buildScrollAnimation--isAllowLeftScroll---scrollDx=$scrollDx, scrollLeftDx=$scrollRealDx, value=$value,currentPosition=${getCurrentPosition()}, number=$number")
                        }
                    }
                    invalidate()
                }else if(isRightSlid(getCurrentPosition(),mLastPosition)){

                    if(isAllowScroll(getCurrentPosition())){
                        scrollDx = scrollRealDx + value
                        if(toValue.equals(value)){
                            scrollRealDx += value
//                            Log.d(TAG,"buildScrollAnimation--isAllowRightScroll---scrollDx=$scrollDx, scrollLeftDx=$scrollRealDx, value=$value,currentPosition=${getCurrentPosition()}")
                        }
                    }
                    if(scrollDx > 0){
                        scrollDx = 0f
                    }
                    invalidate()
                }

            }
            addListener(object: SimpleAnimatorListener(){
                override fun onAnimationCancel(animation: Animator) {
                    super.onAnimationCancel(animation)
//                    Log.d(TAG,"buildScrollAnimation--onAnimationCancel")
                    if (isLeftSlid(getCurrentPosition(),mLastPosition)){
                        val number = mIndicatorOptions.currentPosition - startScrollPosition
                        val dx = number * pointMargin
                        scrollRealDx = -dx + toValue
                    }else{
                        scrollRealDx += toValue
                    }
                    scrollDx = scrollRealDx
                    invalidate()
                }
            })
        }
        return valueAnimator
    }

    private fun buildNextAnimation(): Animator {
        return ValueAnimator.ofFloat(0.0f, 1.0f).apply {
            interpolator = PathInterpolator(0.33f, 0f, 0.67f, 1f)
            duration = 100
            addUpdateListener {
                val alpha = it.animatedValue as Float
                if (positionIsValid(indicatorNext)) {
                    indicators[indicatorNext].setAlpha(alpha)
                }
            }
        }
    }

    private fun buildUnselectedAnimation(): ArrayList<Animator> {
        val time = 100L
        val sizeInterpolator = PathInterpolator(0f, 0f, 1f, 1f)
        val sizeWidthAnimator = ValueAnimator.ofFloat(26f, 8f).apply {
            interpolator = sizeInterpolator
            duration = time
            addUpdateListener {
                drawLastSelectedWidth = it.animatedValue as Float
            }
        }
        val sizeHeightAnimator = ValueAnimator.ofFloat(10f, 8f).apply {
            interpolator = sizeInterpolator
            duration = time
            addUpdateListener {
                drawLastSelectedHeight = it.animatedValue as Float
            }
        }

        val colorAnimator = ValueAnimator.ofInt(getCheckedColor(), mIndicatorOptions.normalSliderColor).apply {
            interpolator = PathInterpolator(0f, 0f, 1f, 1f)
            duration = time
            addUpdateListener {
                val value = it.animatedValue as Int
                if(value == getCheckedColor() || value == mIndicatorOptions.normalSliderColor){
                    drawLastSelectedPointColor = value
                }
            }
        }
        return arrayListOf(sizeWidthAnimator, sizeHeightAnimator,colorAnimator)
    }

    private fun buildSelectedAnimation(): ArrayList<Animator> {
        val time = 333L
        val sizeInterpolator = PathInterpolator(0.65f, 0.00f, 0.35f, 1f)
        val sizeWidthAnimator = ValueAnimator.ofFloat(7f, 26f).apply {
            interpolator = sizeInterpolator
            duration = time
            addUpdateListener {
                drawSelectedWidth = it.animatedValue as Float
                invalidate()
            }
        }
        val sizeHeightAnimator = ValueAnimator.ofFloat(7f, 10f).apply {
            interpolator = sizeInterpolator
            duration = time
            addUpdateListener {
                drawSelectedHeight = it.animatedValue as Float
            }
        }

        val colorAnimator = ValueAnimator.ofInt(mIndicatorOptions.normalSliderColor, getCheckedColor()).apply {
            interpolator = PathInterpolator(0f, 0f, 1f, 1f)
            duration = 100L
            addUpdateListener {
                val value = it.animatedValue as Int
                if(mIndicatorOptions.normalSliderColor == value || getCheckedColor() == value){
                    drawSelectedPointColor = value
                }

            }
        }
        return arrayListOf(sizeWidthAnimator, sizeHeightAnimator, colorAnimator)
    }

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
        super.onPageScrolled(position, positionOffset, positionOffsetPixels)
    }

    override fun onPageSelected(position: Int) {
//        super.onPageSelected(position)
//        Log.d(TAG,"onPageSelected---position=$position, indicators=$indicators")
        if(indicators.isEmpty()){
            return
        }

        if (position != getCurrentPosition()) {
            cancelAnimation()
            mLastPosition = getCurrentPosition()
            setCurrentPosition(position)
            setAnimationIndicator(getCurrentPosition())
            startIndicatorAnimation()
        }

    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
//        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        var length = showNumber + hideNumber
//            if(mIndicatorOptions.pageSize < maxShowNumber){
//                length = mIndicatorOptions.pageSize
//            }
        val width =
            (pointMargin + pointSize) * length + pointMargin + paddingStart + paddingEnd
        val height = sliderHeight * 2 + paddingTop + paddingBottom
//        Log.d(TAG, "onMeasure---width=$width, height=$height")
        setMeasuredDimension(width.toInt(), height.toInt())

        if (getPageSize() > 1) {
            indicators.clear()
            for (i in 0 until getPageSize()) {
                val centerX = if(i < startScrollPosition){
                    (pointMargin + pointSize) * (i + 1) - pointSize/2f
                }else{
                    (pointMargin + smallPointSize) * (i + 1) - smallPointSize/2f
                }

                val indicator = Indicator()
                indicator.setCenterX(centerX)
                indicator.setCenterY(height / 2f)
                indicator.setPosition(i)
                indicator.setRectCorner(3f)

                if (i == getCurrentPosition()) {
                    indicator.setColor(getCheckedColor())
                } else {
                    indicator.setColor(mIndicatorOptions.normalSliderColor)
                }
                indicators.add(indicator)
            }

            setAnimationIndicator(getCurrentPosition())

//            Log.d(TAG, "onMeasure---pointCenterArr=$indicators")
        }

    }


    private fun setAnimationIndicator(position: Int) {
        if (indicators.size > startScrollPosition) {
            startScrollIndicator = indicators[startScrollPosition]
        }
        resetIndicators()

        if (isAllowScroll(position)) {
            if (isLeftSlid(position, mLastPosition)) {
                //正常滚动
                if (isNormalScrollPosition(position)) {
                    indicatorPrevPrev = position - 4
                    indicatorPrev = position - 3
                    indicatorNext = position + 1
                    indicatorNextNext = position + 2

                    for (i in indicatorPrev until indicatorNextNext) {
                        indicators[i].setAlpha(1f)
                        if(i < indicatorNext){
                            setIndicatorSize(i,pointSize,pointSize)
                        }
                    }
                } else if (isLastPrevPosition(position)) {
                    //倒数第二个位置
                    indicatorPrevPrev = position - 4
                    indicatorPrev = position - 3
                    indicatorNext = position + 1

                    for (i in indicatorPrevPrev..indicatorNext) {
                        indicators[i].setAlpha(1f)
                        if(i < indicators.size -1){
                            setIndicatorSize(i,pointSize,pointSize)
                        }
                    }
                } else {
                    //滚动到最后
                    indicatorPrevPrev = position - 4
                    indicatorPrev = position - 3

                    for (i in (indicatorPrevPrev - 1)..position) {
                        indicators[i].setAlpha(1f)
                        if(i > (indicatorPrevPrev - 1)){
                            setIndicatorSize(i,pointSize,pointSize)
                        }
                    }
                }
            } else {
                //向右滚动
                if(isLastPosition(position, mIndicatorOptions.pageSize)){
                    indicatorPrevPrev = position - 4
                    indicatorPrev = position - 3
                    for (i in indicatorPrevPrev until mIndicatorOptions.pageSize) {
                        indicators[i].setAlpha(1f)
                        if(i > indicatorPrevPrev){
                            setIndicatorSize(i,pointSize,pointSize)
                        }
                    }
                }else if(isLastPrevPosition(position)){
                    indicatorPrev = position - 3
                    indicatorNext = position + 1
                    for (i in indicatorPrev .. indicatorNext) {
                        indicators[i].setAlpha(1f)
                        if(i in (indicatorPrev + 1) until indicatorNext){
                            setIndicatorSize(i,pointSize,pointSize)
                        }
                    }
                }else{
                    indicatorPrevPrev = position - 4
                    indicatorPrev = position - 3
                    indicatorNext = position + 1
                    indicatorNextNext = position + 2

                    for (i in indicatorPrev until indicatorNextNext) {
                        indicators[i].setAlpha(1f)
                        if(i > indicatorPrev && i< (indicatorNextNext - 1)){
                            setIndicatorSize(i,pointSize,pointSize)
                        }
                    }
                }
            }
        } else {
            indicatorNextNext = showNumber

            for (i in 0 until showNumber) {
                indicators[i].setAlpha(1f)
                setIndicatorSize(i,pointSize,pointSize)
            }
            setIndicatorSize(0,smallPointSize,smallPointSize)
            setIndicatorSize(showNumber,smallPointSize,smallPointSize)
            scrollDx = 0f
        }
//        Log.d(
//            TAG,
//            "setAnimationIndicator---position=$position, indicatorPrevPrev=$indicatorPrevPrev, indicatorPrev=$indicatorPrev, indicatorNext=$indicatorNext, indicatorNextNext=$indicatorNextNext"
//        )
    }

    private fun resetIndicators() {
        indicatorPrevPrev = -1
        indicatorPrev = -1
        indicatorNext = -1
        indicatorNextNext = -1
        for (i in 0 until indicators.size) {
            indicators[i].setAlpha(0f)
            setIndicatorSize(i, smallPointSize, smallPointSize)
        }
    }

    private fun setIndicatorSize(position: Int, width: Float, height: Float){
        if(indicators.isNotEmpty() && position >=0 && position < indicators.size){
            indicators[position].setWidth(width)
            indicators[position].setHeight(height)
        }
    }

    private fun isLeftSlid(position: Int, lastPosition: Int): Boolean {
        val leftSlid = position > lastPosition
//        Log.d(TAG, "isLeftSlid---isLeftSlid=$leftSlid")
        return leftSlid
    }

    private fun isRightSlid(position: Int, lastPosition: Int): Boolean {
        val rightSlid = position < lastPosition
//        Log.d(TAG, "isRightSlid---rightSlid=$rightSlid")
        return rightSlid
    }

    //到时第二个位置
    private fun isLastPrevPosition(position: Int): Boolean {
        val lastPrevPosition = (position == (indicators.size - 2))
//        Log.d(TAG, "isLastPrevPosition---lastPrevPosition=$lastPrevPosition")
        return lastPrevPosition
    }

    private fun isNormalScrollPosition(position: Int) = position < (mIndicatorOptions.pageSize - 2)

    private fun isAllowScroll(position: Int): Boolean {
        val allowScroll = position >= startScrollPosition
//        Log.d(TAG, "isAllowScroll---allowScroll=$allowScroll, position=$position, startScrollPosition=$startScrollPosition")
        return allowScroll
    }


    override fun onDraw(canvas: Canvas?) {
        super.onDraw(canvas)
        canvas?.let {
            it.save()

            mPaint.reset()
            mPaint.isAntiAlias = true

            it.translate(scrollDx, scrollDy)

            val centerY = height / 2f
            for (i in 0 until indicators.size) {
                val indicator = indicators[i]
//                Log.d(TAG,"onDraw---lastPosition=$mLastPosition, currentPosition=${mIndicatorOptions.currentPosition}, nextPosition=$mNextPosition")
                if (mLastPosition == i) {
                    drawLastSelectedPoint(indicator, canvas)
                } else if (getCurrentPosition() == i) {
                    drawSelectPoint(indicator, canvas)
                }
//                else if(getCurrentPosition() > startScrollPosition){
//                    drawSmallPoint(indicator,canvas)
//                }
//                else if(positionIsValid(indicatorPrevPrev) && i<=indicatorPrevPrev){
//                    drawSmallPoint(indicator,canvas)
//                }
                else {
//                    if(mIndicatorOptions.currentPosition <= startScrollIndicator!!.getPosition() + 1){
                    drawNormalPoint(indicator, canvas)

                }
            }
        }
    }

    private fun drawLastSelectedPoint(indicator: Indicator, canvas: Canvas) {
        indicator.setColor(drawLastSelectedPointColor)

        val dx = indicator.getCenterX() - drawLastSelectedWidth / 2f
        val dy = indicator.getCenterY() - drawLastSelectedHeight / 2f
        indicator.setOffsetX(dx)
        indicator.setOffsetY(dy)
        indicator.setWidth(drawLastSelectedWidth)
        indicator.setHeight(drawLastSelectedHeight)
        indicator.draw(canvas, mPaint)
    }

    private fun drawSelectPoint(indicator: Indicator, canvas: Canvas) {
        indicator.setColor(drawSelectedPointColor)
        val dx = indicator.getCenterX() - drawSelectedWidth / 2f
        val dy = indicator.getCenterY() - drawSelectedHeight / 2f
        indicator.setOffsetX(dx)
        indicator.setOffsetY(dy)
        indicator.setWidth(drawSelectedWidth)
        indicator.setHeight(drawSelectedHeight)
        indicator.draw(canvas, mPaint)
    }

    private fun drawNormalPoint(indicator: Indicator, canvas: Canvas) {
        indicator.setColor(mIndicatorOptions.normalSliderColor)
        val dx = indicator.getCenterX() - pointSize/2f
        val dy = indicator.getCenterY() - pointSize/2f
        indicator.setOffsetX(dx)
        indicator.setOffsetY(dy)
        indicator.draw(canvas, mPaint)
    }

    private fun drawSmallPoint(indicator: Indicator, canvas: Canvas) {
        indicator.setColor(mIndicatorOptions.normalSliderColor)
        val dx = indicator.getCenterX() - smallPointSize / 2f
        val dy = indicator.getCenterY() - smallPointSize / 2f
        indicator.setOffsetX(dx)
        indicator.setOffsetY(dy)
        indicator.draw(canvas, mPaint)
    }


}