package com.dfl.smartscene.widget.seekbar;

import android.content.Context;
import android.content.res.Configuration;
import android.content.res.TypedArray;
import android.os.Handler;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.dfl.common.nightmode.widget.SkinCompatLinearLayout;
import com.dfl.smartscene.R;

import me.jessyan.autosize.utils.AutoSizeUtils;

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/11/25
 * desc :中控亮度、音量进度条
 * version: 1.0
 */
public class CustomSeekBar extends SkinCompatLinearLayout {
    private final Handler mHandler = new Handler();
    private SeekBar compatSeekBar;
    private TextView tvItem;
    private TextView tvLeftDesc;
    private TextView tvUnit;
    //进度最大值
    private int max;
    //进度最小值
    private int min;
    //进度
    private int mProgress;
    //自定义icon
    private int mIcon;
    //自定义进度条图标
    private int mSeekIcon = -1;
    private LinearLayout mLlContent;
    private ConstraintLayout mParentCsl;
    private OnSeekChangeListener onSeekChangeListener;
    private int mSeekBarWidth = 660;
    private Context mContext;
    private ImageView ivLeftIcon;
    private int mMultiple = 1;//颗粒度
    private String mUnit = "";//单位
    private String mLeftDesc = "";//刻度左边描述

    public CustomSeekBar(Context context) {
        super(context);
    }

    public CustomSeekBar(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CustomSeekBar(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mContext = context;
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.CustomSeekBar);
        //显示文本  tvItem
        max = typedArray.getInt(R.styleable.CustomSeekBar_max, 16);
        mProgress = typedArray.getInt(R.styleable.CustomSeekBar_defaultv, 0);
        mIcon = typedArray.getResourceId(R.styleable.CustomSeekBar_icon, -1);
        typedArray.recycle();
        initLayout(context);
    }

    @Override
    public void applySkin() {
        if (mContext != null) {
            removeAllViews();
            initLayout(mContext);
        }
    }

    private void initLayout(Context context) {
        View view = View.inflate(context, R.layout.scene_widget_seekbar_custom, this);
        tvItem = view.findViewById(R.id.tv_item);
        tvLeftDesc = view.findViewById(R.id.tv_left_desc_custom);
        tvUnit = view.findViewById(R.id.tv_unit_custom);
        ivLeftIcon = view.findViewById(R.id.iv_icon_custom);
        mLlContent = view.findViewById(R.id.ll_content_custom);
        mParentCsl = view.findViewById(R.id.csl_custom_parent);
        AppCompatImageView ivIcon = view.findViewById(R.id.iv_icon);
        if (mIcon != -1) {
            ivIcon.setImageResource(mIcon);
        }
        if (mSeekIcon != -1) {
            ivLeftIcon.setImageResource(mSeekIcon);
        }

        compatSeekBar = view.findViewById(R.id.seekbar);
        compatSeekBar.setMax(max);
        compatSeekBar.setMin(min);
        compatSeekBar.setProgress(mProgress);
        setSeekBarUnit(mUnit);
        tvItem.setText(String.valueOf(mProgress * mMultiple));
        setLeftDesc(mLeftDesc);
        translateTv(mProgress);
        compatSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                mProgress = progress;
                mHandler.post(() -> {//更新UI防止滑动卡顿
                    String degree = String.valueOf(progress * mMultiple);
                    tvItem.setText(degree);
                    translateTv(progress);
                });
                if (onSeekChangeListener != null) {
                    onSeekChangeListener.onProgressChanged(CustomSeekBar.this, seekBar, progress,
                            fromUser);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {

            }
        });

    }

    public void updateIcon(int icon) {
        if (icon != -1) {
            mSeekIcon = icon;
            ivLeftIcon.setImageResource(icon);
        }
    }

    private void translateTv(int progress) {
        if (compatSeekBar != null) {
            double numLen = ((double) (progress - min) / (max - min)) * mSeekBarWidth;
            ConstraintLayout.LayoutParams layoutParams =
                    (ConstraintLayout.LayoutParams) mLlContent.getLayoutParams(); //箭头
            int offset = -AutoSizeUtils.dp2px(mContext, 104);
            int margin = (int) numLen - offset;
            layoutParams.setMarginStart(margin);
            mLlContent.setLayoutParams(layoutParams);
        }
    }

    public void setOnSeekChangeListener(OnSeekChangeListener onSeekChangelistener) {
        this.onSeekChangeListener = onSeekChangelistener;
    }

    public void setMin(int min) {
        this.min = min;
        compatSeekBar.setMin(min);
    }

    public void setSeekBarWidth(int width) {
        this.mSeekBarWidth = width;
    }

    public void setTextSize(int size) {
        tvItem.setTextSize(size);
    }

    public void setSeekBarMultiple(int multiple) {
        this.mMultiple = multiple;
    }

    public void setSeekBarUnit(String unit) {
        this.mUnit = unit;
        if (tvUnit != null) {
            tvUnit.setText(mUnit);
            tvUnit.setTextColor(getResources().getColor(R.color.scene_color_text_1));
        }

    }

    public void setLeftDesc(String leftDesc) {
        this.mLeftDesc = leftDesc;
        if (tvLeftDesc != null)
            tvLeftDesc.setText(leftDesc);
        if (TextUtils.isEmpty(leftDesc)) {
            tvLeftDesc.setVisibility(GONE);
        } else {
            tvLeftDesc.setVisibility(VISIBLE);
        }
    }

    public int getMax() {
        return compatSeekBar.getMax();
    }

    public void setMax(int max) {
        this.max = max;
        compatSeekBar.setMax(max);
    }

    public int getProgress() {
        return compatSeekBar.getProgress();
    }

    public void setProgress(int progress) {
        this.mProgress = progress;
        compatSeekBar.setProgress(mProgress);
    }

    @Override
    protected void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        initLayout(mContext);
    }


    public interface OnSeekChangeListener {
        void onProgressChanged(View view, SeekBar seekBar, int progress, boolean fromUser);
    }
}
