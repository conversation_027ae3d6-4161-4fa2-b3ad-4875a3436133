package com.dfl.smartscene.widget.indicatorseekbar;


import androidx.annotation.ColorInt;

/**
 * for collecting each section track color
 * <p>
 * https://github.com/warkiz/IndicatorSeekBar
 * <p>
 *
 * @see <a href="https://www.paypal.me/BuyMeACupOfTeaThx">Zhuan<PERSON>uang<PERSON>uan's Paypal</a>, or
 * @see
 * <a href="https://github.com/warkiz/IndicatorSeekBar/blob/master/app/src/main/res/mipmap-xxhdpi/wechat_pay.png?raw=true">微信支付</a>, or
 * @see
 * <a href="https://github.com/warkiz/IndicatorSeekBar/blob/master/app/src/main/res/mipmap-xxhdpi/alipay.png?raw=true">支付宝</a>
 * <p>
 */
public interface ColorCollector {
    /**
     * to collect each section track's color
     *
     * @param colorIntArr ColorInt the container for each section tracks' color.
     *                    this array's length will auto equals section track' count.
     * @return True if apply color , otherwise no change
     */
    boolean collectSectionTrackColor(@ColorInt int[] colorIntArr);
}