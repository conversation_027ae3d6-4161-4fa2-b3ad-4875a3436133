package com.dfl.smartscene.widget.box

import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.graphics.*
import android.graphics.drawable.Drawable
import android.view.animation.LinearInterpolator

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/10/10
 * desc :
 * version: 1.0
 */
class FluidColorfulFrameDrawable : Drawable() {
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private lateinit var bounds: RectF
    private val rectF = RectF()
    private val defaultRadius: Float = 10f
    private val defaultStrokeWidth: Float = 5f

    private val colors: IntArray
    private val positions: FloatArray
    private val mtx = Matrix()

    private var degree: Float = 0f
        set(value) {
            field = value
            invalidateSelf()
        }

    init {
        paint.color = Color.RED
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = defaultStrokeWidth
        paint.maskFilter = BlurMaskFilter(defaultRadius, BlurMaskFilter.Blur.SOLID)

        colors = intArrayOf(
            Color.parseColor("#FFFF0000"),
            Color.parseColor("#FFFF0000"),
            Color.parseColor("#FFFF0000"),
            Color.parseColor("#FFFF0000"),
            Color.parseColor("#FFFFD700"),
            Color.parseColor("#FFFFD700"),
            Color.parseColor("#FFFFD700"),
            Color.parseColor("#FFFFD700"),
            Color.parseColor("#FFFFD700"),
            Color.parseColor("#FFFFD700"),
            Color.parseColor("#FFFF0000"),
            Color.parseColor("#FFFF0000"),
            Color.parseColor("#FFFF0000"),
            Color.parseColor("#FFFF0000"),
        )

        positions = floatArrayOf(
            0f, 0.02f, 0.25f, 0.27f, 0.37f, 0.39f, 0.49f, 0.51f, 0.53f, 0.75f, 0.77f,
            0.87f, 0.91f, 0.96f
        )
    }

    override fun setBounds(left: Int, top: Int, right: Int, bottom: Int) {
        super.setBounds(left, top, right, bottom)
        bounds = RectF(left.toFloat(), top.toFloat(), right.toFloat(), bottom.toFloat())
        rectF.left = defaultStrokeWidth / 2
        rectF.top = defaultStrokeWidth / 2
        rectF.right = bounds.width() - defaultStrokeWidth / 2
        rectF.bottom = bounds.height() - defaultStrokeWidth / 2

        paint.shader = SweepGradient(bounds.centerX(), bounds.centerY(), colors, positions)
    }

    override fun draw(canvas: Canvas) {
        mtx.reset()
        mtx.setRotate(degree, bounds.centerX(), bounds.centerY())
        (paint.shader as SweepGradient).setLocalMatrix(mtx)
        canvas.drawRoundRect(rectF, defaultRadius, defaultRadius, paint)
    }

    override fun setAlpha(alpha: Int) {
        paint.alpha = alpha
    }

    override fun setColorFilter(colorFilter: ColorFilter?) {
        paint.colorFilter = colorFilter
    }

    @Deprecated("Deprecated in Java")
    override fun getOpacity(): Int {
        return PixelFormat.TRANSLUCENT
    }

    private var fluidAnim: ObjectAnimator? = null

    fun startFluid() {
        fluidAnim = ObjectAnimator.ofFloat(this, "degree", 0f, 360f)
        fluidAnim?.duration = 1000L
        fluidAnim?.interpolator = LinearInterpolator()
        fluidAnim?.repeatCount = ValueAnimator.INFINITE
        fluidAnim?.start()
    }

    fun cancelFluid() {
        fluidAnim?.cancel()
    }
}