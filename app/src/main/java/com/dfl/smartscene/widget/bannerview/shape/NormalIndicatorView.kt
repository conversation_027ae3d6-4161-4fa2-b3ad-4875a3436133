package com.dfl.smartscene.widget.bannerview.shape

import android.animation.Animator
import android.animation.Animator.AnimatorListener
import android.animation.AnimatorSet
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.RectF
import android.util.AttributeSet
import android.view.animation.PathInterpolator


/**
 * @className SmarSceneIndicatorView
 * <AUTHOR>
 * @version 1.0.0
 * @description 多点翻页-页数未超过点数 View
 * @createTime 2025/01/09/10:00
 */
@Deprecated("两点使用 TwoIndicatorView， 多点使用MultiIndicatorView")
class NormalIndicatorView : SmartIndicatorBase {

    //颜色动画时间
    private var colorAnimaDuration = 50L

    //大小动画时间
    private var sizeAnimaDuration = 250L

    private var pointRadius = 4f

    private var corner = 3f


    private val selectedRect = RectF()
    private val lastSelectedRect = RectF()

    private var isAnimationRunning = false



    private var leftScrollThreshold = 0f
    private var rightScrollThreshold = 0f

    private var mNextPosition = -1


    private var selectDx: Float = 0f

    private var pointCenterArr: MutableList<Float> = mutableListOf<Float>()

    companion object {
        private const val TAG = "SmartSceneIndicatorView"
    }


    constructor(
        context: Context?,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0
    ) : super(context!!, attrs, defStyleAttr) {

    }

    constructor(
        context: Context?,
        attrs: AttributeSet? = null
    ) : this(context!!, attrs, 0)

    constructor(context: Context) : this(context, null)

    private fun startSliderAnimation() {
        animSet = AnimatorSet()
        val animators = mutableListOf<Animator>()
        animators.addAll(buildSelectedAnimation())
        animators.addAll(buildUnselectedAnimation())
        animSet?.playTogether(animators)
        animSet?.addListener(object : AnimatorListener {
            override fun onAnimationStart(animation: Animator) {
                isAnimationRunning = true
                selectDx = 0f
            }

            override fun onAnimationEnd(animation: Animator) {
                isAnimationRunning = false
                selectDx = 0f
            }

            override fun onAnimationCancel(animation: Animator) {
                isAnimationRunning = false
                selectDx = 0f
            }

            override fun onAnimationRepeat(animation: Animator) {

            }

        })
        animSet?.start()
    }


    private fun buildUnselectedAnimation(): ArrayList<Animator> {
        val sizeInterpolator = PathInterpolator(0.22f, 0.09f, 0.36f, 1f)
        val sizeWidthAnimator = ValueAnimator.ofFloat(26f, 8f).apply {
            interpolator = sizeInterpolator
            duration = sizeAnimaDuration
            addUpdateListener {
                drawLastSelectedWidth = it.animatedValue as Float
            }
        }
        val sizeHeightAnimator = ValueAnimator.ofFloat(10f, 8f).apply {
            interpolator = sizeInterpolator
            duration = sizeAnimaDuration
            addUpdateListener {
                drawLastSelectedHeight = it.animatedValue as Float
            }
        }

        val colorAnimator = ValueAnimator.ofInt(getSelectedColor(), getNormalColor()).apply {
            interpolator = PathInterpolator(0f, 0f, 1f, 1f)
            duration = colorAnimaDuration
            addUpdateListener {
                val value = it.animatedValue as Int
                if(value == getSelectedColor() || value ==getNormalColor()){
                    drawLastSelectedPointColor = value
                }
            }
        }

        drawLastSelectedPointColor = getNormalColor()
        return arrayListOf(sizeWidthAnimator, sizeHeightAnimator, colorAnimator)
    }

    private fun buildSelectedAnimation(): ArrayList<Animator> {
        val sizeInterpolator = PathInterpolator(0.22f, 0.09f, 0.36f, 1f)
        val sizeWidthAnimator = ValueAnimator.ofFloat(8f, 26f).apply {
            interpolator = sizeInterpolator
            duration = sizeAnimaDuration
            addUpdateListener {
                drawSelectedWidth = it.animatedValue as Float
                invalidate()
            }
        }
        val sizeHeightAnimator = ValueAnimator.ofFloat(8f, 10f).apply {
            interpolator = sizeInterpolator
            duration = sizeAnimaDuration
            addUpdateListener {
                drawSelectedHeight = it.animatedValue as Float
            }
        }

        val colorAnimator = ValueAnimator.ofInt(getNormalColor(), getSelectedColor()).apply {
            interpolator = PathInterpolator(0f, 0f, 1f, 1f)
            duration = colorAnimaDuration
            addUpdateListener {
                val value = it.animatedValue as Int
                if(value == getNormalColor() || value == getSelectedColor()){
                    drawSelectedPointColor = value
                }
            }
        }
        drawSelectedPointColor = getSelectedColor()
        return arrayListOf(sizeWidthAnimator, sizeHeightAnimator,colorAnimator)
    }

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
//        super.onPageScrolled(position, positionOffset, positionOffsetPixels)
    }

    override fun onPageSelected(position: Int) {
//        super.onPageSelected(position)
//        Log.d(TAG,"onPageSelected---position=$position, currentPosition=${getCurrentPosition()}")
        if (position != getCurrentPosition()) {
            cancelAnimation()
            mLastPosition = getCurrentPosition()
            setCurrentPosition(position)
            startSliderAnimation()
        }

    }

    override fun toggle(isAnimation: Boolean) {

    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        if (getPageSize() > 1) {
            //            var length = maxShowNumber
            //            if(mIndicatorOptions.pageSize < maxShowNumber){
            val length = getPageSize()
            //            }
            val width = (shapeMargin + pointRadius * 2) * length + shapeMargin
            val height = getSelectedHeight() * 2 + paddingTop + paddingBottom
//            Log.d(TAG, "onMeasure---width=$width, height=$height")
            setMeasuredDimension(width.toInt(), height.toInt())

            pointCenterArr.clear()
            for (i in 0 until mIndicatorOptions.pageSize) {
                val centerX = (shapeMargin + pointRadius * 2) * (i + 1) - pointRadius
                pointCenterArr.add(centerX)
            }
//            Log.d(TAG, "onMeasure---pointCenterArr=$pointCenterArr")
            leftScrollThreshold = shapeMargin + pointRadius
            rightScrollThreshold = width - shapeMargin - pointRadius

//            Log.d(
//                TAG,
//                "onMeasure---leftScrollThreshold=$leftScrollThreshold, rightScrollThreshold=$rightScrollThreshold"
//            )
        }else{
            val length = 2
            val width = (shapeMargin + pointRadius * 2) * length + shapeMargin
            val height = getSelectedHeight() * 2 + paddingTop + paddingBottom
            setMeasuredDimension(width.toInt(), height.toInt())
        }

    }

    override fun getAnimations(): MutableList<Animator> {
        return mutableListOf()
    }

    override fun getAnimationDuration(): Long {
        return 600L
    }

    override fun onCancelAnimation() {

    }


    override fun onDraw(canvas: Canvas?) {
        super.onDraw(canvas)
        canvas?.let {
            it.save()

            mPaint.reset()
            mPaint.isAntiAlias = true
            val centerY = height / 2f
            mPaint.color = getNormalColor()


            for (i in 0 until mIndicatorOptions.pageSize) {
                val centerX = pointCenterArr[i]

//                Log.d(TAG,"onDraw---lastPosition=$mLastPosition, currentPosition=${mIndicatorOptions.currentPosition}, nextPosition=$mNextPosition")
                if (mLastPosition == i) {
                    drawLastSelectedPoint(it, centerX, centerY)
                } else if (mIndicatorOptions.currentPosition == i) {
                    drawSelectPoint(it, centerX, centerY)
                } else {
                    drawNormalPoint(it, centerX, centerY)
                }
            }
        }
    }

    private fun drawLastSelectedPoint(canvas: Canvas, centerX: Float, centerY: Float) {
        canvas.save()
        mPaint.color = drawLastSelectedPointColor

        val dx = centerX - drawLastSelectedWidth / 2f
        val dy = centerY - drawLastSelectedHeight / 2f

        canvas.translate(dx, dy)
        lastSelectedRect.top = 0f
        lastSelectedRect.bottom = drawLastSelectedHeight
        lastSelectedRect.left = 0f
        lastSelectedRect.right = drawLastSelectedWidth

//        Log.i(TAG,"drawLastSelectedPoint--drawLastSelectedWidth=$drawLastSelectedWidth, drawLastSelectedHeight=$drawLastSelectedHeight")

        canvas.drawRoundRect(lastSelectedRect, corner, corner, mPaint)

        canvas.restore()
    }

    private fun drawSelectPoint(canvas: Canvas, centerX: Float, centerY: Float) {
        canvas.save()
        mPaint.color = drawSelectedPointColor

        val dx = centerX - drawSelectedWidth / 2f
        val dy = centerY - drawSelectedHeight / 2f
        canvas.translate(dx, dy)
        selectDx += dx

//        Log.d(TAG,"drawSelectPoint---centerX=$centerX, dx=$dx, dy=$dy")

        selectedRect.top = 0f
        selectedRect.bottom = drawSelectedHeight
        selectedRect.left = 0f
        selectedRect.right = drawSelectedWidth

//        Log.d(TAG,"drawSelectPoint---selectedRect=${selectedRect.toShortString()}")

        canvas.drawRoundRect(selectedRect, corner, corner, mPaint)
        canvas.restore()
    }

    private fun drawNormalPoint(canvas: Canvas, centerX: Float, centerY: Float) {
        canvas.save()
        mPaint.color = getNormalColor()
        val dx = centerX - pointRadius
        val dy = centerY - pointRadius
        canvas.translate(dx, dy)
        canvas.drawRoundRect(0f, 0f, pointRadius * 2, pointRadius * 2, corner, corner, mPaint)
        canvas.restore()
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        setNextPosition(mIndicatorOptions.currentPosition)
    }

    fun setNextPosition(currentPotion: Int) {
        mNextPosition = currentPotion + 1
        if (mNextPosition > (mIndicatorOptions.pageSize - 1)) {
            mNextPosition = -1
        }
    }

}