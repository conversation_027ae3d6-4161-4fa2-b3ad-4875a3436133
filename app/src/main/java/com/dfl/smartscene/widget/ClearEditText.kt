package com.dfl.smartscene.widget

import android.content.Context
import android.graphics.drawable.Drawable
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.View.OnFocusChangeListener
import android.view.View.OnTouchListener
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.DrawableCompat
import com.dfl.smartscene.R


/**
 *    author : Android 轮子哥
 *    github : https://github.com/getActivity/AndroidProject-Kotlin
 *    time   : 2018/10/18
 *    desc   : 带清除按钮的 EditText
 */
@Suppress("ClickableViewAccessibility")
class ClearEditText @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = android.R.attr.editTextStyle
) : RegexEditText(context, attrs, defStyleAttr), OnTouchListener, OnFocusChangeListener, TextWatcher {

    private val clearDrawable: Drawable = DrawableCompat.wrap(
        ContextCompat.getDrawable(
            context, R.drawable.scene_icon_common_edit_delete
        )!!
    )
    private var touchListener: OnTouchListener? = null
    private var focusChangeListener: OnFocusChangeListener? = null

    init {
        clearDrawable.setBounds(0, 0, clearDrawable.intrinsicWidth, clearDrawable.intrinsicHeight)
        setDrawableVisible(false)
        super.setOnTouchListener(this)
        super.setOnFocusChangeListener(this)
        super.addTextChangedListener(this)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        // 当尺寸改变时，强制重新设置CompoundDrawable以确保清除图标位置正确
        if (clearDrawable.isVisible) {
            // 先获取当前的drawable状态
            val drawables: Array<Drawable> = compoundDrawablesRelative
            // 强制重新设置，这会触发drawable位置的重新计算
            setCompoundDrawablesRelative(
                drawables[0], drawables[1], clearDrawable, drawables[3]
            )
            // 重新设置bounds确保图标尺寸正确
            clearDrawable.setBounds(0, 0, clearDrawable.intrinsicWidth, clearDrawable.intrinsicHeight)
        }
    }

    private fun setDrawableVisible(visible: Boolean) {
        if (clearDrawable.isVisible == visible) {
            return
        }
        clearDrawable.setVisible(visible, false)
        val drawables: Array<Drawable> = compoundDrawablesRelative
        setCompoundDrawablesRelative(
            drawables[0], drawables[1], if (visible) clearDrawable else null, drawables[3]
        )
    }

    override fun setOnFocusChangeListener(onFocusChangeListener: OnFocusChangeListener?) {
        focusChangeListener = onFocusChangeListener
    }

    override fun setOnTouchListener(onTouchListener: OnTouchListener?) {
        touchListener = onTouchListener
    }

    /**
     * [OnFocusChangeListener]
     */
    override fun onFocusChange(view: View, hasFocus: Boolean) {
        setDrawableVisible(hasFocus && !TextUtils.isEmpty(text))
        focusChangeListener?.onFocusChange(view, hasFocus)
    }

    /**
     * [OnTouchListener]
     */
    override fun onTouch(view: View, event: MotionEvent): Boolean {
        val x: Int = event.x.toInt()

        // 是否触摸了 Drawable
        var touchDrawable = false
        // 获取布局方向
        val layoutDirection: Int = layoutDirection
        if (layoutDirection == LAYOUT_DIRECTION_LTR) {
            // 从左往右
            touchDrawable = x > width - clearDrawable.intrinsicWidth - paddingEnd && x < width - paddingEnd
        } else if (layoutDirection == LAYOUT_DIRECTION_RTL) {
            // 从右往左
            touchDrawable = x > paddingStart && x < paddingStart + clearDrawable.intrinsicWidth
        }
        if (clearDrawable.isVisible && touchDrawable) {
            if (event.action == MotionEvent.ACTION_UP) {
                setText("")
                mCloseListener?.close()
            }
            return true
        }
        return touchListener?.onTouch(view, event) ?: false
    }

    fun setCloseListener(closeListener: OnCloseListener) {
        mCloseListener = closeListener
    }

    private var mCloseListener: OnCloseListener? = null

    interface OnCloseListener {
        fun close()
    }

    /**
     * [TextWatcher]
     */
    override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
        if (isFocused) {
            setDrawableVisible(s.isNotEmpty())
        }
    }

    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

    override fun afterTextChanged(s: Editable?) {}
}