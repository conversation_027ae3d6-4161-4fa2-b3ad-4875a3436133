package com.dfl.smartscene.widget.flodlayout;

import android.view.View;
import android.view.ViewGroup;

import com.dfl.androhermas.utils.LoggerUtil;
import com.dfl.android.common.global.GlobalConstant;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @describe 流布局适配器
 * @date on 2020/12/31
 */
public abstract class FlowAdapter<T> {

    private static final String TAG = GlobalConstant.GLOBAL_TAG + "FlowAdapter";
    /**
     * 暴露的删除点击事件
     */
    protected OnDeleteListener onDeleteListener;
    /**
     * 暴露的点击事件
     */
    protected OnClickListener onClickListener;
    private OnDataChangedListener onDataChangedListener;
    private List<T> data;

    /**
     * 子View创建
     *
     * @param parent
     * @param item
     * @param position
     * @return
     */
    public abstract View getView(ViewGroup parent, T item, int position);

    /**
     * 初始化View
     *
     * @param view
     * @param item
     * @param position
     * @return
     */
    public abstract void initView(View view, T item, int position);

    /**
     * 折叠View 默认不设置
     *
     * @return
     */
    public View foldView() {
        return null;
    }

    /**
     * 数据的数量
     *
     * @return
     */
    public int getCount() {
        return this.data == null ? 0 : this.data.size();
    }

    /**
     * 获取数据
     *
     * @return
     */
    public List<T> getData() {
        return data;
    }

    /**
     * 设置新数据
     *
     * @param data
     */
    public void setNewData(List<T> data) {
        this.data = data;
        notifyDataChanged();
    }

    /**
     * 添加数据
     *
     * @param data
     */
    public void addData(List<T> data) {
        if (this.data == null) {
            this.data = new ArrayList<>();
        }
        this.data.addAll(data);
        notifyDataChanged();
    }

    /**
     * 添加数据
     *
     * @param index
     * @param data
     */
    public void addData(int index, List<T> data) {
        if (this.data == null) {
            this.data = new ArrayList<>();
        }
        this.data.addAll(index, data);
        notifyDataChanged();
    }

    /**
     * 删除数据
     *
     * @param index
     */
    public void removeData(int index) {
        if (data == null || data.size() == 0 || data.size() < index) {
            LoggerUtil.d(TAG, "Data Is Null");
            return;
        }
        data.remove(index);
        notifyDataChanged();
    }

    /**
     * 添加数据
     *
     * @param data
     */
    public void addData(T data) {
        if (this.data == null) {
            this.data = new ArrayList<>();
        }
        this.data.add(data);
        notifyDataChanged();
    }

    /**
     * 获取指定位置的数据
     *
     * @param position
     * @return
     */
    public T getItem(int position) {
        if (this.data != null && position >= 0 && position < this.data.size()) {
            return this.data.get(position);
        }
        return null;
    }

    public void setOnClickListener(OnClickListener onClickListener) {
        this.onClickListener = onClickListener;
    }

    public void setOnDeleteListener(OnDeleteListener onDelete) {
        this.onDeleteListener = onDelete;
    }

    /**
     * 刷新数据
     */
    public void notifyDataChanged() {
        if (this.onDataChangedListener != null) {
            this.onDataChangedListener.onChanged();
        }
    }

    public void setOnDataChangedListener(OnDataChangedListener listener) {
        this.onDataChangedListener = listener;
    }


    public interface OnDeleteListener {
        void onDelete(String keyWord);
    }

    public interface OnClickListener {
        void onClick(String keyWord);
    }

    public interface OnDataChangedListener {
        void onChanged();
    }


}
