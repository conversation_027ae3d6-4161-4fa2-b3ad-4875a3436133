/*
Copyright 2017 zhpanvip The BannerViewPager Open Source Project

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
 */
package com.dfl.smartscene.widget.bannerview.utils;

import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Rect;
import android.graphics.Shader;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;

import com.dfl.smartscene.widget.bannerview.BannerViewPager;
import com.dfl.smartscene.widget.bannerview.BaseBannerAdapter;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import static com.dfl.smartscene.widget.bannerview.BaseBannerAdapter.MAX_VALUE;

/**
 * <pre>
 *   Created by zhangpan on 2019-08-14.
 *   Description:
 * </pre>
 */
public class BannerUtils {

    public enum BannerSlide {
        LEFT(0),
        RIGHT(1),
        STOP(2);

        private int value;

        public int getValue() {
            return value;
        }

        BannerSlide(int value) {
            this.value = value;
        }
    }

    private static boolean debugMode = false;

    private static final String TAG = "BVP";

    /** 是否左滑 */
    public static BannerSlide isLeftSlide = BannerSlide.STOP;

    public static void setDebugMode(boolean isDebug) {
        debugMode = isDebug;
    }

    public static boolean isDebugMode() {
        return debugMode;
    }

    public static int dp2px(float dpValue) {
        return (int) (0.5F + dpValue * Resources.getSystem().getDisplayMetrics().density);
    }

    public static void log(String tag, String msg) {
        if (isDebugMode()) {
            Log.e(tag, msg);
        }
    }

    public static void log(String msg) {
        if (isDebugMode()) {
            log(TAG, msg);
        }
    }

    /**
     * 在循环模式下{@link com.dfl.smartscene.widget.bannerview.BannerViewPager}会初始化一个item为
     * {@link BaseBannerAdapter#MAX_VALUE}的ViewPager2,并将当前position设置为ViewPager2
     * 的中间位置，因此，此时的position需要通过该方法进行转换为真实的position。
     *
     * @param position 当前position
     * @param pageSize 轮播图页面数
     * @return 真实的position
     */
    public static int getRealPosition(int position, int pageSize) {
        if (pageSize == 0) {
            return 0;
        }
        return (position + pageSize) % pageSize;
    }

    /**
     * @param pageSize 轮播图页面数
     * @return 轮播图初始位置
     */
    public static int getOriginalPosition(int pageSize) {
        return MAX_VALUE / 2 - ((MAX_VALUE / 2) % pageSize);
    }


    private static BannerScrollLeftRightBean bannerScrollLeftRightBean;

    /**
     * @param banner banner
     * @param leftPosition 左图数组index
     * @param positionOffset 比例
     * @param positionOffsetPixels 从0到2560 滑动的距离
     * @return 4个数值  左图真实index，右图真实index ， 左图数组index，右图数组index，-1为无效，
     */
    public static BannerScrollLeftRightBean getBannerScrollingIndex(BannerViewPager banner,
                                                                    int leftPosition,
                                                                    float positionOffset,
                                                                    int positionOffsetPixels) {

        RecyclerView recyclerView = (RecyclerView) banner.getViewPager().getChildAt(0);
        LinearLayoutManager lm = (LinearLayoutManager) recyclerView.getLayoutManager();
        if (lm == null) return new BannerScrollLeftRightBean(false);
        // 获取左图真实index
        int leftVisibleItemPosition = lm.findFirstVisibleItemPosition();
        // 获取右图真实index
        int rightVisibleItemPosition = lm.findLastVisibleItemPosition();
//        Log.e(
//                "sdhdfr", "前1:" + leftPosition + " ,后1转:" + BannerUtils.getRealPosition(
//                        rightVisibleItemPosition, 5
//                ) + ", 前:" + leftVisibleItemPosition + " ,后:" + rightVisibleItemPosition + " ,比例:" + positionOffset
//                + " ,路:" + positionOffsetPixels
//        );
        if (leftVisibleItemPosition == rightVisibleItemPosition) return new BannerScrollLeftRightBean(false);

        if (bannerScrollLeftRightBean == null) {
            bannerScrollLeftRightBean = new BannerScrollLeftRightBean(true, leftVisibleItemPosition,
                    rightVisibleItemPosition,
                    BannerUtils.getRealPosition(leftVisibleItemPosition, recyclerView.getAdapter().getItemCount()),
                    BannerUtils.getRealPosition(rightVisibleItemPosition, recyclerView.getAdapter().getItemCount()));
            return bannerScrollLeftRightBean;
        } else {
            if (bannerScrollLeftRightBean.getLeftVisiblePosition() == leftVisibleItemPosition &&
                    bannerScrollLeftRightBean.getRightVisiblePosition() == rightVisibleItemPosition) {
                return bannerScrollLeftRightBean;
            } else {
                bannerScrollLeftRightBean = new BannerScrollLeftRightBean(true, leftVisibleItemPosition,
                        rightVisibleItemPosition,
                        BannerUtils.getRealPosition(leftVisibleItemPosition, recyclerView.getAdapter().getItemCount()),
                        BannerUtils.getRealPosition(rightVisibleItemPosition,
                                recyclerView.getAdapter().getItemCount()));
                return bannerScrollLeftRightBean;
            }
        }


    }

    /** 根据可见index获取holderview */
    public static View getBannerHolderView(BannerViewPager banner, int visibleIndex) {
        if (visibleIndex == -1) return null;

        RecyclerView recyclerView = (RecyclerView) banner.getViewPager().getChildAt(0);
        RecyclerView.ViewHolder viewHolder = recyclerView.findViewHolderForAdapterPosition(visibleIndex);
        if (viewHolder == null) return null;
        return viewHolder.itemView;

    }

    public static Bitmap createMixImages(
            Bitmap bottomBitmap,
            Bitmap maskBitmap,          //640*850
            float xOffset,              //底层 从左到右的比例 ,控件的比例，也是图片的比例
            int mixViewWidth,           //mixview控件的宽度
            boolean isBottomLeft) {     //是否底层的左边

        if (bottomBitmap.getWidth() <= 0 || bottomBitmap.getHeight() <= 0) return null;
        //缩小倍数
        float bei = bottomBitmap.getWidth() / 2560.0f;

        //混合图的宽度 高度
        int mixBitmapWidth = (int) (mixViewWidth * bei);
        int mixBitmapHeight = bottomBitmap.getHeight();
        if (mixBitmapWidth <= 0 || mixBitmapHeight <= 0) return null;


        //Paint
        Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG);
        paint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.MULTIPLY));

        //画布
        Canvas mixCanvas = new Canvas();
        int saveLayerId = mixCanvas.saveLayer(0f, 0f, mixBitmapWidth, mixBitmapHeight, paint);
        Bitmap mixBitmap;

        //mixBitmapWidth：mix图宽度 0逐渐变大到640
//        Log.e("sdklejr",
//                "mixBitmap: (" + mixBitmapWidth + "," + mixBitmapHeight + ") maskBitmap:(" + maskBitmap.getWidth() +
//                        "," + maskBitmap.getHeight() + ") 底：(" + (int) (bottomBitmap.getWidth() * xOffset) + "," +
//                        bottomBitmap.getWidth() + "," + bottomBitmap.getHeight() + ")");
        if (!isBottomLeft) { //剪底层的右边
            // 剪切底层
            mixBitmap = Bitmap.createBitmap(bottomBitmap,
                    (int) (bottomBitmap.getWidth() * xOffset), 0,
                    mixBitmapWidth,
                    mixBitmapHeight);
            mixCanvas.setBitmap(mixBitmap);

            //蒙层
            mixCanvas.drawBitmap(maskBitmap,  //640*850
                    new Rect(0, 0, mixBitmapWidth, maskBitmap.getHeight()),
                    new Rect(0, 0, mixBitmapWidth, mixBitmapHeight),
                    paint);
            //mBitmap 的 (100,100) 到 (300,300) 区域拿出来参数1，自动缩放并画到屏幕的 (100,100) 到 (200,200) 区域参数2

        } else { //剪底层的左边
            // 剪切底层
            mixBitmap = Bitmap.createBitmap(bottomBitmap,
                    Math.max((int) (bottomBitmap.getWidth() * xOffset) - mixBitmapWidth, 0), 0,
                    mixBitmapWidth,
                    mixBitmapHeight);
            mixCanvas.setBitmap(mixBitmap);

            //蒙层
            mixCanvas.drawBitmap(maskBitmap,  //640*850
                    new Rect(maskBitmap.getWidth() - mixBitmapWidth, 0, maskBitmap.getWidth(), maskBitmap.getHeight()),
                    new Rect(0, 0, mixBitmapWidth, mixBitmapHeight),
                    paint);
        }

        // 清空图像混合模式
        paint.setXfermode(null);
        mixCanvas.restoreToCount(saveLayerId);
        return mixBitmap;

    }


    /** 创建轮播图滑动蒙层 */
    public static Bitmap createMaskBitmap(boolean isLeft) {
        int bitmapWidth = 640;
        int bitmapHeight = 850;
        Bitmap maskBitmap = Bitmap.createBitmap(bitmapWidth, bitmapHeight, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(maskBitmap);
        Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG);
        paint.setStyle(Paint.Style.FILL);
        LinearGradient linearGradient;
        if (isLeft) {
            linearGradient = new LinearGradient(
                    0,
                    bitmapHeight / 2.0f,
                    bitmapWidth / 2.0f,
                    bitmapHeight / 2.0f,
                    0xffffffff,
                    0x00ffffff,
                    Shader.TileMode.CLAMP
            );
        } else {
            linearGradient = new LinearGradient(
                    bitmapWidth,
                    bitmapHeight / 2.0f,
                    bitmapWidth / 2.0f,
                    bitmapHeight / 2.0f,
                    0xffffffff,
                    0x00ffffff,
                    Shader.TileMode.CLAMP
            );
        }

        paint.setShader(linearGradient);
        canvas.drawRoundRect(0f, 0f, bitmapWidth, bitmapHeight, 0f, 0f, paint);
        return maskBitmap;
    }

    /** 通过ImageView 获取 Bitmap */
    public static Bitmap getBitmapFromImageView(ImageView imageView) {
        // 获取 Drawable 对象
        Drawable drawable = imageView.getDrawable();
        // 检查 Drawable 是否为空
        if (drawable == null) {
            return null;
        }
        // 将 Drawable 转换为 Bitmap
        Bitmap bitmap;
        if (drawable instanceof BitmapDrawable) {
            bitmap = ((BitmapDrawable) drawable).getBitmap();
        } else {
            // 如果不是 BitmapDrawable，需要进行转换
            bitmap = Bitmap.createBitmap(drawable.getIntrinsicWidth(),
                    drawable.getIntrinsicHeight(),
                    Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(bitmap);
            drawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
            drawable.draw(canvas);
        }
        return bitmap;
    }
}
