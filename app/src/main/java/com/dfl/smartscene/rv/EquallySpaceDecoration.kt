package com.dfl.smartscene.rv

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import kotlin.math.ceil

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2023/11/14
 * desc : 实现rv grid布局等距分布间距,传入行间距,列间距；LinearLayout传入第一个参数
 * version: 1.1
 */
class EquallySpaceDecoration(
    private val rowSpace: Int,
    private val columnSpace: Int,
) :
    RecyclerView.ItemDecoration() {
    private var needBottomGap: Boolean = false

    constructor(rowSpace: Int) : this(rowSpace, rowSpace)
    constructor(rowSpace: Int, columnSpace: Int, needBottomGap: Boolean) : this(rowSpace, columnSpace) {
        this.needBottomGap = needBottomGap
    }

    /**
     * 通过获取item偏移量为分割线设置位置，达到类似padding效果，防止重叠
     * 使用网格布局会对item+item偏移量进行位置预设，
     * 当rv水平大小被约束时，单纯设置rect左右达不到预想的效果，故设置间距逻辑不同
     * /---------------------------------/
     * /               |                 /
     * /            rect.top             /
     * /               |                 /
     * /--rect.left--item--rect.right----/
     * /              |                  /
     * /          rect.bottom            /
     * /            |                    /
     * /---------------------------------/
     * @param outRect 接受输出的rect
     * @param view item
     * @param parent recyclerview
     * @param state RecyclerView.State
     */
    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        if (parent.adapter == null) return
        //获取当前view在adapter中的位置防止滑动后布局错乱
        val position = parent.getChildAdapterPosition(view)
        if (parent.layoutManager is GridLayoutManager) {
            val gridLayoutManage = parent.layoutManager as GridLayoutManager
            val spanCount = gridLayoutManage.spanCount
            val curColumn = position % spanCount
            val curRow = position / spanCount + 1
            //行数向上取整
            val lastRow = ceil(1.0 * parent.adapter!!.itemCount / spanCount).toInt()
            /*
            为实现间距大小为设定的参数，且等距分布满足：
                各个模块的大小相等，即各列的left+right 值相等；
                各列的间距相等，即前列的right + 后列的left = 列间距；
            得出公式
                某列的left = 所在的列数 * （列间距 * (1 / 列数)）
                某列的right = 列间距 - 后列的left = 列间距 -（所在的列数+1） * （列间距 * (1 / 列数)）
                注：这里用的所在列数为从0开始
             */
            outRect.left = curColumn * columnSpace / spanCount
            outRect.right = columnSpace - (curColumn + 1) * columnSpace / spanCount
            outRect.bottom = if (curRow == lastRow && !needBottomGap) {
                0
            } else {
                rowSpace
            }
        } else if (parent.layoutManager is LinearLayoutManager) {
            val linearLayoutManager = parent.layoutManager as LinearLayoutManager
            if (position == parent.adapter!!.itemCount - 1) return
            if (linearLayoutManager.orientation == LinearLayoutManager.HORIZONTAL) {
                outRect.right = rowSpace
            } else {
                outRect.bottom = rowSpace
            }
        }
    }
}