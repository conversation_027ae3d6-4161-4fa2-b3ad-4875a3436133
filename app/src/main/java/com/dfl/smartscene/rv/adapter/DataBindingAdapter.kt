package com.dfl.smartscene.rv.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.RecyclerView
import com.dfl.smartscene.rv.viewholder.DataBindingViewHolder

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/19
 * desc   :
 * version: 1.0
 */
abstract class DataBindingAdapter<T> :
    RecyclerView.Adapter<DataBindingViewHolder<T>?>(), IDataAdapter<T?> {
    var data: MutableList<T?>? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DataBindingViewHolder<T> {
        val viewDataBinding = DataBindingUtil.inflate<ViewDataBinding>(
            LayoutInflater.from(parent.context),
            layoutId(viewType), parent, false
        )
        return newViewHolder(viewDataBinding, viewType)
    }

    override fun onBindViewHolder(holder: DataBindingViewHolder<T>, position: Int) {
        holder.bind(data!![position])
    }

    override fun getItemCount(): Int {
        return if (data != null) data!!.size else 0
    }

    override fun fillData(data: MutableList<T?>?) {
        this.data = data
        notifyDataSetChanged()
    }

    protected abstract fun newViewHolder(
        viewDataBinding: ViewDataBinding,
        viewType: Int
    ): DataBindingViewHolder<T>

    protected abstract fun layoutId(viewType: Int): Int
}