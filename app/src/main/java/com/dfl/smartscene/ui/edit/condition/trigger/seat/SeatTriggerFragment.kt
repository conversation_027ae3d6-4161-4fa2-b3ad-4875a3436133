package com.dfl.smartscene.ui.edit.condition.trigger.seat

import android.annotation.SuppressLint
import android.view.View
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.TriggerSeat
import com.dfl.smartscene.databinding.SceneFragmentEditConditionBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.common.checklist.CheckListType
import com.dfl.smartscene.ui.overlay.common.checklist.RadioListDialogFragment
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :条件->座椅
 * version: 1.0
 */
class SeatTriggerFragment(private val preCondition: ScenarioInfo.Condition?) :
    MVVMAdapterFragment<SeatTriggerAdapter, SceneFragmentEditConditionBinding, SeatTriggerViewModel>() {

    private var mAdapter: SeatTriggerAdapter? = null

    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_condition
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    override fun getViewModelClass(): Class<SeatTriggerViewModel> {
        return SeatTriggerViewModel::class.java
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        super.initData()
        mViewModel.initData(resources)

        mViewModel.mSeatLiveData.observe(viewLifecycleOwner) {
            mAdapter?.data = it
            mAdapter?.notifyDataSetChanged()
            val position = getSimulateClickPosition(preCondition, null)
            if (position != -1) {
                view?.let { it1 -> handleItemClick(it1, position) }
            }
        }
    }

    override fun getAdapter(): SeatTriggerAdapter? {
        return mAdapter
    }

    override fun initView(view: View?) {
        super.initView(view)
        mAdapter = SeatTriggerAdapter()
        mAdapter?.setOnItemClickListener { _, v, position ->
            handleItemClick(v, position)
        }
        mViewDataBinding.rvCondition.adapter = mAdapter
    }

    private fun handleItemClick(view: View, position: Int) {
        if (DebouncingUtils.isValid(view, SceneEditManager.mViewClickDuration)) {
            mAdapter?.data?.get(position)?.let {
                if (it.conditionType == null) return@let
                showRadioListDialog(it.conditionName, it.conditionType)
            }
        }
    }

    private fun showRadioListDialog(title: String, type: TriggerSeat) {
        val dialog: RadioListDialogFragment<ScenarioInfo.Condition, Int> = RadioListDialogFragment(
            title,
            CheckListType.INPUT_CHECK_MODE,
            mViewModel.getRadioList(resources, type),
            mViewModel.getTriggerConditionSeatData(type, resources),
        )
        if (type == TriggerSeat.LEFT_BELT_STATE || type == TriggerSeat.RIGHT_BELT_STATE) {
            dialog.desc = getString(R.string.scene_status_condition_right_seat_tips)
        }
        dialog.preSetCondition = preCondition
        dialog.inputArgValueList = mViewModel.getInputArgInfoValueData(type)
        dialog.show(parentFragmentManager, "showRadioListDialog")
    }
}