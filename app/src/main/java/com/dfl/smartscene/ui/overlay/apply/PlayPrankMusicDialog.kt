package com.dfl.smartscene.ui.overlay.apply

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.dfl.android.common.base.BaseDialogFragment
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.common.util.ObjectUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.databinding.SceneDialogSoundPlayPrankMusicBinding
import com.dfl.smartscene.util.UIUtils
import com.iauto.scenarioadapter.ScenarioInfo
import org.greenrobot.eventbus.EventBus

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/01/15
 * desc : 4个radio开关单选+wheel滚动条 播放整蛊音对话框
 * version: 1.0
 * @param mSequence 对话框对应动作
 * @param mPreSequence 之前的动作
 */
class PlayPrankMusicDialog(
    private var mSequence: List<ScenarioInfo.Sequence> = emptyList(),
    private var mPreSequence: ScenarioInfo.Sequence? = null
) : BaseDialogFragment() {
    private lateinit var mViewBind: SceneDialogSoundPlayPrankMusicBinding

    /**
     * radio单选开关文本列表
     */
    var switchTextList: List<String> = emptyList()

    /**
     * radio单选开关文本对应的输入值InputArg列表
     */
    var switchValueList: List<String> = emptyList()

    /**
     * wheel滚动条文本列表
     */
    var wheelTextList: List<String> = emptyList()

    /**
     * wheel滚动条文本对应的输入值InputArg列表
     */
    var wheelValueList: List<String> = emptyList()

    /**
     * 选中的radio位置
     */
    private var mCheckedSwitchIndex = 0

    /**
     * 选中的wheel位置
     */
    private var mCheckedWheelIndex = 0

    companion object {
        /**
         * 打开播放整蛊音对话框
         * @param manager FragmentManager
         * @param sequence 对应动作参数
         * @param preSequence 之前的动作参数
         */
        fun showPlayPrankMusicDialog(
            manager: FragmentManager, sequence: List<ScenarioInfo.Sequence>, preSequence: ScenarioInfo.Sequence?
        ) {
            val dialog = PlayPrankMusicDialog(sequence, preSequence)
            //位置
            dialog.switchTextList = listOf(
                CommonUtils.getString(R.string.scene_text_oms_main_driver),
                CommonUtils.getString(R.string.scene_text_oms_copilot),
                CommonUtils.getString(R.string.scene_text_edit_action_apply_sound_play_position_left_rear),
                CommonUtils.getString(R.string.scene_text_edit_action_apply_sound_play_position_right_rear),
            )
            //声音
            dialog.wheelTextList = listOf(
                CommonUtils.getString(R.string.scene_text_edit_action_apply_sound_play_sound_0),
                CommonUtils.getString(R.string.scene_text_edit_action_apply_sound_play_sound_1),
                CommonUtils.getString(R.string.scene_text_edit_action_apply_sound_play_sound_2),
                CommonUtils.getString(R.string.scene_text_edit_action_apply_sound_play_sound_3),
                CommonUtils.getString(R.string.scene_text_edit_action_apply_sound_play_sound_4),
                CommonUtils.getString(R.string.scene_text_edit_action_apply_sound_play_sound_5),
            )
            dialog.switchValueList = listOf("1", "2", "3", "4")
            dialog.wheelValueList = listOf("1", "2", "3", "4", "5", "6")
            dialog.checkItem(1, 0)
            dialog.show(manager, "PlayPrankMusicDialog")
        }

    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        mViewBind = SceneDialogSoundPlayPrankMusicBinding.inflate(inflater, container, false)
        return mViewBind.root
    }

    override fun initView(view: View) {
        setCanceledOnTouchOutside(true)
        //初始化radio
        mViewBind.tabSwitchViewCondition.generateViews(switchTextList)
        //选中对应radio
        mViewBind.tabSwitchViewCondition.selectedIndex = mCheckedSwitchIndex
        //初始化滚动条
        UIUtils.initWheelView(context, mViewBind.wheelSound, wheelTextList, "", 0)

        mViewBind.wheelSound.currentItem = mCheckedWheelIndex
        //确认按钮
        mViewBind.includeDialogBottom.btnPrimary.setOnClickListener {
            //播放整蛊音 一个动作 两个输入参数 [0]radio值,[1]wheel值
            if (mSequence.isNotEmpty() && mSequence[0].action != null) {
                mSequence[0].action.category = mViewBind.tvTitle.text.toString()
                val inputArg = mSequence[0].action.input
                if (inputArg.isNotEmpty()) {
                    val position = switchTextList[mViewBind.tabSwitchViewCondition.selectedIndex]
                    val sound = wheelTextList[mViewBind.wheelSound.currentItem]
                    inputArg[0].value = switchValueList[mViewBind.tabSwitchViewCondition.selectedIndex]
                    inputArg[0].desc = position
                    inputArg[1].value = wheelValueList[mViewBind.wheelSound.currentItem]
                    inputArg[1].desc = sound
                    mSequence[0].action.desc = "$position $sound"
                    EventBus.getDefault().post(mSequence[0])
                }
            }
            dismissWithAnimate()
        }
        mViewBind.includeDialogBottom.btnNormal.setOnClickListener {
            dismissWithAnimate()
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.scene_dialog_sound_play_prank_music
    }

    /**
     * 根据之前的动作参数选中对应选项
     */
    override fun initData() {
        if (!ObjectUtils.checkScenarioDataIsEmpty(mPreSequence)) {
            mPreSequence!!.let {
                //如果是播放整蛊音
                if (it.action.skillId == SkillsListConstant.SKILL_ID_ACTION_APP_PLAY_PRANK_MUSIC) {
                    val positionIndex = it.action.input[0].value.toInt() - 1
                    val soundIndex = it.action.input[1].value.toInt() - 1
                    mCheckedSwitchIndex = positionIndex
                    mCheckedWheelIndex = soundIndex
                }
            }
        }
    }

    fun checkItem(switchIndex: Int, wheelIndex: Int) {
        mCheckedWheelIndex = wheelIndex
        mCheckedSwitchIndex = switchIndex
    }

}