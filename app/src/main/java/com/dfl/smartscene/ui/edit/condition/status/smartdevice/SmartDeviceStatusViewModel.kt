package com.dfl.smartscene.ui.edit.condition.status.smartdevice

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.StatusSmartDevice
import com.dfl.smartscene.soa.SceneConditionManager
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.soacenter.communication.CarControlManager
import com.dfl.soacenter.entity.AIOTListBean
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.launch

/**
 *Created by 钟文祥 on 2023/12/12.
 *Describer: 参照模版
 */
class SmartDeviceStatusViewModel : BaseViewModel() {

    val liveData = MutableLiveData<ArrayList<ConditionItemBean<StatusSmartDevice>>>()

    //初始化二级菜单列表数据
    fun initData(resources: Resources, skillId: Int) {
        val result = updateList(null, resources, skillId)
        liveData.postValue(result)

        CoroutineScope(Dispatchers.IO).launch {
            val value = async {
                CarControlManager.findAIOTList()
            }.await()

            liveData.postValue(updateList(value, resources, skillId))
        }
    }

    private fun updateList(
        value: AIOTListBean?, resources: Resources, skillId: Int
    ): ArrayList<ConditionItemBean<StatusSmartDevice>> {
        val list = ArrayList<ConditionItemBean<StatusSmartDevice>>()
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_TITLE, //adapter专用类型,是小标题 还是内容栏 类型
                resources.getString(R.string.scene_text_action_air_purifier), //条件名称
                -1, //图标
                null, //能力ids
                null, //内容标志
                true, //是否能用
                StatusSmartDevice.TITLE_AIR_PURIFIER  //标题标识
            )
        )

        list.add(
            //注意没有小标题的，省略最后两个参数
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT, //adapter专用类型,是小标题 还是内容栏 类型
                resources.getString(R.string.scene_text_edit_condition_air_quality), //条件名称
                R.drawable.scene_icon_me_status_smart_device_air_quality, //图标
                arrayListOf( //能力ids
                    SkillsListConstant.SKILL_ID_STATUS_SMART_DEVICE_AIR_QUALITY
                ), StatusSmartDevice.AIR_QUALITY, //内容标志
                value?.isHaveName("空气净化器") ?: false, //是否能用
                StatusSmartDevice.TITLE_AIR_PURIFIER //标题标识
            )
        )

        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_TITLE,
                resources.getString(R.string.scene_text_action_child_seat),
                -1,
                null,
                null,
                true,
                StatusSmartDevice.TITLE_CHILD_SEAT
            )
        )

        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_add_status_condition_riding_status),
                R.drawable.scene_icon_me_status_smart_device_riding_status,
                arrayListOf(SkillsListConstant.SKILL_ID_STATUS_SMART_DEVICE_RIDING_STATUS),
                StatusSmartDevice.RIDING_STATUS,
                value?.isHaveName("儿童座椅") ?: false,
                StatusSmartDevice.TITLE_CHILD_SEAT
            )
        )
        val type = SceneEditManager.StatusType.SMARTDEVICES
        val result = SceneEditManager.checkChildItemIsActive(type, list)
        SceneConditionManager.checkItemConditionHasAdd(skillId, result)

        return result
    }

    fun getSmartStatusCondition(type: StatusSmartDevice): ArrayList<ScenarioInfo.Condition> {
        val conditionList = ArrayList<ScenarioInfo.Condition>()
        val inputArgList = ArrayList<InputArgInfo>()
        when (type) {
            StatusSmartDevice.AIR_QUALITY -> {
                inputArgList.add(
                    InputArgInfo( //键值对
                        //				优 : 1,良 : 2, 差:3 默认“差”
                        SkillsListConstant.INPUT_ARG_STATUS_SMART_AIR_QUALITY, ArgType.INT32, "3"
                    )
                )
                conditionList.add(
                    ScenarioInfo.Condition(
                        1, //从1开始
                        "",
                        SkillsListConstant.SKILL_ID_STATUS_SMART_DEVICE_AIR_QUALITY, //能力id
                        "",
                        inputArgList //键值对
                    )
                )
            }
            StatusSmartDevice.RIDING_STATUS -> {
                inputArgList.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    )
                )

                conditionList.add(
                    ScenarioInfo.Condition(
                        1, "", SkillsListConstant.SKILL_ID_STATUS_SMART_DEVICE_RIDING_STATUS, "", inputArgList
                    )
                )
            }
            else -> {}
        }
        return conditionList
    }

    //添加默认值
    fun getAirQualityConditionData(resources: Resources): ArrayList<ScenarioInfo.Condition> {
        val conditionList = ArrayList<ScenarioInfo.Condition>()
        val inputArgList = ArrayList<InputArgInfo>()
        inputArgList.add(
            InputArgInfo( //键值对
                //				优 : 1,良 : 2, 差:3 默认“差”
                SkillsListConstant.INPUT_ARG_STATUS_SMART_AIR_QUALITY, ArgType.INT32, "3"
            )
        )
        conditionList.add(
            ScenarioInfo.Condition(
                1, //从1开始
                resources.getString(R.string.scene_text_edit_condition_air_quality), //二级菜单
                SkillsListConstant.SKILL_ID_STATUS_SMART_DEVICE_AIR_QUALITY, //能力id
                resources.getString(R.string.scene_text_action_smart_device), //一级菜单
                inputArgList //键值对
            )
        )
        return conditionList
    }


    fun getStatusConditionSeatData(): ArrayList<ScenarioInfo.Condition> {
        val conditionList = ArrayList<ScenarioInfo.Condition>()
        val inputArgList = ArrayList<InputArgInfo>()
        inputArgList.add(
            InputArgInfo(
                SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
            )
        )

        conditionList.add(
            ScenarioInfo.Condition(
                1, "", SkillsListConstant.SKILL_ID_STATUS_SMART_DEVICE_RIDING_STATUS, "", inputArgList
            )
        )

        return conditionList
    }

    fun getInputArgInfoValueData(type: StatusSmartDevice): ArrayList<Int> {
        return when (type) {
            StatusSmartDevice.AIR_QUALITY -> {
                arrayListOf(3, 2, 1)
            }
            else -> {
                arrayListOf(1, 0)
            }
        }

    }

    fun getListData(resources: Resources, type: StatusSmartDevice): List<CheckListBean> {
        return when (type) {
            StatusSmartDevice.AIR_QUALITY -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_edit_condition_air_difference), true),
                    CheckListBean(resources.getString(R.string.scene_text_edit_condition_air_nice), false),
                    CheckListBean(resources.getString(R.string.scene_text_edit_condition_air_excellent), false),
                )
            }
            else -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_status_condition_seat_someone), true),
                    CheckListBean(resources.getString(R.string.scene_text_status_condition_seat_no_one), false),
                )
            }
        }
    }

}