package com.dfl.smartscene.ui.edit.condition.status.location

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.StatusLocation
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.smartscene.soa.SceneConditionManager
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo

/**
 *Created by 钟文祥 on 2023/12/12.
 *Describer:
 */
class LocationStatusViewModel : BaseViewModel() {

    val liveData = MutableLiveData<ArrayList<ConditionItemBean<StatusLocation>>>()


    fun initData(resources: Resources, skillId: Int) {
        val list = ArrayList<ConditionItemBean<StatusLocation>>()

        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_condition_location_located_at),
                R.drawable.scene_icon_me_status_location_position,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_STATUS_LOCATION_LOCATED_AT
                ),
                StatusLocation.LOCATION_VEHICLE_ARRIVAL
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_condition_location_located_at_no),
                R.drawable.scene_icon_me_status_location_noposition,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_STATUS_LOCATION_LOCATED_AT_NOT
                ),
                StatusLocation.LOCATION_VEHICLE_DEPARTURE
            )
        )

        val type = SceneEditManager.StatusType.LOCATION
        val result = SceneEditManager.checkChildItemIsActive(type, list)
        SceneConditionManager.checkItemConditionHasAdd(skillId, result)
        liveData.postValue(result)

    }

    //根据skillId 组建默认的条件值
    fun getConditionValue(
        resources: Resources, item: ConditionItemBean<StatusLocation>
    ): ScenarioInfo.Condition {


        val inputArgList = mutableListOf<InputArgInfo>(
            InputArgInfo(SkillsListConstant.INPUT_ARG_TRIGGER_LOCATION_LATITUDE, ArgType.DOUBLE, "0"),
            InputArgInfo(SkillsListConstant.INPUT_ARG_TRIGGER_LOCATION_LONGITUDE, ArgType.DOUBLE, "0"),
            InputArgInfo(SkillsListConstant.INPUT_ARG_TRIGGER_LOCATION_DISTANCE, ArgType.INT32, "100"),
        )

        return ScenarioInfo.Condition(
            1,
            item.conditionName, //车辆到达，车辆离开
            item.conditionSkillId?.get(0) ?: -1,
            resources.getString(R.string.scene_text_edit_add_condition_location),
            inputArgList
        )

    }
}