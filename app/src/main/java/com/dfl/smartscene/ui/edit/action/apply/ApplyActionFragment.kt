package com.dfl.smartscene.ui.edit.action.apply

import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.ActionSkillItemBean
import com.dfl.smartscene.bean.action.ApplicationActionType
import com.dfl.smartscene.databinding.SceneFragmentEditActionBinding
import com.dfl.smartscene.soa.getSequenceValueByName
import com.dfl.smartscene.ui.edit.action.apply.adapter.ApplyActionAdapter
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.apply.NavigationActionDialogFragment
import com.dfl.smartscene.ui.overlay.apply.VpaReplyDialogFragment
import com.dfl.smartscene.ui.overlay.common.checklist.CheckListType
import com.dfl.smartscene.ui.overlay.common.checklist.RadioListDialogFragment
import com.dfl.smartscene.ui.overlay.common.doublewheel.DoubleWheelDialogFragment
import com.dfl.smartscene.ui.overlay.common.doublewheel.DoubleWheelType
import com.dfl.smartscene.ui.overlay.common.singlewheel.SingleWheelDialog
import com.dfl.smartscene.ui.overlay.common.singlewheel.SingleWheelType
import com.dfl.smartscene.ui.overlay.media.QQCheck6DialogFragment2a
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo
import org.greenrobot.eventbus.EventBus

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :动作->应用页面
 * version: 1.0
 */
class ApplyActionFragment(private val sequence: ScenarioInfo.Sequence?) :
    MVVMAdapterFragment<ApplyActionAdapter, SceneFragmentEditActionBinding, ApplyActionViewModel>() {
    private var mAdapter: ApplyActionAdapter? = null

    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_action
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    override fun getViewModelClass(): Class<ApplyActionViewModel> {
        return ApplyActionViewModel::class.java
    }

    override fun initData() {
        mAdapter = ApplyActionAdapter()
        mViewModel.applyActionLiveData.observe(viewLifecycleOwner) {
            mAdapter?.setNewInstance(it)
            val position = getSimulateClickPosition(null, sequence)
            if (position != -1) {
                view?.let { it1 -> handleItemClick(it1, position) }
            }
        }
        mViewModel.initData(resources)

    }

    override fun getAdapter(): ApplyActionAdapter? {
        return mAdapter
    }

    override fun initView(view: View?) {
        mViewDataBinding.rvAction.adapter = mAdapter
        //动作子项点击事件
        mAdapter?.setOnItemClickListener { _, v, position ->
            run {
                handleItemClick(v, position)
            }
        }
        //rv布局管理器,标题独一行
        val manager = GridLayoutManager(context, 4)
        manager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                if (mAdapter?.data?.get(position)?.itemType == ActionSkillItemBean.SKILL_TITLE) return 4
                return 1
            }
        }
        mViewDataBinding.rvAction.layoutManager = manager
    }

    private fun handleItemClick(view: View, position: Int) {
        if (DebouncingUtils.isValid(view, SceneEditManager.mViewClickDuration)) {
            val applicationAction = mAdapter?.data?.get(position)
            if (applicationAction == null || applicationAction.type == ActionSkillItemBean.SKILL_TITLE) return
            showApplicationDialog(applicationAction)
        }
    }

    /**
     * 根据ApplicationActionType打开对应对话框
     */
    private fun showApplicationDialog(application: ActionSkillItemBean<ApplicationActionType>) {
        if (application.actionType == null) return
        val content = application.content.replace("\n", "")
        when (application.actionType) {
            ApplicationActionType.START_NAVI -> { //发起导航
                NavigationActionDialogFragment.show(childFragmentManager, application.actionSkillId, sequence)
            }

            //路线偏好,沉浸音效,打开应用
            //ApplicationActionType.NAVIGATION_ROUTE, ApplicationActionType.SOUND_EFFECT, ApplicationActionType.APP_OPEN -> {
            //    showSingleWheelDialog(application.content, application.actionType)
            //}

            ApplicationActionType.PLAY_QQ -> { //播放QQ音乐
                QQCheck6DialogFragment2a.showFragment(childFragmentManager, sequence)
            }

            ApplicationActionType.VPA_REPLY -> { //小尼回复
                openVpaReplyDialog(application.content)
            }

            ApplicationActionType.APP_NOTIFY -> { //消息通知
                showSetMsgDialog(application.content)
            }
            //播放整蛊音
            ApplicationActionType.PLAY_PRANK_MUSIC -> {
                //PlayPrankMusicDialog.showPlayPrankMusicDialog(
                //    childFragmentManager,
                //    mViewModel.getActionList(application.content, application.actionType),
                //    sequence
                //)
                showDoubleWheelDialog(application.content, application.actionType)
            }
            ApplicationActionType.OUTER_SPEAKER -> {
                showSingleWheelDialog(content, application.actionType)
            }

            else -> {
                showRadioListDialog(application.content, application.actionType)
            }
        }
    }

    private fun showDoubleWheelDialog(title: String, type: ApplicationActionType) {
        val dialog = DoubleWheelDialogFragment(
            title,
            mViewModel.getLeftWheelMap(),
            mViewModel.getRightWheelMap(),
            mViewModel.getActionList(title, type),
            DoubleWheelType.SINGLE_SKILL
        )
        dialog.setupPreAction(sequence)
        dialog.setWheelDefaultPosition(1, 0)
        dialog.show(parentFragmentManager, title)
    }

    private fun showRadioListDialog(title: String, type: ApplicationActionType) {
        val dialog: RadioListDialogFragment<ScenarioInfo.Sequence, Int>
        when (type) {
            ApplicationActionType.MEDIA_PLAY_CONTROL, ApplicationActionType.WEATHER_BROADCAST, ApplicationActionType.TRIP_BROADCAST, ApplicationActionType.CLOSE_MEDIA, ApplicationActionType.APP_CLOSE -> {
                //单选,播放多媒体等不需要判断inputArg,只需要选中对应sequence
                dialog = RadioListDialogFragment(
                    title,
                    CheckListType.DEFAULT_CHECK_MODE,
                    mViewModel.getListData(resources, type, title),
                    mViewModel.getActionList(title, type),
                )
                if (type == ApplicationActionType.MEDIA_PLAY_CONTROL) {
                    dialog.desc = getString(R.string.scene_text_action_only_qq_bluetooth_usb)
                }
            }
            ApplicationActionType.APP_OPEN -> {
                dialog = RadioListDialogFragment(
                    title,
                    CheckListType.APP_OPEN,
                    mViewModel.getListData(resources, type, title),
                    mViewModel.getActionList(title, type)
                )
                dialog.inputArgValueList = mViewModel.getInputArgsList(type)
            }
            else -> {
                //一个sequence根据inputArg选项
                dialog = RadioListDialogFragment(
                    title,
                    CheckListType.INPUT_CHECK_MODE,
                    mViewModel.getListData(resources, type, title),
                    mViewModel.getActionList(title, type),
                )
                dialog.inputArgValueList = mViewModel.getInputArgsList(type)
                if (type == ApplicationActionType.USE_VOICE) {
                    dialog.desc = getString(R.string.scene_text_action_use_voice_content)
                }
            }
        }
        dialog.preSetSequence = sequence
        dialog.show(parentFragmentManager, "showRadioListDialog")
    }


    private fun openVpaReplyDialog(content: String) {
        val replayMsg = getSequenceValueByName(sequence, SkillsListConstant.INPUT_ARG_ACTION_APP_VPA_REPLY)
        val vpaReplyDialogFragment = VpaReplyDialogFragment.getInstance()
        vpaReplyDialogFragment.title = content
        vpaReplyDialogFragment.type = VpaReplyDialogFragment.EditInputType.VPA_REPLY
        vpaReplyDialogFragment.setEditHint(resources.getString(R.string.scene_edit_input_hint_vpa_reply_please))
        vpaReplyDialogFragment.setReplayContent(replayMsg)
        vpaReplyDialogFragment.onInputListener = object : VpaReplyDialogFragment.OnInputListener {
            override fun onInput(inputData: String) {
                val inputArgList = ArrayList<InputArgInfo>()
                val inputArg = InputArgInfo(
                    SkillsListConstant.INPUT_ARG_ACTION_APP_VPA_REPLY, ArgType.STRING, inputData
                )
                inputArgList.add(inputArg)
                val action = ScenarioInfo.Action(
                    inputData, SkillsListConstant.SKILL_ID_ACTION_APP_VPA_REPLY, content, inputArgList
                )
                val sequence = ScenarioInfo.Sequence(1, 1, action)
                EventBus.getDefault().post(sequence)
            }
        }
        vpaReplyDialogFragment.show(
            childFragmentManager, VpaReplyDialogFragment.DIALOG_FRAGMENT_TAG
        )
    }

    private fun showSetMsgDialog(title: String) {
        val vpaReplyDialogFragment = VpaReplyDialogFragment.getInstance()
        vpaReplyDialogFragment.type = VpaReplyDialogFragment.EditInputType.SHORT_TEXT_PROMPT
        vpaReplyDialogFragment.title = title
        val msg = getSequenceValueByName(
            sequence, SkillsListConstant.INPUT_ARG_ACTION_APP_NOTIFY
        )
        vpaReplyDialogFragment.setReplayContent(msg)
        vpaReplyDialogFragment.setEditHint(getString(R.string.scene_edit_input_hint_short_text_tips_please))
        vpaReplyDialogFragment.onInputListener = object : VpaReplyDialogFragment.OnInputListener {
            override fun onInput(inputData: String) {
                val inputArgList = ArrayList<InputArgInfo>()
                val inputArg = InputArgInfo(
                    SkillsListConstant.INPUT_ARG_ACTION_APP_NOTIFY, ArgType.STRING, inputData
                )
                inputArgList.add(inputArg)
                val action = ScenarioInfo.Action(
                    inputData, SkillsListConstant.SKILL_ID_ACTION_APP_NOTIFY, title, inputArgList
                )
                val sequence = ScenarioInfo.Sequence(1, 1, action)
                EventBus.getDefault().post(sequence)
            }
        }
        vpaReplyDialogFragment.show(
            childFragmentManager, VpaReplyDialogFragment.DIALOG_FRAGMENT_TAG
        )
    }

    /**
     * 打开单个wheel滚动条对话框
     */
    private fun showSingleWheelDialog(title: String, type: ApplicationActionType) {
        val dialog = SingleWheelDialog<ScenarioInfo.Sequence, Int>(
            context,
            mViewModel.getSingleWheelData(resources, type),
            title,
            mViewModel.getActionList(title, type),
            SingleWheelType.SINGLE_WHEEL_INPUT_ARG
        )
        dialog.setupPreAction(sequence)
        dialog.setInputArgValueList(mViewModel.getInputArgsList(type))
        dialog.showWithAnimate()
    }

}