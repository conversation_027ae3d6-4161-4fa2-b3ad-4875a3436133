package com.dfl.smartscene.ui.overlay.common.checklist

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/13
 * desc :多项选择单选共用弹窗枚举区分类别,注意这里是出现特殊的数据处理的时候才会新增枚举
 * version: 1.0
 */
enum class CheckListType {
    /**
     * 输入动作或条件list包含多个值,对话框不设置inputArg,选项选中值 对应 输出动作或条件list[index]
     * 多个sequence一个InputArg,不需要创建特殊type
     */
    DEFAULT_CHECK_MODE,

    /**
     * 选项选中的值对应输入动作或条件的InputArgValues值,一个sequence一个inputArg
     * 注意如果使用此模式必须在弹窗show之前调用setInputArgValues方法
     */
    INPUT_CHECK_MODE,

    /**播放qq音乐，返回卡片的标题要改为 播放qq音乐*/
    QQ_MUSIC_MODE,

    /**
     * 车门触发状态特殊数据处理,同一个skillId,一个condition两个inputArg,
     * 选项值对应InputArg[1]
     */
    DOOR_SWITCH_TYPE,

    /**
     * 每周
     * */
    CONDITION_WEEKLY,

    /**
     *打开应用,一个skillid，两个inputArg，
     * 选项值输InputArg[1]
     */
    APP_OPEN,

    /**多选类型，inputArgList索引0选中值，1未选中值*/
    MULTI_INPUT_SINGLE_SEQUENCE_TYPE,

    /**
     * 场景灯效的特殊的数据处理,一个sequence两个inputArg
     * radio+wheel
     */
    @Deprecated("lk1a不再使用")
    CHECK_SCENE_LIGHT_EFFECT_MODE,

    /**
     * 播放控制的特殊的数据处理,播放暂停,上一首下一首2个sequence,一个inputArg
     * 选项选中的值对应输入InputArgValues值
     */
    @Deprecated("RadioListDialog使用DEFAULT_CHECK_MODE输入全部选项值即可")
    MEDIA_CONTROL,

    /**
     * 打开播放器的特殊的数据处理
     */
    @Deprecated("ui2.0ThreeDialog已被废弃,RadioListDialog使用DEFAULT_CHECK_MODE")
    MEDIA_OPEN,

    /**
     * 座椅按摩特殊的数据处理,关和力度两个sequence,一个InputArg
     * 选项选中的值对应输入InputArgValues值
     */
    @Deprecated("RadioListDialog使用DEFAULT_CHECK_MODE输入全部选项值即可")
    SEAT_MASSAGE,
}