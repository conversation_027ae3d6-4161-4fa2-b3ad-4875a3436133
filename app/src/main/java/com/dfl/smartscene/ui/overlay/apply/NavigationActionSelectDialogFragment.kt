package com.dfl.smartscene.ui.overlay.apply

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.RadioButton
import androidx.fragment.app.FragmentManager
import com.dfl.android.common.base.BaseDialogFragment
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.common.util.CommonToastUtils
import com.dfl.android.common.util.EncodeUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.databinding.SceneDialogFragmentNavigationActionSelectBinding
import com.dfl.soacenter.entity.ReqPlanRoute
import com.google.gson.Gson
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo
import org.greenrobot.eventbus.EventBus

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2022/09/22
 * desc:发起导航的动作选择弹框
 * version:1.0
 */
@Deprecated("2a被废弃,使用 NavigationActionDialogFragment")
class NavigationActionSelectDialogFragment(
    private var curSequence: ScenarioInfo.Sequence?
) : BaseDialogFragment(), View.OnClickListener {
    private var _binding: SceneDialogFragmentNavigationActionSelectBinding? = null
    private val viewBinging get() = _binding!!

    /**当前选中的按钮*/
    private var currentCheckView: RadioButton? = null

    private var mSkillIdList: ArrayList<Int>? = null

    companion object {
        const val TAG_DIALOG_ADDRESS = "tag_dialog_address"
        fun showFragment(manager: FragmentManager, skillIdList: ArrayList<Int>?, curSequence: ScenarioInfo.Sequence?) {
            val dialog = NavigationActionSelectDialogFragment(curSequence)
            dialog.mSkillIdList = skillIdList
            dialog.show(manager, TAG_DIALOG_ADDRESS)
        }

    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = SceneDialogFragmentNavigationActionSelectBinding.inflate(inflater, container, false)
        return viewBinging.root
    }


    override fun initView(view: View) {
        setCanceledOnTouchOutside(true)
        //默认选中回家
        currentCheckView = viewBinging.ctvGoHome
        viewBinging.includeDialogBottom.btnPrimary.setOnClickListener(this)
        viewBinging.includeDialogBottom.btnNormal.setOnClickListener(this)
        viewBinging.ctvGoCompany.setOnClickListener(this)
        viewBinging.ctvGoHome.setOnClickListener(this)
        viewBinging.ctvGoSomeWhere.setOnClickListener(this)
        //        viewBinging.ctvPassSomeWhere.setOnClickListener(this)
        mSkillIdList?.let {
            if (!it.contains(SkillsListConstant.SKILL_ID_ACTION_NAV_HOME_AND_COMPANY)) {
                viewBinging.ctvGoCompany.visibility = View.GONE
                viewBinging.ctvGoHome.visibility = View.GONE
                //如果禁用回家回公司的ID情况下，仅剩下去某地，需要将去某地的marginStart属性值归0
                val params = viewBinging.ctvGoSomeWhere.layoutParams as LinearLayout.LayoutParams
                params.marginStart = 0
                viewBinging.ctvGoSomeWhere.layoutParams = params
            }
            if (!it.contains(SkillsListConstant.SKILL_ID_ACTION_NAV_GO_SOME_WHERE)) {
                //禁用去某地的ID
                viewBinging.ctvGoSomeWhere.visibility = View.GONE
            }
            initCurrentCheckView()
        }
    }

    //初始化 哪个被选择了
    private fun initCurrentCheckView() {
        var curSelectIndex = 1
        curSequence?.let {
            if (it.action.skillId == SkillsListConstant.SKILL_ID_ACTION_NAV_HOME_AND_COMPANY) {
                it.action.input?.get(0)?.let { item ->
                    if (item.value == "2") {
                        curSelectIndex = 2
                    }
                }
            } else if (it.action.skillId == SkillsListConstant.SKILL_ID_ACTION_NAV_GO_SOME_WHERE) {
                curSelectIndex = 3
            } else {
                curSelectIndex = 1
            }
        }
        currentCheckView = when (curSelectIndex) {
            1 -> viewBinging.ctvGoHome
            2 -> viewBinging.ctvGoCompany
            else -> viewBinging.ctvGoSomeWhere
        }
        currentCheckView?.isChecked = true
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun getLayoutId(): Int {
        return R.layout.scene_dialog_fragment_navigation_action_select
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.btn_normal -> {
                dismissWithAnimate()
            }

            R.id.btn_primary -> {
                handleConfirm()
            }

            R.id.ctv_go_home -> {
                handleClickCheckView(viewBinging.ctvGoHome)
            }

            R.id.ctv_go_company -> {
                handleClickCheckView(viewBinging.ctvGoCompany)
            }

            R.id.ctv_go_some_where -> {
                handleClickCheckView(viewBinging.ctvGoSomeWhere)
                openAddressInputDialog4Go()
            }
        }
    }

    //去某地
    private fun openAddressInputDialog4Go() {
        val searchAddressDialogFragment = SearchAddressDialogFragment.getInstance()
        searchAddressDialogFragment.title = viewBinging.ctvGoSomeWhere.text.toString()
        searchAddressDialogFragment.onInputListener = object : SearchAddressDialogFragment.OnInputListener {

            override fun onInput(jsonInputData: String, isAddWayside: Boolean?) {
                if (jsonInputData.isEmpty()) {
                    CommonToastUtils.show(getString(R.string.scene_text_action_select_address_tips))
                    return
                }
                val planRoute: ReqPlanRoute = Gson().fromJson(jsonInputData, ReqPlanRoute::class.java)
                val desc = if (planRoute.midPois != null) {
                    "${planRoute.midPois!![0].name}+${planRoute.endPoi?.name}"
                } else {
                    planRoute.endPoi?.name
                }
                val inputArgList = ArrayList<InputArgInfo>()
                val inputArgInfo: InputArgInfo?
                val skillId = SkillsListConstant.SKILL_ID_ACTION_NAV_GO_SOME_WHERE
                val address4Base64 = EncodeUtils.base64Encode2String(jsonInputData.toByteArray())
                inputArgInfo = InputArgInfo(
                    SkillsListConstant.INPUT_ARG_ACTION_APP_GO_KEYWORD, ArgType.STRING, address4Base64
                )

                inputArgInfo.let { inputArgList.add(it) }
                val action = ScenarioInfo.Action(
                    desc, skillId, viewBinging.tvTitle.text.toString(), inputArgList
                )
                val sequence = ScenarioInfo.Sequence(1, 1, action)
                EventBus.getDefault().post(sequence)
                dismissWithAnimate()
            }

            override fun cancel() {
                dismissWithAnimate()
                super.cancel()
            }
        }
        searchAddressDialogFragment.show(childFragmentManager, TAG_DIALOG_ADDRESS)
    }

    private fun handleConfirm() {
        val inputArgList = ArrayList<InputArgInfo>()
        var inputArgInfo: InputArgInfo? = null
        var desc = ""
        var skillId: Int = -1

        if (viewBinging.ctvGoHome.isChecked) {
            desc = viewBinging.ctvGoHome.text.toString()
            skillId = SkillsListConstant.SKILL_ID_ACTION_NAV_HOME_AND_COMPANY
            inputArgInfo = InputArgInfo(
                SkillsListConstant.INPUT_ARG_NAV_HOME_COMPANY, ArgType.INT32, "1"
            )
        }
        if (viewBinging.ctvGoCompany.isChecked) {
            desc = viewBinging.ctvGoCompany.text.toString()
            skillId = SkillsListConstant.SKILL_ID_ACTION_NAV_HOME_AND_COMPANY
            inputArgInfo = InputArgInfo(
                SkillsListConstant.INPUT_ARG_NAV_HOME_COMPANY, ArgType.INT32, "2"
            )
        }
        //		if (viewBinging.ctvGoSomeWhere.isSelected) {
        //			if (inputAddress4Go.isEmpty()) {
        //				CommonToastUtils.show(getString(R.string.scene_text_action_select_address_tips))
        //				return
        //			}
        //			desc = "去$inputAddress4Go"
        //			skillId = SkillsListConstant.SKILL_ID_NAV_GO_SOME_WHERE
        //			inputArgInfo = InputArgInfo(
        //				SkillsListConstant.ARG_NAME_GO_KEYWORD, ArgType.STRING, inputAddress4Go
        //			)
        //		}

        inputArgInfo?.let { inputArgList.add(it) }
        val action = ScenarioInfo.Action(desc, skillId, viewBinging.tvTitle.text.toString(), inputArgList)
        val sequence = ScenarioInfo.Sequence(1, 1, action)
        EventBus.getDefault().post(sequence)
        dismissWithAnimate()
    }

    private fun uncheckView() {
        if (currentCheckView != null) {
            currentCheckView?.isChecked = false
        }
    }

    private fun handleClickCheckView(checkedTextView: RadioButton) {
        uncheckView()
        currentCheckView = checkedTextView
        checkedTextView.isChecked = true
    }

}