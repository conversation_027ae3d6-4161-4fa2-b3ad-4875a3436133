package com.dfl.smartscene.ui.splash

import android.app.ActivityOptions
import android.content.Intent
import android.os.Bundle
import androidx.lifecycle.Observer
import com.dfl.android.common.base.BaseActivity
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.util.CommonToastUtils
import com.dfl.android.common.util.StringUtils.getString
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.communication.SystemSettingsLocalManager
import com.dfl.smartscene.communication.SystemSettingsLocalManager.getAgreementStatus
import com.dfl.smartscene.ui.main.MainActivity
import com.jeremyliao.liveeventbus.LiveEventBus
import java.lang.ref.WeakReference

class SplashActivity : BaseActivity() {
    private var agreeObserver: Observer<Int>? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.scene_activity_splash)
        //测试使用
        //        startMainWithDisplayId();
        CommonLogUtils.logD(TAG, "隐私协议进入时间:" + System.currentTimeMillis())

        initLiveEventListener()
        //if (BuildConfig.DEBUG) {
        //    normalStart();
        //    return
        //}
    }

    override fun onResume() {
        super.onResume()
        //onResume获取防止重新进入后不唤起隐私协议
        if (!SystemSettingsLocalManager.isProtocolAgree(this)) { //不同意就需要打开弹窗
            getAgreementStatus()
        } else {
            normalStart()
        }
    }

    override fun initView() {
    }

    override fun initData() {
    }

    private fun initLiveEventListener() {
        LiveEventBus.get(GlobalLiveEventConstants.KEY_PROTOCOL_STATUS, Boolean::class.java)
            .observe(this) { isAgree: Boolean ->
                if (isAgree) {
                    normalStart()
                } else {
                    CommonToastUtils.show(getString(R.string.scene_toast_privacy_disagree))
                    //splash不需要再次finish，application监听不同意关闭所有activity
                    //finish()
                }
            }
        agreeObserver = AgreeObserver(this)
        LiveEventBus.get<Int>(GlobalLiveEventConstants.KEY_SYSTEM_SETTING_PRIVATE_STATE_CHANGED)
            .observe(this, agreeObserver as AgreeObserver)
    }

    private fun normalStart() {
        CommonLogUtils.logD(TAG, "隐私协议完成时间:" + System.currentTimeMillis())
        val options = ActivityOptions.makeBasic()
        val intent = Intent(this, MainActivity::class.java)
        //        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        startActivity(intent, options.toBundle())
        finish()
    }

    private class AgreeObserver(splashActivity: SplashActivity) : Observer<Int> {
        private val weakReference: WeakReference<SplashActivity>

        init {
            weakReference = WeakReference(splashActivity)
        }

        override fun onChanged(result: Int) {
            CommonLogUtils.logD(TAG, "AgreeObserver:$result")
            val splashActivity = weakReference.get()
            if (splashActivity != null) {
                if (result == -1) { //请求超时
                    CommonToastUtils.show(getString(R.string.scene_toast_privacy_timeout))
                    splashActivity.finish()
                } else if (result == 1) {
                    splashActivity.normalStart()
                    splashActivity.finish()
                }
            }
        }
    }

    companion object {
        private const val TAG = GlobalConstant.GLOBAL_TAG.plus("SplashActivity")
    }
}