package com.dfl.smartscene.ui.main.me

import android.annotation.SuppressLint
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.util.getItemView
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.main.MySceneBean
import com.dfl.smartscene.databinding.SceneRecycleItemMeMySceneBinding
import com.dfl.smartscene.util.UIUtils
import com.dfl.smartscene.widget.SceneBaseDataBindingViewHolder
import com.dfl.smartscene.widget.recycleview.ItemHolderMoveCallback
import com.dfl.smartscene.widget.recycleview.ItemMoveCallback
import java.util.*


/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2022/07/21
 * desc: 我的场景卡片列表adapter
 * version:1.0
 */
class MeSceneAdapter(layoutResId: Int) :
    BaseQuickAdapter<MySceneBean, SceneBaseDataBindingViewHolder<SceneRecycleItemMeMySceneBinding>>(
        layoutResId
    ), ItemMoveCallback {

    private val mIsCollectIconList = arrayListOf(
        R.drawable.scene_img_me_card_collect_1,
        R.drawable.scene_img_me_card_collect_2,
        R.drawable.scene_img_me_card_collect_3
    )

    /**自定义长按Handler*/
    private val mLongPressHandler = Handler(Looper.getMainLooper())

    /**卡片长按时间*/
    private val LONG_PRESS_TIME = 400L

    override fun onCreateDefViewHolder(
        parent: ViewGroup, viewType: Int
    ): SceneBaseDataBindingViewHolder<SceneRecycleItemMeMySceneBinding> {
        return MeSceneViewHolder(parent.getItemView(layoutResId = R.layout.scene_recycle_item_me_my_scene))
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun convert(
        holder: SceneBaseDataBindingViewHolder<SceneRecycleItemMeMySceneBinding>, item: MySceneBean
    ) {
        holder.viewBing?.bean = item
        holder.viewBing?.executePendingBindings()
        
        //holder.viewBing?.switchAutoRun?.adapterSkinColor()
        //holder.viewBing?.clAutoRunHotArea?.setOnClickListener() {
        //    //切换 CheckBox 的选中状态和场景数据自动执行状态
        //    item.scenario.isAutoRun = item.scenario.isAutoRun == false
        //    val isChecked = item.scenario.isAutoRun
        //    if (isChecked != null) {
        //        holder.viewBing.cbAutoRun.isChecked = isChecked
        //    }
        //    // 如果需要，您可以在这里通知数据变化，例如：
        //    notifyItemChanged(getItemPosition(item))
        //
        //}
        holder.viewBing?.btnToggleSceneState?.adapterSkinColor()
        
        // 根据场景执行状态设置按钮的图片和文本
        if (item.isSceneStart == true) {
        // 场景正在执行中，显示暂停图标和取消文本
        holder.viewBing?.btnToggleSceneState?.setImgSrc(R.drawable.scene_img_me_item_pause)
        holder.viewBing?.btnToggleSceneState?.setText(context.getString(R.string.scene_text_common_cancel))
        } else {
        // 场景未执行，显示执行图标和执行文本
        holder.viewBing?.btnToggleSceneState?.setImgSrc(R.drawable.scene_icon_me_card_run)
        holder.viewBing?.btnToggleSceneState?.setText(context.getString(R.string.scene_button_common_execute))
        }
        if (item.isTop) {
            holder.viewBing?.ivTop?.setImageResource(mIsCollectIconList[holder.absoluteAdapterPosition % 3])
            holder.viewBing?.clRootMe?.setBackgroundResource(R.drawable.scene_shape_common_bg_card_1_r30_top)
        } else {
            holder.viewBing?.clRootMe?.setBackgroundResource(R.drawable.scene_shape_common_bg_card_1_r30)
        }
//        UIUtils.initRecyclerView(
//            context,
//            holder.viewBing?.wllIconList,
//            LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false),
//            false
//        )
//        val adapter = MeIconBeanAdapter()
//        holder.viewBing?.wllIconList?.adapter = adapter
//        adapter.setList(item.meIconBeanList)

//        holder.viewBing?.txtActionIng?.adapterSkinColor()
//        holder.viewBing?.txtActionIng?.setOnClickListener {
//            setOnItemClick(
//                holder.itemView, holder.bindingAdapterPosition
//            )
//        }
        // setUiModeChangeListener必须在adapterSkinColor前
//        holder.viewBing?.ivActionIng?.setUiModeChangeListener { theme ->
//            if (theme == 0) {
//                holder.viewBing?.ivActionIng?.imgView?.setAnimation(R.raw.scene_action_ing_day)
//            } else if (theme == 1) {
//                holder.viewBing?.ivActionIng?.imgView?.setAnimation(R.raw.scene_action_ing_night)
//            }
//        }
//        holder.viewBing?.ivActionIng?.adapterSkinColor()
//        holder.viewBing?.ivActionIng?.setOnClickListener {
//            setOnItemClick(
//                holder.itemView, holder.bindingAdapterPosition
//            )
//        }
    }


    /**重写baseQuickAdapter普通itemView绑定长按点击事件，修改触发长按点击时间*/
    override fun bindViewClickListener(
        viewHolder: SceneBaseDataBindingViewHolder<SceneRecycleItemMeMySceneBinding>, viewType: Int
    ) {
        super.bindViewClickListener(viewHolder, viewType)

        //把原来的长按事件置空
        viewHolder.itemView.setOnLongClickListener(null)
        //设置400ms触发长按事件
        UIUtils.setLongClick(mLongPressHandler, viewHolder.itemView, LONG_PRESS_TIME) { v ->
            var position = viewHolder.bindingAdapterPosition
            //仿照baseQuickAdapter写法
            if (position != RecyclerView.NO_POSITION) {
                position -= headerLayoutCount
                setOnItemLongClick(viewHolder.itemView, position)
            }
            true
        }
    }

    override fun onItemMove(fromPosition: Int, toPosition: Int) {
        //由于添加FootView占用了item位置，故需要判断toPosition位置是否大于数据源集合
        if (toPosition >= data.size) return
        //如果两者不是同一类型的数据则不能拖拽，置顶与非置顶数据不能互相拖拽调换位置
        //        if ((data[fromPosition].isTop) xor ((data[toPosition].isTop))) {
        //            return
        //        }
        if (fromPosition < toPosition) {
            for (i in fromPosition until toPosition) {
                data.let { Collections.swap(it, i, i + 1) }
            }
        } else {
            for (i in fromPosition downTo toPosition + 1) {
                data.let { Collections.swap(it, i, i - 1) }
            }
        }
        notifyItemMoved(fromPosition, toPosition)
    }

    /**
     * 判断是否一屏显示，没有过多数据，不用滑动
     */
    fun isNotOneScreen(): Boolean {
        val layoutManager = recyclerView.layoutManager
        if (layoutManager is LinearLayoutManager) {
            return (layoutManager.findLastCompletelyVisibleItemPosition() + 1) != this.getDefItemCount() || layoutManager.findFirstCompletelyVisibleItemPosition() != 0
        }
        return false
    }

    /**实现rv特定情况下不可滑动*/
    class ScrollGridLayoutManager(context: Context?, spanCount: Int) : GridLayoutManager(context, spanCount) {
        private var isScrollEnabled = true

        fun setScrollEnabled(flag: Boolean) {
            isScrollEnabled = flag
        }

        fun getScrollEnabled(): Boolean {
            return isScrollEnabled
        }

        override fun canScrollVertically(): Boolean {
            //Similarly you can customize "canScrollHorizontally()" for managing horizontal scroll
            return isScrollEnabled && super.canScrollVertically()
        }
    }
}

class MeSceneViewHolder(view: View) : SceneBaseDataBindingViewHolder<SceneRecycleItemMeMySceneBinding>(view),
    ItemHolderMoveCallback {
    override fun onItemHolderMoveStart(fromPosition: Int?) {
    }

    override fun onItemHolderMoveEnd(toPosition: Int?) {
        toPosition?.let {
            itemView.setBackgroundResource(R.drawable.scene_shape_common_bg_card_1_r30)
            bindingAdapter?.notifyItemChanged(toPosition)
        }
    }

}



