package com.dfl.smartscene.ui.edit.condition.trigger.location

import android.annotation.SuppressLint
import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.TriggerLocation
import com.dfl.smartscene.databinding.SceneFragmentEditConditionBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.apply.NavigationConditionDialogFragment
import com.iauto.scenarioadapter.ScenarioInfo

/**
 *Created by 钟文祥 on 2023/12/12.
 *Describer:触发条件->位置
 */
@Suppress("INVISIBLE_SETTER_FROM_DERIVED")
class LocationTriggerFragment(private val preCondition: ScenarioInfo.Condition?) :
    MVVMAdapterFragment<LocationTriggerAdapter, SceneFragmentEditConditionBinding, LocationTriggerViewModel>() {

    private var mAdapter: LocationTriggerAdapter? = null

    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_condition
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    override fun getViewModelClass(): Class<LocationTriggerViewModel> {
        return LocationTriggerViewModel::class.java
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        super.initData()
        mViewModel.initData(resources)

        mViewModel.liveData.observe(viewLifecycleOwner) {
            mAdapter?.data = it
            mAdapter?.notifyDataSetChanged()
            val position = getSimulateClickPosition(preCondition, null)
            if (position != -1) {
                view?.let { it1 -> handleItemClick(it1, position) }
            }
        }
    }

    override fun getAdapter(): LocationTriggerAdapter? {
        return mAdapter
    }

    override fun initView(view: View?) {
        super.initView(view)
        mAdapter = LocationTriggerAdapter()
        mAdapter?.setOnItemClickListener(object : OnItemClickListener {
            override fun onItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
                handleItemClick(view, position)
            }
        })
        mViewDataBinding.rvCondition.adapter = mAdapter
    }

    private fun handleItemClick(view: View, position: Int) {
        if (mAdapter?.data?.get(position)?.isEnable == false) {
            return
        }
        if (!DebouncingUtils.isValid(view, SceneEditManager.mViewClickDuration)) return

        mAdapter?.data?.get(position)?.let {
            showLocationDistanceDialog(it)
        }
    }


    private fun showLocationDistanceDialog(item: ConditionItemBean<TriggerLocation>) {

        val condition = if (preCondition != null) {
            if (preCondition.skillId == item.conditionSkillId?.get(0)) {
                preCondition
            } else {
                mViewModel.getConditionValue(resources, item)
            }
        } else {
            mViewModel.getConditionValue(resources, item)
        }

        NavigationConditionDialogFragment.show(
            childFragmentManager,
            item.conditionName,
            condition,
        )
    }
}