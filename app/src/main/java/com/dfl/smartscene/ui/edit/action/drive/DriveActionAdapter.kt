package com.dfl.smartscene.ui.edit.action.drive

import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.android.common.util.MatchableAdapter
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.ActionSkillItemBean
import com.dfl.smartscene.bean.action.DriveType

/**
 * author : raoyulin
 * e-mail : <EMAIL>
 * time : 2025/04/21
 * desc : 动作->驾驶adapter
 * version: 1.0
 */
class DriveActionAdapter : BaseQuickAdapter<ActionSkillItemBean<DriveType>, BaseViewHolder>(
    R.layout.scene_recycle_item_edit_action_dialog_item
), MatchableAdapter<ActionSkillItemBean<DriveType>> {
    override fun convert(holder: BaseViewHolder, item: ActionSkillItemBean<DriveType>) {
        holder.getView<AppCompatImageView>(R.id.iv_action_reflex_icon).setImageResource(item.iconId)
        holder.getView<TextView>(R.id.tv_action_reflex_name).text = item.content
    }

    override val adapterData: List<ActionSkillItemBean<DriveType>>
        get() = data

    override fun findPositionByTitle(title: String): Int {
        return data.indexOfFirst { it.content.replace("\n", "") == title && !it.actionSkillId.isNullOrEmpty() }
    }
}