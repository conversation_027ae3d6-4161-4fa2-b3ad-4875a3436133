package com.dfl.smartscene.ui.overlay.apply

import android.content.res.Configuration
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.dfl.android.common.base.BaseDialogFragment
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.databinding.SceneDialogFragmentLocationFavoritesBinding
import com.dfl.smartscene.ui.edit.action.apply.adapter.LocationFavoritesAdapter
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.util.UIUtils
import com.dfl.soacenter.communication.NaviManager
import com.dfl.soacenter.entity.RoutePoi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference

/**
 *Created by 钟文祥 on 2023/12/18.
 *Describer: 位置收藏夹 选择框
 */
class LocationFavoritesDialogFragment : BaseDialogFragment() {
    private var mBinding: SceneDialogFragmentLocationFavoritesBinding? = null;
    private val viewBinding get() = mBinding!!

    private var selectPoint: RoutePoi? = null //被选中的

    private var mFavoritesList: MutableList<RoutePoi>? = mutableListOf()
    private var mAdapter: LocationFavoritesAdapter? = null

    private var listener: OnInputListener? = null

    companion object {
        fun showFragment(manager: FragmentManager, listener: OnInputListener) {
            val dialog = LocationFavoritesDialogFragment()
            dialog.listener = listener;
            dialog.show(manager, LocationFavoritesDialogFragment.javaClass.name)
        }
    }

    interface OnInputListener {
        fun onSelectPoint(endPoint: RoutePoi?) {} //返回终点实体
        fun cancel() {}
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        mBinding = SceneDialogFragmentLocationFavoritesBinding.inflate(inflater, container, false)
        return viewBinding.root
    }

    override fun initView(view: View) {
        viewBinding.includeDialogBottom.btnPrimary.setOnClickListener(MyOnClickListener(this))
        viewBinding.includeDialogBottom.btnNormal.setOnClickListener(MyOnClickListener(this))

        UIUtils.initRecyclerView(
            activity, viewBinding.rvLocation, LinearLayoutManager(activity), false
        )
        initAdapter()
        updateTvConfirm()
        initViewData()
    }

    private fun initViewData() {
        mFavoritesList?.add(
            RoutePoi(
                getString(R.string.scene_text_action_home),
                "",
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                isEnable = false
            )
        )
        mFavoritesList?.add(
            RoutePoi(
                getString(R.string.scene_text_action_back_company),
                "",
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                isEnable = false
            )
        )
        updateAdapterList()

        lifecycleScope.launch(Dispatchers.IO) {
            //同一soa方法需要串行
            val resHomeListBean = async {
                NaviManager.getFavoriteList(1)
            }.await()
            val resCompanyListBean = async {
                NaviManager.getFavoriteList(2)
            }.await()
            val resListBean = async {
                NaviManager.getFavoriteList(0) //0 普通点 3 全部
            }.await()

            mFavoritesList?.clear()

            if (resHomeListBean?.isSuccess() == true) {
                val homeBean = resHomeListBean.protocolFavPoiInfos?.get(0)
                mFavoritesList?.add(
                    RoutePoi(
                        getString(R.string.scene_text_action_home),
                        homeBean?.favoriteAddress,
                        homeBean?.lon ?: 0.0,
                        homeBean?.lat ?: 0.0,
                        homeBean?.itemId,
                        homeBean?.itemId,
                        homeBean?.itemId,
                    )
                )
            } else {
                mFavoritesList?.add(
                    RoutePoi(
                        getString(R.string.scene_text_action_home),
                        "",
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        isEnable = false
                    )
                )
            }

            if (resCompanyListBean?.isSuccess() == true) {
                val companyListBean = resCompanyListBean.protocolFavPoiInfos?.get(0)
                mFavoritesList?.add(
                    RoutePoi(
                        getString(R.string.scene_text_action_back_company),
                        companyListBean?.favoriteAddress,
                        companyListBean?.lon ?: 0.0,
                        companyListBean?.lat ?: 0.0,
                        companyListBean?.itemId,
                        companyListBean?.itemId,
                        companyListBean?.itemId,
                    )
                )
            } else {
                mFavoritesList?.add(
                    RoutePoi(
                        getString(R.string.scene_text_action_back_company),
                        "",
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        isEnable = false
                    )
                )
            }

            if (resListBean?.isSuccess() == true) {
                resListBean.protocolFavPoiInfos?.forEachIndexed { index, favoriteItemInfo ->
                    mFavoritesList?.add(
                        RoutePoi(
                            favoriteItemInfo.favoriteName,
                            favoriteItemInfo.favoriteAddress,
                            favoriteItemInfo.lon ?: 0.0,
                            favoriteItemInfo.lat ?: 0.0,
                            favoriteItemInfo.itemId,
                            favoriteItemInfo.itemId,
                            favoriteItemInfo.itemId
                        )
                    )
                }
            }
            withContext(Dispatchers.Main) {
                updateAdapterList()
            }
        }
    }

    private fun updateAdapterList() {
        if (mFavoritesList?.size!! == 2) {
            viewBinding.viewEmpty.root.visibility = View.VISIBLE
        } else {
            viewBinding.viewEmpty.root.visibility = View.GONE
        }
        mAdapter?.setList(mFavoritesList)
    }

    override fun initData() {

    }

    private fun initAdapter() {
        mAdapter = LocationFavoritesAdapter()
        mAdapter?.setOnItemClickListener { adapter, view, position ->
            if (mFavoritesList?.get(position)?.isEnable == false) {
                return@setOnItemClickListener
            }
            val lastSelected = mAdapter?.mSelectedIndex
            if (position == lastSelected) { //点击相同项
                mAdapter?.mSelectedIndex = -1
                lastSelected.let {
                    mAdapter?.notifyItemChanged(it)
                }
                selectPoint = null
                updateTvConfirm()
            } else { //点击不同项
                mAdapter?.mSelectedIndex = position

                lastSelected?.let {
                    mAdapter?.notifyItemChanged(it)
                }
                mAdapter?.mSelectedIndex?.let {
                    mAdapter?.notifyItemChanged(it)
                }
                selectPoint = mFavoritesList?.get(position)
                updateTvConfirm()
                //                    viewBinding.ctvGoHome.isChecked = false
                //                    viewBinding.ctvGoCompany.isChecked = false
            }
        }
        viewBinding.rvLocation.adapter = mAdapter
        //        mAdapter?.setEmptyView(R.layout.scene_layout_comment_empty) //放在setadaper后，当0或null显示
    }

    class MyOnClickListener(dialogFragment: LocationFavoritesDialogFragment) : View.OnClickListener {

        private var reference: WeakReference<LocationFavoritesDialogFragment> =
            WeakReference<LocationFavoritesDialogFragment>(dialogFragment)

        override fun onClick(v: View?) {
            if (!DebouncingUtils.isValid(v!!, SceneEditManager.mViewClickDuration)) return
            reference.get()?.let {
                when (v.id) {
                    //                    R.id.ctv_go_home -> {//回家
                    //                        it.viewBinding.ctvGoHome.isChecked = true
                    //                        it.viewBinding.ctvGoCompany.isChecked = false
                    //                        it.selectPoint = it.mHomePoint
                    //                        it.updateTvConfirm()
                    //
                    //                        val lastSelected = it.mAdapter?.mSelectedIndex
                    //                        it.mAdapter?.mSelectedIndex = -1
                    //                        lastSelected?.let { last ->
                    //                            it.mAdapter?.notifyItemChanged(last)
                    //                        }
                    //                    }
                    //                    R.id.ctv_go_company -> { //去公司
                    //                        it.viewBinding.ctvGoHome.isChecked = false
                    //                        it.viewBinding.ctvGoCompany.isChecked = true
                    //
                    //                        it.selectPoint = it.mCompanyPoint
                    //                        it.updateTvConfirm()
                    //
                    //                        val lastSelected = it.mAdapter?.mSelectedIndex
                    //                        it.mAdapter?.mSelectedIndex = -1
                    //                        lastSelected?.let { last ->
                    //                            it.mAdapter?.notifyItemChanged(last)
                    //                        }
                    //                    }
                    R.id.btn_primary -> { //确认
                        it.listener?.let { itListener ->
                            itListener.onSelectPoint(it.selectPoint)
                        }
                        it.dismissWithAnimate()
                    }

                    R.id.btn_normal -> {
                        it.listener?.let { itListener ->
                            itListener.cancel()
                        }
                        it.dismissWithAnimate()
                    }

                    else -> {}
                }
            }

        }
    }


    private fun updateTvConfirm() {
        viewBinding.includeDialogBottom.btnPrimary.isEnabled = selectPoint != null
    }


    override fun onStart() {
        super.onStart()
        dialog?.window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT
        )
        dialog?.window?.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        dialog?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)

    }


    override fun onDestroyView() {
        super.onDestroyView()
        mBinding = null
    }

    override fun getLayoutId(): Int {
        return R.layout.scene_dialog_fragment_location_favorites
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        viewBinding.rvLocation.adapterSkinColor()
    }

}