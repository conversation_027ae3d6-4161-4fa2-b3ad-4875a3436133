package com.dfl.smartscene.ui.edit.condition.trigger.oms

import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.android.common.util.MatchableAdapter
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.TriggerOMS

/**
 *Created by 钟文祥 on 2025/1/2.
 *Describer:
 */
class OMSTriggerAdapter : BaseQuickAdapter<ConditionItemBean<TriggerOMS>, BaseViewHolder>(
    R.layout.scene_recycle_item_edit_condition_dialog_item
), MatchableAdapter<ConditionItemBean<TriggerOMS>> {
    override fun convert(holder: BaseViewHolder, item: ConditionItemBean<TriggerOMS>) {
        holder.getView<TextView>(R.id.tv_condition_reflex_name).text = item.conditionName
        holder.getView<AppCompatImageView>(R.id.iv_condition_reflex_icon).setImageResource(item.conditionIcon)
    }

    override val adapterData: List<ConditionItemBean<TriggerOMS>>
        get() = data

    override fun findPositionByTitle(title: String): Int {
        return data.indexOfFirst { it.conditionName == title && !it.conditionSkillId.isNullOrEmpty() }
    }
}