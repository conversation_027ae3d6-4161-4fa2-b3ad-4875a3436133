package com.dfl.smartscene.ui.edit.action.apply.adapter

import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.smartscene.R
import com.dfl.soacenter.entity.PoiItem

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/10/21
 * desc :去某地，途径某地搜索地址结果列表适配器
 * version: 1.0
 */
class SearchAddressAdapter2a : BaseQuickAdapter<PoiItem, BaseViewHolder>(
    R.layout.scene_recycle_item_search_address_item_2a
) {
    private var mKeyWord = ""
    override fun convert(holder: BaseViewHolder, item: PoiItem) {
        val address = holder.getView<TextView>(R.id.tv_address_item)
        val detail = holder.getView<TextView>(R.id.address_detail_item)
        address.text = item.name
        detail.text = item.addr
        stringInterceptionChangeRed(address, mKeyWord, address.text.toString())
    }

    fun setKeyWord(key: String) {
        this.mKeyWord = key
    }

    /**
     * TextView部分文字变色
     * keyword = 关键字、需要变色的文字   string = 包含变色文字的全部文字
     */
    private fun stringInterceptionChangeRed(textView: TextView, keyword: String?, string: String) {
        if (keyword == null || keyword.trim().isEmpty()) return
        if (!string.contains(keyword)) return
        val start = string.indexOf(keyword)
        val end = start + keyword.length
        if (end != 0 && start != -1) {
            val style = SpannableStringBuilder()
            style.append(string)
            //设置部分文字颜色
            val foregroundColorSpan = ForegroundColorSpan(
                ContextCompat.getColor(context, R.color.common_lib_color_brand_highlight)
            )
            style.setSpan(foregroundColorSpan, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            textView.text = style
        }
    }
}