package com.dfl.smartscene.ui.overlay.common.checklist

import android.view.View
import android.widget.RadioButton
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.CheckListBean
import me.jessyan.autosize.utils.AutoSizeUtils

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/09/10
 * desc : ui2.0带背景的单选框
 * version: 1.0
 */
interface OnRadioItemClickListener {
    fun onRadioItemClick(position: Int)
}

class RadioCheckAdapter(
    private var listener: OnRadioItemClickListener?
) : BaseQuickAdapter<CheckListBean, BaseViewHolder>(
    R.layout.scene_recycle_item_radio_check
) {

    override fun convert(holder: BaseViewHolder, item: CheckListBean) {
        val radio = holder.getView<RadioButton>(R.id.rb_radio)
        radio.text = item.content
        radio.setButtonDrawable(if (item.isSingleSelectIcon) R.drawable.scene_selector_me_radio_check_style else R.drawable.scene_selector_me_radio_multiple_check_style)
        radio.isChecked = item.isCheck
        
        // 根据item总数动态调整item尺寸
        adjustItemSize(holder)
        
        //更多
        holder.getView<View>(R.id.view_more).visibility = if (item.isItemShowMoreIcon) View.VISIBLE else View.GONE
        if (item.isItemShowMoreIcon) {
            holder.itemView.setOnClickListener {
                listener?.onRadioItemClick(holder.position)
            }
        }
    }
    
    /**
     * 根据item总数动态调整item尺寸
     * @param holder ViewHolder
     */
    private fun adjustItemSize(holder: BaseViewHolder) {
        val itemView = holder.itemView
        val layoutParams = itemView.layoutParams
        
        when {
            data.size == 1 || data.size == 2 -> {
                // 1个或2个item：item尺寸616*120
                layoutParams.width = AutoSizeUtils.dp2px(context, 616f)
                layoutParams.height = AutoSizeUtils.dp2px(context, 120f)
            }
            data.size >= 3 -> {
                // 3个及以上item：item尺寸430*120
                layoutParams.width = AutoSizeUtils.dp2px(context, 430f)
                layoutParams.height = AutoSizeUtils.dp2px(context, 120f)
            }
        }
        
        itemView.layoutParams = layoutParams
    }

    // 提供解绑监听器的方法
    fun detachListener() {
        listener = null
    }
}