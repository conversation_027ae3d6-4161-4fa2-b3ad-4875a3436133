package com.dfl.smartscene.ui.overlay.myscene

import android.graphics.Rect
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.dfl.android.common.base.BaseMenuPopup
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.main.MySceneBean
import com.dfl.smartscene.rv.listener.OnItemClickListener

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/20
 * desc   :
 * version: 1.0
 */
class MySceneMenuPopup(onMenuClickListener: OnMenuClickListener<MySceneBean>) :
    BaseMenuPopup<MySceneBean>(onMenuClickListener), OnItemClickListener<String> {
    companion object {
        const val INDEX_DELETE = 0  // 现在删除是唯一选项，索引为0
    }

    override val TAG = GlobalConstant.GLOBAL_TAG.plus("MySceneMenuPopup")
    private var listView: RecyclerView? = null
    
    /**
     * 显示菜单弹窗
     * @param view 触发显示的视图（ll_menu按钮）
     */
    override fun show(view: View) {
        // 获取item的根视图
        val itemRootView = findItemRootView(view)
        if (itemRootView == null) {
            CommonLogUtils.logE(TAG, "Cannot find item root view")
            return
        }
        
        val context = view.context
        
        if (mPopupWindow == null) {
            val rootView = LayoutInflater.from(context).inflate(R.layout.scene_popup_my_scene_menu, null)
            listView = rootView.findViewById(R.id.rv_my_scene_menu_list)
            val mCslBgPop = rootView.findViewById<ConstraintLayout>(R.id.csl_pop_bg)

            listView?.layoutManager = LinearLayoutManager(context)
            
            // 使用item的尺寸作为PopupWindow的尺寸，实现局部蒙层效果
            val popup = PopupWindow(rootView, itemRootView.width, itemRootView.height)
            popup.isOutsideTouchable = true
            popup.isFocusable = true
            popup.animationStyle = R.style.popupAnimationTheme
            mPopupWindow = popup
            
            // 点击蒙层关闭弹窗
            mCslBgPop.setOnClickListener {
                popup.dismiss()
            }
        }
        
        // 设置菜单适配器
        val menuAdapter = MySceneMenuAdapter(context, this)
        listView?.adapter = menuAdapter
        
        val menuPopup = mPopupWindow ?: return
        
        // 获取item在屏幕中的位置
        val itemLocation = IntArray(2)
        itemRootView.getLocationInWindow(itemLocation)
        
        // 显示PopupWindow，位置基于item
        menuPopup.showAtLocation(
            itemRootView, 
            Gravity.NO_GRAVITY, 
            itemLocation[0], 
            itemLocation[1]
        )
        
        CommonLogUtils.logD(TAG, "Menu popup shown at position: ${itemLocation[0]}, ${itemLocation[1]}")
    }
    
    /**
     * 查找item的根视图
     * @param view 当前视图
     * @return item的根视图
     */
    private fun findItemRootView(view: View): View? {
        var parent = view.parent
        while (parent != null && parent is View) {
            val parentView = parent as View
            // 查找id为cl_root_me的视图，这是item的根容器
            if (parentView.id == R.id.cl_root_me) {
                return parentView
            }
            parent = parentView.parent
        }
        return null
    }

    override fun dismiss() {
        mPopupWindow?.dismiss()
    }

    /**
     * 处理菜单项点击事件
     * @param position 点击位置
     * @param data 点击的数据
     */
    override fun onItemClick(position: Int, data: String) {
        // 由于现在只有删除选项，position始终为0，对应删除操作
        onMenuClickListener?.onMenuItemSelected(this, INDEX_DELETE, rvDataIndex, rvData)
    }
}