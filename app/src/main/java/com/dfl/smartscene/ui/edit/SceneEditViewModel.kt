package com.dfl.smartscene.ui.edit

import android.os.Parcel
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.common.global.GlobalConfig
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.edit.SceneActionBean
import com.dfl.smartscene.bean.edit.SceneConditionBean
import com.dfl.smartscene.bean.edit.SkillInfoBean
import com.dfl.smartscene.bean.main.MySceneBean
import com.dfl.smartscene.bean.main.ScenarioBean
import com.dfl.smartscene.soa.SceneConditionManager
import com.dfl.smartscene.soa.SceneManager
import com.dfl.smartscene.util.ConditionOrActionIconUtils
import com.dfl.smartscene.util.MMKVUtils
import com.iauto.scenarioadapter.ScenarioInfo

/**
 *Created by 钟文祥 on 2024/10/17.
 *Describer:
 */
class SceneEditViewModel : BaseViewModel() {
    companion object {
        private const val TAG = GlobalConstant.GLOBAL_TAG + "SceneEditViewModel2"
    }

    //原始的数据，不是进来时的数据，可能会被草稿覆盖
    var mEnterBean: MySceneBean? = null

    //进入的类型  1下载（编辑场景），2发现详情，用模板进入（编辑场景），3我的场景（编辑场景）,4创建场景 ,5草稿（编辑场景）
    var mEnterType: SceneNewEditEnterType? = SceneNewEditEnterType.Normal

    //社区id
    var mEnterScenarioL3id: String? = null

    /**界面实时的数据对象*/
    var curSceneEditBean: MySceneBean? = null

    // scenario
    var scenarioBeanLiveData = MutableLiveData<ScenarioBean>()

    /**条件数据回调*/
    var sceneConditionLiveData = MutableLiveData<ArrayList<SceneConditionBean>>()

    /**动作数据回调*/
    var sceneActionLiveData = MutableLiveData<ArrayList<SceneActionBean>>()

    /**能力图标数据回调*/
    var mSceneSkillInfoLiveData = MutableLiveData<ArrayList<SkillInfoBean>>()

    // 是否改变了原始数据 true：重置按钮可用，false：按钮不可用
    var btnReSetIsEnAbleLiveData = MutableLiveData<Boolean>()

    // 场景当前修改后能否达成条件 保存 完成按钮
    var sceneEditReadyLiveData = MutableLiveData<Boolean>()

    //是否可试用状态
    var tryStartSceneReadyLiveData = MutableLiveData<Boolean>()

    //试用状态
    var onTryStartSceneLiveData = MutableLiveData<Boolean>()

    /**是否下载来的场景*/
    var isDownLoadSceneLiveData = MutableLiveData<Boolean>()

    /**初始页状态 ， 0 :数据初始化时，不做处理，做界面初始化。1：即第一次进来时的样子，不管有没有内容 。就是初始页 。2或以上非初始页*/
    var isFirstState: Int = 0

    //设置进来的数据
    fun setEnterData(enterBean: MySceneBean?, enterType: SceneNewEditEnterType, enterScenarioL3id: String?) {
        this.mEnterType = enterType
        this.mEnterScenarioL3id = enterScenarioL3id

        if (enterType == SceneNewEditEnterType.AddNew) {
            val draftBean = getCacheEditData(enterBean?.scenario?.scenarioInfo?.scenarioId)
            draftBean?.let {
                this.mEnterType = SceneNewEditEnterType.Draft
                saveEnterBean2Save(it)
            }
        }
        if (enterType == SceneNewEditEnterType.TTS) {
            this.mEnterType = SceneNewEditEnterType.AddNew
        }

        if (this.mEnterType != SceneNewEditEnterType.Draft) {
            saveEnterBean2Save(enterBean)
        }

        this.mEnterBean = getContent2Save()
        this.onTryStartSceneLiveData.value = false
    }

    /**保存原始数据*/
    private fun saveEnterBean2Save(enterBean: MySceneBean?) {
        MMKVUtils.getInstance().saveDownloadOriginalSceneEditBean(enterBean)
    }

    /**获取原始数据*/
    private fun getContent2Save(): MySceneBean? {
        return MMKVUtils.getInstance().downloadOriginalSceneEditBean
    }

    private fun copyScenarioBean(source: ScenarioBean?): ScenarioBean {
        val parcel = Parcel.obtain()
        source?.writeToParcel(parcel, 0)
        parcel.setDataPosition(0)
        val copy = ScenarioBean.createFromParcel(parcel)
        parcel.recycle()
        return copy
    }

    /**重置*/
    fun reset() {
        isFirstState = 0
        updateCurSceneAndUI(this.mEnterBean)
        onTryStartSceneLiveData.postValue(false)
    }

    fun updateCurSceneAndUI(bean: MySceneBean?) {
        if (bean == null) {
            this.curSceneEditBean = null
            return
        }
        this.curSceneEditBean = MySceneBean(
            scenario = copyScenarioBean(bean.scenario),
            isSceneStart = bean.isSceneStart,
            isTop = bean.isTop,
            isNewSign = bean.isNewSign,
            isAddNewScene2ShowAnima = bean.isAddNewScene2ShowAnima,
            isAddScene = bean.isAddScene,
            meIconBeanList = bean.meIconBeanList
        )
        scenarioBeanLiveData.postValue(curSceneEditBean?.scenario)


        updateConditions() //条件
        updateActions() //动作
        updateIcons() //小图标

    }

    //条件数据更新
    fun updateConditions() {
        curSceneEditBean?.let {
            val list = ArrayList<SceneConditionBean>()
            //根据json提供的0.5版本提示会添加对应的字段表示触发条件所以不需要根据SkillId去判断筛选
            //是故此处的筛选后续将在jar更新后同步修改为对应的集合标签即可
            val statusConditions = it.scenario.scenarioInfo?.conditions
            val triggerConditions = it.scenario.scenarioInfo?.edgeCondition
            //缓存当前场景的状态条件的数据
            statusConditions?.let {
                if (statusConditions.size > 0) {
                    SceneConditionManager.setConditionSkillIdList(statusConditions)
                } else {
                    SceneConditionManager.clearSkillIdData()
                }
            }

            if (triggerConditions != null) {
                val trigger = if (triggerConditions.skillId == SkillsListConstant.SKILL_ID_TRIGGER_DOOR_ALL_STATE) {
                    triggerConditions
                } else {
                    null
                }
                val conditionBean = SceneConditionBean(
                    null,
                    triggerConditions,
                    SceneConditionBean.ADD_CONDITION_CONTENT_ITEM,
                    SceneConditionBean.ConditionType.TRIGGER_CONDITION,
                    ConditionOrActionIconUtils.getTriggerConditionIcon(
                        triggerConditions.skillId, trigger
                    ),
                    SceneConditionBean.ITEM_CONTENT
                )
                list.add(conditionBean)
            }

            statusConditions?.forEach { condition ->
                val conditionBean = SceneConditionBean(
                    null,
                    condition,
                    SceneConditionBean.ADD_CONDITION_CONTENT_ITEM,
                    SceneConditionBean.ConditionType.STATUS_CONDITION,
                    ConditionOrActionIconUtils.getStatusConditionIcon(condition.skillId),
                    SceneConditionBean.ITEM_CONTENT
                )
                list.add(conditionBean)
            }
            if (list.size < 6) {
                list.add(
                    SceneConditionBean(
                        if (statusConditions != null && statusConditions.size != 0) "1" else "0", //注：这里title设置1和0是不想添加标签去判断添加条件是必填状态还是初始状态
                        null,
                        SceneConditionBean.ADD_CONDITION_ITEM,
                        if (triggerConditions == null) SceneConditionBean.ConditionType.TRIGGER_CONDITION
                        else SceneConditionBean.ConditionType.STATUS_CONDITION,
                        R.drawable.scene_icon_me_action_add,
                        SceneConditionBean.ITEM_CONTENT
                    )
                )
            }
            sceneConditionLiveData.postValue(list)
        }
    }

    //动作数据更新
    private fun updateActions() {
        curSceneEditBean?.let {
            val list = ArrayList<SceneActionBean>()
            it.scenario.scenarioInfo?.sequence?.forEach { sequence ->
                list.add(
                    SceneActionBean(
                        sequence,
                        ConditionOrActionIconUtils.getActionBgImg(sequence.action.skillId),
                        SceneActionBean.ADD_ACTION_CONTENT_ITEM
                    )
                )
            }
            //动作个数不能大于30
            if (list.size < GlobalConfig.SCENE_ACTION_COUNT_MAX) {
                list.add(
                    SceneActionBean(
                        null, R.drawable.scene_selector_edit_action_item_bg, SceneActionBean.ADD_ACTION_ITEM
                    )
                )
            }
            sceneActionLiveData.postValue(list)
        }
    }

    //小图标数据更新
    fun updateIcons() {
        curSceneEditBean?.let {
            val list = ArrayList<SkillInfoBean>()
            //            if (it.scenario?.isAutoRun==true){
            val triggerCon = it.scenario.scenarioInfo?.edgeCondition
            val statusList = it.scenario.scenarioInfo?.conditions
            val actionList = it.scenario.scenarioInfo?.sequence
            //条件9个+动作9个+箭头=19个
            //1、判断如果条件+动作<18的情况，正常显示
            //2、条件+动作>18，
            val conditionSize = if (triggerCon != null) {
                1 + (statusList?.size ?: 0)
            } else {
                statusList?.size ?: 0
            }
            val actionSize = actionList?.size ?: 0
            val iconSize = conditionSize + actionSize
            //type 0：size<18；1：size>18并且条件>一半动作小于一半；
            //2:与1状态相反，3：size>18并且条件动作都大于一半
            //4：仅条件并大于18；5仅动作并大于18
            val type = if (iconSize > 18) {
                if (actionSize == 0) { //仅条件
                    if (iconSize == 19) 0 else 4
                } else if (conditionSize == 0) { //仅动作
                    if (iconSize == 19) 0 else 5
                } else if (actionSize <= 9 && conditionSize > 9) {
                    1
                } else if (conditionSize <= 9 && actionSize > 9) {
                    2
                } else {
                    3
                }
            } else {
                0
            }


            if (triggerCon != null) {
                val trigger = if (triggerCon.skillId == SkillsListConstant.SKILL_ID_TRIGGER_DOOR_ALL_STATE) {
                    triggerCon
                } else {
                    null
                }
                list.add(
                    SkillInfoBean(
                        ConditionOrActionIconUtils.getTriggerConditionIcon(
                            triggerCon.skillId, trigger
                        ), triggerCon.skillId, SkillInfoBean.SkillIconType.TRIGGER, triggerCon.category
                    )
                )
            }

            if (statusList != null && statusList.isNotEmpty()) {
                kotlin.run {
                    statusList.forEach { condition ->
                        list.add(
                            SkillInfoBean(
                                ConditionOrActionIconUtils.getStatusConditionIcon(condition.skillId),
                                condition.skillId,
                                SkillInfoBean.SkillIconType.STATUS,
                                condition.category
                            )
                        )
                        if (type == 4 && list.size == 18) {
                            list.add(
                                SkillInfoBean(
                                    -1, -1, SkillInfoBean.SkillIconType.MORE, ""
                                )
                            )
                            return@run
                        }
                        if (type == 3 && list.size == 8) {
                            list.add(
                                SkillInfoBean(
                                    -1, -1, SkillInfoBean.SkillIconType.MORE, ""
                                )
                            )
                            return@run
                        }
                        if (type == 1 && list.size == 17 - actionSize) {
                            list.add(
                                SkillInfoBean(
                                    -1, -1, SkillInfoBean.SkillIconType.MORE, ""
                                )
                            )
                            return@run
                        }
                    }
                }
            }

            if (actionList != null && actionList.isNotEmpty()) {
                if (type != 4 || type != 5) {
                    if (list.size != 0) {
                        list.add(
                            SkillInfoBean(
                                R.drawable.scene_img_me_edit_arrow, -1, SkillInfoBean.SkillIconType.ARROW, ""
                            )
                        )
                    }
                }

                kotlin.run {
                    actionList.forEach { action ->
                        list.add(
                            SkillInfoBean(
                                ConditionOrActionIconUtils.getActionIcon(action.action.skillId),
                                action.action.skillId,
                                SkillInfoBean.SkillIconType.ACTION,
                                action.action.category
                            )
                        )
                        if ((type == 5 || type == 2 || type == 3) && list.size == 18) {
                            list.add(
                                SkillInfoBean(
                                    -1, -1, SkillInfoBean.SkillIconType.MORE, ""
                                )
                            )
                            return@run
                        }
                    }
                }
            }
            mSceneSkillInfoLiveData.postValue(list)
        }
    }

    //按钮状态更新
    fun updateBtnState() {
        if (this.curSceneEditBean == null) return

        curSceneEditBean?.let {
            //1、场景当前修改后能否达成条件 保存
            var isSceneEditReady = false
            if (!it.scenario.scenarioInfo?.scenarioName.isNullOrEmpty()) {
                //有触发条件和动作
                if (it.scenario.scenarioInfo?.edgeCondition != null && //
                    !it.scenario.scenarioInfo?.sequence.isNullOrEmpty()
                ) {
                    isSceneEditReady = true
                }
                //无触发条件和状态条件但有动作
                else if (it.scenario.scenarioInfo?.edgeCondition == null && //
                    it.scenario.scenarioInfo?.conditions.isNullOrEmpty() && //
                    !it.scenario.scenarioInfo?.sequence.isNullOrEmpty()
                ) {
                    isSceneEditReady = true
                }
            }
            sceneEditReadyLiveData.postValue(isSceneEditReady)

            //2、是否可试用
            var isTryStartSceneReady = false
            if (!it.scenario.scenarioInfo?.sequence.isNullOrEmpty()) {
                isTryStartSceneReady = true
            }
            tryStartSceneReadyLiveData.postValue(isTryStartSceneReady)

            //3、重置按钮是否可用
            btnReSetIsEnAbleLiveData.postValue(checkDiff())
        }
    }

    /**判断现在数据与原始数据有没有发生改变*/
    private fun checkDiff(): Boolean {
        val a0 = mEnterBean?.scenario
        val a1 = curSceneEditBean?.scenario

        if (a0?.scenarioInfo?.scenarioName != a1?.scenarioInfo?.scenarioName) {
            return true
        }
        if (a0?.scenarioInfo?.scenarioDesc != a1?.scenarioInfo?.scenarioDesc) {
            return true
        }
        if (a0?.scenarioInfo?.edgeCondition?.desc != a1?.scenarioInfo?.edgeCondition?.desc) {
            return true
        }
        if ((a0?.scenarioInfo?.conditions?.size ?: 0) != (a1?.scenarioInfo?.conditions?.size ?: 0)) {
            return true
        }
        if ((a0?.scenarioInfo?.sequence?.size ?: 0) != (a1?.scenarioInfo?.sequence?.size ?: 0)) {
            return true
        }
        a0?.scenarioInfo?.conditions?.forEachIndexed { index, condition ->
            if (condition.desc != a1?.scenarioInfo?.conditions?.get(index)?.desc) {
                return true
            }
        }
        a0?.scenarioInfo?.sequence?.forEachIndexed { index, sequence ->
            if (sequence.action.desc != a1?.scenarioInfo?.sequence?.get(index)?.action?.desc) {
                return true
            }
        }

        if (getConditionNum(a0?.scenarioInfo!!) > 0 && getConditionNum(a1?.scenarioInfo!!) > 0) {
            if (a0.isAutoRun != a1.isAutoRun) {
                return true
            }
            if (a0.isAskBeforeAutoRun != a1.isAskBeforeAutoRun) {
                return true
            }
            if (a0.executeFrequency != a1.executeFrequency) {
                return true
            }
        }
        return false
    }

    /**获取条件的总数*/
    private fun getConditionNum(bean: ScenarioInfo): Int {
        var num = 0
        if (bean.edgeCondition != null) {
            num++
        }
        if (bean.conditions != null) {
            num += bean.conditions.size
        }
        return num
    }

    //----试用相关 start---
    fun tryStartScene() {
        if (onTryStartSceneLiveData.value == true) {
            return
        }
        //临停判断
        if (SceneManager.isOnDisableSceneStatus()) {
            return
        }
        onTryStartSceneLiveData.postValue(true)
        curSceneEditBean?.scenario?.scenarioInfo?.let {
            SceneManager.trySceneInfo(it)
        }
    }

    fun stopTryScene() {
        if (onTryStartSceneLiveData.value == true) {
            curSceneEditBean?.scenario?.scenarioInfo?.let {
                SceneManager.stopTryScene(it.scenarioId)
            }
        }
    }

    fun handleTrySceneEnd(sceneId: String?) {
        if (curSceneEditBean?.scenario?.scenarioInfo?.scenarioId == sceneId) {
            onTryStartSceneLiveData.postValue(false)
        }
    }
    //----试用相关 end---

    /**获取当前场景已添加动作的数量*/
    fun getActionSize(): Int {
        return curSceneEditBean?.scenario?.scenarioInfo?.sequence?.size ?: 0
    }

    fun getCacheEditData(sceneId: String?): MySceneBean? {
        val bean = MMKVUtils.getInstance().getSceneEditData(true, sceneId)
        return bean
    }

    /**显示下载进来的场景界面*/
    fun showDownLoadView() {
        isDownLoadSceneLiveData.postValue(true)
    }

    fun initClearBtn() {
        btnReSetIsEnAbleLiveData.postValue(false)
    }

    //自动执行事件
    fun notifyAutoRunStateChanged(isChecked: Boolean) {
        //0是自动执行，1是不自动执行
        curSceneEditBean?.let {
            it.scenario.isAutoRun = isChecked
            updateBtnState()
        }
    }

    //执行前询问事件
    fun notifyAskBeforeStateChanged(isChecked: Boolean) {
        //0是执行前询问，1是不执行前询问
        curSceneEditBean?.let {
            it.scenario.isAskBeforeAutoRun = isChecked
            updateBtnState()
        }
    }

    //执行频率变更事件
    fun notifyExeFrequencyStateChanged(exeFrequency: ExecuteFrequency) {
        /**
         *  0 不限次数1全天执行一次2开机执行一次
         */
        curSceneEditBean?.let {
            it.scenario.executeFrequency = exeFrequency.value
            updateBtnState()
        }
    }


    fun checkSceneName(): Boolean {
        curSceneEditBean?.scenario?.scenarioInfo?.let {
            return SceneManager.checkSceneName(it.scenarioName, it.scenarioId)
        }
        return false
    }

    /**保存草稿*/
    fun saveCacheEditData() {
        MMKVUtils.getInstance()
            .saveSceneEditData(true, curSceneEditBean?.scenario?.scenarioInfo?.scenarioId, curSceneEditBean)
    }

    /**删除当前的草稿*/
    private fun deleteCurCacheEditData() {
        curSceneEditBean?.scenario?.let {
            MMKVUtils.getInstance().deleteSceneEditData(true, null)
        }
    }

    /**保存场景*/
    fun saveScene(): Boolean {
        curSceneEditBean?.let {
            it.scenario.scenarioInfo?.scenarioId ?: return false
            it.scenario.scenarioInfo?.autoExeFlag = if (it.scenario.isAutoRun == true) 0 else 1
            it.scenario.scenarioInfo?.secondAskeFlag = if (it.scenario.isAskBeforeAutoRun == true) 0 else 1
            it.scenario.scenarioInfo?.executeFrequency = it.scenario.executeFrequency ?: 0

            if (it.isAddScene) { //为需要创建并保存到我的场景的场景 ，否为需要编辑并保存的场景
                deleteCurCacheEditData()
                return SceneManager.addSceneInfo(it)
            } else {
                return SceneManager.modifySceneInfo(it)
            }
        }
        return false
    }

    /**
     * 自动执行 启动
     * SceneManager中autoExecuteScene的封装
     * autoExeRunState-0，ccm允许自动执行;1表示ccm终止自动执行。
     * autoRunBtnState-0，表示自动执行开关为开;1表示自动执行开关为关 好像没用
     * scenarioId 场景ID
     */
    fun autoExecuteScenario() {
        curSceneEditBean?.scenario?.scenarioInfo?.let {
            SceneManager.autoExecuteScene(0, curSceneEditBean?.scenario, 0, 0)
        }
    }

    enum class ExecuteFrequency(var value: Int) {
        NO_LIMIT(0), DAILY(1), ONCE_DURING_POWER_ON(2)
    }

    fun convertFrequencyCode(code: Int): ExecuteFrequency {
        for (frequency in ExecuteFrequency.values()) {
            if (frequency.value == code) {
                return frequency
            }
        }
        throw IllegalArgumentException("未知的执行频率代码: $code")
    }
}