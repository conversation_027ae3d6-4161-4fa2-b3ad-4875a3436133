package com.dfl.smartscene.ui.overlay.ambientlight

import android.content.Context
import android.os.Bundle
import android.view.View
import android.widget.CheckedTextView
import android.widget.RadioButton
import android.widget.TextView
import com.dfl.android.common.base.BaseDialog
import com.dfl.android.common.util.ObjectUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.databinding.SceneDialogSceneLightEffectBinding
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.smartscene.soa.getSequenceValueByName
import com.iauto.scenarioadapter.ScenarioInfo
import org.greenrobot.eventbus.EventBus

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2023/03/20
 * desc :623氛围灯场景灯效弹窗
 * version: 1.0
 */
@Deprecated("623 氛围场景灯效,lk1a不使用")
class SceneLightEffectDialog(
    context: Context, private val mTitle: String, private val mActionList: ArrayList<ScenarioInfo.Sequence>
) : BaseDialog(context), View.OnClickListener {
    private lateinit var mVB: SceneDialogSceneLightEffectBinding
    private var mRbDriveMode: RadioButton? = null
    private var mRbAirMode: RadioButton? = null
    private var mOpenCtv: CheckedTextView? = null
    private var mCloseCtv: CheckedTextView? = null
    private var mTitleTv: TextView? = null
    private var preSetAction: ScenarioInfo.Sequence? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mVB = SceneDialogSceneLightEffectBinding.inflate(layoutInflater)
        setContentView(mVB.root)
        initView()
        setupInitData()
    }

    private fun setupInitData() {
        preSetAction?.let {
            if (ObjectUtils.checkScenarioDataIsEmpty(preSetAction)
                    || ObjectUtils.checkScenarioDataIsEmpty(mActionList)
                    || it.action.skillId != mActionList.get(0).action.skillId) return
            val mode = getSequenceValueByName(it, SkillsListConstant.INPUT_ARG_MODE)
            val status = getSequenceValueByName(it, SkillsListConstant.INPUT_ARG_STATUS)
            if (mode == "1") {
                setCheckDriveMode()
            } else if (mode == "2") {
                setCheckAirMode()
            }

            if (status == "0") {
                setCheckCloseItem()
            } else if (status == "1") {
                setCheckOpenItem()
            }
        }
    }

    private fun initView() {
        mTitleTv = findViewById(R.id.tv_light_effect_title)
        mTitleTv?.text = mTitle
        mRbDriveMode = findViewById(R.id.rb_drive_mode_effect)
        mRbAirMode = findViewById(R.id.rb_air_mode_effect)
        mOpenCtv = findViewById(R.id.ctv_open_effect)
        mCloseCtv = findViewById(R.id.ctv_close_effect)
        mVB.includeDialogBottom.btnPrimary.setOnClickListener(this)
        mVB.includeDialogBottom.btnNormal.setOnClickListener(this)
        mRbDriveMode?.setOnClickListener(this)
        mRbAirMode?.setOnClickListener(this)
        mOpenCtv?.setOnClickListener(this)
        mCloseCtv?.setOnClickListener(this)
    }

    fun setupPreAction(action: ScenarioInfo.Sequence?) {
        this.preSetAction = action
    }

    override fun onClick(v: View?) {
        v?.id?.let {
            when (it) {
                R.id.btn_normal -> dismissWithAnimate()
                R.id.ctv_open_effect -> {
                    setCheckOpenItem()
                }

                R.id.ctv_close_effect -> {
                    setCheckCloseItem()
                }

                R.id.btn_primary -> {
                    val action = mActionList[0]
                    action.action.category = mTitle
                    action.action.desc = if (mRbDriveMode?.isChecked == true) {
                        context.getString(R.string.scene_text_action_drive_mode).plus(
                            context.getString(
                                if (mOpenCtv?.isChecked == true) R.string.scene_text_common_open
                                else R.string.scene_text_common_close
                            )
                        )
                    } else {
                        context.getString(R.string.scene_text_action_air_mode).plus(
                            context.getString(
                                if (mOpenCtv?.isChecked == true) R.string.scene_text_common_open
                                else R.string.scene_text_common_close
                            )
                        )
                    }
                    action.action.input[0].value = if (mRbDriveMode?.isChecked == true) "1" else "2"
                    action.action.input[1].value = if (mOpenCtv?.isChecked == true) "1" else "0"
                    EventBus.getDefault().post(action)
                    dismissWithAnimate()
                }

                R.id.rb_drive_mode_effect -> {
                    setCheckDriveMode()
                }

                R.id.rb_air_mode_effect -> {
                    setCheckAirMode()
                }
            }
        }

    }

    private fun setCheckAirMode() {
        mRbDriveMode?.isChecked = false
        mRbAirMode?.isChecked = true
    }

    private fun setCheckDriveMode() {
        mRbAirMode?.isChecked = false
        mRbDriveMode?.isChecked = true
    }

    private fun setCheckCloseItem() {
        mOpenCtv?.isChecked = false
        mCloseCtv?.isChecked = true
    }

    private fun setCheckOpenItem() {
        mOpenCtv?.isChecked = true
        mCloseCtv?.isChecked = false
    }
}