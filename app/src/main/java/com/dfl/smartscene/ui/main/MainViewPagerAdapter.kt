package com.dfl.smartscene.ui.main

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.dfl.smartscene.community.home.CommunityFragment
import com.dfl.smartscene.ui.main.discover.card.SceneCardFragment
import com.dfl.smartscene.ui.main.me.MeFragment

/**
 *Created by 钟文祥 on 2024/3/29.
 *Describer:
 */
class MainViewPagerAdapter(
    fragmentActivity: FragmentActivity, var tabTypeList: List<MainActivity.MainTagType>
) : FragmentStateAdapter(fragmentActivity) {

    override fun createFragment(position: Int): Fragment {
        return when (tabTypeList[position]) {
            MainActivity.MainTagType.COMMUNITY -> CommunityFragment()
            MainActivity.MainTagType.ME -> MeFragment()
            MainActivity.MainTagType.DISCOVER -> SceneCardFragment()
        }
    }

    override fun getItemCount(): Int {
        return tabTypeList.size
    }

    //根据数据生成唯一id**
    // 如果不重写，那么在调用[notifyDataSetChanged]更新的时候**
    // 会抛出```new IllegalStateException("Fragment already added")```异常
    override fun getItemId(position: Int): Long {
        return tabTypeList[position].value.toLong()
    }

    override fun containsItem(itemId: Long): Boolean {
        for (tabType in tabTypeList) {
            if (tabType.value.toLong() == itemId) {
                return true
            }
        }
        return false
    }
}