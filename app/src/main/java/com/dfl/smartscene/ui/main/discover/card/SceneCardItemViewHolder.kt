package com.dfl.smartscene.ui.main.discover.card

import android.view.View
import androidx.databinding.ViewDataBinding
import com.dfl.smartscene.BR
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.main.SceneCardBean
import com.dfl.smartscene.rv.viewholder.DataBindingViewHolder
import com.dfl.smartscene.widget.recycleview.ItemHolderMoveCallback

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/19
 * desc   :
 * version: 1.0
 */
class SceneCardItemViewHolder(viewDataBinding: ViewDataBinding) :
    DataBindingViewHolder<SceneCardBean>(viewDataBinding), View.OnClickListener,
    ItemHolderMoveCallback {

    companion object Instance {
        const val LAYOUT_ID = R.layout.scene_recycle_item_discover_scene_card
    }

    override fun getBindingVariable(): Int {
        return BR.bean
    }

    init {
        itemView.setOnClickListener(this)
    }

    override fun onClick(v: View?) {
        val adapter = bindingAdapter
        if (adapter !is SceneCardItemAdapter) {
            return
        }
        //        adapter.onItemViewClick(absoluteAdapterPosition)
    }

    override fun onItemHolderMoveStart(position: Int?) {
        itemView.setBackgroundResource(R.drawable.scene_shape_me_bg_rect_press)
    }

    override fun onItemHolderMoveEnd(position: Int?) {
        itemView.setBackgroundResource(R.drawable.scene_shape_me_bg_rect)
    }
}