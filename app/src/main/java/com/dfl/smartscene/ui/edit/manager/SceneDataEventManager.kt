package com.dfl.smartscene.ui.edit.manager

import com.dfl.android.common.util.StringUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.ui.edit.container.SceneNewEditType
import com.iauto.scenarioadapter.ScenarioInfo
import com.iauto.scenarioadapter.ScenarioInfo.Condition

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2023/03/01
 * desc :缓存添加场景的类别埋点
 * version: 1.0
 */
object SceneDataEventManager {

    fun getTriggerConditionDetail(edgeCondition: Condition?): String {
        edgeCondition?.let {
            return "触发条件-" + it.category + "-" + it.desc + "-" + it.skillId
        }
        return ""
    }

    fun getStatusConditionDetail(list: List<ScenarioInfo.Condition>?): String {
        val sb = StringBuilder()
        if (!list.isNullOrEmpty()) {
            try {
                for (i in list.indices) {
                    if (i != list.size - 1) {
                        sb.append("状态条件-" + list[i].category + "-" + list[i].desc + "-" + list[i].skillId + "\n")
                    } else {
                        sb.append("状态条件-" + list[i].category + "-" + list[i].desc + "-" + list[i].skillId)
                    }
                }
            } catch (e: Exception) {
                CommonLogUtils.logD("SceneDataEventManager", "SOA condition data has exception:${e.message}")
            }

        }
        return sb.toString()
    }

    fun getActionDetail(list: List<ScenarioInfo.Sequence>?): String {
        val sb = StringBuilder()
        if (!list.isNullOrEmpty()) {
            try {
                for (i in list.indices) {
                    if (i != list.size - 1) {
                        sb.append("动作-" + list[i].action.category + "-" + list[i].action.desc + "-" + list[i].action.skillId + "\n")
                    } else {
                        sb.append("动作-" + list[i].action.category + "-" + list[i].action.desc + "-" + list[i].action.skillId)
                    }
                }
            } catch (e: Exception) {
                CommonLogUtils.logD("SceneDataEventManager", "SOA action data has exception:${e.message}")
            }
        }
        return sb.toString()
    }

    /**获取能力对应的一级菜单的名称*/
    fun getSkillIdFirstMenuName(scenarioInfo: ScenarioInfo?, type: SceneNewEditType): String {
        if (type == SceneNewEditType.TRIGGER_CONDITION) {
            scenarioInfo?.edgeCondition?.let {
                val skillIds = arrayListOf<Int>(it.skillId)
                return getSkillIdFirstMenuName(skillIds, type)
            }
        } else if (type == SceneNewEditType.STATUS_CONDITION) {
            scenarioInfo?.conditions?.let {
                val skillIds = it.map { item -> item.skillId }
                return getSkillIdFirstMenuName(skillIds, type)
            }
        } else if (type == SceneNewEditType.ACTION) {
            scenarioInfo?.sequence?.let {
                val skillIds = it.map { item -> item.action.skillId }
                return getSkillIdFirstMenuName(skillIds, type)
            }
        }
        return ""

    }

    /**获取能力对应的一级菜单的名称*/
    fun getSkillIdFirstMenuName(skillIds: List<Int>, type: SceneNewEditType): String {
        if (skillIds.isEmpty()) return ""
        var nameList: ArrayList<String>? = null
        val list = when (type) {
            SceneNewEditType.TRIGGER_CONDITION -> {
                nameList = arrayListOf(
                    StringUtils.getString(R.string.scene_text_edit_add_condition_time),
                    StringUtils.getString(R.string.scene_text_edit_add_condition_environment),
                    StringUtils.getString(R.string.scene_text_edit_add_condition_location),
                    StringUtils.getString(R.string.scene_text_edit_add_condition_drive),
                    StringUtils.getString(R.string.scene_text_edit_add_action_car_door_lock),
                    StringUtils.getString(R.string.scene_text_edit_add_action_chair),
                    StringUtils.getString(R.string.scene_text_edit_add_action_car_light),
                    StringUtils.getString(R.string.scene_text_edit_add_charge),
                    StringUtils.getString(R.string.scene_text_edit_add_common_oms),

                    )
                SceneEditManager.mTriggerItemDataList.filter { it.itemIsActive }
            }

            SceneNewEditType.STATUS_CONDITION -> {
                nameList = arrayListOf(
                    StringUtils.getString(R.string.scene_text_edit_add_condition_time),
                    StringUtils.getString(R.string.scene_text_edit_add_condition_environment),
                    StringUtils.getString(R.string.scene_text_edit_add_condition_location),
                    StringUtils.getString(R.string.scene_text_edit_add_action_navigation),
                    StringUtils.getString(R.string.scene_text_edit_add_condition_doors_and_windows),
                    StringUtils.getString(R.string.scene_text_edit_add_action_chair),
                    StringUtils.getString(R.string.scene_text_edit_add_action_car_light),
                    StringUtils.getString(R.string.scene_text_edit_add_common_oms),
                    StringUtils.getString(R.string.scene_text_edit_add_condition_link),
                    StringUtils.getString(R.string.scene_text_action_smart_device),
                    StringUtils.getString(R.string.scene_text_edit_add_action_other),
                )
                SceneEditManager.mStatusItemDataList.filter { it.itemIsActive }
            }

            else -> {
                nameList = arrayListOf(
                    StringUtils.getString(R.string.scene_text_edit_add_action_recommend),
                    StringUtils.getString(R.string.scene_text_app_open_5),
                    StringUtils.getString(R.string.scene_text_edit_add_condition_doors_and_windows),
                    StringUtils.getString(R.string.scene_text_edit_add_action_car_light),
                    StringUtils.getString(R.string.scene_text_edit_add_action_chair),
                    CommonUtils.getString(R.string.scene_text_me_action_fridge),
                    CommonUtils.getString(R.string.scene_text_edit_add_condition_drive),
                    StringUtils.getString(R.string.scene_text_edit_add_action_application),
                    StringUtils.getString(R.string.scene_text_app_open_8),
                    StringUtils.getString(R.string.scene_text_action_smart_device),
                    StringUtils.getString(R.string.scene_text_edit_add_action_other),
                )
                SceneEditManager.mActionItemDataList.filter { it.itemIsActive }
            }
        }
        if (list.isEmpty()) return ""
        val sb = StringBuilder()
        skillIds.forEachIndexed { index, skillId ->
            //kotlin循环退出写法
            run breaking@{
                list.forEachIndexed continuing@{ index1, sceneEditSkillBean ->
                    if (type == SceneNewEditType.ACTION && index1 == 0) {
                        return@continuing
                    }
                    sceneEditSkillBean.skillIdList.forEachIndexed { index2, skillItemBean ->
                        if (skillItemBean.skillId == skillId && skillItemBean.isActive) {

                            when (type) {
                                SceneNewEditType.TRIGGER_CONDITION -> {
                                    sb.append("触发条件类型-" + nameList[index1])
                                }

                                SceneNewEditType.STATUS_CONDITION -> {
                                    if (index != skillIds.size - 1) {
                                        sb.append("状态条件类型-" + nameList[index1] + "\n")
                                    } else {
                                        sb.append("状态条件类型-" + nameList[index1])
                                    }
                                }

                                else -> {
                                    if (index != skillIds.size - 1) {
                                        sb.append("动作类型-" + nameList[index1] + "\n")
                                    } else {
                                        sb.append("动作类型-" + nameList[index1])
                                    }
                                }
                            }
                            return@breaking
                        }
                    }
                }
            }
        }
        return sb.toString()

    }

}