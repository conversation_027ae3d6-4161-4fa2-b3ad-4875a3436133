package com.dfl.smartscene.ui.edit.condition.status.environment

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.commonlib.CommonUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.StatusEnvironment
import com.dfl.smartscene.soa.SceneConditionManager
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :条件->环境 view model
 * version: 1.0
 */
class EnvironmentStatusViewModel : BaseViewModel() {
    val environmentLiveData = MutableLiveData<ArrayList<ConditionItemBean<StatusEnvironment>>>()
    fun initData(resources: Resources, skillId: Int) {
        val list = ArrayList<ConditionItemBean<StatusEnvironment>>()
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_condition_outside_weather),
                R.drawable.scene_icon_me_status_environment_outside_weather,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_STATUS_ENVIRONMENT_CAR_OUTSIDE_WEATHER_RAIN,
                    SkillsListConstant.SKILL_ID_STATUS_ENVIRONMENT_CAR_OUTSIDE_WEATHER_NO_RAIN
                ),
                StatusEnvironment.OUTSIDE_WEATHER
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_add_condition_weather),
                R.drawable.scene_icon_me_status_environment_weather,
                arrayListOf(SkillsListConstant.SKILL_ID_STATUS_ENVIRONMENT_WEATHER),
                StatusEnvironment.WEATHER_FORECAST
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_condition_air_quality),
                R.drawable.scene_icon_me_status_environment_air_quality,
                arrayListOf(SkillsListConstant.SKILL_ID_STATUS_ENVIRONMENT_AIR_QUALITY),
                StatusEnvironment.AIR_QUALITY
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_condition_environment_incar_humidity),
                R.drawable.scene_icon_me_status_environment_humidity_in_car,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_STATUS_ENVIRONMENT_HUMIDITY_IN_CAR_LESS,
                    SkillsListConstant.SKILL_ID_STATUS_ENVIRONMENT_HUMIDITY_IN_CAR_MORE
                ),
                StatusEnvironment.HUMIDITY_IN_CAR
            )
        )

        val type = SceneEditManager.StatusType.ENVIRONMENT
        val result = SceneEditManager.checkChildItemIsActive(type, list)
        SceneConditionManager.checkItemConditionHasAdd(skillId, result)
        environmentLiveData.postValue(result)
    }

    fun getEnvironmentConditionData(type: StatusEnvironment): ArrayList<ScenarioInfo.Condition> {
        val conditionList = ArrayList<ScenarioInfo.Condition>()
        val inputArgList = ArrayList<InputArgInfo>()
        when (type) {
            StatusEnvironment.AIR_QUALITY -> {
                inputArgList.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "0"
                    )
                )
                conditionList.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_STATUS_ENVIRONMENT_AIR_QUALITY,
                        "",
                        inputArgList
                    )
                )
            }
            StatusEnvironment.WEATHER_FORECAST -> {
                inputArgList.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "0"
                    )
                )
                conditionList.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_STATUS_ENVIRONMENT_WEATHER,
                        "",
                        inputArgList
                    )
                )
            }
            StatusEnvironment.OUTSIDE_WEATHER -> {
                inputArgList.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS_ENVIRONMENT_WIPER_GEA_VALUE, ArgType.INT32, "0"
                    )
                )
                conditionList.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_STATUS_ENVIRONMENT_CAR_OUTSIDE_WEATHER_RAIN,
                        "",
                        inputArgList
                    )
                )
                conditionList.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_STATUS_ENVIRONMENT_CAR_OUTSIDE_WEATHER_NO_RAIN,
                        "",
                        inputArgList
                    )
                )
            }
            StatusEnvironment.HUMIDITY_IN_CAR -> {
                inputArgList.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS_ENVIRONMENT_HUMIDITY_VALUE, ArgType.DOUBLE, "0"
                    )
                )
                conditionList.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_STATUS_ENVIRONMENT_HUMIDITY_IN_CAR_MORE,
                        "",
                        inputArgList
                    )
                )
                conditionList.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_STATUS_ENVIRONMENT_HUMIDITY_IN_CAR_LESS,
                        "",
                        inputArgList
                    )
                )
            }
        }
        return conditionList
    }

    /**
     * 0:晴天1:阴天2:雨天3:雪天4:雾天5:雾霾6:沙尘7:其他
     */
    fun initWeatherWheelData(resources: Resources): ArrayList<String> {
        val mWeatherList = ArrayList<String>()
        mWeatherList.add(resources.getString(R.string.scene_text_common_weather_sunny))
        mWeatherList.add(resources.getString(R.string.scene_text_common_weather_cloudy_day))
        mWeatherList.add(resources.getString(R.string.scene_text_common_weather_rain))
        mWeatherList.add(resources.getString(R.string.scene_text_common_weather_snow))
        mWeatherList.add(resources.getString(R.string.scene_text_common_weather_foggy))
        mWeatherList.add(resources.getString(R.string.scene_text_common_weather_haze))
        mWeatherList.add(resources.getString(R.string.scene_text_common_weather_dust))
        mWeatherList.add(resources.getString(R.string.scene_text_edit_add_action_other))
        return mWeatherList
    }

    //enum: 晴天:0x1,阴天 : 0x2,雨天 : 0x3,雪天 : 0x4,雾天 : 0x5,雾霾 : 0x6,沙尘 : 0x7,其他 : 0x8
    //晴天 1/阴天2/⾬天3/雪天4/雾天5/雾霾6/沙尘7/其他8
    fun getInputArgInfoData(type: StatusEnvironment): ArrayList<Int> {
        return when (type) {
            StatusEnvironment.WEATHER_FORECAST -> {
                arrayListOf(1, 2, 3, 4, 5, 6, 7, 8)
            }
            else -> {
                //空气质量
                arrayListOf(0, 1, 2)
            }
        }
    }

    fun getRadioList(resources: Resources, type: StatusEnvironment): List<CheckListBean> {
        return when (type) {
            StatusEnvironment.AIR_QUALITY -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_edit_condition_air_difference), true),
                    CheckListBean(resources.getString(R.string.scene_text_edit_condition_air_nice), false),
                    CheckListBean(resources.getString(R.string.scene_text_edit_condition_air_excellent), false),
                )
            }
            StatusEnvironment.WEATHER_FORECAST -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_common_weather_sunny), true),
                    CheckListBean(resources.getString(R.string.scene_text_common_weather_cloudy_day), false),
                    CheckListBean(resources.getString(R.string.scene_text_common_weather_rain), false),
                    CheckListBean(resources.getString(R.string.scene_text_common_weather_snow), false),
                    CheckListBean(resources.getString(R.string.scene_text_common_weather_foggy), false),
                    CheckListBean(resources.getString(R.string.scene_text_common_weather_haze), false),
                    CheckListBean(resources.getString(R.string.scene_text_common_weather_dust), false),
                    CheckListBean(resources.getString(R.string.scene_text_edit_add_action_other), false),
                )
            }
            else -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_edit_condition_rain), true),
                    CheckListBean(resources.getString(R.string.scene_text_edit_condition_no_rain), false),
                )
            }
        }
    }

    /**
     * 左侧WheelView数据
     */
    fun getLeftWheelMap(): MutableMap<String, String> {
        return mutableMapOf(
            Pair(CommonUtils.getString(R.string.scene_text_common_more_than), "0"),
            Pair(CommonUtils.getString(R.string.scene_text_common_less_than), "1"),
        )
    }

    /**
     * 键wheel的文本值,值 文本对应的InputArg.Value值
     */
    fun getRightWheelMap(start: Int, end: Int, multiple: Int): MutableMap<String, String> {
        val mutableList = mutableMapOf<String, String>()
        for (i in start..end step multiple) {
            mutableList[i.toString()] = i.toString()
        }
        return mutableList
    }
}