package com.dfl.smartscene.ui.edit.condition.trigger.charge

import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import com.chad.library.adapter.base.BaseMultiItemQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.android.common.util.MatchableAdapter
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.TriggerCharger
import me.jessyan.autosize.utils.AutoSizeUtils

/**
 *Created by 钟文祥 on 2025/4/17.
 *Describer: 充放电
 */
class ChargeTriggerAdapter : BaseMultiItemQuickAdapter<ConditionItemBean<TriggerCharger>, BaseViewHolder>(),
                             MatchableAdapter<ConditionItemBean<TriggerCharger>> {

    init {
        addItemType(ConditionItemBean.SKILL_TITLE, R.layout.scene_recycle_item_edit_action_dialog_title)
        addItemType(ConditionItemBean.SKILL_CONTENT, R.layout.scene_recycle_item_edit_condition_dialog_item)
    }

    override fun convert(holder: BaseViewHolder, item: ConditionItemBean<TriggerCharger>) {
        if (item.itemType == ConditionItemBean.SKILL_CONTENT) {
            holder.getView<TextView>(R.id.tv_condition_reflex_name).text = item.conditionName
            holder.getView<AppCompatImageView>(R.id.iv_condition_reflex_icon).setImageResource(item.conditionIcon)
        } else {
            val txtTitle = holder.getView<TextView>(R.id.tv_edit_scene_action_name_item_dialog)
            val params = txtTitle.layoutParams as ViewGroup.MarginLayoutParams
            txtTitle.text = item.conditionName
            if (holder.layoutPosition == 0) {
                params.topMargin = AutoSizeUtils.dp2px(context, 16.0f)
            } else {
                params.topMargin = AutoSizeUtils.dp2px(context, 44.0f)
            }
            txtTitle.layoutParams = params
        }
    }

    override val adapterData: List<ConditionItemBean<TriggerCharger>>
        get() = data

    override fun findPositionByTitle(title: String): Int {
        return data.indexOfFirst { it.conditionName == title && !it.conditionSkillId.isNullOrEmpty() }
    }
}