package com.dfl.smartscene.ui.edit.adapter

import android.view.View
import android.widget.ImageView
import androidx.appcompat.widget.AppCompatImageView
import com.bumptech.glide.Glide
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.edit.SkillInfoBean

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :编辑页面上方图标映射列表适配器
 * version: 1.0
 */
class SceneEditSkillIconAdapter : BaseQuickAdapter<SkillInfoBean, BaseViewHolder>(
    R.layout.scene_recycle_item_edit_skill_icon
) {

    override fun convert(holder: BaseViewHolder, item: SkillInfoBean) {
        val ivSkill = holder.getView<AppCompatImageView>(R.id.iv_edit_skill_icon_item)
        val tvEllipsis = holder.getView<ImageView>(R.id.tv_ellipsis_item)
        item.let {
            ivSkill.visibility = View.VISIBLE
            tvEllipsis.visibility = View.GONE
            holder.itemView.visibility = View.VISIBLE
            when (item.iconType) {
                SkillInfoBean.SkillIconType.TRIGGER, SkillInfoBean.SkillIconType.STATUS -> {
                    ivSkill.setBackgroundResource(R.drawable.scene_shape_icon_bg_fang_r16)
                    ivSkill.setImageResource(it.imgId)
                    //                    ivSkill.setBackgroundResource(R.drawable.scene_shape_condition_icon_bg)
                    //                    Glide.with(context).load(it.imgId).apply(mImgOptions).into(ivSkill)
                }
                SkillInfoBean.SkillIconType.ACTION -> {
                    ivSkill.setBackgroundResource(R.drawable.scene_shape_icon_bg_yuan_r16)
                    ivSkill.setImageResource(it.imgId)
                    //                    ivSkill.setBackgroundResource(R.drawable.scene_shape_action_icon_bg)
                    //                    Glide.with(context).load(it.imgId).apply(mImgOptions).into(ivSkill)
                }
                SkillInfoBean.SkillIconType.ARROW -> {
                    ivSkill.background = null
                    ivSkill.setImageResource(it.imgId)
                    //Glide.with(context).load(it.imgId).apply(mImgOptions).into(ivSkill)
                }
                else -> {
                    ivSkill.visibility = View.GONE
                    tvEllipsis.visibility = View.VISIBLE
                }
            }
        }
    }

    override fun onViewRecycled(holder: BaseViewHolder) {
        context.let {
            val ivSkill = holder.getView<AppCompatImageView>(R.id.iv_edit_skill_icon_item)
            ivSkill.let {
                Glide.with(context).clear(it)
            }
        }
        super.onViewRecycled(holder)
    }

}