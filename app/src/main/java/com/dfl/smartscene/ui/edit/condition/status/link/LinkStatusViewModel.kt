package com.dfl.smartscene.ui.edit.condition.status.link

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.StatusLink
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.smartscene.soa.SceneConditionManager
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :条件->连接view model
 * version: 1.0
 */
class LinkStatusViewModel : BaseViewModel() {
    val linkConditionLiveData = MutableLiveData<ArrayList<ConditionItemBean<StatusLink>>>()

    /**
     * 能力ID的列表
     */
    private val linkConditionIdList = ArrayList<ArrayList<Int>>()
    private val tag = GlobalConstant.GLOBAL_TAG.plus("LinkViewModel")

    /**
     * 初始化连接状态条件的数据
     */
    fun initData(resources: Resources, skillId: Int) {
        val conditionList = ArrayList<ConditionItemBean<StatusLink>>()

        linkConditionIdList.add(
            arrayListOf(
                SkillsListConstant.SKILL_ID_STATUS_LINK_BLUETOOTH_SWITCH,
                SkillsListConstant.SKILL_ID_STATUS_LINK_BLUETOOTH_CONNECTION
            )
        )
        linkConditionIdList.add(
            arrayListOf(
                SkillsListConstant.SKILL_ID_STATUS_LINK_HOTSPOT_SWITCH,
                SkillsListConstant.SKILL_ID_STATUS_LINK_HOTSPOT_CONNECTION
            )
        )
        linkConditionIdList.add(
            arrayListOf(
                SkillsListConstant.SKILL_ID_STATUS_LINK_WLAN_SWITCH,
                SkillsListConstant.SKILL_ID_STATUS_LINK_WLAN_CONNECTION
            )
        )
        linkConditionIdList.add(
            arrayListOf(
                SkillsListConstant.SKILL_ID_STATUS_LINK_MOBILE_SWITCH,
                //				SkillsListConstant.SKILL_ID_LINK_MOBILE_CONNECTION
            )
        )

        conditionList.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_condition_bluetooth),
                R.drawable.scene_icon_me_status_link_bluetooth, linkConditionIdList[0],
                StatusLink.BLUETOOTH
            )
        )
        conditionList.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_condition_hotspot),
                R.drawable.scene_icon_me_status_link_hot_point, linkConditionIdList[1], StatusLink.HOTSPOT
            )
        )
        conditionList.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_condition_wlan),
                R.drawable.scene_icon_me_status_link_wifi, linkConditionIdList[2], StatusLink.WLAN
            )
        )
        conditionList.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_condition_mobile_network),
                R.drawable.scene_icon_me_status_link_4g, linkConditionIdList[3], StatusLink.MOBILE
            )
        )

        val type = SceneEditManager.StatusType.LINK
        val result = SceneEditManager.checkChildItemIsActive(type, conditionList)
        SceneConditionManager.checkItemConditionHasAdd(skillId, result)
        linkConditionLiveData.postValue(result)
    }

    fun getLinkStatusConditionList(linkConditionType: StatusLink): ArrayList<ScenarioInfo.Condition> {
        val linkConditionList = ArrayList<ScenarioInfo.Condition>()
        when (linkConditionType) {
            StatusLink.BLUETOOTH -> {
                //蓝牙打开的状态条件
                linkConditionList.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_STATUS_LINK_BLUETOOTH_SWITCH,
                        "",
                        listOf(
                            InputArgInfo(
                                SkillsListConstant.INPUT_ARG_ACTION_SYSTEM_SWITCH_STATUS,
                                ArgType.INT32,
                                "1"
                            )
                        )
                    )
                )
                //蓝牙关闭的状态条件
                linkConditionList.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_STATUS_LINK_BLUETOOTH_SWITCH,
                        "",
                        listOf(
                            InputArgInfo(
                                SkillsListConstant.INPUT_ARG_ACTION_SYSTEM_SWITCH_STATUS,
                                ArgType.INT32,
                                "0"
                            )
                        )
                    )
                )
                //蓝牙已连接的状态条件
                linkConditionList.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_STATUS_LINK_BLUETOOTH_CONNECTION,
                        "",
                        listOf(
                            InputArgInfo(
                                SkillsListConstant.INPUT_ARG_STATUS_LINK_CONNECTION_STATUS,
                                ArgType.INT32,
                                "1"
                            )
                        )
                    )
                )
                //蓝牙未连接的状态条件
                linkConditionList.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_STATUS_LINK_BLUETOOTH_CONNECTION,
                        "",
                        listOf(
                            InputArgInfo(
                                SkillsListConstant.INPUT_ARG_STATUS_LINK_CONNECTION_STATUS,
                                ArgType.INT32,
                                "0"
                            )
                        )
                    )
                )
            }

            StatusLink.HOTSPOT -> {
                //热点打开的状态条件
                linkConditionList.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_STATUS_LINK_HOTSPOT_SWITCH,
                        "",
                        listOf(
                            InputArgInfo(
                                SkillsListConstant.INPUT_ARG_ACTION_SYSTEM_SWITCH_STATUS,
                                ArgType.INT32,
                                "1"
                            )
                        )
                    )
                )
                //热点关闭的状态条件
                linkConditionList.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_STATUS_LINK_HOTSPOT_SWITCH,
                        "",
                        listOf(
                            InputArgInfo(
                                SkillsListConstant.INPUT_ARG_ACTION_SYSTEM_SWITCH_STATUS,
                                ArgType.INT32,
                                "0"
                            )
                        )
                    )
                )
                //热点已连接的状态条件
                linkConditionList.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_STATUS_LINK_HOTSPOT_CONNECTION,
                        "",
                        listOf(
                            InputArgInfo(
                                SkillsListConstant.INPUT_ARG_STATUS_LINK_CONNECTION_STATUS,
                                ArgType.INT32,
                                "1"
                            )
                        )
                    )
                )
                //热点未连接的状态条件
                linkConditionList.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_STATUS_LINK_HOTSPOT_CONNECTION,
                        "",
                        listOf(
                            InputArgInfo(
                                SkillsListConstant.INPUT_ARG_STATUS_LINK_CONNECTION_STATUS,
                                ArgType.INT32,
                                "0"
                            )
                        )
                    )
                )
            }
            StatusLink.WLAN -> {
                //WLAN打开的状态条件
                linkConditionList.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_STATUS_LINK_WLAN_SWITCH,
                        "",
                        listOf(
                            InputArgInfo(
                                SkillsListConstant.INPUT_ARG_ACTION_SYSTEM_SWITCH_STATUS,
                                ArgType.INT32,
                                "1"
                            )
                        )
                    )
                )
                //WLAN关闭的状态条件
                linkConditionList.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_STATUS_LINK_WLAN_SWITCH,
                        "",
                        listOf(
                            InputArgInfo(
                                SkillsListConstant.INPUT_ARG_ACTION_SYSTEM_SWITCH_STATUS,
                                ArgType.INT32,
                                "0"
                            )
                        )
                    )
                )
                //WLAN已连接的状态条件
                linkConditionList.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_STATUS_LINK_WLAN_CONNECTION,
                        "",
                        listOf(
                            InputArgInfo(
                                SkillsListConstant.INPUT_ARG_STATUS_LINK_CONNECTION_STATUS,
                                ArgType.INT32,
                                "1"
                            )
                        )
                    )
                )
                linkConditionList.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_STATUS_LINK_WLAN_CONNECTION,
                        "",
                        listOf(
                            InputArgInfo(
                                SkillsListConstant.INPUT_ARG_STATUS_LINK_CONNECTION_STATUS,
                                ArgType.INT32,
                                "0"
                            )
                        )
                    )
                )
            }

            StatusLink.MOBILE -> {
                linkConditionList.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_STATUS_LINK_MOBILE_SWITCH,
                        "",
                        listOf(
                            InputArgInfo(
                                SkillsListConstant.INPUT_ARG_ACTION_SYSTEM_SWITCH_STATUS,
                                ArgType.INT32,
                                "1"
                            )
                        )
                    )
                )
            }
        }
        return linkConditionList
    }

    fun getInputArgInfoValueData(type: StatusLink): ArrayList<Int> {
        return arrayListOf(1, 0)
    }

    fun getListData(resources: Resources, type: StatusLink): List<CheckListBean> {
        return when (type) {
            StatusLink.MOBILE -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_common_open), true),
                    CheckListBean(resources.getString(R.string.scene_text_common_close), false),
                )
            }
            else -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_common_open), true),
                    CheckListBean(resources.getString(R.string.scene_text_common_close), false),
                    CheckListBean(resources.getString(R.string.scene_text_common_connected), false),
                    CheckListBean(resources.getString(R.string.scene_text_common_disconnect), false),
                )
            }
        }
    }
}