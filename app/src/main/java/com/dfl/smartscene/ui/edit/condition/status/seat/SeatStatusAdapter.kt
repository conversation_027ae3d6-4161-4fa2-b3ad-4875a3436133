package com.dfl.smartscene.ui.edit.condition.status.seat

import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.android.common.util.MatchableAdapter
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.StatusSeat

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/15
 * desc :条件->座椅适配器
 * version: 1.0
 */
class SeatStatusAdapter : BaseQuickAdapter<ConditionItemBean<StatusSeat>, BaseViewHolder>(
    R.layout.scene_recycle_item_edit_condition_dialog_item
), MatchableAdapter<ConditionItemBean<StatusSeat>> {
    override fun convert(holder: BaseViewHolder, item: ConditionItemBean<StatusSeat>) {
        holder.getView<TextView>(R.id.tv_condition_reflex_name).text = item.conditionName

        holder.getView<AppCompatImageView>(R.id.iv_condition_reflex_icon).setImageResource(item.conditionIcon)
        if (item.isEnable == false) {
            holder.itemView.alpha = 0.5f
        } else {
            holder.itemView.alpha = 1f
        }
    }

    override val adapterData: List<ConditionItemBean<StatusSeat>>
        get() = data

    override fun findPositionByTitle(title: String): Int {
        return data.indexOfFirst { it.conditionName == title && !it.conditionSkillId.isNullOrEmpty() }
    }
}