package com.dfl.smartscene.ui.main.me.scenerecord

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Configuration
import android.view.View
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.dfl.android.common.base.MVVMDialogFragment
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.BR
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.main.SceneRecordBean
import com.dfl.smartscene.customapi.SoundManager
import com.dfl.smartscene.databinding.SceneDialogFragmentMeSceneRecordBinding
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.smartscene.ui.overlay.common.DefaultDialogFragment
import com.dfl.smartscene.util.TrackUtils
import com.dfl.smartscene.util.UIUtils
import com.jeremyliao.liveeventbus.LiveEventBus
import java.text.SimpleDateFormat
import java.util.*

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/23
 * desc   : 执行日志记录
 * version: 1.0
 */
class SceneRecordDialogFragment : MVVMDialogFragment<SceneDialogFragmentMeSceneRecordBinding, SceneRecordViewModel>(),
                                  View.OnClickListener {

    private var adapter: SceneRecordItemAdapter? = null
    private var page = 1

    companion object {
        private const val TAG = GlobalConstant.GLOBAL_TAG.plus("SceneRecordDialogFragment")
        fun showSceneRecordDialog(fragmentManager: FragmentManager) {
            val mRecordDialog = SceneRecordDialogFragment()
            mRecordDialog.showNow(fragmentManager, "FRAGMENT_TAG_SCENE_RECORD_DIALOG")
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.scene_dialog_fragment_me_scene_record
    }

    override fun getBindingVariable(): Int {
        return BR.vm
    }

    override fun getViewModelClass(): Class<SceneRecordViewModel> {
        return SceneRecordViewModel::class.java
    }

    override fun initData() {
        super.initData()
        mViewModel?.initData(page)
    }

    override fun initView(view: View) {
        super.initView(view)
        setCanceledOnTouchOutside(true)
        initRecycleView(view.context)
        initListener()
        TrackUtils.viewStart(TrackUtils.PageNameType.MyLog)
        mViewDataBinding.nsvRecord.isVerticalScrollBarEnabled = true
    }

    private fun initRecycleView(context: Context) {
        adapter = SceneRecordItemAdapter()
        mViewDataBinding.rvRecord.adapter = adapter
        mViewDataBinding.rvRecord.layoutManager = LinearLayoutManager(context)
        //        val itemDecoration=SceneRecordItemDecoration(context, R.color.recycle_divider_line)
        //        itemDecoration.setShowBottomLine(true)
        //        mViewDataBinding?.rvRecord?.addItemDecoration(itemDecoration)
    }

    /**
     * 初始化监听器
     */
    @SuppressLint("NotifyDataSetChanged")
    private fun initListener() {
        // 将原来底部按钮的事件移动到顶部按钮
        mViewDataBinding.btnRecordClean.setOnClickListener(this)
        mViewDataBinding.ivClose.setOnClickListener(this)

        mViewDataBinding.slRecord.setEnableRefresh(false)
        mViewDataBinding.slRecord.setOnLoadMoreListener {
            page++
            mViewModel?.initData(page)
        }
        LiveEventBus.get<ArrayList<SceneRecordBean>>(GlobalLiveEventConstants.KEY_SCENE_RUN_RECORD)
            .observe(viewLifecycleOwner) { recordList ->
                run {
                    CommonLogUtils.logD(TAG, "page:${page}触发数据加载回调${recordList.size}")
                    if (page == 1) {
                        if (recordList.isEmpty()) {
                            mViewDataBinding.tvEmptyMsg.visibility = View.VISIBLE
                            mViewDataBinding.tvRecordTips.visibility = View.GONE
                            mViewDataBinding.slRecord.setEnableLoadMore(false)
                            mViewDataBinding.rvRecord.visibility = View.GONE
                        } else {
                            mViewDataBinding.tvEmptyMsg.visibility = View.GONE
                        }
                        // 控制顶部清理按钮的启用状态
                        mViewDataBinding.btnRecordClean.isEnabled = recordList.isNotEmpty()
                        if (recordList.size != 0) {
                            // 处理数据，添加日期头部
                            val processedData = processDataWithDateHeaders(recordList)
                            adapter?.setNewInstance(processedData as MutableList<SceneRecordBean>?)
                        }
                    } else {
                        if (recordList.size != 0) {
                            val list = adapter?.data?.toMutableList() ?: mutableListOf()
                            // 处理新数据，添加日期头部
                            val processedNewData = processDataWithDateHeaders(recordList)
                            list.addAll(processedNewData)
                            adapter?.setList(list)
                        }
                    }
                    if ((recordList.size in 0..9 && page > 1) || (page == 1 && recordList.size in 1..9)) {
                        mViewDataBinding.tvRecordTips.visibility = View.VISIBLE
                        mViewDataBinding.slRecord.finishLoadMoreWithNoMoreData()
                        mViewDataBinding.slRecord.setEnableLoadMore(false)
                    } else if (recordList.size == 10 && page > 1) {
                        mViewDataBinding.slRecord.finishLoadMore()
                    }
                }
            }
}

/**
 * 处理数据，添加日期头部，并将记录项按两个一组进行分组
 */
private fun processDataWithDateHeaders(recordList: ArrayList<SceneRecordBean>): List<SceneRecordBean> {
    val result = mutableListOf<SceneRecordBean>()
    val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    var currentDate = ""
    
    // 按时间排序（最新的在前面）
    val sortedList = recordList.sortedByDescending { it.record.finishSceneTime }
    
    // 按日期分组
    val groupedByDate = sortedList.groupBy { record ->
        dateFormat.format(Date(record.record.finishSceneTime))
    }
    
    // 处理每个日期组
    for ((date, records) in groupedByDate) {
        // 添加日期头部
        result.add(SceneRecordBean.createDateItem(date))
        
        // 将记录按两个一组进行分组
        val pairedRecords = records.chunked(2)
        
        for (pair in pairedRecords) {
            if (pair.size == 2) {
                // 创建包含两个记录的item
                result.add(SceneRecordBean.createPairedRecordItem(pair[0], pair[1]))
            } else {
                // 只有一个记录的情况，第二个位置为空
                result.add(SceneRecordBean.createPairedRecordItem(pair[0], null))
            }
        }
    }
    
    return result
}

    /**
     * 点击事件处理
     */
    override fun onClick(v: View?) {
        v ?: return
        when (v.id) {
            R.id.btn_record_clean -> {  // 原来的btn_primary逻辑
                if (DebouncingUtils.isValid(v)) {
                    DefaultDialogFragment.showDialog(
                        parentFragmentManager,
                        "",
                        "",
                        getString(R.string.scene_text_record_delete_all_record),
                        getString(R.string.scene_button_common_clean_up),
                        getString(R.string.scene_text_common_cancel),
                        object : DefaultDialogFragment.OnDialogButtonClickListener {
                            override fun onRightButtonClick(dialog: DefaultDialogFragment) {
                                dialog.dismissWithAnimate()
                            }

                            override fun onLeftButtonClick(dialog: DefaultDialogFragment) {
                                page = 1
                                mViewModel?.cleanUpSceneRecordData()
                                //mViewDataBinding.includeDialogBottom.btnPrimary.alpha = 0.5f
                                //mViewDataBinding.includeDialogBottom.btnPrimary.isEnabled = false
                                SoundManager.playSoundEffect(SoundManager.SoundType.DELETE)
                                dialog.dismissWithAnimate()
                            }
                        }
                    )
                }
            }
            R.id.iv_close -> {  // 原来的btn_normal逻辑
                dismissWithAnimate()
            }
        }
    }

    override fun dismissWithAnimate() {
        super.dismissWithAnimate()
        page = 1
        TrackUtils.viewEnd(TrackUtils.PageNameType.MyLog)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        UIUtils.setSmartRefreshOnConfigurationChanged(null, mViewDataBinding.classicsFooter)
    }

}