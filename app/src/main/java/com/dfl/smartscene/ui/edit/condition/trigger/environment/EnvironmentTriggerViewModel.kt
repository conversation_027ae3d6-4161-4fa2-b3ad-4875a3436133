package com.dfl.smartscene.ui.edit.condition.trigger.environment

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.commonlib.CommonUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.TriggerEnvironment
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :条件->环境 view model
 * version: 1.0
 */
class EnvironmentTriggerViewModel : BaseViewModel() {
    val environmentLiveData = MutableLiveData<ArrayList<ConditionItemBean<TriggerEnvironment>>>()

    fun initData(resources: Resources) {
        val list = ArrayList<ConditionItemBean<TriggerEnvironment>>()
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_condition_temperature_interior),
                R.drawable.scene_icon_me_trigger_environment_interior_temperature,
                arrayListOf(
                    SkillsListConstant.SKILLS_ID_TRIGGER_ENVIRONMENT_INNER_TEMPERATURE_HIGHER,
                    SkillsListConstant.SKILLS_ID_TRIGGER_ENVIRONMENT_INNER_TEMPERATURE_LOW
                ),
                TriggerEnvironment.CAR_INNER_TEMP
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_condition_temperature_outside),
                R.drawable.scene_icon_me_trigger_environment_outside_temperature,
                arrayListOf(
                    SkillsListConstant.SKILLS_ID_TRIGGER_ENVIRONMENT_OUTSIDE_TEMPERATURE_HIGHER,
                    SkillsListConstant.SKILLS_ID_TRIGGER_ENVIRONMENT_OUTSIDE_TEMPERATURE_LOW
                ),
                TriggerEnvironment.CAR_OUTSIDE_TEMP
            )
        )
        val type = SceneEditManager.TriggerType.ENVIRONMENT
        val result = SceneEditManager.checkChildItemIsActive(type, list)
        environmentLiveData.postValue(result)
    }

    /**
     * 获取车内外温度对应的ScenarioInfo.Condition格式
     * @param isInner
     * @param resources
     * @return 触发条件ScenarioInfo.Condition
     */
    fun getTemperature(
        isInner: Boolean, resources: Resources
    ): ArrayList<ScenarioInfo.Condition> {
        val conditionList = ArrayList<ScenarioInfo.Condition>()
        val inputArgList = ArrayList<InputArgInfo>()

        inputArgList.add(
            InputArgInfo(
                if (isInner) SkillsListConstant.INPUT_ARG_TRIGGER_ENVIRONMENT_INSIDE_TEMP
                else SkillsListConstant.INPUT_ARG_TRIGGER_ENVIRONMENT_OUTSIDE_NAME, ArgType.DOUBLE, "0"
            )
        )

        if (isInner) {
            conditionList.add(
                ScenarioInfo.Condition(
                    1,
                    "",
                    SkillsListConstant.SKILLS_ID_TRIGGER_ENVIRONMENT_INNER_TEMPERATURE_HIGHER,
                    resources.getString(R.string.scene_text_edit_add_condition_environment),
                    inputArgList
                )
            )
            conditionList.add(
                ScenarioInfo.Condition(
                    1,
                    "",
                    SkillsListConstant.SKILLS_ID_TRIGGER_ENVIRONMENT_INNER_TEMPERATURE_LOW,
                    resources.getString(R.string.scene_text_edit_add_condition_environment),
                    inputArgList
                )
            )
        } else {
            conditionList.add(
                ScenarioInfo.Condition(
                    1,
                    "",
                    SkillsListConstant.SKILLS_ID_TRIGGER_ENVIRONMENT_OUTSIDE_TEMPERATURE_HIGHER,
                    resources.getString(R.string.scene_text_edit_add_condition_environment),
                    inputArgList
                )
            )
            conditionList.add(
                ScenarioInfo.Condition(
                    1,
                    "",
                    SkillsListConstant.SKILLS_ID_TRIGGER_ENVIRONMENT_OUTSIDE_TEMPERATURE_LOW,
                    resources.getString(R.string.scene_text_edit_add_condition_environment),
                    inputArgList
                )
            )
        }
        return conditionList
    }

    fun getRbMap(): MutableMap<String, String> {
        return mutableMapOf(
            Pair(CommonUtils.getString(R.string.scene_text_common_more_than), "0"),
            Pair(CommonUtils.getString(R.string.scene_text_common_less_than), "1"),
        )
    }

    fun getWheelMap(): MutableMap<String, String> {
        val mutableList = mutableMapOf<String, String>()
        for (i in -40..80) {
            mutableList[i.toString()] = i.toString()
        }
        return mutableList
    }
}