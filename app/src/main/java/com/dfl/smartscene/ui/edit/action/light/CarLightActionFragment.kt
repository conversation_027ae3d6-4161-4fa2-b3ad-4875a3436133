package com.dfl.smartscene.ui.edit.action.light

import android.annotation.SuppressLint
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.ActionSkillItemBean
import com.dfl.smartscene.bean.action.CarLightType
import com.dfl.smartscene.bean.edit.CustomSeekBarBean
import com.dfl.smartscene.databinding.SceneFragmentEditActionBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.ambientlight.ReadLampDialogFragment
import com.dfl.smartscene.ui.overlay.ambientlight.SceneLightEffectDialog
import com.dfl.smartscene.ui.overlay.common.checklist.CheckListType
import com.dfl.smartscene.ui.overlay.common.checklist.RadioListDialogFragment
import com.dfl.smartscene.ui.overlay.common.doublewheel.DoubleWheelDialogFragment
import com.dfl.smartscene.ui.overlay.common.doublewheel.DoubleWheelType
import com.dfl.smartscene.ui.overlay.common.singlewheel.SingleWheelDialog
import com.dfl.smartscene.ui.overlay.common.singlewheel.SingleWheelType
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :动作->车灯页面
 * version: 1.0
 */
class CarLightActionFragment(private val sequence: ScenarioInfo.Sequence?) :
    MVVMAdapterFragment<CarLightActionAdapter, SceneFragmentEditActionBinding, CarLightActionViewModel>() {

    private var mAdapter: CarLightActionAdapter? = null

    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_action
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    override fun getViewModelClass(): Class<CarLightActionViewModel> {
        return CarLightActionViewModel::class.java
    }

    override fun initView(view: View?) {
        super.initView(view)
        mAdapter = CarLightActionAdapter()

        mAdapter?.setOnItemClickListener { _, v, position ->
            handleItemClick(v, position)
        }
        val manager = GridLayoutManager(context, 4)
        manager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                if (mAdapter?.data?.get(position)?.itemType == ActionSkillItemBean.SKILL_TITLE) return 4
                return 1
            }
        }
        mViewDataBinding.rvAction.layoutManager = manager
        mViewDataBinding.rvAction.adapter = mAdapter
    }

    private fun handleItemClick(view: View, position: Int) {
        if (mAdapter?.data?.get(position)?.itemType == ActionSkillItemBean.SKILL_TITLE) return
        if (DebouncingUtils.isValid(view, SceneEditManager.mViewClickDuration)) {
            mAdapter?.data?.get(position)?.let {
                if (it.actionType == null) return@let
                val content = it.content.replace("\n", "")
                when (it.actionType) {
                    //CarLightType.CAR_HEAD_LIGHT_LAMP_LANGUAGE -> { //大灯打招呼灯语
                    //    showLightLampLanguageDialog(content, it.actionType)
                    //}

                    CarLightType.CAR_AMBIENT_LAMP_LIGHT -> { //氛围灯亮度
                        showAtmosphereLampBrightnessDialog(it.actionType)
                    }
                    CarLightType.CAR_OUTSIDE_LAMP_LIGHT_SOA, //大灯灯光秀
                    CarLightType.CAR_AMBIENT_LAMP_AMBIENT_SOA, //氛围灯SOA灯语
                    CarLightType.CAR_SCENE_LAMP_EFFECT, //氛围灯功能联动
                    CarLightType.CAR_OUTSIDE_LAMP_SCENE_EFFECT -> //大灯场景灯语
                    {
                        val dialog = DoubleWheelDialogFragment(
                            content,
                            mViewModel.getRbMap(resources, it.actionType),
                            mViewModel.getWheelMap(resources, it.actionType),
                            mViewModel.getActionData(it.actionType),
                            DoubleWheelType.SINGLE_SKILL_LEFT_1
                        )
                        if (it.actionType == CarLightType.CAR_OUTSIDE_LAMP_LIGHT_SOA) {
                            dialog.titleDesc = getString(R.string.scene_text_desc_light_limit)
                            dialog.setWheelDefaultPosition(0, 1)
                        }
                        if (it.actionType == CarLightType.CAR_AMBIENT_LAMP_AMBIENT_SOA) {
                            dialog.setWheelDefaultPosition(0, 1)
                        }
                        dialog.setupPreAction(sequence)
                        dialog.show(parentFragmentManager, "showCarLightDialog")
                    }
                    CarLightType.CAR_EXTERIOR_LIGHT_MUSIC_EFFECT -> //大灯音乐律动
                    {
                        val dialog = DoubleWheelDialogFragment(
                            content,
                            mViewModel.getRbMap(resources, it.actionType),
                            mViewModel.getWheelMap(resources, it.actionType),
                            mViewModel.getActionData(it.actionType),
                            DoubleWheelType.SINGLE_SKILL
                        )
                        dialog.setupPreAction(sequence)
                        dialog.show(parentFragmentManager, "showCarLightDialog")
                    }
                    else -> {
                        showRadioListDialog(content, it.actionType)
                    }
                }
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        super.initData()
        mViewModel.initData(resources)

        mViewModel.mCarLightLiveData.observe(viewLifecycleOwner) {
            mAdapter?.data = it
            mAdapter?.notifyDataSetChanged()
            val position = getSimulateClickPosition(null, sequence)
            if (position != -1) {
                view?.let { it1 -> handleItemClick(it1, position) }
            }
        }
    }

    override fun getAdapter(): CarLightActionAdapter? {
        return mAdapter
    }

    //室内灯 阅读灯
    private fun showReadLampDialog(title: String, type: CarLightType) {
        //一个sequence根据inputArg选项
        val dialog = ReadLampDialogFragment(
            title,
            CheckListType.INPUT_CHECK_MODE,
            mViewModel.getListData(resources, type), //list文本显示
            mViewModel.getActionData(type), //list对应的动作
            CustomSeekBarBean(1, 100, 1, 50, pre = "", unit = "%", icon = -1),
            CustomSeekBarBean(1, 100, 1, 50, pre = "", unit = "%", icon = -1),
        )
        dialog.inputArgValueList = mViewModel.getCheckInputArgValueData(type) //文本对应的值
        dialog.preSetSequence = sequence //上一个动作
        dialog.show(parentFragmentManager, "showReadLampDialog")
    }

    private fun showRadioListDialog(title: String, type: CarLightType) {
        //一个sequence根据inputArg选项
        val dialog: RadioListDialogFragment<ScenarioInfo.Sequence, Int> = RadioListDialogFragment(
            title,
            CheckListType.INPUT_CHECK_MODE,
            mViewModel.getListData(resources, type),
            mViewModel.getActionData(type),
        )
        if (type == CarLightType.CAR_HEAD_LIGHT_LAMP_LANGUAGE) {
            dialog.desc = getString(R.string.scene_text_desc_light_limit)
        }
        dialog.inputArgValueList = mViewModel.getCheckInputArgValueData(type)
        dialog.preSetSequence = sequence
        dialog.show(parentFragmentManager, "showRadioListDialog")
    }

    @Deprecated("623 氛围场景灯效,lk1a不使用")
    private fun showSceneLightEffectDialog(title: String, position: CarLightType) {
        val dialog = SceneLightEffectDialog(context, title, mViewModel.getActionData(position))
        dialog.setupPreAction(sequence)
        dialog.showWithAnimate()
    }

    private fun showAtmosphereLampBrightnessDialog(type: CarLightType) {
        val dialog = SingleWheelDialog<ScenarioInfo.Sequence, Int>(
            context,
            mViewModel.initAtmosphereLampBrightnessWheelData(),
            resources.getString(R.string.scene_text_action_ambient_light_brightness),
            mViewModel.getActionData(type),
            SingleWheelType.SINGLE_WHEEL_INPUT_ARG
        )
        dialog.setUnit(getString(R.string.scene_text_indicator_unit_car_light))
        dialog.setupPreAction(sequence)
        dialog.setWheelDefaultPosition(0)
        dialog.setInputArgValueList(mViewModel.getAtmosphereLampBrightnessArgInfoList())
        dialog.showWithAnimate()
    }
}