package com.dfl.smartscene.ui.overlay.smart

import android.content.Context
import android.os.Bundle
import androidx.appcompat.widget.AppCompatImageView
import androidx.constraintlayout.widget.ConstraintLayout
import com.dfl.android.common.base.BaseDialog
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.SmartDeviceType

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2023/01/13
 * desc :购买智能设备弹窗 for 动作
 * version: 1.0
 */
class BuySmartDeviceDialog(context: Context, private val deviceType: SmartDeviceType) : BaseDialog(context) {

    private var mIvClose: AppCompatImageView? = null
    private var mCslContainer: ConstraintLayout? = null
    private var mIvBuyQrCode: AppCompatImageView? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.scene_dialog_buy_smart_device)

        initView()
    }

    private fun initView() {
        mIvClose = findViewById(R.id.iv_close_buy)
        mCslContainer = findViewById(R.id.csl_buy_smart_device_container)
        mCslContainer?.setOnClickListener {
            dismissWithAnimate()
        }
        mIvClose?.setOnClickListener {
            dismissWithAnimate()
        }
        mIvBuyQrCode = findViewById(R.id.iv_qrcode_buy)
        val resId = when (deviceType) {
            SmartDeviceType.SMART_FRAGRANCE_TYPE -> {
                R.drawable.scene_img_aiot_fragrance_code
            }
            SmartDeviceType.SMART_AIR_TYPE -> {
                R.drawable.scene_img_aiot_air_code
            }
            SmartDeviceType.SMART_CHILD_SEAT_TYPE -> {
                R.drawable.scene_img_aiot_child_seat_code
            }
            SmartDeviceType.SMART_MASSAGE_NECK_PILLOW_TYPE -> {
                R.drawable.scene_img_aiot_massage_neck_pillow_code
            }
            SmartDeviceType.SMART_MASSAGE_CUSHION_TYPE -> {
                R.drawable.scene_img_aiot_massage_cushion_code
            }
            else -> {
                R.drawable.scene_img_aiot_massage_cushion_code
            }
        }
        mIvBuyQrCode?.setImageResource(resId)
    }
}