package com.dfl.smartscene.ui.edit.condition.status.navigation

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.StatusNavigation
import com.dfl.smartscene.soa.SceneConditionManager
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo

/**
 *Created by 钟文祥 on 2023/12/12.
 *Describer:
 */
class NavigationStatusViewModel : BaseViewModel() {

    val liveData = MutableLiveData<ArrayList<ConditionItemBean<StatusNavigation>>>()


    fun initData(resources: Resources, skillId: Int) {
        val list = ArrayList<ConditionItemBean<StatusNavigation>>()

        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_condition_navigation_expected_arrival_time),
                R.drawable.scene_icon_me_status_navigation_expected_arrival_distance_time,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_STATUS_NAVIGATION_EXPECTED_ARRIVAL_TIME
                ),
                StatusNavigation.NAVIGATION_EXPECTED_ARRIVAL_TIME
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_condition_navigation_expected_arrival_distance),
                R.drawable.scene_icon_me_status_navigation_expected_arrival_distance_m,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_STATUS_NAVIGATION_EXPECTED_ARRIVAL_DISTANCE
                ),
                StatusNavigation.NAVIGATION_EXPECTED_ARRIVAL_DISTANCE
            )
        )

        val type = SceneEditManager.StatusType.NAVIGATION
        val result = SceneEditManager.checkChildItemIsActive(type, list)
        SceneConditionManager.checkItemConditionHasAdd(skillId, result)
        liveData.postValue(result)

    }


    fun getDriveConditionData(
        position: StatusNavigation
    ): ArrayList<ScenarioInfo.Condition> {
        val list = ArrayList<ScenarioInfo.Condition>()
        val args = ArrayList<InputArgInfo>()
        when (position) {
            StatusNavigation.NAVIGATION_EXPECTED_ARRIVAL_DISTANCE -> { //预计到达距离
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_TRIGGER_LOCATION_DISTANCE, ArgType.DOUBLE, "0"
                    )
                )
                list.add(
                    ScenarioInfo.Condition(
                        1, "", SkillsListConstant.SKILL_ID_STATUS_NAVIGATION_EXPECTED_ARRIVAL_DISTANCE, "", args
                    )
                )
            }
            else -> {}
        }


        return list
    }

    /**
     * 键wheel的文本值,值 文本对应的InputArg.Value值
     */
    fun getWheelMap(start: Int, end: Int, multiple: Int): MutableMap<String, String> {
        val mutableList = mutableMapOf<String, String>()
        for (i in start..end step multiple) {
            mutableList[i.toString()] = i.toString()
        }
        return mutableList
    }

}