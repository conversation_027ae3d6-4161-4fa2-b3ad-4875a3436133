package com.dfl.smartscene.ui.edit.condition.trigger.charge

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.TriggerCharger
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo

/**
 *Created by 钟文祥 on 2025/4/17.
 *Describer: 充放电
 */
class ChargeTriggerViewModel : BaseViewModel() {
    val chargeWindowsLiveData = MutableLiveData<ArrayList<ConditionItemBean<TriggerCharger>>>()

    fun initData(resources: Resources) {
        val list = ArrayList<ConditionItemBean<TriggerCharger>>()

        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_add_charge),
                R.drawable.scene_icon_me_trigger_charge,
                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_CHARGE),
                TriggerCharger.CHARGER
            )
        )
//        list.add(
//            ConditionItemBean(
//                ConditionItemBean.SKILL_CONTENT,
//                resources.getString(R.string.scene_text_edit_add_charge_end_time),
//                R.drawable.scene_icon_me_trigger_charge_end_time,
//                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_CHARGE_END_TIME),
//                TriggerCharger.CHARGER_END_TIME
//            )
//        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_add_discharge),
                R.drawable.scene_icon_me_trigger_discharge,
                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_DISCHARGE),
                TriggerCharger.DISCHARGE
            )
        )
        val type = SceneEditManager.TriggerType.CHARGE
        val result = SceneEditManager.checkChildItemIsActive(type, list)
        chargeWindowsLiveData.postValue(result)
    }

    fun getListData(resources: Resources, type: TriggerCharger): List<CheckListBean> {
        return when (type) {
            TriggerCharger.CHARGER, TriggerCharger.DISCHARGE -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_common_start), true),
                    CheckListBean(resources.getString(R.string.scene_text_common_end), false),
                )
            }

            TriggerCharger.CHARGER_END_TIME -> {
                arrayListOf(
                    CheckListBean("10" + resources.getString(R.string.scene_text_edit_minute), true),
                    CheckListBean("20" + resources.getString(R.string.scene_text_edit_minute), false),
                    CheckListBean("30" + resources.getString(R.string.scene_text_edit_minute), false),
                    CheckListBean("40" + resources.getString(R.string.scene_text_edit_minute), false),
                    CheckListBean("50" + resources.getString(R.string.scene_text_edit_minute), false),
                )
            }
        }
    }

    fun getTriggerConditionData(type: TriggerCharger): ArrayList<ScenarioInfo.Condition> {
        val list = ArrayList<ScenarioInfo.Condition>()
        val args = ArrayList<InputArgInfo>()
        when (type) {
            TriggerCharger.CHARGER -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_TRIGGER_CHARGE, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Condition(
                        1, "", SkillsListConstant.SKILL_ID_TRIGGER_CHARGE, "", args
                    )
                )
            }

            TriggerCharger.DISCHARGE -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_TRIGGER_DISCHARGE, ArgType.INT32, "9"
                    )
                )

                list.add(
                    ScenarioInfo.Condition(
                        1, "", SkillsListConstant.SKILL_ID_TRIGGER_DISCHARGE, "", args
                    )
                )
            }

            TriggerCharger.CHARGER_END_TIME -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_TRIGGER_CHARGE_END_TIME, ArgType.INT32, "2"
                    )
                )

                list.add(
                    ScenarioInfo.Condition(
                        1, "", SkillsListConstant.SKILL_ID_TRIGGER_CHARGE_END_TIME, "", args
                    )
                )
            }
        }


        return list
    }

    fun getInputArg(type: TriggerCharger): ArrayList<Int> {
        return when (type) {
            TriggerCharger.CHARGER -> {
                arrayListOf(1, 0)
            }

            TriggerCharger.DISCHARGE -> {
                arrayListOf(9, 0)
            }

            TriggerCharger.CHARGER_END_TIME -> {
                arrayListOf(2, 3, 4, 5, 6)
            }
        }
    }
}