package com.dfl.smartscene.ui.main.discover.detail

import android.animation.Animator
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.view.View
import androidx.core.view.isVisible
import androidx.recyclerview.widget.GridLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.dfl.android.common.base.MVVMActivity
import com.dfl.android.common.global.GlobalConfig
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.util.CommonToastUtils
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.BR
import com.dfl.smartscene.R
import com.dfl.smartscene.apiweb.utils.ApiReqResState
import com.dfl.smartscene.bean.community.reponse.CommunitySceneInfo
import com.dfl.smartscene.bean.community.request.CommunityFocusRequest
import com.dfl.smartscene.bean.main.MySceneBean
import com.dfl.smartscene.bean.main.ScenarioBean
import com.dfl.smartscene.communication.UserCenterManager
import com.dfl.smartscene.community.usercenter.UserCenterActivity
import com.dfl.smartscene.customapi.SoundManager
import com.dfl.smartscene.databinding.SceneActivityDetailBinding
import com.dfl.smartscene.soa.SceneManager
import com.dfl.smartscene.ui.edit.SceneNewEditActivity
import com.dfl.smartscene.ui.edit.SceneNewEditEnterType
import com.dfl.smartscene.ui.main.discover.detail.adapter.SceneDetailActionAdapter
import com.dfl.smartscene.ui.main.discover.detail.adapter.SceneDetailConditionAdapter
import com.dfl.smartscene.util.NetWorkUtils
import com.dfl.smartscene.util.UIUtils
import com.google.gson.Gson
import com.jeremyliao.liveeventbus.LiveEventBus

/**
 *  wbwswang
 *  email: <EMAIL>
 *  time: 2022/08/05
 *  desc: 场景详情页面
 *  version:1.0
 */
class SceneDetailActivity : MVVMActivity<SceneActivityDetailBinding, SceneDetailViewModel>(),
    View.OnClickListener {

    private var mConditionAdapter: SceneDetailConditionAdapter? = null
    private var mActionAdapter: SceneDetailActionAdapter? = null

    private var mIsTryRunState = false //判断当前是否是'试用执行'状态

    /**
     * 是否从账号页跳转
     * 是,不再跳转到账号页
     */
    private var mFromUserCenterStart = false
    override fun getLayoutId(): Int {
        return R.layout.scene_activity_detail
    }

    override fun getBindingVariable(): Int {
        return BR.vm
    }

    override fun getViewModelClass(): Class<SceneDetailViewModel> {
        return SceneDetailViewModel::class.java
    }

    //1
    override fun initView() {
        super.initView()
        transparentStatusBar()
        mViewDataBinding.btnUseTemplateDetail.setOnClickListener(this)
        mViewDataBinding.btnBackDetail.setOnClickListener(this)
        mViewDataBinding.tvTitle.setOnClickListener(this)
        mViewDataBinding.btnTryRunDetail.setOnClickListener(this)
        mViewDataBinding.tvCommunityUserName.setOnClickListener(this)
        mViewDataBinding.ivCommunityUserHeard.setOnClickListener(this)
        //关注
        mViewDataBinding.tvCommunitySubscribe.setOnClickListener(this)
        //已关注
        mViewDataBinding.tvCommunitySubscribed.setOnClickListener(this)
        //不再关注按钮
        mViewDataBinding.tvNoLongerSub.setOnClickListener(this)
        //取消按钮
        mViewDataBinding.tvNoLongerSubCancel.setOnClickListener(this)
        //下载
        mViewDataBinding.btnCommunityDownScene.setOnClickListener(this)
        //点赞
//        mViewDataBinding.btnCommunityLikes.setOnClickListener(this)
//        //让点赞按钮监听深浅主题切换
//        mViewDataBinding.btnCommunityLikes.setUiModeChangeListener { theme ->
//            if (theme == 0) {
//                mViewDataBinding.btnCommunityLikes.imgView?.setAnimation(R.raw.scene_likes_day)
//            } else if (theme == 1) {
//                mViewDataBinding.btnCommunityLikes.imgView?.setAnimation(R.raw.scene_likes_night)
//            }
//            if (mViewModel.communitySceneInfo?.isLike == true) {
//                mViewDataBinding.btnCommunityLikes.setTextColor(getColor(R.color.scene_primary_color_highlight))
//            } else {
//                mViewDataBinding.btnCommunityLikes.setTextColor(getColor(R.color.scene_color_text_1))
//            }
//        }
        initLiveEventListener()
    }

    private fun initLiveEventListener() {
        //试用结束
        LiveEventBus.get(GlobalLiveEventConstants.KEY_TRY_SCENE_END, String::class.java).observe(this) {
            mViewModel.handleTrySceneEnd(it)
        }
        //取消下载
        LiveEventBus.get(GlobalLiveEventConstants.KEY_COMMUNITY_DOWNLOAD_CANCEL, Int::class.java).observe(this) {
            if (it == 1) {
                mViewModel.cancelDownLoadDisposable()
            }
        }
        //场景添加成功的事件
        LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_ADD_SUCCESS, MySceneBean::class.java).observe(this) {
            //添加成功，退出界面
            if (it != null) {
                Handler(Looper.getMainLooper()).postDelayed({
                    if (!isFinishing && !isDestroyed) {
                        CommonLogUtils.logD(TAG, "下载页添加场景成功，退出当前界面")
                        finish()
                    }
                }, 100)
            }
        }
    }

    //2
    override fun initData() {
        super.initData()

        mViewModel.communitySceneInfo = intent.getParcelableExtra<CommunitySceneInfo?>(COMMUNITY_DETAIL)
        if (mViewModel.communitySceneInfo != null) { //社区场景 跳转进来
            mViewDataBinding.item = mViewModel.communitySceneInfo
            mViewModel.scenarioBean = ScenarioBean(
                mViewModel.communitySceneInfo?.scenarioInfoDa, false, false, false, 0
            )
            //是否从账号页跳转
            mFromUserCenterStart = intent.getBooleanExtra(FROM_USER_CENTER_START, false)
            showCommunityView()
        } else {
            val sceneDetailJson = intent.getStringExtra(SCENE_DETAIL)
            mViewModel.scenarioBean = Gson().fromJson(sceneDetailJson, ScenarioBean::class.java)
            mViewDataBinding.item = null
        }

        if (!mViewModel.initData(resources)) {
            CommonToastUtils.show(getString(R.string.scene_toast_detail_error_data))
            finish()
            return
        }
        //加上日志打印，方便定位场景详情数据错误
        CommonLogUtils.logI(TAG, mViewModel.scenarioBean.toString())
        createObserver()

        mViewModel.scenarioBean?.scenarioInfo?.conditions?.size?.let {
            if (it > 0 || mViewModel.scenarioBean?.scenarioInfo?.edgeCondition != null) mViewModel.scenarioBean?.isAutoRun =
                true
            mViewModel.scenarioBean?.isAskBeforeAutoRun = true
        }
        //		dealScene()
        //需要将新建场景的ID给设置一下，否则可能无法进行场景的试用
        mViewModel.scenarioBean?.scenarioInfo?.scenarioId = SceneManager.getNewSceneId()

        initRecyclerView()
    }

    private fun createObserver() {
        initObserverTryStartScene() //试用方法回调
        initObserverFocusApiReqResState() //关注回调
        initObserverLikeApiReqResState() //点赞回调
        //        initObserverDownLoadApiReqResState() //下载回调
        initEventBusFocus()
        initEventBusLike()
    }

    //关注事件成功后更新本页
    private fun initEventBusFocus() {
        LiveEventBus.get<CommunitySceneInfo>(GlobalLiveEventConstants.KEY_COMMUNITY_FOCUS_SUCCESS_UPDATE_UI)
            .observe(this) {
                val isSubscribed = CommunityFocusRequest.isSubscribed(it.userSubscribe)
                //已经更新关注/未关注ui，不再变更
                if (mViewDataBinding.tvCommunitySubscribed.isVisible == isSubscribed) {
                    CommonLogUtils.logI(TAG, "isSubscribed:$isSubscribed,已经更新关注/未关注ui")
                    return@observe
                }
                mViewModel.communitySceneInfo?.userSubscribe = it.userSubscribe
                updateBtnUserSubscribe()
            }
    }

    //点赞事件成功后更新本页
    private fun initEventBusLike() {
        LiveEventBus.get<CommunitySceneInfo>(GlobalLiveEventConstants.KEY_COMMUNITY_LIKE_SUCCESS_UPDATE_UI)
            .observe(this) {
                if (mViewModel.communitySceneInfo?.communityScenarioId == it.communityScenarioId) {
                    mViewModel.communitySceneInfo?.likes = it.likes
                    mViewModel.communitySceneInfo?.isLike = it.isLike
                    mViewDataBinding.item = mViewModel.communitySceneInfo
//                    initLikeUpdate()
                }
            }
    }

    //关注回调
    private fun initObserverFocusApiReqResState() {
        mViewModel.mFocusApiReqResState.observe(this) {
            when (it.state) {
                ApiReqResState.ReqResState.GetValueStart, null -> {}
                ApiReqResState.ReqResState.GetValueFinish -> {
                    updateBtnUserSubscribe()
                }

                ApiReqResState.ReqResState.Error -> {
                    CommonToastUtils.show(
                        it.map[GlobalLiveEventConstants.KEY_COMMUNITY_FOCUS_SUCCESS_UPDATE_UI] as String
                    )
                }
            }
        }
    }

//    private fun initLikeUpdate() {
//        //点赞的逻辑
//        if (mViewModel.communitySceneInfo?.isLike == true) {
//            // 进入到此行，说明数字颜色已改变了
//            if (!mViewDataBinding.btnCommunityLikes.imgView.isAnimating) { //已成功点赞后，发现动画还没播，改变图片
//                mViewDataBinding.btnCommunityLikes.imgView.progress = 1.0f
//            } else {
//                //已成功点赞后，发现动画还在播，不改变图片
//            }
//            mViewDataBinding.btnCommunityLikes.imgView.removeAllUpdateListeners()
//            mViewDataBinding.btnCommunityLikes.setTextColor(getColor(R.color.scene_primary_color_highlight))
//        } else {
//            mViewDataBinding.btnCommunityLikes.imgView.progress = 0.0f
//            mViewDataBinding.btnCommunityLikes.imgView.addAnimatorListener(object : Animator.AnimatorListener {
//                override fun onAnimationStart(animation: Animator) {
//                }
//
//                override fun onAnimationEnd(animation: Animator) { //动画播放完后改变文字颜色，和定住最后一帧
//                    mViewDataBinding.btnCommunityLikes.imgView.progress = 1.0f
//                    mViewDataBinding.btnCommunityLikes.setTextColor(getColor(R.color.scene_primary_color_highlight))
//                }
//
//                override fun onAnimationCancel(animation: Animator) {
//                }
//
//                override fun onAnimationRepeat(animation: Animator) {
//                }
//            })
//            mViewDataBinding.btnCommunityLikes.setTextColor(getColor(R.color.scene_color_text_1))
//        }
//    }

    //点赞回调
    private fun initObserverLikeApiReqResState() {
//        initLikeUpdate()
        mViewModel.mLikeApiReqResState.observe(this) {
            when (it.state) {
                ApiReqResState.ReqResState.GetValueStart -> {}
                ApiReqResState.ReqResState.GetValueFinish -> {
                    mViewDataBinding.item = mViewModel.communitySceneInfo
                    //                    CommonToastUtils.show(it.map[LiveEventConstants.KEY_COMMUNITY_LIKE_SUCCESS_UPDATE_UI] as String)
//                    initLikeUpdate()
                }

                ApiReqResState.ReqResState.Error -> {
                    //                    CommonToastUtils.show(it.map[LiveEventConstants.KEY_COMMUNITY_LIKE_SUCCESS_UPDATE_UI] as String)
                }
            }
        }
    }

    //    //下载回调
    //    private fun initObserverDownLoadApiReqResState() {
    //        mViewModel.mDownLoadApiReqResState.observe(this) {
    //            when (it.state!!) {
    //                ApiReqResState.ReqResState.GetValueStart -> {}
    //                ApiReqResState.ReqResState.GetValueFinish -> {
    //                    mViewDataBinding.item = mViewModel.communitySceneInfo
    //                }
    //                ApiReqResState.ReqResState.Error -> {
    //                    //                    ToastUtils.showShort("下载失败")
    //                }
    //            }
    //        }
    //    }

    //试用方法回调
    private fun initObserverTryStartScene() {
        mViewModel.onTryStartSceneLiveData.observe(this) { onTry ->
            if (onTry) {
                mIsTryRunState = true
                mViewDataBinding.btnTryRunDetail.text = ""
                mViewDataBinding.lottieDetailTryRun.setAnimation(R.raw.scene_try_run)
                mViewDataBinding.lottieDetailTryRun.playAnimation()
            } else {
                mIsTryRunState = false
                mViewDataBinding.lottieDetailTryRun.cancelAnimation()
                mViewDataBinding.lottieDetailTryRun.clearAnimation()
                mViewDataBinding.btnTryRunDetail.text = getString(R.string.scene_text_scene_detail_try)
            }
            mViewDataBinding.lottieDetailTryRun.isVisible = onTry
        }
    }

    //显示社区场景 详情的专有控件
    private fun showCommunityView() {

        mViewDataBinding.ivCommunityUserHeard.setLayerType(View.LAYER_TYPE_HARDWARE, null)
        mViewDataBinding.ivCommunityUserHeard.setRoundCorner(50)
        val mHeardOptions: RequestOptions = UIUtils.getGlideOptions(R.drawable.scene_icon_community_user_head_default)
        Glide.with(this).load(mViewModel.communitySceneInfo?.userAvatar).apply(mHeardOptions)
            .into(mViewDataBinding.ivCommunityUserHeard)

        //关注按钮呈现
        updateBtnUserSubscribe()

    }

    /**关注按钮呈现*/
    private fun updateBtnUserSubscribe() {
        //更新关注按钮隐藏不再关注菜单，防止显示关注按钮发送不再关注请求，导致关注数-1
        mViewDataBinding.viewPop.visibility = View.GONE
        //用户未登录不显示关注按钮
        if (UserCenterManager.checkUserLoginStatus(false) == null) {
            mViewDataBinding.tvCommunitySubscribe.visibility = View.GONE
            mViewDataBinding.tvCommunitySubscribed.visibility = View.GONE
            return
        }
        when (mViewModel.communitySceneInfo?.userSubscribe) {
            //未关注
            0 -> {
                mViewDataBinding.tvCommunitySubscribe.visibility = View.VISIBLE
                mViewDataBinding.tvCommunitySubscribed.visibility = View.INVISIBLE
            }
            //已关注
            1 -> {
                mViewDataBinding.tvCommunitySubscribe.visibility = View.INVISIBLE
                mViewDataBinding.tvCommunitySubscribed.visibility = View.VISIBLE
            }
            //用户自己
            2 -> {
                mViewDataBinding.tvCommunitySubscribe.visibility = View.GONE
                mViewDataBinding.tvCommunitySubscribed.visibility = View.GONE
            }
        }
    }


    /**
     * 此方法仅为了适配后台接口返回的desc内容为“续航里程 小于60km”这样的情况，
     * 如果后台修复此问题的话，可以注释此方法
     */
    private fun dealScene() {
        mViewModel.scenarioBean?.scenarioInfo?.edgeCondition?.let {
            try {
                val content = it.desc.split(" ")
                it.category = content[0]
                it.desc = content[1]
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        mViewModel.scenarioBean?.scenarioInfo?.conditions?.let {
            if (it.size > 0) {
                try {
                    it.forEach { scene ->
                        val content = scene.desc.split(" ")
                        scene.category = content[0]
                        scene.desc = content[1]
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }

    override fun onDestroy() {
        if (mViewDataBinding.lottieDetailTryRun.isVisible) {
            mViewDataBinding.lottieDetailTryRun.cancelAnimation()
            mViewDataBinding.lottieDetailTryRun.clearAnimation()
        }
        mViewModel.cancelDisposable()
        super.onDestroy()
    }


    @SuppressLint("CheckResult")
    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.btn_use_template_detail -> { //用模板创建
                if (DebouncingUtils.isValid(v)) {
                    if (SceneManager.getSceneInfoList().size >= GlobalConfig.SCENE_COUNT_MAX) {
                        //从UX2.5开始，改用toast提示
                        CommonToastUtils.show(R.string.scene_text_me_scene_limit)
                        SoundManager.playSoundEffect(SoundManager.SoundType.TOAST)
                        return
                    }

                    mViewModel.scenarioBean ?: return
                    SceneNewEditActivity.launchSceneEditWithMyScene(
                        this, enterBean = MySceneBean(
                            mViewModel.scenarioBean!!,
                            isSceneStart = false,
                            isTop = false,
                            isAddNewScene2ShowAnima = true,
                            isAddScene = true  //需要创建并保存
                        ), enterType = SceneNewEditEnterType.Recommend, scenario_L3id = ""
                    )
                }
            }

            R.id.btn_back_detail, R.id.tv_title -> {
                finish()
            }

            R.id.btn_try_run_detail -> {
                if (DebouncingUtils.isValid(v)) {
                    if (!mIsTryRunState) {
                        mViewModel.tryStartScene()
                    } else {
                        mViewModel.stopTryScene()
                    }
                }
            }

            //关注事件
            R.id.tv_community_subscribe -> {
                //检查账号登录
                UserCenterManager.checkDebouncingAndNetworkAndLogin(v, supportFragmentManager) {
                    //未关注发送关注
                    mViewModel.focusOrNoFocus(
                        mViewModel.communitySceneInfo, supportFragmentManager, CommunityFocusRequest.SUBSCRIBED
                    )
                }
            }

            R.id.tv_community_subscribed -> {
                //检查账号登录
                UserCenterManager.checkDebouncingAndNetworkAndLogin(v, supportFragmentManager) {
                    mViewDataBinding.viewPop.visibility = View.VISIBLE
                }
            }
            //不再关注
            R.id.tv_no_longer_sub -> {
                //检查账号登录
                UserCenterManager.checkDebouncingAndNetworkAndLogin(v, supportFragmentManager) {
                    //显示不再关注按钮时才发送取消关注，避免无意义重复请求
                    if (mViewDataBinding.tvCommunitySubscribed.isVisible) {
                        mViewModel.focusOrNoFocus(
                            mViewModel.communitySceneInfo, supportFragmentManager, CommunityFocusRequest.UNSUBSCRIBED
                        )
                    }
                    mViewDataBinding.viewPop.visibility = View.GONE
                }
            }
            //不再关注 取消
            R.id.tv_no_longer_sub_cancel -> {
                if (!DebouncingUtils.isValid(v)) return
                mViewDataBinding.viewPop.visibility = View.GONE
            }
            //下载
            R.id.btn_community_down_scene -> {
                if (!DebouncingUtils.isValid(v)) return
                mViewModel.downloadOrNo(
                    mViewModel.communitySceneInfo, supportFragmentManager, this@SceneDetailActivity, true
                )
            }
            //点赞
//            R.id.btn_community_likes -> {
//                if (!DebouncingUtils.isValid(v)) return
//                if (mViewModel.communitySceneInfo?.isLike == false) {
//                    mViewDataBinding.btnCommunityLikes.imgView.repeatCount = 0 //多重复0次循环
//                    mViewDataBinding.btnCommunityLikes.imgView.playAnimation()
//                }
//                mViewModel.likeOrNoLike(mViewModel.communitySceneInfo, supportFragmentManager)
//            }
            //用户头像,用户名,判断双击,网络
            R.id.tv_community_user_name, R.id.iv_community_user_heard -> {
                //从账号页跳转到详情不再跳转
                if (mFromUserCenterStart || !DebouncingUtils.isValid(v)) return
                if (!NetWorkUtils.isConnectNetWork()) {
                    CommonToastUtils.showLong(CommonUtils.getString(R.string.scene_toast_web_exception_device_no_network))
                    return
                }
                //社区场景跳转过来
                if (mViewModel.communitySceneInfo != null) {
                    UserCenterActivity.actionStart(
                        this, mViewModel.communitySceneInfo?.isOfficial, mViewModel.communitySceneInfo?.uuid
                    )
                }
            }
        }
    }


    @SuppressLint("NotifyDataSetChanged")
    private fun initRecyclerView() {
        mConditionAdapter = SceneDetailConditionAdapter()
        val manager = GridLayoutManager(this, 3)
        manager.spanSizeLookup = mConditionAdapter?.getLayoutManager()
        mViewDataBinding.rvDetailSceneCondition.layoutManager = manager
        mViewDataBinding.rvDetailSceneCondition.addItemDecoration(SceneDetailConditionAdapter.MyGridItemDecoration())
        mViewDataBinding.rvDetailSceneCondition.adapter = mConditionAdapter

        mActionAdapter = SceneDetailActionAdapter()

        mViewDataBinding.rvDetailSceneAction.addItemDecoration(
            SceneDetailActionAdapter.MyGridItemDecoration()
        )
        mViewDataBinding.rvDetailSceneAction.adapter = mActionAdapter

        mViewModel.sceneConditionLiveData.observe(this) {
            mConditionAdapter?.data = it
            mConditionAdapter?.notifyDataSetChanged()
            if (it.isEmpty()) {
                mViewDataBinding.rvDetailSceneCondition.visibility = View.GONE
                mViewDataBinding.tvDetailSceneConditionName.visibility = View.GONE
            } else {
                mViewDataBinding.rvDetailSceneCondition.visibility = View.VISIBLE
                mViewDataBinding.tvDetailSceneConditionName.visibility = View.VISIBLE
            }
            if (mViewDataBinding.item == null) {
                mViewDataBinding.cslDetailBottom.visibility = View.VISIBLE
            } else {
                mViewDataBinding.cslDetailBottom.visibility = View.GONE
            }

        }

        mViewModel.sceneActionLiveData.observe(this) {
            mActionAdapter?.data = it
            mActionAdapter?.notifyDataSetChanged()
        }
    }

    companion object {
        private const val SCENE_DETAIL = "SCENE_DETAIL"
        private const val TAG = GlobalConstant.GLOBAL_TAG.plus("SceneDetailActivity")

        /**从发现进入*/
        fun startSceneDetailActivity(context: Context, json: String) {
            val intent = Intent(context, SceneDetailActivity::class.java)
            intent.putExtra(SCENE_DETAIL, json)
            context.startActivity(intent)
        }

        private const val COMMUNITY_DETAIL = "COMMUNITY_DETAIL"

        /**从社区进入*/
        fun startCommunitySceneDetailActivity(
            context: Context, communitySceneInfo: CommunitySceneInfo
        ) {
            val intent = Intent(context, SceneDetailActivity::class.java)
            intent.putExtra(COMMUNITY_DETAIL, communitySceneInfo)
            context.startActivity(intent)
        }

        private const val FROM_USER_CENTER_START = "FROM_USER_CENTER_START_COMMUNITY_SCENE_DETAIL_ACTIVITY"
        fun fromUserCenterStartCommunitySceneDetailActivity(
            context: Context, communitySceneInfo: CommunitySceneInfo
        ) {
            val intent = Intent(context, SceneDetailActivity::class.java)
            intent.putExtra(COMMUNITY_DETAIL, communitySceneInfo)
            intent.putExtra(FROM_USER_CENTER_START, true)
            context.startActivity(intent)
        }
    }
}