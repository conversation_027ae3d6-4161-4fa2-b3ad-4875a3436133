package com.dfl.smartscene.ui.overlay.common.singlewheel

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/13
 * desc :用于区分所有共用单滚轮弹窗，
 * 一般使用SINGLE_WHEEL_INPUT_ARG,手动设置输入值
 * 注意如果由于显示原因需要做特殊处理，可以自行新增枚举
 * version: 1.0
 */
enum class SingleWheelType {

    /**
     * 使用输入的InputArgList值,值输出给ActionList的index=0
     */
    SINGLE_WHEEL_INPUT_ARG,

    /**使用滚轮值作为InputArg值,值输出给ActionList的index=0*/
    SINGLE_WHEEL_DEFAULT,

    /**
     *打开应用,值输出给ActionList的index=1
     */
    APP_OPEN,

    /**
     * 车窗关闭度描述：全开，全关，关闭xx%
     */
    SINGLE_WHEEL_DOOR_WINDOWS,
}