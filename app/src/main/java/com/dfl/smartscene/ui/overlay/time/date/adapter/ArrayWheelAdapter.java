package com.dfl.smartscene.ui.overlay.time.date.adapter;

import com.dfl.smartscene.widget.wheel.adapter.WheelAdapter;

import java.util.List;

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :滚轮适配器
 * version: 1.0
 */
public class ArrayWheelAdapter<T> implements WheelAdapter<T> {

    // items
    private final List<T> items;

    /**
     * Constructor
     *
     * @param items the items
     */
    public ArrayWheelAdapter(List<T> items) {
        this.items = items;
    }

    @Override
    public T getItem(int index) {
        if (index >= 0 && index < items.size()) {
            return items.get(index);
        }
        return null;
    }

    @Override
    public int getItemsCount() {
        return items.size();
    }

    @Override
    public int indexOf(T o) {
        return items.indexOf(o);
    }

}