package com.dfl.smartscene.ui.overlay.common.singlecheck

import android.content.Context
import android.os.Bundle
import android.view.View
import android.widget.CheckedTextView
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.core.view.isVisible
import com.dfl.android.common.base.BaseDialog
import com.dfl.smartscene.R
import com.dfl.smartscene.databinding.SceneDialogSingleCheckBinding
import com.iauto.scenarioadapter.ScenarioInfo
import org.greenrobot.eventbus.EventBus

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/20
 * desc :单一选项的选择弹窗 一项选择
 * version: 1.0
 * update：钟文祥 1个选项 （通用）
 */
@Deprecated("ui2.0后使用RadioListDialogFragment")
class SingleCheckDialog(
    context: Context, private var title: String, checkText: String
) : BaseDialog(context), View.OnClickListener {
    private lateinit var mVB: SceneDialogSingleCheckBinding

    /**
     * 对话框对应的条件值
     */
    private var condition: ScenarioInfo.Condition? = null

    /**
     * 对话框对应的动作列表
     */
    private var sequenceList: ArrayList<ScenarioInfo.Sequence>? = null

    private var mTitleTv: TextView? = null
    private var mCheckCtv: CheckedTextView? = null

    private var mCheckText = checkText
    private var mLeftTvIcon: Int = 0
    private var mTitleDesc: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mVB = SceneDialogSingleCheckBinding.inflate(layoutInflater)
        setContentView(mVB.root)
        initView()
    }

    fun setSceneCondition(condition: ScenarioInfo.Condition) {
        this.condition = condition
    }

    fun setSceneSequence(sequence: ArrayList<ScenarioInfo.Sequence>) {
        this.sequenceList = sequence
    }

    fun setTextDrawableLeftIcon(@DrawableRes resId: Int) {
        mLeftTvIcon = resId
    }

    fun setTitleDesc(titleDesc: String) {
        mTitleDesc = titleDesc
    }

    fun initView() {
        mTitleTv = findViewById(R.id.tv_single_switch_title)
        mCheckCtv = findViewById(R.id.ctv_single_open)
        val imageView = findViewById<ImageView>(R.id.imv_open)
        val linearLayout = findViewById<LinearLayout>(R.id.ll_open)
        val titleDesc = findViewById<TextView>(R.id.tv_single_switch_title_desc)
        linearLayout.isSelected = true
        mTitleTv?.text = title
        mCheckCtv?.text = mCheckText
        if (mLeftTvIcon != 0) {
            imageView.setImageResource(mLeftTvIcon)
            imageView.isVisible = true
        }
        if (mTitleDesc.isNotEmpty()) {
            titleDesc.text = mTitleDesc
            titleDesc.isVisible = true
        }
        mCheckCtv?.setOnClickListener(this)
        mVB.includeDialogBottom.btnNormal.setOnClickListener(this)
        mVB.includeDialogBottom.btnPrimary.setOnClickListener(this)
    }

    override fun onClick(v: View?) {
        v?.id?.let { it ->
            if (it == R.id.btn_normal) {
                dismissWithAnimate()
            } else if (it == R.id.btn_primary) {
                condition?.let { c ->
                    c.desc = title.plus(" ").plus(mCheckCtv?.text.toString())
                    c.category = title
                    EventBus.getDefault().post(c)
                }
                sequenceList?.let {
                    val sequence = it[0]
                    sequence.action.category = title
                    sequence.action.desc = mCheckCtv?.text.toString()
                    EventBus.getDefault().post(sequence)
                }
                dismissWithAnimate()
            }
        }
    }
}