package com.dfl.smartscene.ui.overlay.time.timepicker;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dfl.android.common.base.BaseDialog;
import com.dfl.android.common.global.SkillsListConstant;
import com.dfl.android.common.util.TimeUtils;
import com.dfl.smartscene.R;
import com.dfl.smartscene.customapi.SoundManager;
import com.dfl.smartscene.databinding.SceneDialogTimePickerBinding;
import com.dfl.smartscene.util.UIUtils;
import com.dfl.smartscene.widget.wheel.OnItemScrollListener;
import com.dfl.smartscene.widget.wheel.WheelView;
import com.iauto.scenarioadapter.ArgType;
import com.iauto.scenarioadapter.InputArgInfo;
import com.iauto.scenarioadapter.ScenarioInfo;

import org.greenrobot.eventbus.EventBus;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * Created by Tobin on 2021/11/2.
 * Email: <EMAIL>
 * Description: 时分秒wheel选择对话框
 * update ：钟文祥
 */
public class TimePickerDialog<T> extends BaseDialog implements View.OnClickListener {
    private final List<T> hourList;
    private final List<T> minuteList;
    private final String title;
    private final Context mContext;
    private final int mSkillID;
    /** 指定时间确认按钮事件 */
    public OnTimePickerListener listener = null;
    private SceneDialogTimePickerBinding mVB;
    private List<T> secondList;
    private T defaultSec;
    private T defaultMin;
    private T defaultHour;
    // component
    private TextView prefixTv, pointTv;
    private WheelView hourWheelView;
    private WheelView minuteWheelView;
    private WheelView secondWheelView;
    private String mLeftUnit, mMiddleUnit, mRightUnit;
    private boolean[] isCyclicArr = new boolean[]{true, true, true};

    /**
     * 构造方法.
     *
     * @param context Context
     * @param hourList 滚动数据 小时
     * @param minuteList 滚动数据 分
     * @param title 标题
     */
    public TimePickerDialog(@NonNull Context context, @NonNull List<T> hourList,
                            @NonNull List<T> minuteList,
                            @NonNull String title,
                            int skillId) {
        super(context);
        this.mContext = context;
        this.hourList = hourList;
        this.minuteList = minuteList;
        this.title = title;
        this.mSkillID = skillId;
    }

    /**
     * 构造方法.
     *
     * @param context Context
     * @param hourList 滚动数据 小时
     * @param minuteList 滚动数据 分
     * @param title 标题
     */
    public TimePickerDialog(@NonNull Context context, @NonNull List<T> hourList,
                            @NonNull List<T> minuteList,
                            @NonNull String title,
                            int skillId,
                            @Nullable T defaultHour,
                            @Nullable T defaultMin,
                            @Nullable T defaultSec

    ) {
        super(context);
        this.mContext = context;
        this.hourList = hourList;
        this.minuteList = minuteList;
        this.title = title;
        this.mSkillID = skillId;
        this.defaultHour = defaultHour;
        this.defaultMin = defaultMin;
        this.defaultSec = defaultSec;
    }

    public TimePickerDialog(@NonNull Context context, @NonNull List<T> hourList,
                            @NonNull List<T> minuteList, @NonNull List<T> secondList,
                            @NonNull String title,
                            int skillId) {
        super(context);
        this.mContext = context;
        this.hourList = hourList;
        this.minuteList = minuteList;
        this.secondList = secondList;
        this.title = title;
        this.mSkillID = skillId;
    }


    public TimePickerDialog(@NonNull Context context, @NonNull List<T> hourList,
                            @NonNull List<T> minuteList, @NonNull List<T> secondList,
                            @NonNull String title,
                            int skillId,
                            @Nullable T defaultHour,
                            @Nullable T defaultMin,
                            @Nullable T defaultSec) {
        super(context);
        this.mContext = context;
        this.hourList = hourList;
        this.minuteList = minuteList;
        this.secondList = secondList;
        this.defaultHour = defaultHour;
        this.defaultMin = defaultMin;
        this.defaultSec = defaultSec;
        this.title = title;
        this.mSkillID = skillId;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mVB = SceneDialogTimePickerBinding.inflate(getLayoutInflater());
        setContentView(mVB.getRoot());

        mVB.includeDialogBottom.btnPrimary.setOnClickListener(this);
        mVB.includeDialogBottom.btnNormal.setOnClickListener(this);
        TextView tvTitle = findViewById(R.id.tv_picker_title);
        tvTitle.setText(title);
        hourWheelView = findViewById(R.id.wv_hour_options);
        initWheelView(hourWheelView, hourList, mLeftUnit, 1);

        minuteWheelView = findViewById(R.id.wv_minute_options);
        initWheelView(minuteWheelView, minuteList, mMiddleUnit, 2);

        secondWheelView = findViewById(R.id.wv_second_options);

        prefixTv = findViewById(R.id.tv_prefix);
        //prefixTv2 = findViewById(R.id.tv_prefix2);
        //prefixTv3 = findViewById(R.id.tv_prefix3);
        pointTv = findViewById(R.id.tv_point_dialog);
        //hourTv = findViewById(R.id.tv_hour_dialog);
        //minuteTv = findViewById(R.id.tv_minute_dialog);
        //secondTv = findViewById(R.id.tv_second_dialog);

        if (mSkillID == SkillsListConstant.SKILL_ID_STATUS_NAVIGATION_EXPECTED_ARRIVAL_TIME) {
            initExpectedArrivalTimeView();
        } else {
            initNormalView();
        }
        hourWheelView.setCyclic(isCyclicArr[0]);
        minuteWheelView.setCyclic(isCyclicArr[1]);
        secondWheelView.setCyclic(isCyclicArr[2]);
        checkDay2Enable();
    }

    private void initExpectedArrivalTimeView() {
        prefixTv.setVisibility(View.VISIBLE);
        //LinearLayout.LayoutParams lp1 = (LinearLayout.LayoutParams) prefixTv.getLayoutParams();
        //lp1.setMarginStart(AutoSizeUtils.dp2px(getContext(), 170));
        //lp1.setMarginEnd(AutoSizeUtils.dp2px(getContext(), 190));
        //prefixTv.setLayoutParams(lp1);
        //prefixTv2.setVisibility(View.INVISIBLE);
        //prefixTv3.setVisibility(View.INVISIBLE);
        //pointTv.setVisibility(View.GONE);
        //hourTv.setVisibility(View.VISIBLE);
        //LinearLayout.LayoutParams lp2 = (LinearLayout.LayoutParams) hourTv.getLayoutParams();
        //lp2.setMarginEnd(AutoSizeUtils.dp2px(getContext(), 80));
        //hourTv.setLayoutParams(lp2);
        //minuteTv.setVisibility(View.VISIBLE);
        //secondTv.setVisibility(View.GONE);
        //secondWheelView.setVisibility(View.GONE);

        hourWheelView.setCurrentItem(getSafeIndexFromList(hourList, defaultHour, 1));//默认值为09:00
        minuteWheelView.setCurrentItem(getSafeIndexFromList(minuteList, defaultMin, 0));
    }

    private void initNormalView() {
        if (secondList != null) {
            secondWheelView.setVisibility(View.VISIBLE);
            initWheelView(secondWheelView, secondList, mRightUnit, 3);
            mVB.includeDialogBottom.btnPrimary.setText(mContext.getString(R.string.scene_button_common_sure));
            hourWheelView.setCurrentItem(getSafeIndexFromList(hourList, defaultHour, 0));
            //当为动作推荐延时的时候默认值为00-00-00
            secondWheelView.setCurrentItem(getSafeIndexFromList(secondList, defaultSec, 0));
        } else {
            hourWheelView.setCurrentItem(getSafeIndexFromList(hourList, defaultHour, 9));//默认值为09:00
        }

        pointTv.setVisibility(secondList == null ? View.VISIBLE : View.GONE);
        //hourTv.setVisibility(secondList == null ? View.GONE : View.VISIBLE);
        //minuteTv.setVisibility(secondList == null ? View.GONE : View.VISIBLE);
        //secondTv.setVisibility(secondList == null ? View.GONE : View.VISIBLE);
        //CommonLogUtils.logD("TimePickerDialog---minute---initNormalView---Before", ""+minuteWheelView.getCurrentItem());
        minuteWheelView.setCurrentItem(getSafeIndexFromList(minuteList, defaultMin, 0));
        if (mSkillID == SkillsListConstant.SKILL_ID_STATUS_TIME_SOME_DAY_ID) {
            updateDays();
        }
    }

    private int getSafeIndexFromList(List<T> list, T value, int defaultIndex) {
        if (list == null || list.isEmpty()) return defaultIndex;
        if (value == null) return defaultIndex;
        int index = list.indexOf(value);
        return (index == -1) ? defaultIndex : index;
    }

    private void initWheelView(WheelView wheelView, List<T> list, String unit, int type) {
        if (mSkillID == SkillsListConstant.SKILL_ID_STATUS_TIME_SOME_DAY_ID) {
            UIUtils.initWheelViewP42R(mContext, wheelView, list, unit, 0, index -> {
                checkDay2Enable();
                if (type == 1 || type == 2) {
                    updateDays();
                }
            }, new OnItemScrollListener() {
                @Override
                public void onItemScrollStateChanged(WheelView wheelView, int currentPassItem, int oldScrollState, int scrollState, float totalScrollY) {
                    if (scrollState == WheelView.SCROLL_STATE_IDLE && currentPassItem != wheelView.getInitPosition()) {
                        wheelView.setCurrentItem(currentPassItem);
                        SoundManager.INSTANCE.playSoundEffect(SoundManager.SoundType.ROLLER);
                    }
                }

                @Override
                public void onItemScrolling(WheelView wheelView, int currentPassItem, int scrollState, float totalScrollY) {
                    mVB.includeDialogBottom.btnPrimary.setEnabled(false);
                    if (scrollState == WheelView.SCROLL_STATE_DRAGGING && currentPassItem != wheelView.getInitPosition()) {
                        wheelView.setCurrentItem(currentPassItem);
                        SoundManager.INSTANCE.playSoundEffect(SoundManager.SoundType.ROLLER);
                    }
                }
            });
        } else {
            UIUtils.initWheelViewP42R(mContext, wheelView, list, unit, 0, index -> {
                checkDay2Enable();
            }, new OnItemScrollListener() {
                @Override
                public void onItemScrollStateChanged(WheelView wheelView, int currentPassItem, int oldScrollState, int scrollState, float totalScrollY) {

                }

                @Override
                public void onItemScrolling(WheelView wheelView, int currentPassItem, int scrollState, float totalScrollY) {

                }
            });
        }

    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.btn_primary) {
            if (mSkillID == SkillsListConstant.SKILL_ID_STATUS_NAVIGATION_EXPECTED_ARRIVAL_TIME) {
                ok4ArrivalTime();
            } else if (mSkillID == SkillsListConstant.SKILL_ID_STATUS_TIME_SOME_DAY_ID) {
                if (listener != null)
                    listener.primary(hourWheelView.getCurrentItem(), //
                            (String) hourList.get(hourWheelView.getCurrentItem()), //
                            minuteWheelView.getCurrentItem(),//
                            (String) minuteList.get(minuteWheelView.getCurrentItem()),//
                            secondWheelView.getCurrentItem(),//
                            (String) secondList.get(secondWheelView.getCurrentItem()));
            } else {
                normalOk();
            }
            dismissWithAnimate();
        } else if (v.getId() == R.id.btn_normal) {
            dismissWithAnimate();
        }

    }

    private void ok4ArrivalTime() {
        String hour = (String) hourList.get(hourWheelView.getCurrentItem());
        String minutes = (String) minuteList.get(minuteWheelView.getCurrentItem());
        List<InputArgInfo> infoLists = new ArrayList<>();

        //        infoLists.add(new InputArgInfo(SkillsListConstant.INPUT_ARG_NAVIGATION_EXPECTED_ARRIVAL_TIME,
        //                ArgType.INT32,
        //                (Integer.parseInt(hour.substring(1)) * 60 + Integer.parseInt(minutes)) * 60 + ""
        //                , ""));

        infoLists.add(new InputArgInfo(SkillsListConstant.INPUT_ARG_STATUS_NAVIGATION_H,
                ArgType.INT32, Integer.parseInt(hour) + "", ""));
        infoLists.add(new InputArgInfo(SkillsListConstant.INPUT_ARG_STATUS_NAVIGATION_MIN,
                ArgType.INT32, Integer.parseInt(minutes) + "", ""));

        ScenarioInfo.Condition condition = new ScenarioInfo.Condition(
                1,
                prefixTv.getText().toString() + hour + mContext.getString(R.string.scene_text_common_hour) + minutes + mContext.getString(R.string.scene_text_common_minute),
                mSkillID, title, infoLists);
        EventBus.getDefault().post(condition);

    }

    private void normalOk() {
        String hour = (String) hourList.get(hourWheelView.getCurrentItem());
        String minutes = (String) minuteList.get(minuteWheelView.getCurrentItem());
        List<InputArgInfo> infoLists = new ArrayList<>();
        if (secondList != null) {
            String second = (String) secondList.get(secondWheelView.getCurrentItem());
            infoLists.add(new InputArgInfo(SkillsListConstant.INPUT_ARG_TRIGGER_TIME_SECOND_NAME, ArgType.INT32,
                    getDelayValue(hour, minutes, second)));
            String desc;
            if (hour.equals("00") && minutes.equals("00") && second.equals("00")) {
                desc = mContext.getString(R.string.scene_text_time_no_delay);
            } else if (hour.equals("00") && minutes.equals("00")) {
                desc = second + mContext.getString(R.string.scene_text_common_second);
            } else if (hour.equals("00")) {
                desc = minutes + mContext.getString(R.string.scene_text_common_minute) +
                        second + mContext.getString(R.string.scene_text_common_second);
            } else {
                desc = hour + mContext.getString(R.string.scene_text_common_hour) +
                        minutes + mContext.getString(R.string.scene_text_common_minute)
                        + second + mContext.getString(R.string.scene_text_common_second);
            }
            ScenarioInfo.Sequence action = new ScenarioInfo.Sequence(1, 1,
                    new ScenarioInfo.Action(
                            desc,
                            mSkillID,
                            title,
                            infoLists));
            EventBus.getDefault().post(action);
        } else {
            String desc = (mSkillID == SkillsListConstant.SKILLS_ID_TRIGGER_TIME_TIME_POINT ? "" :
                    prefixTv.getText().toString()) + hour + ":" + minutes;
            hour = TimeUtils.singleTimeRemoveZero(hour);
            minutes = TimeUtils.singleTimeRemoveZero(minutes);
            infoLists.add(new InputArgInfo(SkillsListConstant.INPUT_ARG_TRIGGER_TIME_HOUR_NAME, ArgType.INT32, hour,
                    ""));
            infoLists.add(new InputArgInfo(SkillsListConstant.INPUT_ARG_TRIGGER_TIME_MINUTE_NAME, ArgType.INT32,
                    minutes, ""));
            infoLists.add(new InputArgInfo(SkillsListConstant.INPUT_ARG_TRIGGER_TIME_SECOND_NAME, ArgType.INT32, "0",
                    ""));
            ScenarioInfo.Condition condition = new ScenarioInfo.Condition(
                    1, desc, mSkillID, title, infoLists);
            EventBus.getDefault().post(condition);
        }
    }

    private String getDelayValue(String hours, String minutes, String seconds) {
        if (hours == null || "".equals(hours)) {
            hours = "0";
        }
        if (minutes == null || "".equals(minutes)) {
            minutes = "0";
        }
        if (seconds == null || "".equals(seconds)) {
            seconds = "0";
        }
        int hour = Integer.parseInt(hours);
        int minute = Integer.parseInt(minutes);
        int second = Integer.parseInt(seconds);
        return String.valueOf(hour * 3600 + minute * 60 + second);
    }

    /** 设置三个滚轮时分秒 */
    public void setWheelUnit(String left, String middle, String right) {
        mLeftUnit = left;
        mMiddleUnit = middle;
        mRightUnit = right;
    }

    public void setIsCyclicArr(boolean[] isCyclicArr) {
        this.isCyclicArr = isCyclicArr;
    }

    public interface OnTimePickerListener {
        void primary(int wheel1Index, String whell1Value, int wheel2Index, String whell2Value, int wheel3Index,
                     String whell3Value);
    }

    private void checkDay2Enable() {
        if (mSkillID == SkillsListConstant.SKILL_ID_STATUS_TIME_SOME_DAY_ID) {
            String year = (String) hourList.get(hourWheelView.getCurrentItem());
            String month = (String) minuteList.get(minuteWheelView.getCurrentItem());
            String day = (String) secondList.get(secondWheelView.getCurrentItem());

            try {
                @SuppressLint("SimpleDateFormat")
                DateFormat df = new SimpleDateFormat("yyyyMMdd");
                Date wheelViewDate = df.parse(year + month + day);
                String curDateStr = df.format(new Date());
                Date curDate = df.parse(curDateStr);
                //选的日期是今天之前 为-1
                mVB.includeDialogBottom.btnPrimary.setEnabled(TimeUtils.compare_date(wheelViewDate, curDate) != -1);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 更新天数滚轮的内容（闰年、大小月导致的天数不一样）
     */
    public void updateDays() {
        // 确保 secondList 是天数列表
        if (secondList == null || !(secondList.get(0) instanceof String)) {
            return; // 如果 secondList 未正确初始化，直接返回
        }
        String minuteStr = (String) minuteList.get(minuteWheelView.getCurrentItem());
        int month = Integer.parseInt(minuteStr);  // "01" → 1, "02" → 2, ..., "12" → 12
        String hourStr = (String) hourList.get(hourWheelView.getCurrentItem());
        int year = Integer.parseInt(hourStr);  // "01" → 1, "02" → 2, ..., "12" → 12

        int size = 30;
        if (month == 1 || month == 3 || month == 5 || month == 7 || month == 8 || month == 10 || month == 12) {
            size = 31;
        }
        if (month == 2 && isRunYear(year)) {
            size = 29;
        } else if (month == 2) {
            size = 28;
        }
        int currentIndex = secondWheelView.getCurrentItem();
        List<T> days = new ArrayList<>();
        for (int i = 1; i <= size; i++) {
            days.add((T) String.format(Locale.getDefault(), "%02d", i));
            secondList.clear();
            secondList.addAll(days);
            // 保持当前选中的天数（如果新列表包含当前值，则保持；否则选中第一个）
            if (currentIndex >= days.size()) {
                secondWheelView.setCurrentItem(0); // 如果超出范围，选中第一个
            } else {
                secondWheelView.setCurrentItem(currentIndex); // 否则保持当前选中
            }
        }
    }

    /**
     * 判断闰年
     *
     * @param year
     * @return
     */
    private boolean isRunYear(int year) {
        return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
    }
}
