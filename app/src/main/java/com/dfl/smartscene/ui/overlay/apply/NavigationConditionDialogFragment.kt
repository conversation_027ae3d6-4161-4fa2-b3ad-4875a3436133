package com.dfl.smartscene.ui.overlay.apply

import android.annotation.SuppressLint
import android.content.res.Configuration
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.dfl.android.common.base.BaseDialogFragment
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.common.util.CommonToastUtils
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.commonlib.KeyboardUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.communication.SearchAddressManager
import com.dfl.smartscene.databinding.SceneDialogFragmentNavigationBinding
import com.dfl.smartscene.ui.edit.action.apply.adapter.SearchAddressAdapter2a
import com.dfl.smartscene.ui.overlay.common.singlewheel.SingleWheelDialog
import com.dfl.smartscene.ui.overlay.common.singlewheel.SingleWheelDialog.OnSceneActionListener
import com.dfl.smartscene.ui.overlay.common.singlewheel.SingleWheelType
import com.dfl.smartscene.util.UIUtils
import com.dfl.soacenter.communication.NaviManager
import com.dfl.soacenter.entity.PoiItem
import com.dfl.soacenter.entity.RoutePoi
import com.google.android.material.textfield.TextInputEditText
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo
import com.jeremyliao.liveeventbus.LiveEventBus
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import me.jessyan.autosize.utils.AutoSizeUtils
import org.greenrobot.eventbus.EventBus
import java.lang.ref.WeakReference

/**
 *Created by 钟文祥 on 2025/2/28.
 *Describer: 到达 离开 条件的弹框 (lk2a 新的)
 */
@Suppress("INVISIBLE_SETTER_FROM_DERIVED")
class NavigationConditionDialogFragment : BaseDialogFragment(), View.OnClickListener, View.OnFocusChangeListener {
    private lateinit var viewBinging: SceneDialogFragmentNavigationBinding

    /**对话框标题*/
    private var mTitle: String? = null

    /**条件*/
    private var mCondition: ScenarioInfo.Condition? = null

    /**距离默认100m*/
    private var mDistance: Int = 100

    /**收藏夹adapter*/
    private var mFavoritesAdapter: NavigationFavoritesAdapter? = null

    /**被选中的收藏点 (家与公司等）*/
    private var selectPoint: RoutePoi? = null

    /**收藏点数据列表*/
    private var mFavoritesList: MutableList<RoutePoi>? = mutableListOf()

    /**是否搜索结果列表选择到输入框成功*/
    private var isSearchInputSuccess = false

    /**目的地*/
    private var endPoiInfo: RoutePoi? = null

    /**搜索结果adapter*/
    private var mSearchAdapter: SearchAddressAdapter2a? = null

    /**输入法IME选项*/
    private val imeOptions: Int = EditorInfo.IME_ACTION_SEARCH

    /**是否是选择家与公司模式 ，默认是*/
    private var isSelectHomeCompany = true

    companion object {
        private val SHOW_TAG = Companion::class.java.name
        fun show(
            manager: FragmentManager, title: String?, condition: ScenarioInfo.Condition?,
        ) {
            val dialog = NavigationConditionDialogFragment()
            dialog.mTitle = title
            dialog.mCondition = condition
            condition?.let {
                val distance = it.input.filter { inputArgInfo ->
                    inputArgInfo.name == SkillsListConstant.INPUT_ARG_TRIGGER_LOCATION_DISTANCE
                }[0]?.value?.toInt() ?: 100 //默认100
                dialog.mDistance = distance
            }
            dialog.show(manager, SHOW_TAG)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        viewBinging = SceneDialogFragmentNavigationBinding.inflate(inflater, container, false)
        return viewBinging.root
    }

    override fun getLayoutId(): Int {
        return R.layout.scene_dialog_fragment_navigation
    }

    @SuppressLint("SetTextI18n")
    override fun initView(view: View) {
        viewBinging.tvTitle.text = mTitle
        viewBinging.ivInputEnd.setImageResource(R.drawable.scene_icon_commom_navigation_search)
        viewBinging.txtRadius.text = "${getString(R.string.scene_text_range_radius)}  ${mDistance}m"
        viewBinging.txtRadius.visibility = View.VISIBLE
        viewBinging.viewMore.visibility = View.VISIBLE
        viewBinging.etEditInputEnd.hint = getString(R.string.scene_text_select_points)

        initLiveEventBus()
        initListener()
        addEditChangeListener()
        initRecycleView()
        initViewData()
        adjustButtonSizeForTwoColumns()

    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initLiveEventBus() {
        LiveEventBus.get<Int>(GlobalLiveEventConstants.KEY_GET_DATA_ERROR_CALLBACK).observe(viewLifecycleOwner) {
            CommonToastUtils.show(R.string.scene_toast_common_get_data_error)
        }
        LiveEventBus.get<List<PoiItem>>(GlobalLiveEventConstants.KEY_SEARCH_ADDRESS_RESULT_CALLBACK)
            .observe(viewLifecycleOwner) {
                if (it.isNotEmpty()) {
                    //出现异常数据地址名为空还有返回列表的情况直接进行数据加载前的拦截
                    if (it[0].name.isNullOrEmpty()) {
                        CommonToastUtils.show(R.string.scene_toast_common_get_data_error)
                        return@observe
                    }
                    //筛选过滤掉，经纬度都为0的情况,否则无法正常发起导航
                    val filterList = ArrayList<PoiItem>()
                    for (poiItem in it) {
                        if (poiItem.point?.lat != 0.toDouble() && poiItem.point?.lon != 0.toDouble()) {
                            filterList.add(poiItem)
                        }
                    }
                    val keyWord = viewBinging.etEditInputEnd.text.toString()
                    mSearchAdapter?.data = ArrayList(filterList)
                    mSearchAdapter?.setKeyWord(keyWord)

                    viewBinging.rvSearch.visibility = View.VISIBLE
                    mSearchAdapter?.notifyDataSetChanged()
                    viewBinging.rvSearch.smoothScrollToPosition(0) //恢复初始化位置
                }
            }
    }

    private fun initListener() {
        viewBinging.txtRadius.setOnClickListener(this)
        viewBinging.includeDialogBottom.btnPrimary.setOnClickListener(this)
        viewBinging.includeDialogBottom.btnNormal.setOnClickListener(this)
        viewBinging.ivBack.setOnClickListener(this)
    }


    /**
     * 调整按钮尺寸为424*76
     */
    private fun adjustButtonSizeForTwoColumns() {
        val btnPrimary = viewBinging.includeDialogBottom.btnPrimary
        val btnNormal = viewBinging.includeDialogBottom.btnNormal

        // 设置按钮尺寸为424*76
        val buttonWidth = AutoSizeUtils.dp2px(context, 424f)
        val buttonHeight = AutoSizeUtils.dp2px(context, 76f)

        // 调整主按钮尺寸
        val primaryLayoutParams = btnPrimary.layoutParams
        primaryLayoutParams.width = buttonWidth
        primaryLayoutParams.height = buttonHeight
        btnPrimary.layoutParams = primaryLayoutParams

        // 调整次要按钮尺寸
        val normalLayoutParams = btnNormal.layoutParams
        normalLayoutParams.width = buttonWidth
        normalLayoutParams.height = buttonHeight
        btnNormal.layoutParams = normalLayoutParams
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun addEditChangeListener() {
        //9月ota 定义搜索框有输入内容时，键盘【搜索】按钮高亮可点击，点击后退出键盘（因导航弹窗的特殊性，搜索推荐页即为搜索结果页，因此点击【搜索】后界面无变化）
        UIUtils.setKeyBroadInputMinIfNotEnable(viewBinging.etEditInputEnd, 1)
        //目标点 焦点监听
        viewBinging.etEditInputEnd.onFocusChangeListener = this

        //键盘事件
        viewBinging.etEditInputEnd.imeOptions = imeOptions
        viewBinging.etEditInputMid.imeOptions = imeOptions
        //键盘确定按钮事件
        viewBinging.etEditInputEnd.setOnEditorActionListener(
            MyOnEditorActionListener(
                this@NavigationConditionDialogFragment
            )
        )

        //目标点 文字改变,搜索地址
        viewBinging.etEditInputEnd.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            private var curTime1 = System.currentTimeMillis()
            private val antiShakeTime: Long = 400 //防抖动时间 毫秒

            @SuppressLint("NotifyDataSetChanged")
            override fun afterTextChanged(s: Editable?) {
                if (!isSearchInputSuccess) {
                    val curTime2 = System.currentTimeMillis()

                    val job = lifecycleScope.launch(Dispatchers.IO) {
                        delay(antiShakeTime)
                        withContext(Dispatchers.Main) {
                            endPoiInfo = null
                            updateTvConfirm4Search()
                            val content = viewBinging.etEditInputEnd.text.toString()
                            if (content.isNotEmpty()) {
                                SearchAddressManager.sendKeyWordToNavi(content)
                            } else {
                                viewBinging.rvSearch.visibility = View.GONE
                            }
                        }
                    }
                    if (curTime2 - curTime1 < antiShakeTime) {
                        job.cancel()
                    }
                    curTime1 = System.currentTimeMillis()
                } else {
                    isSearchInputSuccess = false
                }
            }
        })
    }

    private fun initRecycleView() {
        UIUtils.initRecyclerView(
            activity, viewBinging.rvFavorites, GridLayoutManager(activity, 2), false
        )
        mFavoritesAdapter = NavigationFavoritesAdapter()

        mFavoritesAdapter?.setOnItemClickListener { adapter, view, position ->
            if (mFavoritesList?.get(position)?.isEnable == false) {
                return@setOnItemClickListener
            }

            //新逻辑 点击列表item，填写进输入框
            isSelectHomeCompany = false
            viewBinging.txtFavorites.visibility = View.GONE
            viewBinging.rvFavorites.visibility = View.GONE

            //---固定整体  start--
            isSearchInputSuccess = true
            endPoiInfo = mFavoritesList?.get(position)
            viewBinging.etEditInputEnd.setText(endPoiInfo?.name)
            viewBinging.etEditInputEnd.clearFocus()
            updateTvConfirm()
            dialog?.window?.let {
                KeyboardUtils.hideSoftInput(it)
            }
            //---固定整体  end--

        }
        viewBinging.rvFavorites.adapter = mFavoritesAdapter
        mFavoritesAdapter?.setEmptyView(R.layout.scene_dialog_fragment_navigation_empty) //放在setadaper后，当0或null显示


        UIUtils.initRecyclerView(
            activity, viewBinging.rvSearch, LinearLayoutManager(activity), false
        )
        mSearchAdapter = SearchAddressAdapter2a()
        viewBinging.rvSearch.adapter = mSearchAdapter
        //滑动事件
        viewBinging.rvSearch.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                dialog?.window?.let {
                    KeyboardUtils.hideSoftInput(it)
                }
            }
        })

        //列表点击事件
        mSearchAdapter?.setOnItemClickListener { _, _, position ->
            mSearchAdapter?.data?.let {
                viewBinging.rvSearch.visibility = View.GONE

                //---固定整体  start--
                isSearchInputSuccess = true
                endPoiInfo = RoutePoi(
                    it[position].name,
                    it[position].addr,
                    it[position].point?.lon ?: 0.0,
                    it[position].point?.lat ?: 0.0,
                    it[position].typeCode,
                    it[position].adCode,
                    it[position].pid
                )
                viewBinging.etEditInputEnd.setText(endPoiInfo?.name)
                viewBinging.etEditInputEnd.clearFocus()
                //                viewBinging.etEditInputEnd.setSelection(viewBinging.etEditInputEnd.length())
                updateTvConfirm()
                dialog?.window?.let {
                    KeyboardUtils.hideSoftInput(it)
                }
                //---固定整体  end--
            }
        }
    }

    private fun initViewData() {
        mFavoritesAdapter?.setList(mFavoritesList)
        updateTvConfirm()

        getFavorites()
    }

    private fun getFavorites() {
        //获取服务
        lifecycleScope.launch(Dispatchers.IO) {
            //同一soa方法需要串行
            val resHomeListBean = async {
                NaviManager.getFavoriteList(1)
            }.await()
            val resCompanyListBean = async {
                NaviManager.getFavoriteList(2)
            }.await()
            val resListBean = async {
                NaviManager.getFavoriteList(0) //0 普通点 3 全部
            }.await()

            if (((resHomeListBean?.isSuccess() == false || resHomeListBean == null) || //
                        (resCompanyListBean?.isSuccess() == false || resCompanyListBean == null) || //
                        (resListBean?.isSuccess() == false) || resListBean == null)
            ) {
                CommonToastUtils.show(R.string.scene_toast_common_get_data_error)
                return@launch
            }

            mFavoritesList?.clear()
            var isHaveHome = true
            if (resHomeListBean?.isSuccess() == true && resHomeListBean?.protocolFavPoiInfos?.size != 0) {
                val homeBean = resHomeListBean.protocolFavPoiInfos?.get(0)
                mFavoritesList?.add(
                    RoutePoi(
                        getString(R.string.scene_text_action_home), homeBean?.favoriteName, homeBean?.lon ?: 0.0,
                        homeBean?.lat ?: 0.0, homeBean?.itemId, homeBean?.itemId, homeBean?.itemId,
                    )
                )
            } else {
                mFavoritesList?.add(
                    RoutePoi(
                        getString(R.string.scene_text_action_home),
                        getString(R.string.scene_text_location_favorites_dialog_empty_view2),
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        isEnable = false
                    )
                )
                isHaveHome = false
            }
            var isHaveCompany = true
            if (resCompanyListBean?.isSuccess() == true && resCompanyListBean?.protocolFavPoiInfos?.size != 0) {
                val companyListBean = resCompanyListBean.protocolFavPoiInfos?.get(0)
                mFavoritesList?.add(
                    RoutePoi(
                        getString(R.string.scene_text_action_back_company),
                        companyListBean?.favoriteName,
                        companyListBean?.lon ?: 0.0,
                        companyListBean?.lat ?: 0.0,
                        companyListBean?.itemId,
                        companyListBean?.itemId,
                        companyListBean?.itemId,
                    )
                )
            } else {
                mFavoritesList?.add(
                    RoutePoi(
                        getString(R.string.scene_text_action_back_company),
                        getString(R.string.scene_text_location_favorites_dialog_empty_view2),
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        isEnable = false
                    )
                )
                isHaveCompany = false
            }
            if (resListBean?.isSuccess() == true && resListBean?.protocolFavPoiInfos?.size != 0) {
                resListBean.protocolFavPoiInfos?.forEachIndexed { index, favoriteItemInfo ->
                    mFavoritesList?.add(
                        RoutePoi(
                            favoriteItemInfo.favoriteName,
                            favoriteItemInfo.favoriteAddress,
                            favoriteItemInfo.lon ?: 0.0,
                            favoriteItemInfo.lat ?: 0.0,
                            favoriteItemInfo.itemId,
                            favoriteItemInfo.itemId,
                            favoriteItemInfo.itemId
                        )
                    )
                }
            }
            if (mFavoritesList?.size == 2) {
                if (!isHaveHome && !isHaveCompany) {
                    mFavoritesList?.clear()
                }
            }

            withContext(Dispatchers.Main) {
                //改为默认不选中
//                val currentIndex = initCurrentCheckView()
//                mFavoritesAdapter?.mSelectedIndex = currentIndex
//                if (currentIndex >= 0) {
//                    selectPoint = mFavoritesList?.get(currentIndex)
//                }
                viewBinging.txtFavorites.visibility = if (mFavoritesList!!.size > 0) View.VISIBLE else View.GONE
                mFavoritesAdapter?.setList(mFavoritesList)
                updateTvConfirm()
            }
        }
    }

    //根据传进来的数据 初始化 哪个被选择
    private fun initCurrentCheckView(): Int {
        mCondition?.let {
            val lat = it.input.filter { inputArgInfo ->
                inputArgInfo.name == SkillsListConstant.INPUT_ARG_TRIGGER_LOCATION_LATITUDE
            }[0]?.value?.toDouble() ?: 0.0
            val lon = it.input.filter { inputArgInfo ->
                inputArgInfo.name == SkillsListConstant.INPUT_ARG_TRIGGER_LOCATION_LONGITUDE
            }[0]?.value?.toDouble() ?: 0.0

            mFavoritesList?.forEachIndexed { index, routePoi ->
                if (routePoi.isEnable == true) {
                    if (routePoi.lat == lat && routePoi.lon == lon) {
                        return index
                    }
                }
            }
        }

        run breaking@{
            mFavoritesList?.forEachIndexed { index, routePoi ->
                if (routePoi.isEnable == true) {
                    return index
                    //                    return@breaking //退出循环
                }
            }
        }
        return -1
    }

    /**改变确定按钮的形态——总*/
    private fun updateTvConfirm() {
        if (isSelectHomeCompany) {
            viewBinging.ivBack.visibility = View.GONE
            viewBinging.includeDialogBottom.btnPrimary.isEnabled = false
        } else {
            viewBinging.ivBack.visibility = View.VISIBLE
            updateTvConfirm4Search()
        }
    }

    /**改变确定按钮的形态*/
    private fun updateTvConfirm4Search() {
        viewBinging.includeDialogBottom.btnPrimary.isEnabled = endPoiInfo != null
    }

    /**键盘确定按钮事件*/
    private class MyOnEditorActionListener(dialogFragment: NavigationConditionDialogFragment) :
        TextView.OnEditorActionListener {
        private var reference: WeakReference<NavigationConditionDialogFragment> =
            WeakReference<NavigationConditionDialogFragment>(dialogFragment)

        //键盘确定事件
        override fun onEditorAction(v: TextView?, actionId: Int, event: KeyEvent?): Boolean {
            reference.get()?.let {
                if (actionId == it.imeOptions) {
//                    if (it.isAllow || (event?.action == KeyEvent.ACTION_DOWN && event.keyCode == KeyEvent.KEYCODE_ENTER)) {
//                        it.handleConfirm()
//                    }
                }
            }
            return true
        }
    }

    /**提交 -总*/
    private fun handleConfirm() {
        val lat = if (isSelectHomeCompany) selectPoint?.lat else endPoiInfo?.lat
        val lon = if (isSelectHomeCompany) selectPoint?.lon else endPoiInfo?.lon
        val name = if (isSelectHomeCompany) selectPoint?.name else endPoiInfo?.name

        mCondition?.let {
            val inputArgList = mutableListOf<InputArgInfo>(
                InputArgInfo(
                    SkillsListConstant.INPUT_ARG_TRIGGER_LOCATION_LATITUDE, ArgType.DOUBLE, lat.toString()
                ),
                InputArgInfo(
                    SkillsListConstant.INPUT_ARG_TRIGGER_LOCATION_LONGITUDE, ArgType.DOUBLE, lon.toString()
                ),
                InputArgInfo(
                    SkillsListConstant.INPUT_ARG_TRIGGER_LOCATION_DISTANCE, ArgType.INT32, mDistance.toString()
                ),
            )
            it.input = inputArgList
            it.desc = name.plus(resources.getString(R.string.scene_text_range_radius)).plus(mDistance)
                .plus(getString(R.string.scene_text_condition_drive_distance_mi))
            it.category = mTitle
            EventBus.getDefault().post(it)
        }
        dialog?.window?.let {
            KeyboardUtils.hideSoftInput(it)
        }
        dismissWithAnimate()
    }


    //回调 按钮事件 setOnClickListener
    @SuppressLint("NotifyDataSetChanged")
    override fun onClick(v: View?) {
        if (!DebouncingUtils.isValid(v!!)) return
        when (v.id) {
            R.id.txt_radius -> { //半径
                showNewRangeWheelDialog()
            }

            // 在 onClick 方法的 R.id.iv_back 分支中
            R.id.iv_back -> { //返回
            //搜索模式 ->选家模式
            isSelectHomeCompany = true
            viewBinging.txtFavorites.visibility = if (mFavoritesList!!.size > 0) View.VISIBLE else View.GONE
            viewBinging.rvFavorites.visibility = View.VISIBLE
            
            // 恢复隐藏的UI元素
            viewBinging.tvTitle.visibility = View.VISIBLE
            viewBinging.txtRadius.visibility = View.VISIBLE
            viewBinging.viewMore.visibility = View.VISIBLE
            viewBinging.ivInputEnd.visibility = View.VISIBLE
            
            // 恢复cl_end_address的原始约束和布局参数
            val clEndAddressParams = viewBinging.clEndAddress.layoutParams as androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
            clEndAddressParams.topToTop = viewBinging.tvTitle.id // 修复：应该是相对于tv_title的顶部，不是底部
            clEndAddressParams.topToBottom = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
            clEndAddressParams.width = AutoSizeUtils.dp2px(requireContext(), 164f)
            clEndAddressParams.height = AutoSizeUtils.dp2px(requireContext(), 68f)
            clEndAddressParams.marginStart = AutoSizeUtils.dp2px(requireContext(), 16f)
            clEndAddressParams.topMargin = AutoSizeUtils.dp2px(requireContext(), 16f) // 恢复原始顶部边距16
            viewBinging.clEndAddress.layoutParams = clEndAddressParams
            
            // 恢复ll_end_address的原始布局参数
            val llEndAddressParams = viewBinging.llEndAddress.layoutParams as androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
            llEndAddressParams.width = AutoSizeUtils.dp2px(requireContext(), 164f)
            llEndAddressParams.height = AutoSizeUtils.dp2px(requireContext(), 68f)
            viewBinging.llEndAddress.layoutParams = llEndAddressParams
            
            // 恢复et_edit_input_end的原始布局参数
            val etEditInputEndParams = viewBinging.etEditInputEnd.layoutParams
            etEditInputEndParams.width = ViewGroup.LayoutParams.WRAP_CONTENT
            etEditInputEndParams.height = AutoSizeUtils.dp2px(requireContext(), 68f)
            viewBinging.etEditInputEnd.layoutParams = etEditInputEndParams
            viewBinging.etEditInputEnd.setPadding(
                AutoSizeUtils.dp2px(requireContext(), 16f),
                viewBinging.etEditInputEnd.paddingTop,
                viewBinging.etEditInputEnd.paddingEnd,
                viewBinging.etEditInputEnd.paddingBottom
            )
        
            mSearchAdapter?.data = ArrayList()
            mSearchAdapter?.notifyDataSetChanged()
            viewBinging.rvSearch.visibility = View.GONE

            viewBinging.etEditInputEnd.hint = getString(R.string.scene_text_select_points)
            viewBinging.etEditInputEnd.setHintTextColor(ContextCompat.getColor(requireContext(), R.color.scene_color_text_1))
        
            //---固定整体  start--
            isSearchInputSuccess = true
            endPoiInfo = null
            viewBinging.etEditInputEnd.setText("") //setText 之前需要  isSearchInputSuccess = true
            viewBinging.etEditInputEnd.clearFocus()
            updateTvConfirm()
            dialog?.window?.let {
                KeyboardUtils.hideSoftInput(it)
            }
            //---固定整体  end--
        }

            R.id.btn_normal -> { //取消
                dismissWithAnimate()
            }

            R.id.btn_primary -> { //确定
                handleConfirm()
            }
        }
    }


    //回调 目的地输入框焦点监听  onFocusChangeListener
    override fun onFocusChange(v: View?, hasFocus: Boolean) {
        if (hasFocus) { //获取焦点了
            isSelectHomeCompany = false
            viewBinging.txtFavorites.visibility = View.GONE
            viewBinging.rvFavorites.visibility = View.GONE
            
            // 隐藏指定的UI元素
            viewBinging.tvTitle.visibility = View.GONE
            viewBinging.txtRadius.visibility = View.GONE
            viewBinging.viewMore.visibility = View.GONE
            viewBinging.ivInputEnd.visibility = View.GONE
            
            // 调整cl_end_address的约束，改为相对于父容器顶部
            val clEndAddressParams = viewBinging.clEndAddress.layoutParams as androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
            clEndAddressParams.topToTop = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
            clEndAddressParams.topToBottom = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
            clEndAddressParams.width = AutoSizeUtils.dp2px(requireContext(), 1028f)
            clEndAddressParams.height = AutoSizeUtils.dp2px(requireContext(), 76f)
            clEndAddressParams.marginStart = AutoSizeUtils.dp2px(requireContext(), 124f)
            clEndAddressParams.topMargin = AutoSizeUtils.dp2px(requireContext(), 14f) // 修改：设置topMargin为14
            viewBinging.clEndAddress.layoutParams = clEndAddressParams
            
            // 调整ll_end_address的布局参数
            val llEndAddressParams = viewBinging.llEndAddress.layoutParams as androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
            llEndAddressParams.width = AutoSizeUtils.dp2px(requireContext(), 1028f)
            llEndAddressParams.height = AutoSizeUtils.dp2px(requireContext(), 76f)
            viewBinging.llEndAddress.layoutParams = llEndAddressParams
            
            // 调整et_edit_input_end的布局参数
            val etEditInputEndParams = viewBinging.etEditInputEnd.layoutParams
            etEditInputEndParams.width = AutoSizeUtils.dp2px(requireContext(), 1028f)
            etEditInputEndParams.height = AutoSizeUtils.dp2px(requireContext(), 76f)
            viewBinging.etEditInputEnd.layoutParams = etEditInputEndParams
            viewBinging.etEditInputEnd.setPadding(
                AutoSizeUtils.dp2px(requireContext(), 36f),
                viewBinging.etEditInputEnd.paddingTop,
                AutoSizeUtils.dp2px(requireContext(), 22f),
                viewBinging.etEditInputEnd.paddingBottom
            )
            viewBinging.etEditInputEnd.hint = getString(R.string.scene_edit_input_hint_address)
            viewBinging.etEditInputEnd.setHintTextColor(ContextCompat.getColor(requireContext(), R.color.scene_color_text_3))
            updateTvConfirm()
        }
    }

    private fun showNewRangeWheelDialog() {
        val list = initDriveTimeData()
        val dialog = SingleWheelDialog<ScenarioInfo.Condition, Int>(
            requireContext(),
            list,
            getString(R.string.scene_text_range_radius),
            null,
            SingleWheelType.SINGLE_WHEEL_INPUT_ARG
        )
        //        dialog.setupPreCondition(mCondition)
        var index = 0
        run breaking@{
            list.forEachIndexed { index1, s ->
                if (mDistance == s.toInt()) {
                    index = index1
                    return@breaking
                }
            }
        }
        dialog.isLevel4OrAbove = true
        dialog.setWheelDefaultPosition(index)
        dialog.setUnit(resources.getString(R.string.scene_text_condition_drive_distance_mi))
        dialog.isLoop = false
        dialog.setShowLeftDesc(false, "")
        dialog.setOnSceneActionListener(object : OnSceneActionListener {
            override fun selectAction(sequence: ScenarioInfo.Sequence?) {
            }

            override fun cancel() {
            }

            @SuppressLint("SetTextI18n")
            override fun selectValue(value: String?, index: Int) {
                mDistance = value?.toInt()!!
                viewBinging.txtRadius.text = "${getString(R.string.scene_text_range_radius)}  ${mDistance}m"
            }
        })
        dialog.showWithAnimate()
    }

    private fun initDriveTimeData(): ArrayList<String> {
        //        CustomSeekBarBean(100, 5000, 100, 100, "", "m", -1)
        val list = ArrayList<String>()
        var i = 0
        while (100 + i * 100 <= 5000) {
            list.add((100 + i * 100).toString())
            i++
        }

        return list
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        mFavoritesAdapter?.notifyDataSetChanged()
    }

}