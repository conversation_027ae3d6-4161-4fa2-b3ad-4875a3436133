package com.dfl.smartscene.ui.overlay.apply

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnClickListener
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.dfl.android.common.base.BaseDialogFragment
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.edit.CustomSeekBarBean
import com.dfl.smartscene.databinding.SceneDialogFragmentLocationDistanceBinding
import com.dfl.smartscene.util.UIUtils
import com.dfl.soacenter.entity.RoutePoi
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo
import org.greenrobot.eventbus.EventBus
import java.lang.ref.WeakReference

/**
 *Created by 钟文祥 on 2023/12/15.
 *Describer: 位置+距离  选择框 。注意：不设传入默认值，默认不选择
 */
@Deprecated("2a被废弃,使用 NavigationConditionDialogFragment")
class LocationDistanceDialogFragment : BaseDialogFragment() {
    private var mBinding: SceneDialogFragmentLocationDistanceBinding? = null
    private val viewBinding get() = mBinding!!

    private var mTitle: String? = null
    private var mDesc: String? = null
    private var mCondition: ScenarioInfo.Condition? = null  //条件
    private var mCustomSeekBarBean: CustomSeekBarBean? = null //滑条实体

    /**终点: RoutePoiBean*/
    private var mEndPoint: RoutePoi? = null


    companion object {
        fun showFragment(
            manager: FragmentManager,
            title: String?,
            desc: String?,
            condition: ScenarioInfo.Condition?,
            customSeekBarBean: CustomSeekBarBean?
        ) {
            val dialog = LocationDistanceDialogFragment()
            dialog.mTitle = title
            dialog.mDesc = desc
            dialog.mCondition = condition
            dialog.mCustomSeekBarBean = customSeekBarBean
            dialog.show(manager, LocationDistanceDialogFragment.javaClass.name)
        }
    }


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        mBinding = SceneDialogFragmentLocationDistanceBinding.inflate(inflater, container, false)
        return viewBinding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        mBinding = null
    }

    override fun getLayoutId(): Int {
        return R.layout.scene_dialog_fragment_location_distance
    }

    override fun initView(view: View) {
        mTitle?.let { viewBinding.tvTitle.text = mTitle }
        mDesc?.let { viewBinding.tvDesc.text = mDesc }
        viewBinding.ctvGoCollectTxt.setOnClickListener(MyOnClickListener(this))
        viewBinding.ctvGoSomeWhereTxt.setOnClickListener(MyOnClickListener(this))
        viewBinding.includeDialogBottom.btnPrimary.setOnClickListener(MyOnClickListener(this))
        viewBinding.includeDialogBottom.btnNormal.setOnClickListener(MyOnClickListener(this))

        updateTvConfirm()
        initSbDistance()
        setCanceledOnTouchOutside(true)
        setOnTouchListener()
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setOnTouchListener() {
        viewBinding.clView.setOnTouchListener { v, event ->
            event?.pointerCount == 1  //单指事件  true拦截事件  false不拦截事件
        }
    }

    private fun updateTvConfirm() {
        viewBinding.includeDialogBottom.btnPrimary.isEnabled =
            viewBinding.ctvGoCollectTxt.isChecked || viewBinding.ctvGoSomeWhereTxt.isChecked
    }

    //初始化滑条
    private fun initSbDistance() {
        UIUtils.initSeekBar(context, viewBinding.sbDistance, mCustomSeekBarBean, 660)
    }


    class MyOnClickListener(dialogFragment: LocationDistanceDialogFragment) : OnClickListener {
        private var reference: WeakReference<LocationDistanceDialogFragment> =
            WeakReference<LocationDistanceDialogFragment>(dialogFragment)

        override fun onClick(v: View?) {
            if (!DebouncingUtils.isValid(v!!)) return
            reference.get()?.let {
                when (v.id) {
                    R.id.ctv_go_collect_txt -> { //收藏夹
                        it.viewBinding.ctvGoSomeWhereTxt.isEnabled = false
                        LocationFavoritesDialogFragment.showFragment(it.childFragmentManager,
                            object : LocationFavoritesDialogFragment.OnInputListener {
                                override fun onSelectPoint(endPoint: RoutePoi?) {

                                    it.mEndPoint = endPoint
                                    it.viewBinding.ctvGoCollectTxt.text = endPoint?.name
                                    it.viewBinding.ctvGoCollectTxt.isChecked = true
                                    it.viewBinding.ctvGoSomeWhereTxt.isChecked = false
                                    it.updateTvConfirm()
                                    it.viewBinding.ctvGoSomeWhereTxt.isEnabled = true
                                }

                                override fun cancel() {
                                    it.viewBinding.ctvGoSomeWhereTxt.isEnabled = true
                                }
                            })
                    }

                    R.id.ctv_go_some_where_txt -> { //选点
                        it.viewBinding.ctvGoCollectTxt.isEnabled = false
                        it.openAddressInputDialog4Go()
                    }

                    R.id.btn_primary -> { //确认
                        it.confirm()
                    }

                    R.id.btn_normal -> { //取消
                        it.dismissWithAnimate()
                    }
                }
            }
        }
    }


    //选点对话框
    private fun openAddressInputDialog4Go() {
        val searchAddressDialogFragment = SearchAddressDialogFragment.getInstance()
        searchAddressDialogFragment.setMidAddressDisShow()
        searchAddressDialogFragment.title = getString(R.string.scene_text_select_points)
        searchAddressDialogFragment.onInputListener = object : SearchAddressDialogFragment.OnInputListener {
            override fun onSelectPoint(endPoint: RoutePoi) {
                mEndPoint = endPoint
                viewBinding.ctvGoSomeWhereTxt.text = endPoint.name
                viewBinding.ctvGoSomeWhereTxt.isChecked = true
                viewBinding.ctvGoCollectTxt.isChecked = false
                updateTvConfirm()
                viewBinding.ctvGoCollectTxt.isEnabled = true
            }

            override fun onInput(jsonInputData: String, isAddWayside: Boolean?) {

            }

            override fun cancel() {
                viewBinding.ctvGoCollectTxt.isEnabled = true
            }
        }
        searchAddressDialogFragment.show(
            childFragmentManager, NavigationActionSelectDialogFragment.TAG_DIALOG_ADDRESS
        )
    }

    private fun confirm() {
        val distance: Int =
            if (mCustomSeekBarBean?.multiple == 0) viewBinding.sbDistance.getProgress() else viewBinding.sbDistance.getProgress() * mCustomSeekBarBean?.multiple!!

        mCondition?.let {
            val inputArgList = mutableListOf<InputArgInfo>(
                InputArgInfo(
                    SkillsListConstant.INPUT_ARG_TRIGGER_LOCATION_LATITUDE, ArgType.DOUBLE, mEndPoint?.lat.toString()
                ),
                InputArgInfo(
                    SkillsListConstant.INPUT_ARG_TRIGGER_LOCATION_LONGITUDE, ArgType.DOUBLE, mEndPoint?.lon.toString()
                ),
                InputArgInfo(
                    SkillsListConstant.INPUT_ARG_TRIGGER_LOCATION_DISTANCE, ArgType.INT32, distance.toString()
                ),
            )
            it.input = inputArgList
            it.desc = mEndPoint?.name.plus(resources.getString(R.string.scene_text_range_radius)).plus(distance)
                .plus(mCustomSeekBarBean?.unit)
            it.category = mTitle
            EventBus.getDefault().post(it)
        }
    }

}