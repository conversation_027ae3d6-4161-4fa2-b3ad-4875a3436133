package com.dfl.smartscene.ui.edit.action.air

import android.annotation.SuppressLint
import android.view.View
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.AirConditionerType
import com.dfl.smartscene.databinding.SceneFragmentEditActionBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.common.checklist.CheckListType
import com.dfl.smartscene.ui.overlay.common.checklist.RadioListDialogFragment
import com.dfl.smartscene.ui.overlay.common.doublewheel.DoubleWheelDialogFragment
import com.dfl.smartscene.ui.overlay.common.doublewheel.DoubleWheelType
import com.dfl.smartscene.ui.overlay.common.singlewheel.SingleWheelDialog
import com.dfl.smartscene.ui.overlay.common.singlewheel.SingleWheelType
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :动作->空调页面
 * version: 1.0
 */
class AirActionFragment(
    private val sequence: ScenarioInfo.Sequence?
) : MVVMAdapterFragment<AirActionAdapter, SceneFragmentEditActionBinding, AirActionViewModel>() {

    private val TAG: String = GlobalConstant.GLOBAL_TAG.plus("AirConditionerFragment")
    private var mAdapter: AirActionAdapter? = null

    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_action
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    override fun getViewModelClass(): Class<AirActionViewModel> {
        return AirActionViewModel::class.java
    }

    override fun initView(view: View?) {
        super.initView(view)
        mAdapter = AirActionAdapter()

        mAdapter?.setOnItemClickListener { _, v, position ->
            handleItemClick(v, position)
        }
        mViewDataBinding.rvAction.adapter = mAdapter
    }

    private fun handleItemClick(view: View, position: Int) {
        if (DebouncingUtils.isValid(view, SceneEditManager.mViewClickDuration)) {
            mAdapter?.data?.let {
                if (it[position].actionType == null) return@let
                when (it[position].actionType) {
                    AirConditionerType.AIR_CONDITIONER_TEMPERATURE, AirConditionerType.AIR_CONDITIONER_TEMPERATURE_TWO -> { //空调温度
                        showTempWheelDialog(it[position].actionType)
                    }

                    AirConditionerType.AIR_CONDITIONER_VOLUME -> { //空调风量
                        showAirConditionerVolume(it[position].actionType)
                    }

                    else -> {
                        showRadioListDialog(it[position].content, it[position].actionType!!)
                    }
                }
            }
        }
    }
    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        super.initData()
        mViewModel.airConditionerLiveData.observe(viewLifecycleOwner) {
            mAdapter?.data = it
            mAdapter?.notifyDataSetChanged()
            
            // 修改这里的逻辑
            var position = -1
            if (sequence != null) {
                // 优先使用skillId精确匹配
                position = mAdapter?.findPositionBySkillId(sequence!!.action.skillId) ?: -1
            }
            if (position == -1) {
                // 如果skillId匹配失败，使用原来的方法
                position = getSimulateClickPosition(null, sequence)
            }
            
            if (position != -1) {
                view?.let { it1 -> handleItemClick(it1, position) }
            }
        }
        mViewModel.initData(resources)
    }

    override fun getAdapter(): AirActionAdapter? {
        return mAdapter
    }

    private fun showRadioListDialog(title: String, type: AirConditionerType) {
        val dialog: RadioListDialogFragment<ScenarioInfo.Sequence, Int>
        when (type) {
            AirConditionerType.AUTO -> {
                //单选,auto等不需要判断inputArg,只需要选中对应sequence
                dialog = RadioListDialogFragment(
                    title,
                    CheckListType.DEFAULT_CHECK_MODE,
                    mViewModel.getListData(resources, type, title),
                    mViewModel.getAirConditionerAction(type),
                )
            }
            else -> {
                //一个sequence根据inputArg选项
                dialog = RadioListDialogFragment(
                    title,
                    CheckListType.INPUT_CHECK_MODE,
                    mViewModel.getListData(resources, type, title),
                    mViewModel.getAirConditionerAction(type),
                )
                dialog.inputArgValueList = mViewModel.getInputArgIntValue(type)
            }
        }

        dialog.preSetSequence = sequence
        dialog.show(parentFragmentManager, "showRadioListDialog")
    }

    private fun showAirConditionerVolume(type: AirConditionerType?) {
        type?.let {
            val dialog = SingleWheelDialog<ScenarioInfo.Sequence, Int>(
                context,
                mViewModel.initAirConditionerVolumeWheelData(),
                resources.getString(R.string.scene_text_common_action_air_conditioner_air_volume),
                mViewModel.getAirConditionerAction(type),
                SingleWheelType.SINGLE_WHEEL_INPUT_ARG
            )
            dialog.setUnit(getString(R.string.scene_text_indicator_unit_air_volume))
            dialog.setupPreAction(sequence)
            dialog.setWheelDefaultPosition(1)
            dialog.setInputArgValueList(mViewModel.getAirConditionerVolumeArgInfoList())
            dialog.showWithAnimate()
        }
    }


    private fun showTempWheelDialog(type: AirConditionerType?) {
        type?.let {
            if (it == AirConditionerType.AIR_CONDITIONER_TEMPERATURE_TWO) {
                //显示双区空调
                val dialog = DoubleWheelDialogFragment(
                    resources.getString(R.string.scene_text_common_action_air_conditioner_temperature),
                    mViewModel.getRbMap(resources, it),
                    mViewModel.getWheelMap(it),
                    mViewModel.getAirConditionerAction(type),
                    DoubleWheelType.DOUBLE_SKILL
                )
                dialog.setupPreAction(sequence)
                dialog.setUnit("", getString(R.string.scene_text_common_temperature_unit))
                dialog.setRightWheelLoop(false)
                dialog.setWheelDefaultPosition(0, 8)
                dialog.show(parentFragmentManager, "showTempWheelDialog")
            } else {
                val dialog = SingleWheelDialog<ScenarioInfo.Sequence, Double>(
                    context,
                    mViewModel.initTemperatureWheelData(),
                    resources.getString(R.string.scene_text_common_action_air_conditioner_temperature),
                    mViewModel.getAirConditionerAction(
                        type
                    ),
                    SingleWheelType.SINGLE_WHEEL_INPUT_ARG

                )
                dialog.setUnit(getString(R.string.scene_text_common_temperature_unit))
                dialog.isLoop = false
                dialog.setupPreAction(sequence)
                dialog.setWheelDefaultPosition(8)
                dialog.setInputArgValueList(mViewModel.getInputArgInfoList())
                dialog.showWithAnimate()
            }
        }
    }

}