package com.dfl.smartscene.ui.overlay.common;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dfl.android.common.base.BaseDialog;
import com.dfl.android.common.util.ObjectUtils;
import com.dfl.android.commonlib.log.CommonLogUtils;
import com.dfl.smartscene.R;
import com.dfl.smartscene.bean.condition.TriggerDrive;
import com.dfl.smartscene.bean.edit.CustomSeekBarBean;
import com.dfl.smartscene.databinding.SceneDialogLocationSettingBinding;
import com.dfl.smartscene.util.UIUtils;
import com.dfl.smartscene.widget.seekbar.CustomSeekBar;
import com.iauto.scenarioadapter.ScenarioInfo;

import org.greenrobot.eventbus.EventBus;

import java.util.Arrays;
import java.util.List;

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/13
 * desc :位置弹窗 两个条件+滑动条;2A被合并使用DoubleWheelDialog
 * version: 1.0
 * update: 钟文祥
 */
@Deprecated
public class RadioProgressDialog extends BaseDialog implements View.OnClickListener {
    private final String mTitle;
    private SceneDialogLocationSettingBinding mVB;
    private String mLeftDescDefault = null;
    private int mMultiple = 1;//颗粒度
    private int minNum = -1;
    private int maxNum = -1;
    private String mUnit;
    private int mDefaultUnit;
    private boolean mIsMoreThan = true;
    private CustomSeekBar mCustomSeekBar;
    private int mIsSeekBarTopTextType = -1;
    private int mSeekBarIcon = -1;
    private List<ScenarioInfo.Condition> mConditionList;
    private ScenarioInfo.Condition preSetCondition;

    public RadioProgressDialog(@NonNull Context context,
            String title,
            TriggerDrive triggerDrive,
            List<ScenarioInfo.Condition> list) {
        super(context);
        this.mTitle = title;
        this.mConditionList = list;
    }

    /**
     * 不局限于驾驶触发条件的弹窗
     */
    public RadioProgressDialog(@NonNull Context context,
            String title,
            List<ScenarioInfo.Condition> list, String leftDescDefault) {
        super(context);
        this.mTitle = title;
        this.mConditionList = list;
        this.mLeftDescDefault = leftDescDefault;
    }

    /**
     * 设置调节条icon
     */
    public void setSeekBarIcon(@DrawableRes int icon) {
        this.mSeekBarIcon = icon;
    }

    /**
     * 设置之前的条件Condition数据
     */
    public void setPreSetCondition(@Nullable ScenarioInfo.Condition condition) {
        this.preSetCondition = condition;
    }

    /**
     * 设置是大于还是小于
     *
     * @param isMoreThan true设置大于
     */
    public void setMoreThanState(boolean isMoreThan) {
        this.mIsMoreThan = isMoreThan;
    }

    /**
     * 设置单位
     *
     * @param unit 单位
     */
    public void setUnit(String unit) {
        this.mUnit = unit;
    }

    /**
     * 设置颗粒度
     */
    public void setSeekBarMultiple(int multiple) {
        this.mMultiple = multiple;
    }

    /**
     * 设置最小值
     */
    public void setMinNum(int minNum) {
        this.minNum = minNum;
    }

    /**
     * 设置最大值
     */
    public void setMaxNum(int maxNum) {
        this.maxNum = maxNum;
    }

    /**
     * 设置调节条上方字体
     *
     * @param type -1默认 2有左边描述词
     */
    public void setSeekBarTopTextType(int type) {
        this.mIsSeekBarTopTextType = type;
    }

    /**
     * 设置默认值
     */
    public void setDefaultUnit(int defaultUnit) {
        this.mDefaultUnit = defaultUnit;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mVB = SceneDialogLocationSettingBinding.inflate(getLayoutInflater());
        setContentView(mVB.getRoot());
        initView();
        if (preSetCondition != null) {
            setupInitData();
        }
        CommonLogUtils.logD("LocationDialog", "onCreate");
        setCanceledOnTouchOutside(true);
    }

    @Override
    protected void onStart() {
        super.onStart();
        CommonLogUtils.logD("LocationDialog", "onStart");
    }

    @Override
    protected void onStop() {
        super.onStop();
        CommonLogUtils.logD("LocationDialog", "onStop");
    }


    private void setupInitData() {
        int index = ObjectUtils.findPreIndex(preSetCondition, mConditionList);
        if (index != -1) {
            String v = preSetCondition.getInput().get(0).getValue();
            int value = mMultiple == 0 ? Integer.parseInt(v) : Integer.parseInt(v) / mMultiple;
            if (mConditionList.size() == 2) {
                mVB.tabSwitchViewCondition.setSelectedIndex(index);
            }
            mCustomSeekBar.setProgress(value);
        }
    }

    private void initView() {
        TextView mTitleTv = findViewById(R.id.tv_location_title);
        mTitleTv.setText(mTitle);
        if (mIsSeekBarTopTextType == 2) {
            mVB.tabSwitchViewCondition.setVisibility(View.GONE);
        } else {
            mVB.tabSwitchViewCondition.generateViews(Arrays.asList(getContext().getString(R.string.scene_text_common_more_than), getContext().getString(R.string.scene_text_common_less_than)));
            mVB.tabSwitchViewCondition.setSelectedIndex(mIsMoreThan ? 0 : 1);
        }

        //初始化seekbar
        mCustomSeekBar = findViewById(R.id.cs_bar_location_dialog);
        CustomSeekBarBean bean = new CustomSeekBarBean(minNum, maxNum, mMultiple,
                                                       mDefaultUnit,
                                                       mIsSeekBarTopTextType == 2 ? (mLeftDescDefault == null ?
                                                               getContext().getString(R.string.scene_text_common_more_than) : mLeftDescDefault) : null,
                                                       mUnit, mSeekBarIcon);
        UIUtils.initSeekBarDefaultWidth(getContext(), mCustomSeekBar, bean);

        mVB.includeDialogBottom.btnNormal.setOnClickListener(this);
        mVB.includeDialogBottom.btnPrimary.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.btn_primary) {
            ok4Normal();
            dismissWithAnimate();
        } else if (v.getId() == R.id.btn_normal) {
            dismissWithAnimate();
        }
    }


    private void ok4Normal() {
        String content = "";
        String desc = "";
        int index = 0;
        if (mIsSeekBarTopTextType != 2) {
            if (mVB.tabSwitchViewCondition.getSelectedIndex() == 0) {
                content = "(";
                desc = getContext().getString(R.string.scene_text_common_more_than);
            } else {
                content = ")";
                desc = getContext().getString(R.string.scene_text_common_less_than);
                index = 1;
            }
        }


        int distance = mMultiple == 0 ? mCustomSeekBar.getProgress() :
                mCustomSeekBar.getProgress() * mMultiple;

        if (mConditionList != null && mConditionList.size() > 0) {
            if (mConditionList.size() == 1) {
                //Condition只通过一个值控制
                ScenarioInfo.Condition condition = mConditionList.get(0);
                condition.getInput().get(0).setValue(content.concat(String.valueOf(distance)));
                if (mLeftDescDefault != null) {
                    condition.setDesc(mLeftDescDefault.concat(distance + mUnit));
                } else {
                    condition.setDesc(getContext().getString(R.string.scene_text_common_more_than).concat(desc.concat(distance + "km")));
                }
                condition.setCategory(mTitle);
                EventBus.getDefault().post(condition);
            } else {
                //Condition通过两值控制,大于索引0,小于索引1
                ScenarioInfo.Condition condition = mConditionList.get(index);
                condition.getInput().get(0).setValue(String.valueOf(distance));
                condition.getInput().get(0).setValue(String.valueOf(distance));
                condition.setDesc(desc.concat(distance + mUnit));
                condition.setCategory(mTitle);
                EventBus.getDefault().post(condition);
            }
        }
    }

}
