package com.dfl.smartscene.ui.edit.condition.trigger.door

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.TriggerDoor
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :条件->门窗view model
 * version: 1.0
 */
class DoorsTriggerViewModel : BaseViewModel() {

    val doorsWindowsLiveData = MutableLiveData<ArrayList<ConditionItemBean<TriggerDoor>>>()

    private val mTriggerConditionDoorsData = arrayListOf(1, 2, 3, 4)

    fun initData(resources: Resources) {
        val list = ArrayList<ConditionItemBean<TriggerDoor>>()
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_condition_all_door_locks),
                R.drawable.scene_icon_me_status_door_all_door_lock,
                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_DOOR_ALL_DOOR_LOCK),
                TriggerDoor.ALL_DOOR_LOCK
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_condition_main_driver_door),
                R.drawable.scene_icon_me_trigger_door_upperleft_door,
                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_DOOR_ALL_STATE),
                TriggerDoor.UPPER_LEFT_DOOR
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_condition_front_passenger_door),
                R.drawable.scene_icon_me_trigger_door_upperright_door,
                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_DOOR_ALL_STATE),
                TriggerDoor.UPPER_RIGHT_DOOR
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_condition_rear_left_door),
                R.drawable.scene_icon_me_trigger_door_lowerleft_door,
                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_DOOR_ALL_STATE),
                TriggerDoor.LOWER_LEFT_DOOR
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_condition_rear_right_door),
                R.drawable.scene_icon_me_trigger_door_lowerright_door,
                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_DOOR_ALL_STATE),
                TriggerDoor.LOWER_RIGHT_DOOR
            )
        )
//        list.add(
//            ConditionItemBean(
//                ConditionItemBean.SKILL_CONTENT,
//                resources.getString(R.string.scene_text_edit_add_action_door_left_child_lock),
//                R.drawable.scene_icon_me_action_door_children_lock_l,
//                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_DOOR_LEFT_CHILD_LOCK),
//                TriggerDoor.LEFT_CHILD_LOCK
//            )
//        )
//        list.add(
//            ConditionItemBean(
//                ConditionItemBean.SKILL_CONTENT,
//                resources.getString(R.string.scene_text_edit_add_action_door_right_child_lock),
//                R.drawable.scene_icon_me_action_door_children_lock_r,
//                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_DOOR_RIGHT_CHILD_LOCK),
//                TriggerDoor.RIGHT_CHILD_LOCK
//            )
//        )
//        list.add(
//            ConditionItemBean(
//                ConditionItemBean.SKILL_CONTENT,
//                "任意车门打开",
//                R.drawable.scene_icon_me_action_door_children_lock_r,
//                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_DOOR_ANY_OPEN),
//                TriggerDoor.DOOR_ANY_OPEN
//            )
//        )
        val type = SceneEditManager.TriggerType.DOOR
        val result = SceneEditManager.checkChildItemIsActive(type, list)
        doorsWindowsLiveData.postValue(result)

    }

    fun getTriggerConditionDoorsData(type: TriggerDoor): ArrayList<ScenarioInfo.Condition> {
        val list = ArrayList<ScenarioInfo.Condition>()
        val args = ArrayList<InputArgInfo>()
        when (type) {
            TriggerDoor.ALL_DOOR_LOCK -> {
                args.add(InputArgInfo(SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "0"))
                list.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_TRIGGER_DOOR_ALL_DOOR_LOCK,
                        "",
                        args
                    )
                )
            }

            TriggerDoor.UPPER_LEFT_DOOR -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_TRIGGER_DOOR_DOOR_ID,
                        ArgType.INT32,
                        "1"
                    )
                )
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS,
                        ArgType.INT32,
                        "1"
                    )
                )
                list.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_TRIGGER_DOOR_ALL_STATE,
                        "",
                        args
                    )
                )
            }

            TriggerDoor.UPPER_RIGHT_DOOR -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_TRIGGER_DOOR_DOOR_ID,
                        ArgType.INT32,
                        "2"
                    )
                )
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS,
                        ArgType.INT32,
                        "2"
                    )
                )
                list.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_TRIGGER_DOOR_ALL_STATE,
                        "",
                        args
                    )
                )
            }

            TriggerDoor.LOWER_LEFT_DOOR -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_TRIGGER_DOOR_DOOR_ID,
                        ArgType.INT32,
                        "3"
                    )
                )
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS,
                        ArgType.INT32,
                        "3"
                    )
                )
                list.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_TRIGGER_DOOR_ALL_STATE,
                        "",
                        args
                    )
                )
            }

            TriggerDoor.LOWER_RIGHT_DOOR -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_TRIGGER_DOOR_DOOR_ID,
                        ArgType.INT32,
                        "4"
                    )
                )
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS,
                        ArgType.INT32,
                        "4"
                    )
                )
                list.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_TRIGGER_DOOR_ALL_STATE,
                        "",
                        args
                    )
                )
            }

            TriggerDoor.LEFT_CHILD_LOCK -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS,
                        ArgType.INT32,
                        "0"
                    )
                )
                list.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_TRIGGER_DOOR_LEFT_CHILD_LOCK,
                        "",
                        args
                    )
                )
            }

            TriggerDoor.RIGHT_CHILD_LOCK -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS,
                        ArgType.INT32,
                        "0"
                    )
                )
                list.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_TRIGGER_DOOR_RIGHT_CHILD_LOCK,
                        "",
                        args
                    )
                )
            }

            TriggerDoor.DOOR_ANY_OPEN -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_TRIGGER_DOOR_STATUS_1,
                        ArgType.INT32,
                        "0"
                    )
                )
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_TRIGGER_DOOR_STATUS_2,
                        ArgType.INT32,
                        "0"
                    )
                )
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_TRIGGER_DOOR_STATUS_3,
                        ArgType.INT32,
                        "0"
                    )
                )
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_TRIGGER_DOOR_STATUS_4,
                        ArgType.INT32,
                        "0"
                    )
                )
                list.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_TRIGGER_DOOR_ANY_OPEN,
                        "",
                        args
                    )
                )
            }
        }


        return list
    }


    fun getInputArg(type: TriggerDoor): ArrayList<Int> {
        return when (type) {
            TriggerDoor.LEFT_CHILD_LOCK, TriggerDoor.RIGHT_CHILD_LOCK, TriggerDoor.ALL_DOOR_LOCK -> {
                arrayListOf(0, 1)
            }

            else -> {
                arrayListOf(1, 0)
            }
        }
    }

    fun getRadioList(resources: Resources, type: TriggerDoor): List<CheckListBean> {
        return when (type) {
            TriggerDoor.LEFT_CHILD_LOCK, TriggerDoor.RIGHT_CHILD_LOCK, TriggerDoor.ALL_DOOR_LOCK -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_condition_unlock), true),
                    CheckListBean(resources.getString(R.string.scene_text_condition_lock), false),
                )
            }

            TriggerDoor.DOOR_ANY_OPEN -> {
                arrayListOf(
                    CheckListBean(
                        resources.getString(R.string.scene_text_condition_unlock),
                        false,
                        isSingleSelectIcon = false
                    ),
                    CheckListBean(
                        resources.getString(R.string.scene_text_condition_lock),
                        false,
                        isSingleSelectIcon = false
                    ),
                    CheckListBean(
                        resources.getString(R.string.scene_text_condition_lock),
                        false,
                        isSingleSelectIcon = false
                    ),
                    CheckListBean(
                        resources.getString(R.string.scene_text_condition_lock),
                        false,
                        isSingleSelectIcon = false
                    ),
                )
            }

            else -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_common_open), true),
                    CheckListBean(resources.getString(R.string.scene_text_common_close), false),
                )
            }
        }
    }

}