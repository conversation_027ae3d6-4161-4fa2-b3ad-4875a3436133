package com.dfl.smartscene.ui.edit.condition.trigger.lighting

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.TriggerLighting
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo

/**
 *Created by 钟文祥 on 2025/4/18.
 *Describer:触发条件 灯光
 */
class LightingTriggerViewModel : BaseViewModel() {
    val lightingWindowsLiveData = MutableLiveData<ArrayList<ConditionItemBean<TriggerLighting>>>()

    fun initData(resources: Resources) {
        val list = ArrayList<ConditionItemBean<TriggerLighting>>()
        //左转向和右转向灯，ID要改
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_add_light_left),
                R.drawable.scene_icon_me_action_light_left_turn_signal,
                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_FAR_LIGHT),
                TriggerLighting.FAR_LIGHT
            )
        )

        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_add_light_right),
                R.drawable.scene_icon_me_action_light_right_turn_signal,
                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_FAR_LIGHT),
                TriggerLighting.FAR_LIGHT
            )
        )

        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_add_far_light),
                R.drawable.scene_icon_me_action_light_highbeam,
                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_FAR_LIGHT),
                TriggerLighting.FAR_LIGHT
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_add_near_light),
                R.drawable.scene_icon_me_action_light_lowbeam,
                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_NEAR_LIGHT),
                TriggerLighting.NEAR_LIGHT
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_add_fog_light),
                R.drawable.scene_icon_me_action_light_fog,
                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_FOG_LIGHT),
                TriggerLighting.FOG_LIGHT
            )
        )
        val type = SceneEditManager.TriggerType.LIGHTING
        val result = SceneEditManager.checkChildItemIsActive(type, list)
        lightingWindowsLiveData.postValue(result)
    }

    fun getListData(resources: Resources, type: TriggerLighting): List<CheckListBean> {
        return when (type) {
            TriggerLighting.FAR_LIGHT, TriggerLighting.NEAR_LIGHT, TriggerLighting.FOG_LIGHT -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_common_open), true),
                )
            }
        }
    }

    fun getConditionData(type: TriggerLighting): ArrayList<ScenarioInfo.Condition> {
        val list = ArrayList<ScenarioInfo.Condition>()
        val args = ArrayList<InputArgInfo>()
        when (type) {
            TriggerLighting.FAR_LIGHT -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_TRIGGER_FAR_LIGHT, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Condition(
                        1, "", SkillsListConstant.SKILL_ID_TRIGGER_FAR_LIGHT, "", args
                    )
                )
            }

            TriggerLighting.NEAR_LIGHT -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_TRIGGER_NEAR_LIGHT, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Condition(
                        1, "", SkillsListConstant.SKILL_ID_TRIGGER_NEAR_LIGHT, "", args
                    )
                )
            }

            TriggerLighting.FOG_LIGHT -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_TRIGGER_FOG_LIGHT, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Condition(
                        1, "", SkillsListConstant.SKILL_ID_TRIGGER_FOG_LIGHT, "", args
                    )
                )
            }
        }
        return list
    }

    fun getInputArg(type: TriggerLighting): ArrayList<Int> {
        return when (type) {
            TriggerLighting.FAR_LIGHT, TriggerLighting.NEAR_LIGHT, TriggerLighting.FOG_LIGHT -> {
                arrayListOf(1)
            }
        }
    }

}