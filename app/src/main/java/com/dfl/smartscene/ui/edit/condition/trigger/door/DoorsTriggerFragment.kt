package com.dfl.smartscene.ui.edit.condition.trigger.door

import android.annotation.SuppressLint
import android.view.View
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.TriggerDoor
import com.dfl.smartscene.databinding.SceneFragmentEditConditionBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.common.checklist.CheckListType
import com.dfl.smartscene.ui.overlay.common.checklist.RadioListDialogFragment
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :条件->门窗
 * version: 1.0
 */
class DoorsTriggerFragment(private val preCondition: ScenarioInfo.Condition?) :
    MVVMAdapterFragment<DoorsTriggerAdapter, SceneFragmentEditConditionBinding, DoorsTriggerViewModel>() {
    private var mAdapter: DoorsTriggerAdapter? = null
    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_condition
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        super.initData()
        mViewModel.doorsWindowsLiveData.observe(viewLifecycleOwner) {
            mAdapter?.data = it
            mAdapter?.notifyDataSetChanged()
            val position = getSimulateClickPosition(preCondition, null)
            if (position != -1) {
                view?.let { it1 -> handleItemClick(it1, position) }
            }
        }
        mViewModel.initData(resources)
    }

    override fun getAdapter(): DoorsTriggerAdapter? {
        return mAdapter
    }

    override fun initView(view: View?) {
        mAdapter = DoorsTriggerAdapter()
        mAdapter?.setOnItemClickListener { _, v, position ->
            run {
                handleItemClick(v, position)
            }
        }
        mViewDataBinding.rvCondition.adapter = mAdapter
    }

    private fun handleItemClick(view: View, position: Int) {
        if (DebouncingUtils.isValid(view, SceneEditManager.mViewClickDuration)) {
            mAdapter?.data?.let {
                if (it[position].conditionType == null) return@let
                showRadioListDialog(it[position].conditionName, it[position].conditionType!!)
            }
        }
    }

    override fun getViewModelClass(): Class<DoorsTriggerViewModel> {
        return DoorsTriggerViewModel::class.java
    }

    private fun showRadioListDialog(title: String, type: TriggerDoor) {
        val dialog: RadioListDialogFragment<ScenarioInfo.Condition, Int>
        when (type) {
            TriggerDoor.LEFT_CHILD_LOCK, TriggerDoor.RIGHT_CHILD_LOCK, TriggerDoor.ALL_DOOR_LOCK -> {
                dialog = RadioListDialogFragment(
                    title,
                    CheckListType.INPUT_CHECK_MODE,
                    mViewModel.getRadioList(resources, type),
                    mViewModel.getTriggerConditionDoorsData(type)
                )
            }

            TriggerDoor.DOOR_ANY_OPEN -> {
                dialog = RadioListDialogFragment(
                    title,
                    CheckListType.MULTI_INPUT_SINGLE_SEQUENCE_TYPE,
                    mViewModel.getRadioList(resources, type),
                    mViewModel.getTriggerConditionDoorsData(type)
                )
                dialog.isSingleSelect = false
            }

            else -> {
                dialog = RadioListDialogFragment(
                    title,
                    CheckListType.DOOR_SWITCH_TYPE,
                    mViewModel.getRadioList(resources, type),
                    mViewModel.getTriggerConditionDoorsData(type)
                )
            }
        }
        dialog.inputArgValueList = mViewModel.getInputArg(type)
        dialog.preSetCondition = preCondition
        dialog.show(parentFragmentManager, "showRadioListDialog")
    }

}