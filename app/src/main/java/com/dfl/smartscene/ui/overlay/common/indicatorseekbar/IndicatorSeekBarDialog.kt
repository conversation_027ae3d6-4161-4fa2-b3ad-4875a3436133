package com.dfl.smartscene.ui.overlay.common.indicatorseekbar

import android.content.Context
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.core.view.isVisible
import com.bumptech.glide.Glide
import com.dfl.android.common.base.BaseDialog
import com.dfl.android.common.util.ObjectUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.databinding.SceneDialogIndicatorSeekBarBinding
import com.dfl.smartscene.widget.indicatorseekbar.IndicatorSeekBar
import com.iauto.scenarioadapter.ScenarioInfo
import org.greenrobot.eventbus.EventBus

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/30
 * desc :空调风量,车窗调节,氛围灯亮度IndicatorSeekBar弹窗
 * version: 1.0
 */
class IndicatorSeekBarDialog(
    context: Context,
    private val mTitle: String,
    private val mActionList: ArrayList<ScenarioInfo.Sequence>,
    private val mInputType: IndicatorInputType
) : BaseDialog(context), View.OnClickListener {
    private lateinit var mVB: SceneDialogIndicatorSeekBarBinding

    /**
     * 标题
     */
    private lateinit var mTitleTv: TextView

    /**
     * 标题备注
     */
    private lateinit var mTitleDescTv: TextView


    /**
     * 车控空调风量seekbar
     */
    private lateinit var mIndicatorSeekBar: IndicatorSeekBar

    /**
     * 指示器单位TextView
     * isb_indicator_content_layout="@layout/scene_widget_indicator"
     */
    private lateinit var mIndicatorUnitTextView: TextView

    /**
     * 左侧icon
     */
    private lateinit var mLeftImageView: ImageView

    /**
     * 右侧icon
     */
    private lateinit var mRightImageView: ImageView

    /**
     * 左侧icon resId
     */
    @DrawableRes
    private var mLeftImageViewRes: Int? = null

    /**
     * 右侧icon resId
     */
    @DrawableRes
    private var mRightImageViewRes: Int? = null

    /**
     * 左右两个icon大小是否一致
     */
    private var mIsSetIconWithDifferentSize: Boolean = true

    /**
     * 上一次的动作
     */
    private var preSetAction: ScenarioInfo.Sequence? = null

    /**
     * 默认输入模式为空
     */
    var inputArgsValueList: List<Int>? = null

    /**
     * 标题备注
     */
    var titleDesc: String? = null

    /**
     * 当前指示器单位，默认为空
     */
    var indicatorUnit: String = ""

    /**
     * 指示器默认位置
     */
    var defaultIndex: Float = 2f

    /**
     * 可滑动的最小值
     */
    var seekBarMin: Float = 1f

    /**
     * 可滑动的最大值
     */
    var seekBarMax: Float = 7f

    /**
     * 包括两端的刻度数量
     */
    var tickCount: Int = 8

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mVB = SceneDialogIndicatorSeekBarBinding.inflate(layoutInflater)
        setContentView(mVB.root)
        initView()
        setupInitData()
    }

    private fun setupInitData() {
        var value: Float? = null
        //根据输入类型得到之前选择的进度条值
        if (mInputType == IndicatorInputType.DOORS_WINDOWS) {
            val index = ObjectUtils.findPreIndexWithInput(preSetAction, mActionList.toList(), inputArgsValueList)
            if (index != -1) {
                value = index.toFloat() * 10
            }
        } else {
            val index = ObjectUtils.findPreIndex(preSetAction, mActionList.toList())
            if (index != -1) {
                value = preSetAction!!.action.input[0].value.toFloat()
            }
        }
        //进度条值有效性校验
        if (value != null && value in seekBarMin..seekBarMax) {
            mIndicatorSeekBar.setProgress(value)
        }
    }

    private fun initView() { // 获取控件
        mTitleTv = findViewById(R.id.tv_volume_title)
        mTitleDescTv = findViewById(R.id.tv_volume_title_desc)
        mIndicatorSeekBar = findViewById(R.id.indicator_seek_bar)
        mIndicatorUnitTextView = mIndicatorSeekBar.indicator.contentView.findViewById(R.id.isb_indicator_unit)
        mLeftImageView = findViewById(R.id.iv_left_icon)
        mRightImageView = findViewById(R.id.iv_right_icon) // 初始化值
        mTitleTv.text = mTitle
        if (!titleDesc.isNullOrEmpty()) {
            mTitleDescTv.text = titleDesc
            mTitleDescTv.isVisible = true
        } // IndicatorSeekBar初始化
        mIndicatorUnitTextView.text = indicatorUnit
        mIndicatorSeekBar.min = seekBarMin
        mIndicatorSeekBar.max = seekBarMax
        mIndicatorSeekBar.tickCount = tickCount
        mIndicatorSeekBar.setProgress(defaultIndex)
        // 加载图片
        if (mLeftImageViewRes != null) {
            val leftLp = mLeftImageView.layoutParams
            leftLp.width = if (mIsSetIconWithDifferentSize) {
                context.resources.getDimensionPixelSize(R.dimen.scene_icon_size_indicator_dialog_left)
            } else {
                context.resources.getDimensionPixelSize(R.dimen.scene_icon_size_normal_icon)
            }
            leftLp.height = leftLp.width
            mLeftImageView.layoutParams = leftLp
            Glide.with(context).load(mLeftImageViewRes).into(mLeftImageView)
            mLeftImageView.isVisible = true
        }
        if (mRightImageViewRes != null) {
            Glide.with(context).load(mRightImageViewRes).into(mRightImageView)
            mRightImageView.isVisible = true
        }
        mVB.includeDialogBottom.btnPrimary.setOnClickListener(this)
        mVB.includeDialogBottom.btnNormal.setOnClickListener(this)
    }

    fun setPreSetAction(sequence: ScenarioInfo.Sequence?) {
        this.preSetAction = sequence
    }

    fun setIconWithSameSize(@DrawableRes leftIcon: Int, @DrawableRes rightIcon: Int) {
        mLeftImageViewRes = leftIcon
        mRightImageViewRes = rightIcon
        mIsSetIconWithDifferentSize = false
    }

    fun setIconWithDifferentSize(@DrawableRes leftIcon: Int, @DrawableRes rightIcon: Int) {
        mLeftImageViewRes = leftIcon
        mRightImageViewRes = rightIcon
        mIsSetIconWithDifferentSize = true
    }

    override fun onClick(v: View?) {
        v?.id?.let {
            if (it == R.id.btn_normal) {
                dismissWithAnimate()
            } else if (it == R.id.btn_primary) {
                val action = mActionList[0]
                val progress = mIndicatorSeekBar.progress
                action.action.category = mTitle
                if (mInputType == IndicatorInputType.DEFAULT_INPUT) {
                    action.action.desc = progress.toString().plus(indicatorUnit)
                    action.action.input[0].value = progress.toString()
                } else if (mInputType == IndicatorInputType.DOORS_WINDOWS) {
                    action.action.desc = when (progress) {
                        0 -> context.getString(R.string.scene_text_condition_full_open)
                        100 -> context.getString(R.string.scene_text_condition_full_close)
                        else -> context.getString(R.string.scene_text_common_close)
                            .plus((progress.toString().plus(indicatorUnit)))
                    }
                    action.action.input[0].value = inputArgsValueList?.get(progress / 10).toString()
                }
                EventBus.getDefault().post(action)
                dismissWithAnimate()
            }
        }
    }

    enum class IndicatorInputType {
        /**
         * 当前位置值作为InputArg
         */
        DEFAULT_INPUT,

        /**
         * 门窗动作的InputArg输入
         */
        DOORS_WINDOWS
    }
}
