package com.dfl.smartscene.ui.edit.condition.trigger.drive

import android.annotation.SuppressLint
import android.view.View
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.TriggerDrive
import com.dfl.smartscene.databinding.SceneFragmentEditConditionBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.common.RadioProgressDialog
import com.dfl.smartscene.ui.overlay.common.checklist.CheckListType
import com.dfl.smartscene.ui.overlay.common.checklist.RadioListDialogFragment
import com.dfl.smartscene.ui.overlay.common.doublewheel.DoubleWheelDialogFragment
import com.dfl.smartscene.ui.overlay.common.doublewheel.DoubleWheelType
import com.dfl.smartscene.ui.overlay.common.singlewheel.SingleWheelDialog
import com.dfl.smartscene.ui.overlay.common.singlewheel.SingleWheelType
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :触发条件->驾驶
 * version: 1.0
 */
class DriveTriggerFragment(private val preCondition: ScenarioInfo.Condition?) :
    MVVMAdapterFragment<DriveTriggerAdapter, SceneFragmentEditConditionBinding, DriveTriggerViewModel>() {

    private var mAdapter: DriveTriggerAdapter? = null

    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_condition
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    override fun getViewModelClass(): Class<DriveTriggerViewModel> {
        return DriveTriggerViewModel::class.java
    }

    override fun initView(view: View?) {
        super.initView(view)
        mAdapter = DriveTriggerAdapter()
        mAdapter?.setOnItemClickListener { _, v, position ->
            handleItemClick(v, position)
        }

        mViewDataBinding.rvCondition.adapter = mAdapter
    }

    private fun handleItemClick(view: View, position: Int) {
        if (DebouncingUtils.isValid(view, SceneEditManager.mViewClickDuration)) {
            mAdapter?.data?.let {
                if (it[position].conditionType == null) return@let
                when (it[position].conditionType) {
                    //TriggerDrive.BATTERY_POWER, TriggerDrive.BATTERY_LIFE -> {
                    //    showDistanceDialog(
                    //        it[position].conditionName, it[position].conditionType!!
                    //    )
                    //}
                    TriggerDrive.BATTERY_POWER, TriggerDrive.BATTERY_LIFE, TriggerDrive.DRIVE_SPEED -> {
                        showDoubleWheelDialog(
                            it[position].conditionName, it[position].conditionType!!
                        )
                    }
                    TriggerDrive.DRIVE_TIME, TriggerDrive.DRIVE_DISTANCE -> {
                        showSingleWheelDialog(
                            it[position].conditionName, it[position].conditionType!!
                        )
                    }
                    TriggerDrive.DRIVE_GEAR -> {
                        showRadioListDialog(
                            it[position].conditionName, it[position].conditionType!!
                        )
                    }

                    else -> {}
                }
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        super.initData()
        mViewModel.initData(resources)

        mViewModel.mDriveLiveData.observe(viewLifecycleOwner) {
            mAdapter?.data = it
            mAdapter?.notifyDataSetChanged()
            val position = getSimulateClickPosition(preCondition, null)
            if (position != -1) {
                view?.let { it1 -> handleItemClick(it1, position) }
            }
        }
    }

    override fun getAdapter(): DriveTriggerAdapter? {
        return mAdapter
    }

    private fun showRadioListDialog(title: String, type: TriggerDrive) {
        val dialog: RadioListDialogFragment<ScenarioInfo.Condition, Int> = RadioListDialogFragment(
            title,
            CheckListType.INPUT_CHECK_MODE,
            mViewModel.getGearCheckData(resources),
            mViewModel.getDriveConditionData(resources, type)

        )
        dialog.preSetCondition = preCondition
        dialog.inputArgValueList = mViewModel.getGearInputData()
        dialog.show(parentFragmentManager, "showRadioListDialog")
    }

    @Deprecated("2A合并对话框")
    private fun showDistanceDialog(title: String?, position: TriggerDrive) {
        title?.let {
            val dialog = RadioProgressDialog(
                context, title, position, mViewModel.getDriveConditionData(resources, position)
            )
            dialog.setPreSetCondition(preCondition)
            if (position == TriggerDrive.BATTERY_POWER) {
                dialog.setSeekBarMultiple(1)
                dialog.setMinNum(0)
                dialog.setMaxNum(100)
                dialog.setUnit("%")
                dialog.setDefaultUnit(30)
                dialog.setMoreThanState(false)
            } else if (position == TriggerDrive.BATTERY_LIFE) { //驾驶里程
                dialog.setSeekBarMultiple(10)
                dialog.setMinNum(0)
                dialog.setMaxNum(500)
                dialog.setUnit(getString(R.string.scene_text_condition_drive_distance_unit))
                dialog.setDefaultUnit(100)
                dialog.setMoreThanState(false)
                dialog.setSeekBarTopTextType(1)
            } else { //驾驶里程
                dialog.setSeekBarMultiple(10)
                dialog.setMinNum(0)
                dialog.setMaxNum(500)
                dialog.setUnit(getString(R.string.scene_text_condition_drive_distance_unit))
                dialog.setDefaultUnit(60)
                dialog.setSeekBarTopTextType(2)
            }
            dialog.showWithAnimate()
        }
    }

    private fun showDoubleWheelDialog(title: String?, position: TriggerDrive) {
        title?.let {
            var unit: String = ""
            var map: Map<String, String> = mapOf()
            var left = 0
            var right = 0
            var isLoop = true
            if (position == TriggerDrive.BATTERY_LIFE) {
                unit = getString(R.string.scene_text_condition_drive_distance_unit)
                map = mViewModel.getWheelMap(0, 500, 10)
                left = 1
                right = 10
                isLoop = false
            }
            if (position == TriggerDrive.BATTERY_POWER) {
                unit = getString(R.string.scene_text_common_unit_percent)
                map = mViewModel.getWheelMap(0, 100, 1)
                left = 1
                right = 30
            }
            if (position == TriggerDrive.DRIVE_SPEED) {
                unit = getString(R.string.scene_text_condition_drive_speed_unit)
                map = mViewModel.getWheelMap(0, 200, 5)
                right = 12
            }
            val dialog = DoubleWheelDialogFragment(
                title,
                mViewModel.getRbMap(),
                map,
                mViewModel.getDriveConditionData(resources, position),
                DoubleWheelType.DOUBLE_SKILL
            )
            dialog.setupPreCondition(preCondition)
            dialog.setUnit("", unit)
            dialog.setRightWheelLoop(isLoop)
            dialog.setWheelDefaultPosition(left, right)
            dialog.show(parentFragmentManager, "showDoubleWheelDialog")
        }
    }

    private fun showSingleWheelDialog(title: String?, position: TriggerDrive) {
        title?.let {
            val dialog: SingleWheelDialog<ScenarioInfo.Condition, Int>
            if (position == TriggerDrive.DRIVE_DISTANCE) {
                dialog = SingleWheelDialog(
                    context,
                    mViewModel.getWheelData(0.0, 500.0, 10.0),
                    title,
                    mViewModel.getDriveConditionData(resources, position),
                    SingleWheelType.SINGLE_WHEEL_DEFAULT
                )
                dialog.setUnit(getString(R.string.scene_text_condition_drive_distance_unit))
                dialog.isLoop = false
                dialog.setWheelDefaultPosition(6)
            } else {
                //驾驶时长
                dialog = SingleWheelDialog<ScenarioInfo.Condition, Int>(
                    context,
                    mViewModel.getWheelData(0.0, 24.0, 0.5),
                    title,
                    mViewModel.getDriveConditionData(resources, position),
                    SingleWheelType.SINGLE_WHEEL_DEFAULT
                )
                dialog.setWheelDefaultPosition(8)
                dialog.setUnit(resources.getString(R.string.scene_text_condition_drive_time_unit))
                //dialog.setShowUnit(true)
            }
            dialog.setShowLeftDesc(true, getString(R.string.scene_text_common_more_than))
            dialog.setupPreCondition(preCondition)
            dialog.showWithAnimate()
        }
    }
}