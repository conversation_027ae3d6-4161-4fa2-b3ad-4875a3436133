package com.dfl.smartscene.ui.edit.action.seat

import android.annotation.SuppressLint
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.ActionSkillItemBean
import com.dfl.smartscene.bean.action.SeatType
import com.dfl.smartscene.databinding.SceneFragmentEditActionBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.common.checklist.CheckListType
import com.dfl.smartscene.ui.overlay.common.checklist.RadioListDialogFragment
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :动作->座椅页面
 * version: 1.0
 */
class SeatActionFragment(private val sequence: ScenarioInfo.Sequence?) :
    MVVMAdapterFragment<SeatActionAdapter, SceneFragmentEditActionBinding, SeatActionViewModel>() {

    private var mAdapter: SeatActionAdapter? = null

    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_action
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    override fun getViewModelClass(): Class<SeatActionViewModel> {
        return SeatActionViewModel::class.java
    }

    override fun initView(view: View?) {
        super.initView(view)
        mAdapter = SeatActionAdapter()
        mAdapter?.setOnItemClickListener { _, v, position ->
            handleItemClick(v, position)
        }
        val manager = GridLayoutManager(context, 4)
        manager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                if (mAdapter?.data?.get(position)?.itemType == ActionSkillItemBean.SKILL_TITLE) return 4
                return 1
            }
        }

        mViewDataBinding.rvAction.layoutManager = manager
        mViewDataBinding.rvAction.adapter = mAdapter
    }

    private fun handleItemClick(view: View, position: Int) {
        if (mAdapter?.data?.get(position)?.type == ActionSkillItemBean.SKILL_TITLE) return
        if (DebouncingUtils.isValid(view, SceneEditManager.mViewClickDuration)) {
            mAdapter?.data?.get(position)?.let {
                if (it.actionType == null) return@let
                val content = it.content.replace("\n", "")
                when (it.actionType) {
                    //SeatType.RIGHT_SEAT_MASSAGE_MODE, SeatType.LEFT_SEAT_MASSAGE_MODE -> {
                    //    showSeatMassageModeDialog(content, it.actionType)
                    //}
                    else -> {
                        showRadioListDialog(content, it.actionType)
                    }
                }
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        super.initData()
        mViewModel.initData(resources)

        mViewModel.mSeatActionLiveData.observe(viewLifecycleOwner) {
            mAdapter?.data = it
            mAdapter?.notifyDataSetChanged()
            val position = getSimulateClickPosition(null, sequence)
            if (position != -1) {
                view?.let { it1 -> handleItemClick(it1, position) }
            }
        }
    }

    override fun getAdapter(): SeatActionAdapter? {
        return mAdapter
    }

    private fun showRadioListDialog(title: String, type: SeatType) {
        val dialog: RadioListDialogFragment<ScenarioInfo.Sequence, Int>
        when (type) {
            SeatType.LEFT_SEAT_MASSAGE, SeatType.RIGHT_SEAT_MASSAGE,
            SeatType.BACK_LEFT_SEAT_MASSAGE, SeatType.BACK_RIGHT_SEAT_MASSAGE -> {
                //座椅按摩,2个sequence
                dialog = RadioListDialogFragment(
                    title,
                    CheckListType.DEFAULT_CHECK_MODE,
                    mViewModel.getSeatListData(resources, type),
                    mViewModel.getSeatActionListData(type, resources),
                )
            }

            else -> {
                //一个sequence根据inputArg选项
                dialog = RadioListDialogFragment(
                    title,
                    CheckListType.INPUT_CHECK_MODE,
                    mViewModel.getSeatListData(resources, type),
                    mViewModel.getSeatActionListData(type, resources),
                )
                dialog.inputArgValueList = mViewModel.getSeatValueList(type)
            }
        }
        //6月ota临时对策添加备注，待9月更改走车控
        if (type == SeatType.LEFT_SEAT_MASSAGE_MODE || type == SeatType.RIGHT_SEAT_MASSAGE_MODE
            || type == SeatType.BACK_LEFT_SEAT_MASSAGE_MODE || type == SeatType.BACK_RIGHT_SEAT_MASSAGE_MODE) {
            dialog.desc = getString(R.string.scene_text_me_comment_seat_massage_mode)
        }
        dialog.preSetSequence = sequence
        dialog.show(parentFragmentManager, "showRadioListDialog")
    }
}