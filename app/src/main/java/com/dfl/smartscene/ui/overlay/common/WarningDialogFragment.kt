package com.dfl.smartscene.ui.overlay.common

import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.dfl.android.common.base.BaseDialogFragment
import com.dfl.smartscene.databinding.SceneDialogFragmentWarningBinding

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2022/07/07
 * desc: 只用做提示信息的弹框，点击后隐藏弹框
 * version:1.0
 */
class WarningDialogFragment : BaseDialogFragment() {
    private var _binding: SceneDialogFragmentWarningBinding? = null
    private val binding get() = _binding!!
    private var msg = ""
    private var strBtnMsg = ""

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = SceneDialogFragmentWarningBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun initView(view: View) {
        binding.tvMsgWarning.text = msg
        if (!TextUtils.isEmpty(strBtnMsg)) {
            binding.btnCancelWarning.text = strBtnMsg
        }
        binding.btnCancelWarning.setOnClickListener {
            dismissWithAnimate()
        }
        binding.ivDialogWarning.setOnClickListener {
            dismissWithAnimate()
        }
    }

    companion object {
        private val TAG = WarningDialogFragment::class.java.simpleName
        fun showDialog(
            manager: FragmentManager, msg: String = "", strBtnMsg: String = ""
        ) {
            val dialogFragment = WarningDialogFragment()
            dialogFragment.msg = msg
            dialogFragment.strBtnMsg = strBtnMsg
            dialogFragment.showNow(manager, TAG)
        }
    }

    override fun onStart() {
        super.onStart()
        dialog?.setCanceledOnTouchOutside(true)
    }

    override fun getLayoutId(): Int {
        return -1
    }
}