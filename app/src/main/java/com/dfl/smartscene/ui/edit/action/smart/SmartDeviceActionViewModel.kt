package com.dfl.smartscene.ui.edit.action.smart

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.ActionSkillItemBean
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.bean.action.SmartDeviceStateBean
import com.dfl.smartscene.bean.action.SmartDeviceType
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/26
 * desc :
 * version: 1.0
 */
class SmartDeviceActionViewModel : BaseViewModel() {

    val mSmartDeviceLiveData = MutableLiveData<ArrayList<ActionSkillItemBean<SmartDeviceType>>>()

    private fun initDeviceState(stateList: ArrayList<String>?): ArrayList<SmartDeviceStateBean> {
        val deviceStateList = ArrayList<SmartDeviceStateBean>()
        var mFragranceDeviceState = false //香氛
        var mAirPurifierDeviceState = false //空气净化器
        var mChildSeatDeviceState = false //儿童座椅
        var mMassageNeckPillow = false //颈枕
        var mMassageCushion = false //腰枕
        var mColorfulAmbientLight = false //炫彩氛围灯
        var mPlasmaFragrance = false //等离子香氛

        if (stateList?.contains("19") == true) { //香氛
            mFragranceDeviceState = true
        }
        if (stateList?.contains("23") == true) { //空气净化器
            mAirPurifierDeviceState = true
        }
        if (stateList?.contains("16") == true) { //儿童座椅
            mChildSeatDeviceState = true
        }
        if (stateList?.contains("77") == true) { //颈枕
            mMassageNeckPillow = true
        }
        if (stateList?.contains("78") == true) { //腰枕
            mMassageCushion = true
        }
        if (stateList?.contains("160") == true) { //炫彩氛围灯
            mColorfulAmbientLight = true
        }
        if (stateList?.contains("161") == true) { //等离子香氛
            mPlasmaFragrance = true
        }


        deviceStateList.add(SmartDeviceStateBean(1, mFragranceDeviceState))
        deviceStateList.add(SmartDeviceStateBean(2, mAirPurifierDeviceState))
        deviceStateList.add(SmartDeviceStateBean(3, mChildSeatDeviceState))
        //下面设备不上
        //        deviceStateList.add(SmartDeviceStateBean(4, mMassageNeckPillow))
        //        deviceStateList.add(SmartDeviceStateBean(5, mMassageCushion))
        //        deviceStateList.add(SmartDeviceStateBean(6, mColorfulAmbientLight))
        //        deviceStateList.add(SmartDeviceStateBean(7, mPlasmaFragrance))
        return deviceStateList
    }

    fun initData(resources: Resources, stateList: ArrayList<String>?) {
        val list = ArrayList<ActionSkillItemBean<SmartDeviceType>>()
        val deviceList = initDeviceState(stateList)
        deviceList.sortBy { !it.deviceState }
        deviceList.forEach {
            initDevice(it.deviceState, list, resources, it.type)
        }
        val type = SceneEditManager.ActionType.SMART
        val result = SceneEditManager.checkActionItemIsActive(type, list)
        mSmartDeviceLiveData.postValue(result)
    }

    fun getActionData(position: SmartDeviceType?): ArrayList<ScenarioInfo.Sequence> {
        val list = ArrayList<ScenarioInfo.Sequence>()
        val args = ArrayList<InputArgInfo>()
        when (position) {
            //香氛机开关
            SmartDeviceType.AROMA_DIFFUSER_SWITCH -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_AROMA_DIFFUSER_SWITCH_ID, "", args
                        )
                    )
                )
            }
            //香氛散香浓度
            SmartDeviceType.FRAGRANCE_STALL -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_FRAGRANCE_STALL_ID, "", args
                        )
                    )
                )
            }
            //空气净化器开关
            SmartDeviceType.AIR_PURIFIER_SWITCH -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_AIR_PURIFIER_ID, "", args
                        )
                    )
                )
            }
            //空气质量同步
            SmartDeviceType.AIR_QUALITY_SYNC -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_AIR_QUALITY_SYNC_ID, "", args
                        )
                    )
                )
            }
            //香氛模式
            SmartDeviceType.FRAGRANCE_MODE -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SMART_FRAGRANCE_MODE, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_FRAGRANCE_MODE_ID, "", args
                        )
                    )
                )
            }
            //香氛散香时长
            SmartDeviceType.FRAGRANCE_DURATION -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SMART_FRAGRANCE_DURATION_NAME, ArgType.INT32, "5"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_FRAGRANCE_DURATION_ID, "", args
                        )
                    )
                )
            }
            //儿童座椅通风
            SmartDeviceType.CHILD_SEAT_BLOW -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SMART_CHILD_SEAT_DEVICE_NAME, ArgType.INT32, "0"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_CHILD_SEAT_BLOW_ID, "", args
                        )
                    )
                )
            }
            //儿童座椅加热
            SmartDeviceType.CHILD_SEAT_HEATING -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SMART_CHILD_SEAT_DEVICE_NAME, ArgType.INT32, "0"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_CHILD_SEAT_HEAT_ID, "", args
                        )
                    )
                )
            }
            //按摩颈枕开关
            SmartDeviceType.MASSAGE_NECK_PILLOW_SWITCH -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_NECK_PILLOW_SWITCH_ID, "", args
                        )
                    )
                )
            }
            //按摩颈枕加热
            SmartDeviceType.MASSAGE_NECK_PILLOW_HEATING -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_NECK_PILLOW_HEAT_ID, "", args
                        )
                    )
                )
            }
            //颈枕按摩力度
            SmartDeviceType.NECK_PILLOW_MASSAGE_STRENGTH -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SMART_NECK_PILLOW_MASSAGE_STRENGTH_NAME, ArgType.INT32, "2"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_NECK_PILLOW_MASSAGE_STRENGTH_ID, "", args
                        )
                    )
                )
            }
            //颈枕按摩时间
            SmartDeviceType.NECK_PILLOW_MASSAGE_TIME -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SMART_FRAGRANCE_DURATION_NAME, ArgType.INT32, "5"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_NECK_PILLOW_MASSAGE_TIME_ID, "", args
                        )
                    )
                )
            }
            //颈枕按摩模式
            SmartDeviceType.NECK_PILLOW_MASSAGE_MODE -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_MODE, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_NECK_PILLOW_MASSAGE_MODE_ID, "", args
                        )
                    )
                )
            }
            //按摩腰靠开关
            SmartDeviceType.MASSAGE_CUSHION_SWITCH -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_CUSHION_SWITCH_ID, "", args
                        )
                    )
                )
            }
            //按摩腰靠加热
            SmartDeviceType.MASSAGE_CUSHION_HEATING -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_CUSHION_HEAT_ID, "", args
                        )
                    )
                )
            }
            //腰靠按摩模式
            SmartDeviceType.MASSAGE_PART -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_MODE, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_PART_ID, "", args
                        )
                    )
                )
            }
            //腰靠按摩力度
            SmartDeviceType.MASSAGE_CUSHION_TIME -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SMART_FRAGRANCE_DURATION_NAME, ArgType.INT32, "5"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_CUSHION_TIME_ID, "", args
                        )
                    )
                )
            }
            //腰靠按摩时间*
            SmartDeviceType.MASSAGE_CUSHION_STRENGTH -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SMART_NECK_PILLOW_MASSAGE_STRENGTH_NAME, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_CUSHION_STRENGTH_ID, "", args
                        )
                    )
                )
            }
            //炫彩氛围灯开关
            SmartDeviceType.COLORFUL_AMBIENT_LIGHT_SWITCH -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_SWITCH, "", args
                        )
                    )
                )
            }
            // 炫彩氛围灯亮度
            SmartDeviceType.COLORFUL_AMBIENT_LIGHT_BRIGHTNESS -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_LIGHT_AMBIENT_LIGHT_BRIGHTNESS_NAME, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_BRIGHTNESS, "", args
                        )
                    )
                )
            }
            //炫彩氛围灯模式
            SmartDeviceType.COLORFUL_AMBIENT_LIGHT_MODE -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_MODE, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_MODE, "", args
                        )
                    )
                )
            }
            //炫彩氛围灯颜色
            SmartDeviceType.COLORFUL_AMBIENT_LIGHT_COLOR -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_LIGHT_AMBIENT_LIGHT_COLOR_THEME_NAME, ArgType.INT32, "0"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_COLOR_THEME, "", args
                        )
                    )
                )
                val arg = ArrayList<InputArgInfo>()
                arg.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_COLORFUL_AMBIENT_LIGHT_COLOR_DETAILED_COLOR_NAME,
                        ArgType.INT32,
                        "0"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_COLOR_CUSTOMIZE, "", arg
                        )
                    )
                )
            }
            //等离子香氛开关
            SmartDeviceType.PLASMA_FRAGRANCE_SWITCH -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_PLASMA_FRAGRANCE_SWITCH, "", args
                        )
                    )
                )
            }
            //等离子香氛浓度
            SmartDeviceType.PLASMA_FRAGRANCE_CONCENTRATION -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_PLASMA_FRAGRANCE_CONCENTRATION, "", args
                        )
                    )
                )
            }
            //等离子香氛模式
            SmartDeviceType.PLASMA_FRAGRANCE_MODE -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_MODE, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_PLASMA_FRAGRANCE_MODE, "", args
                        )
                    )
                )
            }
            //等离子香氛香型
            SmartDeviceType.PLASMA_FRAGRANCE_FLAVOR -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SMART_PLASMA_FRAGRANCE_FLAVOR_SMART_NAME, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SMART_PLASMA_FRAGRANCE_FLAVOR, "", args
                        )
                    )
                )
            }
            else -> {}
        }
        return list

    }

    private fun initDevice(
        state: Boolean, list: ArrayList<ActionSkillItemBean<SmartDeviceType>>, resources: Resources, item: Int
    ) {
        if (item == 1) {
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_aroma_diffuser),
                    -1,
                    ActionSkillItemBean.SKILL_TITLE,
                    SmartDeviceType.ITEM_TITLE,
                    null,
                    SmartDeviceType.SMART_FRAGRANCE_TYPE
                )
            )
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_aroma_diffuser_switch),
                    R.drawable.scene_icon_me_action_smart_aroma_diffuser_switch,
                    ActionSkillItemBean.SKILL_CONTENT,
                    SmartDeviceType.AROMA_DIFFUSER_SWITCH,
                    arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SMART_AROMA_DIFFUSER_SWITCH_ID),
                    SmartDeviceType.SMART_FRAGRANCE_TYPE,
                    state
                )
            )
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_fragrance_mode),
                    R.drawable.scene_icon_me_action_smart_aroma_diffuser_mode,
                    ActionSkillItemBean.SKILL_CONTENT,
                    SmartDeviceType.FRAGRANCE_MODE,
                    arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SMART_FRAGRANCE_MODE_ID),
                    SmartDeviceType.SMART_FRAGRANCE_TYPE,
                    state
                )
            )
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_fragrance_stall),
                    R.drawable.scene_icon_me_action_smart_aroma_diffuser_concentration,
                    ActionSkillItemBean.SKILL_CONTENT,
                    SmartDeviceType.FRAGRANCE_STALL,
                    arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SMART_FRAGRANCE_STALL_ID),
                    SmartDeviceType.SMART_FRAGRANCE_TYPE,
                    state
                )
            )
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_fragrance_duration),
                    R.drawable.scene_icon_me_action_smart_aroma_diffuser_duration,
                    ActionSkillItemBean.SKILL_CONTENT,
                    SmartDeviceType.FRAGRANCE_DURATION,
                    arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SMART_FRAGRANCE_DURATION_ID),
                    SmartDeviceType.SMART_FRAGRANCE_TYPE,
                    state
                )
            )

        }
        if (item == 2) {
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_air_purifier),
                    -1,
                    ActionSkillItemBean.SKILL_TITLE,
                    SmartDeviceType.ITEM_TITLE,
                    null,
                    SmartDeviceType.SMART_AIR_TYPE
                )
            )
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_air_purification_switch),
                    R.drawable.scene_icon_me_status_smart_device_air_quality,
                    ActionSkillItemBean.SKILL_CONTENT,
                    SmartDeviceType.AIR_PURIFIER_SWITCH,
                    arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SMART_AIR_PURIFIER_ID),
                    SmartDeviceType.SMART_AIR_TYPE,
                    state
                )
            )
        }
        if (item == 3) {
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_child_seat),
                    -1,
                    ActionSkillItemBean.SKILL_TITLE,
                    SmartDeviceType.ITEM_TITLE,
                    null,
                    SmartDeviceType.SMART_CHILD_SEAT_TYPE
                )
            )
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_child_seat_blow),
                    R.drawable.scene_icon_me_status_smart_device_children_ventilate,
                    ActionSkillItemBean.SKILL_CONTENT,
                    SmartDeviceType.CHILD_SEAT_BLOW,
                    arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SMART_CHILD_SEAT_BLOW_ID),
                    SmartDeviceType.SMART_CHILD_SEAT_TYPE,
                    state
                )
            )
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_child_seat_heat),
                    R.drawable.scene_icon_me_status_smart_device_children_heating,
                    ActionSkillItemBean.SKILL_CONTENT,
                    SmartDeviceType.CHILD_SEAT_HEATING,
                    arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SMART_CHILD_SEAT_HEAT_ID),
                    SmartDeviceType.SMART_CHILD_SEAT_TYPE,
                    state
                )
            )
        }

        if (item == 4) {
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_massage_neck_pillow),
                    -1,
                    ActionSkillItemBean.SKILL_TITLE,
                    SmartDeviceType.ITEM_TITLE,
                    null,
                    SmartDeviceType.SMART_MASSAGE_NECK_PILLOW_TYPE
                )
            )
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_massage_neck_pillow_switch),
                    R.drawable.scene_icon_me_action_smart_neck_pillow,
                    ActionSkillItemBean.SKILL_CONTENT,
                    SmartDeviceType.MASSAGE_NECK_PILLOW_SWITCH,
                    arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_NECK_PILLOW_SWITCH_ID),
                    SmartDeviceType.SMART_MASSAGE_NECK_PILLOW_TYPE,
                    state
                )
            )
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_neck_pillow_massage_time),
                    R.drawable.scene_icon_me_action_smart_neck_pillow,
                    ActionSkillItemBean.SKILL_CONTENT,
                    SmartDeviceType.NECK_PILLOW_MASSAGE_TIME,
                    arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SMART_NECK_PILLOW_MASSAGE_TIME_ID),
                    SmartDeviceType.SMART_MASSAGE_NECK_PILLOW_TYPE,
                    state
                )
            )
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_neck_pillow_massage_mode),
                    R.drawable.scene_icon_me_action_smart_neck_pillow,
                    ActionSkillItemBean.SKILL_CONTENT,
                    SmartDeviceType.NECK_PILLOW_MASSAGE_MODE,
                    arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SMART_NECK_PILLOW_MASSAGE_MODE_ID),
                    SmartDeviceType.SMART_MASSAGE_NECK_PILLOW_TYPE,
                    state
                )
            )
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_neck_pillow_massage_strength),
                    R.drawable.scene_icon_me_action_smart_neck_pillow,
                    ActionSkillItemBean.SKILL_CONTENT,
                    SmartDeviceType.NECK_PILLOW_MASSAGE_STRENGTH,
                    arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SMART_NECK_PILLOW_MASSAGE_STRENGTH_ID),
                    SmartDeviceType.SMART_MASSAGE_NECK_PILLOW_TYPE,
                    state
                )
            )
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_massage_neck_pillow_heat),
                    R.drawable.scene_icon_me_action_smart_neck_pillow,
                    ActionSkillItemBean.SKILL_CONTENT,
                    SmartDeviceType.MASSAGE_NECK_PILLOW_HEATING,
                    arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_NECK_PILLOW_HEAT_ID),
                    SmartDeviceType.SMART_MASSAGE_NECK_PILLOW_TYPE,
                    state
                )
            )
        }
        if (item == 5) {
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_massage_cushion),
                    -1,
                    ActionSkillItemBean.SKILL_TITLE,
                    SmartDeviceType.ITEM_TITLE,
                    null,
                    SmartDeviceType.SMART_MASSAGE_CUSHION_TYPE
                )
            )
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_massage_cushion_switch),
                    R.drawable.scene_icon_me_action_smart_massage_cushion,
                    ActionSkillItemBean.SKILL_CONTENT,
                    SmartDeviceType.MASSAGE_CUSHION_SWITCH,
                    arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_CUSHION_SWITCH_ID),
                    SmartDeviceType.SMART_MASSAGE_CUSHION_TYPE,
                    state
                )
            )
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_massage_cushion_time),
                    R.drawable.scene_icon_me_action_smart_massage_cushion,
                    ActionSkillItemBean.SKILL_CONTENT,
                    SmartDeviceType.MASSAGE_CUSHION_TIME,
                    arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_CUSHION_TIME_ID),
                    SmartDeviceType.SMART_MASSAGE_CUSHION_TYPE,
                    state
                )
            )
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_massage_part),
                    R.drawable.scene_icon_me_action_smart_massage_cushion,
                    ActionSkillItemBean.SKILL_CONTENT,
                    SmartDeviceType.MASSAGE_PART,
                    arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_PART_ID),
                    SmartDeviceType.SMART_MASSAGE_CUSHION_TYPE,
                    state
                )
            )
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_massage_cushion_strength),
                    R.drawable.scene_icon_me_action_smart_massage_cushion,
                    ActionSkillItemBean.SKILL_CONTENT,
                    SmartDeviceType.MASSAGE_CUSHION_STRENGTH,
                    arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_CUSHION_STRENGTH_ID),
                    SmartDeviceType.SMART_MASSAGE_CUSHION_TYPE,
                    state
                )
            )
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_massage_cushion_heat),
                    R.drawable.scene_icon_me_action_smart_massage_cushion,
                    ActionSkillItemBean.SKILL_CONTENT,
                    SmartDeviceType.MASSAGE_CUSHION_HEATING,
                    arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_CUSHION_HEAT_ID),
                    SmartDeviceType.SMART_MASSAGE_CUSHION_TYPE,
                    state
                )
            )
        }

        if (item == 6) {
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_colorful_ambient_light),
                    -1,
                    ActionSkillItemBean.SKILL_TITLE,
                    SmartDeviceType.ITEM_TITLE,
                    null,
                    SmartDeviceType.SMART_COLORFUL_AMBIENT_LIGHT_TYPE
                )
            )
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_colorful_ambient_light_switch),
                    R.drawable.scene_icon_me_action_smart_colorful_ambient_light_switch,
                    ActionSkillItemBean.SKILL_CONTENT,
                    SmartDeviceType.COLORFUL_AMBIENT_LIGHT_SWITCH,
                    arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_SWITCH),
                    SmartDeviceType.SMART_COLORFUL_AMBIENT_LIGHT_TYPE,
                    state
                )
            )
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_colorful_ambient_light_brightness),
                    R.drawable.scene_icon_me_action_smart_colorful_ambient_light_brightnes,
                    ActionSkillItemBean.SKILL_CONTENT,
                    SmartDeviceType.COLORFUL_AMBIENT_LIGHT_BRIGHTNESS,
                    arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_BRIGHTNESS),
                    SmartDeviceType.SMART_COLORFUL_AMBIENT_LIGHT_TYPE,
                    state
                )
            )
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_colorful_ambient_light_mode),
                    R.drawable.scene_icon_me_action_smart_colorful_ambient_light_mode,
                    ActionSkillItemBean.SKILL_CONTENT,
                    SmartDeviceType.COLORFUL_AMBIENT_LIGHT_MODE,
                    arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_MODE),
                    SmartDeviceType.SMART_COLORFUL_AMBIENT_LIGHT_TYPE,
                    state
                )
            )
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_colorful_ambient_light_color),
                    R.drawable.scene_icon_me_action_smart_colorful_ambient_light_color,
                    ActionSkillItemBean.SKILL_CONTENT,
                    SmartDeviceType.COLORFUL_AMBIENT_LIGHT_COLOR,
                    arrayListOf(
                        SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_COLOR_THEME,
                        SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_COLOR_CUSTOMIZE
                    ),
                    SmartDeviceType.SMART_COLORFUL_AMBIENT_LIGHT_TYPE,
                    state
                )
            )
        }
        if (item == 7) {
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_plasma_fragrance),
                    -1,
                    ActionSkillItemBean.SKILL_TITLE,
                    SmartDeviceType.ITEM_TITLE,
                    null,
                    SmartDeviceType.SMART_PLASMA_FRAGRANCE_TYPE
                )
            )
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_plasma_fragrance_switch),
                    R.drawable.scene_icon_me_action_smart_aroma_diffuser_switch,
                    ActionSkillItemBean.SKILL_CONTENT,
                    SmartDeviceType.PLASMA_FRAGRANCE_SWITCH,
                    arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SMART_PLASMA_FRAGRANCE_SWITCH),
                    SmartDeviceType.SMART_PLASMA_FRAGRANCE_TYPE,
                    state
                )
            )
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_plasma_fragrance_concentration),
                    R.drawable.scene_icon_me_action_smart_aroma_diffuser_concentration,
                    ActionSkillItemBean.SKILL_CONTENT,
                    SmartDeviceType.PLASMA_FRAGRANCE_CONCENTRATION,
                    arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SMART_PLASMA_FRAGRANCE_CONCENTRATION),
                    SmartDeviceType.SMART_PLASMA_FRAGRANCE_TYPE,
                    state
                )
            )
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_plasma_fragrance_mode),
                    R.drawable.scene_icon_me_action_smart_aroma_diffuser_mode,
                    ActionSkillItemBean.SKILL_CONTENT,
                    SmartDeviceType.PLASMA_FRAGRANCE_MODE,
                    arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SMART_PLASMA_FRAGRANCE_MODE),
                    SmartDeviceType.SMART_PLASMA_FRAGRANCE_TYPE,
                    state
                )
            )
            list.add(
                ActionSkillItemBean(
                    resources.getString(R.string.scene_text_action_plasma_fragrance_flavor),
                    R.drawable.scene_icon_me_action_smart_aroma_diffuser_mode,
                    ActionSkillItemBean.SKILL_CONTENT,
                    SmartDeviceType.PLASMA_FRAGRANCE_FLAVOR,
                    arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SMART_PLASMA_FRAGRANCE_FLAVOR),
                    SmartDeviceType.SMART_PLASMA_FRAGRANCE_TYPE,
                    state
                )
            )
        }
    }

    fun getListData(resources: Resources, type: SmartDeviceType): List<CheckListBean> {
        return when (type) {
            SmartDeviceType.FRAGRANCE_STALL -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_action_fragrance_rich), true),
                    CheckListBean(resources.getString(R.string.scene_text_action_fragrance_light), false),
                )
            }
            SmartDeviceType.FRAGRANCE_MODE -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_action_fragrance_mode_sustain), true),
                    CheckListBean(resources.getString(R.string.scene_text_action_fragrance_mode_interval), false),
                )
            }
            SmartDeviceType.NECK_PILLOW_MASSAGE_STRENGTH, SmartDeviceType.MASSAGE_CUSHION_STRENGTH -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_action_weak), true),
                    CheckListBean(resources.getString(R.string.scene_text_edit_action_middle), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_strong), false),
                )
            }
            SmartDeviceType.NECK_PILLOW_MASSAGE_MODE, //颈枕按摩模式
            SmartDeviceType.MASSAGE_PART -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_massage_neck_mode_left), true),
                    CheckListBean(resources.getString(R.string.scene_text_massage_neck_mode_middle), false),
                    CheckListBean(resources.getString(R.string.scene_text_massage_neck_mode_right), false),
                )
            }
            SmartDeviceType.CHILD_SEAT_BLOW, SmartDeviceType.CHILD_SEAT_HEATING -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_common_close), true),
                    CheckListBean(resources.getString(R.string.scene_text_edit_action_low), false),
                    CheckListBean(resources.getString(R.string.scene_text_edit_action_middle), false),
                    CheckListBean(resources.getString(R.string.scene_text_edit_action_high), false)
                )
            }
            SmartDeviceType.COLORFUL_AMBIENT_LIGHT_MODE -> { //炫彩氛围灯模式
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_action_always_bright), true),
                    CheckListBean(resources.getString(R.string.scene_text_action_breathing), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_musical_rhythm), false)
                )
            }
            SmartDeviceType.PLASMA_FRAGRANCE_CONCENTRATION -> { //等离子香氛浓度
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_action_fresh), true),
                    CheckListBean(resources.getString(R.string.scene_text_action_strong_fragrance), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_light_fragrance), false),
                )
            }
            SmartDeviceType.PLASMA_FRAGRANCE_MODE -> { //等离子香氛模式
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_action_fragrance_mode_sustain), true),
                    CheckListBean(resources.getString(R.string.scene_text_action_fragrance_mode_interval), false),
                )
            }
            SmartDeviceType.PLASMA_FRAGRANCE_FLAVOR -> { //等离子香氛香型
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_action_east), true),
                    CheckListBean(resources.getString(R.string.scene_text_action_rosin), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_tea_charm), false),
                )
            }
            else -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_common_open), true),
                    CheckListBean(resources.getString(R.string.scene_text_common_close), false),
                )
            }
        }
    }

    fun getStringInputArg(isFragranceDuration: Boolean): ArrayList<String> {
        val list = if (isFragranceDuration) {
            arrayListOf("5", "15", "30", "45", "60", "75", "90", "105", "120")
        } else {
            arrayListOf("5", "10", "15", "20", "25", "30")
        }
        return list
    }

    fun getInputArgIntValue(type: SmartDeviceType?): ArrayList<Int> {
        return when (type) {
            SmartDeviceType.PLASMA_FRAGRANCE_MODE, //等离子香氛模式
            SmartDeviceType.FRAGRANCE_STALL, //香氛散香浓度
            SmartDeviceType.FRAGRANCE_MODE -> { //香氛模式
                arrayListOf(1, 2)
            }
            SmartDeviceType.NECK_PILLOW_MASSAGE_MODE, //颈枕按摩模式
            SmartDeviceType.NECK_PILLOW_MASSAGE_STRENGTH, //颈枕按摩力度
            SmartDeviceType.MASSAGE_CUSHION_STRENGTH, // 腰靠按摩时间
            SmartDeviceType.COLORFUL_AMBIENT_LIGHT_MODE, //炫彩氛围灯模式
            SmartDeviceType.PLASMA_FRAGRANCE_CONCENTRATION, //等离子香氛浓度
            SmartDeviceType.PLASMA_FRAGRANCE_FLAVOR, //等离子香氛香型
            SmartDeviceType.MASSAGE_PART -> {  //腰靠按摩模式：震动 : 0x1,捶打 : 0x2,揉捏:0x3
                arrayListOf(1, 2, 3)
            }
            SmartDeviceType.CHILD_SEAT_BLOW, //儿童座椅通风
            SmartDeviceType.CHILD_SEAT_HEATING -> { //儿童座椅加热
                arrayListOf(0, 1, 2, 3)
            }
            SmartDeviceType.COLORFUL_AMBIENT_LIGHT_SWITCH -> { //炫彩氛围灯开关
                arrayListOf(2, 1)
            }
            SmartDeviceType.COLORFUL_AMBIENT_LIGHT_COLOR -> { //炫彩氛围灯颜色
                arrayListOf(1, 2, 3, 4)
            }
            else -> {
                arrayListOf(1, 0)
            }
        }
    }

    //单个Wheel时的所有显示值
    fun getSingleWheelNameList(resources: Resources, type: SmartDeviceType?): ArrayList<String> {
        return when (type) {
            SmartDeviceType.COLORFUL_AMBIENT_LIGHT_COLOR -> {
                arrayListOf(
                    resources.getString(R.string.scene_text_action_vitality_orange),
                    resources.getString(R.string.scene_text_action_quiet_blue),
                    resources.getString(R.string.scene_text_action_psychedelic_purple),
                    resources.getString(R.string.scene_text_action_charm_red),
                )
            }
            else -> {
                arrayListOf()
            }
        }
    }
}