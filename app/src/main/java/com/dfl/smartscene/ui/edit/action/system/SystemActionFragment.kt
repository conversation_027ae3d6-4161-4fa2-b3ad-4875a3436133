package com.dfl.smartscene.ui.edit.action.system

import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.ActionSkillItemBean
import com.dfl.smartscene.bean.action.SystemType
import com.dfl.smartscene.bean.edit.CustomSeekBarBean
import com.dfl.smartscene.databinding.SceneFragmentEditActionBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.common.checklist.CheckListType
import com.dfl.smartscene.ui.overlay.common.checklist.RadioListDialogFragment
import com.dfl.smartscene.ui.overlay.common.singleprogress.SingleProgressDialog
import com.dfl.smartscene.ui.overlay.common.singlewheel.SingleWheelDialog
import com.dfl.smartscene.ui.overlay.common.singlewheel.SingleWheelType
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :动作->系统页面
 * version: 1.0
 */
class SystemActionFragment(private val sequence: ScenarioInfo.Sequence?) :
    MVVMAdapterFragment<SystemActionAdapter, SceneFragmentEditActionBinding, SystemActionViewModel>() {

    private var mAdapter: SystemActionAdapter? = null
    private val TAG = GlobalConstant.GLOBAL_TAG.plus("SystemActionFragment")
    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_action
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    override fun getViewModelClass(): Class<SystemActionViewModel> {
        return SystemActionViewModel::class.java
    }

    override fun initView(view: View?) {
        super.initView(view)
        mAdapter = SystemActionAdapter()

        mAdapter?.setOnItemClickListener { _, v, position ->
            handleItemClick(v, position)
        }
        val manager = GridLayoutManager(context, 4)
        manager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                if (mAdapter?.data?.get(position)?.itemType == ActionSkillItemBean.SKILL_TITLE) return 4
                return 1
            }
        }
        mViewDataBinding.rvAction.layoutManager = manager
        mViewDataBinding.rvAction.adapter = mAdapter
    }

    override fun initData() {
        super.initData()
        mViewModel.initData(resources)

        mViewModel.mSystemLiveData.observe(viewLifecycleOwner) {
            mAdapter?.setNewInstance(it)
            val position = getSimulateClickPosition(null, sequence)
            if (position != -1) {
                view?.let { it1 -> handleItemClick(it1, position) }
            }
        }
    }

    private fun handleItemClick(view: View, position: Int) {
        if (mAdapter?.data?.get(position)?.itemType == ActionSkillItemBean.SKILL_TITLE) return
        if (DebouncingUtils.isValid(view, SceneEditManager.mViewClickDuration)) {
            mAdapter?.data?.get(position)?.let {
                if (it.actionType == null) return@let
                when (it.actionType) {
                    SystemType.CENTER_SCREEN_BRIGHTNESS, SystemType.MEDIA_VOLUME, SystemType.VOICE_ASSISTANT_VOLUME, SystemType.BLUETOOTH_VOLUME, SystemType.NAVI_VOLUME, SystemType.CALL_VOLUME, SystemType.CAT_OUT_VOLUME, SystemType.RING_VOLUME -> {
                        showSingleWheelDialog(it.content, it.actionType)
                    }

                    else -> {
                        showRadioListDialog(it.content, it.actionType)
                    }
                }
            }
        }
    }

    override fun getAdapter(): SystemActionAdapter? {
        return mAdapter
    }

    private fun showRadioListDialog(title: String, type: SystemType) {
        //一个sequence根据inputArg选项
        val dialog: RadioListDialogFragment<ScenarioInfo.Sequence, Int> = RadioListDialogFragment(
            title,
            CheckListType.INPUT_CHECK_MODE,
            mViewModel.getListData(resources, type),
            listOf(mViewModel.getSystemActionData(type, resources)),
        )
        dialog.inputArgValueList = mViewModel.getInputArgIntValue(type)

        dialog.preSetSequence = sequence
        dialog.show(parentFragmentManager, "showRadioListDialog")
    }


    /**
     * 打开滑动条弹窗
     * 包括 中控屏亮度,多媒体音量,语音音量,导航音量,通话音量,蓝牙音乐音量
     */
    @Deprecated("2A合并对话框")
    private fun showSingleProgressDialog(title: String, type: SystemType) {
        var start = 0
        var end = 0
        var defaultIndex = 0
        when (type) {
            SystemType.CAT_OUT_VOLUME -> {
                start = 0
                end = 15
                defaultIndex = 8
            }

            SystemType.CENTER_SCREEN_BRIGHTNESS -> {
                start = 1
                end = 49
                defaultIndex = 30
            }

            SystemType.MEDIA_VOLUME, SystemType.NAVI_VOLUME, SystemType.VOICE_ASSISTANT_VOLUME, SystemType.CALL_VOLUME, SystemType.BLUETOOTH_VOLUME, SystemType.RING_VOLUME -> {
                start = 0
                end = 40
                defaultIndex = 16
            }

            else -> {
                CommonLogUtils.logE(TAG, "showSingleProgressDialog 错误的actionType类型:$type")
            }
        }

        val dialog = SingleProgressDialog(
            context,
            title,
            mViewModel.getSystemActionData(type, resources),
            CustomSeekBarBean(start, end, 1, defaultIndex),
        )
        dialog.setupPreAction(sequence)
        dialog.showWithAnimate()
    }

    private fun showSingleWheelDialog(title: String, type: SystemType) {
        var start = 0
        var end = 0
        var defaultIndex = 0
        when (type) {
            SystemType.CAT_OUT_VOLUME -> {
                start = 0
                end = 15
                defaultIndex = 8
            }

            SystemType.CENTER_SCREEN_BRIGHTNESS -> {
                start = 1
                end = 49
                defaultIndex = 29
            }

            SystemType.MEDIA_VOLUME, SystemType.NAVI_VOLUME, SystemType.VOICE_ASSISTANT_VOLUME, SystemType.CALL_VOLUME, SystemType.BLUETOOTH_VOLUME, SystemType.RING_VOLUME -> {
                start = 0
                end = 40
                defaultIndex = 16
            }

            else -> {
                CommonLogUtils.logE(TAG, "showSingleProgressDialog 错误的actionType类型:$type")
            }
        }
        val map = mViewModel.getWheelMap(start, end)
        val dialog = SingleWheelDialog<ScenarioInfo.Sequence, String>(
            context,
            map.keys.toList(),
            title,
            listOf(mViewModel.getSystemActionData(type, resources)),
            SingleWheelType.SINGLE_WHEEL_INPUT_ARG
        )
        dialog.setupPreAction(sequence)
        dialog.setInputArgValueList(map.values.toList())
        dialog.setWheelDefaultPosition(defaultIndex)
        dialog.showWithAnimate()
    }

}