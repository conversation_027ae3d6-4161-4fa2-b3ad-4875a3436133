package com.dfl.smartscene.ui.overlay.smart

import android.content.Context
import android.os.Bundle
import androidx.appcompat.widget.AppCompatImageView
import androidx.constraintlayout.widget.ConstraintLayout
import com.dfl.android.common.base.BaseDialog
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.StatusSmartDevice

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2023/01/13
 * desc :购买智能设备弹窗 for 状态
 * version: 1.0
 */
class BuySmartDevice4StatusDialog(context: Context, private val deviceType: StatusSmartDevice) : BaseDialog(context) {

    private var mIvClose: AppCompatImageView? = null
    private var mCslContainer: ConstraintLayout? = null
    private var mIvBuyQrCode: AppCompatImageView? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.scene_dialog_buy_smart_device)

        initView()
    }

    private fun initView() {
        mIvClose = findViewById(R.id.iv_close_buy)
        mCslContainer = findViewById(R.id.csl_buy_smart_device_container)
        mCslContainer?.setOnClickListener {
            dismissWithAnimate()
        }
        mIvClose?.setOnClickListener {
            dismissWithAnimate()
        }
        mIvBuyQrCode = findViewById(R.id.iv_qrcode_buy)
        val resId = when (deviceType) {
            StatusSmartDevice.AIR_QUALITY -> {
                R.drawable.scene_img_aiot_air_code
            }
            StatusSmartDevice.RIDING_STATUS -> {
                R.drawable.scene_img_aiot_child_seat_code
            }
            else -> {
                R.drawable.scene_img_aiot_air_code
            }
        }
        mIvBuyQrCode?.setImageResource(resId)
    }
}