package com.dfl.smartscene.ui.edit.action.other

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.ActionSkillItemBean
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.bean.action.OtherType
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :动作->其他view model
 * version: 1.0
 */
class OtherActionViewModel : BaseViewModel() {

    val mOtherLiveData = MutableLiveData<ArrayList<ActionSkillItemBean<OtherType>>>()

    fun initData(resources: Resources) {
        val list = ArrayList<ActionSkillItemBean<OtherType>>()
        //        if (ConfigManager.isHighConfig()) {
//        list.add(
//            ActionSkillItemBean(
//                resources.getString(R.string.scene_text_edit_add_action_mirror),
//                0,
//                ActionSkillItemBean.SKILL_TITLE,
//                OtherType.ACTION_TITLE,
//                null,
//                OtherType.OTHER_REARVIEW_MIRROR_TYPE
//            )
//        )
//        list.add(
//            ActionSkillItemBean(
//                resources.getString(R.string.scene_text_action_rearview_mirror_switch),
//                R.drawable.scene_icon_me_action_others_rearview_mirror_switch,
//                ActionSkillItemBean.SKILL_CONTENT,
//                OtherType.REARVIEW_MIRROR_SWITCH,
//                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_OTHER_REARVIEW_MIRROR_SWITCH_ID),
//                OtherType.OTHER_REARVIEW_MIRROR_TYPE
//            )
//        )
//        list.add(
//            ActionSkillItemBean(
//                resources.getString(R.string.scene_text_action_rearview_mirror_heating),
//                R.drawable.scene_icon_me_action_others_rearview_mirror_heating,
//                ActionSkillItemBean.SKILL_CONTENT,
//                OtherType.REARVIEW_MIRROR_HEATING,
//                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_OTHER_REARVIEW_MIRROR_HEATING_OPEN_ID),
//                OtherType.OTHER_REARVIEW_MIRROR_TYPE
//            )
//        )
        //        }

        //		消息中心lk1a不在其他分类
        //		list.add(
        //			ActionSkillItemBean(
        //				resources.getString(R.string.scene_text_action_msg_center),
        //				0,
        //				ActionSkillItemBean.SKILL_TITLE,
        //				OtherType.MSG_CENTER, null,
        //				OtherType.OTHER_MESSAGE_CENTER_TYPE
        //			)
        //		)
        //		list.add(
        //			ActionSkillItemBean(
        //				resources.getString(R.string.scene_text_action_text_short_prompt),
        //				R.drawable.scene_icon_text_short_prompt,
        //				ActionSkillItemBean.SKILL_CONTENT,
        //				OtherType.TEXT_SHORT_PROMPT,
        //				arrayListOf(SkillsListConstant.SKILL_ID_APP_NOTIFY),
        //				OtherType.OTHER_MESSAGE_CENTER_TYPE
        //			)
        //		)

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_common_action_delay),
                0,
                ActionSkillItemBean.SKILL_TITLE,
                OtherType.ACTION_TITLE,
                null,
                OtherType.OTHER_DELAY_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_common_action_delay),
                R.drawable.scene_icon_me_action_delay,
                ActionSkillItemBean.SKILL_CONTENT,
                OtherType.DELAY,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_RECOMMEND_DELAY_ID),
                OtherType.OTHER_DELAY_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_screen_off),
                0,
                ActionSkillItemBean.SKILL_TITLE,
                OtherType.ACTION_TITLE,
                null,
                OtherType.SCREEN_OFF_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_screen_off),
                R.drawable.scene_icon_me_action_others_extinguish,
                ActionSkillItemBean.SKILL_CONTENT,
                OtherType.SCREEN_OFF,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_OTHER_SCREEN_OFF),
                OtherType.SCREEN_OFF_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_screen_protect),
                0,
                ActionSkillItemBean.SKILL_TITLE,
                OtherType.ACTION_TITLE,
                null,
                OtherType.SCREEN_PROTECT_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_screen_protect),
                R.drawable.scene_icon_me_action_delay_screen_saver,
                ActionSkillItemBean.SKILL_CONTENT,
                OtherType.SCREEN_PROTECT,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_OTHER_SCREEN_PROTECT),
                OtherType.SCREEN_PROTECT_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_wireless_charger),
                0,
                ActionSkillItemBean.SKILL_TITLE,
                OtherType.ACTION_TITLE,
                null,
                OtherType.WIRELESS_CHARGER_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_wireless_charger),
                R.drawable.scene_icon_me_action_others_wirelesscharging,
                ActionSkillItemBean.SKILL_CONTENT,
                OtherType.WIRELESS_CHARGER,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_OTHER_WIRELESS_CHARGER),
                OtherType.WIRELESS_CHARGER_TYPE
            )
        )
        val type = SceneEditManager.ActionType.OTHER
        val result = SceneEditManager.checkActionItemIsActive(type, list)
        mOtherLiveData.postValue(result)
    }


    fun getActionData(type: OtherType): ArrayList<ScenarioInfo.Sequence> {
        val list = ArrayList<ScenarioInfo.Sequence>()
        val args = ArrayList<InputArgInfo>()
        when (type) {
            OtherType.REARVIEW_MIRROR_SWITCH -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_OTHER_REARVIEW_MIRROR_SWITCH_NAME, ArgType.INT32, "0"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_OTHER_REARVIEW_MIRROR_SWITCH_ID, "", args
                        )
                    )
                )
            }

            OtherType.REARVIEW_MIRROR_HEATING -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_OTHER_REARVIEW_MIRROR_HEATING_NAME, ArgType.INT32, "0"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_OTHER_REARVIEW_MIRROR_HEATING_OPEN_ID, "", args
                        )
                    )
                )
            }

            OtherType.SCREEN_OFF -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "0"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_OTHER_SCREEN_OFF,
                            "",
                            args
                        )
                    )
                )
            }

            OtherType.SCREEN_PROTECT -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "0"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_OTHER_SCREEN_PROTECT,
                            "",
                            args
                        )
                    )
                )
            }
            OtherType.WIRELESS_CHARGER -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.UINT8, "0"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_OTHER_WIRELESS_CHARGER,
                            "",
                            args
                        )
                    )
                )
            }

            else -> {}
        }

        return list
    }

    fun getListData(resources: Resources, type: OtherType): List<CheckListBean> {
        return when (type) {
            OtherType.REARVIEW_MIRROR_SWITCH -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_action_spread), true),
                    CheckListBean(resources.getString(R.string.scene_text_action_fold), false),
                )
            }

            else -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_common_open), true),
                    CheckListBean(resources.getString(R.string.scene_text_common_close), false),
                )
            }
        }
    }

    fun getInputArgIntValue(type: OtherType): List<Int> {
        return when (type) {
            OtherType.REARVIEW_MIRROR_HEATING, OtherType.SCREEN_PROTECT, OtherType.SCREEN_OFF -> {
                arrayListOf(0, 1)
            }

            else -> {
                arrayListOf(1, 0)
            }
        }
    }
}