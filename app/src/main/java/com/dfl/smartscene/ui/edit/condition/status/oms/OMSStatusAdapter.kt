package com.dfl.smartscene.ui.edit.condition.status.oms

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.android.common.util.MatchableAdapter
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.StatusOMS

/**
 *Created by 钟文祥 on 2025/1/2.
 *Describer:
 */
class OMSStatusAdapter : BaseQuickAdapter<ConditionItemBean<StatusOMS>, BaseViewHolder>(
    R.layout.scene_recycle_item_edit_condition_dialog_item
), MatchableAdapter<ConditionItemBean<StatusOMS>> {

    override fun convert(holder: BaseViewHolder, item: ConditionItemBean<StatusOMS>) {
        holder.setText(R.id.tv_condition_reflex_name, item.conditionName)
        holder.setImageResource(R.id.iv_condition_reflex_icon, item.conditionIcon)
        if (item.isEnable == false) {
            holder.itemView.alpha = 0.5f
        } else {
            holder.itemView.alpha = 1f
        }
    }

    override val adapterData: List<ConditionItemBean<StatusOMS>>
        get() = data

    override fun findPositionByTitle(title: String): Int {
        return data.indexOfFirst { it.conditionName == title && !it.conditionSkillId.isNullOrEmpty() }
    }
}