package com.dfl.smartscene.ui.overlay.apply

import android.annotation.SuppressLint
import android.content.res.Configuration
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import android.widget.Toast
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.dfl.android.common.base.BaseDialogFragment
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.common.util.CommonToastUtils
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.common.util.EncodeUtils
import com.dfl.android.commonlib.KeyboardUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.communication.SearchAddressManager
import com.dfl.smartscene.databinding.SceneDialogFragmentNavigationBinding
import com.dfl.smartscene.ui.edit.action.apply.adapter.SearchAddressAdapter2a
import com.dfl.smartscene.util.UIUtils
import com.dfl.soacenter.communication.NaviManager
import com.dfl.soacenter.entity.PoiItem
import com.dfl.soacenter.entity.ReqPlanRoute
import com.dfl.soacenter.entity.RoutePoi
import com.google.gson.Gson
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo
import com.jeremyliao.liveeventbus.LiveEventBus
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import me.jessyan.autosize.utils.AutoSizeUtils
import org.greenrobot.eventbus.EventBus
import java.lang.ref.WeakReference

/**
 *Created by 钟文祥 on 2025/2/25.
 *Describer: 发起导航的弹框 (lk2a 新的)
 */
@Suppress("INVISIBLE_SETTER_FROM_DERIVED")
class NavigationActionDialogFragment(
    private var curSequence: ScenarioInfo.Sequence?
) : BaseDialogFragment(), View.OnClickListener, View.OnFocusChangeListener {

    private lateinit var viewBinging: SceneDialogFragmentNavigationBinding

    /**对话框包含的skillId*/
    private var mSkillIdList: ArrayList<Int>? = null

    /**收藏点卡片adapter*/
    private var mFavoritesAdapter: NavigationFavoritesAdapter? = null

    /**被选中的收藏点 (家与公司等）*/
    private var selectPoint: RoutePoi? = null

    /**收藏点数据列表*/
    private var mFavoritesList: MutableList<RoutePoi>? = mutableListOf()

    /**是否搜索结果列表选择到输入框成功*/
    private var isSearchInputSuccess = false

    /**目的地*/
    private var endPoiInfo: PoiItem? = null

    /**途径点*/
    private var midPoiInfo: PoiItem? = null

    /**搜索结果列表adapter*/
    private var mSearchAdapter: SearchAddressAdapter2a? = null

    /**选择哪个输入了，默认目的地，不是点击哪个输入框*/
    private var mInputSelectType: InputSelectType = InputSelectType.End

    /**是否滑动列表的时候隐藏输入法*/
    private var scrollNoHideSoft = false

    /**选择哪个输入枚举类*/
    enum class InputSelectType {
        /**没有选择*/
        None,

        /**目的地*/
        End,

        /**途径地*/
        Mid
    }

    /**输入法IME选项*/
    private val imeOptions: Int = EditorInfo.IME_ACTION_SEARCH

    /**是否是选择家与公司模式 ，默认是*/
    private var isSelectHomeCompany = true


    companion object {
        private val SHOW_TAG = Companion::class.java.name
        fun show(
            manager: FragmentManager, skillIdList: ArrayList<Int>?, curSequence: ScenarioInfo.Sequence?
        ) {
            val dialog = NavigationActionDialogFragment(curSequence)
            dialog.mSkillIdList = skillIdList
            dialog.show(manager, SHOW_TAG)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        viewBinging = SceneDialogFragmentNavigationBinding.inflate(inflater, container, false)
        return viewBinging.root
    }

    override fun getLayoutId(): Int {
        return R.layout.scene_dialog_fragment_navigation
    }

    override fun initView(view: View) {
        setCanceledOnTouchOutside(true)
        viewBinging.tvTitle.text = getString(R.string.scene_text_action_initiate_navigation)
        checkSkillIdAllow()

        initLiveEventBus()
        initListener()
        addEditChangeListener()
        initRecycleView()
        initViewData()

        val clEndAddressParams = viewBinging.clEndAddress.layoutParams as ViewGroup.MarginLayoutParams
        clEndAddressParams.width = AutoSizeUtils.dp2px(requireContext(), 164f) // 恢复原始宽度
        clEndAddressParams.height = AutoSizeUtils.dp2px(requireContext(), 68f) // 恢复原始高度
        clEndAddressParams.marginStart = 45
        viewBinging.clEndAddress.layoutParams = clEndAddressParams
    }

    /**根据skillID控制ui显示*/
    private fun checkSkillIdAllow() {
        mSkillIdList?.let {
            if (!it.contains(SkillsListConstant.SKILL_ID_ACTION_NAV_HOME_AND_COMPANY) && !it.contains(SkillsListConstant.SKILL_ID_ACTION_NAV_GO_CHARGE)) {
                viewBinging.rvFavorites.visibility = View.GONE
            }
            if (!it.contains(SkillsListConstant.SKILL_ID_ACTION_NAV_GO_SOME_WHERE)) {
                viewBinging.clEndAddress.visibility = View.GONE
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initLiveEventBus() {
        LiveEventBus.get<Int>(GlobalLiveEventConstants.KEY_GET_DATA_ERROR_CALLBACK).observe(viewLifecycleOwner) {
            CommonToastUtils.show(R.string.scene_toast_common_get_data_error)
        }

        LiveEventBus.get<List<PoiItem>>(GlobalLiveEventConstants.KEY_SEARCH_ADDRESS_RESULT_CALLBACK)
            .observe(viewLifecycleOwner) {
                if (it.isNotEmpty()) {
                    //出现异常数据地址名为空还有返回列表的情况直接进行数据加载前的拦截
                    if (it[0].name.isNullOrEmpty()) {
                        CommonToastUtils.show(R.string.scene_toast_common_get_data_error)
                        return@observe
                    }
                    //筛选过滤掉，经纬度都为0的情况,否则无法正常发起导航
                    val filterList = ArrayList<PoiItem>()
                    for (poiItem in it) {
                        if (poiItem.point?.lat != 0.toDouble() && poiItem.point?.lon != 0.toDouble()) {
                            filterList.add(poiItem)
                        }
                    }
                    val keyWord = if (mInputSelectType == InputSelectType.End) {
                        viewBinging.etEditInputEnd.text.toString()
                    } else {
                        viewBinging.etEditInputMid.text.toString()
                    }
                    mSearchAdapter?.data = ArrayList(filterList)
                    mSearchAdapter?.setKeyWord(keyWord)

                    val layoutParams = viewBinging.rvSearch.layoutParams as ConstraintLayout.LayoutParams
                    layoutParams.topMargin =
                        AutoSizeUtils.dp2px(context, if (mInputSelectType == InputSelectType.End) 294f else 416f)
                    viewBinging.rvSearch.layoutParams = layoutParams

                    viewBinging.rvSearch.visibility = View.VISIBLE
                    mSearchAdapter?.notifyDataSetChanged()
                    scrollNoHideSoft = true
                    viewBinging.rvSearch.scrollToPosition(0) //恢复初始化位置
                }
            }
    }

    private fun initListener() {
        viewBinging.ivBack.setOnClickListener(this)
        viewBinging.includeDialogBottom.btnPrimary.setOnClickListener(this)
        viewBinging.includeDialogBottom.btnNormal.setOnClickListener(this)

        viewBinging.tvMidAddress.setOnClickListener(this) //途经点按钮
        viewBinging.ivWaysideAddressClose.setOnClickListener(this) //途经点 隐藏
        viewBinging.ivCloseEndAddress.setOnClickListener(this) //目标点 隐藏

    }

    /**输入法相关Listener*/
    @SuppressLint("ClickableViewAccessibility")
    private fun addEditChangeListener() {
        //9月ota 定义搜索框有输入内容时，键盘【搜索】按钮高亮可点击，点击后退出键盘（因导航弹窗的特殊性，搜索推荐页即为搜索结果页，因此点击【搜索】后界面无变化）
        UIUtils.setKeyBroadInputMinIfNotEnable(viewBinging.etEditInputEnd, 1)
        UIUtils.setKeyBroadInputMinIfNotEnable(viewBinging.etEditInputMid, 1)

        //目标点 焦点监听
        viewBinging.etEditInputEnd.onFocusChangeListener = this

        //键盘事件
        viewBinging.etEditInputEnd.imeOptions = imeOptions
        viewBinging.etEditInputMid.imeOptions = imeOptions
        //键盘确定按钮事件
        viewBinging.etEditInputEnd.setOnEditorActionListener(MyOnEditorActionListener(this@NavigationActionDialogFragment))
        viewBinging.etEditInputMid.setOnEditorActionListener(MyOnEditorActionListener(this@NavigationActionDialogFragment))

        //目标点 文字改变,搜索地址
        viewBinging.etEditInputEnd.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            private var curTime1 = System.currentTimeMillis()
            private val antiShakeTime: Long = 400 //防抖动时间 毫秒

            @SuppressLint("NotifyDataSetChanged")
            override fun afterTextChanged(s: Editable?) {
                if (!isSearchInputSuccess) {
                    val curTime2 = System.currentTimeMillis()

                    val job = lifecycleScope.launch(Dispatchers.IO) {
                        delay(antiShakeTime)
                        withContext(Dispatchers.Main) {
                            selectPoint = null
                            endPoiInfo = null
                            mInputSelectType = InputSelectType.End

                            updateTvConfirm4Search()
                            val content = viewBinging.etEditInputEnd.text.toString()
                            if (content.isNotEmpty()) {
                                SearchAddressManager.sendKeyWordToNavi(content)
                            } else {
                                viewBinging.rvSearch.visibility = View.GONE
                            }
                        }
                    }
                    if (curTime2 - curTime1 < antiShakeTime) {
                        job.cancel()
                    }
                    curTime1 = System.currentTimeMillis()
                } else {
                    isSearchInputSuccess = false
                }
            }
        })
        //途经点 文字变化 ，搜索文字
        viewBinging.etEditInputMid.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            private var curTime1 = System.currentTimeMillis()
            private val antiShakeTime: Long = 400 //防抖动时间 毫秒

            override fun afterTextChanged(s: Editable?) {
                if (viewBinging.clMidAddress.visibility == View.VISIBLE) { //有途径地的情况下
                    if (!isSearchInputSuccess) {

                        val curTime2 = System.currentTimeMillis()

                        val job = lifecycleScope.launch(Dispatchers.IO) {
                            delay(antiShakeTime)
                            withContext(Dispatchers.Main) {
                                midPoiInfo = null
                                mInputSelectType = InputSelectType.Mid

                                updateTvConfirm4Search()
                                val content = viewBinging.etEditInputMid.text.toString()
                                if (content.isNotEmpty()) {
                                    SearchAddressManager.sendKeyWordToNavi(content)
                                }
                            }
                        }
                        if (curTime2 - curTime1 < antiShakeTime) {
                            job.cancel()
                        }
                        curTime1 = System.currentTimeMillis()


                    } else {
                        isSearchInputSuccess = false
                    }
                } else { //点击 途经点大叉都会进来
                    if (mInputSelectType == InputSelectType.End) {
                        selectPoint = null
                        endPoiInfo = midPoiInfo
                        midPoiInfo = null
                    }
                    mInputSelectType = InputSelectType.None
                }
            }
        })

    }

    private fun initRecycleView() {
        UIUtils.initRecyclerView(
            activity, viewBinging.rvFavorites, GridLayoutManager(activity, 2), false
        )
        mFavoritesAdapter = NavigationFavoritesAdapter()
        mFavoritesAdapter?.setOnItemClickListener { adapter, view, position ->
            if (mFavoritesList?.get(position)?.isEnable == false) {
                return@setOnItemClickListener
            }


            isSelectHomeCompany = false
            viewBinging.rvFavorites.visibility = View.GONE
            
            // 更新tv_mid_address的可见性
            updateTvMidAddressVisibility()
            
            mInputSelectType = InputSelectType.End

            isSearchInputSuccess = true
            selectPoint = mFavoritesList?.get(position)
            endPoiInfo = null
            midPoiInfo = null
            viewBinging.etEditInputEnd.setText(selectPoint?.name)
            viewBinging.etEditInputEnd.clearFocus()
            updateTvConfirm()
            dialog?.window?.let {
                KeyboardUtils.hideSoftInput(it)
            }

//            val lastSelected = mFavoritesAdapter?.mSelectedIndex
//            if (position == lastSelected) { //点击相同项
//                mFavoritesAdapter?.mSelectedIndex = -1
//                lastSelected.let {
//                    mFavoritesAdapter?.notifyItemChanged(it)
//                }
//                selectPoint = null
//                updateTvConfirm()
//            } else { //点击不同项
//                mFavoritesAdapter?.mSelectedIndex = position
//
//                lastSelected?.let {
//                    mFavoritesAdapter?.notifyItemChanged(it)
//                }
//                mFavoritesAdapter?.mSelectedIndex?.let {
//                    mFavoritesAdapter?.notifyItemChanged(it)
//                }
//                selectPoint = mFavoritesList?.get(position)
//                updateTvConfirm()
//            }
        }
        viewBinging.rvFavorites.adapter = mFavoritesAdapter

        UIUtils.initRecyclerView(
            activity, viewBinging.rvSearch, LinearLayoutManager(activity), false
        )
        mSearchAdapter = SearchAddressAdapter2a()
        viewBinging.rvSearch.adapter = mSearchAdapter
        //滑动事件
        viewBinging.rvSearch.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (scrollNoHideSoft) {
                    scrollNoHideSoft = false
                    return
                }
                dialog?.window?.let {
                    KeyboardUtils.hideSoftInput(it)
                }
            }
        })

        //列表点击事件
        mSearchAdapter?.setOnItemClickListener { _, _, position ->
            mSearchAdapter?.data?.let {
                viewBinging.rvSearch.visibility = View.GONE

                isSearchInputSuccess = true
                if (mInputSelectType == InputSelectType.End) {
                    selectPoint = null
                    endPoiInfo = it[position]
                    viewBinging.etEditInputEnd.setText(endPoiInfo?.name)
                    if (viewBinging.clMidAddress.visibility == View.VISIBLE && viewBinging.etEditInputMid.text.toString()
                            .isEmpty()
                    ) { //途经点显示为空，途经点需要焦点
                        viewBinging.etEditInputEnd.clearFocus()
                        mInputSelectType = InputSelectType.Mid
                        viewBinging.etEditInputMid.requestFocus()
                        updateTvConfirm()
                    } else { //焦点一直在目标点
                        mInputSelectType = InputSelectType.End
                        viewBinging.etEditInputEnd.clearFocus()
                        //                        viewBinging.etEditInputEnd.setSelection(viewBinging.etEditInputEnd.length())
                        updateTvConfirm()
                        dialog?.window?.let {
                            KeyboardUtils.hideSoftInput(it)
                        }
                    }
                } else if (mInputSelectType == InputSelectType.Mid) {
                    midPoiInfo = it[position]
                    viewBinging.etEditInputMid.setText(midPoiInfo?.name)
                    if (viewBinging.etEditInputEnd.text.toString().isEmpty()) { //目标地为空，需要焦点
                        viewBinging.etEditInputMid.clearFocus()
                        mInputSelectType = InputSelectType.End
                        viewBinging.etEditInputEnd.requestFocus()
                        updateTvConfirm()
                    } else { //焦点一直在途径点
                        mInputSelectType = InputSelectType.Mid
                        viewBinging.etEditInputMid.clearFocus()
                        //                        viewBinging.etEditInputMid.setSelection(viewBinging.etEditInputMid.length())
                        updateTvConfirm()
                        dialog?.window?.let {
                            KeyboardUtils.hideSoftInput(it)
                        }
                    }
                }
            }
        }
    }

    private fun initViewData() {
        mFavoritesList?.add(
            RoutePoi(
                getString(R.string.scene_text_action_go_home),
                getString(R.string.scene_text_location_favorites_dialog_empty_view2),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                isEnable = false
            )
        )
        mFavoritesList?.add(
            RoutePoi(
                getString(R.string.scene_text_action_back_to_company),
                getString(R.string.scene_text_location_favorites_dialog_empty_view2),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                isEnable = false
            )
        )
        mFavoritesAdapter?.setList(mFavoritesList)
        updateTvConfirm()
        getFavorites()
    }

    /**从导航获取收藏夹数据*/
    private fun getFavorites() {
        //获取服务
        lifecycleScope.launch(Dispatchers.IO) {
            //同一soa方法需要串行
            val resHomeListBean = async {
                NaviManager.getFavoriteList(1)
            }.await()
            val resCompanyListBean = async {
                NaviManager.getFavoriteList(2)
            }.await()

            if ((resHomeListBean?.isSuccess() == false || resHomeListBean == null) || (resCompanyListBean?.isSuccess() == false || resCompanyListBean == null)) {
                CommonToastUtils.show(R.string.scene_toast_common_get_data_error)
                return@launch
            }

            mFavoritesList?.clear()
            if (resHomeListBean?.isSuccess() == true && resHomeListBean?.protocolFavPoiInfos?.size != 0) {
                val homeBean = resHomeListBean.protocolFavPoiInfos?.get(0)
                mFavoritesList?.add(
                    RoutePoi(
                        getString(R.string.scene_text_action_go_home), homeBean?.favoriteName,
                        homeBean?.lon ?: 0.0,
                        homeBean?.lat ?: 0.0,
                        homeBean?.itemId, homeBean?.itemId, homeBean?.itemId,
                    )
                )
            } else {
                mFavoritesList?.add(
                    RoutePoi(
                        getString(R.string.scene_text_action_go_home),
                        getString(R.string.scene_text_location_favorites_dialog_empty_view2),
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        isEnable = false
                    )
                )
            }
            if (resCompanyListBean?.isSuccess() == true && resCompanyListBean?.protocolFavPoiInfos?.size != 0) {
                val companyListBean = resCompanyListBean.protocolFavPoiInfos?.get(0)
                mFavoritesList?.add(
                    RoutePoi(
                        getString(R.string.scene_text_action_back_to_company),
                        companyListBean?.favoriteName,
                        companyListBean?.lon ?: 0.0,
                        companyListBean?.lat ?: 0.0,
                        companyListBean?.itemId,
                        companyListBean?.itemId,
                        companyListBean?.itemId,
                    )
                )
            } else {
                mFavoritesList?.add(
                    RoutePoi(
                        getString(R.string.scene_text_action_back_to_company),
                        getString(R.string.scene_text_location_favorites_dialog_empty_view2),
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        isEnable = false
                    )
                )
            }

            withContext(Dispatchers.Main) {
                //改为默认不选中
//                val currentIndex = initCurrentCheckView()
//                mFavoritesAdapter?.mSelectedIndex = currentIndex
//                if (currentIndex >= 0) {
//                    selectPoint = mFavoritesList?.get(currentIndex)
//                }
                mFavoritesAdapter?.setList(mFavoritesList)
                updateTvConfirm()
            }
        }
    }

    /**根据传进来的数据 初始化 哪个被选择*/
    private fun initCurrentCheckView(): Int {
        var curSelectIndex = -1
        run breaking@{
            mFavoritesList?.forEachIndexed { index, routePoi ->
                if (routePoi.isEnable == true) {
                    curSelectIndex = index
                    return@breaking //退出循环
                }
            }
        }

        curSequence?.let { curSequence ->
            if (curSequence.action.skillId == SkillsListConstant.SKILL_ID_ACTION_NAV_HOME_AND_COMPANY) {
                curSequence.action.input?.get(0)?.let { item ->
                    if (item.value == "2") { //公司
                        curSelectIndex = 1
                    } else { //家
                        curSelectIndex = 0
                    }
                }
            }
            if (curSequence.action.skillId == SkillsListConstant.SKILL_ID_ACTION_NAV_GO_CHARGE) {
                curSequence.action.input?.get(0)?.let { item ->
                    if (item.value == "1") { //去充电
                        curSelectIndex = 2
                    }
                }
            }
        }
        return curSelectIndex
    }


    /**键盘确定按钮事件*/
    private class MyOnEditorActionListener(dialogFragment: NavigationActionDialogFragment) :
        TextView.OnEditorActionListener {
        private var reference: WeakReference<NavigationActionDialogFragment> =
            WeakReference<NavigationActionDialogFragment>(dialogFragment)

        //键盘确定事件
        override fun onEditorAction(v: TextView?, actionId: Int, event: KeyEvent?): Boolean {
            reference.get()?.let {
                if (actionId == it.imeOptions) {
//                    if (it.isAllow || (event?.action == KeyEvent.ACTION_DOWN && event.keyCode == KeyEvent.KEYCODE_ENTER)) {
//                        it.handleConfirm4Search()
//                    }
                }
            }
            return true
        }
    }


    /**改变确定按钮的形态——总*/
    private fun updateTvConfirm() {
        if (isSelectHomeCompany) {
            viewBinging.ivBack.visibility = View.GONE
            viewBinging.includeDialogBottom.btnPrimary.isEnabled = false
        } else {
            viewBinging.ivBack.visibility = View.VISIBLE
            if (selectPoint != null) {
                viewBinging.includeDialogBottom.btnPrimary.isEnabled = true
            } else {
                updateTvConfirm4Search()
            }
        }
    }

    /**改变确定按钮的形态*/
    private fun updateTvConfirm4Search() {
        var isAllow1 = false
        if (viewBinging.clMidAddress.visibility == View.VISIBLE) { //途经点显示时，需要两个有地址才能有效
            if (endPoiInfo != null && midPoiInfo != null) {
                isAllow1 = true
            }
        } else {
            if (endPoiInfo != null) {
                isAllow1 = true
            }
        }
        viewBinging.includeDialogBottom.btnPrimary.isEnabled = isAllow1
    }


    //回调 点击事件 按钮事件 OnClickListener
    @SuppressLint("NotifyDataSetChanged")
    override fun onClick(v: View?) {
        if (!DebouncingUtils.isValid(v!!)) return
        when (v.id) {
            R.id.tv_mid_address -> { //途经点按钮
                viewBinging.tvMidAddress.visibility = View.GONE
                viewBinging.clMidAddress.visibility = View.VISIBLE
                viewBinging.ivCloseEndAddress.visibility = View.VISIBLE
                viewBinging.clEndAddress.setBackgroundColor(resources.getColor(R.color.transparent))

                // 使用ConstraintLayout.LayoutParams而不是ViewGroup.MarginLayoutParams
                val clEndAddressParams = viewBinging.clEndAddress.layoutParams as ConstraintLayout.LayoutParams
                val clMidAddressParams = viewBinging.clMidAddress.layoutParams as ConstraintLayout.LayoutParams
                
                // 设置cl_mid_address与cl_end_address相同的宽高
                clMidAddressParams.width = clEndAddressParams.width
                clMidAddressParams.height = clEndAddressParams.height
                
                // 设置相同的marginStart，确保头对头对齐
                clMidAddressParams.marginStart = clEndAddressParams.marginStart
                
                // 确保约束关系正确
                clMidAddressParams.startToStart = clEndAddressParams.startToStart
                clMidAddressParams.endToEnd = clEndAddressParams.endToEnd

                // 应用布局参数
                viewBinging.clMidAddress.layoutParams = clMidAddressParams
                
                // 立即调整et_edit_input_end和ll_end_address的宽度为944dp
                val llEndAddressParams = viewBinging.llEndAddress.layoutParams as ViewGroup.MarginLayoutParams
                llEndAddressParams.width = AutoSizeUtils.dp2px(requireContext(), 944f)
                viewBinging.llEndAddress.layoutParams = llEndAddressParams
                
                val etEditInputEndParams = viewBinging.etEditInputEnd.layoutParams
                etEditInputEndParams.width = AutoSizeUtils.dp2px(requireContext(), 944f)
                viewBinging.etEditInputEnd.layoutParams = etEditInputEndParams
                
                // 强制刷新布局，确保清除图标位置正确更新
                viewBinging.etEditInputEnd.requestLayout()
                viewBinging.llEndAddress.requestLayout()
                
                viewBinging.clMidAddress.setPadding(
                    viewBinging.clMidAddress.paddingStart,
                    viewBinging.clMidAddress.paddingTop,
                    viewBinging.clMidAddress.paddingEnd,
                    viewBinging.clMidAddress.paddingBottom
                )

                updateTvConfirm() //updateTvConfirm此处要先于showSoftInput
                mInputSelectType = InputSelectType.Mid
                showSoftInput()
            }

            R.id.iv_wayside_address_close -> { //途经点大叉删除
                viewBinging.clEndAddress.setBackgroundResource(R.drawable.scene_shape_common_bg_2_r12)
                mInputSelectType = InputSelectType.Mid
                midPoiInfo = null
                closeWayside()
                updateTvConfirm()
            }

            R.id.iv_close_end_address -> { //目标点大叉删除，将途径内容给目标地

                viewBinging.clEndAddress.setBackgroundResource(R.drawable.scene_shape_common_bg_2_r12)
                mInputSelectType = InputSelectType.End
                viewBinging.ivCloseEndAddress.visibility = View.GONE

                val temp = viewBinging.etEditInputMid.text.toString()
                closeWayside()
                isSearchInputSuccess = true
                viewBinging.etEditInputEnd.setText(temp)
                viewBinging.etEditInputEnd.clearFocus()
                updateTvConfirm()
            }

            R.id.iv_back -> { //返回
                //搜索模式 -> 选家 公司模式
                isSelectHomeCompany = true

                viewBinging.rvFavorites.visibility = View.VISIBLE

                // 隐藏iv_back
                viewBinging.ivBack.visibility = View.GONE

                // 恢复tv_title的显示
                viewBinging.tvTitle.visibility = View.VISIBLE

                // 恢复cl_end_address的原始宽高和marginStart
                val clEndAddressParams = viewBinging.clEndAddress.layoutParams as ViewGroup.MarginLayoutParams
                clEndAddressParams.width = AutoSizeUtils.dp2px(requireContext(), 164f) // 恢复原始宽度
                clEndAddressParams.height = AutoSizeUtils.dp2px(requireContext(), 68f) // 恢复原始高度
                clEndAddressParams.marginStart = 45
                viewBinging.clEndAddress.layoutParams = clEndAddressParams

                // 恢复cl_mid_address的marginStart
//                val clMidAddressParams = viewBinging.clMidAddress.layoutParams as ViewGroup.MarginLayoutParams
//                clMidAddressParams.marginStart = 0 // 恢复原始marginStart为0
//                viewBinging.clMidAddress.layoutParams = clMidAddressParams

                // 恢复ll_end_address的原始宽高和marginStart
                val llEndAddressParams = viewBinging.llEndAddress.layoutParams as ViewGroup.MarginLayoutParams
                llEndAddressParams.width = AutoSizeUtils.dp2px(requireContext(), 164f) // 恢复原始宽度
                llEndAddressParams.height = AutoSizeUtils.dp2px(requireContext(), 68f) // 恢复原始高度
                llEndAddressParams.marginStart = 0 // 移除marginStart，恢复为0
                viewBinging.llEndAddress.layoutParams = llEndAddressParams

                // 恢复iv_input_end的原始marginStart
                val ivInputEndParams = viewBinging.ivInputEnd.layoutParams as ViewGroup.MarginLayoutParams
                ivInputEndParams.marginStart = AutoSizeUtils.dp2px(requireContext(), 34f) // 恢复原始marginStart
                viewBinging.ivInputEnd.layoutParams = ivInputEndParams

                // 恢复et_edit_input_end的原始宽高和padding
                val etEditInputEndParams = viewBinging.etEditInputEnd.layoutParams
                etEditInputEndParams.width = ViewGroup.LayoutParams.WRAP_CONTENT // 恢复原始宽度
                etEditInputEndParams.height = AutoSizeUtils.dp2px(requireContext(), 68f) // 恢复原始高度
                viewBinging.etEditInputEnd.layoutParams = etEditInputEndParams
                viewBinging.etEditInputEnd.setPadding(
                    AutoSizeUtils.dp2px(requireContext(), 16f), // 恢复原始paddingStart
                    viewBinging.etEditInputEnd.paddingTop,
                    AutoSizeUtils.dp2px(requireContext(), 16f),
                    viewBinging.etEditInputEnd.paddingBottom
                )

                viewBinging.clMidAddress.visibility = View.GONE //途经点
                viewBinging.ivCloseEndAddress.visibility = View.GONE //目标点的隐藏
                
                // 更新tv_mid_address的可见性
                updateTvMidAddressVisibility()

                viewBinging.etEditInputEnd.hint = getString(R.string.scene_text_select_points)
                viewBinging.etEditInputEnd.setHintTextColor(ContextCompat.getColor(requireContext(), R.color.scene_color_text_1))

                mSearchAdapter?.data = ArrayList()
                mSearchAdapter?.notifyDataSetChanged()
                viewBinging.rvSearch.visibility = View.GONE

                //---固定整体  start--
                isSearchInputSuccess = true
                selectPoint = null
                endPoiInfo = null
                midPoiInfo = null
                viewBinging.etEditInputEnd.setText("")
                viewBinging.etEditInputMid.setText("")

                viewBinging.etEditInputEnd.clearFocus()
                viewBinging.etEditInputMid.clearFocus()
                updateTvConfirm()
                dialog?.window?.let {
                    KeyboardUtils.hideSoftInput(it)
                }
                //---固定整体  end--
            }

            R.id.btn_normal -> { //取消
                dismissWithAnimate()
            }

            R.id.btn_primary -> { //确定
                if (isSelectHomeCompany) {
//                    handleConfirm() //已不进入
                } else {
                    if (selectPoint != null && midPoiInfo == null) { //去选点
                        Log.e("erkltj", "onClick: 选点")
                        handleConfirmGoSelect()
                    } else if (selectPoint != null && midPoiInfo != null) { //选点+途经点
                        Log.e("erkltj", "onClick: 选点+途经点")
                        handleConfirmGoSelectAndMid()
                    } else {
                        Log.e("erkltj", "onClick: 去某点")
                        handleConfirm4Search()
                    }
                }
            }
        }
    }


    /**回调 目的地输入框焦点监听  onFocusChangeListener*/
    override fun onFocusChange(v: View?, hasFocus: Boolean) {
        if (hasFocus) {
            isSelectHomeCompany = false
            viewBinging.rvFavorites.visibility = View.GONE

            // 同步显示iv_back
            viewBinging.ivBack.visibility = View.VISIBLE

            // 同步隐藏tv_title
            viewBinging.tvTitle.visibility = View.GONE

            // 首先修改父容器cl_end_address的尺寸
            val clEndAddressParams = viewBinging.clEndAddress.layoutParams as ViewGroup.MarginLayoutParams
            clEndAddressParams.width = AutoSizeUtils.dp2px(requireContext(), 1028f)
            clEndAddressParams.height = AutoSizeUtils.dp2px(requireContext(), 76f)
            clEndAddressParams.marginStart = AutoSizeUtils.dp2px(requireContext(), 124f) // 添加marginStart 124dp
            viewBinging.clEndAddress.layoutParams = clEndAddressParams

            val clMidAddressParams = viewBinging.clMidAddress.layoutParams as ViewGroup.MarginLayoutParams
            clMidAddressParams.marginStart = AutoSizeUtils.dp2px(requireContext(), 124f)
            viewBinging.clMidAddress.layoutParams = clMidAddressParams

            // 修改ll_end_address的宽高，根据是否点击了tv_mid_address来决定宽度
            val llEndAddressParams = viewBinging.llEndAddress.layoutParams as ViewGroup.MarginLayoutParams
            
            // 判断是否点击了tv_mid_address（通过cl_mid_address的可见性判断）
            if (viewBinging.clMidAddress.visibility == View.VISIBLE) {
                // 点击了tv_mid_address，宽度设为944
                llEndAddressParams.width = AutoSizeUtils.dp2px(requireContext(), 944f)
            } else {
                // 未点击tv_mid_address，宽度设为1028
                llEndAddressParams.width = AutoSizeUtils.dp2px(requireContext(), 1028f)
            }
            
            llEndAddressParams.height = AutoSizeUtils.dp2px(requireContext(), 76f)
            viewBinging.llEndAddress.layoutParams = llEndAddressParams

            // 修改iv_input_end的marginStart为36r
            val ivInputEndParams = viewBinging.ivInputEnd.layoutParams as ViewGroup.MarginLayoutParams
            ivInputEndParams.marginStart = AutoSizeUtils.dp2px(requireContext(), 36f)
            viewBinging.ivInputEnd.layoutParams = ivInputEndParams

            // 修改et_edit_input_end的宽高，根据是否点击了tv_mid_address来决定宽度
            val etEditInputEndParams = viewBinging.etEditInputEnd.layoutParams
            
            // 判断是否点击了tv_mid_address（通过cl_mid_address的可见性判断）
            if (viewBinging.clMidAddress.visibility == View.VISIBLE) {
                // 点击了tv_mid_address，宽度设为944
                etEditInputEndParams.width = AutoSizeUtils.dp2px(requireContext(), 944f)
            } else {
                // 未点击tv_mid_address，宽度设为1028
                etEditInputEndParams.width = AutoSizeUtils.dp2px(requireContext(), 1028f)
            }
            
            etEditInputEndParams.height = AutoSizeUtils.dp2px(requireContext(), 70f)
            viewBinging.etEditInputEnd.layoutParams = etEditInputEndParams
            viewBinging.etEditInputEnd.setPadding(
                AutoSizeUtils.dp2px(requireContext(), 20f), // paddingStart改为20dp
                viewBinging.etEditInputEnd.paddingTop,
                AutoSizeUtils.dp2px(requireContext(), 110f),
                viewBinging.etEditInputEnd.paddingBottom
            )

            // 更新tv_mid_address的可见性
            updateTvMidAddressVisibility()

            viewBinging.etEditInputEnd.hint = getString(R.string.scene_edit_input_hint_destination_address_no_please)
            viewBinging.etEditInputEnd.setHintTextColor(ContextCompat.getColor(requireContext(), R.color.scene_color_text_3))
            updateTvConfirm()
        }
    }

    /**统一管理tv_mid_address的可见性*/
    private fun updateTvMidAddressVisibility() {
        // 当rv_favorites隐藏且cl_mid_address也隐藏时，显示tv_mid_address
        if (viewBinging.rvFavorites.visibility == View.GONE && 
            viewBinging.clMidAddress.visibility == View.GONE) {
            viewBinging.tvMidAddress.visibility = View.VISIBLE
            viewBinging.etEditInputMid.clearFocus()
        } else {
            viewBinging.tvMidAddress.visibility = View.GONE
        }
    }

    /**提交 -选择*/
    private fun handleConfirmGoSelect() {
        val inputArgList = ArrayList<InputArgInfo>()
        var inputArgInfo: InputArgInfo? = null
        var desc = ""
        var skillId: Int = -1

        if (selectPoint?.name == getString(R.string.scene_text_action_go_home)) {
            desc = selectPoint?.name!!
            skillId = SkillsListConstant.SKILL_ID_ACTION_NAV_HOME_AND_COMPANY
            inputArgInfo = InputArgInfo(
                SkillsListConstant.INPUT_ARG_NAV_HOME_COMPANY, ArgType.INT32, "1"
            )
        } else if (selectPoint?.name == getString(R.string.scene_text_action_back_to_company)) {
            desc = selectPoint?.name!!
            skillId = SkillsListConstant.SKILL_ID_ACTION_NAV_HOME_AND_COMPANY
            inputArgInfo = InputArgInfo(
                SkillsListConstant.INPUT_ARG_NAV_HOME_COMPANY, ArgType.INT32, "2"
            )
        } else if (selectPoint?.name == getString(R.string.scene_text_action_go_charge)) {
            desc = selectPoint?.name!!
            skillId = SkillsListConstant.SKILL_ID_ACTION_NAV_GO_CHARGE
            inputArgInfo = InputArgInfo(
                SkillsListConstant.INPUT_ARG_NAV_GO_CHARGE, ArgType.INT32, "1"
            )
        }

        inputArgInfo?.let { inputArgList.add(it) }
        val action = ScenarioInfo.Action(desc, skillId, viewBinging.tvTitle.text.toString(), inputArgList)
        val sequence = ScenarioInfo.Sequence(1, 1, action)
        EventBus.getDefault().post(sequence)
        dismissWithAnimate()
    }

    /**提交 -选择 过时*/
    private fun handleConfirm() {
        val inputArgList = ArrayList<InputArgInfo>()
        var inputArgInfo: InputArgInfo? = null
        var desc = ""
        var skillId: Int = -1

        if (mFavoritesAdapter?.mSelectedIndex == 0) {
            desc = mFavoritesAdapter?.data?.get(mFavoritesAdapter?.mSelectedIndex!!)?.name!!
            skillId = SkillsListConstant.SKILL_ID_ACTION_NAV_HOME_AND_COMPANY
            inputArgInfo = InputArgInfo(
                SkillsListConstant.INPUT_ARG_NAV_HOME_COMPANY, ArgType.INT32, "1"
            )
        } else if (mFavoritesAdapter?.mSelectedIndex == 1) {
            desc = mFavoritesAdapter?.data?.get(mFavoritesAdapter?.mSelectedIndex!!)?.name!!
            skillId = SkillsListConstant.SKILL_ID_ACTION_NAV_HOME_AND_COMPANY
            inputArgInfo = InputArgInfo(
                SkillsListConstant.INPUT_ARG_NAV_HOME_COMPANY, ArgType.INT32, "2"
            )
        } else if (mFavoritesAdapter?.mSelectedIndex == 2) {
            desc = mFavoritesAdapter?.data?.get(mFavoritesAdapter?.mSelectedIndex!!)?.name!!
            skillId = SkillsListConstant.SKILL_ID_ACTION_NAV_GO_CHARGE
            inputArgInfo = InputArgInfo(
                SkillsListConstant.INPUT_ARG_NAV_GO_CHARGE, ArgType.INT32, "1"
            )
        }

        inputArgInfo?.let { inputArgList.add(it) }
        val action = ScenarioInfo.Action(desc, skillId, viewBinging.tvTitle.text.toString(), inputArgList)
        val sequence = ScenarioInfo.Sequence(1, 1, action)
        EventBus.getDefault().post(sequence)
        dismissWithAnimate()
    }

    /**提交 -去某地*/
    private fun handleConfirm4Search() {
        val inputText = viewBinging.etEditInputEnd.text.toString()
        val waySideText = viewBinging.etEditInputMid.text.toString()
        val isAddWaySide = viewBinging.clMidAddress.visibility == View.VISIBLE
        if (waySideText.isEmpty() && isAddWaySide) {
            CommonToastUtils.show(getString(R.string.scene_edit_input_hint_wayside_address))
            return
        } else if (inputText.isEmpty()) {
            CommonToastUtils.show(getString(R.string.scene_edit_input_hint_destination_address))
            return
        }

        val endRouteBean = RoutePoi(
            endPoiInfo?.name,
            endPoiInfo?.addr,
            endPoiInfo?.point?.lon ?: 0.0,
            endPoiInfo?.point?.lat ?: 0.0,
            endPoiInfo?.typeCode,
            endPoiInfo?.adCode,
            endPoiInfo?.pid
        )

        val midPois = if (midPoiInfo != null) {
            val midRouteBean = RoutePoi(
                midPoiInfo?.name,
                midPoiInfo?.addr,
                midPoiInfo?.point?.lon ?: 0.0,
                midPoiInfo?.point?.lat ?: 0.0,
                midPoiInfo?.typeCode,
                midPoiInfo?.adCode,
                midPoiInfo?.pid
            )
            arrayListOf(midRouteBean)
        } else {
            null
        }

        val planRoute = ReqPlanRoute(endPoi = endRouteBean, midPois = midPois)
        val jsonInputData = Gson().toJson(planRoute)

        val desc = if (planRoute.midPois != null) {
            "${planRoute.midPois!![0].name}+${planRoute.endPoi?.name}"
        } else {
            planRoute.endPoi?.name
        }
        val inputArgList = ArrayList<InputArgInfo>()
        val inputArgInfo: InputArgInfo?
        val skillId = SkillsListConstant.SKILL_ID_ACTION_NAV_GO_SOME_WHERE
        val address4Base64 = EncodeUtils.base64Encode2String(jsonInputData.toByteArray())
        inputArgInfo = InputArgInfo(
            SkillsListConstant.INPUT_ARG_ACTION_APP_GO_KEYWORD, ArgType.STRING, address4Base64
        )

        inputArgInfo.let { inputArgList.add(it) }
        val action = ScenarioInfo.Action(
            desc, skillId, viewBinging.tvTitle.text.toString(), inputArgList
        )
        val sequence = ScenarioInfo.Sequence(1, 1, action)
        EventBus.getDefault().post(sequence)
        dialog?.window?.let {
            KeyboardUtils.hideSoftInput(it)
        }
        dismissWithAnimate()
    }


    /**提交 -（回家+回公司）+途经点*/
    private fun handleConfirmGoSelectAndMid() {
        val inputArgList = ArrayList<InputArgInfo>()
        var inputArgInfo: InputArgInfo? = null

        if (selectPoint?.name == getString(R.string.scene_text_action_go_home)) {
            inputArgInfo = InputArgInfo(
                SkillsListConstant.INPUT_ARG_TYPE, ArgType.INT32, "1"
            )
        } else if (selectPoint?.name == getString(R.string.scene_text_action_back_to_company)) {
            inputArgInfo = InputArgInfo(
                SkillsListConstant.INPUT_ARG_TYPE, ArgType.INT32, "2"
            )
        }
        inputArgInfo?.let { inputArgList.add(it) }

        val midRouteBean = RoutePoi(
            midPoiInfo?.name,
            midPoiInfo?.addr,
            midPoiInfo?.point?.lon ?: 0.0,
            midPoiInfo?.point?.lat ?: 0.0,
            midPoiInfo?.typeCode,
            midPoiInfo?.adCode,
            midPoiInfo?.pid
        )
        val jsonMid = Gson().toJson(midRouteBean)
        val base64Mid = EncodeUtils.base64Encode2String(jsonMid.toByteArray())
        inputArgInfo = InputArgInfo(
            SkillsListConstant.INPUT_ARG_ACTION_NAV_GO_SELECT_AND_MID, ArgType.STRING, base64Mid
        )
        inputArgInfo.let { inputArgList.add(it) }

        val skillId: Int = SkillsListConstant.SKILL_ID_ACTION_NAV_GO_SELECT_AND_MID
        val desc = "${midPoiInfo?.name}+${selectPoint?.name}"
        val action = ScenarioInfo.Action(
            desc, skillId, viewBinging.tvTitle.text.toString(), inputArgList
        )
        val sequence = ScenarioInfo.Sequence(1, 1, action)
        EventBus.getDefault().post(sequence)
        dialog?.window?.let {
            KeyboardUtils.hideSoftInput(it)
        }
        dismissWithAnimate()
    }

    //途经点大叉删除
    private fun closeWayside() {
        viewBinging.clMidAddress.visibility = View.GONE //途径地view 隐藏
        viewBinging.ivCloseEndAddress.visibility = View.GONE
        
        // 恢复et_edit_input_end和ll_end_address的宽度为1028dp
        val layoutParams1 = viewBinging.llEndAddress.layoutParams
        layoutParams1.width = AutoSizeUtils.dp2px(requireContext(), 1028f)
        viewBinging.llEndAddress.layoutParams = layoutParams1
        
        val layoutParams2 = viewBinging.etEditInputEnd.layoutParams
        layoutParams2.width = AutoSizeUtils.dp2px(requireContext(), 1028f)
        viewBinging.etEditInputEnd.layoutParams = layoutParams2
        
        // 强制刷新布局
        viewBinging.etEditInputEnd.requestLayout()
        viewBinging.llEndAddress.requestLayout()
        
        // 更新tv_mid_address的可见性
        updateTvMidAddressVisibility()

        isSearchInputSuccess = true
        viewBinging.etEditInputMid.setText("")
        viewBinging.etEditInputMid.clearFocus()
        mInputSelectType = InputSelectType.End

    }

    //显示键盘
    private fun showSoftInput() {
        if (mInputSelectType == InputSelectType.End) {
            KeyboardUtils.showSoftInput(viewBinging.etEditInputEnd)
        } else {
            KeyboardUtils.showSoftInput(viewBinging.etEditInputMid)
        }
    }

    override fun onTouchOutside() {
        super.onTouchOutside()
        dialog?.window?.let {
            KeyboardUtils.hideSoftInput(it)
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        mFavoritesAdapter?.notifyDataSetChanged()
    }

}