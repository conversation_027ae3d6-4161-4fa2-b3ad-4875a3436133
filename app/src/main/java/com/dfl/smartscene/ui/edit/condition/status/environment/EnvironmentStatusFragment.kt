package com.dfl.smartscene.ui.edit.condition.status.environment

import android.annotation.SuppressLint
import android.view.View
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.StatusEnvironment
import com.dfl.smartscene.databinding.SceneFragmentEditConditionBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.common.RadioProgressDialog
import com.dfl.smartscene.ui.overlay.common.checklist.CheckListType
import com.dfl.smartscene.ui.overlay.common.checklist.RadioListDialogFragment
import com.dfl.smartscene.ui.overlay.common.doublewheel.DoubleWheelDialogFragment
import com.dfl.smartscene.ui.overlay.common.doublewheel.DoubleWheelType
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :条件->环境
 * version: 1.0
 */
class EnvironmentStatusFragment(
    private val skillId: Int, private val preCondition: ScenarioInfo.Condition?
) : MVVMAdapterFragment<EnvironmentStatusAdapter, SceneFragmentEditConditionBinding, EnvironmentStatusViewModel>() {
    private var mAdapter: EnvironmentStatusAdapter? = null

    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_condition
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    override fun getViewModelClass(): Class<EnvironmentStatusViewModel> {
        return EnvironmentStatusViewModel::class.java
    }

    override fun initView(view: View?) {
        super.initView(view)
        mAdapter = EnvironmentStatusAdapter()
        mAdapter?.setOnItemClickListener { _, v, position ->
            handleItemClick(v, position)
        }
        mViewDataBinding.rvCondition.adapter = mAdapter
    }

    private fun showRadioListDialog(title: String, type: StatusEnvironment) {
        val dialog: RadioListDialogFragment<ScenarioInfo.Condition, Int>
        if (type == StatusEnvironment.OUTSIDE_WEATHER) {
            dialog = RadioListDialogFragment(
                title,
                CheckListType.DEFAULT_CHECK_MODE,
                mViewModel.getRadioList(resources, type),
                mViewModel.getEnvironmentConditionData(type)
            )
            dialog.desc = getString(R.string.scene_text_edit_condition_outside_weather_desc)
        } else {
            //空气质量
            dialog = RadioListDialogFragment(
                title,
                CheckListType.INPUT_CHECK_MODE,
                mViewModel.getRadioList(resources, type),
                mViewModel.getEnvironmentConditionData(type)
            )
            dialog.inputArgValueList = mViewModel.getInputArgInfoData(type)
        }
        dialog.preSetCondition = preCondition
        dialog.show(parentFragmentManager, "showRadioListDialog")
    }

    @Deprecated("2A被合并使用DoubleWheelDialog")
    private fun showHumidityInCarDialog(title: String, type: StatusEnvironment) {
        val dialog = RadioProgressDialog(
            context, title, mViewModel.getEnvironmentConditionData(type), null
        )
        dialog.setPreSetCondition(preCondition)
        dialog.setMinNum(0)
        dialog.setMaxNum(100)
        dialog.setUnit("%")
        dialog.setDefaultUnit(40)
        dialog.setMoreThanState(false)
        dialog.setSeekBarMultiple(1)
        dialog.showWithAnimate()
    }

    private fun showDoubleWheelDialog(title: String, type: StatusEnvironment) {
        val dialog = DoubleWheelDialogFragment(
            title,
            mViewModel.getLeftWheelMap(),
            mViewModel.getRightWheelMap(0, 100, 1),
            mViewModel.getEnvironmentConditionData(type),
            DoubleWheelType.DOUBLE_SKILL
        )
        dialog.setUnit("", getString(R.string.scene_text_common_unit_percent))
        dialog.setupPreCondition(preCondition)
        dialog.setWheelDefaultPosition(1, 40)
        dialog.show(parentFragmentManager, "showDoubleWheelDialog")
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        super.initData()
        mViewModel.initData(resources, skillId)

        mViewModel.environmentLiveData.observe(viewLifecycleOwner) {
            mAdapter?.data = it
            mAdapter?.notifyDataSetChanged()
            val position = getSimulateClickPosition(preCondition, null)
            if (position != -1) {
                view?.let { it1 -> handleItemClick(it1, position) }
            }
        }
    }

    private fun handleItemClick(view: View, position: Int) {
        if (DebouncingUtils.isValid(view, SceneEditManager.mViewClickDuration)) {
            mAdapter?.data?.let {
                if (it[position].isEnable == false || it[position].conditionType == null) {
                    return
                }
                when (it[position].conditionType) {
                    //StatusEnvironment.WEATHER_FORECAST -> {
                    //    showWeatherDialog(it[position].conditionType!!)
                    //}

                    StatusEnvironment.HUMIDITY_IN_CAR -> { //车内湿度
                        //showHumidityInCarDialog(it[position].conditionName, it[position].conditionType!!)
                        showDoubleWheelDialog(it[position].conditionName, it[position].conditionType!!)
                    }

                    else -> {
                        showRadioListDialog(it[position].conditionName, it[position].conditionType!!)
                    }
                }
            }
        }
    }

    override fun getAdapter(): EnvironmentStatusAdapter? {
        return mAdapter
    }

}