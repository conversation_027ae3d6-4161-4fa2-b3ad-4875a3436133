package com.dfl.smartscene.ui.main.help

import android.annotation.SuppressLint
import android.content.Intent
import android.content.res.Configuration
import android.net.Uri
import android.os.Bundle
import android.view.View
import androidx.viewpager2.widget.ViewPager2
import com.dfl.android.common.base.MVVMDialogFragment
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.util.CommonToastUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.databinding.SceneFragmentHelpBinding
import com.dfl.smartscene.util.TrackUtils
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.dfl.smartscene.widget.VerticalTabLayout

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/20
 * desc   : 帮助界面
 * version: 1.0
 */
class HelpDialogFragment : MVVMDialogFragment<SceneFragmentHelpBinding, HelpViewModel>(), View.OnClickListener {

    // 变量声明
    var mHelpViewPager: ViewPager2? = null
    var mHelpTabLayout: TabLayout? = null
    var mHelpVerticalTabLayout: VerticalTabLayout? = null
    private var lastClickTime = 0L
    
    // Tab标题数组
    private lateinit var mHelpTabTitle: Array<String>

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        arguments?.getInt(ARG_SELECTED_TAB)?.let { selectedTab ->
            setSelectedTab(selectedTab)
        }
    }

    /**
     * 设置选中的Tab
     * @param position Tab位置
     */
    private fun setSelectedTab(position: Int) {
        mHelpVerticalTabLayout?.setSelectedTab(position)
        mHelpViewPager?.setCurrentItem(position, false)
    }

    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_help
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    override fun getViewModelClass(): Class<HelpViewModel> {
        return HelpViewModel::class.java
    }

    /**
     * 初始化视图
     */
    override fun initView(view: View) {
        super.initView(view)
        setCanceledOnTouchOutside(true)
        initListener()
        initBanner()
        TrackUtils.viewStart(TrackUtils.PageNameType.Help)
        mViewDataBinding.includeDialogBottom.btnPrimary.text = getString(R.string.scene_text_helper_user_manual)
        mViewDataBinding.includeDialogBottom.btnNormal.text = getString(R.string.scene_text_common_close)
        val nightMode = resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK
        setTabLineColor(nightMode)
    }
    
    /**
     * 初始化Banner和Tab
     */
    private fun initBanner() {
        val helpBannerFragmentsAdapter: HelpBannerFragmentsAdapter = HelpBannerFragmentsAdapter(
            childFragmentManager, lifecycle
        )
        mHelpViewPager = mViewDataBinding.viewPagerHelp
        mHelpVerticalTabLayout = mViewDataBinding.helpTabs
    
        mHelpViewPager?.adapter = helpBannerFragmentsAdapter
        mHelpViewPager?.offscreenPageLimit = 1
        mHelpViewPager?.isUserInputEnabled = true
        mHelpViewPager?.isSaveEnabled = true
        
        // 初始化Tab标题数组
        mHelpTabTitle = arrayOf(
            getString(R.string.scene_text_common_help_intro),
            getString(R.string.scene_text_common_help_create),
            getString(R.string.scene_text_common_help_perform),
            getString(R.string.scene_text_common_help_set_card),
        )
    
        // 设置垂直Tab标题
        mHelpVerticalTabLayout?.setTabTitles(mHelpTabTitle)
        
        // 设置Tab选择监听
        mHelpVerticalTabLayout?.setOnTabSelectedListener { position ->
            mHelpViewPager?.setCurrentItem(position, true)
        }
        
        // 设置ViewPager页面变化监听
        mHelpViewPager?.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                mHelpVerticalTabLayout?.setSelectedTab(position)
            }
        })
        
        // 添加TabLayout监听器（如果需要保留原有TabLayout功能）
        addTabLayoutListener()
    }

    /**
     * 添加TabLayout监听器
     */
    private fun addTabLayoutListener() {
        // 如果需要保留原有的TabLayout功能，可以在这里添加
        // 隐藏长按显示文本
        mHelpTabLayout?.let { tabLayout ->
            mHelpViewPager?.let { viewPager ->
                TabLayoutMediator(tabLayout, viewPager) { tab, position ->
                    tab.text = mHelpTabTitle[position]
                    tab.view.setOnLongClickListener { true }
                    tab.view.tooltipText = null
                }.attach()
            }
        }

        mHelpTabLayout?.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                if (tab != null) {
                    //使用 post 方法来确保 UI 的更新操作在正确的时机执行
                    mHelpTabLayout?.post(Runnable {
                        mHelpTabLayout?.getTabAt(tab.position)?.select()
                        mHelpTabLayout?.setScrollPosition(tab.position, 0f, true)
                        mHelpViewPager?.setCurrentItem(tab.position, false)
                    })
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                // 可以在这里添加取消选中的逻辑
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
                // 可以在这里添加重新选中的逻辑
            }
        })
    }

    /**
     * 初始化监听器
     */
    private fun initListener() {
        mViewDataBinding.includeDialogBottom.btnPrimary.setOnClickListener(this)
        mViewDataBinding.includeDialogBottom.btnNormal.setOnClickListener(this)
    }

    /**
     * 点击事件处理
     */
    override fun onClick(v: View?) {
        v ?: return
        when (v.id) {
            R.id.btn_primary -> { //前往车主指南
                if (!clickBreak()) {
                    startOwnerGuideApp()
                }
            }

            R.id.btn_normal -> { //取消
                dismissWithAnimate()
            }
        }
    }

    /**
     * 防止重复点击
     */
    private fun clickBreak(): Boolean {
        if (System.currentTimeMillis() - lastClickTime < CLICK_INTERVAL_TIME) {
            CommonToastUtils.show(getString(R.string.scene_toast_me_later_to_try))
            lastClickTime = System.currentTimeMillis()
            return true
        }
        lastClickTime = System.currentTimeMillis()
        return false
    }

    /**
     * 启动车主指南app
     */
    @SuppressLint("QueryPermissionsNeeded")
    private fun startOwnerGuideApp() {
        try {
            val intent = Intent()
            intent.data = Uri.parse("venucia://com.dfl.ownersguide/detail")
            intent.putExtra("manual_only_code", "pf_zkp_15") //String类型正文code
            startActivity(intent)
            dismissWithAnimate()
        } catch (e: Exception) {
            CommonLogUtils.logE("HelpDialogFragment", "start OwnerGuide App error:${e.message}")
        }
    }

    /**
     * 配置变化处理（主题切换等）
     */
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        
        val nightMode = newConfig.uiMode and Configuration.UI_MODE_NIGHT_MASK
        
        // 设置垂直Tab的颜色 - 更新为新的颜色资源
        mHelpVerticalTabLayout?.setSelectedTextColor(
            resources.getColor(R.color.scene_color_text_1)
        )
        mHelpVerticalTabLayout?.setNormalTextColor(
            resources.getColor(R.color.scene_color_text_3)
        )
        mHelpVerticalTabLayout?.setIndicatorColor(
            resources.getColor(R.color.scene_color_text_1)
        )
        
        // 设置原有TabLayout的颜色（如果需要保留）
        mHelpTabLayout?.setTabTextColors(
            resources.getColor(R.color.scene_color_text_3),
            resources.getColor(R.color.scene_color_text_1)
        )
        mHelpTabLayout?.setSelectedTabIndicatorColor(
            resources.getColor(R.color.scene_color_text_1)
        )
        
        setTabLineColor(nightMode)
    }

    /**
     * 设置Tab线条颜色
     */
    private fun setTabLineColor(nightMode: Int) {
        if (nightMode == Configuration.UI_MODE_NIGHT_YES) {
            mHelpTabLayout?.setBackgroundResource(R.drawable.scene_shape_help_tab_dash_bg_night)
        } else {
            mHelpTabLayout?.setBackgroundResource(R.drawable.scene_shape_help_tab_dash_bg_day)
        }
    }

    /**
     * 销毁时清理资源
     */
    override fun onDestroy() {
        super.onDestroy()
        mHelpTabLayout = null
        mHelpVerticalTabLayout = null
        mHelpViewPager?.adapter = null
        mHelpViewPager = null
    }

    /**
     * 带动画的关闭
     */
    override fun dismissWithAnimate() {
        TrackUtils.viewEnd(TrackUtils.PageNameType.Help)
        super.dismissWithAnimate()
    }

    companion object {
        const val CLICK_INTERVAL_TIME = 500
        const val TAG = GlobalConstant.GLOBAL_TAG.plus("HelpDialogFragment")
        private const val ARG_SELECTED_TAB = "selected_tab"

        /**
         * 创建新实例
         * @param selectedTab 选中的Tab位置
         */
        fun newInstance(selectedTab: Int): HelpDialogFragment {
            val fragment = HelpDialogFragment()
            val args = Bundle()
            args.putInt(ARG_SELECTED_TAB, selectedTab)
            fragment.arguments = args
            return fragment
        }
    }
}