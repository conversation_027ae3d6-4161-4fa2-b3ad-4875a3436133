package com.dfl.smartscene.ui.edit.action.air

import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.android.common.util.MatchableAdapter
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.ActionSkillItemBean
import com.dfl.smartscene.bean.action.AirConditionerType

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/08
 * desc : 动作->空调的rv适配器
 * version: 1.0
 */
class AirActionAdapter : BaseQuickAdapter<ActionSkillItemBean<AirConditionerType>, BaseViewHolder>(
    R.layout.scene_recycle_item_edit_action_dialog_item
), MatchableAdapter<ActionSkillItemBean<AirConditionerType>> {
    override fun convert(holder: BaseViewHolder, item: ActionSkillItemBean<AirConditionerType>) {
        holder.getView<AppCompatImageView>(R.id.iv_action_reflex_icon).setImageResource(item.iconId)
        holder.getView<TextView>(R.id.tv_action_reflex_name).text = item.content
    }

    override val adapterData: List<ActionSkillItemBean<AirConditionerType>>
        get() = data

    override fun findPositionByTitle(title: String): Int {
        return data.indexOfFirst { it.content == title && !it.actionSkillId.isNullOrEmpty() }
    }
    
    // 添加新方法，根据skillId精确匹配
    fun findPositionBySkillId(skillId: Int): Int {
        return data.indexOfFirst { item ->
            !item.actionSkillId.isNullOrEmpty() && 
            item.actionSkillId!!.contains(skillId)
        }
    }
}