package com.dfl.smartscene.ui.edit.condition.trigger.location

import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.android.common.util.MatchableAdapter
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.TriggerLocation

/**
 *Created by 钟文祥 on 2023/12/12.
 *Describer:
 */
class LocationTriggerAdapter : BaseQuickAdapter<ConditionItemBean<TriggerLocation>, BaseViewHolder>(
    R.layout.scene_recycle_item_edit_condition_dialog_item
), MatchableAdapter<ConditionItemBean<TriggerLocation>> {

    override fun convert(holder: BaseViewHolder, item: ConditionItemBean<TriggerLocation>) {
        holder.getView<TextView>(R.id.tv_condition_reflex_name).text = item.conditionName
        holder.getView<AppCompatImageView>(R.id.iv_condition_reflex_icon).setImageResource(item.conditionIcon)
    }

    override val adapterData: List<ConditionItemBean<TriggerLocation>>
        get() = data

    override fun findPositionByTitle(title: String): Int {
        return data.indexOfFirst { it.conditionName == title && !it.conditionSkillId.isNullOrEmpty() }
    }
}