package com.dfl.smartscene.ui.main.discover.card

import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.commonlib.CommonUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.soa.SceneManager
import com.dfl.smartscene.util.MMKVUtils
import com.dfl.soacenter.communication.MessageCenterManager

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/19
 * desc   :
 * version: 1.0
 */
class SceneCardViewModel : BaseViewModel() {
    fun initData() {

    }

    /**
     * 获取发现场景列表缓存在本地的数据
     */
    fun getCacheDiscoverSceneData() {
        val data = MMKVUtils.getInstance().discoverSceneData
        val list = SceneManager.getDiscoverInfoList()
        val newSceneIdList = ArrayList<String>()
        //判断是否是新增
        if (list.size != 0) {
            //获取到新数据的情况
            val stringBuffer = StringBuffer()
            val nameList = ArrayList<String>()
            for (i in list.indices) {
                val sceneId = list[i].scenario.scenarioInfo?.scenarioId
                val sceneName = list[i].scenario.scenarioInfo?.scenarioName
                if (data != null) {
                    var isNewScene = true
                    for (cacheId in data) {
                        if (sceneId == cacheId) {
                            isNewScene = false
                            break
                        }
                    }
                    if (isNewScene) {
                        nameList.add(sceneName ?: "")
                    }
                }
                newSceneIdList.add(sceneId ?: "")
            }
            if (nameList.size != 0) {
                stringBuffer.append("【")
                for (i in nameList.indices) {
                    if (i != nameList.size - 1) {
                        stringBuffer.append(nameList[i].plus("、"))
                    } else {
                        stringBuffer.append(nameList[i].plus("】"))
                    }
                }
                val message = stringBuffer.toString()
                    .plus(CommonUtils.getApp().getString(R.string.scene_text_scene_has_update_desc))
                MessageCenterManager.sendMessage(
                    CommonUtils.getApp().getString(R.string.scene_text_scene_has_update),
                    message
                )
            }
            //通知完之后需要同步更新本地缓存数据.数据异常就清空缓存
            if (newSceneIdList.size > 0)
                MMKVUtils.getInstance().saveDiscoverSceneData(newSceneIdList)
        }
    }
}