package com.dfl.smartscene.apiweb.utils;

import android.annotation.SuppressLint;

import com.dfl.android.common.util.TaskExecutor;
import com.dfl.android.commonlib.CommonUtils;
import com.dfl.android.commonlib.log.CommonLogUtils;
import com.dfl.smartscene.BuildConfig;
import com.dfl.android.common.global.GlobalConfig;
import com.dfl.android.common.global.GlobalConstant;
import com.dfl.smartscene.util.pki.PkiUtils;

import java.io.File;
import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.FutureTask;
import java.util.concurrent.TimeUnit;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import okhttp3.Cache;
import okhttp3.Dispatcher;
import okhttp3.Dns;
import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory;
import retrofit2.converter.gson.GsonConverterFactory;
import retrofit2.converter.scalars.ScalarsConverterFactory;

/**
 * Created by zwx on 2018/6/5.
 * Describer: 服务管理者  单例化Retrofit
 */
public class ServiceManager {
    /**
     * 最大的网络缓存大小
     */
    private static final long CACHE_MAX_SIZE = 10 * 1024 * 1024;
    private static final String TAG = GlobalConstant.GLOBAL_TAG + "ServiceManager";
    private static Retrofit retrofit;

    public static synchronized <T> T getClientService(final Class<T> service) {
        return getClientRetrofit(null).create(service);
    }

    public static synchronized <T> T getClientService(final Class<T> service, ProgressResponseBody
            .IProgressListener listener) {
        return getClientRetrofit(listener).create(service);
    }


    @SuppressLint("CustomX509TrustManager")
    private static Retrofit getClientRetrofit(final ProgressResponseBody.IProgressListener
                                                      progressListener) {
        if (retrofit == null) {
            Dispatcher dispatcher = new Dispatcher();
            //由5设置8，同一个域名的最大请求加大
            dispatcher.setMaxRequestsPerHost(8);
            OkHttpClient.Builder builder = new OkHttpClient().newBuilder()
                    //设置超时
                    .connectTimeout(GlobalConfig.NET_TIME_OUT, TimeUnit.SECONDS)//设置连接超时时间
                    .writeTimeout(GlobalConfig.NET_TIME_OUT, TimeUnit.SECONDS)
                    .readTimeout(GlobalConfig.NET_TIME_OUT, TimeUnit.SECONDS)//获取response的返回等待时间
                    //错误重连
                    .retryOnConnectionFailure(true)
                    //                    //401 HTTP状态码 处理 token验证
                    //                    .authenticator(new TokenAuthenticator())
                    //头处理
                    .addNetworkInterceptor(new HeaderInterceptor())
                    //添加log拦截器 显示日志
                    .addNetworkInterceptor(getHttpLoggingInterceptor())
                    //中断耗时的dns操作，在无网连接中解决超时无效
                    .dns(new XDns(GlobalConfig.NET_TIME_OUT))
                    //自定义code=403  token验证
                    //                    .addInterceptor(new TokenInterceptor())
                    .hostnameVerifier(new TrustAllHostnameVerifier())
                    .dispatcher(dispatcher);
            if (PkiUtils.isInstallCert()) {
                TrustManager[] trustManagers = PkiUtils.initTrustManagers(
                        PkiUtils.getStairCerInputStream(),
                        PkiUtils.getSecondCerInputStream()
                );
                //20240829,因需要请求时添加证书
                builder.sslSocketFactory(PkiUtils.createSSLSocketFactory4PKI(trustManagers),
                        (X509TrustManager) trustManagers[0]);
            } else {
                //解决ssl https问题
                builder.sslSocketFactory(createSSLSocketFactory(), new X509TrustManager() {
                    @SuppressLint("TrustAllX509TrustManager")
                    @Override
                    public void checkClientTrusted(X509Certificate[] chain, String authType) {

                    }

                    @SuppressLint("TrustAllX509TrustManager")
                    @Override
                    public void checkServerTrusted(X509Certificate[] chain, String authType) {

                    }

                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0];
                    }
                });
            }
            Cache cache = getCache();
            if (cache != null) {
                builder.cache(cache);
            }
            if (progressListener != null) {
                //进度
                builder.addNetworkInterceptor(chain -> {
                    Response response = chain.proceed(chain.request());
                    return response.newBuilder().body(new ProgressResponseBody(response.body()
                            , progressListener)).build();
                });
            }
            OkHttpClient okHttpClient = builder.build();

            retrofit = new Retrofit.Builder()
                    .baseUrl(GlobalConfig.Companion.getFINAL_COMMUNITY_URL())
                    //添加字符串转换支持
                    .addConverterFactory(ScalarsConverterFactory.create())
                    //添加GSON转换支持
                    .addConverterFactory(GsonConverterFactory.create())
                    // .create()转换器的缘故，会将参数请求头的content-type值默认赋值application/json,
                    // 所以这里需要对参数请求头的content-type设置一个正确的值：text/plain
                    .addCallAdapterFactory(RxJava2CallAdapterFactory.create())
                    .client(okHttpClient)
                    .build();
        }
        return retrofit;
    }

    /**
     * 获取缓存配置
     *
     * @return cache对象
     */
    private static Cache getCache() {
        File cacheDir = new File(CommonUtils.getApp().getCacheDir(), "networkCache");
        Cache cache = null;
        try {
            cache = new Cache(cacheDir, CACHE_MAX_SIZE);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return cache;
    }

    /**
     * 显示日志
     */
    private static HttpLoggingInterceptor getHttpLoggingInterceptor() {
        HttpLoggingInterceptor logInterceptor =
                new HttpLoggingInterceptor(new HttpLoggingInterceptor.Logger() {
                    @Override
                    public void log(String message) {
                        CommonLogUtils.logD(TAG, "网络日志：" + message);
                    }
                });
        if (BuildConfig.DEBUG) {
            logInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
        } else {
            logInterceptor.setLevel(HttpLoggingInterceptor.Level.NONE);
        }
        return logInterceptor;
    }

    private static SSLSocketFactory createSSLSocketFactory() {
        SSLSocketFactory ssfFactory = null;
        try {
            SSLContext sc = SSLContext.getInstance("TLS");
            sc.init(null, new TrustManager[]{new TrustAllCerts()}, new SecureRandom());
            ssfFactory = sc.getSocketFactory();
        } catch (Exception e) {
        }
        return ssfFactory;
    }

    /**
     * 头处理
     */
    private static class HeaderInterceptor implements Interceptor {

        @Override
        public Response intercept(Chain chain) throws IOException {
            Request request = chain.request();
            //头部参数
            request = request.newBuilder()
                    .removeHeader("User-Agent")
                    .removeHeader("Accept-Encoding")
                    .header("Content-Type", "application/json")//添加统一通用header
                    .build();

            Response response = chain.proceed(request);
            if (response.body() != null && response.body().contentType() != null) {
                MediaType mediaType = response.body().contentType();
                String content = response.body().string();
                ResponseBody responseBody = ResponseBody.create(mediaType, content);
                return response.newBuilder().body(responseBody).build();
            } else {
                return response;
            }
        }
    }

    /**
     * token验证 （401 HTTP状态码）
     */
    //    private static class TokenAuthenticator implements Authenticator {
    //        //	如果服务端返回一个401错误码(HTTP状态码)的时候，
    //        // 401表示未认证，这个时候系统就会调用该方法来获取新的token并重新发起请求
    //        @Override
    //        public Request authenticate(Route route, Response response) throws IOException {
    //            if (response.code() == 401) {
    //                //请求获取拿到新的token,重新发起请求
    //                String newToken = CommunityService.INSTANCE.register();
    //                return response.request().newBuilder()
    //                        .header("Authorization", "Bear " + newToken).build();
    //            } else {
    //                response.request();
    //            }
    //            return null;
    //        }
    //    }

    //自定义SS验证相关类
    private static class TrustAllCerts implements X509TrustManager {
        @Override
        public void checkClientTrusted(X509Certificate[] chain, String authType) throws
                CertificateException {
        }

        @Override
        public void checkServerTrusted(X509Certificate[] chain, String authType) throws
                CertificateException {
        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[0];
        }
    }

    private static class TrustAllHostnameVerifier implements HostnameVerifier {
        @Override
        public boolean verify(String hostname, SSLSession session) {
            return true;
        }
    }

    private static class XDns implements Dns {
        private long timeout;

        public XDns(long timeout) {
            this.timeout = timeout;
        }

        @Override
        public List<InetAddress> lookup(String hostname) throws UnknownHostException {
            if (hostname == null) {
                throw new UnknownHostException("hostname == null");
            } else {
                try {
                    FutureTask<List<InetAddress>> task =
                            new FutureTask<>(new Callable<List<InetAddress>>() {
                                @Override
                                public List<InetAddress> call() throws Exception {
                                    return Arrays.asList(InetAddress.getAllByName(hostname));
                                }
                            });
                    TaskExecutor.io(task);
                    return task.get(timeout, TimeUnit.SECONDS);
                } catch (Exception var4) {
                    UnknownHostException unknownHostException =
                            new UnknownHostException("查找不到hostname主机： " + hostname);
                    unknownHostException.initCause(var4);
                    throw unknownHostException;
                }
            }
        }
    }

}
