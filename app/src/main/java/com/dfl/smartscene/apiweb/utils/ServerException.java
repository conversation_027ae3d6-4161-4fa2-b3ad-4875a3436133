package com.dfl.smartscene.apiweb.utils;

/**
 * Created by 钟文祥 on 2018/6/5.
 * Describer: 服务器返回的错误result！=1的情况下， 自定义响应错误 这时 http状态码为200
 * 用来触发 BaseService的 onErrorResumeNext方法
 */
public class ServerException extends RuntimeException {
    public String result; //对应 BaseResponseBean的result
    public String msg;   //对应BaseResponseBean的msg

    public ServerException(String result, String msg) {
        this.result = result;
        this.msg = msg;
    }
}
