package com.dfl.smartscene.apiweb.utils;


import com.dfl.android.commonlib.log.CommonLogUtils;
import com.dfl.smartscene.R;
import com.dfl.smartscene.application.SceneApplication;
import com.dfl.android.common.global.GlobalConstant;
import com.google.gson.JsonParseException;

import org.json.JSONException;

import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.text.ParseException;

import retrofit2.adapter.rxjava2.HttpException;

/**
 * Created by 钟文祥 on 2023/7/11.
 * Describer: 处理异常的驱动器  适合爱奇艺
 */
public class ExceptionEngine {
    public static final String TAG = GlobalConstant.GLOBAL_TAG + "ExceptionEngine";
    // HTTP的状态码
    public static final int ReqSuccess = 200;// 请求成功
    public static final int NoContent = 204;//  无返回内容
    public static final int BadRequest = 400;// 该请求是无效的，详细的错误信息会说明原因
    public static final int Unauthorized = 401;// 验证失败，详细的错误信息会说明原因
    public static final int Forbidden = 403;// 被拒绝调用，详细的错误信息会说明原因
    public static final int NotFound = 404;//资源不存在
    public static final int TooManyRequest = 429;//超出了调用频率限制，详细的错误信息会说明原因
    public static final int InternalServerError = 500;//服务器内部出错了，请联系我们尽快解决问题
    public static final int GatewayTimeout = 504;//服务器在运行，本次请求响应超时，请稍候重试


    //自定义对应HTTP的状态码
    /**
     * 未知错误
     */
    public static final int UNKNOWN = 1000;
    /**
     * 解析错误
     */
    public static final int PARSE_ERROR = 1001;

    /**
     * 连接错误
     */
    public static final int CONNECT_NETWORD_ERROR = 1002;
    /**
     * 超时错误
     */
    public static final int TIMEOUT_NETWORD_ERROR = 1003;
    /**
     * 设备无网错误
     */
    public static final int DEVICE_NO_NETWORK_ERROR = 1004;
    /**
     * 设备无wifi开启流量，但没流量的错误
     */
    public static final int DEVICE_NO_FLOW_ERROR = 1005;

    /** 临时 数据为0 */
    public static final int DATA_SIZE_0 = 1006;


    public static ApiException handleException(Throwable e) {
        ApiException ex = null;
        if (e instanceof HttpException) { //HTTP错误
            HttpException httpException = (HttpException) e;
            ex = new ApiException(e,
                    httpException.response().raw().request().url().toString(),
                    httpException.code(),
                    "HTTP错误", "HTTP错误");
            switch (httpException.code()) {
                case NoContent:
                    ex.message = "无返回内容";
                    break;
                case BadRequest:
                    ex.message = "请求无效";
                    break;
                case Unauthorized:
                    ex.message = "验证失败";
                    break;
                case Forbidden:
                    ex.message = "被拒绝调用";
                    break;
                case NotFound:
                    ex.message = "资源不存在";
                    break;
                case TooManyRequest:
                    ex.message = "超出了调用频率限制";
                    break;
                case InternalServerError:
                    ex.message = "服务器内部出错";
                    break;
                case GatewayTimeout:
                    ex.message = "请求响应超时";
                    break;
                default:
                    ex.message = "HTTP未知错误";
                    break;
            }

        } else if (e instanceof ServerException) {    //服务器返回的错误
            ServerException resultException = (ServerException) e;
            ex = new ApiException(resultException, InternalServerError);
            ex.toastMsg = ex.message = resultException.msg;
            return ex;
        } else if (e instanceof ConnectException || e instanceof UnknownHostException) {//⽹络连接异常
            ex = new ApiException(e, CONNECT_NETWORD_ERROR);
            ex.toastMsg = ex.message =
                    SceneApplication.Companion.getContextObject().getString(R.string.scene_toast_web_exception_unconnect_or_timeout);
        } else if (e instanceof SocketTimeoutException) {//响应超时
            ex = new ApiException(e, TIMEOUT_NETWORD_ERROR);
            ex.toastMsg = ex.message =
                    SceneApplication.Companion.getContextObject().getString(R.string.scene_toast_web_exception_unconnect_or_timeout);
        } else if (e instanceof JsonParseException
                || e instanceof JSONException
                || e instanceof ParseException) {//返回数据异常
            ex = new ApiException(e, PARSE_ERROR);
            ex.toastMsg = ex.message =
                    SceneApplication.Companion.getContextObject().getString(R.string.scene_toast_web_exception_json);
        } else {
            ex = new ApiException(e, UNKNOWN);//未知错误
            ex.toastMsg = ex.message =
                    SceneApplication.Companion.getContextObject().getString(R.string.scene_toast_web_exception_unknown)
                            + ":" + e.getClass().getName() + " " + e.getMessage();
        }
        CommonLogUtils.logD(TAG, e.getMessage());
        return ex;
    }


}
