package com.dfl.smartscene.apiweb.utils;

import com.dfl.android.commonlib.log.CommonLogUtils;
import com.dfl.android.common.global.GlobalConstant;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

/**
 * Created by zwx on 2018/6/5.
 * Describer: 自定义响应请求回调类
 */
public abstract class RxSubscriber<T> implements Observer<T> {
    private static final String TAG = GlobalConstant.GLOBAL_TAG + "RxSubscriber";

    public RxSubscriber() {
    }

    @Override
    public void onSubscribe(Disposable d) {
        onAddDisposable(d);
    }


    @Override
    public void onError(Throwable error) {
        if (error instanceof ApiException) {
            ApiException apiException = (ApiException) error;

            CommonLogUtils.logE(TAG,
                    "异常错误:" + "url:" + apiException.getReqURL() +
                            " HTTP错误码:" + apiException.getHttpCode() +
                            " 描述:" + apiException.getMessage());
            onApiError(apiException);
        } else {
            CommonLogUtils.logE(TAG, "未知错误:" + error.getMessage());
            onApiError(new ApiException(error, "", ExceptionEngine.UNKNOWN,
                    "未知错误:" + error.getMessage(), "返回数据异常"));
        }
    }

    @Override
    public void onComplete() {

    }


    @Override
    public void onNext(T value) {
        onApiNext(value);
    }


    public abstract void onAddDisposable(Disposable d);

    /**
     * 正确回调
     */
    public abstract void onApiNext(T value);

    /**
     * 异常回调
     */
    public abstract void onApiError(ApiException ex);

}