package com.dfl.smartscene.apiweb.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by 钟文祥 on 2023/10/31.
 * Describer: 处理api 状态
 */
public class ApiReqResState {
    /** 状态 */
    private ReqResState state;
    /** 错误封装 */
    private ApiException ex;
    /** 传递所需的数据 ，比较少用 */
    private Map<String, Object> map = new HashMap<>();


    public enum ReqResState {
        /** 开始获取数据 */
        GetValueStart(0),
        /** 完成获取数据 */
        GetValueFinish(1),
        /** 处理异常 */
        Error(2);


        private int value;

        public int getValue() {
            return value;
        }

        ReqResState(int value) {
            this.value = value;
        }
    }

    public ReqResState getState() {
        return state;
    }

    public void setState(ReqResState state) {
        this.state = state;
    }

    public ApiException getEx() {
        return ex;
    }

    public void setEx(ApiException ex) {
        this.ex = ex;
    }

    public ApiReqResState() {
    }

    public ApiReqResState(ReqResState state) {
        this(state, null);
    }

    public ApiReqResState(ReqResState state, ApiException ex) {
        this.state = state;
        this.ex = ex;
    }

    public Map<String, Object> getMap() {
        return map;
    }
}
