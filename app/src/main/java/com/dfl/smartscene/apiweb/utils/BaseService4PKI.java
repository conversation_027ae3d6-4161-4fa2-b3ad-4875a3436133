package com.dfl.smartscene.apiweb.utils;

import com.dfl.android.common.util.ConvertUtils;
import com.dfl.android.common.util.EncryptUtils;
import com.dfl.android.common.util.SM2Util;
import com.dfl.smartscene.bean.apibase.BaseRequestBean;
import com.dfl.smartscene.bean.apibase.BaseRequestV;
import com.dfl.smartscene.bean.apibase.BaseResponseBean;
import com.dfl.smartscene.bean.apibase.BaseResponseBeanV;
import com.dfl.smartscene.bean.community.reponse.CommunityListResponse;
import com.dfl.smartscene.bean.community.reponse.CommunitySceneInfo;
import com.dfl.smartscene.bean.community.request.CommunityTabType;
import com.dfl.android.common.global.GlobalConfig;
import com.dfl.smartscene.util.pki.PkiUtils;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Random;

import io.reactivex.Observable;
import io.reactivex.ObservableSource;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Function;
import io.reactivex.schedulers.Schedulers;

/**
 * Created by 钟文祥 on 2024/9/3.
 * Describer:
 */
public class BaseService4PKI {
    /**
     * 对请求内容 加密 4 PKI
     */
    protected static <T> BaseRequestV getRequestJsonEncryption(T obj) {
        String json = new Gson().toJson(obj);
        byte[] encrypt = SM2Util.encrypt(json, PkiUtils.FINAL_PKI_PUBLIC_KEY);
        String encryption = ConvertUtils.bytes2HexString(encrypt).toLowerCase(Locale.getDefault());
        return new BaseRequestV(encryption);
    }

    /**
     * 组建heard 4 PKI
     */
    protected static Map<String, String> getHeardMap(String api, BaseRequestV requestV) {
        Map<String, String> headerMap = new HashMap<>();
        String encryptJsonStr = "";
        if (requestV != null) {
            encryptJsonStr = new Gson().toJson(requestV); //请求V对象转json
        }

        String timestamp = System.currentTimeMillis() + "";
        String nonceStr = getRandomString(32);
        //keysign = sha1512(appid+appkey + api + noncestr + timestamp + encryptJsonStr)
        String keySign =
                EncryptUtils.encryptSHA512ToString(PkiUtils.FINAL_PKI_APPID
                        + PkiUtils.FINAL_PKI_APP_KEY
                        + api
                        + nonceStr
                        + timestamp
                        + encryptJsonStr).toLowerCase(Locale.getDefault());
        headerMap.put("api", api);
        headerMap.put("content-type", "application/json");
        headerMap.put("appid", PkiUtils.FINAL_PKI_APPID);
        headerMap.put("keysign", keySign);
        headerMap.put("noncestr", nonceStr);
        headerMap.put("timestamp", timestamp);
        headerMap.put("uuid", BaseRequestBean.Companion.getUUID());
        return headerMap;
    }


    /***
     * 添加拦截器
     * @param observable api返回来的 标准数据
     * @return 对标准数据 解密得出的对象
     * @param <T> 想要的对象 业务数据
     */
    protected static <T> Observable<T> initObservableByBaseResponse(
            String apiStr, Observable<BaseResponseBeanV> observable, Class<T> classOfT) {
        return observable.switchMap(new Function<BaseResponseBeanV, ObservableSource<T>>() {
            @Override
            public ObservableSource<T> apply(BaseResponseBeanV baseResponseV) throws Exception {
                if (!baseResponseV.isSuccess())
                    throw new ServerException(baseResponseV.getResult(), baseResponseV.getMsg());
                if (baseResponseV.getV() == null)
                    throw new ServerException(baseResponseV.getResult(), baseResponseV.getMsg());

                String vJson =
                        SM2Util.decrypt(ConvertUtils.hexString2Bytes(baseResponseV.getV()),
                                PkiUtils.FINAL_PKI_PRIVATE_KEY);

                Gson gson = new Gson();
                BaseResponseBean bean = gson.fromJson(vJson, BaseResponseBean.class);
                if (!bean.isSuccess())
                    throw new ServerException(bean.getResult(), bean.getMsg());

                if (apiStr.equals("getbanner")) { //轮播图
                    List<CommunitySceneInfo> list = new ArrayList<>();
                    for (int i = 0, size = ((List) bean.getRows()).size(); i < size; i++) {
                        CommunitySceneInfo item =
                                gson.fromJson(gson.toJson(((List) bean.getRows()).get(i)),
                                        CommunitySceneInfo.class);
                        list.add(item);
                    }
                    CommunityListResponse value = new CommunityListResponse(0,
                            CommunityTabType.Banner.getValue(), 1, 1,
                            GlobalConfig.REQ_PAGE_SIZE_4_BANNER,
                            GlobalConfig.REQ_PAGE_SIZE_4_BANNER, list);
                    return Observable.just((T) value);
                } else if (apiStr.equals("focusornofocus")//关注
                        || apiStr.equals("upload")//发布
                        || apiStr.equals("likeOrNoLike")//点赞
                        || apiStr.equals("downloadOrNo")//下载
                        || apiStr.equals("clearsearchHistory")//清空历史搜索
                        || apiStr.equals("delete")//删除上传场景
                ) {
                    return Observable.just((T) bean);
                } else {
                    String gsonStr = gson.toJson(bean.getRows());
                    T value = gson.fromJson(gsonStr, classOfT);
                    //对返回码进行判断，如果http状态码不是200，则证明服务器端返回错误信息了，便根据跟服务器约定好的错误码去解析异常
                    return Observable.just(value);//服务器请求数据成功，返回里面的数据实体
                }
            }
        }).onErrorResumeNext(new Function<Throwable, ObservableSource<T>>() {
            @Override
            public ObservableSource<T> apply(Throwable throwable) throws Exception {
                //ExceptionEngine为处理异常的驱动器
                return Observable.error(ExceptionEngine.handleException(throwable));
            }
        }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread());
    }


    /**
     * 获取指定长度随机字符串
     *
     * @param length 随机字符串长度
     * @return
     */
    private static String getRandomString(int length) {
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(3);
            long result;
            if (number == 0) {
                result = (long) (Math.random() * 25 + 65);
                sb.append(((char) (int) result));
            } else if (number == 1) {
                result = (long) (Math.random() * 25 + 97);
                sb.append(((char) (int) result));
            } else if (number == 2) {
                sb.append(new Random().nextInt(10));
            }
        }
        return sb.toString();
    }
}
