package com.dfl.smartscene.bean.communication

import com.dfl.android.common.util.GeneralInfoUtils
import com.dfl.soacenter.SoaConstants


/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/02/21
 * desc : 主进程与个人中心App通讯基类
 * version: 1.0
 */
/**
 * 在智慧场景主线程的请求基类
 */
data class RequestUserCenterBean<T>(
    /**具体的协议ID*/
    var protocolId: Int,
    /**请求码*/
    var requestCode: String = GeneralInfoUtils.getRequestCode(),
    /**应答码  应答回来与请求码需要一致*/
    var responseCode: String? = null,
    /**协议版本号，本次请求协议的版本号*/
    var versionName: String = GeneralInfoUtils.getVersionName(),
    /**发送方标志 本应用包名*/
    var requestAuthor: String = GeneralInfoUtils.getRequestAuthor(),
    /**消息类型 : request：请求的消息 response：响应的消息 dispatch：主动透出的消息*/
    var messageType: String = SoaConstants.MESSAGE_TYPE_REQUEST,
    /**具体请求数据*/
    var data: T?
)

/**
 * @property cpType 默认1
 * @property autoHide 默认true,等于true的话，登陆成功后自动隐藏个人中心，false反之
 * @property autoLogin 默认true,如果个人中心未登录，直接打开个人中心登陆页面进行登陆，登陆成功之后返回用户信息
 * @constructor 获取用户登录信息请求体
 */
data class RequestUserLoginStatus(
    val cpType: Int = 1, val autoHide: Boolean = true, val autoLogin: Boolean = true
)


/**
 * @property protocolId 协议ID
 * @property responseCode 应答码 应答回来与请求码需要一致
 * @property versionName 协议版本号，本次请求协议的版本号
 * @property result 1成功其他失败
 * @property message 响应消息,失败原因
 * @property data 响应体
 * @constructor 用户中心通信响应
 */
data class UserCenterResponse<T>(
    val protocolId: Int?,
    val responseCode: String?,
    val versionName: String?,
    val result: Int?,
    val message: String?,
    var data: T?
) {
    companion object {
        const val SUCCESS = 1
        const val UN_LOGIN = -2
    }
}

/**
 * @property uuid 个人中心的uuid
 * @property userNickName 用户名称
 * @property userAccount 用户账号
 * @property iconUrl 用户头像的url，网络地址
 * @constructor 获取个人中心账号信息 result -1未登录 -2未实名 1登录;只有登录状态有data
 */
data class UserLoginStatus(
    val uuid: String, val userNickName: String, val userAccount: String, val iconUrl: String
)

/**
 * @property uuid 个人中心的uuid
 * @property token DAToken,退登事件无该值
 * @property status 登陆事件1，退登事件-1
 * @constructor 监听个人中心登陆情况
 */
data class DispatchUserLoginStatus(
    val uuid: String, val token: String, val status: Int = USER_SIGN_IN
) {
    companion object {
        const val USER_SIGN_IN = 1
        const val USER_SIGN_OUT = -1
    }
}