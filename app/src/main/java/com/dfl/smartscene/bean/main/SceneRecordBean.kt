package com.dfl.smartscene.bean.main

import com.chad.library.adapter.base.entity.MultiItemEntity

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/23
 * desc   : 场景记录，支持多布局类型
 * version: 1.0
 */
data class SceneRecordBean(
    var title: String,
    var record: SceneRecordEntity,
    var secondRecord: SceneRecordEntity? = null,  // 第二个记录，用于一行两列显示
    override var itemType: Int = TYPE_RECORD
) : MultiItemEntity {
    
    companion object {
        const val TYPE_DATE = 1    // 日期类型
        const val TYPE_RECORD = 2  // 记录类型
        
        /**
         * 创建日期头部item
         */
        fun createDateItem(dateTitle: String): SceneRecordBean {
            val emptyRecord = SceneRecordEntity(
                srId = null,
                scenarioGroupId = "",
                sceneName = "",
                finishSceneTime = 0L,
                startSceneResult = 0
            )
            return SceneRecordBean(
                title = dateTitle,
                record = emptyRecord,
                secondRecord = null,
                itemType = TYPE_DATE
            )
        }
        
        /**
         * 创建单个记录item
         */
        fun createRecordItem(title: String, record: SceneRecordEntity): SceneRecordBean {
            return SceneRecordBean(
                title = title,
                record = record,
                secondRecord = null,
                itemType = TYPE_RECORD
            )
        }
        
        /**
         * 创建配对记录item（一行两列）
         */
        fun createPairedRecordItem(firstRecord: SceneRecordBean, secondRecord: SceneRecordBean?): SceneRecordBean {
            return SceneRecordBean(
                title = firstRecord.title,
                record = firstRecord.record,
                secondRecord = secondRecord?.record,
                itemType = TYPE_RECORD
            )
        }
    }
}
