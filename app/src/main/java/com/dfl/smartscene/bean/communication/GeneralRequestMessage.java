package com.dfl.smartscene.bean.communication;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * <pre>
 * desc   : aidl 通用请求实体类
 * version: 1.0
 * </pre>
 */

public class GeneralRequestMessage implements Parcelable {
    private int protocolId;
    private String requestCode;
    private String versionName;
    private String requestAuthor;
    private String messageType;
    private Object data;

    public GeneralRequestMessage() {
    }

    public GeneralRequestMessage(int protocolId, String versionName, String requestAuthor,
                                 String messageType, Object data) {
        this.protocolId = protocolId;
        this.versionName = versionName;
        this.requestAuthor = requestAuthor;
        this.messageType = messageType;
        this.data = data;
    }

    public GeneralRequestMessage(int protocolId, String requestCode,
                                 String versionName, String requestAuthor, String messageType, Object data) {
        this.protocolId = protocolId;
        this.requestCode = requestCode;
        this.versionName = versionName;
        this.requestAuthor = requestAuthor;
        this.messageType = messageType;
        this.data = data;
    }

    protected GeneralRequestMessage(Parcel in) {
        protocolId = in.readInt();
        requestCode = in.readString();
        versionName = in.readString();
        requestAuthor = in.readString();
        messageType = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(protocolId);
        dest.writeString(requestCode);
        dest.writeString(versionName);
        dest.writeString(requestAuthor);
        dest.writeString(messageType);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<GeneralRequestMessage> CREATOR = new Creator<GeneralRequestMessage>() {
        @Override
        public GeneralRequestMessage createFromParcel(Parcel in) {
            return new GeneralRequestMessage(in);
        }

        @Override
        public GeneralRequestMessage[] newArray(int size) {
            return new GeneralRequestMessage[size];
        }
    };

    public int getProtocolId() {
        return protocolId;
    }

    public void setProtocolId(int protocolId) {
        this.protocolId = protocolId;
    }

    public String getRequestCode() {
        return requestCode;
    }

    public void setRequestCode(String requestCode) {
        this.requestCode = requestCode;
    }

    public String getVersionName() {
        return versionName;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public String getRequestAuthor() {
        return requestAuthor;
    }

    public void setRequestAuthor(String requestAuthor) {
        this.requestAuthor = requestAuthor;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "JsonRequest{" +
                "protocolId=" + protocolId +
                ", requestCode='" + requestCode + '\'' +
                ", versionName='" + versionName + '\'' +
                ", requestAuthor='" + requestAuthor + '\'' +
                ", messageType='" + messageType + '\'' +
                ", data=" + data +
                '}';
    }
}
