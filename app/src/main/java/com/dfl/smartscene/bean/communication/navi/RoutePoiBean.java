//package com.dfl.smartscene.basemodule.entity.navi;
//
//
//import java.util.List;
//
///**
// * 语音算路的点
// *
// * <AUTHOR>
// */
//public class RoutePoiBean {
//
//
//    /**
//     * name : XX加油站
//     * address : XXXX
//     * lon : 28.236545
//     * lat : 114.232452
//     * typeCode : 010100
//     * adCode : 431000
//     * poiId : 1232144
//     * childPois : [{"name":"","childType":1,"lon":"28.236545","lat":"114.232452"},{},{}]
//     * polygonBounds : [{"lon":"28.236545","lat":"114.232452"},{"lon":"28.236545","lat":"114
//     * .232452"},{}]
//     */
//
//    private String name;
//    private String address;
//    private double lon;
//    private double lat;
//    private String typeCode;
//    private String adCode;
//    private String poiId;
//    /**
//     * name :
//     * childType : 1
//     * lon : 28.236545
//     * lat : 114.232452
//     */
//
//    private List<ChildPoisBean> childPois;
//    /**
//     * lon : 28.236545
//     * lat : 114.232452
//     */
//
//    private List<GeoPoint> polygonBounds;
//
//
//
//    public String getName() {
//        return name;
//    }
//
//    public void setName(String name) {
//        this.name = name;
//    }
//
//    public String getAddress() {
//        return address;
//    }
//
//    public void setAddress(String address) {
//        this.address = address;
//    }
//
//    public double getLon() {
//        return lon;
//    }
//
//    public void setLon(double lon) {
//        this.lon = lon;
//    }
//
//    public double getLat() {
//        return lat;
//    }
//
//    public void setLat(double lat) {
//        this.lat = lat;
//    }
//
//    public String getTypeCode() {
//        return typeCode;
//    }
//
//    public void setTypeCode(String typeCode) {
//        this.typeCode = typeCode;
//    }
//
//    public String getAdCode() {
//        return adCode;
//    }
//
//    public void setAdCode(String adCode) {
//        this.adCode = adCode;
//    }
//
//    public String getPoiId() {
//        return poiId;
//    }
//
//    public void setPoiId(String poiId) {
//        this.poiId = poiId;
//    }
//
//    public List<ChildPoisBean> getChildPois() {
//        return childPois;
//    }
//
//    public void setChildPois(List<ChildPoisBean> childPois) {
//        this.childPois = childPois;
//    }
//
//    public List<GeoPoint> getPolygonBounds() {
//        return polygonBounds;
//    }
//
//    public void setPolygonBounds(List<GeoPoint> polygonBounds) {
//        this.polygonBounds = polygonBounds;
//    }
//
//    public RoutePoiBean() {
//    }
//
//    public RoutePoiBean(String name, String address, double lon, double lat, String typeCode,
//                        String adCode, String poiId) {
//        this.name = name;
//        this.address = address;
//        this.lon = lon;
//        this.lat = lat;
//        this.typeCode = typeCode;
//        this.adCode = adCode;
//        this.poiId = poiId;
//    }
//
//    public static class ChildPoisBean {
//        private String name;
//        private int childType;
//        private double lon;
//        private double lat;
//
//        public String getName() {
//            return name;
//        }
//
//        public void setName(String name) {
//            this.name = name;
//        }
//
//        public int getChildType() {
//            return childType;
//        }
//
//        public void setChildType(int childType) {
//            this.childType = childType;
//        }
//
//        public double getLon() {
//            return lon;
//        }
//
//        public void setLon(double lon) {
//            this.lon = lon;
//        }
//
//        public double getLat() {
//            return lat;
//        }
//
//        public void setLat(double lat) {
//            this.lat = lat;
//        }
//    }
//
//    public static class GeoPoint {
//        private double lat;
//        private double lon;
//
//        public double getLat() {
//            return lat;
//        }
//
//        public void setLat(double lat) {
//            this.lat = lat;
//        }
//
//        public double getLon() {
//            return lon;
//        }
//
//        public void setLon(double lon) {
//            this.lon = lon;
//        }
//    }
//}
