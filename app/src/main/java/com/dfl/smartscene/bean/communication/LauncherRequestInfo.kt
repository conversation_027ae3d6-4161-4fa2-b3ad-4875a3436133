package com.dfl.smartscene.bean.communication

/**
 * author:z<PERSON><PERSON><PERSON>
 * e-mail:z<PERSON><PERSON><PERSON>@dfl.com.cn
 * time: 2023/12/31
 * desc: launcher请求的data信息
 * version:1.0
 */
data class LauncherRequestInfo(
    /**
     * 场景名称
     */
    val name: String,
    /**
     * 场景id
     */
    val id: String
)

/**
 * 智慧场景常规卡片的数据，只需要发送收藏的场景数量，最多3个
 */
data class CollectSceneList(
    val quickSceneList: List<LauncherRequestInfo>
)

/**
 * 智慧场景收藏场景正在运行的状态
 */
data class RunSceneList(
    val executingList: List<LauncherRequestInfo>
)