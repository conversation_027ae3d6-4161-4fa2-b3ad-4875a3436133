package com.dfl.smartscene.bean.community.reponse

import android.graphics.Color
import android.os.Parcelable
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.community.request.CommunityTabType
import com.dfl.android.common.global.GlobalConfig
import com.iauto.scenarioadapter.ScenarioInfo
import kotlinx.parcelize.Parcelize
import kotlin.random.Random

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2023/10/30
 * desc:社区场景详情
 * version:1.0
 */
@Parcelize
data class CommunitySceneInfo(
    /**下载数*/
    var downloads: Int? = 0,

    /**点赞数*/
    var likes: Int? = 0,

    /** 上传日期时间戳*/
    val uploadTimestamp: Long = 0L,

    /**用户id*/
    var uuid: String? = "",

    /**用户名称*/
    var userName: String? = "",

    /** 用户头像URL*/
    var userAvatar: String? = "",

    /**用户是否关注，三种状态:0未关注，1已关注，2用户自己的场景*/
    var userSubscribe: Int = 0,

    /**场景是否被点赞 true点赞*/
    var isLike: Boolean? = false,

    /**是否官方 1-官方*/
    var isOfficial: Int? = 0,

    /**社区场景的id*/
    val communityScenarioId: String? = "",

    /**服务端 社区场景详情*/
    val scenarioInfo: ScenarioInfoConverter? = null,

    /**车机端 社区场景详情*/
    var scenarioInfoDa: ScenarioInfo? = null,

    /**是否显示闪烁*/
    var isShowLottieAnimation: Boolean = false,

    /**轮播图的img 白天图*/
    var bannerImg: String? = "",
    /**轮播图黑夜图*/
    var darkBannerImg: String? = "",

    ) : Parcelable {

    //转换
    fun convert() {
        scenarioInfoDa = scenarioInfo?.convert()
    }

    //是否是加载态
    fun isLoadingStatus(): Boolean {
        return downloads == -1 && likes == -1 && uploadTimestamp == -1L
    }

    //根据点赞状态 获取图片
    fun getLikeImg(): Int {
        return if (isLike == true) R.drawable.scene_icon_community_card_likes_selected else R.drawable.scene_icon_community_card_likes_normal
    }

    //根据点赞状态 获取文字颜色
    fun getLikeTextColor(): Int {
        return if (isLike == true) R.color.scene_primary_color_highlight else R.color.scene_color_text_1
    }

    //根据点赞状态 获取文字颜色 轮播图
    fun getLikeTextColor4Banner(): Int {
        return if (isLike == true) Color.parseColor("#FFDF7727") else Color.parseColor("#FF1D1D1E")
    }


    companion object {
        /**
         * 是否以及关注
         * @param userSubscribe 关注状态参数
         * @return
         */
        fun isSubscribed(userSubscribe: Int): Boolean {
            return userSubscribe == 1
        }

        /**
         * 用于账号无上传场景情况下关注变化,创建简易CommunitySceneInfo通过LiveEventBus广播
         * @return 有效字段只有输入的字段
         */
        fun createInstance(uuid: String, userSubscribe: Int): CommunitySceneInfo {
            val scenarioInfoDa: ScenarioInfo = ScenarioInfo(
                "1", "1", "1", 1, 1, 1, 1, "", 0, null, null, null
            )
            return CommunitySceneInfo(
                1, 1, 1, uuid, null, null, userSubscribe, null, null, null, null, scenarioInfoDa, false, null
            )
        }

        fun getBannerList(pageNum: Int): ArrayList<CommunitySceneInfo> {
            var list = ArrayList<CommunitySceneInfo>()
            for (i in 100 * pageNum until 100 * pageNum + 5) {
                var scenarioInfoDa: ScenarioInfo = ScenarioInfo(
                    i.toString(),
                    "轮播场景" + i.toString(),
                    " ${i} 我是desc，开启导航至公司，播放音频节目并调节车内环境开启导航至公司，播放音频节目并调节车内环境开启导航至公司，播放音频节目并调节车内环境。",
                    i,
                    i,
                    i,
                    System.currentTimeMillis(),
                    "",
                    0,
                    null,
                    null,
                    null
                )
                list.add(
                    CommunitySceneInfo(
                        i,
                        i + 1,
                        System.currentTimeMillis(),
                        "${i}",
                        "ABC${i}",
                        "https://img1.baidu.com/it/u=2526782352,137544254&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500",
                        Random.nextInt(3),
                        Random.nextInt(2) == 0,
                        0,
                        "changjing${i}",
                        null,
                        scenarioInfoDa,
                        isShowLottieAnimation = false,
                        bannerImg = "",
                        darkBannerImg = "",
                    )
                )
            }
            return list;
        }


        fun getRvList(tabType: CommunityTabType, pageNum: Int): ArrayList<CommunitySceneInfo> {
            var list = ArrayList<CommunitySceneInfo>()
            for (i in 200 * pageNum until 200 * pageNum + 10) {
                var scenarioInfoDa: ScenarioInfo = ScenarioInfo(
                    i.toString(),
                    "我是 " + tabType.getName() + "场景" + i.toString(),
                    " ${i} 我是desc，开启导航至公司，播放音频节目并调节车内环境开启导航至公司，播放音频节目并调节车内环境开启导航至公司，播放音频节目并调节车内环境。",
                    i,
                    i,
                    i,
                    System.currentTimeMillis(),
                    "",
                    0,
                    null,
                    null,
                    null
                )
                list.add(
                    CommunitySceneInfo(
                        i,
                        i + 1,
                        System.currentTimeMillis(),
                        "${i}",
                        "ABC${i}",
                        "https://img1.baidu.com/it/u=2526782352,137544254&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500",
                        Random.nextInt(3),
                        Random.nextInt(2) == 0,
                        0,
                        "changjing${i}",
                        null,
                        scenarioInfoDa,
                        isShowLottieAnimation = false,
                        bannerImg = "",
                        darkBannerImg = "",
                    )
                )
            }
            return list
        }

        //显示无数据显示
        fun getErrorNullList(): MutableList<CommunitySceneInfo?> {
            var list = mutableListOf<CommunitySceneInfo?>()
            for (i in 0 until GlobalConfig.REQ_PAGE_SIZE) {
                list.add(
                    CommunitySceneInfo(
                        downloads = -1,
                        likes = -1,
                        uploadTimestamp = -1L,
                        uuid = "",
                        userName = "",
                        userAvatar = "",
                        userSubscribe = 2,
                        isLike = false,
                        isOfficial = 0,
                        communityScenarioId = "",
                        scenarioInfo = null,
                        scenarioInfoDa = null,
                        isShowLottieAnimation = false,
                        bannerImg = "",
                        darkBannerImg = "",
                    )
                )
            }
            return list
        }

        //显示闪烁加载
        fun getLottieList(): MutableList<CommunitySceneInfo?> {
            val list = mutableListOf<CommunitySceneInfo?>()
            for (i in 0 until GlobalConfig.REQ_PAGE_SIZE) {
                list.add(
                    CommunitySceneInfo(
                        downloads = -1,
                        likes = -1,
                        uploadTimestamp = -1L,
                        uuid = "",
                        userName = "",
                        userAvatar = "",
                        userSubscribe = 2,
                        isLike = false,
                        isOfficial = 0,
                        communityScenarioId = "",
                        scenarioInfo = null,
                        scenarioInfoDa = null,
                        isShowLottieAnimation = true,
                        bannerImg = "LoadImg",
                        darkBannerImg = ""
                    )
                )
            }
            return list
        }
    }

}
