package com.dfl.smartscene.bean.intermediate

import com.fasterxml.jackson.databind.JsonNode
import org.json.JSONObject

/**
 *Created by 钟文祥 on 2025/3/26.
 *Describer: 传入中间层的 对象
 */
data class IMScenarioInfo(
    /**场景id格式，VEHICLE_+时间戳System.currentTimeMillis()*/
    private var scenarioId: String? = null,
    /**场景名称  ps:最多10个字*/
    private var scenarioName: String? = null,
    /**场景更新次数，初始0修改+1*/
    private var version: Int = 0,
    /**自动执行,0自动执行，1不自动执行*/
    private var autoExeFlag: Int = 0,
    /**自动执行前询问，0询问，1不询问*/
    private var secondAskeFlag: Int = 0,
    /**场景更新时间戳*/
    private var scenarioTimeStamp: Long = 0,
    /**自动执行前频率，2a新字段*/
    private var executeFrequency: Int = 0,
    /**触发条件*/
    private var edgeCondition: IMCondition? = null,
    /**状态条件列表*/
    private var conditions: List<IMCondition>? = null,
    /**动作列表*/
    private var sequence: List<IMSequence>? = null
) {
    companion object {
        const val timeOutSecond: Int = 10 //服务超时秒数

        fun getTestData(): IMScenarioInfo {
            val edgeCondition = IMCondition(
                skillValue = "condition.power.HVBatSOCNotifyGreater",
                category = "时间点",
                desc = "XX",
                id = 0,
                input = arrayListOf(
                    IMInputArgInfo(name = "hvBatSOC", type = IMArgType.DOUBLE, value = "5"),
                    //IMInputArgInfo(name = "minute", type = IMArgType.INT32, value = "6"),
                    //IMInputArgInfo(name = "second", type = IMArgType.INT32, value = "7"),
                )
            )
            //val edgeCondition = IMCondition(
            //    skillValue = "condition.common.Time", category = "时间点", desc = "XX", id = 0, input = arrayListOf(
            //        IMInputArgInfo(name = "hour", type = IMArgType.INT32, value = "5"),
            //        IMInputArgInfo(name = "minute", type = IMArgType.INT32, value = "6"),
            //        IMInputArgInfo(name = "second", type = IMArgType.INT32, value = "7"),
            //    )
            //)
            //            val edgeCondition = IMCondition(
            //                skillValue = "condition.env.InsideTempNotifyGreater",
            //                category = "车内温度大于",
            //                desc = "130℃",
            //                id = 0,
            //                input = arrayListOf(IMInputArgInfo(name = "InsideTemp", type = IMArgType.DOUBLE, value = "30"))
            //            )
            //val edgeCondition = IMCondition(
            //    skillValue = "condition.navi.ArriveDestinationNotify",
            //    category = "车辆到达",
            //    desc = "%d",
            //    id = 0,
            //    input = arrayListOf(
            //        IMInputArgInfo(name = "latitude", type = IMArgType.DOUBLE, value = ""),
            //        IMInputArgInfo(name = "longitude", type = IMArgType.DOUBLE, value = ""),
            //        IMInputArgInfo(name = "distance", type = IMArgType.INT32, value = "200")
            //    ),
            //    params = arrayListOf(IMParams(index = 0, arg = "平安"))
            //)

            val conditions = arrayListOf<IMCondition>(
                //    //                IMCondition(
                //    //                    skillValue = "condition.common.OnceEveryWeek",
                //    //                    category = "周期",
                //    //                    desc = "XX",
                //    //                    id = 0,
                //    //                    input = arrayListOf(
                //    //                        IMInputArgInfo(name = "day", type = IMArgType.INT32, value = "7"),
                //    //                    )
                //    //                ),
                //    IMCondition(
                //        skillValue = "condition.common.TimeRanges",
                //        category = "时间区间",
                //        desc = "XX",
                //        id = 0,
                //        input = arrayListOf(
                //            IMInputArgInfo(name = "start_hour", type = IMArgType.INT32, value = "23"),
                //            IMInputArgInfo(name = "start_minute", type = IMArgType.INT32, value = "4"),
                //            IMInputArgInfo(name = "start_second", type = IMArgType.INT32, value = "5"),
                //            IMInputArgInfo(name = "stop_hour", type = IMArgType.INT32, value = "6"),
                //            IMInputArgInfo(name = "stop_minute", type = IMArgType.INT32, value = "7"),
                //            IMInputArgInfo(name = "stop_second", type = IMArgType.INT32, value = "8"),
                //        )
                //    ),
                //    IMCondition(
                //        skillValue = "condition.common.Date", category = "周期", desc = "XX", id = 0, input = arrayListOf(
                //            IMInputArgInfo(name = "year", type = IMArgType.INT32, value = "2010"),
                //            IMInputArgInfo(name = "month", type = IMArgType.INT32, value = "4"),
                //            IMInputArgInfo(name = "day", type = IMArgType.INT32, value = "8")
                //        )
                //    ),
                //    IMCondition(
                //        skillValue = "condition.navi.ArrivalTimeLess",
                //        category = "导航预计到达时间",
                //        desc = "XX",
                //        id = 0,
                //        input = arrayListOf(
                //            IMInputArgInfo(name = "h", type = IMArgType.INT32, value = "3"),
                //            IMInputArgInfo(name = "min", type = IMArgType.INT32, value = "4"),
                //        )
                //    ),

                //                IMCondition(
                //                    skillValue = "condition.lock.CarLockStatus",
                //                    category = "全车门锁",
                //                    desc = "上锁",
                //                    id = 0,
                //                    input = arrayListOf(IMInputArgInfo(name = "status", type = IMArgType.INT32, value = "1"))
                //                ),
                //                IMCondition(
                //                    skillValue = "condition.navi.DistanceToPoiStatusGreater",
                //                    category = "车辆不位于",
                //                    desc = "%d",
                //                    id = 0,
                //                    input = arrayListOf(
                //                        IMInputArgInfo(name = "latitude", type = IMArgType.DOUBLE, value = ""),
                //                        IMInputArgInfo(name = "longitude", type = IMArgType.DOUBLE, value = ""),
                //                        IMInputArgInfo(name = "distance", type = IMArgType.INT32, value = "700")
                //                    ),
                //                    params = arrayListOf(IMParams(index = 0, arg = "收藏夹"))
                //                ),
                //                IMCondition(
                //                    skillValue = "condition.weather.WeatherStatusEqual",
                //                    category = "天气预报",
                //                    desc = "%d",
                //                    id = 0,
                //                    input = arrayListOf(IMInputArgInfo(name = "status", type = IMArgType.INT32, value = "2"))
                //                ),
                //                IMCondition(
                //                    skillValue = "condition.weather.WeatherStatusEqual",
                //                    category = "天气预报",
                //                    desc = "%d",
                //                    id = 0,
                //                    input = arrayListOf(IMInputArgInfo(name = "status", type = IMArgType.INT32, value = "2"))
                //                ),
                //                IMCondition(
                //                    skillValue = "condition.weather.WeatherStatusEqual",
                //                    category = "天气预报",
                //                    desc = "%d",
                //                    id = 0,
                //                    input = arrayListOf(IMInputArgInfo(name = "status", type = IMArgType.INT32, value = "2"))
                //                ),
                //                IMCondition(
                //                    skillValue = "condition.weather.WeatherStatusEqual",
                //                    category = "天气预报",
                //                    desc = "%d",
                //                    id = 0,
                //                    input = arrayListOf(IMInputArgInfo(name = "status", type = IMArgType.INT32, value = "2"))
                //                ),
                //                IMCondition(
                //                    skillValue = "condition.weather.WeatherStatusEqual",
                //                    category = "天气预报",
                //                    desc = "%d",
                //                    id = 0,
                //                    input = arrayListOf(IMInputArgInfo(name = "status", type = IMArgType.INT32, value = "2"))
                //                ),
            )
            val sequence = arrayListOf<IMSequence>(
                //IMSequence(
                //    id = 0, type = 1, action = IMAction(
                //        skillValue = "action.common.Delay",
                //        category = "延时",
                //        desc = "",
                //        input = arrayListOf(IMInputArgInfo(name = "second", type = IMArgType.INT32, value = "1000"))
                //
                //    )
                //),
                //IMSequence(
                //    id = 0, type = 1, action = IMAction(
                //        skillValue = "action.launcher.OpenApplication",
                //        category = "延时",
                //        desc = "",
                //        input = arrayListOf(
                //            IMInputArgInfo(name = "status", type = IMArgType.INT32, value = "1"),
                //            IMInputArgInfo(name = "type", type = IMArgType.INT32, value = "1")
                //        )
                //
                //    )
                //),
                //                IMSequence(
                //                    id = 0, type = 1, action = IMAction(
                //                        skillValue = "action.light.AtmosphereLightBriLevel",
                //                        category = "氛围灯亮度",
                //                        desc = "",
                //                        input = arrayListOf(IMInputArgInfo(name = "brightness", type = IMArgType.INT32, value = "0"))
                //
                //                    )
                //                ),
                //                IMSequence(
                //                    id = 1, type = 1, action = IMAction(
                //                        skillValue = "action.light.AtmosphereLightColor",
                //                        category = "氛围灯颜色",
                //                        desc = "",
                //                        input = arrayListOf(
                //                            IMInputArgInfo(
                //                                name = "color", type = IMArgType.INT32, value = "-209982297"
                //                            )
                //                        )
                //                    )
                //                ),
                //                IMSequence(
                //                    id = 2, type = 1, action = IMAction(
                //                        skillValue = "action.voice.PlaySpeechTTS",
                //                        category = "小尼回复",
                //                        desc = "",
                //                        input = arrayListOf(IMInputArgInfo(name = "content", type = IMArgType.STRING, value = "07563"))
                //
                //                    )
                //                ),
                //
                //                IMSequence(
                //                    id = 2, type = 1, action = IMAction(
                //                        skillValue = "action.navi.NaviRequest",
                //                        category = "发起导航",
                //                        desc = "去%d",
                //                        input = arrayListOf(IMInputArgInfo(name = "json", type = IMArgType.STRING, value = "")),
                //                        params = arrayListOf(
                //                            IMParams(index = 0, arg = "回公司"), IMParams(index = 1, arg = "故宫1234567890")
                //                        )
                //                    )
                //                ),
                //                IMSequence(
                //                    id = 0, type = 1, action = IMAction(
                //                        skillValue = "action.ac.ACSwitch",
                //                        category = "空调开关",
                //                        desc = "打开",
                //                        input = arrayListOf(IMInputArgInfo(name = "hvacswstate", type = IMArgType.INT32, value = "0"))
                //                    )
                //                ),
                //                IMSequence(
                //                    id = 1, type = 1, action = IMAction(
                //                        skillValue = "action.ac.ACTemperature",
                //                        category = "空调温度",
                //                        desc = "%d℃",
                //                        input = arrayListOf(IMInputArgInfo(name = "temp", type = IMArgType.DOUBLE, value = "26.5"))
                //                    )
                //                ),
                //                IMSequence(
                //                    id = 3, type = 1, action = IMAction(
                //                        skillValue = "action.media.PlaySingerSong",
                //                        category = "播放QQ音乐",
                //                        desc = "%d",
                //                        input = arrayListOf(IMInputArgInfo(name = "singer", type = IMArgType.STRING, value = "")),
                //                        params = arrayListOf(IMParams(index = 0, arg = "周杰伦"))
                //                    )
                //                ),
                //                IMSequence(
                //                    id = 3, type = 1, action = IMAction(
                //                        skillValue = "action.media.PlaySong",
                //                        category = "播放QQ音乐",
                //                        desc = "%d",
                //                        input = arrayListOf(IMInputArgInfo(name = "song", type = IMArgType.STRING, value = "")),
                //                        params = arrayListOf(IMParams(index = 0, arg = "生日快乐"))
                //                    )
                //                ),

            )

            val info = IMScenarioInfo(
                scenarioId = "tts_VEHICLE_" + System.currentTimeMillis(),
                scenarioName = "高温", // "高温自动空调     高温自动空调",
                version = 0,
                autoExeFlag = 0,
                secondAskeFlag = 0,
                scenarioTimeStamp = System.currentTimeMillis(),
                edgeCondition = edgeCondition,
                conditions = conditions,
                sequence = sequence
            )
            return info
        }
    }
}

data class IMCondition(
    /**能力语义*/
    private var skillValue: String? = null,
    /**能力类别描述，对应ui标题*/
    private var category: String? = null,
    /**能力描述，对应ui副标题*/
    private var desc: String? = null,
    /**能力顺序，从0开始*/
    private var id: Int = 0,
    /**能力参数列表，没有参数要传空列表*/
    private var input: List<IMInputArgInfo> = emptyList(),
    /**输入中间层额外参数*/
    private var params: List<IMParams>? = null
)

data class IMInputArgInfo(
    /**参数名*/
    private var name: String? = null,
    /**参数值类别*/
    private var type: IMArgType? = null,
    /**参数值*/
    private var value: String? = null
)

data class IMSequence(
    /**动作排序*/
    private var id: Int = 0,
    /**ccm预留参数，填1*/
    private var type: Int = 1,
    /**动作类*/
    private var action: IMAction? = null
)

data class IMAction(
    /**能力语义*/
    private var skillValue: String? = null,
    /**能力类别描述，对应ui标题*/
    private var category: String? = null,
    /**能力描述，对应ui副标题*/
    private var desc: String? = null,
    /**能力参数列表，没有参数要传空列表*/
    private var input: List<IMInputArgInfo> = emptyList(),
    /**输入中间层额外参数*/
    private var params: List<IMParams>? = null
)

//第三方协议需要用到的参数
data class IMParams(
    /**第几个参数 0,1,2,3,4...，按顺序*/
    private var index: Int = 0,
    /**参数值*/
    private var arg: String? = null //最多30个字 地名 歌名 歌手名
)

enum class IMArgType(var value: Int) {
    UNUSED(0), UINT8(1), UINT16(2), UINT32(3), UINT64(4),
    INT8(5), INT16(6), INT32(7), INT64(8), FLOAT(9), DOUBLE(10), STRING(11);

    companion object {
        /**需大写*/
        fun fromIntStr(intStr: String?): IMArgType? { //"0" 或“UNUSED”

            return when (intStr) {

                //常用文字
                INT32.name -> INT32
                FLOAT.name -> FLOAT
                DOUBLE.name -> DOUBLE
                STRING.name -> STRING

                //常用数字
                INT32.value.toString() -> INT32
                FLOAT.value.toString() -> FLOAT
                DOUBLE.value.toString() -> DOUBLE
                STRING.value.toString() -> STRING
                else -> {
                    if (intStr?.contains("INT") == true) {
                        INT32
                    } else {
                        when (intStr) {
                            //少用文字
                            UNUSED.name -> UNUSED
                            UINT8.name -> UINT8
                            UINT16.name -> UINT16
                            UINT32.name -> UINT32
                            UINT64.name -> UINT64
                            INT8.name -> INT8
                            INT16.name -> INT16
                            INT64.name -> INT64

                            //少用数字
                            UNUSED.value.toString() -> UNUSED
                            UINT8.value.toString() -> UINT8
                            UINT16.value.toString() -> UINT16
                            UINT32.value.toString() -> UINT32
                            UINT64.value.toString() -> UINT64
                            INT8.value.toString() -> INT8
                            INT16.value.toString() -> INT16
                            INT64.value.toString() -> INT64
                            else -> null
                        }
                    }
                }
            }
        }

        /**判断是否是int类型，ccm会自动选择合适的比特位格式存储，中间层不严格限制*/
        fun isIntType(type: IMArgType): Boolean {
            return type != FLOAT && type != DOUBLE && type != STRING && type != UNUSED
        }

        /**判断是否是float类型，ccm会自动选择合适的比特位格式存储，中间层不严格限制*/
        fun isFloatType(type: IMArgType): Boolean {
            return type == FLOAT || type == DOUBLE
        }

        /**判断是否是数字类型*/
        fun isNumberType(type: IMArgType): Boolean {
            return type != STRING && type != UNUSED
        }
    }
}


data class IMRes(
    /**0服务成功，1服务失败*/
    var result: Int = 0,
    /**服务失败的原因，服务成功时为空字符串*/
    var failReason: String? = null,
    /**条件支持状态  -1其他(没有动作或条件时回传),0不支持,1部分支持,2全部支持*/
    var conditionMatch: Int = -1,
    /**动作支持状态  -1其他(没有动作或条件时回传),0不支持,1部分支持,2全部支持*/
    var sequenceMatch: Int = -1,
    /**每个能力对应的结果*/
    var resList: List<IMResItem>? = null
)

data class IMResItem(
    /**传入的能力语义*/
    private var skillValue: String? = null,
    /**能力转换后的状态 0成功，1失败，2超时*/
    private var status: Int? = 1,
    /**转化失败的原因，当0成功时为空字符串*/
    private var failReason: String? = null
)

data class Req2NodeARule(
    var reqJsonRootNode: JsonNode? = null, //
    var ruleJson: JSONObject? = null
)

/**InputArgInfo所采用转换方式，赋值给desc，即卡片第二行*/
enum class RuleInputMethod(var value: String) {
    ENUM("enum"), //需要验证枚举
    RANGE("range"), //需要验证取值范围
    THIRDPARTY("thirdParty"), //需要调用第三方
    VALUE2DESC("value2Desc"), //无需验证，将value通过关键字替换
    NOCHECK("noCheck"), //不处理
}
