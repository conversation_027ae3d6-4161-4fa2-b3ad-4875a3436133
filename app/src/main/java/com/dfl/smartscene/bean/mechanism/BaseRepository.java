package com.dfl.smartscene.bean.mechanism;

import com.dfl.android.commonlib.log.CommonLogUtils;
import com.dfl.smartscene.R;
import com.dfl.smartscene.apiweb.utils.ApiException;
import com.dfl.smartscene.apiweb.utils.ExceptionEngine;
import com.dfl.smartscene.application.SceneApplication;
import com.dfl.smartscene.bean.community.request.CommunityTabType;
import com.dfl.android.common.global.GlobalConstant;
import com.dfl.smartscene.room.helper.DBHelper;
import com.dfl.smartscene.room.helper.OnDaoFindsListener;
import com.dfl.smartscene.room.helper.OnDaoInsertListener;
import com.dfl.smartscene.room.helper.OnDaoUpdateOrDeleteListener;
import com.dfl.smartscene.util.NetWorkUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by 钟文祥 on 2023/6/27.
 * Describer:基类 存储库 只封装了获取数据的业务逻辑
 * ps1: loadData()逻辑：
 * 1）LoadType.Cached 读缓存 没有数据读远程
 * 2）LoadType.Refresh 读本地，再读远程 刷新缓存 更新本地
 * 3）LoadType.RefreshOnlyRemote 只读远程  刷新缓存
 * 4)LoadType.LoadMore 读远程 加载更多，新增缓存 ，新增本地
 * 5)CachedAndRefreshOnlyRemote 用内存缓存数据 并 刷新新数据只用远程
 */
public abstract class BaseRepository<T> {
    private static final String TAG = GlobalConstant.GLOBAL_TAG + "数据库";
    /**
     * 缓存社区首页tab数据
     */
    public Map<CommunityTabType, T> mCachedDataMap = new HashMap<>();
    /**
     * 加载形式
     */
    private Map<CommunityTabType, LoadType> mLoadTypeMap = new HashMap<>();
    /**
     * 缓存社区首页tab数据源
     */
    private Map<CommunityTabType, IBaseDataSourceCallBack> mDataSourceMap = new HashMap<>();


    public <T> BaseRepository() {
    }

    /**
     * 重新赋值 避免单例同一值
     */
    public void setDataSource(CommunityTabType tagType, IBaseDataSourceCallBack mDataSource) {
        this.mDataSourceMap.put(tagType, mDataSource);
    }

    public IBaseDataSourceCallBack getDataSource(CommunityTabType tagType) {
        return mDataSourceMap.get(tagType);
    }

    /**
     * 加载远程数据
     */
    private void loadRemoteData(CommunityTabType tabType, ILoadDataCallBack<T> callBack) {
        loadRemoteData(tabType, callBack, true);
    }

    /***
     * 加载远程数据
     * @param tabType
     * @param callBack
     * @param isNeedPostNull 是否需要在无网时发送 空数据，默认为true。频道页加载内存后用false
     */
    private void loadRemoteData(CommunityTabType tabType, ILoadDataCallBack<T> callBack,
                                boolean isNeedPostNull) {
        if (!NetWorkUtils.INSTANCE.isConnectNetWork()) {
            ApiException ex = new ApiException(new Throwable(),
                    ExceptionEngine.CONNECT_NETWORD_ERROR);
            ex.toastMsg = ex.message =
                    SceneApplication.Companion.getContextObject().getString(R.string.scene_toast_web_exception_device_no_network);  //⽹络未连接
            if (isNeedPostNull) {
                callBack.onDataIsNullOrError(BaseDataSource.DataSourceType.Remote);
                callBack.onApiError(ex);
            }
            return;
        }

        mDataSourceMap.get(tabType).loadDataFromWeb(new ILoadDataCallBack<T>() {
            @Override
            public void onLoadRemoteStart() {
                callBack.onLoadRemoteStart();
            }

            @Override
            public void onDataLoaded(T values, BaseDataSource.DataSourceType type) {
                switch (mLoadTypeMap.get(tabType)) {
                    case Refresh:
                        //刷新缓存
                        refreshCache(tabType, values);
                        //刷新本地并保存
                        deleteAllAndSave2Local(tabType, values);
                        break;
                    case RefreshOnlyRemote:
                        //刷新缓存
                        refreshCache(tabType, values);
                        //刷新本地并保存
                        deleteAllAndSave2Local(tabType, values);
                        break;
                    case LoadMore:
                        //新增缓存
                        addCache(tabType, values);
                        //  2023/12/7 可以考虑去掉  add2Local
                        //新增本地
                        add2Local(tabType, values);
                        break;
                }
                callBack.onDataLoaded(values, BaseDataSource.DataSourceType.Remote);
            }

            @Override
            public void onDataIsNullOrError(BaseDataSource.DataSourceType type) {
                callBack.onDataIsNullOrError(type);
            }

            @Override
            public void onApiError(ApiException ex) {
                callBack.onApiError(ex);
            }
        });
    }


    //加载本地数据
    private void loadLocalAndRemoteData(CommunityTabType tabType, ILoadDataCallBack<T> callBack) {
        DBHelper.daoFinds(new OnDaoFindsListener<T>() {
            @Override
            public T call() {
                return (T) mDataSourceMap.get(tabType).loadDataFromLocal();
            }

            @Override
            public void onSuccess(T t) {
                if (t != null) {
                    if (t instanceof List) {
                        if (((List<?>) t).size() > 0) {
                            callBack.onDataLoaded(t, BaseDataSource.DataSourceType.Local);
                        } else {
                            callBack.onDataIsNullOrError(BaseDataSource.DataSourceType.Local);
                        }
                    } else {
                        callBack.onDataLoaded(t, BaseDataSource.DataSourceType.Local);
                    }
                } else {
                    callBack.onDataIsNullOrError(BaseDataSource.DataSourceType.Local);
                }
                loadRemoteData(tabType, callBack);
            }

            @Override
            public void onError(Throwable e) {
                callBack.onDataIsNullOrError(BaseDataSource.DataSourceType.Local);
                loadRemoteData(tabType, callBack);
            }
        });
    }

    private void deleteAllAndSave2Local(CommunityTabType tabType, T values) {
        DBHelper.doUpdateOrDelete(new OnDaoUpdateOrDeleteListener() {
            @Override
            public Integer call() {
                return mDataSourceMap.get(tabType).deleteAll2Local();
            }

            @Override
            public void onSuccess(int integer) {

                DBHelper.doInsert(new OnDaoInsertListener() {
                    @Override
                    public List<Long> call() {
                        return mDataSourceMap.get(tabType).add2Local(values);
                    }

                    @Override
                    public void onSuccess(List<Long> longs) {
                        //返回添加记录的id
                        CommonLogUtils.logD(TAG, "deleteAllAndSave2Local新增数据库成功:" + longs);
                    }

                    @Override
                    public void onError(Throwable e) {
                        CommonLogUtils.logD(TAG, "deleteAllAndSave2Local新增数据库失败:" + e.getMessage());
                    }
                });
            }

            @Override
            public void onError(Throwable e) {

            }
        });
    }

    private void add2Local(CommunityTabType tabType, T values) {
        DBHelper.doInsert(new OnDaoInsertListener() {
            @Override
            public List<Long> call() {
                return mDataSourceMap.get(tabType).add2Local(values);
            }

            @Override
            public void onSuccess(List<Long> longs) {
                //返回添加记录的id
                CommonLogUtils.logD(TAG, "add2Local新增数据库成功:" + longs);
            }

            @Override
            public void onError(Throwable e) {
                CommonLogUtils.logD(TAG, "add2Local新增数据库失败:" + e.getMessage());
            }
        });
    }

    //刷新缓存
    private void refreshCache(CommunityTabType tabType, T values) {
        mCachedDataMap.put(tabType, values);
    }

    //添加缓存
    private void addCache(CommunityTabType tabType, T values) {
        if (!overwriteAddCache(tabType, values)) {
            if (values instanceof List) {
                ((List) mCachedDataMap.get(tabType)).addAll((List) values);
            } else {
                mCachedDataMap.put(tabType, values);
            }
        }
    }

    /***
     *
     * @param tabType
     * @param values
     * @return 返回true 表示自己重写，不采纳父类的方法
     */
    public abstract boolean overwriteAddCache(CommunityTabType tabType, T values);


    /**
     * 需要从内存缓存数据
     */
    public void needCachedData(CommunityTabType tabType) {
        mLoadTypeMap.put(tabType, LoadType.Cached);
    }

    /**
     * 需要从刷新新的数据
     */
    public void needRefresh(CommunityTabType tabType) {
        mLoadTypeMap.put(tabType, LoadType.Refresh);
    }

    /**
     * 需要从刷新新的数据 只用远程
     */
    public void needRefreshOnlyRemote(CommunityTabType tabType) {
        mLoadTypeMap.put(tabType, LoadType.RefreshOnlyRemote);
    }

    /**
     * 需要加载更多数据
     */
    public void needLoadMore(CommunityTabType tabType) {
        mLoadTypeMap.put(tabType, LoadType.LoadMore);
    }


    /**
     * 获取数据
     * 添加VideoResTabType 解决不同tab的缓存的区别，只用在首页tab页 ，其他场景用上面不带VideoResTabType参数的
     */
    public void loadData(CommunityTabType tabType, ILoadDataCallBack<T> callBack) {
        switch (mLoadTypeMap.get(tabType)) {
            case Cached: //用内存缓存数据,没有就用远程数据
                if (mCachedDataMap.get(tabType) != null) {
                    callBack.onDataLoaded(mCachedDataMap.get(tabType),
                            BaseDataSource.DataSourceType.Cached);
                } else {
                    mLoadTypeMap.put(tabType, LoadType.Refresh);
                    loadLocalAndRemoteData(tabType, callBack);
                }
                break;
            case Refresh: //刷新新数据
                loadLocalAndRemoteData(tabType, callBack);
                break;
            case RefreshOnlyRemote: //刷新新数据 只用远程
                loadRemoteData(tabType, callBack);
                break;
            case LoadMore: //加载更多数据
                loadRemoteData(tabType, callBack);
                break;
            case CachedAndRefreshOnlyRemote: //用内存缓存数据 并 刷新新数据只用远程
                if (mCachedDataMap.get(tabType) != null) {
                    mLoadTypeMap.put(tabType, LoadType.Cached);
                    callBack.onDataLoaded(mCachedDataMap.get(tabType),
                            BaseDataSource.DataSourceType.Cached);
                    mLoadTypeMap.put(tabType, LoadType.RefreshOnlyRemote);
                    loadRemoteData(tabType, callBack, false);
                } else {
                    mLoadTypeMap.put(tabType, LoadType.Refresh);
                    loadLocalAndRemoteData(tabType, callBack);
                }
                break;
        }
    }

    public void setLoadType(CommunityTabType tabType, LoadType mLoadType) {
        this.mLoadTypeMap.put(tabType, mLoadType);
    }

    public LoadType getLoadType(CommunityTabType tabType) {
        return mLoadTypeMap.get(tabType);
    }

    /**
     * 加载形式
     */
    public enum LoadType {
        /**
         * 用内存缓存数据
         */
        Cached(0),
        /**
         * 刷新新数据,本地和远程
         */
        Refresh(1),
        /**
         * 刷新新数据只用远程
         */
        RefreshOnlyRemote(2),
        /**
         * 远程加载更多
         */
        LoadMore(3),
        /**
         * 用内存缓存数据 并 刷新新数据只用远程
         */
        CachedAndRefreshOnlyRemote(4);


        private int value;

        LoadType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }
}
