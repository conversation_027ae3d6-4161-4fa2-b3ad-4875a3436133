package com.dfl.smartscene.bean.community.datasource

import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.apiweb.utils.ApiException
import com.dfl.smartscene.apiweb.utils.ExceptionEngine
import com.dfl.smartscene.bean.community.reponse.CommunitySceneInfo
import com.dfl.smartscene.bean.community.reponse.CommunityUserInfo
import com.dfl.smartscene.bean.community.request.CommunityFocusRequest
import com.dfl.smartscene.bean.mechanism.BaseDataSource
import com.dfl.smartscene.bean.mechanism.BaseRepository.LoadType
import com.dfl.smartscene.bean.mechanism.ILoadDataCallBack
import com.dfl.smartscene.room.helper.DBHelper
import com.dfl.smartscene.room.helper.OnDaoFindsListener
import com.dfl.smartscene.room.helper.OnDaoInsertListener
import com.dfl.smartscene.room.helper.OnDaoUpdateOrDeleteListener
import com.dfl.smartscene.util.NetWorkUtils
import java.util.WeakHashMap

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/05/10
 * desc : 账号页存储库操作回调类
 * version: 1.0
 */
class UserCenterRepository() {
    private val TAG = GlobalConstant.GLOBAL_TAG.plus("UserCenterRepository")

    /**
     * 本地和远程数据操作回调
     */
    var dataSourceCallBack: UserCenterDataSource = UserCenterDataSource(1, 0, "")

    /**
     * 缓存的用户数据 key:uuid，使用弱引用防止oom
     */
    val cachedDataMap: WeakHashMap<String, CommunityUserInfo> = WeakHashMap()

    /**
     * 加载形式
     */
    var loadType: LoadType = LoadType.Cached

    companion object {
        fun getInstance() = INSTANCE.instance
    }

    private object INSTANCE {
        val instance = UserCenterRepository()
    }

    /**
     * 加载远程数据
     */
    private fun loadRemoteData(callBack: ILoadDataCallBack<CommunityUserInfo>) {
        //检查网络状态
        if (!NetWorkUtils.isConnectNetWork()) {
            val ex = ApiException(Throwable(), ExceptionEngine.CONNECT_NETWORD_ERROR)
            ex.message = CommonUtils.getString(R.string.scene_toast_web_exception_device_no_network)
            ex.toastMsg = ex.message
            callBack.onApiError(ex)
            return
        }
        dataSourceCallBack.loadDataFromWeb(object : ILoadDataCallBack<CommunityUserInfo> {
            override fun onLoadRemoteStart() {
                callBack.onLoadRemoteStart()
            }

            override fun onDataIsNullOrError(type: BaseDataSource.DataSourceType) {
                callBack.onDataIsNullOrError(type)
            }

            override fun onApiError(ex: ApiException) {
                callBack.onApiError(ex)
            }

            override fun onDataLoaded(value: CommunityUserInfo, type: BaseDataSource.DataSourceType?) {
                when (loadType) {
                    LoadType.Refresh, LoadType.RefreshOnlyRemote -> {
                        deleteAndSave2Local(value)
                    }
                    LoadType.LoadMore -> {
                        updateCache(value)
                    }
                    else -> {}
                }
                callBack.onDataLoaded(value, BaseDataSource.DataSourceType.Remote)
            }
        })
    }

    /**
     * 加载本地和远程数据
     */
    private fun loadLocalAndRemoteData(callBack: ILoadDataCallBack<CommunityUserInfo>) {
        DBHelper.daoFinds(object : OnDaoFindsListener<CommunityUserInfo> {
            override fun call(): CommunityUserInfo? {
                return dataSourceCallBack.loadDataFromLocal()
            }

            override fun onError(e: Throwable?) {
                callBack.onDataIsNullOrError(BaseDataSource.DataSourceType.Local)
                loadRemoteData(callBack)
            }

            override fun onSuccess(communityUserInfo: CommunityUserInfo) {
                callBack.onDataLoaded(communityUserInfo, BaseDataSource.DataSourceType.Local)
                loadRemoteData(callBack)
            }
        })
    }

    /**
     * 删除本地单个数据并缓存最新数据进本地
     */
    private fun deleteAndSave2Local(value: CommunityUserInfo) {
        DBHelper.doUpdateOrDelete(object : OnDaoUpdateOrDeleteListener {
            override fun call(): Int {
                return dataSourceCallBack.deleteAll2Local()
            }

            override fun onSuccess(integer: Int) {
                CommonLogUtils.logD(TAG, "deleteAllAndSave2Local 删除旧数据成功")
                add2Local(value)
            }

            override fun onError(e: Throwable) {
                CommonLogUtils.logE(TAG, "deleteAllAndSave2Local 删除旧数据失败:" + e.message)
            }
        })
    }

    /**
     * 刷新缓存
     */
    private fun updateCache(value: CommunityUserInfo) {
        cachedDataMap[value.uuid] = value
    }

    /**
     * 缓存数据到本地,更新缓存
     */
    fun add2Local(value: CommunityUserInfo) {
        DBHelper.doInsert(object : OnDaoInsertListener {
            override fun call(): MutableList<Long> {
                return dataSourceCallBack.add2Local(value)
            }

            override fun onSuccess(longs: MutableList<Long>?) {
                //返回添加记录的id
                CommonLogUtils.logD(TAG, "add2Local新增数据库成功:$longs")
                //更新缓存
                updateCache(value)
            }

            override fun onError(e: Throwable) {
                CommonLogUtils.logE(TAG, "add2Local新增数据库失败:" + e.message)
            }
        })
    }

    /**
     * 根据mLoadType不同方式获取数据
     */
    fun loadData(uuid: String, callBack: ILoadDataCallBack<CommunityUserInfo>) {
        when (loadType) {
            //用内存缓存数据,没有就用远程数据
            LoadType.Cached -> {
                if (cachedDataMap.containsKey(uuid)) {
                    callBack.onDataLoaded(cachedDataMap[uuid], BaseDataSource.DataSourceType.Cached)
                } else {
                    loadType = LoadType.Refresh
                    loadLocalAndRemoteData(callBack)
                }
            }
            //刷新本地和远程数据
            LoadType.Refresh -> {
                loadLocalAndRemoteData(callBack)
            }
            //刷新新数据,加载更多数据 只用远程
            LoadType.RefreshOnlyRemote, LoadType.LoadMore -> {
                loadRemoteData(callBack)
            }
            //用内存缓存数据 并 刷新新数据只用远程
            LoadType.CachedAndRefreshOnlyRemote -> {
                if (cachedDataMap.containsKey(uuid)) {
                    loadType = LoadType.Cached
                    callBack.onDataLoaded(cachedDataMap[uuid], BaseDataSource.DataSourceType.Cached)
                    loadType = LoadType.RefreshOnlyRemote
                    loadRemoteData(callBack)
                } else {
                    loadType = LoadType.Refresh
                    loadLocalAndRemoteData(callBack)
                }
            }
        }
    }

    /**
     * 根据是关注,下载,点赞事件更新数据库缓存
     * @param updateItem
     * @param type
     */
    fun updateLocalByFocusLikeDown(updateItem: CommunitySceneInfo, type: String) {
        DBHelper.daoFinds(object : OnDaoFindsListener<CommunityUserInfo> {
            override fun call(): CommunityUserInfo? {
                return dataSourceCallBack.queryByUuid(updateItem.uuid)
            }

            override fun onSuccess(values: CommunityUserInfo?) {
                values?.let { communityUserInfo ->
                    when (type) {
                        "Focus" -> {
                            communityUserInfo.userSubscribe = updateItem.userSubscribe
                            //关注数要手动变更,因为CommunitySceneInfo没有关注数
                            communityUserInfo.userFanNum =
                                if (CommunityFocusRequest.isSubscribed(updateItem.userSubscribe)) {
                                    communityUserInfo.userFanNum + 1
                                } else {
                                    communityUserInfo.userFanNum - 1
                                }
                            communityUserInfo.sceneList.forEach {
                                it.userSubscribe = updateItem.userSubscribe
                            }
                        }
                        "Like" -> {
                            //点赞数要手动变更,因为CommunitySceneInfo没有点赞数
                            communityUserInfo.likeNum = if (updateItem.isLike!!) {
                                communityUserInfo.likeNum + 1
                            } else {
                                communityUserInfo.likeNum - 1
                            }
                            communityUserInfo.sceneList.forEach {
                                if (it.communityScenarioId == updateItem.communityScenarioId) {
                                    it.isLike = updateItem.isLike
                                    it.likes = updateItem.likes
                                }
                            }
                        }
                        "Down" -> {
                            communityUserInfo.sceneList.forEach {
                                if (it.communityScenarioId == updateItem.communityScenarioId) {
                                    it.downloads = updateItem.downloads
                                }
                            }
                        }
                    }
                    //数据库更新
                    DBHelper.doUpdateOrDelete(object : OnDaoUpdateOrDeleteListener {
                        override fun call(): Int? {
                            return dataSourceCallBack.update(communityUserInfo)
                        }

                        override fun onSuccess(integer: Int) {
                            CommonLogUtils.logD(
                                TAG, "$type 的数据库更新成功：$integer"
                            )
                        }

                        override fun onError(e: Throwable?) {
                            CommonLogUtils.logE(
                                TAG, "$type 的数据库更新失败：$e"
                            )
                        }
                    })
                    //内存更新
                    updateCache(communityUserInfo)
                }
            }

            override fun onError(e: Throwable?) {
                CommonLogUtils.logE(
                    TAG, "uuid=${updateItem.uuid},$type 的数据库更新失败：$e"
                )
            }
        })
    }

    /**
     * 清空本地和内存数据
     */
    fun deleteAllData() {
        //清除内存缓存
        cachedDataMap.clear()
        //清除数据库本地缓存
        DBHelper.doUpdateOrDelete(object : OnDaoUpdateOrDeleteListener {
            override fun call(): Int {
                return dataSourceCallBack.deleteAll()
            }

            override fun onSuccess(integer: Int) {
                CommonLogUtils.logI(
                    TAG, "用户退出登录清空数据库 成功"
                )
            }

            override fun onError(e: Throwable?) {
                CommonLogUtils.logE(
                    TAG, "用户退出登录清空数据库 失败:$e"
                )
            }

        })
    }
}