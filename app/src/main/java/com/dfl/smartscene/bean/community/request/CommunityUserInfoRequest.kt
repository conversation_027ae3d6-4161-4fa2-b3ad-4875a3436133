package com.dfl.smartscene.bean.community.request

import com.dfl.smartscene.bean.apibase.BaseRequestBean
import com.dfl.android.common.global.GlobalConfig

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2023/11/21
 * desc : 社区场景 用户信息 请求类
 * version: 1.0
 */
data class CommunityUserInfoRequest(
    /**
     * 是否官方 1-官方
     */
    val isOfficial: Int,
    /**
     * 分页页数
     */
    val pageNum: Int,
    /**
     * 访问用户id
     */
    val visitedUuid: String,
    /**
     * 分页大小
     */
    val pageSize: Int = GlobalConfig.REQ_PAGE_SIZE
) : BaseRequestBean()
