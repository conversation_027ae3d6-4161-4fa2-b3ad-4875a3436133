package com.dfl.smartscene.bean.community.request

import com.dfl.smartscene.bean.apibase.BaseRequestBean
import com.dfl.android.common.global.GlobalConfig

/**
 *Created by 钟文祥 on 2023/11/16.
 *Describer: 请求社区场景列表
 */
data class CommunityGetListRequest(
	var pageNum: Int,  //查询页码，1代表第1页
	var pageSize: Int = GlobalConfig.REQ_PAGE_SIZE  //每页条数
) : BaseRequestBean() {

	override fun toString(): String {
		return "uuid:" + uuid + " vin:" + vin + " port:" + port + " appCode:" + appCode
	}
}