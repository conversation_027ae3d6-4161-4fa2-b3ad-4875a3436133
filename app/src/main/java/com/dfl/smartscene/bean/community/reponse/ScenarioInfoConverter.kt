package com.dfl.smartscene.bean.community.reponse

import android.os.Parcelable
import com.dfl.android.common.util.NumberToCNUtil
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo
import kotlinx.parcelize.Parcelize

/**
 *Created by 钟文祥 on 2023/11/29.
 *Describer: ScenarioInfo 服务端与车机端对象的转换器
 */
@Parcelize
data class ScenarioInfoConverter(
    var auto_execute: Int?, //场景自动执行的配置，枚举类型： 0：开启自动执行 1：关闭自动执行
    var executeFrequency: Int?, //执行频率，0:unlimit(无限制，触发即执行场景) ，1:once_daily(每天只执行一次 00:00:00-23:59:59) 2:once_startup(点火循环周期内只执行一次)，
    var conditions: List<ConditionConverter>?, //场景执行条件列表，包含多个条件，每个条件的内 容为 Condition 结构
    var desc: String?, //场景说明，非必须（汉字、数字、英文大小写）
    var img_path: String?, //场景图片路径
    var name: String?, //场景名称（汉字、数字、英文大小写）
    var scenario_id: String?, //场景的唯一标识符
    var second_ask: Int?, //场景自动执行前二次询问的配置，枚举类型： 0：开启二次询问 1：关闭二次询问
    var sequences: List<SequenceConverter>?, //场景执行动作流程，内容为 Sequence 结构
    var timestamp: String?, //场景被创建，修改，删除时的时间戳
    var trigger_con: ConditionConverter?, //边缘条件，有且仅有一个，为 Condition 结构
    var version: Int?, //场景版本号

) : Parcelable {

    constructor(bean: ScenarioInfo) : this(
        auto_execute = bean.autoExeFlag,
        conditions = bean.conditions?.map { ConditionConverter(it) },
        desc = bean.scenarioDesc,
        img_path = bean.imgPath,
        name = bean.scenarioName,
        scenario_id = bean.scenarioId,
        second_ask = bean.secondAskeFlag,
        sequences = bean.sequence?.map { SequenceConverter(it) },
        executeFrequency = bean.executeFrequency,
        timestamp = bean.scenarioTimeStamp.toString(),
        trigger_con = bean.edgeCondition?.let { ConditionConverter(it) },
        version = bean.version
    )

    fun convert(): ScenarioInfo {
        // String scenarioId, String scenarioName,
        // String scenarioDesc, int version, int autoExeFlag,
        // int secondAskeFlag, long scenarioTimeStamp,
        // String imgPath, Condition edgeCondition,
        // List<Condition> conditions, List<Sequence> sequence

        val conditionsTemp = mutableListOf<ScenarioInfo.Condition?>()
        conditions?.forEachIndexed { index, condition ->
            conditionsTemp.add(condition.convert(index))
        }
        //
        //		var sequencesTemp = mutableListOf<ScenarioInfo.Sequence>()
        //		sequences?.forEachIndexed { index, sequence ->
        //			sequencesTemp.add(sequence.convert())
        //		}
        val sequencesTemp = sequences?.map { it.convert() }

        val info = ScenarioInfo(
            scenario_id,
            name,
            desc,
            version ?: 0,
            auto_execute ?: 1,
            second_ask ?: 1,
            NumberToCNUtil.isToLong(timestamp)?.toLong() ?: 0,
            img_path,
            executeFrequency ?: 0,
            trigger_con?.convert(0),
            conditionsTemp,
            sequencesTemp
        )
        return info
    }


}

@Parcelize
data class ConditionConverter(
    var args: List<InputArgInfoConverter>?, //能力输入参数列表
    var class_name: String?, //能力分类
    var desc: String?, //对条件的文字描述
    var skill_id: Int?, //条件中用到的能力 id
) : Parcelable {

    constructor(bean: ScenarioInfo.Condition?) : this(
        args = bean?.input?.map { InputArgInfoConverter(it) },
        class_name = bean?.category,
        desc = bean?.category + " " + bean?.desc,
        skill_id = bean?.skillId
    )

    fun convert(id: Int): ScenarioInfo.Condition? {
        if (skill_id == null) return null
        var category = ""
        var des = ""
        desc?.let { desc ->
            //判断是否是oem场景条件格式，主驾车门 打开
            if (desc.contains(" ")) {
                category = desc.split(" ")[0]
                des = desc.indexOf(" ").let { desc.substring(it + 1) }
            } else {
                //不是oem格式则正常使用class_name和desc
                category = class_name ?: ""
                des = desc
            }
        }

        return ScenarioInfo.Condition(id,
            des,
            skill_id ?: -1,
            category,
            if (args != null) args?.map { it.convert() } else arrayListOf())
    }
}

@Parcelize
data class SequenceConverter(
    //场景执行动作流程，内容为 Sequence 结构
    var action: ActionConverter, //TimeWait 见 1.3.4 小节 action Action 见 1.3.5 小节
    var id: Int, //表明执行的次序，从 0 开始
    var time: Int, //保留字段，但不再使用
    var type: Int, //场景执行的分类： 0 : Timewait （是一个延时） 1 : Action（是一个动作触发）默认为 1

    //        private int id;
    //        private int type;
    //        private int delay;
    //        private Action action;

) : Parcelable {

    constructor(bean: ScenarioInfo.Sequence) : this(
        action = ActionConverter(bean.action), id = bean.id, time = bean.time, type = bean.type
    )

    //int id, int type, int delay
    fun convert(): ScenarioInfo.Sequence {
        val info = ScenarioInfo.Sequence(id, type, action.convert())
        return info
    }
}

@Parcelize
data class ActionConverter(
    var args: List<InputArgInfoConverter>?, //能力输入参数列表
    var class_name: String, //能力分类
    var desc: String, //对动作的文字描述
    var skill_id: Int, //动作中用到的能力 id
) : Parcelable {

    constructor(bean: ScenarioInfo.Action) : this(
        args = bean.input?.map { InputArgInfoConverter(it) },
        class_name = bean.category,
        desc = bean.desc,
        skill_id = bean.skillId
    )

    //String desc, int skillId, String category, List<InputArgInfo> input
    fun convert(): ScenarioInfo.Action {
        val input = if (args != null) {
            args?.map { it.convert() }
        } else {
            ArrayList()
        }
        val info = ScenarioInfo.Action(desc, skill_id, class_name, input)
        return info
    }
}

@Parcelize
data class InputArgInfoConverter(
    var name: String?, var type: Int?, var value: String?, var desc: String?
) : Parcelable {
    constructor(bean: InputArgInfo) : this(
        name = bean.name, type = bean.type.value, value = bean.value, desc = bean.desc
    )

    fun convert(): InputArgInfo {
        val typeConvert: ArgType
        if (type == null) {
            if (value?.toDoubleOrNull() == null) { //字符串
                typeConvert = ArgType.STRING
            } else {
                typeConvert = ArgType.DOUBLE
            }
        } else {
            typeConvert = ArgType.values()[type!!]
        }
        val info = InputArgInfo(name, typeConvert, value, desc ?: "")
        return info
    }
}
