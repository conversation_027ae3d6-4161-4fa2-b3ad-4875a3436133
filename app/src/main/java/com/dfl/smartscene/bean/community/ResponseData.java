package com.dfl.smartscene.bean.community;

import android.os.Parcel;
import android.os.Parcelable;

import com.dfl.smartscene.bean.communication.ItemBean;
import com.dfl.smartscene.bean.communication.KtvRecordFileInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/08/27
 */

public class ResponseData implements Parcelable {

    public static final Creator<ResponseData> CREATOR = new Creator<ResponseData>() {
        @Override
        public ResponseData createFromParcel(Parcel in) {
            return new ResponseData(in);
        }

        @Override
        public ResponseData[] newArray(int size) {
            return new ResponseData[size];
        }
    };
    /**
     * 数据源类型
     */
    private int sourceType;
    /**
     * 数据类型
     */
    private int useType;
    /**
     * 数据主体
     */
    private List<ItemBean> data;
    private List<KtvRecordFileInfo> LocalData;
    /**
     * 请求是否成功
     */
    private boolean isSuccess;
    private int total;
    private int limit;
    private int offset;
    private int errorCode;
    private String msg;
    /**
     * 请求接口类型
     */
    private int requsetType;
    private List<ResponseData> subData;
    private String keyWord;
    private boolean hasSubData = false;

    public ResponseData() {
    }

    protected ResponseData(Parcel in) {
        sourceType = in.readInt();
        useType = in.readInt();
        data = in.createTypedArrayList(ItemBean.CREATOR);
        LocalData = in.createTypedArrayList(KtvRecordFileInfo.CREATOR);
        isSuccess = in.readByte() != 0;
        total = in.readInt();
        limit = in.readInt();
        offset = in.readInt();
        errorCode = in.readInt();
        msg = in.readString();
        requsetType = in.readInt();
        subData = in.createTypedArrayList(ResponseData.CREATOR);
        keyWord = in.readString();
        hasSubData = in.readByte() != 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(sourceType);
        dest.writeInt(useType);
        dest.writeTypedList(data);
        dest.writeTypedList(LocalData);
        dest.writeByte((byte) (isSuccess ? 1 : 0));
        dest.writeInt(total);
        dest.writeInt(limit);
        dest.writeInt(offset);
        dest.writeInt(errorCode);
        dest.writeString(msg);
        dest.writeInt(requsetType);
        dest.writeTypedList(subData);
        dest.writeString(keyWord);
        dest.writeByte((byte) (hasSubData ? 1 : 0));
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public List<KtvRecordFileInfo> getLocalData() {
        return LocalData;
    }

    public void setLocalData(List<KtvRecordFileInfo> localData) {
        LocalData = localData;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    public List<ResponseData> getSubData() {
        return subData;
    }

    public void setSubData(List<ResponseData> subData) {
        this.subData = subData;
    }

    public boolean isHasSubData() {
        return hasSubData;
    }

    public void setHasSubData(boolean hasSubData) {
        this.hasSubData = hasSubData;
    }

    public int getRequsetType() {
        return requsetType;
    }

    public void setRequsetType(int requsetType) {
        this.requsetType = requsetType;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getSourceType() {
        return sourceType;
    }

    public void setSourceType(int sourceType) {
        this.sourceType = sourceType;
    }

    public int getUseType() {
        return useType;
    }

    public void setUseType(int useType) {
        this.useType = useType;
    }

    public List<ItemBean> getData() {
        return data;
    }

    public void setData(List<ItemBean> data) {
        this.data = data;
    }

    public boolean isSuccess() {
        return isSuccess;
    }

    public void setSuccess(boolean success) {
        isSuccess = success;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }


    @Override
    public String toString() {
        return "ResponseData{" +
                "sourceType=" + sourceType +
                ", useType=" + useType +
                ", data=" + data +
                ", isSuccess=" + isSuccess +
                ", total=" + total +
                ", limit=" + limit +
                ", offset=" + offset +
                ", errorCode=" + errorCode +
                ", msg='" + msg + '\'' +
                ", requsetType=" + requsetType +
                ", subData=" + subData +
                ", keyWord='" + keyWord + '\'' +
                ", hasSubData=" + hasSubData +
                '}';
    }
}
