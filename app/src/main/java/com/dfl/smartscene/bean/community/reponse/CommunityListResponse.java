package com.dfl.smartscene.bean.community.reponse;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;

import java.lang.reflect.Type;
import java.util.List;

import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;
import androidx.room.TypeConverter;
import androidx.room.TypeConverters;

/**
 * Created by 钟文祥 on 2023/11/18.
 * Describer:社区场景列表响应类
 */
@Entity(tableName = "CommunityListResponse")
public class CommunityListResponse {

    @PrimaryKey(autoGenerate = true)
    private int autoId_CommunityListResponse;
    private int tabId; //tabType的index

    //以下为原始参数
    private int pageIndex;
    private int pageSize;
    private int pages;  //有多少页 pages>pageIndex 表示有下一页
    private int records;//总记录数

    @TypeConverters(ConvertersCommunitySceneInfo.class)
    private List<CommunitySceneInfo> rows;


    public static class ConvertersCommunitySceneInfo {
        @TypeConverter
        public String objectToString(List<CommunitySceneInfo> list) {
            return new Gson().toJson(list);
        }

        @TypeConverter
        public List<CommunitySceneInfo> stringToObject(String json) {
            Type listType = new TypeToken<List<CommunitySceneInfo>>() {
            }.getType();
            return new Gson().fromJson(json, listType);
        }
    }

    @Ignore
    public CommunityListResponse() {
    }

    public CommunityListResponse(int tabId) {
        this.tabId = tabId;
    }

    @Ignore
    public CommunityListResponse(int autoId_CommunityListResponse, int tabId, int pageIndex,
                                 int pages, int pageSize,
                                 int records, List<CommunitySceneInfo> rows) {
        this.autoId_CommunityListResponse = autoId_CommunityListResponse;
        this.tabId = tabId;
        this.pageIndex = pageIndex;
        this.pages = pages;
        this.pageSize = pageSize;
        this.records = records;
        this.rows = rows;
    }

    public int getAutoId_CommunityListResponse() {
        return autoId_CommunityListResponse;
    }

    public void setAutoId_CommunityListResponse(int autoId_CommunityListResponse) {
        this.autoId_CommunityListResponse = autoId_CommunityListResponse;
    }

    public int getTabId() {
        return tabId;
    }

    public void setTabId(int tabId) {
        this.tabId = tabId;
    }

    public int getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(int pageIndex) {
        this.pageIndex = pageIndex;
    }

    public int getPages() {
        return pages;
    }

    public void setPages(int pages) {
        this.pages = pages;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getRecords() {
        return records;
    }

    public void setRecords(int records) {
        this.records = records;
    }

    public List<CommunitySceneInfo> getRows() {
        return rows;
    }

    public void setRows(List<CommunitySceneInfo> rows) {
        this.rows = rows;
    }

    public boolean hasNextPage() {
        return pages > pageIndex;
    }
}
