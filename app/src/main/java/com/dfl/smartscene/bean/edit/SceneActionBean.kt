package com.dfl.smartscene.bean.edit

import android.os.Parcelable
import com.chad.library.adapter.base.entity.MultiItemEntity
import com.iauto.scenarioadapter.ScenarioInfo
import kotlinx.parcelize.Parcelize

@Parcelize
class SceneActionBean(
	val action: ScenarioInfo.Sequence?,
	val itemBg: Int,
	private val actionType: Int,
	val isActive: Boolean = true //卡片动作能力是否可用
) : Parcelable, MultiItemEntity {
	companion object {
		const val ADD_ACTION_ITEM = 0
		const val ADD_ACTION_CONTENT_ITEM = 1
	}

	override val itemType: Int
		get() = actionType
}