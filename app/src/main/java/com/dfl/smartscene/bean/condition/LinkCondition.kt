package com.dfl.smartscene.bean.condition



/**
 * author:z<PERSON><PERSON><PERSON>
 * e-mail:z<PERSON><PERSON><PERSON>@dfl.com.cn
 * time: 2022/09/20
 * desc:连接的条件实体类
 * version:1.0
 */
data class LinkCondition(
    /**
     * 条件名称
     */
    val content:String,
    /**
     * 图标ID
     */
    val iconId:Int,
    /**
     * 条件类型
     */
    val type: LinkConditionType
)

/**
 * 连接条件的类型
 */
enum class LinkConditionType{
    /**
     * 蓝牙
     */
    BLUETOOTH,

    /**
     * 热点
     */
    HOTSPOT,

    /**
     * WIFI
     */
    WLAN
}
