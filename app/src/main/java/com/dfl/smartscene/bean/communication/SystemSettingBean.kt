package com.dfl.smartscene.bean.communication

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2023/02/03
 * desc :系统设置隐私协议实体类
 * version: 1.0
 */
data class SystemSettingBean<T>(
    val messageType: String,
    val protocolId: String,
    val requestAuthor: String,
    val requestCode: String,
    val versionName: String,
    val data: T?,
    val statusCode: String? = null
)

data class AppPackageNameBean(
    val appId: String
)

data class SetAppPermission(
    val appId: String,
    val permissions: List<Permission>?
)

data class Permission(
    val status: Int,
    val type: Int
)

data class SetAgreeStatus(
    val status: Int
)

data class SetTheme(
    val themeType: Int
)
