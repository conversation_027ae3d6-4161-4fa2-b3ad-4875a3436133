package com.dfl.smartscene.bean.action

import com.chad.library.adapter.base.entity.MultiItemEntity

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/15
 * version: 1.0
 * 动作tab栏右边adapter的 item类
 * @param T 具体动作类型
 * @property content 名称
 * @property iconId 图标
 * @property type 用于动作adapter 判断title 还是 内容
 * @property actionType 内容的标识类型
 * @property actionSkillId 动作能力id
 * @property actionItemType 动作标题的标志类型,用于分类动作子项
 * @property isEnable 是否可用,用于智能场景
 */
open class ActionSkillItemBean<T>(
    val content: String,
    val iconId: Int,
    val type: Int,
    val actionType: T?,
    val actionSkillId: ArrayList<Int>?,
    val actionItemType: T? = null,
    val isEnable: Boolean? = true
) : MultiItemEntity {
    companion object {
        /**
         * tab栏右边子项标题
         */
        const val SKILL_TITLE = 0

        /**
         * tab栏右边子项
         */
        const val SKILL_CONTENT = 1

        fun <T> initTitle(content: String, actionItemType: T? = null): ActionSkillItemBean<T> {
            return ActionSkillItemBean(
                content, 0, SKILL_TITLE, null, null, actionItemType
            )
        }
    }

    /**
     * 实现adapter多布局接口,通过type区分
     */
    override val itemType: Int
        get() = type


}