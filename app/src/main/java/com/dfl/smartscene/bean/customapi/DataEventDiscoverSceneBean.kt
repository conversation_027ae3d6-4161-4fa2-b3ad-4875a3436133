package com.dfl.smartscene.bean.customapi

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2023/03/03
 * desc :
 * version: 1.0
 */
data class DataEventDiscoverSceneBean(
    val scene_detail:List<DataEventSceneDetailBean>
)
data class DataEventSceneDetailBean(
    val scenario_name:String,
    val scenario_id:String,
    val scenario_sequence:String,//场景下标
    val scenario_type:Boolean,//手动执行/自动执行
    val trigger_condition_detail:String,
    val status_condition_detail:String,
    val action_detail:String,
    val inquiry_switch:String//取开关情况：开/关/无
)