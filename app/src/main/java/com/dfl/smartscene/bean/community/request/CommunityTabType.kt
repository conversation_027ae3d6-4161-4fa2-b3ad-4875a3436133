package com.dfl.smartscene.bean.community.request

import com.dfl.android.common.util.StringUtils
import com.dfl.smartscene.R

/**
 *Created by 钟文祥 on 2023/11/17.
 *Describer: 社区场景中的tab类型
 */
enum class CommunityTabType(var value: Int) {
	Official(0),  //官方
	LastNew(1),   //最新
	Hot(2),       //热门
	Special(3),   //爱车专属
	Banner(4);    //轮播图

	private var mTitleArr = arrayOf<Int>(
		R.string.scene_text_community_official,
		R.string.scene_text_community_new,
		R.string.scene_text_community_hot,
		R.string.scene_text_community_love_car,
		R.string.scene_text_community_banner
	)

	fun getName(): String? {
		return StringUtils.getString(mTitleArr[value])
	}

}