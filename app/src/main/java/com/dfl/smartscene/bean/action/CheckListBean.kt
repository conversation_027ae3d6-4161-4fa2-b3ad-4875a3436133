package com.dfl.smartscene.bean.action

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/09
 * desc : 通用-复选列表实体类
 * version: 1.0
 */
data class CheckListBean(
    var content: String, //名称
    var isCheck: Boolean, //是否被选择了
    var isItemShowMoreIcon: Boolean = false, //是否显示更多的小图标
    /**注意这个图标使用于drawableStart、drawableEnd如果弹窗没有这个图标的话，请使用-1代替不然会默认显示*/
    @Deprecated("ui2.0后三级对话框不带icon,用不上的属性") val reflexIconId: Int = -1,
    @Deprecated("ui2.0后三级对话框不带icon,用不上的属性") val selectedImgId: Int = -1,
    var desc: String = "",//小描述
    var isSingleSelectIcon: Boolean = true //是否显示单选按钮图标
)
