package com.dfl.smartscene.bean.main

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.dfl.smartscene.room.SceneRecordTable

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/06/08
 * desc   :
 * version: 1.0
 */
@Entity(tableName = SceneRecordTable.TABLE_NAME)
data class SceneRecordEntity(
    @PrimaryKey
    @ColumnInfo(name = SceneRecordTable.FIELD_ID)
    var srId: Long?,             // 条目ID
    @ColumnInfo(name = SceneRecordTable.FIELD_SCENARIO_ID)
    var scenarioGroupId: String,  // 场景组ID
    @ColumnInfo(name = SceneRecordTable.FIELD_SCENE_NAME)
    var sceneName: String,      // 场景名称
    @ColumnInfo(name = SceneRecordTable.FIELD_FINISH_SCENE_TIME)
    var finishSceneTime: Long,   // 时间
    @ColumnInfo(name = SceneRecordTable.FIELD_START_SCENE_RESULT)
    var startSceneResult: Int,  // 执行结果
)
