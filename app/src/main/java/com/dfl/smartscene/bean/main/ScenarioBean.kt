package com.dfl.smartscene.bean.main

import android.os.Parcel
import android.os.Parcelable
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/19
 * desc   :
 * version: 1.0
 */
data class ScenarioBean(
    var scenarioInfo: ScenarioInfo?, // 场景组列表
    var isAskBeforeAutoRun: Boolean?, // 是否开始自动执行前询问，界面
    var isTemplateScene: Boolean?, // 该场景是否来自发现（模板场景）
    var isAutoRun: Boolean?, // 自动运行，界面
    var executeFrequency: Int? //执行频率
) : Parcelable {

    constructor(parcel: Parcel) : this(
        parcel.readParcelable(ScenarioInfo::class.java.classLoader),
        parcel.readValue(Boolean::class.java.classLoader) as? Boolean,
        parcel.readValue(Boolean::class.java.classLoader) as? Boolean,
        parcel.readValue(Boolean::class.java.classLoader) as? Boolean,
        parcel.readValue(Int::class.java.classLoader) as? Int
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeParcelable(scenarioInfo, flags)
        parcel.writeValue(isAskBeforeAutoRun)
        parcel.writeValue(isTemplateScene)
        parcel.writeValue(isAutoRun)
        parcel.writeValue(executeFrequency)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<ScenarioBean> {
        override fun createFromParcel(parcel: Parcel): ScenarioBean {
            return ScenarioBean(parcel)
        }

        override fun newArray(size: Int): Array<ScenarioBean?> {
            return arrayOfNulls(size)
        }
    }
}