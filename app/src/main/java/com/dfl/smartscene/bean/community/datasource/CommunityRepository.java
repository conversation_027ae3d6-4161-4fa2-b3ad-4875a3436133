package com.dfl.smartscene.bean.community.datasource;

import com.dfl.smartscene.bean.community.reponse.CommunityListResponse;
import com.dfl.smartscene.bean.community.reponse.CommunitySceneInfo;
import com.dfl.smartscene.bean.community.request.CommunityTabType;
import com.dfl.smartscene.bean.mechanism.BaseRepository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by 钟文祥 on 2023/11/18.
 * Describer:社区场景 每个tab的 存储库
 */
public class CommunityRepository<T> extends BaseRepository<T> {

    public CommunityRepository() {
        super();
    }

    private static volatile CommunityRepository<CommunityListResponse> INSTANCE;

    public static CommunityRepository<CommunityListResponse> getInstance() {
        if (INSTANCE == null) {
            synchronized (CommunityRepository.class) {
                if (INSTANCE == null) {
                    INSTANCE = new CommunityRepository<>();
                }
            }
        }
        return INSTANCE;
    }

    @Override
    public boolean overwriteAddCache(CommunityTabType tabType, T values) {
        if (values instanceof CommunityListResponse) {
            if (!mCachedDataMap.containsKey(tabType)) {
                mCachedDataMap.put(tabType, values);
            } else {
                List<CommunitySceneInfo> last =
                        ((CommunityListResponse) mCachedDataMap.get(tabType)).getRows();
                CommunityListResponse value = (CommunityListResponse) values;
                CommunityListResponse temp = new CommunityListResponse(
                        -1, value.getTabId(),
                        value.getPageIndex(),
                        value.getPages(), value.getPageSize(),
                        value.getRecords(),
                        new ArrayList<>()
                );
                temp.getRows().addAll(last);
                temp.getRows().addAll(value.getRows());
                mCachedDataMap.put(tabType, (T) temp);
            }
        }


        return true;
    }
}
