package com.dfl.smartscene.community.usercenter

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.View
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.dfl.android.common.base.BaseMenuPopup
import com.dfl.android.common.base.BaseVBActivity
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.util.CommonToastUtils
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.apiweb.utils.ApiReqResState
import com.dfl.smartscene.bean.community.reponse.CommunitySceneInfo
import com.dfl.smartscene.bean.community.request.CommunityFocusRequest
import com.dfl.smartscene.bean.community.request.CommunityReqType
import com.dfl.smartscene.bean.main.MySceneBean
import com.dfl.smartscene.communication.UserCenterManager
import com.dfl.smartscene.community.usercenter.adpter.UserNumberInfoAdapter
import com.dfl.smartscene.community.usercenter.adpter.UserUploadSceneAdapter
import com.dfl.smartscene.customapi.SoundManager
import com.dfl.smartscene.databinding.SceneActivityUserCenterBinding
import com.dfl.smartscene.rv.EquallySpaceDecoration
import com.dfl.smartscene.ui.main.discover.detail.SceneDetailActivity
import com.dfl.smartscene.ui.overlay.common.DefaultDialogFragment
import com.dfl.smartscene.util.NetWorkUtils
import com.dfl.smartscene.util.UIUtils
import com.jeremyliao.liveeventbus.LiveEventBus
import com.scwang.smartrefresh.layout.api.RefreshLayout
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import me.jessyan.autosize.AutoSizeCompat
import me.jessyan.autosize.utils.AutoSizeUtils

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2023/11/01
 * desc : 账号个人中心界面
 * version: 1.2
 */
class UserCenterActivity : BaseVBActivity<UserCenterViewModel, SceneActivityUserCenterBinding>(), View.OnClickListener {
    /**
     *用户上传场景rv的adapter
     */
    private val mUserUploadSceneAdapter: UserUploadSceneAdapter by lazy {
        UserUploadSceneAdapter { position, valueObject ->
            val item: CommunitySceneInfo = mUserUploadSceneAdapter.data[position]
            if (valueObject == CommunityReqType.Likes) { //点赞
                mViewModel.likeOrNoLike(item, supportFragmentManager);
            } else if (valueObject == CommunityReqType.Down) { //下载
                mViewModel.downloadOrNo(item, supportFragmentManager, this, true);
            }
        }
    }

    /**
     *用户信息rv的adapter
     */
    private val mUserNumberInfoAdapter by lazy { UserNumberInfoAdapter() }

    /**上传场景更多菜单*/
    private var mPopMenu: UserCenterSceneMenuPopup =
        UserCenterSceneMenuPopup(object : BaseMenuPopup.OnMenuClickListener<CommunitySceneInfo> {
            override fun onMenuItemSelected(
                menuPopup: BaseMenuPopup<CommunitySceneInfo>, menuIndex: Int, dataIndex: Int, data: CommunitySceneInfo?
            ) {
                DefaultDialogFragment.showDialog(
                    supportFragmentManager,
                    "",
                    "",
                    getString(R.string.scene_text_community_usercenter_delete_scene_content),
                    getString(R.string.scene_button_common_delete),
                    getString(R.string.scene_text_common_cancel),
                    object : DefaultDialogFragment.OnDialogButtonClickListener {
                        override fun onRightButtonClick(dialog: DefaultDialogFragment) {
                            dialog.dismissWithAnimate()
                        }

                        override fun onLeftButtonClick(dialog: DefaultDialogFragment) {
                            NetWorkUtils.haveNetwork({
                                CommonLogUtils.logI(TAG, "menu,$dataIndex")
                                mViewModel.deleteUploadScene(data?.communityScenarioId, dataIndex)
                                mUserUploadSceneAdapter.removeAt(dataIndex)
                                SoundManager.playSoundEffect(SoundManager.SoundType.DELETE)
                                dialog.dismissWithAnimate()
                            })
                        }

                    },
                    isSoundToast = true
                )
                menuPopup.dismiss()
            }
        })


    companion object {
        /**
         *bundle中isOfficial对应的key
         */
        const val EXTRA_IS_OFFICIAL = "extra_is_official"

        /**
         *bundle中visitedUserId对应的key
         */
        const val EXTRA_VISITED_USER_ID = "extra_visited_user_id"

        /**
         *跳转到用户界面bundle对应的key
         */
        const val EXTRA_BUNDLE = "extra_bundle"

        /**
         * 日志tag
         */
        private const val TAG = GlobalConstant.GLOBAL_TAG.plus("UserCenterActivity")

        /**
         *从别处跳转到该用户页面
         * @param context
         * @param isOfficial 是否官方 1-官方
         * @param visitedUserId 访问用户id
         */
        fun actionStart(context: Context, isOfficial: Int?, visitedUserId: String?) {
            val intent = Intent(context, UserCenterActivity::class.java)
            val bundle = Bundle()
            bundle.putInt(EXTRA_IS_OFFICIAL, isOfficial ?: 0)
            bundle.putString(EXTRA_VISITED_USER_ID, visitedUserId)
            intent.putExtra(EXTRA_BUNDLE, bundle)
            context.startActivity(intent)
        }

    }

    override fun initView(savedInstanceState: Bundle?) {
        //请求用户信息
        val bundle = intent.getBundleExtra(EXTRA_BUNDLE)
        bundle?.apply {
            val isOfficial = getInt(EXTRA_IS_OFFICIAL)
            val visitedUserId = getString(EXTRA_VISITED_USER_ID) ?: ""
            val isCurrentUser = visitedUserId == UserCenterManager.userLoginStatus?.uuid
            //如果是官方账号使用官方背景
            if (isOfficial == 1) {
                mViewBind.root.setBackgroundResource(R.drawable.scene_img_community_user_center_bg_official)
            }
            //tab标签
            mViewBind.tvBackDesc.text = if (isCurrentUser) {
                getString(R.string.scene_text_user_center_my_account_desc)
            } else {
                getString(R.string.scene_text_user_center_other_account_desc)
            }
            mViewModel.initData(isOfficial, visitedUserId, isCurrentUser)

        }
        initRefreshView()
        initRecyclerView()
        UIUtils.setReqResLayoutTransparent(mViewBind)
    }


    override fun createObserver() {
        collectDeleteApiResState()
        collectUserCenterUiState()
        collectFocusApiResState()
        initLiveEventBus()
        initListener()
    }

    /**
     * 用户信息监听
     */
    private fun collectUserCenterUiState() {
        launchAndRepeatWithViewLifecycle {
            mViewModel.userCenterUiState.map { it.userInfoState }.distinctUntilChanged().collect {
                CommonLogUtils.logI(TAG, "userApiResState:${it.userApiResState.state}")
                //显示加载页隐藏账号信息，因为加载页透明
                mViewBind.slUpdate.isVisible = !mViewModel.isShowLoading
                if (mViewModel.isShowLoading) {
                    UIUtils.showLoadingWithText(
                        mViewBind, getString(R.string.scene_text_web_content_is_coming_soon)
                    )
                }
                when (it.userApiResState.state) {
                    ApiReqResState.ReqResState.GetValueStart -> {
                        //重置没有更多状态
                        mViewBind.slUpdate.setNoMoreData(false)
                    }

                    ApiReqResState.ReqResState.GetValueFinish -> {
                        renderUserInfo(it)
                        //如果显示加载页，取消显示
                        if (mViewModel.isShowLoading) {
                            UIUtils.disShow(mViewBind)
                            //设置为false防止下拉刷新再次显示加载页
                            mViewModel.isShowLoading = false
                        }
                        //更新数据
                        mUserUploadSceneAdapter.setList(it.userUploadSceneList)
                        //结束上拉加载下拉刷新
                        mViewBind.slUpdate.isVisible = true
                        mViewBind.slUpdate.finishRefresh()
                        mViewBind.slUpdate.finishLoadMore(
                            1000, true, mUserUploadSceneAdapter.itemCount == it.userUploadNum
                        )
                    }

                    ApiReqResState.ReqResState.Error -> {
                        //如果是加载页面显示网络异常画面
                        if (mViewModel.isShowLoading) {
                            UIUtils.showError(mViewBind, it.userApiResState.ex) {
                                mViewModel.refreshLoadData()
                            }
                        } else {
                            CommonToastUtils.show(it.userApiResState.ex.getToastMsg())
                        }
                        //结束上拉加载下拉刷新
                        mViewBind.slUpdate.finishRefresh(false)
                        mViewBind.slUpdate.finishLoadMore(false)
                    }

                    null -> {
                        renderUserInfo(it)
                    }
                }
            }
        }
    }

    /**
     * 关注状态监听
     */
    private fun collectFocusApiResState() {
        launchAndRepeatWithViewLifecycle {
            mViewModel.userCenterUiState.map { it.focusApiResState }.distinctUntilChanged().collect {
                CommonLogUtils.logI(TAG, "focusApiResState:${it.state}")
                //关注成功界面变更在liveEventBus里面，为了捕捉到其他页面事件
                when (it.state) {
                    ApiReqResState.ReqResState.GetValueStart, null, ApiReqResState.ReqResState.GetValueFinish -> {}
                    ApiReqResState.ReqResState.Error -> {
                        CommonToastUtils.show(
                            getString(
                                R.string.scene_toast_web_exception_unconnect_or_timeout
                            )
                        )
                    }
                }
            }
        }
    }

    /**
     * 删除事件网络请求状态监听
     */
    private fun collectDeleteApiResState() {
        launchAndRepeatWithViewLifecycle {
            mViewModel.userCenterUiState.map { it.deleteApiResState }.distinctUntilChanged().collect {
                CommonLogUtils.logI(TAG, "deleteApiResState:${it.state}")
                when (it.state) {
                    ApiReqResState.ReqResState.GetValueStart, null -> {}
                    ApiReqResState.ReqResState.GetValueFinish -> {
                        //后台删除成功，补位新的数据
                        if (mUserUploadSceneAdapter.itemCount != mViewModel.userCenterUiState.value.userInfoState.userUploadNum) {
                            mViewModel.refreshCurPageData()
                        }
                    }

                    ApiReqResState.ReqResState.Error -> {
                        CommonToastUtils.show(
                            CommonUtils.getString(
                                R.string.scene_toast_web_exception_unconnect_or_timeout
                            )
                        )
                    }
                }
            }
        }
    }

    private fun initLiveEventBus() {
        //监听个人中心用户退登事件
        LiveEventBus.get<Boolean>(GlobalLiveEventConstants.KEY_USER_CENTER_USER_IS_LOGIN_OUT_STATUS)
            .observe(this) { isLogout ->
                if (isLogout && mViewModel.userCenterUiState.value.userInfoState.isCurrentUser) {
                    CommonLogUtils.logI(TAG, "用户退出登录，退出当前账号页")
                    finish()
                }
            }
        //监听场景点赞成功
        LiveEventBus.get<CommunitySceneInfo>(GlobalLiveEventConstants.KEY_COMMUNITY_LIKE_SUCCESS_UPDATE_UI)
            .observe(this) {
                //更新数据库
                mViewModel.updateLocalByFocusLikeDown(it, "Like")

                mUserUploadSceneAdapter.notifyByLike(it)
                mUserNumberInfoAdapter.notifyByLike(it.isLike!!)

            }
        //监听场景下载成功
        LiveEventBus.get<CommunitySceneInfo>(GlobalLiveEventConstants.KEY_COMMUNITY_DOWNLOAD_SUCCESS_UPDATE_UI)
            .observe(this) {
                mUserUploadSceneAdapter.notifyByDown(it)
                mViewModel.updateLocalByFocusLikeDown(it, "Down")
            }
        //监听场景关注状态变化,必须通过LiveEventBus，因为要监听别的页面事件
        LiveEventBus.get<CommunitySceneInfo>(GlobalLiveEventConstants.KEY_COMMUNITY_FOCUS_SUCCESS_UPDATE_UI)
            .observe(this) {
                val isSubscribed = CommunityFocusRequest.isSubscribed(it.userSubscribe)
                //已经更新关注/未关注ui，不再变更
                if (mViewBind.tvCommunitySubscribed.isVisible == isSubscribed) {
                    CommonLogUtils.logI(TAG, "isSubscribed:$isSubscribed,已经更新关注/未关注ui")
                    return@observe
                }
                mUserNumberInfoAdapter.notifyByFocus(isSubscribed)
                mUserUploadSceneAdapter.notifyByFocus(it)
                mViewModel.updateLocalByFocusLikeDown(it, "Focus")
                updateBtnUserSubscribe(it.userSubscribe)
            }
        //场景添加成功的事件
        LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_ADD_SUCCESS, MySceneBean::class.java).observe(this) {
            //添加成功，退出界面
            if (it != null) {
                Handler(Looper.getMainLooper()).postDelayed({
                    if (!isFinishing && !isDestroyed) {
                        CommonLogUtils.logD(TAG, "下载页添加场景成功，退出当前界面")
                        finish()
                    }
                }, 100)
            }
        }
    }

    /**
     * 加载除上传列表的用户中心
     */
    private fun renderUserInfo(uiState: UserInfoState) {
        //用户头像
        Glide.with(this@UserCenterActivity).load(uiState.communityUserInfo?.userAvatar)
            .placeholder(R.drawable.scene_icon_community_user_head_default).apply(RequestOptions().circleCrop())
            .into(mViewBind.ivUserAvatar)
        //用户名
        var userName = uiState.communityUserInfo?.userName
        if (TextUtils.isEmpty(userName) && uiState.isCurrentUser) {
            userName = UserCenterManager.userLoginStatus?.userNickName
        }
        mViewBind.tvUserName.text = userName
        //上传场景数量
        mViewBind.tvUserUploadsNum.text = uiState.userUploadNumDesc
        //账号信息
        mUserNumberInfoAdapter.setList(uiState.userNumberInfoList)
        //上传场景是否为空
        mViewBind.groupEmptyUploadShow.isVisible = uiState.isEmptyUploadList
        mViewBind.rvUserUploadsList.isVisible = !uiState.isEmptyUploadList
        mViewBind.slUpdate.setEnableLoadMore(!uiState.isEmptyUploadList)
        //关注按钮
        updateBtnUserSubscribe(uiState.communityUserInfo?.userSubscribe)
    }

    private fun initListener() {
        mViewBind.ivBackIcon.setOnClickListener(this)
        mViewBind.tvBackDesc.setOnClickListener(this)
        mViewBind.tvCommunitySubscribe.setOnClickListener(this)
        mViewBind.tvCommunitySubscribed.setOnClickListener(this)
        mViewBind.tvNoLongerSub.setOnClickListener(this)
        mViewBind.tvNoLongerSubCancel.setOnClickListener(this)
    }

    private fun initRefreshView() {
        UIUtils.initRefreshLayout(mViewBind)
        mViewBind.slUpdate.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
            override fun onRefresh(refreshLayout: RefreshLayout) {
                mViewModel.refreshLoadData()
                //不显示不再关注菜单
                mViewBind.viewPop.visibility = View.GONE
            }

            override fun onLoadMore(refreshLayout: RefreshLayout) {
                mViewModel.loadMoreData()
            }
        })
    }

    private fun initRecyclerView() {
        //初始化上传列表
        UIUtils.initRecyclerView(this, mViewBind.rvUserUploadsList, GridLayoutManager(this, 3), false)
        mViewBind.rvUserUploadsList.adapter = mUserUploadSceneAdapter
        mViewBind.rvUserUploadsList.addItemDecoration(
            EquallySpaceDecoration(
                AutoSizeUtils.dp2px(this, 52f), AutoSizeUtils.dp2px(this, 52f), true
            )
        )
        mUserUploadSceneAdapter.setOnItemClickListener { adapter, _, position ->
            if (mUserUploadSceneAdapter.data.get(position).isShowLottieAnimation) return@setOnItemClickListener
            SceneDetailActivity.fromUserCenterStartCommunitySceneDetailActivity(
                this, adapter.getItem(position) as CommunitySceneInfo
            )
        }
        mUserUploadSceneAdapter.addChildClickViewIds(R.id.btn_more)
        mUserUploadSceneAdapter.setOnItemChildClickListener { adapter, _, position ->
            CommonLogUtils.logI(TAG, "rv,$position")
            adapter.getViewByPosition(position, R.id.cl_root_card)?.let {
                mPopMenu.rvDataIndex = position
                mPopMenu.rvData = adapter.getItem(position) as CommunitySceneInfo
                mPopMenu.show(it)
            }
        }

        //初始化用户信息
        UIUtils.initRecyclerView(
            this, mViewBind.rvUserinfoList, LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false), false
        )
        mViewBind.rvUserinfoList.adapter = mUserNumberInfoAdapter
    }

    /**
     * 关注按钮呈现
     */
    private fun updateBtnUserSubscribe(userSubscribe: Int?) {
        //更新关注按钮隐藏不再关注菜单，防止显示关注按钮发送不再关注请求，导致关注数-1
        mViewBind.viewPop.visibility = View.GONE
        //用户未登录不显示关注按钮
        if (UserCenterManager.checkUserLoginStatus(false) == null) {
            mViewBind.tvCommunitySubscribe.visibility = View.GONE
            mViewBind.tvCommunitySubscribed.visibility = View.GONE
            return
        }
        when (userSubscribe) {
            //未关注
            CommunityFocusRequest.UNSUBSCRIBED -> {
                mViewBind.tvCommunitySubscribe.visibility = View.VISIBLE
                mViewBind.tvCommunitySubscribed.visibility = View.INVISIBLE
            }
            //已关注
            CommunityFocusRequest.SUBSCRIBED -> {
                mViewBind.tvCommunitySubscribe.visibility = View.INVISIBLE
                mViewBind.tvCommunitySubscribed.visibility = View.VISIBLE
            }
            //用户自己
            CommunityFocusRequest.SELF -> {
                mViewBind.tvCommunitySubscribe.visibility = View.GONE
                mViewBind.tvCommunitySubscribed.visibility = View.GONE
            }

            else -> {}
        }
    }

    override fun onDestroy() {
        mViewModel.cancelDisposable()
        UIUtils.clearReqResLottieOnDestroy(mViewBind)
        super.onDestroy()
    }

    /**
     * 主题切换手动切换进度条png
     */
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        AutoSizeCompat.autoConvertDensityOfGlobal(resources)
        UIUtils.setSmartRefreshOnConfigurationChanged(mViewBind.header, mViewBind.classicsFooter)
        mUserUploadSceneAdapter?.notifyDataSetChanged()
    }

    /**
     * 封装带有生命周期的flow写法
     * @param minActiveState
     * @param block
     * @receiver
     */
    private inline fun launchAndRepeatWithViewLifecycle(
        minActiveState: Lifecycle.State = Lifecycle.State.CREATED, crossinline block: suspend CoroutineScope.() -> Unit
    ) {
        lifecycleScope.launch {
            repeatOnLifecycle(minActiveState) {
                block()
            }
        }
    }

    override fun onClick(v: View?) {
        if (v == null) return
        when (v.id) {
            //返回键
            R.id.iv_back_icon, R.id.tv_back_desc -> {
                if (!DebouncingUtils.isValid(v)) return
                finish()
            }
            //关注按钮
            R.id.tv_community_subscribe -> {
                UserCenterManager.checkDebouncingAndNetworkAndLogin(v, supportFragmentManager) {
                    mViewModel.focusOrNoFocus(CommunityFocusRequest.SUBSCRIBED)
                }
            }
            //已关注按钮
            R.id.tv_community_subscribed -> {
                UserCenterManager.checkDebouncingAndNetworkAndLogin(v, supportFragmentManager) {
                    mViewBind.viewPop.visibility = View.VISIBLE
                }
            }
            //不再关注
            R.id.tv_no_longer_sub -> {
                UserCenterManager.checkDebouncingAndNetworkAndLogin(v, supportFragmentManager) {
                    //显示不再关注按钮时才发送取消关注，避免无意义重复请求
                    if (mViewBind.tvCommunitySubscribed.isVisible) {
                        mViewModel.focusOrNoFocus(CommunityFocusRequest.UNSUBSCRIBED)
                    }
                    mViewBind.viewPop.visibility = View.GONE
                }
            }
            //取消
            R.id.tv_no_longer_sub_cancel -> {
                if (!DebouncingUtils.isValid(v)) return
                mViewBind.viewPop.visibility = View.GONE
            }
        }
    }
}