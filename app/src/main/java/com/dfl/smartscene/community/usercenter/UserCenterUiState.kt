package com.dfl.smartscene.community.usercenter

import com.dfl.android.commonlib.CommonUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.apiweb.utils.ApiReqResState
import com.dfl.smartscene.bean.community.reponse.CommunitySceneInfo
import com.dfl.smartscene.bean.community.reponse.CommunityUserInfo

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2023/11/01
 * desc : 账号个人中心UiState,考虑到跳转场景详情直接使用帐号信息的上传场景列表参数
 * version: 1.2
 */
data class UserCenterUiState(

    /**
     * 关注事件请求状态
     */
    val focusApiResState: ApiReqResState = ApiReqResState(),
    /**
     * 删除场景请求状态
     */
    val deleteApiResState: ApiReqResState = ApiReqResState(),
    /**
     * 用户数据状态
     */
    val userInfoState: UserInfoState = UserInfoState()

)

data class UserInfoState(
    /**
     * 用户发布场景列表
     */
    val userUploadSceneList: List<CommunitySceneInfo> = emptyList(),
    /**
     * 用户数据请求状态
     */
    val userApiResState: ApiReqResState = ApiReqResState(),
    /**
     * 账号信息数字显示
     */
    val userNumberInfoList: List<UserNumberInfo> = emptyList(),
    /**
     * 用于账号信息,头像，用户名,上传场景数量
     */
    val communityUserInfo: CommunityUserInfo? = null,
    /**
     * 是否是当前用户
     */
    val isCurrentUser: Boolean = true
) {
    /**
     * 上传场景列表是否为空
     */
    val isEmptyUploadList: Boolean get() = userUploadNum == 0

    /**
     * 用户上传场景数量
     */
    var userUploadNum: Int = communityUserInfo?.userUploadNum ?: 0

    /**
     * 发布的场景 描述
     */
    val userUploadNumDesc: String
        get() = if (isCurrentUser) {
            val desc = CommonUtils.getString(R.string.scene_text_user_center_my_uploads_num)
            String.format(desc, userUploadNum)
        } else {
            val desc = CommonUtils.getString(R.string.scene_text_user_center_other_uploads_num)
            String.format(desc, userUploadNum)
        }
}
