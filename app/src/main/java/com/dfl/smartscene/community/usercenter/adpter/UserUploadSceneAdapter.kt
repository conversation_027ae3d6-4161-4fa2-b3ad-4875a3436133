package com.dfl.smartscene.community.usercenter.adpter

import androidx.core.view.isVisible
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.dfl.smartscene.bean.community.reponse.CommunitySceneInfo
import com.dfl.smartscene.communication.UserCenterManager
import com.dfl.smartscene.community.home.CommunityAdapter
import com.dfl.smartscene.community.home.CommunityAdapterItemListener
import com.dfl.smartscene.databinding.SceneRecycelItemCommunityBinding

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2023/11/01
 * desc : 个人页用户上传的场景rv的adapter
 * version: 1.0
 */
class UserUploadSceneAdapter(mListener: CommunityAdapterItemListener?) : CommunityAdapter(mListener) {
    override fun convert(holder: BaseDataBindingHolder<SceneRecycelItemCommunityBinding>, item: CommunitySceneInfo) {
        super.convert(holder, item)
        //显示上传时间隐藏用户头像用户名
        holder.dataBinding?.tvSceneUploadTime?.isVisible = true
        holder.dataBinding?.tvUserName?.isVisible = false
        holder.dataBinding?.ivUserHeard?.isVisible = false
        //更多菜单
        holder.dataBinding?.btnMore?.isVisible = item.uuid == UserCenterManager.userLoginStatus?.uuid
    }

    /**关注改变，需要更新列表*/
    override fun notifyByFocus(updateItem: CommunitySceneInfo) {
        //不需要通知rv界面变更
        data.forEach { communitySceneInfo ->
            communitySceneInfo.userSubscribe = updateItem.userSubscribe
        }
    }

}