package com.dfl.smartscene.community.post

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.main.MeIconBean
import com.dfl.smartscene.databinding.SceneRecycelItemCommunityIconBeanBinding

/**
 *Created by 钟文祥 on 2024/7/1.
 *Describer: 社区场景-发布选择-我的场景卡片的icon组
 */
class CommunityIconBeanAdapter :
    BaseQuickAdapter<MeIconBean, BaseDataBindingHolder<SceneRecycelItemCommunityIconBeanBinding>>(
        R.layout.scene_recycel_item_community_icon_bean
    ) {

    override fun convert(holder: BaseDataBindingHolder<SceneRecycelItemCommunityIconBeanBinding>, item: MeIconBean) {
        holder.dataBinding?.ivMeIconBean?.setImageResource(item.icon!!)
        holder.dataBinding?.ivMeIconBean?.setBackgroundResource(
            if (item.type < 0) {
                R.drawable.scene_shape_icon_bg_tr_r16
            } else if (item.type == 0) {
                R.drawable.scene_shape_icon_bg_fang_r16
            } else {
                R.drawable.scene_shape_icon_bg_yuan_r16
            }
        )
    }
}