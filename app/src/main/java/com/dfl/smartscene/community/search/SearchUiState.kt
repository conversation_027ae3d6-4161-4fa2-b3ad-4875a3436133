package com.dfl.smartscene.community.search

import com.dfl.smartscene.apiweb.utils.ApiReqResState
import com.dfl.smartscene.bean.community.reponse.CommunityListResponse

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2023/12/01
 * desc : 社区搜索页面UiState
 * version: 1.0
 */
data class SearchUiState(
	/**
	 * 搜索页面最近搜索状态
	 */
	val recentSearchState: RecentSearchState = RecentSearchState(),
	/**
	 * 搜索页面预搜索状态
	 */
	val preSearchState: PreSearchState = PreSearchState(),
	/**
	 * 搜索页面搜索状态
	 */
	val searchState: SearchState = SearchState()
) {
	companion object {
		/**
		 * 最近搜索状态
		 */
		const val RECENT_SEARCH = 0

		/**
		 * 预搜索状态
		 */
		const val PRE_SEARCH = 1

		/**
		 * 搜索状态
		 */
		const val SEARCH = 2

	}
}

/**
 * 搜索页面最近搜索状态
 * @property recentSearchList 最近搜索列表
 * @property recentApiReqResState 显示最近搜索
 */
data class RecentSearchState(
	/**
	 * 最近搜索列表
	 */
	val recentSearchList: List<String> = emptyList(),
	/**
	 * 最近搜索网络请求状态,切换最近搜索UI
	 */
	val recentApiReqResState: ApiReqResState = ApiReqResState(ApiReqResState.ReqResState.GetValueStart)
)

/**
 * 搜索页面预搜索状态
 * @property isOnPreSearchState 根据有无关键词切换UI
 * @property preApiReqResState 预搜索请求状态
 * @property preDataList 预搜索rv数据
 */
data class PreSearchState(
	/**
	 * 预搜索网络请求状态,切换预搜索状态UI
	 */
	val preApiReqResState: ApiReqResState = ApiReqResState(),
	/**
	 * 预搜索数据
	 */
	val preDataList: List<String> = emptyList(),
)

/**
 * 搜索页面搜索状态
 * @property searchApiReqResState 搜索请求状态
 * @property searchDataList 搜索结果rv数据
 */
data class SearchState(
	/**
	 * 搜索网络请求状态,切换搜索状态UI
	 */
	val searchApiReqResState: ApiReqResState = ApiReqResState(),
	/**
	 * 搜索数据
	 */
	val searchDataList: CommunityListResponse? = null,
)