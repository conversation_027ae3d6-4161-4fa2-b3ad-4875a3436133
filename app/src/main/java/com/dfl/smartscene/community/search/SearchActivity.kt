package com.dfl.smartscene.community.search


import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Html
import android.view.KeyEvent
import android.view.View
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.chad.library.adapter.base.BaseQuickAdapter
import com.dfl.android.common.base.BaseVBActivity
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.util.CommonToastUtils
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.commonlib.KeyboardUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.apiweb.utils.ApiReqResState
import com.dfl.smartscene.bean.community.reponse.CommunitySceneInfo
import com.dfl.smartscene.bean.community.request.CommunityFocusRequest
import com.dfl.smartscene.bean.community.request.CommunityReqType
import com.dfl.smartscene.bean.main.MySceneBean
import com.dfl.smartscene.communication.UserCenterManager
import com.dfl.smartscene.community.home.CommunityAdapter
import com.dfl.smartscene.databinding.SceneActivitySearchBinding
import com.dfl.smartscene.rv.EquallySpaceDecoration
import com.dfl.smartscene.ui.main.discover.detail.SceneDetailActivity
import com.dfl.smartscene.util.NetWorkUtils
import com.dfl.smartscene.util.UIUtils
import com.jeremyliao.liveeventbus.LiveEventBus
import com.scwang.smartrefresh.layout.api.RefreshLayout
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import me.jessyan.autosize.AutoSizeCompat
import me.jessyan.autosize.utils.AutoSizeUtils

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2023/12/01
 * desc : 社区搜索页面,历史搜索,预搜索,搜索结果
 * version: 1.0
 */
class SearchActivity : BaseVBActivity<SearchViewModel, SceneActivitySearchBinding>() {
    /**
     *搜索结果rv的adapter
     */
    private val mSearchAdapter: CommunityAdapter by lazy {
        CommunityAdapter { position, valueObject ->
            val item: CommunitySceneInfo = mSearchAdapter.data[position]
            when (valueObject) {
                CommunityReqType.Subscribe -> { //关注
                    mViewModel.focusOrNoFocus(item, supportFragmentManager, CommunityFocusRequest.SUBSCRIBED);
                }
                CommunityReqType.UnSubscribe -> { //取消关注
                    mViewModel.focusOrNoFocus(item, supportFragmentManager, CommunityFocusRequest.UNSUBSCRIBED);
                }
                CommunityReqType.Likes -> { //点赞
                    mViewModel.likeOrNoLike(item, supportFragmentManager);
                }
                CommunityReqType.Down -> { //下载
                    //处理html字符
                    //返回与原先地址不一样新对象,防止修改item高亮
                    val htmlUserName = Html.fromHtml(item.userName ?: "", Html.FROM_HTML_MODE_COMPACT)
                    val htmlSceneName = Html.fromHtml(item.scenarioInfo?.name ?: "", Html.FROM_HTML_MODE_COMPACT)
                    val htmlSceneDesc =
                        Html.fromHtml(item.scenarioInfoDa?.scenarioDesc ?: "", Html.FROM_HTML_MODE_COMPACT)
                    item.scenarioInfo?.name = htmlSceneName.toString()
                    item.scenarioInfo?.desc = htmlSceneDesc.toString()
                    item.userName = htmlUserName.toString()
                    item.convert()
                    mViewModel.downloadOrNo(item, supportFragmentManager, this, true);
                }
                else -> {}
            }
        }
    }

    /**
     * 当为false点击历史搜索不发起预搜索
     */
    private var needPreSearch = true

    /**
     *搜索联想rv的adapter
     */
    private val mSearchLenovoAdapter by lazy { LenovoAdapter() }

    /**
     * 最近搜索rv的adapter
     */
    private val mRecentSearchAdapter by lazy { RecentSearchAdapter() }

    companion object {
        //log标签
        const val TAG = GlobalConstant.GLOBAL_TAG.plus("SearchActivity")

        //入口
        fun openActivity(context: Context) {
            val intent = Intent(context, SearchActivity::class.java)
            context.startActivity(intent)
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        initRV()
        initRefreshLayout()
        initListener()
        UIUtils.setKeyBroadInputMinIfNotEnable(mViewBind.etSearch, 1)
    }

    override fun createObserver() {
        collectRecentSearchState()
        collectPreSearchState()
        collectSearchState()
        //监听场景点赞改变
        LiveEventBus.get<CommunitySceneInfo>(GlobalLiveEventConstants.KEY_COMMUNITY_LIKE_SUCCESS_UPDATE_UI)
            .observe(this) {
                mSearchAdapter.notifyByLike(it)
            }
        //监听场景下载改变
        LiveEventBus.get<CommunitySceneInfo>(GlobalLiveEventConstants.KEY_COMMUNITY_DOWNLOAD_SUCCESS_UPDATE_UI)
            .observe(this) {
                mSearchAdapter.notifyByDown(it)
            }
        //监听场景关注改变
        LiveEventBus.get<CommunitySceneInfo>(GlobalLiveEventConstants.KEY_COMMUNITY_FOCUS_SUCCESS_UPDATE_UI)
            .observe(this) {
                mSearchAdapter.notifyByFocus(it)
            }
        //场景添加成功的事件
        LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_ADD_SUCCESS, MySceneBean::class.java).observe(this) {
            //添加成功，退出界面
            if (it != null) {
                Handler(Looper.getMainLooper()).postDelayed({
                    if (!isFinishing && !isDestroyed) {
                        CommonLogUtils.logD(TAG, "下载页添加场景成功，退出当前界面")
                        finish()
                    }
                }, 100)
            }
        }
    }

    /**
     * 观察搜索状态相关参数变化并渲染UI
     */
    private fun collectSearchState() {
        launchAndRepeatWithViewLifecycle {
            mViewModel.searchUiState.map { it.searchState }.distinctUntilChanged().collect { state ->
                CommonLogUtils.logD(TAG, "collectSearchState ${state.searchApiReqResState.state}")
                //搜索关键字加载状态回调
                when (state.searchApiReqResState.state) {
                    ApiReqResState.ReqResState.GetValueStart -> {
                        //重置搜索结果为空
                        mViewBind.groupSearchResultEmpty.isVisible = false
                        KeyboardUtils.hideSoftInput(this@SearchActivity)
                        //搜索中
                        if (mSearchAdapter.itemCount == 0) {
                            //显示加载画面
                            UIUtils.showLoadingWithText(
                                mViewBind, getString(R.string.scene_text_web_content_is_searching)
                            )
                            //重置SmartRefreshLayout
                            mViewBind.slUpdate.isVisible = false
                            mViewBind.slUpdate.setNoMoreData(false)
                        }
                    }

                    ApiReqResState.ReqResState.GetValueFinish -> {
                        UIUtils.disShow(mViewBind)
                        //判断搜索结果是否为空
                        mViewBind.groupSearchResultEmpty.isVisible = state.searchDataList?.pages == 0
                        mViewBind.slUpdate.isVisible = !mViewBind.groupSearchResultEmpty.isVisible
                        state.searchDataList?.let {
                            if (it.pageIndex == 1) {
                                //第一次加载搜索结果
                                mSearchAdapter.setList(it.rows)
                            } else {
                                //加载更多
                                mSearchAdapter.addData(it.rows ?: emptyList())
                            }
                            mViewBind.slUpdate.finishLoadMore(1000, true, it.pageIndex >= it.pages)
                            mViewBind.slUpdate.finishRefresh()
                        }
                    }

                    ApiReqResState.ReqResState.Error -> {
                        //加载中网络异常画面 清空,隐藏rv
                        if (mSearchAdapter.itemCount == 0) {
                            mViewBind.slUpdate.isVisible = false
                            UIUtils.showError(mViewBind, state.searchApiReqResState.ex) {
                                //清空之前数据
                                mSearchAdapter.setList(emptyList())
                                mViewModel.getCommunitySearchByRefresh()
                            }
                        } else {
                            //搜索结果网络异常 取消上滑刷新下滑加载更多动画
                            mViewBind.slUpdate.finishLoadMore(false)
                            mViewBind.slUpdate.finishRefresh(false)
                        }
                    }
                    //离开搜索结果状态
                    null -> {
                        mViewModel.cancelSearchDisposable()
                        UIUtils.disShow(mViewBind)
                        mViewBind.groupSearchResultEmpty.isVisible = false
                        mViewBind.slUpdate.isVisible = false
                        mSearchAdapter.setList(emptyList())
                        mViewBind.slUpdate.finishLoadMore(false)
                        mViewBind.slUpdate.finishRefresh(false)
                    }
                }


            }
        }
    }

    /**
     * 观察预搜索状态相关参数变化并渲染UI
     */
    private fun collectPreSearchState() {
        launchAndRepeatWithViewLifecycle {
            mViewModel.searchUiState.map { it.preSearchState }.distinctUntilChanged().collect {
                CommonLogUtils.logD(TAG, "collectPreSearchState ${it.preApiReqResState.state}")
                //预搜索加载状态回调
                when (it.preApiReqResState.state) {
                    ApiReqResState.ReqResState.GetValueStart -> {
                        mViewBind.rvLenovo.isVisible = true
                    }

                    ApiReqResState.ReqResState.GetValueFinish -> {
                        mSearchLenovoAdapter.setList(it.preDataList)
                    }

                    ApiReqResState.ReqResState.Error -> {
                        //请求超时弱网提示
                        CommonToastUtils.show(getString(R.string.scene_toast_web_exception_unconnect_or_timeout))
                    }
                    //离开预搜索状态
                    null -> {
                        mViewModel.cancelPreDisposable()
                        mViewBind.rvLenovo.isVisible = false
                        mSearchLenovoAdapter.setList(emptyList())
                    }
                }
            }
        }
    }

    /**
     * 观察最近搜索状态相关参数变化并渲染UI
     */
    private fun collectRecentSearchState() {
        launchAndRepeatWithViewLifecycle {
            mViewModel.searchUiState.map { it.recentSearchState }.distinctUntilChanged().collect {
                CommonLogUtils.logD(TAG, "collectRecentSearchState ${it.recentApiReqResState.state}")
                when (it.recentApiReqResState.state) {
                    ApiReqResState.ReqResState.GetValueStart -> {
                        //游客身份进入,不显示最近搜索
                        if (UserCenterManager.checkUserLoginStatus(false) == null) {
                            mViewBind.groupRecentSearch.isVisible = false
                            return@collect
                        }
                        if (mViewModel.isFirstEnter) {
                            //第一次进入最近搜索读取数据库缓存且请求网络
                            mViewModel.getRecentSearch()
                            //拉起键盘
                            mViewBind.etSearch.requestFocus()
                            mViewModel.isFirstEnter = false
                        } else {
                            //不是第一次进入最近搜索只发起网络请求
                            mViewModel.getCommunityRecentSearch()
                        }
                        mViewBind.groupRecentSearch.isVisible = it.recentSearchList.isNotEmpty()
                        mRecentSearchAdapter.setNewData(it.recentSearchList)
                    }

                    ApiReqResState.ReqResState.GetValueFinish -> {
                        //加载后台最新数据
                        mViewBind.groupRecentSearch.isVisible = it.recentSearchList.isNotEmpty()
                        mRecentSearchAdapter.setNewData(it.recentSearchList)
                    }

                    ApiReqResState.ReqResState.Error -> {
                        //请求超时弱网提示
                        CommonToastUtils.show(getString(R.string.scene_toast_web_exception_unconnect_or_timeout))
                    }

                    null -> {
                        //离开最近搜索状态
                        mViewBind.groupRecentSearch.isVisible = false
                        mViewModel.cancelRecentSearch()
                    }
                }
            }
        }
    }


    private fun initRV() {
        //初始化搜索结果rv
        UIUtils.initRecyclerView(this, mViewBind.rvSearch, GridLayoutManager(this, 3), false)
        mViewBind.rvSearch.adapter = mSearchAdapter
        mViewBind.rvSearch.addItemDecoration(
            EquallySpaceDecoration(
                AutoSizeUtils.dp2px(this, 52f),
                AutoSizeUtils.dp2px(this, 52f),
                true
            )
        )
        //滑动时加载默认头像 停止滑动继续加载头像
        mViewBind.rvSearch.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == 0) { //停止
                    Glide.with(this@SearchActivity).resumeRequests()
                } else { //滑动
                    Glide.with(this@SearchActivity).pauseRequests()
                }
            }
        })
        //搜索结果rv
        mSearchAdapter.setOnItemClickListener { adapter: BaseQuickAdapter<*, *>, v: View, position: Int ->
            if (!DebouncingUtils.isValid(v)) { //防抖动
                return@setOnItemClickListener
            }
            val item = adapter.getItem(position) as CommunitySceneInfo
            //返回与原先地址不一样新对象,防止修改item高亮
            val htmlUserName = Html.fromHtml(item.userName ?: "", Html.FROM_HTML_MODE_COMPACT)
            val htmlSceneName = Html.fromHtml(item.scenarioInfo?.name ?: "", Html.FROM_HTML_MODE_COMPACT)
            val htmlSceneDesc = Html.fromHtml(item.scenarioInfoDa?.scenarioDesc ?: "", Html.FROM_HTML_MODE_COMPACT)
            item.scenarioInfo?.name = htmlSceneName.toString()
            item.scenarioInfo?.desc = htmlSceneDesc.toString()
            item.userName = htmlUserName.toString()
            item.convert()
            SceneDetailActivity.startCommunitySceneDetailActivity(this, item)
        }

        //初始化搜索联想rv
        UIUtils.initRecyclerView(
            this, mViewBind.rvLenovo, LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false), false
        )
        mViewBind.rvLenovo.adapter = mSearchLenovoAdapter
        mViewBind.rvLenovo.addItemDecoration(EquallySpaceDecoration(AutoSizeUtils.dp2px(this, 30f)))
        mSearchLenovoAdapter.setOnItemClickListener { adapter, v, position ->
            if (!DebouncingUtils.isValid(v)) { //防抖动
                return@setOnItemClickListener
            }
            val htmlKey = Html.fromHtml(adapter.getItem(position) as String, Html.FROM_HTML_MODE_COMPACT).toString()
            mViewBind.etSearch.setText(htmlKey)
            val text = mViewBind.etSearch.text.toString()
            mViewBind.etSearch.setSelection(text.length)
            //清空之前数据
            mSearchAdapter.setList(emptyList())
            mViewModel.getCommunitySearchByBtnSearch(text)
        }

        //初始化最近搜索流式折叠布局
        mViewBind.foldRecentSearch.setAdapter(mRecentSearchAdapter)
        //最近搜索项点击
        mRecentSearchAdapter.setOnClickListener { keyWord ->
            //文本框显示关键词,移动光标,不发起预搜索
            needPreSearch = false
            mViewBind.etSearch.setText(keyWord)
            val text = mViewBind.etSearch.text.toString()
            mViewBind.etSearch.setSelection(text.length)
            //清空之前数据
            mSearchAdapter.setList(emptyList())
            mViewModel.getCommunitySearchByBtnSearch(text)

        }
    }

    private fun initRefreshLayout() {
        UIUtils.initRefreshLayout(mViewBind)
        mViewBind.slUpdate.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
            override fun onRefresh(refreshLayout: RefreshLayout) {
                NetWorkUtils.haveNetwork({
                    mViewModel.getCommunitySearchByRefresh()
                }, {
                    mViewBind.slUpdate.finishRefresh(false)
                })
            }

            override fun onLoadMore(refreshLayout: RefreshLayout) {
                NetWorkUtils.haveNetwork({
                    mViewModel.getCommunitySearchByLoadMore()
                }, {
                    mViewBind.slUpdate.finishLoadMore(false)
                })
            }
        })
    }

    /**
     * 除了rv的控件点击事件
     */
    private fun initListener() {
        //返回
        mViewBind.ivBack.setOnClickListener {
            if (!DebouncingUtils.isValid(it)) {
                return@setOnClickListener
            }
            finish()
        }
        //搜索tv按钮
        mViewBind.tvSearch.setOnClickListener {
            if (!DebouncingUtils.isValid(it) || mViewBind.etSearch.text.toString().isEmpty()) return@setOnClickListener
            NetWorkUtils.haveNetwork({
                //清空之前数据
                mSearchAdapter.setList(emptyList())
                mViewModel.getCommunitySearchByBtnSearch(mViewBind.etSearch.text.toString())
            })
        }
        //输入框
        mViewBind.etSearch.doAfterTextChanged {
            val str = it?.toString() ?: ""
            mViewBind.tvSearch.isEnabled = !it.isNullOrEmpty()
            //点击历史搜索为false 不发起预搜索
            if (needPreSearch) {
                mViewModel.getCommunityPreSearch(str)
            } else {
                needPreSearch = true
            }
        }

        //软键盘
        mViewBind.etSearch.setOnKeyListener { view, keyCode, event ->
            //搜索键或者enter键
            if ((keyCode == KeyEvent.KEYCODE_SEARCH || keyCode == KeyEvent.KEYCODE_ENTER) && event.action == KeyEvent.ACTION_UP) {
                //清空之前数据
                mSearchAdapter.setList(emptyList())
                mViewModel.getCommunitySearchByBtnSearch(mViewBind.etSearch.text.toString())
                true
            } else {
                false
            }
        }
        //清空历史搜索
        mViewBind.ivClearRecentSearch.setOnClickListener {
            if (!DebouncingUtils.isValid(it)) return@setOnClickListener
            //网络异常判断
            NetWorkUtils.haveNetwork({ mViewModel.clearRecentSearch() })
        }
    }

    /**
     * 封装带有生命周期的flow写法
     * @param minActiveState
     * @param block
     * @receiver
     */
    private inline fun launchAndRepeatWithViewLifecycle(
        minActiveState: Lifecycle.State = Lifecycle.State.CREATED, crossinline block: suspend CoroutineScope.() -> Unit
    ) {
        lifecycleScope.launch {
            repeatOnLifecycle(minActiveState) {
                block()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        mViewModel.cancelDisposable()
        UIUtils.clearReqResLottieOnDestroy(mViewBind)
        CommonLogUtils.logI(TAG, "onDestroy")
    }

    /**
     * 主题切换手动切换进度条png
     */
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        AutoSizeCompat.autoConvertDensityOfGlobal(resources)
        UIUtils.setSmartRefreshOnConfigurationChanged(mViewBind.header, mViewBind.classicsFooter)
        mSearchAdapter?.notifyDataSetChanged()
    }

}