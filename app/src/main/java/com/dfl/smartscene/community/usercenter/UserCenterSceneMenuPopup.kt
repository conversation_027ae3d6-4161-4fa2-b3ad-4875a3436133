package com.dfl.smartscene.community.usercenter

import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.PopupWindow
import com.dfl.android.common.base.BaseMenuPopup
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.community.reponse.CommunitySceneInfo
import com.dfl.smartscene.databinding.ScenePopupCommunityUsercenterSceneMenuBinding

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/11/19
 * desc : 账号页删除菜单
 * version: 1.2
 */
class UserCenterSceneMenuPopup(onMenuClickListener: OnMenuClickListener<CommunitySceneInfo>) :
    BaseMenuPopup<CommunitySceneInfo>(onMenuClickListener) {
    override val TAG = GlobalConstant.GLOBAL_TAG.plus("MySceneMenuPopup")
    private lateinit var mVBind: ScenePopupCommunityUsercenterSceneMenuBinding

    override fun show(view: View) {
        if (mPopupWindow == null) {
            val context = view.context
            mVBind = ScenePopupCommunityUsercenterSceneMenuBinding.inflate(LayoutInflater.from(context))
            val popup = PopupWindow(mVBind.root, view.width, view.height)
            popup.isOutsideTouchable = true
            //去掉防止状态栏异常
            //popup.isFocusable = true
            popup.animationStyle = R.style.popupAnimationTheme
            mPopupWindow = popup
            popup.isClippingEnabled = false
            mVBind.cslPopBg.setOnClickListener {
                popup.dismiss()
            }
            mVBind.llPopMenu.setOnClickListener {
                onMenuClickListener?.onMenuItemSelected(this, 0, rvDataIndex, rvData)
            }
        }
        val location = IntArray(2)
        view.getLocationOnScreen(location)
        mPopupWindow!!.showAtLocation(view, Gravity.TOP and Gravity.END, location[0], location[1])
        CommonLogUtils.logD(TAG, "1:isShowing=${mPopupWindow?.isShowing}")
    }

    override fun dismiss() {
        mPopupWindow?.dismiss()
    }

}
