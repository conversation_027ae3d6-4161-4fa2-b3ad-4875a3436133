package com.dfl.smartscene.community.search

import android.text.Html
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.smartscene.R

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2023/12/05
 * desc : 预搜索联想词rv的adapter
 * version: 1.0
 */
class LenovoAdapter() : BaseQuickAdapter<String, BaseViewHolder>(R.layout.scene_recycle_item_search_lenovo) {
    override fun convert(holder: BaseViewHolder, item: String) {
        holder.setText(R.id.tv_search_lenovo, Html.fromHtml(item, Html.FROM_HTML_MODE_COMPACT))
    }
}