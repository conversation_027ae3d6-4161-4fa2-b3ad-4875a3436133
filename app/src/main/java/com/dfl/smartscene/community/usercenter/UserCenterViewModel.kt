package com.dfl.smartscene.community.usercenter

import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.util.StringUtils.getString
import com.dfl.android.common.util.TaskExecutor
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.apiweb.service.CommunityService
import com.dfl.smartscene.apiweb.utils.ApiException
import com.dfl.smartscene.apiweb.utils.ApiReqResState
import com.dfl.smartscene.apiweb.utils.RxSubscriber
import com.dfl.smartscene.bean.apibase.BaseResponseBean
import com.dfl.smartscene.bean.community.datasource.UserCenterDataSource
import com.dfl.smartscene.bean.community.datasource.UserCenterRepository
import com.dfl.smartscene.bean.community.reponse.CommunitySceneInfo
import com.dfl.smartscene.bean.community.reponse.CommunityUserInfo
import com.dfl.smartscene.bean.community.request.CommunityFocusRequest
import com.dfl.smartscene.bean.mechanism.BaseDataSource
import com.dfl.smartscene.bean.mechanism.BaseRepository
import com.dfl.smartscene.bean.mechanism.ILoadDataCallBack
import com.dfl.smartscene.community.home.CommunityBaseViewModel
import com.jeremyliao.liveeventbus.LiveEventBus
import io.reactivex.disposables.Disposable
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import java.lang.ref.WeakReference

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2023/10/30
 * desc:个人中心的viewModel
 * version:1.1
 */
class UserCenterViewModel : CommunityBaseViewModel() {
    /**
     *UserCenterActivity渲染所需的UiSate，可变StateFlow形式
     */
    private val mUserCenterUiState: MutableStateFlow<UserCenterUiState> = MutableStateFlow(UserCenterUiState())
    val userCenterUiState get() = mUserCenterUiState.asStateFlow()
    private val TAG = GlobalConstant.GLOBAL_TAG.plus("UserCenterViewModel")

    /**
     * 第几页
     */
    private var pageNum = 1

    /**
     * 是否显示加载画面,未获取到数据前都显示
     */
    var isShowLoading = true

    /**
     *请求用户信息disposable
     */
    private var mUserInfoDisposable: Disposable? = null

    /**
     *删除场景事件disposable
     */
    private var mDeleteDisposable: Disposable? = null

    /**
     * 访问用户id
     */
    private var visitedUserId: String = ""

    /**
     * 账号页获取数据仓库
     */
    private val mRepository = UserCenterRepository.getInstance()

    /**
     * 账号页获取数据回调
     */
    private var mRepositoryCallBack = UserCenterRepositoryCallBack(this)

    /**
     * 本地之前的数据
     */
    private var preData: CommunityUserInfo? = null

    /**
     * 是否是官方 1-官方
     */
    private var isOfficial: Int = 0

    /**
     * 是否要重新获取当前页数数据
     */
    private var isRefreshCurPage: Boolean = false

    /**
     * 是否是当前用户
     */
    private var isCurrentUser: Boolean = true

    /**是否进入账号页比较本地数据，判断显示红点，比较后false*/
    private var isFirstCompare: Boolean = true

    fun initData(isOfficial: Int, visitedUserId: String, isCurrentUser: Boolean) {
        this.visitedUserId = visitedUserId
        this.isOfficial = isOfficial
        this.isCurrentUser = isCurrentUser
        pageNum = 1
        firstLoadData()
    }

    /**
     * 通过repository获取数据
     * @param loadType 加载的方式
     */
    private fun loadData(loadType: BaseRepository.LoadType) {
        TaskExecutor.io {
            mRepository.loadType = loadType
            mRepository.dataSourceCallBack = UserCenterDataSource(pageNum, isOfficial, visitedUserId)
            mRepository.loadData(visitedUserId, mRepositoryCallBack)
        }
    }

    /**
     * 第一次加载,读取缓存且请求后端
     */
    private fun firstLoadData() {
        pageNum = 1
        loadData(BaseRepository.LoadType.CachedAndRefreshOnlyRemote)
    }

    /**
     * 请求后端刷新
     */
    fun refreshLoadData() {
        pageNum = 1
        loadData(BaseRepository.LoadType.RefreshOnlyRemote)
    }

    /**
     * 请求后端加载更多
     */
    fun loadMoreData() {
        pageNum++
        loadData(BaseRepository.LoadType.LoadMore)
    }

    /**
     * 获取当前页数的数据
     */
    fun refreshCurPageData() {
        CommonLogUtils.logD(TAG, "获取当前页数的数据")
        this.isRefreshCurPage = true
        loadData(BaseRepository.LoadType.RefreshOnlyRemote)
    }

    /**
     * 不使用内部类，防止持有外部类引用导致内存泄漏
     */
    class UserCenterRepositoryCallBack(viewModel: UserCenterViewModel) : ILoadDataCallBack<CommunityUserInfo> {
        private var mWeakReference: WeakReference<UserCenterViewModel>? = WeakReference(viewModel)
        fun clearReference() {
            mWeakReference?.clear()
            mWeakReference = null
        }

        /**
         * 开始发起网络请求回调
         * LoadType.CachedAndRefreshOnlyRemote本地有数据不会进入该回调
         */
        override fun onLoadRemoteStart() {
            mWeakReference?.get()?.apply {
                CommonLogUtils.logD(
                    TAG, "onLoadRemoteStart,uuid:${visitedUserId}发起网络请求显示加载视图"
                )
                mUserCenterUiState.update {
                    it.copy(
                        userInfoState = it.userInfoState.copy(
                            userApiResState = ApiReqResState(ApiReqResState.ReqResState.GetValueStart),
                        )
                    )
                }
            }

        }

        /**
         * 本地或远程获取到数据，本地数据先返回
         */
        override fun onDataLoaded(values: CommunityUserInfo?, type: BaseDataSource.DataSourceType) {
            mWeakReference?.get()?.apply {
                CommonLogUtils.logI(
                    TAG, "onDataLoaded,uuid:${visitedUserId} 有数据,来自${type.valueStr}"
                )
                //先加载ui,防止本地数据之间比较
                convertToUiState(values)
                if (type != BaseDataSource.DataSourceType.Remote) {
                    //存储本地数据，用作比较增加
                    preData = values
                } else if (isFirstCompare) {
                    //进入个人页远程数据比较本地是否增加后，不再比较
                    isFirstCompare = false
                }
            }
        }

        /**
         * 本地获取错误或为空,远程获取为空
         */
        override fun onDataIsNullOrError(type: BaseDataSource.DataSourceType) {
            mWeakReference?.get()?.apply {
                CommonLogUtils.logE(
                    TAG, "onDataIsNullOrError,uuid:${visitedUserId} 无数据,来自${type.valueStr}"
                )
                isRefreshCurPage = false
            }
        }

        /**
         * 远程获取错误
         */
        override fun onApiError(ex: ApiException) {
            mWeakReference?.get()?.apply {
                CommonLogUtils.logE(
                    TAG, "onApiError,uuid:${visitedUserId} 错误:${ex.getHttpCode()},${ex.message}"
                )
                mUserCenterUiState.update {
                    it.copy(
                        userInfoState = it.userInfoState.copy(
                            userApiResState = ApiReqResState(
                                ApiReqResState.ReqResState.Error, ex
                            )
                        )
                    )
                }
                isRefreshCurPage = false
            }
        }
    }

    /**
     *把communityUserInfo转换为所需要的UserCenterUiState
     * @param communityUserInfo
     */
    fun convertToUiState(communityUserInfo: CommunityUserInfo?) {
        if (communityUserInfo != null) {
            mUserCenterUiState.update {
                var isLikeAdd = false
                var isFanAdd = false
                //当前账号且进入账号页比较才会有小红点，再次获取数据默认false不再显示
                if (it.userInfoState.isCurrentUser && isFirstCompare) {
                    preData?.let { preData ->
                        isLikeAdd = preData.likeNum < communityUserInfo.likeNum
                        isFanAdd = preData.userFanNum < communityUserInfo.userFanNum
                    }
                }
                val likes = UserNumberInfo(
                    getString(R.string.scene_text_user_center_likes_desc), communityUserInfo.likeNum, isLikeAdd
                )
                val subscribes = UserNumberInfo(
                    getString(R.string.scene_text_user_center_subscribes_desc),
                    communityUserInfo.userSubScribeNum,
                )
                val fans = UserNumberInfo(
                    getString(R.string.scene_text_user_center_fans_desc), communityUserInfo.userFanNum, isFanAdd
                )
                //刷新重置,加载或者补位添加
                val uploadList = if (pageNum == 1 && !isRefreshCurPage) {
                    mutableListOf()
                } else {
                    it.userInfoState.userUploadSceneList.toMutableList()
                }
                if (isRefreshCurPage && communityUserInfo.sceneList.isNotEmpty()) {
                    CommonLogUtils.logD(TAG, "后台删除成功，补位显示新的数据")
                    uploadList.add(communityUserInfo.sceneList.last())
                    isRefreshCurPage = false
                } else {
                    uploadList.addAll(communityUserInfo.sceneList)
                }
                it.copy(
                    userInfoState = it.userInfoState.copy(
                        userNumberInfoList = listOf(subscribes, fans, likes),
                        userApiResState = ApiReqResState(ApiReqResState.ReqResState.GetValueFinish),
                        communityUserInfo = communityUserInfo,
                        userUploadSceneList = uploadList.toList(),
                        isCurrentUser = isCurrentUser
                    )
                )
            }
        } else {
            mUserCenterUiState.update {
                val uploads = UserNumberInfo(
                    getString(R.string.scene_text_user_center_likes_desc), 0
                )
                val subscribes = UserNumberInfo(
                    getString(R.string.scene_text_user_center_subscribes_desc), 0
                )
                val fans = UserNumberInfo(
                    getString(R.string.scene_text_user_center_fans_desc), 0
                )
                it.copy(
                    userInfoState = it.userInfoState.copy(
                        userNumberInfoList = listOf(uploads, subscribes, fans),
                        userApiResState = ApiReqResState(ApiReqResState.ReqResState.GetValueFinish),
                        communityUserInfo = null,
                        userUploadSceneList = emptyList(),
                        isCurrentUser = isCurrentUser
                    )
                )
            }
        }

    }

    /**
     * 关注,下载,点赞事件更新本地数据库
     *
     * @param updateItem
     * @param type
     */
    fun updateLocalByFocusLikeDown(updateItem: CommunitySceneInfo, type: String) {
        mRepository.updateLocalByFocusLikeDown(updateItem, type)
    }

    /**
     * 关注事件请求
     * @param userSubscribe 请求关注参数，SUBSCRIBED发送关注请求
     */
    fun focusOrNoFocus(userSubscribe: Int) {
        mUserCenterUiState.value.userInfoState.communityUserInfo?.let { communityUserInfo ->
            CommunityService.focusOrNoFocus(visitedUserId, CommunityFocusRequest.isSubscribed(userSubscribe))
                .subscribe(object : RxSubscriber<BaseResponseBean>() {
                    override fun onAddDisposable(d: Disposable?) {
                        mFocusDisposable = d
                    }

                    override fun onApiError(ex: ApiException?) {
                        //请求响应已经被消耗情况退出，防止同类型请求导致界面多次变化
                        if (mFocusDisposable == null) return
                        mFocusDisposable = null
                        CommonLogUtils.logE(
                            TAG,
                            "focusOrNoFocus,失败:${ex?.message}"
                        )
                        mUserCenterUiState.update {
                            val apiReqResState = ApiReqResState(ApiReqResState.ReqResState.Error, ex)
                            apiReqResState.map[GlobalLiveEventConstants.KEY_COMMUNITY_FOCUS_SUCCESS_UPDATE_UI] =
                                CommonUtils.getString(
                                    R.string.scene_toast_web_exception_unconnect_or_timeout
                                )
                            it.copy(focusApiResState = apiReqResState)
                        }
                    }

                    override fun onApiNext(value: BaseResponseBean) {
                        //请求响应已经被消耗情况退出，防止同类型请求导致界面多次变化
                        if (mFocusDisposable == null) return
                        mFocusDisposable = null
                        mUserCenterUiState.update {
                            val apiReqResState = ApiReqResState(ApiReqResState.ReqResState.GetValueFinish)
                            if (value.isSuccess()) {
                                //关注事件成功,变更关注状态
                                communityUserInfo.userSubscribe = userSubscribe
                                //如果没有上传场景返回只有uuid和userSubscribe有效的CommunitySceneInfo;有返回以第一个
                                val communitySceneInfo = if (communityUserInfo.sceneList.isEmpty()) {
                                    CommunitySceneInfo.createInstance(visitedUserId, communityUserInfo.userSubscribe)
                                } else {
                                    communityUserInfo.sceneList[0].userSubscribe = communityUserInfo.userSubscribe
                                    communityUserInfo.sceneList[0]
                                }
                                //发送关注广播
                                LiveEventBus.get(
                                    GlobalLiveEventConstants.KEY_COMMUNITY_FOCUS_SUCCESS_UPDATE_UI,
                                    CommunitySceneInfo::class.java
                                ).post(communitySceneInfo)
                            } else {
                                apiReqResState.state = ApiReqResState.ReqResState.Error
                            }
                            //只更新用户信息ui，列表通过LiveEventBus更新
                            it.copy(
                                focusApiResState = apiReqResState
                            )
                        }
                    }
                })
        }
    }

    /**
     * 删除上传场景
     * @param communityScenarioId 场景id
     */
    fun deleteUploadScene(communityScenarioId: String?, position: Int) {
        if (communityScenarioId == null) return
        CommunityService.deleteCommunityScene(communityScenarioId).subscribe(object : RxSubscriber<BaseResponseBean>() {
            override fun onAddDisposable(d: Disposable?) {
                mDeleteDisposable = d
                CommonLogUtils.logI(
                    TAG,
                    "deleteUploadScene communityScenarioId:$communityScenarioId,position:$position"
                )
                //先假删除
                mUserCenterUiState.update {
                    val apiReqResState = ApiReqResState(ApiReqResState.ReqResState.GetValueStart)
                    apiReqResState.map["position"] = position
                    //删除发布场景ui列表指定位置
                    val newUserUploadSceneList = it.userInfoState.userUploadSceneList.toMutableList()
                    newUserUploadSceneList.removeAt(position)
                    //删除communityUserInfo发布场景列表指定位置
                    var communitySceneInfo = it.userInfoState.communityUserInfo
                    if (communitySceneInfo != null) {
                        val newUserUploadNum = communitySceneInfo.userUploadNum - 1
                        val newSceneList = communitySceneInfo.sceneList.toMutableList()
                        if (position < newSceneList.size) {
                            newSceneList.removeAt(position)
                        }
                        communitySceneInfo = communitySceneInfo.copy(
                            userUploadNum = newUserUploadNum,
                            sceneList = newSceneList
                        )
                    }
                    //userInfoState设置state=null，不让发布列表ui更新
                    it.copy(
                        deleteApiResState = apiReqResState,
                        userInfoState = it.userInfoState.copy(
                            communityUserInfo = communitySceneInfo,
                            userUploadSceneList = newUserUploadSceneList,
                            userApiResState = ApiReqResState()
                        )
                    )
                }
            }

            override fun onApiError(ex: ApiException?) {
                //请求响应已经被消耗情况退出，防止同类型请求导致界面多次变化
                if (mDeleteDisposable == null) return
                mDeleteDisposable = null
                CommonLogUtils.logE(
                    TAG, "deleteUploadScene,communityScenarioId:${communityScenarioId} 失败:${ex?.message}"
                )
                mUserCenterUiState.update {
                    it.copy(deleteApiResState = ApiReqResState(ApiReqResState.ReqResState.Error, ex))
                }
            }

            override fun onApiNext(value: BaseResponseBean) {
                //请求响应已经被消耗情况退出，防止同类型请求导致界面多次变化
                if (mDeleteDisposable == null) return
                mDeleteDisposable = null
                mUserCenterUiState.update {
                    val apiReqResState = ApiReqResState(ApiReqResState.ReqResState.GetValueFinish)
                    if (value.isSuccess() && it.userInfoState.communityUserInfo != null) {
                        mRepository.add2Local(it.userInfoState.communityUserInfo)
                    } else {
                        apiReqResState.state = ApiReqResState.ReqResState.Error
                    }
                    it.copy(deleteApiResState = apiReqResState)
                }
            }

        })
    }

    /**
     * 取消请求
     */
    override fun cancelDisposable() {
        super.cancelDisposable()
        CommonLogUtils.logD(TAG, "cancel disposable")
        mUserInfoDisposable?.let {
            it.takeUnless { it.isDisposed }?.dispose()
            mUserInfoDisposable = null
        }
        mDeleteDisposable?.let {
            it.takeUnless { it.isDisposed }?.dispose()
            mDeleteDisposable = null
        }
        mRepositoryCallBack.clearReference()
        mRepository.dataSourceCallBack.cancelDisposable()
    }
}