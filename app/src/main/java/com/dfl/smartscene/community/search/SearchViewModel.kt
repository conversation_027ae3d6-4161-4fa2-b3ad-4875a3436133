package com.dfl.smartscene.community.search

import androidx.lifecycle.viewModelScope
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.apiweb.service.CommunityService
import com.dfl.smartscene.apiweb.utils.ApiException
import com.dfl.smartscene.apiweb.utils.ApiReqResState
import com.dfl.smartscene.apiweb.utils.RxSubscriber
import com.dfl.smartscene.bean.apibase.BaseResponseBean
import com.dfl.smartscene.bean.community.reponse.CommunityListResponse
import com.dfl.smartscene.bean.community.reponse.CommunityPreSearch
import com.dfl.smartscene.bean.community.reponse.CommunityRecentSearchResponse
import com.dfl.smartscene.community.home.CommunityBaseViewModel
import com.dfl.android.common.global.GlobalConstant
import com.dfl.smartscene.room.DbManager
import com.dfl.smartscene.room.dao.RecentSearchDao
import io.reactivex.disposables.Disposable
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

/**
 *Created by 钟文祥 on 2023/11/20.
 *Describer: 搜索界面 viewModel
 */
class SearchViewModel : CommunityBaseViewModel() {
    private val TAG = GlobalConstant.GLOBAL_TAG.plus("SearchViewModel")

    /**
     * 预搜索请求disposable
     */
    private var mPreDisposable: Disposable? = null

    /**
     * 搜索请求disposable
     */
    private var mSearchDisposable: Disposable? = null

    /**
     * 最近搜索请求disposable
     */
    private var mRecentSearchDisposable: Disposable? = null

    /**
     * 清空历史搜索
     */
    private var mClearRecentSearchDisposable: Disposable? = null

    /**第几页*/
    private var mPageNum: Int = 1

    /**关键字*/
    private var mKeyword: String = ""

    /**
     * searchActivity视图所需UiState,可变形式
     */
    private val mSearchUiState: MutableStateFlow<SearchUiState> = MutableStateFlow(SearchUiState())

    /**
     * searchActivity视图所需UiState,不可变形式
     */
    val searchUiState = mSearchUiState.asStateFlow()

    /**
     * 是否第一次进入最近搜索,是拉起键盘,不发起请求历史搜索接口
     */
    var isFirstEnter = true

    /**
     * 最近搜索数据库dao
     */
    private val mRecentSearchDao: RecentSearchDao? = DbManager.getDBMaster()?.RecentSearchDao()
    private val mRecentSearchList: MutableList<String> = emptyList<String>().toMutableList()


    /**通过按钮搜索*/
    fun getCommunitySearchByBtnSearch(keyword: String) {
        if (keyword.isEmpty()) return
        mPageNum = 1
        mKeyword = keyword
        getCommunitySearch()
    }

    /**刷新搜索*/
    fun getCommunitySearchByRefresh() {
        mPageNum = 1
        getCommunitySearch()
    }

    /**搜索 加载更多*/
    fun getCommunitySearchByLoadMore() {
        mPageNum++
        getCommunitySearch()
    }

    /**
     * 更新Uistate,发起搜索请求,缓存搜索词到本地
     */
    private fun getCommunitySearch() {
        CommunityService.getCommunitySearch(mKeyword, mPageNum)
            .subscribe(object : RxSubscriber<CommunityListResponse>() {
                override fun onAddDisposable(d: Disposable?) {
                    //若上一个请求未完成取消上一次请求
                    mSearchDisposable?.dispose()
                    mSearchDisposable = d
                    //切换到搜索状态,其他状态设为未激活
                    checkoutUiState(SearchUiState.SEARCH)
                    CommonLogUtils.logD(TAG, "getCommunitySearch GetValueStart")
                }

                override fun onApiNext(value: CommunityListResponse?) {
                    //转换服务端到车机端 社区场景详情
                    value?.rows?.forEach {
                        it.userName = renderHtmlString(it.userName)
                        it.scenarioInfo?.name = renderHtmlString(it.scenarioInfo?.name)
                        it.scenarioInfo?.desc = renderHtmlString(it.scenarioInfo?.desc)
                        it.convert()
                    }
                    mSearchUiState.update {
                        val searchState = it.searchState.copy(
                            searchDataList = value,
                            searchApiReqResState = ApiReqResState(ApiReqResState.ReqResState.GetValueFinish)
                        )
                        it.copy(searchState = searchState)
                    }
                    CommonLogUtils.logD(TAG, "getCommunitySearch $mKeyword success, has ${value?.pages} pages  ")
                    mSearchDisposable = null
                }

                override fun onApiError(ex: ApiException?) {
                    mSearchUiState.update {
                        val searchState = it.searchState.copy(
                            searchApiReqResState = ApiReqResState(ApiReqResState.ReqResState.Error, ex)
                        )
                        it.copy(searchState = searchState)
                    }
                    CommonLogUtils.logE(TAG, "getCommunitySearch Error:$ex")
                    mSearchDisposable = null
                }
            })
    }

    /**
     * 更新Uistate,发起预搜索请求
     * @param keyword 关键字
     */
    fun getCommunityPreSearch(keyword: String) {
        if (keyword.isNotEmpty()) {
            //关键词不为空,进入预搜索状态,发起预搜索
            CommunityService.getCommunityPreSearch(keyword).subscribe(object : RxSubscriber<CommunityPreSearch>() {
                override fun onAddDisposable(d: Disposable) {
                    //防抖,如用户输入a后修改为ab,取消a的网络请求,发起ab的请求
                    mPreDisposable?.dispose()
                    mPreDisposable = d
                    //切换到预搜索状态,其他状态设为未激活
                    checkoutUiState(SearchUiState.PRE_SEARCH)
                    CommonLogUtils.logD(TAG, "getCommunityPreSearch GetValueStart")
                }

                override fun onApiNext(value: CommunityPreSearch?) {
                    val renderValue = value?.words?.toMutableList()
                    renderValue?.forEachIndexed { index, s ->
                        renderValue[index] = renderHtmlString(s)
                    }
                    mSearchUiState.update {
                        val preSearchState = it.preSearchState.copy(
                            preDataList = renderValue?.toList() ?: emptyList(),
                            preApiReqResState = ApiReqResState(ApiReqResState.ReqResState.GetValueFinish)
                        )
                        it.copy(preSearchState = preSearchState)
                    }
                    CommonLogUtils.logD(
                        TAG,
                        "getCommunityPreSearch keyword: $keyword GetValueFinish,has ${value?.words?.size} pages"
                    )
                    mPreDisposable = null
                }

                override fun onApiError(ex: ApiException) {
                    mSearchUiState.update {
                        val preSearchState = it.preSearchState.copy(
                            preApiReqResState = ApiReqResState(ApiReqResState.ReqResState.Error, ex)
                        )
                        it.copy(preSearchState = preSearchState)
                    }
                    CommonLogUtils.logE(TAG, "getCommunityPreSearch keyword: $keyword onApiError $ex")
                    mPreDisposable = null
                }
            })
        } else {
            //关键词为空,进入最近搜索状态,其他状态设置未激活
            checkoutUiState(SearchUiState.RECENT_SEARCH)
        }

    }

    /**测试搜索历史*/
    val list = arrayListOf(
        "；；555",
        "呵呵呵",
        "133；；；；；；；；；",
        "4443444434",
        "啊啊啊啊啊啊啊啊",
        "；；555",
        "啊啊啊",
        "啊啊啊啊啊啊啊啊啊啊啊啊啊",
        "啊啊啊啊啊啊啊啊啊啊啊啊啊",
        "啊啊啊啊啊啊啊啊啊啊啊啊啊",
        "啊啊啊啊啊啊啊啊啊啊啊啊啊",
        "啊啊啊啊啊啊啊啊",
        "1234",
        "45677",
        "111111111",
        "啊啊啊",
        "123",
        "1",
        "12",
    )

    /**
     * 获取后台最近搜索字符串列表
     */
    fun getCommunityRecentSearch() {
        CommunityService.getCommunityRecentSearch().subscribe(object : RxSubscriber<CommunityRecentSearchResponse>() {
            override fun onAddDisposable(d: Disposable?) {
                //若上一个请求未完成取消上一次请求
                mRecentSearchDisposable?.dispose()
                mRecentSearchDisposable = d
                //切换到最近搜索状态,加载本地缓存,其他状态设为未激活
                checkoutUiState(SearchUiState.RECENT_SEARCH)
                mSearchUiState.update {
                    it.copy(recentSearchState = it.recentSearchState.copy(recentSearchList = mRecentSearchList))
                }
                CommonLogUtils.logD(TAG, "getRecentSearch GetValueStart")
            }

            override fun onApiError(ex: ApiException?) {
                //请求失败
                mSearchUiState.update {
                    it.copy(
                        recentSearchState = it.recentSearchState.copy(
                            recentApiReqResState = ApiReqResState(ApiReqResState.ReqResState.Error, ex)
                        )
                    )
                }
                CommonLogUtils.logE(TAG, "getRecentSearch ApiReqResState error:$ex")
                mRecentSearchDisposable = null
            }

            override fun onApiNext(value: CommunityRecentSearchResponse?) {
                //请求成功,加载,缓存最新数据
                mRecentSearchList.clear()
                mRecentSearchList.addAll(value?.recentSearchList ?: emptyList())
                viewModelScope.launch(Dispatchers.IO) {
                    mRecentSearchDao?.insert(
                        CommunityRecentSearchResponse(mRecentSearchList.toList())
                    )
                }
                mSearchUiState.update {
                    it.copy(
                        recentSearchState = it.recentSearchState.copy(
                            recentSearchList = value?.recentSearchList ?: emptyList(),
                            recentApiReqResState = ApiReqResState(ApiReqResState.ReqResState.GetValueFinish)
                        )
                    )
                }
                CommonLogUtils.logD(TAG, "getRecentSearch GetValueFinish,size=${value?.recentSearchList?.size}")
                mRecentSearchDisposable = null
            }
        })
    }

    /**
     * 获取最近搜索字符串列表
     * 首先加载本地缓存 后台请求成功进行更新
     */
    fun getRecentSearch() {
        viewModelScope.launch(Dispatchers.IO) {
            val communityRecentSearchList = mRecentSearchDao?.findAll()
            if (!communityRecentSearchList.isNullOrEmpty()) {
                //如果本地缓存不为空
                val recentSearchList = communityRecentSearchList[0].recentSearchList ?: emptyList()
                mRecentSearchList.clear()
                mRecentSearchList.addAll(recentSearchList)
                //显示本地数据库缓存
                mSearchUiState.update {
                    it.copy(
                        recentSearchState = it.recentSearchState.copy(recentSearchList = mRecentSearchList)
                    )
                }
            }
        }
        getCommunityRecentSearch()
    }

    /**
     * 替换搜索关键词的颜色
     */
    fun renderHtmlString(str: String?): String {
        val beReplacedStr = "color=\"blue\""
        val replaceStr = "color=\"${CommonUtils.getApp().getColor(R.color.scene_primary_color_highlight)}\""
        return str?.replace(beReplacedStr, replaceStr) ?: ""
    }

    /**
     * 切换Ui状态,确保只有一个ApiReqResState.ReqResState不为空
     * @param state 0最近搜索 1预搜索 2搜索
     */
    fun checkoutUiState(state: Int) {
        CommonLogUtils.logI(TAG, "checkoutUiState state=$state")
        val checkState = checkUiState()
        mSearchUiState.update {
            var searchState = it.searchState
            var preSearchState = it.preSearchState
            var recentSearchState = it.recentSearchState
            when (state) {
                SearchUiState.RECENT_SEARCH -> {
                    if (checkState == state && it.recentSearchState.recentApiReqResState.state == ApiReqResState.ReqResState.GetValueStart) {
                        return
                    }
                    //关键词为空,进入最近搜索状态,其他状态设置未激活
                    if (searchState.searchApiReqResState.state != null) {
                        searchState = searchState.copy(searchApiReqResState = ApiReqResState())
                    }

                    if (preSearchState.preApiReqResState.state != null) {
                        preSearchState = preSearchState.copy(preApiReqResState = ApiReqResState())
                    }
                    it.copy(
                        recentSearchState = it.recentSearchState.copy(
                            recentApiReqResState = ApiReqResState(
                                ApiReqResState.ReqResState.GetValueStart
                            )
                        ), preSearchState = preSearchState, searchState = searchState
                    )
                }

                SearchUiState.PRE_SEARCH -> {
                    if (checkState == state && it.preSearchState.preApiReqResState.state == ApiReqResState.ReqResState.GetValueStart) {
                        return
                    }
                    //切换到预搜索状态,其他状态设为未激活
                    if (searchState.searchApiReqResState.state != null) {
                        searchState = searchState.copy(searchApiReqResState = ApiReqResState())
                    }

                    if (recentSearchState.recentApiReqResState.state != null) {
                        recentSearchState = recentSearchState.copy(recentApiReqResState = ApiReqResState())
                    }
                    it.copy(
                        recentSearchState = recentSearchState, preSearchState = it.preSearchState.copy(
                            preApiReqResState = ApiReqResState(ApiReqResState.ReqResState.GetValueStart)
                        ), searchState = searchState
                    )
                }
                SearchUiState.SEARCH -> {
                    if (checkState == state && it.searchState.searchApiReqResState.state == ApiReqResState.ReqResState.GetValueStart) {
                        return
                    }
                    //切换到搜索状态,其他状态设为未激活
                    if (preSearchState.preApiReqResState.state != null) {
                        preSearchState = preSearchState.copy(preApiReqResState = ApiReqResState())
                    }
                    if (recentSearchState.recentApiReqResState.state != null) {
                        recentSearchState = recentSearchState.copy(recentApiReqResState = ApiReqResState())
                    }
                    it.copy(
                        recentSearchState = recentSearchState,
                        preSearchState = preSearchState,
                        searchState = it.searchState.copy(
                            searchApiReqResState = ApiReqResState(ApiReqResState.ReqResState.GetValueStart)
                        )
                    )
                }
                else -> {
                    CommonLogUtils.logE(TAG, "checkoutUiState 错误的SearchUiState:$state")
                    it
                }
            }
        }
    }

    /**
     * 判断当前uistate状态 前提保证ApiReqResState.state只有一个不为空
     * @return 0最近搜索 1预搜索 2搜索
     */
    fun checkUiState(): Int {
        var state: Int
        mSearchUiState.value.apply {
            state = if (searchState.searchApiReqResState.state != null) {
                SearchUiState.SEARCH
            } else if (preSearchState.preApiReqResState.state != null) {
                SearchUiState.PRE_SEARCH
            } else {
                SearchUiState.RECENT_SEARCH
            }
        }
        return state
    }

    fun clearRecentSearch() {
        CommunityService.clearCommunityRecentSearch().subscribe(object : RxSubscriber<BaseResponseBean>() {
            override fun onAddDisposable(d: Disposable?) {
                CommonLogUtils.logD(
                    TAG,
                    "clearRecentSearch GetValueStart，上次有请求${mClearRecentSearchDisposable != null}"
                )
                //若上一个请求未完成取消上一次请求
                mClearRecentSearchDisposable?.dispose()
                mClearRecentSearchDisposable = d
                //先假删除
                mSearchUiState.update {
                    it.copy(
                        recentSearchState = it.recentSearchState.copy(
                            recentSearchList = emptyList(),
                            recentApiReqResState = ApiReqResState(ApiReqResState.ReqResState.GetValueFinish)
                        )
                    )
                }
            }

            override fun onApiError(ex: ApiException?) {
                //请求失败
                mSearchUiState.update {
                    it.copy(
                        recentSearchState = it.recentSearchState.copy(
                            recentApiReqResState = ApiReqResState(ApiReqResState.ReqResState.Error, ex)
                        )
                    )
                }
                CommonLogUtils.logE(TAG, "clearRecentSearch ApiReqResState error:$ex")
                mClearRecentSearchDisposable = null
            }

            override fun onApiNext(value: BaseResponseBean?) {
                //删除本地数据
                mRecentSearchList.clear()
                viewModelScope.launch(Dispatchers.IO) {
                    mRecentSearchDao?.insert(
                        CommunityRecentSearchResponse(emptyList())
                    )
                }
                CommonLogUtils.logD(TAG, "clearRecentSearch GetValueFinish")
                mClearRecentSearchDisposable = null
            }
        })
    }

    /**删除请求*/
    override fun cancelDisposable() {
        super.cancelDisposable()
        cancelPreDisposable()
        cancelSearchDisposable()
        cancelRecentSearch()
    }

    fun cancelSearchDisposable() {
        mSearchDisposable?.let {
            it.takeUnless { it.isDisposed }?.dispose()
            mSearchDisposable = null
        }
    }

    fun cancelPreDisposable() {
        mPreDisposable?.let {
            it.takeUnless { it.isDisposed }?.dispose()
            mPreDisposable = null
        }
    }

    fun cancelRecentSearch() {
        mRecentSearchDisposable?.let {
            it.takeUnless { it.isDisposed }?.dispose()
            mRecentSearchDisposable = null
        }
        mClearRecentSearchDisposable?.let {
            CommonLogUtils.logD(TAG, "clearRecentSearch cancelRecentSearch")
            it.takeUnless { it.isDisposed }?.dispose()
            mClearRecentSearchDisposable = null
        }
    }
}