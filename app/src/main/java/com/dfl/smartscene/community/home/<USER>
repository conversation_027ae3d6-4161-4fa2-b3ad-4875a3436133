package com.dfl.smartscene.community.home

import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.util.CommonToastUtils
import com.dfl.android.common.util.TaskExecutor
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.apiweb.utils.ApiException
import com.dfl.smartscene.apiweb.utils.ApiReqResState
import com.dfl.smartscene.apiweb.utils.ExceptionEngine
import com.dfl.smartscene.bean.community.datasource.CommunityDataSource
import com.dfl.smartscene.bean.community.datasource.CommunityRepository
import com.dfl.smartscene.bean.community.reponse.CommunityListResponse
import com.dfl.smartscene.bean.community.reponse.CommunitySceneInfo
import com.dfl.smartscene.bean.community.request.CommunityTabType
import com.dfl.smartscene.bean.community.request.SmartRefreshState
import com.dfl.smartscene.bean.mechanism.BaseDataSource.DataSourceType
import com.dfl.smartscene.bean.mechanism.BaseRepository
import com.dfl.smartscene.bean.mechanism.ILoadDataCallBack
import com.dfl.smartscene.room.DbManager
import com.dfl.smartscene.room.helper.DBHelper
import com.dfl.smartscene.room.helper.OnDaoFindsListener
import com.dfl.smartscene.room.helper.OnDaoUpdateOrDeleteListener
import java.lang.ref.WeakReference

/**
 *Created by 钟文祥 on 2023/11/18.
 *Describer: 社区场景 viewModel
 */
class CommunityViewModel : CommunityBaseViewModel() {
    private val TAG = GlobalConstant.GLOBAL_TAG.plus("CommunityViewModel")

    ///==========存储库===========
    private var mRvRepository: CommunityRepository<CommunityListResponse>? = null

    ///==========liveData==========
    //列表数据
    private val mRvDataListResponse = MutableLiveData<CommunityListResponse>()

    //列表数据请求状态
    private val mRvDataApiReqResState = MutableLiveData<ApiReqResState>()


    /** 主页+fragment 当前的tab类型  需要在loadData之前设置*/
    var mCurTabType: CommunityTabType? = null

    /**被选中的tab类型*/
    var mSelectTabType: CommunityTabType = CommunityTabType.Official

    /**当前请求第几页*/
    private var mPageNum: Int = 1

    /** 刷新控件获取数据的方式  */
    var mSmartRefreshState = SmartRefreshState.None

    /**列表回调*/
    private var mRvLoadDataCallBack: RvLoadDataCallBack? = null

    /**是否需要第一个卡片闪烁*/
    private var isShowLottieAnimation = false

    /**数据为空时，数据来源是什么，用在当来自远程时，ui停止闪烁*/
    var mNullDataType: DataSourceType = DataSourceType.Cached


    fun getRvDataList(): MutableLiveData<CommunityListResponse> {
        return mRvDataListResponse
    }

    fun getApiReqResState(): MutableLiveData<ApiReqResState> {
        return mRvDataApiReqResState
    }


    /**第一次加载*/
    fun firstLoadRvData() {
        mPageNum = 1
        mSmartRefreshState = SmartRefreshState.None
        loadRvData(BaseRepository.LoadType.Refresh)
    }

    /**刷新加载*/
    fun refreshLoadRvData() {
        mPageNum = 1
        mSmartRefreshState = SmartRefreshState.Refresh
        loadRvData(BaseRepository.LoadType.RefreshOnlyRemote)
    }

    /**刷新加载,需要闪烁*/
    fun refreshLoadRvDataAndShowLottieAnimation() {
        isShowLottieAnimation = true
        refreshLoadRvData()
    }

    /**加载更多*/
    fun loadMoreRvData() {
        mPageNum++
        mSmartRefreshState = SmartRefreshState.LoadMore
        loadRvData(BaseRepository.LoadType.LoadMore)
    }

    private fun loadRvData(
        loadType: BaseRepository.LoadType
    ) {
        CommonLogUtils.logA(TAG, "开始网络请求 - loadType: ${loadType}, tabType: ${mCurTabType?.getName()}, pageNum: $mPageNum")
        TaskExecutor.io {
            //1 设置数据源
            mRvRepository = CommunityRepository.getInstance()
            CommonLogUtils.logA(TAG, "网络请求阶段1 - 数据源设置完成")
            
            //2 设置加载方式
            mRvRepository?.setLoadType(mCurTabType, loadType)
            CommonLogUtils.logA(TAG, "网络请求阶段2 - 加载方式设置完成: $loadType")
            
            //3 设置请求参数
            mRvRepository?.setDataSource(
                mCurTabType, CommunityDataSource.init(mCurTabType, mPageNum)
            )
            CommonLogUtils.logA(TAG, "网络请求阶段3 - 请求参数设置完成: tabType=${mCurTabType?.getName()}, pageNum=$mPageNum")

            if (mRvLoadDataCallBack == null) mRvLoadDataCallBack = RvLoadDataCallBack(this)
            CommonLogUtils.logA(TAG, "网络请求阶段4 - 开始执行数据加载")
            mRvRepository?.loadData(mCurTabType, mRvLoadDataCallBack)
        }
    }

    //inner class非静态内部类，包含一个指向外部类的对象的引用,可以访问外部类成员属性和成员函数.
    //其余内部类class为静态内部类
    class RvLoadDataCallBack(
        communityViewModel: CommunityViewModel
    ) : ILoadDataCallBack<CommunityListResponse> {

        /** 防止本地有数据 但没网络报错无数据时，进入错误页面 ，false为没数据显示*/
        private var mTempCurIsGetFallData: Boolean = false
        private var reference: WeakReference<CommunityViewModel> = WeakReference<CommunityViewModel>(communityViewModel)

        override fun onLoadRemoteStart() {
            reference.get()?.let {
                CommonLogUtils.logA(it.TAG, "网络请求开始 - onLoadRemoteStart, tabType: ${it.mCurTabType?.getName()}")
                if (!mTempCurIsGetFallData) {
                    CommonLogUtils.logA(it.TAG, "显示加载视图")
                    //setValue只可以在主线程中调用。postValue可以在主线程或者子线程中调用
                    //https://www.jianshu.com/p/f3be82e2f70d
                    it.mRvDataApiReqResState.postValue(ApiReqResState(ApiReqResState.ReqResState.GetValueStart))
                }
            }
        }

        override fun onDataLoaded(
            values: CommunityListResponse?, type: DataSourceType
        ) {
            reference.get()?.let {
                CommonLogUtils.logD(it.TAG, "网络请求成功 - onDataLoaded, 数据来源: ${type.valueStr}, tabType: ${it.mCurTabType?.getName()}")
                CommonLogUtils.logD(it.TAG, "请求成功数据详情 - 数据条数: ${values?.rows?.size ?: 0}, 是否有下一页: ${values?.hasNextPage() ?: false}")
                
                // 打印前3条数据的详细信息（如果存在）
                values?.rows?.take(3)?.forEachIndexed { index, item ->
                    CommonLogUtils.logD(it.TAG, "数据项${index + 1} - ID: ${item.communityScenarioId}, 标题: ${item.scenarioInfoDa?.scenarioName ?: "无标题"}, 点赞数: ${item.likes}, 下载数: ${item.downloads}")
                }
                
                mTempCurIsGetFallData = true
                //是否需要闪烁
                if (it.isShowLottieAnimation) {
                    if (values?.rows?.size!! >= 1) {
                        values.rows.get(0).isShowLottieAnimation = true
                        CommonLogUtils.logD(it.TAG, "设置第一个项目闪烁动画")
                    }
                }
                it.mRvDataListResponse.postValue(values)
                it.isShowLottieAnimation = false

                CommonLogUtils.logD(
                    it.TAG, "${it.mCurTabType?.getName()} 有数据${values?.rows?.size}个 来自  ${type.valueStr}"
                )
                if (values?.hasNextPage() == false) {
                    CommonLogUtils.logD(it.TAG, "tab页:${it.mCurTabType?.getName()} 没有更多了")
                }
                if (values == null) return
                //轮播图没数据或异常，来自远程，整页显示异常页
                if (it.mCurTabType == CommunityTabType.Banner && values.rows?.size == 0 && (type == DataSourceType.Remote || type == DataSourceType.Local)) {
                    CommonLogUtils.logD(it.TAG, "轮播图无数据，显示异常页面")
                    it.mRvDataApiReqResState.postValue(
                        ApiReqResState(
                            ApiReqResState.ReqResState.Error, ApiException(null, ExceptionEngine.DATA_SIZE_0)
                        )
                    )
                } else {
                    CommonLogUtils.logD(it.TAG, "数据加载完成，隐藏加载视图")
                    it.mRvDataApiReqResState.postValue(
                        ApiReqResState(
                            ApiReqResState.ReqResState.GetValueFinish, ApiException(null, ExceptionEngine.UNKNOWN)
                        )
                    )
                }
            }
        }

        override fun onDataIsNullOrError(type: DataSourceType) {
            reference.get()?.let {
                CommonLogUtils.logD(it.TAG, "网络请求无数据 - onDataIsNullOrError, 数据来源: ${type.valueStr}, tabType: ${it.mCurTabType?.getName()}")
                it.mNullDataType = type
                it.mRvDataListResponse.postValue(null)
                it.isShowLottieAnimation = false

                CommonLogUtils.logD(it.TAG, "${it.mCurTabType?.getName()} 无数据 来自 ${type.valueStr}")

                // 轮播图没数据或异常，来自远程，整页显示异常页
                if (it.mCurTabType == CommunityTabType.Banner && type == DataSourceType.Remote) {
                    CommonLogUtils.logD(it.TAG, "轮播图远程无数据，显示错误页面")
                    it.mRvDataApiReqResState.postValue(
                        ApiReqResState(
                            ApiReqResState.ReqResState.Error
                        )
                    )
                }
            }
        }

        override fun onApiError(ex: ApiException) {
            reference.get()?.let {
                CommonLogUtils.logE(it.TAG, "网络请求失败 - onApiError, tabType: ${it.mCurTabType?.getName()}")
                CommonLogUtils.logE(it.TAG, "错误详情 - HTTP状态码: ${ex.getHttpCode()}, 错误信息: ${ex.message}, Toast消息: ${ex.getToastMsg()}")
                CommonLogUtils.logD(
                    it.TAG,
                    "错误：" + ex.getHttpCode() + " " + ex.message + " " + !mTempCurIsGetFallData + " ex.toastMsg:" + ex.getToastMsg()
                )
                it.isShowLottieAnimation = false

                if (!mTempCurIsGetFallData) { //本地没数据，网络出错，显示错误界面
                    CommonLogUtils.logD(it.TAG, "本地无数据且网络出错，显示错误界面")
                    it.mRvDataApiReqResState.postValue(
                        ApiReqResState(ApiReqResState.ReqResState.Error, ex)
                    )
                } else { //本地有数据 ，但网络出错，此时不需显示错误界面
                    CommonLogUtils.logD(it.TAG, "本地有数据但网络出错，显示Toast提示")
                    ex.getToastMsg()?.let {
                        CommonToastUtils.show(ex.getToastMsg())
                    }
                    if (it.mCurTabType == CommunityTabType.Banner) {
                        it.mRvDataApiReqResState.postValue(
                            ApiReqResState(ApiReqResState.ReqResState.Error, ex)
                        )
                    } else {
                        it.mRvDataApiReqResState.postValue(
                            ApiReqResState(ApiReqResState.ReqResState.Error)
                        )
                    }
                }
            }
        }
    }


    //因为关注改变 需要更改数据库
    fun updateLocalByFocusLikeDown(updateItem: CommunitySceneInfo, type: String) {
        val dao = DbManager.getDBMaster()?.CommunityListResponseDao()

        DBHelper.daoFinds(object : OnDaoFindsListener<List<CommunityListResponse>?> {
            override fun call(): List<CommunityListResponse>? {
                return dao?.findAll()
            }

            override fun onSuccess(values: List<CommunityListResponse>?) {
                values?.forEachIndexed { index, communityListResponse ->
                    communityListResponse.rows.forEachIndexed { index2, communitySceneInfo ->

                        when (type) {
                            "Focus" -> {
                                if (communitySceneInfo.uuid == updateItem.uuid) {
                                    communitySceneInfo.userSubscribe = updateItem.userSubscribe
                                }
                            }

                            "Like" -> {
                                if (communitySceneInfo.communityScenarioId == updateItem.communityScenarioId) {
                                    communitySceneInfo.isLike = updateItem.isLike
                                    communitySceneInfo.likes = updateItem.likes

                                }
                            }

                            "Down" -> {
                                if (communitySceneInfo.communityScenarioId == updateItem.communityScenarioId) {
                                    communitySceneInfo.downloads = updateItem.downloads
                                }
                            }
                        }
                    }
                }

                DBHelper.doUpdateOrDelete(object : OnDaoUpdateOrDeleteListener {
                    override fun call(): Int? {
                        return dao?.update(values)
                    }

                    override fun onSuccess(integer: Int) {
                        CommonLogUtils.logD(
                            "Community_CommunityFragment", "${type} 的数据库更新成功：" + integer
                        )
                    }

                    override fun onError(e: Throwable?) {
                    }
                })
            }

            override fun onError(e: Throwable?) {

            }
        })
    }

    override fun cancelDisposable() {
        super.cancelDisposable()
        mRvRepository?.getDataSource(mCurTabType)?.let {
            it as CommunityDataSource
            it.cancelDisposable()
        }
    }
}