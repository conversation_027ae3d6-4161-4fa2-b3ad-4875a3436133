package com.dfl.smartscene.community.home;

import android.annotation.SuppressLint;
import android.content.res.Configuration;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.dfl.android.common.base.BaseVBFragment;
import com.dfl.android.common.global.GlobalConstant;
import com.dfl.android.common.global.GlobalLiveEventConstants;
import com.dfl.android.common.util.CommonToastUtils;
import com.dfl.android.commonlib.log.CommonLogUtils;
import com.dfl.smartscene.R;
import com.dfl.smartscene.bean.community.reponse.CommunitySceneInfo;
import com.dfl.smartscene.bean.community.request.CommunityFocusRequest;
import com.dfl.smartscene.bean.community.request.CommunityTabType;
import com.dfl.smartscene.bean.community.request.SmartRefreshState;
import com.dfl.smartscene.bean.mechanism.BaseDataSource;
import com.dfl.smartscene.databinding.SceneFragmentCommunityTabBinding;
import com.dfl.smartscene.rv.EquallySpaceDecoration;
import com.dfl.smartscene.ui.main.discover.detail.SceneDetailActivity;
import com.dfl.smartscene.util.UIUtils;
import com.jeremyliao.liveeventbus.LiveEventBus;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener;

import java.lang.ref.WeakReference;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.autosize.utils.AutoSizeUtils;

/**
 * Created by 钟文祥 on 2023/11/17.
 * Describer: 社区场景 tab 的fragment
 */
public class CommunityTabFragment extends BaseVBFragment<CommunityViewModel,
        SceneFragmentCommunityTabBinding> {

    private static final String TAG = GlobalConstant.GLOBAL_TAG + "CommunityTabFragment";
    private static final String EXTRA_COMMUNITY_TAB = "curCommunityTabType";

    public CommunityAdapter mAdapter;

    private Disposable showLottieDisposable = null;//闪烁的延时处理
    private Disposable noMoreDataDisposable = null;//无更多数据的延时处理

    public static CommunityTabFragment newInstance(CommunityTabType mCurTabType) {
        CommunityTabFragment fragment = new CommunityTabFragment();
        Bundle args = new Bundle();
        args.putInt(EXTRA_COMMUNITY_TAB, mCurTabType.getValue());
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {
        initRV();
        initRefreshLayout();
    }

    @Override
    public void createObserver() {
        initObserveRvDataList();
        initObserveApiReqResState();
        initObserverFocusApiReqResState();
        //加载数据
        if (getArguments() != null) {
            mViewModel.setMCurTabType(CommunityTabType.values()[getArguments().getInt(EXTRA_COMMUNITY_TAB)]);
        }
        mViewModel.firstLoadRvData();
        CommonLogUtils.logI(TAG, "onViewCreated: " + mViewModel.getMCurTabType());
    }


    @SuppressLint("CheckResult")
    private void initObserveRvDataList() {
        mViewModel.getRvDataList().observe(this, communityListResponse -> {
            if (communityListResponse == null) {
                if (mAdapter.getData() != null
                        && mAdapter.getData().size() > 0
                        && mAdapter.getData().get(0).getScenarioInfoDa() == null
                        && mViewModel.getMNullDataType() == BaseDataSource.DataSourceType.Remote) {
                    CommonLogUtils.logE(TAG, "CommunityViewModel社区列表数据有异常,需要15个缺省图来占位");
                    mAdapter.setList(CommunitySceneInfo.Companion.getErrorNullList());
                }
                return;
            }
            if (mViewModel.getMSmartRefreshState() == SmartRefreshState.Refresh ||
                    mViewModel.getMSmartRefreshState() == SmartRefreshState.None) {
                mAdapter.setList(communityListResponse.getRows());

                //闪烁
                if (communityListResponse.getRows().size() >= 1 && communityListResponse.getRows().get(0)
                        .isShowLottieAnimation()) {
                    getMViewBind().rvCommunity.scrollToPosition(0);
                    showLottieDisposable = Observable.just(1).delay(4, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread())
                            .subscribe(integer -> {

                                CommunitySceneInfo item0 = mAdapter.getData().get(0);
                                item0.setShowLottieAnimation(false);
                                mAdapter.setData(0, item0);
                            });
                }
            } else if (mViewModel.getMSmartRefreshState() == SmartRefreshState.LoadMore) {
                if (communityListResponse.getRows() != null && communityListResponse.getRows().size() > 0) {
                    for (int i = mAdapter.getData().size() - 1; i >= 0; i--) {
                        if (mAdapter.getData().get(i).getScenarioInfoDa() == null) {
                            mAdapter.getData().remove(i);
                        }
                    }
                }
                mAdapter.addData(communityListResponse.getRows());
            }

            getMViewBind().slUpdate.finishRefresh();
            getMViewBind().slUpdate.finishLoadMore();
            if (communityListResponse.hasNextPage()) {
                getMViewBind().slUpdate.setNoMoreData(false);//恢复没有更多数据的原始状态
            } else {
                noMoreDataDisposable = Observable.just(1).delay(1, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread())
                        .subscribe(integer -> {
                            getMViewBind().slUpdate.setNoMoreData(true); //显示没有更多
                        });
            }

            LiveEventBus.get(GlobalLiveEventConstants.KEY_COMMUNITY_GET_VALUE_CALLBACK,
                    SmartRefreshState.class).post(mViewModel.getMSmartRefreshState());
        });
    }

    private void initObserveApiReqResState() {
        mViewModel.getApiReqResState().observe(this, apiReqResState -> {
            switch (apiReqResState.getState()) {
                case GetValueStart:
                    break;
                case GetValueFinish:
                case Error:
                    if (apiReqResState.getEx() != null && !TextUtils.isEmpty(apiReqResState.getEx().toastMsg)) {
                        CommonToastUtils.show(R.string.scene_toast_common_web_server_error);
                    }
                    getMViewBind().slUpdate.finishRefresh(false);
                    getMViewBind().slUpdate.finishLoadMore(false);
                    break;
            }
        });
    }

    //关注回调
    private void initObserverFocusApiReqResState() {
        mViewModel.getMFocusApiReqResState().observe(this, apiReqResState -> {
            switch (apiReqResState.getState()) {
                case GetValueStart:
                case GetValueFinish:
                    break;
                case Error:
                    CommonToastUtils.show((String) apiReqResState.getMap().get(GlobalLiveEventConstants.KEY_COMMUNITY_FOCUS_SUCCESS_UPDATE_UI));
                    break;
            }
        });
    }

    private void initRV() {
        UIUtils.initRecyclerView(getActivity(), getMViewBind().rvCommunity,
                new GridLayoutManager(getActivity(), 3), false);

        mAdapter = new CommunityAdapter((position, object) -> {
            CommunitySceneInfo item = mAdapter.getData().get(position);
            switch (object) {
                case Subscribe: {
                    mViewModel.focusOrNoFocus(item, getChildFragmentManager(), CommunityFocusRequest.SUBSCRIBED);
                    break;
                }
                case UnSubscribe: {
                    mViewModel.focusOrNoFocus(item, getChildFragmentManager(), CommunityFocusRequest.UNSUBSCRIBED);
                    break;
                }
                case Likes: {
                    mViewModel.likeOrNoLike(item, getChildFragmentManager());
                    break;
                }
                case Down: {
                    mViewModel.downloadOrNo(item, getChildFragmentManager(), requireActivity(), true);
                    break;
                }
            }
        });
        getMViewBind().rvCommunity.setAdapter(mAdapter);
        getMViewBind().rvCommunity.addItemDecoration(new EquallySpaceDecoration(
                AutoSizeUtils.dp2px(requireContext(), 52f),
                AutoSizeUtils.dp2px(requireContext(), 52f),
                true));
        getMViewBind().rvCommunity.addOnScrollListener(new MyOnScrollListener(CommunityTabFragment.this));
        mAdapter.setOnItemClickListener((adapter, view, position) -> {
            if (mAdapter.getData().get(position).isShowLottieAnimation()) return;
            if (mAdapter.getData().get(position).getScenarioInfoDa() == null) return;
            SceneDetailActivity.Companion.startCommunitySceneDetailActivity(requireActivity(),
                    mAdapter.getData().get(position));
        });

        //加载列表时，有闪烁效果
        mAdapter.setList(CommunitySceneInfo.Companion.getLottieList());
    }

    private void initRefreshLayout() {
        UIUtils.initRefreshLayout(getMViewBind());
        //        //设置footer没有更多不跟随内容 ，有回弹效果隐藏 ，原本设置
        //        SmartRefreshLayout srl = mViewBind.getRoot().findViewById(R.id.sl_update);
        //        srl.setEnableFooterFollowWhenNoMoreData(false);

        getMViewBind().slUpdate.setEnableRefresh(false);
        getMViewBind().slUpdate.setOnLoadMoreListener(new MyOnRefreshLoadMoreListener(CommunityTabFragment.this));
    }

    @Override
    public void onStart() {
        super.onStart();
        CommonLogUtils.logI(TAG, "onStart: " + mViewModel.getMCurTabType());
    }

    @Override
    public void onResume() {
        super.onResume();
        CommonLogUtils.logI(TAG, "onResume: " + mViewModel.getMCurTabType());
    }

    @Override
    public void onPause() {
        super.onPause();
        CommonLogUtils.logI(TAG, "onPause: " + mViewModel.getMCurTabType());
    }

    @Override
    public void onStop() {
        super.onStop();
        CommonLogUtils.logI(TAG, "onStop: " + mViewModel.getMCurTabType());
    }

    @Override
    public void onDestroy() {
        if (showLottieDisposable != null && !showLottieDisposable.isDisposed()) {
            showLottieDisposable.dispose();
        }
        showLottieDisposable = null;
        if (noMoreDataDisposable != null && !noMoreDataDisposable.isDisposed()) {
            noMoreDataDisposable.dispose();
        }
        noMoreDataDisposable = null;
        mViewModel.cancelDisposable();
        CommonLogUtils.logI(TAG, "onDestroy: " + mViewModel.getMCurTabType());
        super.onDestroy();
    }

    /**
     * 主题切换
     */
    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        //        if (mAdapter != null) {
        //            mAdapter.notifyDataSetChanged();
        //        }
        //手动切换进度条png
        UIUtils.setSmartRefreshOnConfigurationChanged(null, getMViewBind().classicsFooter);

    }

    private static class MyOnScrollListener extends RecyclerView.OnScrollListener {
        private WeakReference<CommunityTabFragment> reference;

        public MyOnScrollListener(CommunityTabFragment fragment) {
            reference = new WeakReference<>(fragment);
        }

        @Override
        public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            if (reference == null || reference.get() == null) return;
            if (newState == 0) { //停止
                Glide.with(reference.get()).resumeRequests();
            } else { //滑动
                Glide.with(reference.get()).pauseRequests();
            }
        }
    }

    private static class MyOnRefreshLoadMoreListener implements OnRefreshLoadMoreListener {
        private WeakReference<CommunityTabFragment> reference;

        public MyOnRefreshLoadMoreListener(CommunityTabFragment fragment) {
            reference = new WeakReference<>(fragment);
        }

        @Override
        public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
            if (reference == null || reference.get() == null) return;
            reference.get().getMViewModel().loadMoreRvData();
        }

        @Override
        public void onRefresh(@NonNull RefreshLayout refreshLayout) {

        }
    }

}