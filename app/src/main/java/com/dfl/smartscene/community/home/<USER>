package com.dfl.smartscene.community.home

import android.content.Context
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.common.global.GlobalConfig
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.util.CommonToastUtils
import com.dfl.android.common.util.ObjectUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.apiweb.service.CommunityService
import com.dfl.smartscene.apiweb.utils.ApiException
import com.dfl.smartscene.apiweb.utils.ApiReqResState
import com.dfl.smartscene.apiweb.utils.RxSubscriber
import com.dfl.smartscene.bean.apibase.BaseResponseBean
import com.dfl.smartscene.bean.community.reponse.CommunitySceneInfo
import com.dfl.smartscene.bean.community.request.CommunityFocusRequest
import com.dfl.smartscene.bean.main.MySceneBean
import com.dfl.smartscene.bean.main.ScenarioBean
import com.dfl.smartscene.communication.UserCenterManager
import com.dfl.smartscene.customapi.SoundManager
import com.dfl.smartscene.soa.SceneManager
import com.dfl.smartscene.ui.edit.SceneNewEditActivity
import com.dfl.smartscene.ui.edit.SceneNewEditEnterType
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.util.NetWorkUtils
import com.dfl.smartscene.util.TrackUtils
import com.iauto.scenarioadapter.ScenarioInfo
import com.jeremyliao.liveeventbus.LiveEventBus
import io.reactivex.disposables.Disposable

/**
 *Created by 钟文祥 on 2023/12/6.
 *Describer: 适用于社区场景的 关注、点赞、下载
 */
open class CommunityBaseViewModel : BaseViewModel() {

    private val TAG = GlobalConstant.GLOBAL_TAG + "CommunityBaseViewModel"

    //关注相关
    var mFocusApiReqResState = MutableLiveData<ApiReqResState>()
    protected var mFocusDisposable: Disposable? = null

    //点赞 --只给场景详情用
    var mLikeApiReqResState = MutableLiveData<ApiReqResState>()
    protected var mLikeDisposable: Disposable? = null

    //下载
    var mDownLoadApiReqResState = MutableLiveData<ApiReqResState>()
    protected var mDownLoadDisposable: Disposable? = null

    /**
     * 关注与取消关注 判断用户登录状态并打开确认弹框
     * @param item 当前操作数据
     * @param fragmentManager 跳转登录确认对话框所需
     * @param userSubscribe 记录请求的是关注还是取消关注，不使用item自反，防止重复请求导致错误自反
     */
    fun focusOrNoFocus(
        item: CommunitySceneInfo?, fragmentManager: FragmentManager, userSubscribe: Int
    ) {
        UserCenterManager.checkUserLoginStatusWithDialog(fragmentManager)?.let {
            item?.uuid?.let {
                CommunityService.focusOrNoFocus(it, userSubscribe == CommunityFocusRequest.SUBSCRIBED)
                    .subscribe(object : RxSubscriber<BaseResponseBean>() {
                        override fun onAddDisposable(d: Disposable?) {
                            mFocusDisposable = d
                            mFocusApiReqResState.value = ApiReqResState(ApiReqResState.ReqResState.GetValueStart)
                        }

                        override fun onApiNext(value: BaseResponseBean) {
                            //请求响应已经被消耗情况退出，防止同类型请求导致界面多次变化
                            if (mFocusDisposable == null) return
                            mFocusDisposable = null
                            if (value.isSuccess()) {
                                item.userSubscribe = userSubscribe
                                //通知带轮播图的主页去更新所有UI
                                LiveEventBus.get(
                                    GlobalLiveEventConstants.KEY_COMMUNITY_FOCUS_SUCCESS_UPDATE_UI,
                                    CommunitySceneInfo::class.java
                                ).post(item)
                                //本页ui更新
                                mFocusApiReqResState.value = ApiReqResState(ApiReqResState.ReqResState.GetValueFinish)
                                CommonLogUtils.logD(
                                    TAG, "focusOrNoFocus方法成功 更新后userSubscribe：${item.userSubscribe}"
                                )
                            } else {
                                CommonLogUtils.logE(TAG, "focusOrNoFocus方法失败 result：" + value.result)
                                mFocusApiReqResState.value = ApiReqResState(ApiReqResState.ReqResState.Error).apply {
                                    map[GlobalLiveEventConstants.KEY_COMMUNITY_FOCUS_SUCCESS_UPDATE_UI] =
                                        CommonUtils.getString(
                                            R.string.scene_toast_web_exception_unconnect_or_timeout
                                        )
                                }
                            }
                        }

                        override fun onApiError(ex: ApiException?) {
                            //请求响应已经被消耗情况退出，防止同类型请求导致界面多次变化
                            if (mFocusDisposable == null) return
                            mFocusDisposable = null
                            CommonLogUtils.logE(TAG, "focusOrNoFocus方法失败：" + ex?.message)
                            mFocusApiReqResState.value = ApiReqResState(ApiReqResState.ReqResState.Error, ex).apply {
                                map[GlobalLiveEventConstants.KEY_COMMUNITY_FOCUS_SUCCESS_UPDATE_UI] =
                                    CommonUtils.getString(
                                        R.string.scene_toast_web_exception_unconnect_or_timeout
                                    )
                            }
                        }
                    })
            }
        }
    }

    /**点赞或者取消点赞*/
    fun likeOrNoLike(
        communitySceneInfo: CommunitySceneInfo?, fragmentManager: FragmentManager
    ) {
        if (!NetWorkUtils.isConnectNetWork()) { //无网时，假点赞与假取消
            CommonToastUtils.show(R.string.scene_toast_web_exception_device_no_network)
            communitySceneInfo?.let {
                it.isLike = !it.isLike!!
                //通知带轮播图的主页去更新所有UI
                LiveEventBus.get(
                    GlobalLiveEventConstants.KEY_COMMUNITY_LIKE_SUCCESS_UPDATE_UI, CommunitySceneInfo::class.java
                ).post(it)

                //本页ui更新
                mLikeApiReqResState.value = ApiReqResState(ApiReqResState.ReqResState.GetValueFinish).apply {
                    map[GlobalLiveEventConstants.KEY_COMMUNITY_LIKE_SUCCESS_UPDATE_UI] =
                        if (it.isLike!!) "点赞成功" else "取消点赞成功"
                }
            }
            return
        }
        UserCenterManager.checkUserLoginStatusWithDialog(fragmentManager)?.let {
            communitySceneInfo?.let {
                TrackUtils.clickLike(it.scenarioInfoDa, !it.isLike!!, it.communityScenarioId) //埋点

                CommunityService.likeOrNoLike(it.communityScenarioId!!, !it.isLike!!)
                    .subscribe(object : RxSubscriber<BaseResponseBean>() {
                        override fun onAddDisposable(d: Disposable?) {
                            mLikeDisposable = d
                            mLikeApiReqResState.value = ApiReqResState(ApiReqResState.ReqResState.GetValueStart)
                        }

                        override fun onApiNext(value: BaseResponseBean) {
                            //请求响应已经被消耗情况退出，防止同类型请求导致界面多次变化
                            if (mLikeDisposable == null) return
                            mLikeDisposable = null
                            if (value.isSuccess()) {

                                it.isLike = !it.isLike!!
                                it.likes = if (it.isLike!!) it.likes!! + 1 else it.likes!! - 1
                                if (it.likes!! < 0) {
                                    it.likes = 0
                                }

                                //通知带轮播图的主页去更新所有UI
                                LiveEventBus.get(
                                    GlobalLiveEventConstants.KEY_COMMUNITY_LIKE_SUCCESS_UPDATE_UI,
                                    CommunitySceneInfo::class.java
                                ).post(it)

                                //本页ui更新
                                mLikeApiReqResState.value =
                                    ApiReqResState(ApiReqResState.ReqResState.GetValueFinish).apply {
                                        map[GlobalLiveEventConstants.KEY_COMMUNITY_LIKE_SUCCESS_UPDATE_UI] =
                                            if (it.isLike!!) "点赞成功" else "取消点赞成功"
                                    }
                            } else {
                                CommonLogUtils.logE(TAG, "likeOrNoLike方法失败 result：" + value.result)
                                mLikeApiReqResState.value = ApiReqResState(ApiReqResState.ReqResState.Error).apply {
                                    map[GlobalLiveEventConstants.KEY_COMMUNITY_LIKE_SUCCESS_UPDATE_UI] =
                                        if (!it.isLike!!) "点赞失败" else "取消点赞失败"
                                    CommonToastUtils.show(R.string.scene_toast_common_web_server_error)
                                }
                            }
                        }

                        override fun onApiError(ex: ApiException?) {
                            //请求响应已经被消耗情况退出，防止同类型请求导致界面多次变化
                            if (mLikeDisposable == null) return
                            mLikeDisposable = null
                            CommonLogUtils.logE(TAG, "likeOrNoLike方法失败：" + ex?.message)
                            mLikeApiReqResState.value = ApiReqResState(ApiReqResState.ReqResState.Error, ex).apply {
                                map[GlobalLiveEventConstants.KEY_COMMUNITY_LIKE_SUCCESS_UPDATE_UI] =
                                    if (!it.isLike!!) "点赞失败" else "取消点赞失败"
                                CommonToastUtils.show(R.string.scene_toast_common_web_server_error)
                            }
                        }
                    })
            }
        }
    }

    /**下载或者取消下载，现在写死是下载*/
    fun downloadOrNo(
        communitySceneInfo: CommunitySceneInfo?,
        fragmentManager: FragmentManager,
        context: Context,
        isDownLoad: Boolean = true
    ) {
        if (SceneManager.getSceneInfoList().size >= GlobalConfig.SCENE_COUNT_MAX) {
            CommonToastUtils.show(R.string.scene_text_me_scene_limit)
            SoundManager.playSoundEffect(SoundManager.SoundType.TOAST)
            return
        }
        if (!NetWorkUtils.isConnectNetWork()) {
            CommonToastUtils.show(CommonUtils.getString(R.string.scene_toast_web_exception_device_no_network))
            return
        }

        UserCenterManager.checkUserLoginStatusWithDialog(fragmentManager)?.let {

            communitySceneInfo?.let {
                TrackUtils.clickDownLoad(it.scenarioInfoDa, it.communityScenarioId) //埋点

                //点击下载直接进入编辑界面，并请求下载方法
                CommunityService.downloadOrNo(it.communityScenarioId!!, isDownLoad)
                    .subscribe(object : RxSubscriber<BaseResponseBean>() {
                        override fun onAddDisposable(d: Disposable?) {
                            mDownLoadDisposable = d
                            mDownLoadApiReqResState.value = ApiReqResState(ApiReqResState.ReqResState.GetValueStart)
                        }

                        override fun onApiNext(value: BaseResponseBean) {
                            //请求响应已经被消耗情况退出，防止同类型请求导致界面多次变化
                            if (mDownLoadDisposable == null) return
                            mDownLoadDisposable = null
                            if (value.isSuccess()) {
                                it.downloads = if (isDownLoad) it.downloads!! + 1 else it.downloads!! - 1

                                //通知带轮播图的主页去更新所有UI，和通知编辑页已经下载成功隐藏进度条
                                LiveEventBus.get(
                                    GlobalLiveEventConstants.KEY_COMMUNITY_DOWNLOAD_SUCCESS_UPDATE_UI,
                                    CommunitySceneInfo::class.java
                                ).post(it)

                                //本页ui更新
                                mDownLoadApiReqResState.value =
                                    ApiReqResState(ApiReqResState.ReqResState.GetValueFinish)
                                //                                CommonToastUtils.show("场景下载成功")
                            } else {
                                downloadOrNoFail(null)
                            }
                        }

                        override fun onApiError(ex: ApiException?) {
                            //请求响应已经被消耗情况退出，防止同类型请求导致界面多次变化
                            if (mDownLoadDisposable == null) return
                            mDownLoadDisposable = null
                            downloadOrNoFail(ex)
                        }
                    })
                //下载后跳转到编辑界面
                gotoSceneNewEditActivity(it, context)
            }
        }
    }

    /**下载失败事件*/
    private fun downloadOrNoFail(ex: ApiException?) {
        //下载失败发送个null
        LiveEventBus.get(
            GlobalLiveEventConstants.KEY_COMMUNITY_DOWNLOAD_FAIL, CommunitySceneInfo::class.java
        ).post(null)

        mDownLoadApiReqResState.value = ApiReqResState(ApiReqResState.ReqResState.Error, ex)
        // CommonToastUtils.show("场景下载失败")
    }

    /**跳转到编辑界面*/
    private fun gotoSceneNewEditActivity(
        communitySceneInfo: CommunitySceneInfo?,
        context: Context,
    ) {
        communitySceneInfo?.let {
            //点击下载直接进入编辑界面，除去不可用的能力
            val scenarioBean = ScenarioBean(
                scenarioInfo = it.scenarioInfoDa,
                isAskBeforeAutoRun = it.scenarioInfoDa?.secondAskeFlag == 0,
                isTemplateScene = false,
                isAutoRun = it.scenarioInfoDa?.autoExeFlag == 0,
                executeFrequency = it.scenarioInfoDa?.executeFrequency
            )
            SceneNewEditActivity.launchSceneEditWithMyScene(
                context, enterBean = MySceneBean(
                    //进入编辑界面前需要过滤不可用的能力
                    scenarioBean.apply {
                        //下载到新增编辑需要重新赋予场景id，避免下载到自己上传的场景 保存报错
                        scenarioInfo?.scenarioId = SceneManager.getNewSceneId()
                        val edgeCondition: ScenarioInfo.Condition? = scenarioInfo?.edgeCondition
                        val conditions: List<ScenarioInfo.Condition>? = scenarioInfo?.conditions
                        val sequence: List<ScenarioInfo.Sequence>? = scenarioInfo?.sequence

                        edgeCondition?.let { edge ->
                            if (ObjectUtils.checkScenarioDataIsInvalid(edge) || !SceneEditManager.checkIdIsActive(
                                    edge.skillId, 1
                                )
                            ) {
                                scenarioInfo?.edgeCondition = null
                            }
                        }
                        scenarioInfo?.conditions = conditions?.filter { con ->
                            !ObjectUtils.checkScenarioDataIsInvalid(con) && SceneEditManager.checkIdIsActive(
                                con.skillId, 2
                            )
                        }
                        scenarioInfo?.sequence = sequence?.filter { seq ->
                            !ObjectUtils.checkScenarioDataIsInvalid(seq) && SceneEditManager.checkIdIsActive(
                                seq.action.skillId, 0
                            )
                        }

                    }, isSceneStart = false, isTop = false, isAddNewScene2ShowAnima = true, isAddScene = true
                ), enterType = SceneNewEditEnterType.Download, scenario_L3id = it.communityScenarioId!!
            )
        }

    }

    open fun cancelDisposable() {
        mFocusDisposable?.takeIf { !it.isDisposed }?.dispose()
        mFocusDisposable = null

        mLikeDisposable?.takeIf { !it.isDisposed }?.dispose()
        mLikeDisposable = null

        cancelDownLoadDisposable()
    }

    fun cancelDownLoadDisposable() {
        mDownLoadDisposable?.takeIf { !it.isDisposed }?.dispose()
        mDownLoadDisposable = null
    }

}