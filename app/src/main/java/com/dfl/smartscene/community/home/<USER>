package com.dfl.smartscene.community.home;

//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//


import android.os.Parcel;
import android.os.Parcelable;

import com.iauto.scenarioadapter.InputArgInfo;

import java.util.List;

public class ScenarioInfo implements Parcelable {
    private String scenarioId;
    private String scenarioName;
    private String scenarioDesc;
    private int version;
    private int autoExeFlag;
    private int secondAskeFlag;
    private long scenarioTimeStamp;
    private String imgPath;
    private int executeFrequency;
    private Condition edgeCondition;
    private List<Condition> conditions;
    private List<Sequence> sequence;
    public static final Parcelable.Creator<ScenarioInfo> CREATOR = new Parcelable.Creator<ScenarioInfo>() {
        public ScenarioInfo createFromParcel(Parcel in) {
            return new ScenarioInfo(in);
        }

        public ScenarioInfo[] newArray(int size) {
            return new ScenarioInfo[size];
        }
    };

    public ScenarioInfo(String scenarioId, String scenarioName, String scenarioDesc, int version, int autoExeFlag, int secondAskeFlag, long scenarioTimeStamp, String imgPath, int executeFrequency,Condition edgeCondition, List<Condition> conditions, List<Sequence> sequence) {
        this.scenarioId = scenarioId;
        this.scenarioName = scenarioName;
        this.scenarioDesc = scenarioDesc;
        this.version = version;
        this.autoExeFlag = autoExeFlag;
        this.secondAskeFlag = secondAskeFlag;
        this.scenarioTimeStamp = scenarioTimeStamp;
        this.imgPath = imgPath;
        this.executeFrequency = executeFrequency;
        this.edgeCondition = edgeCondition;
        this.conditions = conditions;
        this.sequence = sequence;
    }

    public ScenarioInfo(Parcel in) {
        this.scenarioId = in.readString();
        this.scenarioName = in.readString();
        this.scenarioDesc = in.readString();
        this.version = in.readInt();
        this.autoExeFlag = in.readInt();
        this.secondAskeFlag = in.readInt();
        this.scenarioTimeStamp = in.readLong();
        this.imgPath = in.readString();
        this.executeFrequency = in.readInt();
        this.edgeCondition = (Condition)in.readParcelable(Condition.class.getClassLoader());
        this.conditions = in.createTypedArrayList(Condition.CREATOR);
        this.sequence = in.createTypedArrayList(Sequence.CREATOR);
    }

    public int describeContents() {
        return 0;
    }

    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeString(this.scenarioId);
        parcel.writeString(this.scenarioName);
        parcel.writeString(this.scenarioDesc);
        parcel.writeInt(this.version);
        parcel.writeInt(this.autoExeFlag);
        parcel.writeInt(this.secondAskeFlag);
        parcel.writeLong(this.scenarioTimeStamp);
        parcel.writeString(this.imgPath);
        parcel.writeInt(this.executeFrequency);
        parcel.writeParcelable(this.edgeCondition, i);
        parcel.writeTypedList(this.conditions);
        parcel.writeTypedList(this.sequence);
    }

    public String getScenarioId() {
        return this.scenarioId;
    }

    public void setScenarioId(String scenarioId) {
        this.scenarioId = scenarioId;
    }

    public String getScenarioName() {
        return this.scenarioName;
    }

    public void setScenarioName(String scenarioName) {
        this.scenarioName = scenarioName;
    }

    public String getScenarioDesc() {
        return this.scenarioDesc;
    }

    public void setScenarioDesc(String scenarioDesc) {
        this.scenarioDesc = scenarioDesc;
    }

    public int getVersion() {
        return this.version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public int getAutoExeFlag() {
        return this.autoExeFlag;
    }

    public void setAutoExeFlag(int autoExeFlag) {
        this.autoExeFlag = autoExeFlag;
    }

    public int getSecondAskeFlag() {
        return this.secondAskeFlag;
    }

    public void setSecondAskeFlag(int secondAskeFlag) {
        this.secondAskeFlag = secondAskeFlag;
    }

    public long getScenarioTimeStamp() {
        return this.scenarioTimeStamp;
    }

    public void setScenarioTimeStamp(long scenarioTimeStamp) {
        this.scenarioTimeStamp = scenarioTimeStamp;
    }

    public String getImgPath() {
        return this.imgPath;
    }

    public void setImgPath(String imgPath) {
        this.imgPath = imgPath;
    }

    public int getExecuteFrequency() {
        return this.executeFrequency;
    }

    public void setExecuteFrequency(int executeFrequency) {
        this.executeFrequency = executeFrequency;
    }

    public Condition getEdgeCondition() {
        return this.edgeCondition;
    }

    public void setEdgeCondition(Condition edgeCondition) {
        this.edgeCondition = edgeCondition;
    }

    public List<Condition> getConditions() {
        return this.conditions;
    }

    public void setConditions(List<Condition> conditions) {
        this.conditions = conditions;
    }

    public List<Sequence> getSequence() {
        return this.sequence;
    }

    public void setSequence(List<Sequence> sequence) {
        this.sequence = sequence;
    }

    public String toString() {
        return this.edgeCondition != null ? "ScenarioInfo{scenarioId='" + this.scenarioId + '\'' + ", scenarioName='" + this.scenarioName + '\'' + ", scenarioDesc='" + this.scenarioDesc + '\'' + ", version=" + this.version + ", autoExeFlag=" + this.autoExeFlag + ", secondAskeFlag=" + this.secondAskeFlag + ", scenarioTimeStamp=" + this.scenarioTimeStamp + ", imgPath=" + this.imgPath + ", executeFrequency=" + this.executeFrequency + ", edgeCondition=" + this.edgeCondition.toString() + ", conditions=" + this.conditions + ", sequence=" + this.sequence + '}' : "ScenarioInfo{scenarioId='" + this.scenarioId + '\'' + ", scenarioName='" + this.scenarioName + '\'' + ", scenarioDesc='" + this.scenarioDesc + '\'' + ", version=" + this.version + ", autoExeFlag=" + this.autoExeFlag + ", secondAskeFlag=" + this.secondAskeFlag + ", scenarioTimeStamp=" + this.scenarioTimeStamp + ", imgPath=" + this.imgPath + ", executeFrequency=" + this.executeFrequency + ", edgeCondition=" + this.edgeCondition + ", conditions=" + this.conditions + ", sequence=" + this.sequence + '}';
    }

    public static class Condition implements Parcelable {
        private int id;
        private String desc;
        private int skillId;
        private String category;
        private List<InputArgInfo> input;
        public static final Parcelable.Creator<Condition> CREATOR = new Parcelable.Creator<Condition>() {
            public Condition createFromParcel(Parcel in) {
                return new Condition(in);
            }

            public Condition[] newArray(int size) {
                return new Condition[size];
            }
        };

        public Condition(int id, String desc, int skillId, String category, List<InputArgInfo> input) {
            this.id = id;
            this.desc = desc;
            this.skillId = skillId;
            this.category = category;
            this.input = input;
        }

        public Condition(Parcel in) {
            this.id = in.readInt();
            this.desc = in.readString();
            this.skillId = in.readInt();
            this.category = in.readString();
            this.input = in.createTypedArrayList(InputArgInfo.CREATOR);
        }

        public int describeContents() {
            return 0;
        }

        public void writeToParcel(Parcel parcel, int i) {
            parcel.writeInt(this.id);
            parcel.writeString(this.desc);
            parcel.writeInt(this.skillId);
            parcel.writeString(this.category);
            parcel.writeTypedList(this.input);
        }

        public int getId() {
            return this.id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public int getSkillId() {
            return this.skillId;
        }

        public void setSkillId(int skillId) {
            this.skillId = skillId;
        }

        public String getCategory() {
            return this.category;
        }

        public void setCategory(String category) {
            this.category = category;
        }

        public List<InputArgInfo> getInput() {
            return this.input;
        }

        public void setInput(List<InputArgInfo> input) {
            this.input = input;
        }

        public String toString() {
            return this.input != null ? "Condition{id=" + this.id + ", desc='" + this.desc + '\'' + ", skillId=" + this.skillId + ", category='" + this.category + '\'' + ", input=" + this.input.toString() + '}' : "Condition{id=" + this.id + ", desc='" + this.desc + '\'' + ", skillId=" + this.skillId + ", category='" + this.category + '\'' + ", input=" + this.input + '}';
        }
    }

    public static class Sequence implements Parcelable {
        private int id;
        private int type;
        private int delay;
        private Action action;
        public static final Parcelable.Creator<Sequence> CREATOR = new Parcelable.Creator<Sequence>() {
            public Sequence createFromParcel(Parcel in) {
                return new Sequence(in);
            }

            public Sequence[] newArray(int size) {
                return new Sequence[size];
            }
        };

        public Sequence(int id, int type, int delay) {
            this.id = id;
            this.type = type;
            this.delay = delay;
        }

        public Sequence(int id, int type, Action action) {
            this.id = id;
            this.type = type;
            this.action = action;
        }

        public Sequence(Parcel in) {
            this.id = in.readInt();
            this.type = in.readInt();
            this.delay = in.readInt();
            this.action = (Action)in.readParcelable(Action.class.getClassLoader());
        }

        public int describeContents() {
            return 0;
        }

        public void writeToParcel(Parcel parcel, int i) {
            parcel.writeInt(this.id);
            parcel.writeInt(this.type);
            parcel.writeInt(this.delay);
            parcel.writeParcelable(this.action, i);
        }

        public int getId() {
            return this.id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getType() {
            return this.type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public int getTime() {
            return this.delay;
        }

        public void setTime(int time) {
            this.delay = time;
        }

        public Action getAction() {
            return this.action;
        }

        public void setAction(Action action) {
            this.action = action;
        }

        public String toString() {
            return this.type == 0 ? "Sequence{id=" + this.id + ", type=" + this.type + ", delay=" + this.delay + '}' : "Sequence{id=" + this.id + ", type=" + this.type + ", action=" + this.action.toString() + '}';
        }
    }

    public static class Action implements Parcelable {
        private String desc;
        private int skillId;
        private String category;
        private List<InputArgInfo> input;
        public static final Parcelable.Creator<Action> CREATOR = new Parcelable.Creator<Action>() {
            public Action createFromParcel(Parcel in) {
                return new Action(in);
            }

            public Action[] newArray(int size) {
                return new Action[size];
            }
        };

        public Action(String desc, int skillId, String category, List<InputArgInfo> input) {
            this.desc = desc;
            this.skillId = skillId;
            this.category = category;
            this.input = input;
        }

        protected Action(Parcel in) {
            this.desc = in.readString();
            this.skillId = in.readInt();
            this.category = in.readString();
            this.input = in.createTypedArrayList(InputArgInfo.CREATOR);
        }

        public int describeContents() {
            return 0;
        }

        public void writeToParcel(Parcel parcel, int i) {
            parcel.writeString(this.desc);
            parcel.writeInt(this.skillId);
            parcel.writeString(this.category);
            parcel.writeTypedList(this.input);
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public int getSkillId() {
            return this.skillId;
        }

        public void setSkillId(int skillId) {
            this.skillId = skillId;
        }

        public String getCategory() {
            return this.category;
        }

        public void setCategory(String category) {
            this.category = category;
        }

        public List<InputArgInfo> getInput() {
            return this.input;
        }

        public void setInput(List<InputArgInfo> input) {
            this.input = input;
        }

        public String toString() {
            return "Action{desc=" + this.desc + ", skillId=" + this.skillId + ", category=" + this.category + ", input=" + this.input.toString() + '}';
        }
    }
}

