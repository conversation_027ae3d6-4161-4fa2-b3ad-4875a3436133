package com.dfl.smartscene.community.home;

import android.annotation.SuppressLint;
import android.content.res.Configuration;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.dfl.android.common.base.BaseVBFragment;
import com.dfl.android.common.global.GlobalLiveEventConstants;
import com.dfl.android.common.util.CommonToastUtils;
import com.dfl.android.commonlib.log.CommonLogUtils;
import com.dfl.dflcommonlibs.rxbus.RxBus;
import com.dfl.dflcommonlibs.rxbus.Subscribe;
import com.dfl.smartscene.R;
import com.dfl.smartscene.communication.UserCenterManager;
import com.dfl.smartscene.databinding.SceneFragmentCommunityBinding;
import com.dfl.smartscene.rv.EquallySpaceDecoration;
import com.dfl.smartscene.util.UIUtils;
// 使用CCS相关导入
import com.dfl.smartscene.ccs.wrapper.HttpWrapper;
import com.dfl.smartscene.ccs.http.HttpResult;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SceneCategory;
import com.dfl.smartscene.ccs.busevent.NetWorkEvent;
import com.jeremyliao.liveeventbus.LiveEventBus;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener;

import java.lang.ref.WeakReference;
import java.util.Objects;
import java.util.List;
import java.util.ArrayList;
import io.reactivex.Observable;
import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.autosize.utils.AutoSizeUtils;

/**
 * Created by 钟文祥 on 2023/11/1.
 * Describer: 社区场景 fragment - 融合版本，直接显示热门列表
 * 修改为使用CCS中的HttpWrapper网络请求模式，直接使用SceneBean和SceneCategory
 */
@SuppressLint("CheckResult")
public class CommunityFragment extends BaseVBFragment<CommunityViewModel,
        SceneFragmentCommunityBinding> implements SceneBeanAdapterItemListener {

    private static final String TAG = "CommunityFragment";
    
    // 使用SceneBeanAdapter替代CommunityAdapter
    public SceneBeanAdapter mAdapter;
    private Disposable showLottieDisposable = null;//闪烁的延时处理
    private Disposable noMoreDataDisposable = null;//无更多数据的延时处理
    private Disposable networkDisposable = null;//网络请求的Disposable
    
    // 使用HttpWrapper进行网络请求
    private HttpWrapper httpWrapper;
    private boolean isLoading = false;
    private int currentPage = 1;
    
    // 当前场景分类数据
    private List<SceneCategory> sceneCategoryList = new ArrayList<>();

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {
        // 初始化HttpWrapper
        httpWrapper = HttpWrapper.getInstance();
        
        // 注册RxBus事件监听
        RxBus.getDefault().register(this);
        
        // 初始化RecyclerView
        initRV();
        // 初始化刷新控件
        initRefreshLayout();
    }

    /**
     * 初始化网络状态观察
     */
    private void initNetworkObserver() {
        // 网络状态变化通过@Subscribe注解的方法来处理，不需要在这里手动订阅
        // 如果需要监听网络状态，应该在类中添加@Subscribe方法
    }

    /**
     * 监听网络状态变化事件
     * @param netWorkEvent 网络状态事件
     */
    @Subscribe
    public void onNetworkStateChanged(NetWorkEvent netWorkEvent) {
        CommonLogUtils.logD(TAG, "[网络状态] 网络状态变化: " + netWorkEvent.isNetStatus());
        try {
            if (netWorkEvent.isNetStatus() && (mAdapter == null || mAdapter.getData().isEmpty())) {
                CommonLogUtils.logD(TAG, "[网络状态] 网络恢复且无数据，重新加载");
                // 网络恢复且无数据时重新加载
                loadSceneData(true);
            }
        } catch (Exception e) {
            CommonLogUtils.logE(TAG, "[网络状态] onNetworkStateChanged异常: " + e.getMessage());
        }
    }

    @Override
    public void createObserver() {
        CommonLogUtils.logD(TAG, "[生命周期] createObserver开始");
        try {
            // 初始化网络状态监听
            initNetworkObserver();
            // 个人中心登录状态回调
            initEventBusLoginOutSuccess();
            // 选择场景和发布场景的返回
            initEventBusPostFragmentBack();

            CommonLogUtils.logD(TAG, "[生命周期] 首次加载数据");
            // 首次加载数据
            loadSceneData(true);

            UIUtils.initLoadingLottieSync(mViewBind);
            CommonLogUtils.logD(TAG, "[生命周期] createObserver完成");
        } catch (Exception e) {
            CommonLogUtils.logE(TAG, "[生命周期] createObserver异常: " + e.getMessage());
        }
    }
    /**
     * 个人中心登录退登成功回调
     */
    private void initEventBusLoginOutSuccess() {
        CommonLogUtils.logD(TAG, "[事件监听] 初始化登录状态监听");
        try {
            LiveEventBus.get(GlobalLiveEventConstants.KEY_USER_CENTER_USER_IS_LOGIN_OUT_STATUS,
                    Boolean.class).observe(getViewLifecycleOwner(), item -> {
                try {
                    if (item) {
                        CommonLogUtils.logD(TAG, "[事件监听] 退登成功 界面回调");
                    } else {
                        CommonLogUtils.logD(TAG,
                                "[事件监听] 登录成功 界面回调" + Objects.requireNonNull(UserCenterManager.getUserLoginStatus()).getUuid());
                    }
                    // 刷新数据
                    CommonLogUtils.logD(TAG, "[事件监听] 登录状态变化，刷新数据");
                    loadSceneData(true);
                } catch (Exception e) {
                    CommonLogUtils.logE(TAG, "[事件监听] 登录状态回调异常: " + e.getMessage());
                }
            });
        } catch (Exception e) {
            CommonLogUtils.logE(TAG, "[事件监听] initEventBusLoginOutSuccess异常: " + e.getMessage());
        }
    }

    /**
     * 选择场景和发布场景的返回
     */
    private void initEventBusPostFragmentBack() {
        CommonLogUtils.logD(TAG, "[事件监听] 初始化场景发布监听");
        try {
            LiveEventBus.get(GlobalLiveEventConstants.KEY_POST_FRAGMENT_BACK, Integer.class)
                    .observe(this, integer -> {
                        try {
                            CommonLogUtils.logD(TAG, "[事件监听] 场景发布回调，状态: " + integer);
                            if (integer == 2) {//场景发布成功
                                CommonLogUtils.logD(TAG, "[事件监听] 场景发布成功，刷新数据");
                                // 刷新数据
                                loadSceneData(true);
                            }
                        } catch (Exception e) {
                            CommonLogUtils.logE(TAG, "[事件监听] 场景发布回调异常: " + e.getMessage());
                        }
                    });
        } catch (Exception e) {
            CommonLogUtils.logE(TAG, "[事件监听] initEventBusPostFragmentBack异常: " + e.getMessage());
        }
    }

    @Override
    public void onDestroy() {
        CommonLogUtils.logD(TAG, "[生命周期] onDestroy开始");
        try {
            super.onDestroy();
            // 取消RxBus注册
            CommonLogUtils.logD(TAG, "[生命周期] 取消RxBus注册");
            RxBus.getDefault().unRegister(this);
            
            // 清理资源
            if (showLottieDisposable != null && !showLottieDisposable.isDisposed()) {
                CommonLogUtils.logD(TAG, "[生命周期] 清理showLottieDisposable");
                showLottieDisposable.dispose();
            }
            if (noMoreDataDisposable != null && !noMoreDataDisposable.isDisposed()) {
                CommonLogUtils.logD(TAG, "[生命周期] 清理noMoreDataDisposable");
                noMoreDataDisposable.dispose();
            }
            if (networkDisposable != null && !networkDisposable.isDisposed()) {
                CommonLogUtils.logD(TAG, "[生命周期] 清理networkDisposable");
                networkDisposable.dispose();
            }
            CommonLogUtils.logD(TAG, "[生命周期] onDestroy完成");
        } catch (Exception e) {
            CommonLogUtils.logE(TAG, "[生命周期] onDestroy异常: " + e.getMessage());
        }
    }

    /**
     * 使用HttpWrapper加载场景数据
     * @param isRefresh 是否为刷新操作
     */
    private void loadSceneData(boolean isRefresh) {
        CommonLogUtils.logD(TAG, "[网络请求] loadSceneData开始 - isRefresh: " + isRefresh + ", isLoading: " + isLoading);
        
        if (isLoading) {
            CommonLogUtils.logD(TAG, "[网络请求] 正在加载中，跳过重复请求");
            return;
        }
        
        if (isRefresh) {
            currentPage = 1;
            CommonLogUtils.logD(TAG, "[网络请求] 刷新操作，重置页码为1");
        }
        
        isLoading = true;
        CommonLogUtils.logD(TAG, "[网络请求] 开始使用HttpWrapper加载场景数据，页码: " + currentPage);
        
        try {
            // 显示加载状态
            if (isRefresh && (mAdapter == null || mAdapter.getData().isEmpty())) {
                CommonLogUtils.logD(TAG, "[网络请求] 显示加载动画");
                UIUtils.showLoading(getMViewBind());
            }
            
            CommonLogUtils.logD(TAG, "[网络请求] 准备调用httpWrapper.requestSquareSceneList()");
            
            // 使用HttpWrapper的requestSquareSceneList方法
            networkDisposable = httpWrapper.requestSquareSceneList()
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(
                        // onNext
                        sceneCategories -> {
                            CommonLogUtils.logD(TAG, "[网络请求] onNext回调触发 - 数据: " + (sceneCategories != null ? "非空(" + sceneCategories.size() + "个分类)" : "null"));
                            try {
                                handleSceneCategoryResponse(sceneCategories, isRefresh);
                            } catch (Exception e) {
                                CommonLogUtils.logE(TAG, "[网络请求] handleSceneCategoryResponse异常: " + e.getMessage());
                                handleRequestError(e, isRefresh);
                            }
                            isLoading = false;
                            CommonLogUtils.logD(TAG, "[网络请求] onNext处理完成，isLoading设为false");
                        },
                        // onError
                        throwable -> {
                            CommonLogUtils.logE(TAG, "[网络请求] onError回调触发 - 错误: " + throwable.getMessage());
                            try {
                                handleRequestError(throwable, isRefresh);
                            } catch (Exception e) {
                                CommonLogUtils.logE(TAG, "[网络请求] handleRequestError异常: " + e.getMessage());
                            }
                            isLoading = false;
                            CommonLogUtils.logD(TAG, "[网络请求] onError处理完成，isLoading设为false");
                        }
                    );
            
            CommonLogUtils.logD(TAG, "[网络请求] Observable订阅完成，networkDisposable: " + (networkDisposable != null ? "已创建" : "null"));
            
        } catch (Exception e) {
            CommonLogUtils.logE(TAG, "[网络请求] loadSceneData异常: " + e.getMessage());
            isLoading = false;
            handleRequestError(e, isRefresh);
        }
    }

    /**
     * 处理场景分类响应数据
     * @param sceneCategories 场景分类列表
     * @param isRefresh 是否为刷新操作
     */
    private void handleSceneCategoryResponse(List<SceneCategory> sceneCategories, boolean isRefresh) {
        CommonLogUtils.logD(TAG, "[数据处理] handleSceneCategoryResponse开始 - isRefresh: " + isRefresh);
        
        try {
            isLoading = false;
            CommonLogUtils.logD(TAG, "[数据处理] 隐藏加载动画");
            UIUtils.disShow(getMViewBind());
        
            if (sceneCategories != null && !sceneCategories.isEmpty()) {
                CommonLogUtils.logD(TAG, "[数据处理] 场景分类数据非空，开始处理 - 分类数量: " + sceneCategories.size());
                
                // 从SceneCategory中提取所有SceneBean
                List<SceneBean> allSceneBeans = new ArrayList<>();
                for (int i = 0; i < sceneCategories.size(); i++) {
                    SceneCategory category = sceneCategories.get(i);
                    CommonLogUtils.logD(TAG, "[数据处理] 处理分类[" + i + "]: " + (category != null ? "非空" : "null"));
                    
                    if (category != null && category.getSceneBeanList() != null && !category.getSceneBeanList().isEmpty()) {
                        CommonLogUtils.logD(TAG, "[数据处理] 分类[" + i + "]包含场景数量: " + category.getSceneBeanList().size());
                        allSceneBeans.addAll(category.getSceneBeanList());
                    } else {
                        CommonLogUtils.logD(TAG, "[数据处理] 分类[" + i + "]场景列表为空");
                    }
                }
                
                CommonLogUtils.logD(TAG, "[数据处理] 获取到场景数据，分类数量: " + sceneCategories.size() + ", 场景总数: " + allSceneBeans.size());
        
                if (isRefresh) {
                    CommonLogUtils.logD(TAG, "[数据处理] 执行刷新操作");
                    // 刷新数据
                    if (mAdapter != null) {
                        CommonLogUtils.logD(TAG, "[数据处理] 设置适配器数据，数量: " + allSceneBeans.size());
                        mAdapter.setList(allSceneBeans);
                        CommonLogUtils.logD(TAG, "[数据处理] 适配器数据设置完成");
                    } else {
                        CommonLogUtils.logE(TAG, "[数据处理] mAdapter为null，无法设置数据");
                    }
                    
                    CommonLogUtils.logD(TAG, "[数据处理] 完成刷新状态");
                    getMViewBind().slUpdate.finishRefresh(true);
                } else {
                    CommonLogUtils.logD(TAG, "[数据处理] 执行加载更多操作");
                    // 加载更多数据
                    if (mAdapter != null) {
                        CommonLogUtils.logD(TAG, "[数据处理] 添加更多数据，数量: " + allSceneBeans.size());
                        mAdapter.addData(allSceneBeans);
                        CommonLogUtils.logD(TAG, "[数据处理] 更多数据添加完成");
                    } else {
                        CommonLogUtils.logE(TAG, "[数据处理] mAdapter为null，无法添加数据");
                    }
                    
                    CommonLogUtils.logD(TAG, "[数据处理] 完成加载更多状态");
                    getMViewBind().slUpdate.finishLoadMore(true);
                }
        
                // 检查是否还有更多数据（这里简化处理，实际应该根据API返回判断）
                if (allSceneBeans.size() < 20) { // 假设每页20条数据
                    CommonLogUtils.logD(TAG, "[数据处理] 数据量小于20，设置无更多数据");
                    getMViewBind().slUpdate.setNoMoreData(true);
                } else {
                    CommonLogUtils.logD(TAG, "[数据处理] 数据量大于等于20，可能还有更多数据");
                    getMViewBind().slUpdate.setNoMoreData(false);
                }
        
            } else {
                CommonLogUtils.logE(TAG, "[数据处理] 场景分类数据为空或请求失败");
                handleRequestError(new Exception("数据为空或请求失败"), isRefresh);
            }
            
            CommonLogUtils.logD(TAG, "[数据处理] handleSceneCategoryResponse完成");
            
        } catch (Exception e) {
            CommonLogUtils.logE(TAG, "[数据处理] handleSceneCategoryResponse异常: " + e.getMessage());
            handleRequestError(e, isRefresh);
        }
    }
    
    /**
     * 处理请求错误
     * @param error 错误信息
     * @param isRefresh 是否为刷新操作
     */
    private void handleRequestError(Throwable error, boolean isRefresh) {
        CommonLogUtils.logE(TAG, "[错误处理] handleRequestError开始 - isRefresh: " + isRefresh + ", 错误: " + (error != null ? error.getMessage() : "null"));
        
        try {
            isLoading = false;
            CommonLogUtils.logD(TAG, "[错误处理] 隐藏加载动画");
            UIUtils.disShow(getMViewBind());
            
            CommonLogUtils.logE(TAG, "[错误处理] 处理请求错误: " + (error != null ? error.getMessage() : "未知错误"));
            if (error != null) {
                error.printStackTrace();
            }
            
            CommonLogUtils.logD(TAG, "[错误处理] 显示错误提示");
            CommonToastUtils.show(R.string.scene_toast_common_web_server_error);
            
            if (isRefresh) {
                CommonLogUtils.logD(TAG, "[错误处理] 完成刷新状态(失败)");
                getMViewBind().slUpdate.finishRefresh(false);
            } else {
                CommonLogUtils.logD(TAG, "[错误处理] 完成加载更多状态(失败)");
                getMViewBind().slUpdate.finishLoadMore(false);
            }
            
            // 如果没有数据，显示空列表
            if (mAdapter == null || mAdapter.getData().isEmpty()) {
                CommonLogUtils.logD(TAG, "[错误处理] 适配器无数据，设置空列表");
                List<SceneBean> emptyList = new ArrayList<>();
                if (mAdapter != null) {
                    mAdapter.setList(emptyList);
                    CommonLogUtils.logD(TAG, "[错误处理] 空列表设置完成");
                } else {
                    CommonLogUtils.logE(TAG, "[错误处理] mAdapter为null，无法设置空列表");
                }
            }
            
            CommonLogUtils.logD(TAG, "[错误处理] handleRequestError完成");
            
        } catch (Exception e) {
            CommonLogUtils.logE(TAG, "[错误处理] handleRequestError内部异常: " + e.getMessage());
        }
    }

    /**
     * 初始化RecyclerView
     */
    private void initRV() {
        UIUtils.initRecyclerView(getActivity(), getMViewBind().rvCommunity,
                new GridLayoutManager(getActivity(), 3), false);
    
        // 添加上下蒙层模糊效果
        getMViewBind().rvCommunity.setVerticalFadingEdgeEnabled(true);
        getMViewBind().rvCommunity.setFadingEdgeLength(AutoSizeUtils.dp2px(requireContext(), 10f));
    
        // 使用SceneBeanAdapter
        mAdapter = new SceneBeanAdapter(this);
        getMViewBind().rvCommunity.setAdapter(mAdapter);
        getMViewBind().rvCommunity.addItemDecoration(new EquallySpaceDecoration(
                AutoSizeUtils.dp2px(requireContext(), 24f),
                AutoSizeUtils.dp2px(requireContext(), 24f),
                true));
        getMViewBind().rvCommunity.addOnScrollListener(new MyOnScrollListener(CommunityFragment.this));
    }

    /**
     * 初始化刷新控件
     */
    private void initRefreshLayout() {
        getMViewBind().slUpdate.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                CommonLogUtils.logD(TAG, "触发加载更多");
                currentPage++;
                loadSceneData(false);
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                CommonLogUtils.logD(TAG, "触发下拉刷新");
                loadSceneData(true);
            }
        });
    }

    // 实现SceneBeanAdapterItemListener接口
    @Override
    public void onSceneClick(int position, SceneBean sceneBean) {
        CommonLogUtils.logD(TAG, "点击场景: " + sceneBean.getSceneName());
        // 处理场景点击事件，可以跳转到场景详情页面
        // 这里可以根据需要实现具体的跳转逻辑
    }

    public void onCollectClick(int position, SceneBean sceneBean) {
        CommonLogUtils.logD(TAG, "点击收藏场景: " + sceneBean.getSceneName());
        // 处理收藏点击事件
        sceneBean.setCollectState(!sceneBean.isCollectState());
        if (mAdapter != null) {
            mAdapter.notifyItemChanged(position);
        }
        
        // 这里可以调用相应的API来更新收藏状态
        String action = sceneBean.isCollectState() ? "收藏" : "取消收藏";
        CommonToastUtils.show(action + "成功");
    }


    /**
     * 主题切换
     */
    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        //手动切换进度条png
        UIUtils.setSmartRefreshOnConfigurationChanged(null, getMViewBind().classicsFooter);
    }

    /**
     * RecyclerView滚动监听器
     */
    private static class MyOnScrollListener extends RecyclerView.OnScrollListener {
        private WeakReference<CommunityFragment> reference;

        public MyOnScrollListener(CommunityFragment fragment) {
            reference = new WeakReference<>(fragment);
        }

        @Override
        public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            if (reference == null || reference.get() == null) return;
            if (newState == 0) { //停止
                Glide.with(reference.get()).resumeRequests();
            } else { //滑动
                Glide.with(reference.get()).pauseRequests();
            }
        }
    }
}