package com.dfl.smartscene.soa

import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.util.GsonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.bean.communication.CollectSceneList
import com.dfl.smartscene.bean.communication.LauncherRequestInfo
import com.dfl.smartscene.bean.communication.RunSceneList
import com.dfl.smartscene.bean.main.MySceneBean
import com.dfl.smartscene.communication.LauncherManager
import com.dfl.soacenter.communication.MessageCenterManager
import com.dfl.soacenter.entity.AutoRunSceneInfo
import com.dfl.soacenter.entity.MessageCenterResponse
import java.util.concurrent.CopyOnWriteArraySet

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2022/08/24
 * desc: widget卡片刷新助手
 * version:1.0
 */
class WidgetUpdateHelper {
    companion object {
        private const val TAG = GlobalConstant.GLOBAL_TAG.plus("WidgetUpdateHelper")

        /**
         * 更新桌面卡片中的场景列表
         * @param context 上下文对象
         */
        fun updateDeskCardSceneList(sceneList: ArrayList<MySceneBean>) {
            CommonLogUtils.logI(TAG, "更新桌面卡片场景列表:${sceneList.size}")
            val collectSceneList = SceneManager.getCollectSceneList(sceneList)
            //将场景数据发送给launcher
            sendCollectList2Launcher(collectSceneList)
            //lk1a不再使用widget去更新卡片信息
        }

        /**
         * 发送收藏的数据给launcher
         */
        private fun sendCollectList2Launcher(collectSceneList: java.util.ArrayList<MySceneBean>) {
            CommonLogUtils.logI(TAG, "发送收藏的数据给launcher:${collectSceneList.size}")
            val launcherRequestInfoList = ArrayList<LauncherRequestInfo>()
            val collectSceneInfo = CollectSceneList(launcherRequestInfoList)
            for (collectScene in collectSceneList) {
                launcherRequestInfoList.add(
                    LauncherRequestInfo(
                        collectScene.scenario.scenarioInfo?.scenarioName ?: "",
                        collectScene.scenario.scenarioInfo?.scenarioId ?: ""
                    )
                )
            }
            LauncherManager.sendCollectSceneInfo(collectSceneInfo)
        }

        /**
         * 发送当前正在运行的场景给launcher
         */
        fun sendRunSceneList2Launcher(runSceneSet: CopyOnWriteArraySet<String>) {
            CommonLogUtils.logI(TAG, "发送当前正在运行的场景给launcher:${runSceneSet.size}")
            val launcherRequestInfoList = ArrayList<LauncherRequestInfo>()
            for (sceneId in runSceneSet) {
                val launcherRequestInfo = LauncherRequestInfo("", sceneId)
                launcherRequestInfoList.add(launcherRequestInfo)
            }
            val runSceneList = RunSceneList(launcherRequestInfoList)
            LauncherManager.sendRunSceneList(runSceneList)
        }

        fun sendAutoRunInfo2Launcher(mySceneBean: MySceneBean) {
            val autoRunSceneInfo = AutoRunSceneInfo(
                LauncherManager.OPERATE_TYPE_SHOW,
                mySceneBean.scenario.scenarioInfo?.scenarioName ?: "",
                mySceneBean.scenario.scenarioInfo?.scenarioId ?: ""
            )
            MessageCenterManager.sendAutoRunInfo(autoRunSceneInfo, {
                val res = GsonUtils.fromJson(it, MessageCenterResponse::class.java)
                CommonLogUtils.logE(TAG, "${res.result},${res.msg}")
                if (res.result == MessageCenterResponse.NO_PERMISSION) {
                    //请求消息中心自动执行横幅失败(权限被关)，响应ccm自动执行
                    SceneManager.notifyAskIfExecuteScenario(autoRunSceneInfo.id, true)
                } else if (res.result != MessageCenterResponse.SUCCESS) {
                    //未知成功请求响应，响应ccm不自动执行
                    SceneManager.notifyAskIfExecuteScenario(autoRunSceneInfo.id, false)
                }
            }, {
                //请求消息中心自动执行横幅失败，响应ccm不自动执行
                SceneManager.notifyAskIfExecuteScenario(autoRunSceneInfo.id, false)
            })
        }

        /**
         * 对场景名称进行切分，大于4个字的则需要用...代替
         * @param sceneName 场景名称
         */
        private fun trimSceneName(sceneName: String?): String {
            if (sceneName == null) {
                return ""
            }
            if (sceneName.length < 5) {
                return sceneName
            }
            val trimName = sceneName.substring(0, 4)
            return trimName.plus("...")
        }
    }
}