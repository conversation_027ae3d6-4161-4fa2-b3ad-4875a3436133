package com.dfl.smartscene.soa

import com.dfl.android.common.util.CustomApiUtils
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/20
 * desc :处理状态条件已选的情况下，防止用户去二次点击
 * version: 1.0
 */
object SceneConditionManager {

    /**
     * 用户已选择的状态skillId
     */
    private val skillIdList = ArrayList<Int>()

    fun setConditionSkillIdList(list: MutableList<ScenarioInfo.Condition>) {
        CustomApiUtils.requestCustomApi({
            skillIdList.clear()
            list.forEach {
                skillIdList.add(it.skillId)
            }
        }, {
            it.printStackTrace()
        })
    }

    /**
     * 判断左边类别对应条件是否已经被用户选择添加，如果存在就置灰不给用户再次选中
     */
    fun <T> checkItemConditionHasAdd(skillId: Int, list: ArrayList<ConditionItemBean<T>>) {
        if (skillId == -1 || skillIdList.isEmpty()) {
            return
        }
        skillIdList.forEach {
            list.forEach { item ->
                if (item.conditionSkillId?.contains(it) == true) {
                    item.isEnable = false
                    if (skillId != -1 && skillId == it) {
                        item.isEnable = true
                    }
                }
            }
        }
    }

    /**
     * @param skillID 技能ID
     * @param position 状态条件所在集合下标，-1为添加条件，如果不为-1的情况下，需要判断其它状态条件中有一样的
     */
    fun checkSkillIdIsSelect(skillID: Int, position: Int = -1): Boolean {
        val list = skillIdList.filter { id -> skillID == id }
        if (list.isNotEmpty()) {
            if (position != -1) {
                val id = skillIdList[position]
                return id != list[0]
            }
            return true
        }
        return false
    }

    fun checkSkillIdIsSelect(skillID: List<Int>, position: Int = -1): Boolean {
        if (skillIdList.isNotEmpty()) {
            skillID.forEach {
                val list = skillIdList.filter { id -> it == id }
                if (list.isNotEmpty()) { //已添加该条件
                    if (position != -1) { //编辑状态判断ID是否相同，不相同禁止编辑
                        val id = skillIdList[position]
                        list.forEach { editId ->
                            if (editId == id)
                                return false
                        }
                    }
                    return true
                }
            }
        }
        return false
    }

    fun clearSkillIdData() {
        skillIdList.clear()
    }
}