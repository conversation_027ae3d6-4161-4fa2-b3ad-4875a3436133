package com.dfl.smartscene.soa

import com.dfl.smartscene.bean.main.MySceneBean
import com.dfl.smartscene.bean.main.SceneSaveLocalDataBean
import com.dfl.smartscene.util.MMKVUtils
import java.util.concurrent.CopyOnWriteArrayList

/**
 * author wswang
 * email <EMAIL>
 * time 2022-08-03
 * desc 处理场景本地排序静态对象类
 * version 1.0
 */
object SceneSortManager {
    //    private val TAG=SceneSortManager::class.java.simpleName

    private var sceneSequenceDataList = CopyOnWriteArrayList<SceneSaveLocalDataBean>()

    fun getSceneSequence() {
        sceneSequenceDataList.clear()
        MMKVUtils.getInstance().sceneSequence?.let {
            if (it.size > 60) {
                MMKVUtils.getInstance().clearLocalSceneData()
            } else {
                sceneSequenceDataList.addAll(it)
            }
        }
    }

    fun saveSceneSequence(list: List<MySceneBean>) {
        sceneSequenceDataList.clear()
        list.forEach {
            sceneSequenceDataList.add(
                SceneSaveLocalDataBean(
                    it.scenario.scenarioInfo?.scenarioId, it.isTop
                )
            )
        }
        MMKVUtils.getInstance().saveSceneSequence(sceneSequenceDataList)
    }

    /**
     * 获取当前是否有收藏场景
     */
    fun hasSceneCollect(): Boolean {
        for (localSceneData in sceneSequenceDataList) {
            if (localSceneData.isCollected) {
                return true
            }
        }
        return false
    }

    /**场景序列拦截器*/
    fun sceneSequenceInterceptor(list: ArrayList<MySceneBean>): ArrayList<MySceneBean> {
        if (sceneSequenceDataList.isNullOrEmpty() || sceneSequenceDataList.size == 0) {
            return list
        } else {
            //有3种情况，1、回调出现新增场景 2、本地顺序发生变化 3、本地场景添加收藏，
            // 注意收藏优先置顶，其次顺序可按照顺序变化，然后新增的
            val cacheList = ArrayList<MySceneBean>()
            val cacheTopList = ArrayList<MySceneBean>()
            val cacheNewAddList = ArrayList<MySceneBean>()
            list.forEach outList@{
                //判断是否是车机端新增数据
                var result = false
                for (bean in sceneSequenceDataList) {
                    if (it.scenario.scenarioInfo?.scenarioId == bean.scenarioId) {
                        result = true
                        break
                    }
                }
                if (!result) {
                    cacheNewAddList.add(it)
                }
            }
            //            sceneSequenceDataList.forEach listFor@{
            //               for (bean in list){
            //                   //判断是否存在和本地保存场景id一致的数据，如果有。将同步本地数据
            //                   //如果没有即为已删除数据
            //                   if (bean.scenario.scenarioInfo?.scenarioId == it.scenarioId) {
            //                       if (it.isCollected) {
            //                           bean.isTop = true
            //                           cacheTopList.add(bean)
            //                       } else {
            //                           cacheList.add(bean)
            //                       }
            //                       break
            //                   }
            //               }
            //            }
            //最终场景的数量不能超过云端的数量，还要保证顺序是正确的
            for (localScenario in sceneSequenceDataList) {
                for (remoteScenario in list) {
                    //判断是否存在和本地保存场景id一致的数据，如果有。将同步本地数据
                    //如果没有即为已删除数据
                    if (remoteScenario.scenario.scenarioInfo?.scenarioId == localScenario.scenarioId) {
                        if (localScenario.isCollected) {
                            remoteScenario.isTop = true
                            if (!isRepeatScenario(cacheTopList, localScenario.scenarioId)) {
                                cacheTopList.add(remoteScenario)
                            }
                        } else {
                            remoteScenario.isTop = false
                            if (!isRepeatScenario(cacheList, localScenario.scenarioId)) {
                                cacheList.add(remoteScenario)
                            }
                        }
                        break
                    }
                }
            }

            //注意此处添加顺序，收藏排首位，其次原有数据，最后新添加的
            cacheList.addAll(cacheNewAddList)
            cacheTopList.addAll(cacheList)
            cacheList.clear()
            cacheNewAddList.clear()
            saveSceneSequence(cacheTopList)
            return cacheTopList
        }
    }

    /**
     * 是否重复的场景ID，避免场景重复添加
     */
    private fun isRepeatScenario(sceneList: ArrayList<MySceneBean>, sceneId: String?): Boolean {
        for (remoteScenario in sceneList) {
            if (remoteScenario.scenario.scenarioInfo?.scenarioId == sceneId) {
                return true
            }
        }
        return false
    }
}