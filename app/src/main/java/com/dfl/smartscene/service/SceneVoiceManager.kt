package com.dfl.smartscene.service

import com.dfl.android.common.util.NumberToCNUtil
import com.dfl.android.common.util.StringUtils
import com.dfl.smartscene.bean.communication.SystemUiBean
import com.dfl.smartscene.bean.service.SceneNameBean
import com.dfl.smartscene.soa.SceneManager
import com.dfl.smartscene.util.MMKVUtils
import com.google.gson.Gson
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/11/15
 * desc : 语音调用场景相关能力的类
 * version: 1.0
 */
object SceneVoiceManager {


    /**
     * 2、语音主动获取当前场景的最新数据。
     * 输出一个Json数据String，数据结构为场景ID，场景名称列表；一个场景ID可能会对应多个场景名称。
     * @return 返回json类型{sceneId:"123",nameList:["abc","edf"]}
     */
    fun getSceneList(): ArrayList<SceneNameBean> {
        val list = SceneManager.getSceneInfoList()
        val dataList = ArrayList<SceneNameBean>()
        list.forEach {
            it.scenario.scenarioInfo?.let { scene ->
                val nameList = getSceneNameList(scene.scenarioName)
                val bean = SceneNameBean(nameList, scene.scenarioId, scene.scenarioName)
                dataList.add(bean)
            }
        }
        return dataList
    }

    /**
     * 3、语音告知需要启动的场景
     * @param sceneId 场景ID
     */
    fun startScene(sceneId: String): Boolean {
        if (sceneId.isEmpty())
            return false
        return SceneManager.startScene4Voice(sceneId) //启动场景
    }

    /**
     * 4、信息类接口（获取）Json数据，场景属性结构需要包括：自动执行场景的数量与手动执行场景的数量
     * @return 返回json类型{"autoCount":1,"manualCount":2｝
     */
    fun getSceneInfo(): SystemUiBean {
        val list = SceneManager.getSceneInfoList()
        val autoList = list.filter { it.scenario.isAutoRun == true }
        val autoCount = autoList.size
        val totalCount = list.size
        return SystemUiBean(autoCount, totalCount)
    }


    /**
     * 6、临停模式开启/关闭后需要停止/开启场景监听
     * @param state true:打开自动执行监听,临停关闭 false:关闭自动执行监听，临停开启
     */
    fun setMonitor(state: Boolean, packageName: String): Boolean {
        //用来区分是自在还是车控临停
        if (packageName.contains("comfort")) {
            MMKVUtils.getInstance().saveComfortDisableScenes(!state)
        }
        return SceneManager.setEnableOrDisableScenes(state)
    }


    /**
     * 7、展厅模式试运行场景
     * @param sceneJson 试运行场景的JSON对象
     */
    fun tryScene(sceneJson: String): Boolean {
        if (sceneJson.isEmpty())
            return false
        //临停判断
        if (SceneManager.isOnDisableSceneStatus()) {
            return false
        }
        val json = sceneJson.replace("\\\\", "")
        val bean = Gson().fromJson(json, ScenarioInfo::class.java)
        return SceneManager.trySceneInfo4ShowRoom(bean)
    }


    /**
     * @param result 场景名
     * 将场景名拆分成单个字符判断是否是连串数字然后合并成对应的集合进行下一步处理
     */
    private fun getSceneNameList(result: String): ArrayList<String> {
        val list = result.toCharArray()
        val numList = ArrayList<String>()
        var oldChar: Char
        var newChar: Char
        for (i in list.indices) {
            if (i != 0) {
                oldChar = list[i - 1]
                newChar = list[i]
                if (checkIsNum(newChar.toString()) && checkIsNum(oldChar.toString())) {
                    val old = numList[numList.size - 1]
                    numList[numList.size - 1] = old.plus(newChar.toString())
                } else {
                    numList.add(newChar.toString())
                }
            } else {
                oldChar = list[i]
                numList.add(oldChar.toString())
            }
        }
        return arrayListOf(exchangeName(numList), exchangeName1(result))
    }

    /**
     * 判断是否是数字
     */
    private fun checkIsNum(result: String): Boolean {
        return StringUtils.isNumeric(result, false)
    }

    /**
     * 将通过上一步场景名拆分并组合过的字符集合转换成对应的中文+中文数字
     */
    private fun exchangeName(nameList: ArrayList<String>): String { //场景十一
        val result: StringBuilder = java.lang.StringBuilder("")
        nameList.forEach {
            if (checkIsNum(it)) {
                result.append(NumberToCNUtil.toChineseLower(it))
            } else {
                result.append(it)
            }
        }
        return result.toString()
    }

    /**
     * 将场景名拆分成单个字符判断是否是数字并转换成对应中文数字
     */
    private fun exchangeName1(name: String): String {
        val result: StringBuilder = java.lang.StringBuilder("")
        name.forEach {
            if (checkIsNum(it.toString())) {
                result.append(NumberToCNUtil.toChineseLower(it.toString()))
            } else {
                result.append(it)
            }
        }
        return result.toString()
    }

}