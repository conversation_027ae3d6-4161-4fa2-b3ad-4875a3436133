package com.dfl.smartscene.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.KeepServiceAliveAidlInterface
import com.dfl.smartscene.R
import com.dfl.smartscene.customapi.CarInfoManager
import com.dfl.smartscene.soa.SceneManager
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.splash.SplashActivity
import com.dfl.smartscene.util.ConditionOrActionIconUtils
import com.dfl.soacenter.SoaCenterService
import com.jeremyliao.liveeventbus.LiveEventBus


/**
 * 623 x9hp上 widget卡片动作处理的服务
 * 智慧场景前台通知服务，初始化图标和场景引擎，能力列表
 * 测试adb命名台架更改社区默认vin码
 * 与soaservice双向保活
 */
class SceneWidgetService : Service() {
    companion object {
        const val TAG = GlobalConstant.GLOBAL_TAG.plus("SceneWidgetService")

        /**
         * 场景名称的key
         */
        const val EXTRA_SCENE_NAME = "SCENE_NAME"

        /**
         * 场景ID的key
         */
        const val EXTRA_SCENE_ID = "SCENE_ID"


        /**
         * 通知的ID
         */
        const val NOTIFICATION_ID = 100010

        /**
         * 通知渠道的ID
         */
        const val NOTIFICATION_CHANNEL_ID = "com.dfl.smartscene.notification_id1"

        /**
         * 卡片收起的动画间隔时间300ms
         */
        const val WIDGET_ANIM_TIME = 300

        /**
         * 延时一段时间再显示卡片
         */
        const val WIDGET_SHOW_DELAY = 400L
    }

    private val binder = object : KeepServiceAliveAidlInterface.Stub() {
        override fun getProName(): String {
            return "SceneWidgetService"
        }
    }

    /**重新拉起服务次数*/
    private var reStartCount = 1

    /**上一次拉起服务时间*/
    private var lastTime = 0L

    /**规定计算重新拉起服务次数的时间*/
    private val MIN_TIME = 1000

    private val myServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(arg0: ComponentName, arg1: IBinder) {
            CommonLogUtils.logI(TAG, "SoaService bind成功")
        }

        override fun onServiceDisconnected(arg0: ComponentName) {
            CommonLogUtils.logE(TAG, "SoaService 异常断开，重新拉起${reStartCount}次")
            val curTime = System.currentTimeMillis()
            val diff = curTime - lastTime
            lastTime = curTime
            //一秒内重连50次不再拉起服务
            if (lastTime == 0L || diff < MIN_TIME) {
                reStartCount++
            } else {
                reStartCount = 1
            }
            if (reStartCount >= 50) {
                return
            }
            // 连接的服务被杀死了，出现了异常断开了，重新拉起服务
            startConnectService()
            // 重新绑定
            bindConnectService()
        }
    }

    override fun onCreate() {
        super.onCreate()
        startForegroundWithNotification()
        initData()
    }

    /**
     * 初始化数据
     */
    private fun initData() {
        //初始化SOA adapter
        SceneManager.init()
        //初始化图标缓存
        ConditionOrActionIconUtils.initSceneConditionAndActionIcon()
        //初始化缓存技能列表ID
        SceneEditManager.initSceneSkillCacheIdData()
        LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_EXECUTE_STATUS, Int::class.java).observeForever {
            //0表示禁用所有场景，1表示恢复执行场景
            CommonLogUtils.logD(TAG, "场景监听调用:$it")
            SceneManager.setEnableOrDisableScenes(it == 1)
        }
    }


    /**
     * 开启前景服务并发送通知
     */
    private fun startForegroundWithNotification() {
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        //8.0及以上注册通知渠道
        createNotificationChannel(notificationManager)
        val notification: Notification = createForegroundNotification()
        //将服务置于启动状态 ,NOTIFICATION_ID指的是创建的通知的ID
        startForeground(NOTIFICATION_ID, notification)
        //发送通知到状态栏
        notificationManager.notify(NOTIFICATION_ID, notification)
    }

    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel(notificationManager: NotificationManager) {
        //Android8.0以上的系统，新建消息通道
        //用户可见的通道名称
        val channelName = "SmartScene Service For Widget"
        //通道的重要程度
        val importance: Int = NotificationManager.IMPORTANCE_MIN
        //构建通知渠道
        val notificationChannel = NotificationChannel(
            NOTIFICATION_CHANNEL_ID, channelName, importance
        )
        notificationChannel.description = "Channel description"
        //向系统注册通知渠道，注册后不能改变重要性以及其他通知行为
        notificationManager.createNotificationChannel(notificationChannel)
    }

    /**
     * 创建服务通知
     */
    private fun createForegroundNotification(): Notification {
        val builder: NotificationCompat.Builder =
            NotificationCompat.Builder(applicationContext, NOTIFICATION_CHANNEL_ID)
        ////        通知小图标
        builder.setSmallIcon(R.mipmap.scene_icon_launcher)
        ////        通知标题
        //        builder.setContentTitle("")
        ////        通知内容
        //        builder.setContentText("")
        ////        设置通知显示的时间
        //        builder.setWhen(System.currentTimeMillis())
        builder.priority = NotificationCompat.PRIORITY_DEFAULT
        //设置为进行中的通知
        builder.setOngoing(true)
        //创建通知并返回
        return builder.build()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        bindConnectService()
        handleIntent(intent)
        return START_STICKY
    }

    private fun handleIntent(intent: Intent?) {
        intent ?: return

        val action = intent.action
        CommonLogUtils.logD(TAG, "接收到消息：$action")
        //adb shell am startservice -n com.dfl.smartscene/.service.SceneWidgetService -a com.dfl.smartscene.changeDefaultVin --ei config 1 --es vin LK2ATEST10140039v
        if (action == "com.dfl.smartscene.changeDefaultVin") {
            val config = intent.getIntExtra("config", 0)
            val vin = intent.getStringExtra("vin")
            CarInfoManager.setDefaultVin(config, vin)
        }
    }

    /**
     * 打开主界面
     */
    private fun openHome() {
        //首次启动不会走splashActivity
        //        val intent = Intent(this, MainActivity::class.java)
        //        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        //        startActivity(intent)
        val intentHome = Intent(Intent.ACTION_MAIN)
        val componentName = ComponentName(this, SplashActivity::class.java)
        intentHome.putExtra("isWidgetOpen", true)
        intentHome.addCategory(Intent.CATEGORY_LAUNCHER).setComponent(componentName).flags =
            Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED
        startActivity(intentHome)
    }

    override fun onDestroy() {
        CommonLogUtils.logI(TAG, "SceneWidgetService destroyed")
        //销毁接口和监听事件
        SceneManager.deInit()
        super.onDestroy()
    }

    override fun onBind(intent: Intent): IBinder {
        //返回aidl接口，让SoaService成功绑定自己
        return binder
    }

    fun startConnectService() {
        CommonLogUtils.logI(TAG, "启动 SoaService")
        //启动soa 中心服务的service
        val soaIntent = Intent(this, SoaCenterService::class.java)
        startForegroundService(soaIntent)
    }

    fun bindConnectService() {
        CommonLogUtils.logI(TAG, "SoaService 开始bind")
        val intent = Intent(this, SoaCenterService::class.java)
        bindService(intent, myServiceConnection, BIND_AUTO_CREATE)
    }

}