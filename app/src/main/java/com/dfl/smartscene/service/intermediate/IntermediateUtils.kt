package com.dfl.smartscene.service.intermediate

import android.content.Context
import android.text.TextUtils
import androidx.lifecycle.lifecycleScope
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.common.util.EncodeUtils
import com.dfl.android.common.util.FileIOUtils
import com.dfl.android.common.util.GsonUtils
import com.dfl.android.common.util.ObjectUtils
import com.dfl.android.common.util.TimeUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.BuildConfig
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.intermediate.IMArgType
import com.dfl.smartscene.bean.intermediate.IMRes
import com.dfl.smartscene.bean.intermediate.IMResItem
import com.dfl.smartscene.bean.intermediate.IMScenarioInfo
import com.dfl.smartscene.bean.intermediate.Req2NodeARule
import com.dfl.smartscene.bean.intermediate.RuleInputMethod
import com.dfl.smartscene.bean.main.MySceneBean
import com.dfl.smartscene.bean.main.ScenarioBean
import com.dfl.smartscene.communication.MediaLocalManager
import com.dfl.smartscene.communication.SearchAddressManager
import com.dfl.smartscene.soa.SceneManager
import com.dfl.smartscene.ui.edit.SceneNewEditActivity
import com.dfl.smartscene.ui.edit.SceneNewEditEnterType
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.main.MainActivity
import com.dfl.soacenter.communication.NaviManager
import com.dfl.soacenter.entity.PoiItem
import com.dfl.soacenter.entity.ReqPlanRoute
import com.dfl.soacenter.entity.RoutePoi
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.google.gson.Gson
import com.iauto.scenarioadapter.ScenarioInfo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import java.util.Arrays
import java.util.Locale
import java.util.concurrent.CopyOnWriteArrayList
import kotlin.coroutines.resume
import kotlin.math.min

/**
 *Created by 钟文祥 on 2025/3/24.
 *Describer:中间层 语义逻辑处理类
 */
object IntermediateUtils {
    private const val TAG = GlobalConstant.GLOBAL_TAG + "IntermediateUtils"

    // 通用配置
    private var mTriggerFileNode: JsonNode? = null
    private var mCommonConditionFileNode: JsonNode? = null
    private var mCommonActionFileNode: JsonNode? = null

    // 特殊配置，目前只有部分动作
    private var mConfigActionFileNode: JsonNode? = null

    val resList: CopyOnWriteArrayList<IMResItem> = CopyOnWriteArrayList()


    /**语义转换*/
    fun semanticTransformation(context: Context, reqJson: String): Req2NodeARule? {
        CommonLogUtils.logI(
            TAG, TimeUtils.millis2String(
                System.currentTimeMillis(), "HH:mm:ss:SSS"
            ) + " 10008协议 语义转换前的json：" + reqJson
        )
        resList.clear()

        // 本车配置对应config
        val carTypes: ArrayList<String> = SceneEditManager.skillConfigList

        val mObjectMapper = ObjectMapper()

        try {
            //通用能力 skillvalue对应skillid
            val commonTriggerRuleJson: String =
                FileIOUtils.readAssets2String(context, "skill/scene_trigger_condition.json")
            val commonStatusRuleJson: String = FileIOUtils.readAssets2String(context, "skill/scene_condition.json")
            val commonActionRuleJson: String = FileIOUtils.readAssets2String(context, "skill/scene_action.json")
            //特殊能力 skillvalue需特殊处理 对应skillid
            val configRuleJson: String = FileIOUtils.readAssets2String(context, "skill/scene_config_rule.json")

            mTriggerFileNode = mObjectMapper.readTree(commonTriggerRuleJson)
            mCommonConditionFileNode = mObjectMapper.readTree(commonStatusRuleJson)
            mCommonActionFileNode = mObjectMapper.readTree(commonActionRuleJson)
            mConfigActionFileNode = mObjectMapper.readTree(configRuleJson).path("action")
        } catch (e: Exception) {
            e.printStackTrace()
            CommonLogUtils.logE(TAG, "10008协议 场景中间层 rule.json文件 读取失败：" + e.message)
            return null
        }

        try {
            val reqJsonRootNode: JsonNode = mObjectMapper.readTree(reqJson)

            //需要装conditionRuleArray+actionRuleArray 在配置文件中找到了 条件和动作
            val ruleJson = JSONObject()
            val conditionRuleArray = JSONArray() //触发+状态
            val actionRuleArray = JSONArray()    //动作

            // 触发条件
            val reqEdgeCondition = reqJsonRootNode.path("edgeCondition")
            val reqSkillValue = reqEdgeCondition.path("skillValue").asText()
            var isFindSkillValue = false
            if (!TextUtils.isEmpty(reqSkillValue) && !"unsupport".equals(reqSkillValue, ignoreCase = true)) {
                for (node in mTriggerFileNode!!) {
                    val ruleSkillValue = node.path("skillValue").asText()
                    if (TextUtils.equals(reqSkillValue, ruleSkillValue)) { //搜索到有这个skillvalue
                        val skillId = Integer.decode(node.path("skillId").asText())
                        (reqEdgeCondition as ObjectNode).put("skillId", skillId) //请求json中加上一个skillid
                        conditionRuleArray.put(JSONObject(node.toString()))
                        isFindSkillValue = true
                        break
                    }
                }
            }
            if (!isFindSkillValue) { //找不到此触发条件
                resList.add(
                    IMResItem(
                        reqSkillValue, 1, "配置找不到此触发条件SkillValue，请核实全整正确，或为空，或为unsupport"
                    )
                )
            }
            // 状态条件
            val reqConditions = reqJsonRootNode.path("conditions")
            for (reqConditionNode in reqConditions) {
                isFindSkillValue = false
                val reqConditionSkillValue = reqConditionNode.path("skillValue").asText()
                if (!TextUtils.isEmpty(reqConditionSkillValue) && !"unsupport".equals(
                            reqConditionSkillValue, ignoreCase = true
                        )
                ) {
                    for (node in mCommonConditionFileNode!!) {
                        val ruleSkillValue = node.path("skillValue").asText()
                        if (TextUtils.equals(reqConditionSkillValue, ruleSkillValue)) {
                            val skillId = Integer.decode(node.path("skillId").asText())
                            (reqConditionNode as ObjectNode).put("skillId", skillId) //请求json中加上一个skillid
                            conditionRuleArray.put(JSONObject(node.toString()))
                            isFindSkillValue = true
                            break
                        }
                    }
                }
                if (!isFindSkillValue) {
                    resList.add(
                        IMResItem(
                            reqConditionSkillValue,
                            1,
                            "配置找不到此状态条件SkillValue，请核实全整正确，或为空，或为unsupport"
                        )
                    )
                }
            }

            // 动作
            val reqSequence = reqJsonRootNode.path("sequence")
            for (reqActionNode in reqSequence) {
                isFindSkillValue = false
                val reqChildActionNode = reqActionNode.path("action")
                val reqActionSkillValue = reqChildActionNode.path("skillValue").asText()
                if (!TextUtils.isEmpty(reqActionSkillValue) && !"unsupport".equals(
                            reqActionSkillValue, ignoreCase = true
                        )
                ) {

                    // 请求json的 某个action 在rule表中查找
                    //1、先在特殊配置rule表查找
                    var isFindFromConfig = false
                    for (node in mConfigActionFileNode!!) { //特殊配置
                        val skillValue = node.path("skillValue").asText()
                        if (TextUtils.equals(reqActionSkillValue, skillValue)) { //是特殊的skillValue
                            isFindSkillValue = true
                            isFindFromConfig = true
                            for (carType in carTypes) { //本车配置对应config
                                val carConfigNode: JsonNode = node.path(carType)
                                //找到这动作 在此车的能力配置
                                if (carConfigNode != null && carConfigNode.has("skillId")) {
                                    val skillId = Integer.decode(carConfigNode.path("skillId").asText())
                                    (reqChildActionNode as ObjectNode).put(
                                        "skillId", skillId
                                    ) //请求json中加上一个skillid

                                    val objectNode = node as ObjectNode
                                    carTypes.forEach {
                                        objectNode.remove(it)
                                    }
                                    //replace是put的替代
                                    objectNode.replace("skillId", carConfigNode.path("skillId"))
                                    objectNode.replace("category", carConfigNode.path("category"))
                                    objectNode.replace("desc", carConfigNode.path("desc"))
                                    objectNode.replace("input", carConfigNode.path("input"))

                                    actionRuleArray.put(JSONObject(objectNode.toString()))
                                    break
                                }
                            }
                            break
                        }
                    }
                    if (!isFindFromConfig) { //没找到特殊的
                        for (node in mCommonActionFileNode!!) {
                            val skillValue = node.path("skillValue").asText()
                            if (TextUtils.equals(reqActionSkillValue, skillValue)) { //是普通的skillValue
                                val skillId = Integer.decode(node.path("skillId").asText())
                                (reqChildActionNode as ObjectNode).put("skillId", skillId) //请求json中加上一个skillid
                                actionRuleArray.put(JSONObject(node.toString()))
                                isFindSkillValue = true
                                break
                            }
                        }
                    }
                }
                if (!isFindSkillValue) {
                    resList.add(
                        IMResItem(
                            reqActionSkillValue, 1, "配置找不到此动作SkillValue，请核实全整正确，或为空，或为unsupport"
                        )
                    )
                }
            }
            //ruleJson 装conditionRuleArray ，actionRuleArray  在配置文件中找到的 条件和动作
            ruleJson.put("condition", conditionRuleArray)
            ruleJson.put("action", actionRuleArray)
            return Req2NodeARule(reqJsonRootNode, ruleJson)

            //            //参数转换
            //            val info = async { paramTransformation(reqJsonRootNode, ruleJson) }.await()
            //            continuationS.resume(info)
        } catch (e: Exception) {
            e.printStackTrace()
            CommonLogUtils.logE(TAG, "10008协议 场景中间层转义错误2:${e.message}")
            //            continuationS.resume(null)
            return null
        }
    }


    /**参数转换*/
    suspend fun paramTransformation(reqJsonRootNode: JsonNode, ruleJson: JSONObject): String? {
        return suspendCancellableCoroutine { continuationST ->
            val job = CoroutineScope(Dispatchers.IO).launch {
                CommonLogUtils.logI(
                    TAG, TimeUtils.millis2String(
                        System.currentTimeMillis(), "HH:mm:ss:SSS"
                    ) + " 10008协议 参数转换前的json：" + reqJsonRootNode
                )
                // 在配置文件中找到了 条件和动作
                val ruleConditionArrayNode: JsonNode?
                val ruleActionArrayNode: JsonNode?
                try {
                    val ruleJsonNode = ObjectMapper().readTree(ruleJson.toString())
                    ruleConditionArrayNode = ruleJsonNode.path("condition")
                    ruleActionArrayNode = ruleJsonNode.path("action")
                } catch (e: Exception) {
                    e.printStackTrace()
                    CommonLogUtils.logE(TAG, "10008协议 参数转换错误1:${e.message}")
                    continuationST.resume(null)
                    return@launch
                }

                try {
                    val objectArgs = reqJsonRootNode as ObjectNode

                    // 触发条件检查
                    val reqEdgeCondition = reqJsonRootNode.path("edgeCondition")
                    var isRet: Boolean = async {
                        checkInputNode(ruleConditionArrayNode!!, reqEdgeCondition)
                    }.await()
                    if (!isRet) {
                        objectArgs.remove("edgeCondition")
                    }

                    // 状态条件检查
                    val reqConditions = reqJsonRootNode.path("conditions")
                    val condNodeIterator = reqConditions.elements()
                    while (condNodeIterator.hasNext()) {
                        val reqConditionNode = condNodeIterator.next()
                        isRet = async { checkInputNode(ruleConditionArrayNode!!, reqConditionNode) }.await()
                        if (!isRet) {
                            condNodeIterator.remove()
                        }
                    }

                    // 动作
                    val reqSequence = reqJsonRootNode.path("sequence")
                    val actionNodeIterator = reqSequence.elements()
                    while (actionNodeIterator.hasNext()) {
                        val actionNode = actionNodeIterator.next()
                        val reqChildActionNode = actionNode.path("action")
                        isRet = async { checkInputNode(ruleActionArrayNode!!, reqChildActionNode) }.await()
                        if (!isRet) {
                            actionNodeIterator.remove()
                        }
                    }

                    CommonLogUtils.logI(TAG, "10008协议，参数转换paramTransformation方法结束。")
                    continuationST.resume(reqJsonRootNode.toString())
                    //                    val info = GsonUtils.fromJson(reqJsonRootNode.toString(), ScenarioInfo::class.java)
                    //                    continuationST.resume(info)
                    return@launch
                } catch (e: Exception) {
                    e.printStackTrace()
                    CommonLogUtils.logE(TAG, "10008协议 参数转换错误2:${e.message}")
                    continuationST.resume(null)
                    return@launch
                }
            }
            // 当外部协程取消时，取消内部协程
            continuationST.invokeOnCancellation {
                CommonLogUtils.logI(TAG, "paramTransformation当外部协程取消时，取消内部协程")
                job.cancel()
            }
        }

    }

    /***
     * ruleNode 已经筛出的条件或动作的rule 数组
     * reqNode  req输入的 单个条件或动作
     */
    private suspend fun checkInputNode(ruleArrayNode: JsonNode, reqItemNode: JsonNode): Boolean {
        return suspendCancellableCoroutine { continuationA ->
            val job = CoroutineScope(Dispatchers.IO).launch {
                if (reqItemNode.isEmpty) {
                    continuationA.resume(false)
                    return@launch
                } else {
                    // skillValue
                    val reqSkillValue = reqItemNode.path("skillValue").asText()
                    if (TextUtils.isEmpty(reqSkillValue) || "unsupport".equals(reqSkillValue, ignoreCase = true)) {
                        continuationA.resume(false)
                        return@launch
                    } else {
                        // desc：如果规则是desc包含一个/多个占位%d，对应取值规则且是range，则需要结合ivi取值范围校验
                        val reqCategory = reqItemNode.path("category").asText()
                        val reqDesc = reqItemNode.path("desc").asText()

                        // start 讯飞语义数据给的有问题，暂时转换一下 ---------------------
                        if (TextUtils.isEmpty(reqDesc)) {
                            (reqItemNode as ObjectNode).put("desc", reqCategory)
                        }
                        // end 讯飞语义数据给的有问题，暂时转换一下---------------------

                        // input arg数组
                        val reqInputList = reqItemNode.path("input")

                        // 匹配规则节点
                        for (ruleItemNode in ruleArrayNode) {
                            // 找到和规则树对应的节点
                            if (TextUtils.equals(reqSkillValue, ruleItemNode.path("skillValue").asText())) {
                                // 规则category 卡片第一行文言 需要用配置json的
                                var ruleCategory = ruleItemNode.get("category").asText()
                                (reqItemNode as ObjectNode).put("category", ruleCategory)
                                // 规则节点desc 卡片第二行文言 根据RuleInputMethod枚举值,现在就要赋值防止无参数的desc不对
                                var ruleDesc = ruleItemNode.path("desc").asText()
                                reqItemNode.put("desc", ruleDesc)

                                // 遍历input arg数组与规则节点的input数组，两两比较参数校验
                                val ruleInputList = ruleItemNode.path("input")
                                // 判断input个数是否相同
                                if (ruleInputList.size() != reqInputList.size()) {
                                    resList.add(
                                        IMResItem(
                                            reqSkillValue,
                                            1,
                                            "转换失败，输入的Input里的元素个数不对，需要" + ruleInputList.size() + "个"
                                        )
                                    )
                                    continuationA.resume(false)
                                    return@launch
                                }
                                //todo 参数名不对情况没有提示
                                for (ruleInput in ruleInputList) {
                                    for (reqInput in reqInputList) {
                                        val reqInputName = reqInput.path("name").asText()
                                        val ruleInputName = ruleInput.path("name").asText()
                                        if (TextUtils.equals(reqInputName, ruleInputName)) {

                                            var ret: Boolean = changeType(reqInput)
                                            if (!ret) {
                                                resList.add(
                                                    IMResItem(
                                                        reqSkillValue,
                                                        1,
                                                        "转换失败，输入的type类型不是规定的12个类型之一"
                                                    )
                                                )
                                                continuationA.resume(false)
                                                return@launch
                                            }


                                            // 数据类型检查
                                            val reqInputType = reqInput.path("type").asText() //已经转正确了
                                            val ruleInputType = ruleInput.path("type").asText()
                                            if (!TextUtils.isEmpty(ruleInputType)) {
                                                ret = checkDataType(ruleInputType, reqInputType)
                                                if (!ret) {
                                                    resList.add(
                                                        IMResItem(
                                                            reqSkillValue,
                                                            1,
                                                            "转换失败，type类型不符合，需要【$ruleInputType】"
                                                        )
                                                    )
                                                    continuationA.resume(false)
                                                    return@launch
                                                }
                                            }
                                            val ruleInputDesc = ruleInput.path("desc").asText()
                                            val ruleInputMethod = ruleInput.path("method").asText()
                                            val ruleInputValue = ruleInput.path("value").asText()
                                            val reqInputValue = reqInput.path("value").asText()
                                            if (RuleInputMethod.ENUM.value.equals(ruleInputMethod, ignoreCase = true)) {
                                                ret = checkEnum(ruleInputValue, reqInputValue)
                                                if (!ret) {
                                                    resList.add(
                                                        IMResItem(
                                                            reqSkillValue,
                                                            1,
                                                            "转换失败，enum数值超出，参考【$ruleInputDesc】"
                                                        )
                                                    )
                                                    continuationA.resume(false)
                                                    return@launch
                                                }
                                                //把inputArg的参数值对应的文言
                                                val enum2DescStr = ruleInput.path("valueMap")
                                                        .path(reqInputValue)
                                                        .asText()
                                                //请求的inputArg的参数名
                                                val name = reqInput.path("name").asText()
                                                //定位请求的参数名，把参数名替换为参数值对应文言
                                                if (ruleCategory.contains(name)) {
                                                    ruleCategory = ruleCategory.replace("#$name#", enum2DescStr)
                                                    reqItemNode.put("category", ruleCategory)
                                                } else {
                                                    ruleDesc = ruleDesc.replace("#$name#", enum2DescStr)
                                                    reqItemNode.put("desc", ruleDesc)
                                                }

                                            } else if (RuleInputMethod.RANGE.value.equals(
                                                        ruleInputMethod, ignoreCase = true
                                                    )
                                            ) {
                                                if (!TextUtils.isEmpty(ruleInputValue)) {
                                                    //不再使用ivi_value,语义表直接用智慧场景范围
                                                    //val ruleInputIviValue = ruleInput.path("ivi_value").asText()
                                                    ret = checkRange(ruleInputValue, reqInputValue)
                                                    if (!ret) {
                                                        resList.add(
                                                            IMResItem(
                                                                reqSkillValue,
                                                                1,
                                                                "转换失败，range数值超出，参考【$ruleInputDesc】"
                                                            )
                                                        )
                                                        continuationA.resume(false)
                                                        return@launch
                                                    }
                                                    //if (!TextUtils.isEmpty(ruleInputIviValue)) {
                                                    //    changeIviRange(ruleInputIviValue, reqInput)
                                                    //}
                                                    val name = reqInput.path("name").asText()
                                                    var value = reqInput.path("value").asText()
                                                    if (reqSkillValue == "condition.common.Time" // 时间点
                                                            || reqSkillValue == "condition.navi.ArrivalTimeLess"  //导航预计到达时间 小于
                                                            || reqSkillValue == "condition.navi.ArrivalTimeGreater" //导航预计到达时间 大于
                                                            || reqSkillValue == "condition.common.TimeRanges" //时间区间
                                                    ) {
                                                        value =
                                                            String.format(Locale.getDefault(), "%02d", value.toInt())
                                                    }
                                                    ruleDesc = ruleDesc.replace("#$name#", value) //ruleDesc是字符串，只是字符替换
                                                    reqItemNode.put("desc", ruleDesc)

                                                    if (name == "day" && reqSkillValue == "condition.common.Date") { //指定日期
                                                        val year =
                                                            ruleDesc.subSequence(0, ruleDesc.indexOf("年")).toString()
                                                                    .toInt()
                                                        val mouth = ruleDesc.subSequence(
                                                            ruleDesc.indexOf("年") + 1, ruleDesc.indexOf("月")
                                                        ).toString().toInt()
                                                        val day = ruleDesc.subSequence(
                                                            ruleDesc.indexOf("月") + 1, ruleDesc.indexOf("日")
                                                        ).toString().toInt()
                                                        ret = TimeUtils.isDateValid(year, mouth, day)

                                                        if (!ret) {
                                                            resList.add(
                                                                IMResItem(
                                                                    reqSkillValue, 1, "不存在此日期【${ruleDesc}】"
                                                                )
                                                            )
                                                            continuationA.resume(false)
                                                            return@launch
                                                        } else {
                                                            reqItemNode.put(
                                                                "desc", "${year}年${
                                                                    String.format(
                                                                        Locale.getDefault(), "%02d", mouth
                                                                    )
                                                                }月${String.format(Locale.getDefault(), "%02d", day)}日"
                                                            )
                                                        }
                                                    } else if (name == "day" && reqSkillValue == "condition.common.OnceEveryWeek") { //周期
                                                        val mDaySkillList = mutableListOf(
                                                            SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_ONE_ID,
                                                            SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_TWO_ID,
                                                            SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_THREE_ID,
                                                            SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_FOUR_ID,
                                                            SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_FIVE_ID,
                                                            SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_SIX_ID,
                                                            SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_SEVEN_ID,
                                                        )

                                                        val selectIndexs = arrayListOf<Int>()
                                                        mDaySkillList.forEachIndexed { index, itemSkillId ->
                                                            if (isSelectedDay(value.toInt(), itemSkillId)) {
                                                                selectIndexs.add(index)
                                                            }
                                                        }
                                                        val meizhou = getSelectDayDescAndSkillIds(selectIndexs)
                                                        reqItemNode.put("desc", meizhou)
                                                    } else if (name == "second" && reqSkillValue == "action.common.Delay") { //延时

                                                        val hms: TimeUtils.HMS = TimeUtils.secondToHMS(value.toInt())
                                                        val hour = String.format(Locale.getDefault(), "%02d", hms.h)
                                                        val minutes = String.format(Locale.getDefault(), "%02d", hms.m)
                                                        val second = String.format(Locale.getDefault(), "%02d", hms.s)

                                                        val desc =
                                                            if (hour == "00" && minutes == "00" && second == "00") {
                                                                "无延时"
                                                            } else if (hour == "00" && minutes == "00") {
                                                                second + "秒"
                                                            } else if (hour == "00") {
                                                                minutes + "分" + second + "秒"
                                                            } else {
                                                                hour + "时" + minutes + "分" + second + "秒"
                                                            }
                                                        reqItemNode.put("desc", desc)
                                                    } else if (name == "stop_second" && reqSkillValue == "condition.common.TimeRanges") { //时间区间
                                                        var isShowCi = false
                                                        var arr = ruleDesc.split(":")
                                                        val mStartHour = arr[0]
                                                        val mEndMinute = arr[2]
                                                        arr = arr[1].split("~")
                                                        val mStartMinute = arr[0]
                                                        val mEndHour = arr[1]
                                                        if (mStartHour == mEndHour) {
                                                            isShowCi = mStartMinute.toInt() >= mEndMinute.toInt()
                                                        } else if (mStartHour.toInt() > mEndHour.toInt()) {
                                                            isShowCi = true
                                                        }
                                                        if (isShowCi) {
                                                            ruleDesc = ruleDesc.replace("~", "~次日")
                                                            reqItemNode.put("desc", ruleDesc)
                                                        }
                                                    }
                                                }
                                            }
                                            //无需验证，将value通过关键字替换
                                            else if (RuleInputMethod.VALUE2DESC.value.equals(
                                                        ruleInputMethod, ignoreCase = true
                                                    )
                                            ) {
                                                val name = reqInput.path("name").asText()
                                                val value = reqInput.path("value").asText()
                                                ruleDesc = ruleDesc.replace("#$name#", value) //ruleDesc是字符串，只是字符替换
                                                reqItemNode.put("desc", ruleDesc)
                                            }
                                            //需要请求第三方协议
                                            else if (RuleInputMethod.THIRDPARTY.value.equals(
                                                        ruleInputMethod, ignoreCase = true
                                                    )
                                            ) {
                                                val startTime = System.currentTimeMillis()
                                                ret = async {
                                                    thirdPartyRun(
                                                        reqSkillValue,
                                                        ruleDesc,
                                                        reqDesc,
                                                        reqItemNode,
                                                        reqInput,
                                                        ruleInput
                                                    )
                                                }.await()
                                                CommonLogUtils.logI(
                                                    TAG, "10008协议，${ruleInput.path("desc").asText()}协议方法用时：${
                                                        TimeUtils.millis2String(
                                                            System.currentTimeMillis() - startTime, "ss:SSS"
                                                        )
                                                    }"
                                                )
                                                if (!ret) {
                                                    continuationA.resume(false)
                                                    return@launch
                                                }
                                            }
                                        }
                                    }
                                }
                                break //找到了对应的skillvalue
                            }
                        }

                        resList.add(IMResItem(reqSkillValue, 0, "")) //此SkillValue语义转化成功
                        continuationA.resume(true)
                        return@launch
                    }

                }

            }
            // 当外部协程取消时，取消内部协程 https://juejin.cn/post/7299378258529648674
            continuationA.invokeOnCancellation {
                CommonLogUtils.logI(TAG, "checkInputNode当外部协程取消时，取消内部协程")
                job.cancel()
            }
        }
    }

    /**第三方协议方法*/
    private suspend fun thirdPartyRun(
        reqSkillValue: String,
        ruleDesc: String,
        reqDesc: String,
        reqItemNode: JsonNode,
        reqInput: JsonNode,
        ruleInput: JsonNode
    ): Boolean {
        return suspendCancellableCoroutine { continuationB ->
            val job = CoroutineScope(Dispatchers.IO).launch {

                val ruleInputDesc = ruleInput.path("desc").asText()
                if (!TextUtils.isEmpty(ruleDesc) || (!TextUtils.isEmpty(reqDesc) && reqDesc.contains("%d"))) {
                    //1、获取传进来的参数
                    val reqParams = reqItemNode.path("params")
                    if (reqParams == null || reqParams.size() == 0) {
                        resList.add(IMResItem(reqSkillValue, 1, "转换失败，传入的协议方法 参数个数为0"))
                        continuationB.resume(false)
                        return@launch
                    }
                    var temp = reqParams.filter {
                        it.path("index").asInt() == 0
                    }
                    //1.1、第一个参数
                    val params0 = if (temp.isNotEmpty()) {
                        val a0 = temp[0].path("arg").asText().replace(" ", "")
                        val newA0 = a0.substring(0, min(a0.length, 30))
                        CommonLogUtils.logD(TAG, "第三方 第一个参数原：${a0},截取后：${newA0}")
                        newA0
                    } else {
                        ""
                    }
                    when (ruleInputDesc) {
                        //搜索动作的地址
                        "searchAddress" -> {
                            //1.2、第二个参数
                            temp = reqParams.filter {
                                it.path("index").asInt() == 1
                            }
                            val params1 = if (temp.isNotEmpty()) {
                                val a1 = temp[0].path("arg").asText().replace(" ", "")
                                val newA1 = a1.substring(0, min(a1.length, 30))
                                CommonLogUtils.logD(TAG, "第三方 第二个参数原：${a1},截取后：${newA1}")
                                newA1
                            } else {
                                ""
                            }

                            val planRoute = async {
                                searchAddress(params0, params1)
                            }.await()
                            if (planRoute.endPoi == null || planRoute.endPoi?.name == null) {
                                resList.add(IMResItem(reqSkillValue, 1, "找不到目标地：${params0}，为空null"))
                                continuationB.resume(false)
                                return@launch
                            }

                            var newReqDesc = if (TextUtils.isEmpty(params1)) planRoute.endPoi?.name!! else "${
                                planRoute.midPois?.get(0)?.name
                            }+${planRoute.endPoi?.name!!}"
                            val name = ruleInput.path("name").asText()
                            newReqDesc = ruleDesc.replace("#$name#", newReqDesc) //ruleDesc是字符串，只是字符替换
                            //卡片第二行
                            (reqItemNode as ObjectNode).put("desc", newReqDesc)

                            val jsonInputData = Gson().toJson(planRoute)
                            val address4Base64 = EncodeUtils.base64Encode2String(jsonInputData.toByteArray())
                            (reqInput as ObjectNode).put("value", address4Base64)
                        }
                        //搜索歌手
                        "searchSinger" -> {
                            val dataList = async {
                                MediaLocalManager.searchSinger(params0)
                            }.await()
                            if (dataList.isNullOrEmpty()) {
                                resList.add(IMResItem(reqSkillValue, 1, "找不到歌手，为空null"))
                                continuationB.resume(false)
                                return@launch
                            }
                            var newReqDesc = dataList.get(0).singer_name!!
                            val name = ruleInput.path("name").asText()
                            newReqDesc = ruleDesc.replace("#$name#", newReqDesc) //ruleDesc是字符串，只是字符替换
                            //卡片第二行
                            (reqItemNode as ObjectNode).put("desc", newReqDesc)
                            (reqInput as ObjectNode).put("value", dataList.get(0).singer_id!!) //歌手id
                        }
                        //搜索歌曲
                        "searchSong" -> {
                            val dataList = async {
                                MediaLocalManager.searchSong(params0)
                            }.await()
                            if (dataList.isNullOrEmpty()) {
                                resList.add(IMResItem(reqSkillValue, 1, "找不到歌曲，为空null"))
                                continuationB.resume(false)
                                return@launch
                            }
                            var newReqDesc = dataList.get(0).song_name!!
                            val name = ruleInput.path("name").asText()
                            newReqDesc = ruleDesc.replace("#$name#", newReqDesc) //ruleDesc是字符串，只是字符替换
                            //卡片第二行
                            (reqItemNode as ObjectNode).put("desc", newReqDesc)
                            (reqInput as ObjectNode).put("value", dataList.get(0).song_id!!) //歌手id
                        }
                        //搜索条件的地址
                        "searchConditionAddress" -> {
                            val endPoiInfo = if (params0 == "家" || params0 == "回家") {
                                val resHomeListBean = async {
                                    NaviManager.getFavoriteList(1)
                                }.await()
                                if (resHomeListBean == null || !resHomeListBean.isSuccess() || resHomeListBean.protocolFavPoiInfos.isNullOrEmpty()) {
                                    resList.add(IMResItem(reqSkillValue, 1, "找不到家，为空null"))
                                    continuationB.resume(false)
                                    return@launch
                                }
                                val homeBean = resHomeListBean.protocolFavPoiInfos?.get(0)
                                RoutePoi(
                                    "家", homeBean?.favoriteAddress, homeBean?.lon ?: 0.0,
                                    homeBean?.lat ?: 0.0, homeBean?.itemId, homeBean?.itemId, homeBean?.itemId,
                                )
                            } else if (params0 == "公司" || params0 == "回公司") {
                                val resCompanyListBean = async {
                                    NaviManager.getFavoriteList(2)
                                }.await()
                                if (resCompanyListBean == null || !resCompanyListBean.isSuccess() || resCompanyListBean.protocolFavPoiInfos.isNullOrEmpty()) {
                                    resList.add(IMResItem(reqSkillValue, 1, "找不到公司，为空null"))
                                    continuationB.resume(false)
                                    return@launch
                                }
                                val companyListBean = resCompanyListBean.protocolFavPoiInfos?.get(0)

                                RoutePoi(
                                    "公司",
                                    companyListBean?.favoriteAddress,
                                    companyListBean?.lon ?: 0.0,
                                    companyListBean?.lat ?: 0.0,
                                    companyListBean?.itemId,
                                    companyListBean?.itemId,
                                    companyListBean?.itemId,
                                )
                            } else if ("收藏夹" == params0) {
                                val resFavoriteListBean = async {
                                    NaviManager.getFavoriteList(0)
                                }.await()
                                if (resFavoriteListBean == null || !resFavoriteListBean.isSuccess() || resFavoriteListBean.protocolFavPoiInfos.isNullOrEmpty()) {
                                    resList.add(IMResItem(reqSkillValue, 1, "找不到收藏夹，为空null"))
                                    continuationB.resume(false)
                                    return@launch
                                }
                                val companyListBean = resFavoriteListBean.protocolFavPoiInfos?.get(0)

                                RoutePoi(
                                    companyListBean?.favoriteName,
                                    companyListBean?.favoriteAddress,
                                    companyListBean?.lon ?: 0.0,
                                    companyListBean?.lat ?: 0.0,
                                    companyListBean?.itemId,
                                    companyListBean?.itemId,
                                    companyListBean?.itemId,
                                )
                            } else {
                                val endPoiInfo = async {
                                    SearchAddressManager.searchAddressByIM(params0)
                                }.await()
                                if (endPoiInfo == null) {
                                    resList.add(IMResItem(reqSkillValue, 1, "找不到选点：${params0}，为空null"))
                                    continuationB.resume(false)
                                    return@launch
                                }
                                RoutePoi(
                                    endPoiInfo.name,
                                    endPoiInfo.addr,
                                    endPoiInfo.point?.lon ?: 0.0,
                                    endPoiInfo.point?.lat ?: 0.0,
                                    endPoiInfo.typeCode,
                                    endPoiInfo.adCode,
                                    endPoiInfo.pid,
                                )
                            }
                            val lat = endPoiInfo.lat
                            val lon = endPoiInfo.lon
                            val addressName = endPoiInfo.name

                            var newReqDesc = reqInput.path("value").asText()
                            val name = ruleInput.path("name").asText()
                            newReqDesc = addressName + ruleDesc.replace("#$name#", newReqDesc) //ruleDesc是字符串，只是字符替换
                            //卡片第二行
                            (reqItemNode as ObjectNode).put("desc", newReqDesc)

                            val reqInputList = reqItemNode.path("input")

                            var isHasLatLon = false
                            for (reqInputListItem in reqInputList) {
                                val reqInputListItemName = reqInputListItem.path("name").asText()
                                if (reqInputListItemName == "latitude") {
                                    isHasLatLon = true
                                    (reqInputListItem as ObjectNode).put("value", lat)
                                }
                            }
                            if (!isHasLatLon) {
                                resList.add(IMResItem(reqSkillValue, 1, "没有latitude字段"))
                                continuationB.resume(false)
                                return@launch
                            }
                            isHasLatLon = false
                            for (reqInputListItem in reqInputList) {
                                val reqInputListItemName = reqInputListItem.path("name").asText()
                                if (reqInputListItemName == "longitude") {
                                    isHasLatLon = true
                                    (reqInputListItem as ObjectNode).put("value", lon)
                                }
                            }
                            if (!isHasLatLon) {
                                resList.add(IMResItem(reqSkillValue, 1, "没有longitude字段"))
                                continuationB.resume(false)
                                return@launch
                            }
                        }
                    }
                    continuationB.resume(true)
                    return@launch
                } else {
                    resList.add(IMResItem(reqSkillValue, 1, "转换失败，传入的Desc和配置文件里都没有设置%d"))
                    continuationB.resume(false)
                    return@launch
                }
            }
            // 当外部协程取消时，取消内部协程
            continuationB.invokeOnCancellation {
                CommonLogUtils.logI(TAG, "thirdPartyRun当外部协程取消时，取消内部协程")
                job.cancel()
            }
        }
    }


    private suspend fun searchAddress(params0: String, params1: String): ReqPlanRoute {
        return suspendCancellableCoroutine { continuation ->
            val job = CoroutineScope(Dispatchers.IO).launch {
                //                kotlinx.coroutines.delay(10000)
                //途经点
                val value1 = if (!TextUtils.isEmpty(params1)) {
                    async {
                        SearchAddressManager.searchAddressByIM(params1)
                    }.await()
                } else {
                    null
                }
                //protocolFavPoiInfos有越界崩溃，还原记得修复
                //if (params0 == "家" || params0 == "回家") {
                //    val favoriteListBean = async {
                //        NaviManager.getFavoriteList(1)
                //    }.await()
                //    val favoriteItemInfo = favoriteListBean?.protocolFavPoiInfos?.get(0)
                //    val value0 = favoriteItemInfo?.toRoutePoi()
                //    val planRoute = getPlanRoute(value0, value1)
                //    continuation.resume(planRoute)
                //} else if (params0 == "公司" || params0 == "回公司") {
                //    val favoriteListBean = async {
                //        NaviManager.getFavoriteList(2)
                //    }.await()
                //    val favoriteItemInfo = favoriteListBean?.protocolFavPoiInfos?.get(0)
                //    val value0 = favoriteItemInfo?.toRoutePoi()
                //    val planRoute = getPlanRoute(value0, value1)
                //    continuation.resume(planRoute)
                //} else {
                val value0 = async {
                    SearchAddressManager.searchAddressByIM(params0)
                }.await()
                val planRoute = getPlanRoute(value0, value1)
                continuation.resume(planRoute)
                //}
            }
            // 当外部协程取消时，取消内部协程
            continuation.invokeOnCancellation {
                CommonLogUtils.logI(TAG, "searchAddress当外部协程取消时，取消内部协程")
                job.cancel()
            }
        }
    }

    //改变type字符串为对应的int
    private fun changeType(reqInput: JsonNode): Boolean {
        val reqType = reqInput.path("type").asText() //可能会给到数字 或 文字
        val reqArgType = IMArgType.fromIntStr(reqType.uppercase(Locale.getDefault()))
        if (reqArgType != null) { //不为空表示 输入的是正规的类型
            (reqInput as ObjectNode).put("type", reqArgType.name)
            return true
        }
        return false
    }


    //ruleInputType 为 int 这类
    //reqInputType 为INT32  前面已经转正确了
    private fun checkDataType(ruleInputType: String, reqInputType: String): Boolean {
        val ruleArgType = IMArgType.fromIntStr(ruleInputType.uppercase(Locale.getDefault()))
        val reqArgType = IMArgType.fromIntStr(reqInputType.uppercase(Locale.getDefault()))
        if (ruleArgType == null || reqArgType == null) return false
        //如果类型一，都是int类型，需要浮点传入数字类型 判断通过
        return (ruleArgType == reqArgType) || (IMArgType.isIntType(ruleArgType) && IMArgType.isIntType(reqArgType))
                || (IMArgType.isFloatType(ruleArgType) && IMArgType.isNumberType(reqArgType))
    }

    private fun checkEnum(ruleInputValue: String, reqInputValue: String): Boolean {
        val contains = Arrays.asList(*ruleInputValue.split("_".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray())
                .contains(reqInputValue)
        return contains
    }

    private fun checkRange(ruleInputValue: String, reqInputValue: String): Boolean {
        // 转换输入值为 double 类型
        val inputValue = reqInputValue.toDouble()
        val parts = ruleInputValue.split("_".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
        if (parts.size != 2) {
            return false
        }
        // 将分隔得到的边界值转换为 double 类型
        val min = parts[0].toDouble()
        val max = parts[1].toDouble()
        val contains = inputValue >= min && inputValue <= max
        // 检查输入值是否在范围内
        return contains
    }

    /** 检查IVI取值范围节点，超出上限或下限，取边界值*/
    private fun changeIviRange(ruleInputIviValue: String, reqInput: JsonNode): Boolean {
        if (reqInput.isEmpty) {
            return false
        }
        val parts = ruleInputIviValue.split("_".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
        if (parts.size != 2) {
            return false
        }
        val min = parts[0].toDouble()
        val max = parts[1].toDouble()
        val value = reqInput.path("value").asText()
        val reqValue = value.toDouble()
        if (reqValue < min) {
            (reqInput as ObjectNode).put("value", parts[0])
            return true
        } else if (reqValue > max) {
            (reqInput as ObjectNode).put("value", parts[1])
            return true
        }
        return false
    }

    /**
     * 过滤能力并打开编辑页， 返回ArrayList<Int>（条件支持状态，动作支持状态） -1其他(没有动作或条件时回传),0不支持,1部分支持,2全部支持
     */
    fun filterAndOpenEdit(applicationContext: Context, scenarioInfo: ScenarioInfo): IMRes {
        //1.1、获取进来时条件数和动作数
        val enterConditionNum = getConditionNum(scenarioInfo)
        val enterSequenceNum = getSequenceNum(scenarioInfo)
        //1.2、过滤不可用的能力
        val edgeCondition: ScenarioInfo.Condition? = scenarioInfo.edgeCondition
        val conditions: List<ScenarioInfo.Condition>? = scenarioInfo.conditions
        val sequence: List<ScenarioInfo.Sequence>? = scenarioInfo.sequence

        edgeCondition?.let { edge ->
            if (ObjectUtils.checkScenarioDataIsInvalid(edge) || !SceneEditManager.checkIdIsActive(
                        edge.skillId, 1
                    )
            ) {
                scenarioInfo.edgeCondition = null
            }
        }
        //状态条件重复的skillid去掉，再过滤
        scenarioInfo.conditions = conditions?.distinctBy { it.skillId }?.filter { con ->
            !ObjectUtils.checkScenarioDataIsInvalid(con) && SceneEditManager.checkIdIsActive(
                con.skillId, 2
            ) && !con.desc.contains("%d")
        }
        scenarioInfo.sequence = sequence?.filter { seq ->
            val a1 = !ObjectUtils.checkScenarioDataIsInvalid(seq)
            val a2 = SceneEditManager.checkIdIsActive(seq.action.skillId, 0)
            a1 && a2 && !seq.action.desc.contains("%d")
        }
        //1.3、数量上限
        scenarioInfo.conditions?.let {
            scenarioInfo.conditions = it.subList(0, min(it.size, 5))
        }
        scenarioInfo.sequence?.let {
            scenarioInfo.sequence = it.subList(0, min(it.size, 30))
        }

        //2、设置初始值
        if (scenarioInfo.scenarioName.length < 3) { //场景名称在3-10 ，少于的话用默认名称
            scenarioInfo.scenarioName = SceneManager.getNewSceneName(
                applicationContext.getString(R.string.scene_text_default_scene_name),
                applicationContext.getString(R.string.scene_text_common_name)
            )
        } else {
            scenarioInfo.scenarioName =
                scenarioInfo.scenarioName.replace(" ", "").substring(0, min(scenarioInfo.scenarioName.length, 10))
        }
        scenarioInfo.scenarioDesc = ""
        scenarioInfo.scenarioId = SceneManager.getNewSceneId()
        scenarioInfo.version = BuildConfig.VERSION_CODE
        scenarioInfo.scenarioTimeStamp = 0
        scenarioInfo.imgPath = ""
        scenarioInfo.autoExeFlag = 1
        scenarioInfo.secondAskeFlag = 1
        if (getConditionNum(scenarioInfo) > 0) { //过滤后有条件
            scenarioInfo.autoExeFlag = 0
            scenarioInfo.secondAskeFlag = 0  //0开1关
        }
        //3 、获取条件和动作的匹配情况
        val conditionMatch = matchNum(getConditionNum(scenarioInfo), enterConditionNum)
        val sequenceMatch = matchNum(getSequenceNum(scenarioInfo), enterSequenceNum)

        // 4、跳转到创建场景页
        SceneNewEditActivity.launchSceneEditWithMyScene(
            applicationContext, enterBean = MySceneBean(
                scenario = ScenarioBean(
                    scenarioInfo = scenarioInfo,
                    isAskBeforeAutoRun = (scenarioInfo.secondAskeFlag == 0),
                    isTemplateScene = false,
                    isAutoRun = (scenarioInfo.autoExeFlag == 0),
                    executeFrequency = 0
                ), isSceneStart = false, isTop = false, isAddNewScene2ShowAnima = true, isAddScene = true
            ), enterType = SceneNewEditEnterType.TTS, scenario_L3id = ""
        )
        return IMRes(
            conditionMatch = conditionMatch, sequenceMatch = sequenceMatch, resList = resList
        )
    }

    /**获取条件的总数*/
    private fun getConditionNum(bean: ScenarioInfo): Int {
        var num = 0
        if (bean.edgeCondition != null) {
            num++
        }
        if (bean.conditions != null) {
            num += bean.conditions.size
        }
        return num
    }

    /**获取动作的总数*/
    private fun getSequenceNum(bean: ScenarioInfo): Int {
        var num = 0
        if (bean.sequence != null) {
            num += bean.sequence.size
        }
        return num
    }

    /**匹配情况 -1其他  0不⽀持 1部分支持 2全部支持*/
    private fun matchNum(curNum: Int, enterNum: Int): Int {
        if (enterNum == 0) return -1
        if (curNum == 0) return 0
        if (curNum < enterNum) return 1
        if (curNum == enterNum) return 2
        return -1
    }


    fun getTestDataJson(): String {
        val json = GsonUtils.toJson(IMScenarioInfo.getTestData())
        return json
    }

    private fun getPlanRoute(endPoiInfo: PoiItem?, midPoiInfo: PoiItem?): ReqPlanRoute {
        val endRouteBean = RoutePoi(
            endPoiInfo?.name,
            endPoiInfo?.addr,
            endPoiInfo?.point?.lon ?: 0.0,
            endPoiInfo?.point?.lat ?: 0.0,
            endPoiInfo?.typeCode,
            endPoiInfo?.adCode,
            endPoiInfo?.pid
        )
        return getPlanRoute(endRouteBean, midPoiInfo)
    }

    private fun getPlanRoute(endRouteBean: RoutePoi?, midPoiInfo: PoiItem?): ReqPlanRoute {
        val midPois = if (midPoiInfo != null) {
            val midRouteBean = RoutePoi(
                midPoiInfo.name,
                midPoiInfo.addr,
                midPoiInfo.point?.lon ?: 0.0,
                midPoiInfo.point?.lat ?: 0.0,
                midPoiInfo.typeCode,
                midPoiInfo.adCode,
                midPoiInfo.pid
            )
            arrayListOf(midRouteBean)
        } else {
            null
        }

        return ReqPlanRoute(endPoi = endRouteBean, midPois = midPois)
    }

    fun test(context: Context, activity: MainActivity) {
        val startTime = System.currentTimeMillis()
        var scenarioInfoJson = getTestDataJson()
        //1、语义转换
        val req2NodeARule = semanticTransformation(context, scenarioInfoJson)
        if (req2NodeARule == null) { //已经有打印
            val imRes = IMRes(
                result = 1, failReason = "场景中间层转义错误", resList = resList
            )
            CommonLogUtils.logE(
                TAG, "10008协议，语义转换失败：${imRes.toString()},总用时：" + TimeUtils.millis2String(
                    System.currentTimeMillis() - startTime, "ss:SSS"
                )
            )
            return
        }
        activity.lifecycleScope.launch(Dispatchers.IO) {
            val timeOut = IMScenarioInfo.timeOutSecond
            val job = launch {
                //2、参数转换
                paramTransformation(
                    req2NodeARule.reqJsonRootNode!!, req2NodeARule.ruleJson!!
                )
                withContext(Dispatchers.Main) {
                    //3、转换成功 得出ScenarioInfo
                    val scenarioInfo = GsonUtils.fromJson(
                        req2NodeARule.reqJsonRootNode.toString(), ScenarioInfo::class.java
                    )
                    //4、过滤能力，和不规范的条件动作，并打开编辑页
                    val imRes = filterAndOpenEdit(context, scenarioInfo)

                    CommonLogUtils.logI(
                        TAG, "10008协议，在${timeOut}秒内完成，总用时：${
                            TimeUtils.millis2String(
                                System.currentTimeMillis() - startTime, "ss:SSS"
                            )
                        },最终数据为：${req2NodeARule.reqJsonRootNode.toString()}，服务返回：${imRes.toString()}"
                    )

                }
            }
            job.start()
            kotlinx.coroutines.delay(timeOut * 1000L)
            if (!job.isCompleted) { //超过5秒 结束
                job.cancel()
                withContext(Dispatchers.Main) {
                    //3、转换不成功 得出ScenarioInfo
                    val scenarioInfo = GsonUtils.fromJson(
                        req2NodeARule.reqJsonRootNode.toString(), ScenarioInfo::class.java
                    )
                    //4、过滤能力，和不规范的条件动作，并打开编辑页
                    val imRes = filterAndOpenEdit(context, scenarioInfo)
                    imRes.result = 1
                    imRes.failReason = "超时${timeOut}秒"

                    CommonLogUtils.logI(
                        TAG, "10008协议，超过${timeOut}秒没完成，总用时：${
                            TimeUtils.millis2String(
                                System.currentTimeMillis() - startTime, "ss:SSS"
                            )
                        },最终数据为：${req2NodeARule.reqJsonRootNode.toString()}，服务返回：${imRes.toString()}"
                    )
                }
            }
        }
    }

    private fun isSelectedDay(value: Int, dest: Int): Boolean {
        return value and dest == dest
    }

    private fun getSelectDayDescAndSkillIds(selectIndexes: ArrayList<Int>): String {
        val size = selectIndexes.size
        val buffer = StringBuilder()
        buffer.append("每周")

        val checkDataList = arrayListOf<String>("一", "二", "三", "四", "五", "六", "日")
        var position = 0
        selectIndexes.forEach {
            buffer.append(checkDataList[it])
            if (size != position + 1) {
                buffer.append("/")
            }
            position++
        }
        if (position == 7) {
            buffer.delete(0, buffer.length)
            buffer.append("每天")
        }
        return buffer.toString()
    }
}