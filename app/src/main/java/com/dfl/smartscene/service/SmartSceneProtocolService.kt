package com.dfl.smartscene.service

import android.content.Intent
import android.os.IBinder
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import androidx.lifecycle.lifecycleScope
import com.dfl.androhermas.remote.server.HermasProtocolService
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.util.GsonUtils
import com.dfl.android.common.util.JsonUtils
import com.dfl.android.common.util.TimeUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.BuildConfig
import com.dfl.smartscene.bean.intermediate.IMRes
import com.dfl.smartscene.bean.intermediate.IMScenarioInfo
import com.dfl.smartscene.bean.service.SceneNameBean
import com.dfl.smartscene.bean.service.SmartSceneBaseBean
import com.dfl.smartscene.service.intermediate.IntermediateUtils
import com.google.gson.Gson
import com.iauto.scenarioadapter.ScenarioInfo
import com.jeremyliao.liveeventbus.LiveEventBus
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.locks.Lock
import java.util.concurrent.locks.ReentrantLock

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/12/31
 * desc : 提供给对手应用soa
 * version: 1.0
 */
class SmartSceneProtocolService : HermasProtocolService() {
    companion object {
        private const val REQUEST_AUTHOR = "requestAuthor"
        private const val PROTOCOL_ID = "protocolId"
        private const val REQUEST_CODE = "requestCode"
        private const val DATA = "data"
        private const val MESSAGE_TYPE = "messageType"
        private const val DISPATCH_TYPE = "dispatch"
        private const val TAG = GlobalConstant.GLOBAL_TAG.plus("SmartSceneProtocolService")
        private const val MESSAGE_RESPONSE = "response"
    }

    private var mGson: Gson? = null
    private val mLock: Lock = ReentrantLock()
    private var mProtocolMap = ConcurrentHashMap<String, String>()
    private var mLifecycleOwner: ServiceLifecycleOwner? = null

    private fun init() {
        mLifecycleOwner = ServiceLifecycleOwner()
        mGson = Gson()
        //reason:0-获取所有场景后发送的消息;1-新增场景;2-修改场景;3-删除场景;4-单独修改自动执行开关
        LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_CHANGED, Int::class.java)
            .observe(mLifecycleOwner!!) { reason: Int ->
                CoroutineScope(Dispatchers.IO).launch {
                    CommonLogUtils.logD(TAG, "LiveEventBus==KEY_SCENE_CHANGED====>$reason")
                    mLock.lock()
                    try {
                        for ((key, value) in mProtocolMap) {
                            val cookies = key.split("&").toTypedArray()
                            val requestAuthor = cookies[0]
                            val protocolId = cookies[1]
                            val requestCode = cookies[2]
                            if (protocolId == SmartSceneCustomConstant.SMART_SCENE_CHANGED_PROTOCOL_ID.toString()) {
                                //如果是在卡片上自动执行引起的变更则不需要发送数据给语音
                                if (reason == 4) {
                                    continue
                                }
                                val bean = SmartSceneBaseBean(
                                    requestCode,
                                    protocolId.toInt(),
                                    requestAuthor,
                                    DISPATCH_TYPE,
                                    BuildConfig.VERSION_NAME,
                                    HashMap<String, Int>()
                                )
                                val map: HashMap<String, Int> = HashMap()
                                map["result"] = 0
                                bean.data = map
                                val responseJson = mGson!!.toJson(bean).replace("\\\\".toRegex(), "")
                                doResponseStringToClient(responseJson, value)
                            }
                            //							if (protocolId == SmartSceneCustomConstant.SMART_SCENE_DATA_LIST_PROTOCOL_ID.toString()) {
                            //								val bean = SmartSceneBaseBean(
                            //									requestCode,
                            //									protocolId.toInt(),
                            //									requestAuthor,
                            //									DISPATCH_TYPE,
                            //									BuildConfig.VERSION_NAME,
                            //									HashMap<String, Int>()
                            //								)
                            //								val data = SceneVoiceManager.getSceneInfo()
                            //								bean.data = data
                            //								val responseJson =
                            //									mGson!!.toJson(bean).replace("\\\\".toRegex(), "")
                            //								doResponseStringToClient(responseJson, value)
                            //							}
                        }
                    } catch (tr: Throwable) {
                        CommonLogUtils.logE(TAG, "KEY_SCENE_CHANGED==>${tr.message}")
                    } finally {
                        mLock.unlock()
                    }
                }
            }
        LiveEventBus.get<Int>(GlobalLiveEventConstants.KEY_PLAY_MODE_TRY_RUN_SCENE_LISTENER)
            .observe(mLifecycleOwner!!) { reason ->
                reason?.let {
                    CoroutineScope(Dispatchers.IO).launch {
                        CommonLogUtils.logD(TAG, "LiveEventBus==TRY_RUN_SCENE_LISTENER====>$it")
                        mLock.lock()
                        try {
                            val dispatchKey = ArrayList<String>()
                            mProtocolMap.forEach { (key, value) ->
                                val cookies = key.split("&").toTypedArray()
                                val requestAuthor = cookies[0]
                                val protocolId = cookies[1]
                                val requestCode = cookies[2]
                                if (protocolId == SmartSceneCustomConstant.SMART_SCENE_TRY_RUN_SCENE_PROTOCOL_ID.toString()) {
                                    val bean = SmartSceneBaseBean(
                                        requestCode,
                                        protocolId.toInt(),
                                        requestAuthor,
                                        DISPATCH_TYPE,
                                        BuildConfig.VERSION_NAME,
                                        HashMap<String, Int>()
                                    )
                                    val map: HashMap<String, Int> = HashMap()
                                    map["result"] = it
                                    bean.data = map
                                    val responseJson = mGson!!.toJson(bean).replace("\\\\".toRegex(), "")
                                    doResponseStringToClient(responseJson, value)
                                    dispatchKey.add(key)
                                }
                            }
                            if (it == 3) {
                                dispatchKey.forEach {
                                    mProtocolMap.remove(it)
                                }
                            }
                        } catch (tr: Throwable) {
                            CommonLogUtils.logE(TAG, "TRY_RUN_SCENE_LISTENER==>${tr.message}")
                        } finally {
                            mLock.unlock()
                        }
                    }
                }
            }
    }

    private class ServiceLifecycleOwner : LifecycleOwner {
        private val lifecycle = LifecycleRegistry(this)

        init {
            lifecycle.currentState = Lifecycle.State.CREATED
        }

        fun onStart() {
            lifecycle.currentState = Lifecycle.State.STARTED
        }

        fun onStop() {
            lifecycle.currentState = Lifecycle.State.DESTROYED
        }

        override fun getLifecycle(): Lifecycle {
            return lifecycle
        }
    }

    override fun onBind(intent: Intent): IBinder? {
        CommonLogUtils.logE(TAG, "onBind========")
        mLifecycleOwner?.onStart()
        return super.onBind(intent)
    }

    override fun onDestroy() {
        super.onDestroy()
        mLifecycleOwner?.onStop()
        CommonLogUtils.logE(TAG, "onDestroy======")
    }

    override fun onCreate() {
        super.onCreate()
        init()
        CommonLogUtils.logE(TAG, "onCreate=======")
    }

    override fun onDispatchMessage(json: String?, requestCookie: String?) {
        json?.let {
            try {
                CommonLogUtils.logE(TAG, "json===>$json===requestCookie====>$requestCookie")
                requestCookie?.let {
                    val messageType = JsonUtils.getString(json, MESSAGE_TYPE)
                    if (messageType == DISPATCH_TYPE) {
                        val cookie = getCookie(json)
                        if (!mProtocolMap.containsKey(cookie)) {
                            mProtocolMap[cookie] = requestCookie
                        }
                    }
                    doSomeThing(json, requestCookie)
                }
            } catch (tr: Throwable) {
                CommonLogUtils.logE(TAG, tr.message)
            }
        }
    }


    override fun unRegisterCallback(requestCookie: String?) {
        CommonLogUtils.logE(TAG, "unregisterCallBack====>$requestCookie")
        requestCookie?.let {
            val list = ArrayList<String>()
            mProtocolMap.forEach { (key, value) ->
                if (value == it) {
                    list.add(key)
                }
            }
            list.forEach {
                mProtocolMap.remove(it)
            }
        }
    }

    private fun getCookie(json: String): String {
        val cookie: String
        val requestAuthor = JsonUtils.getString(json, REQUEST_AUTHOR)
        val protocolId = JsonUtils.getInt(json, PROTOCOL_ID)
        val requestCode = JsonUtils.getString(json, REQUEST_CODE)
        cookie = "$requestAuthor&$protocolId&$requestCode"
        return cookie
    }

    /***
     * 这里处理 服务方法
     */
    private fun doSomeThing(json: String, requestCookie: String) {
        //do some thing
        CoroutineScope(Dispatchers.IO).launch {
            response(json, requestCookie)
        }
    }

    /***
     * 这里处理 服务方法
     * json : client端 传来的json
     */
    private fun response(json: String, requestCookie: String) {
        CommonLogUtils.logD(TAG, Thread.currentThread().name)
        mLock.lock()
        //            val isDispatch = JsonUtils.getString(json, MESSAGE_TYPE) == DISPATCH_TYPE
        val protocolId = JsonUtils.getInt(json, PROTOCOL_ID) //协议id
        val requestCode = JsonUtils.getString(json, REQUEST_CODE)
        val requestAuthor = JsonUtils.getString(json, REQUEST_AUTHOR)
        val messageType = JsonUtils.getString(json, MESSAGE_TYPE)
        try {
            if (protocolId == SmartSceneCustomConstant.SMART_SCENE_CHANGED_PROTOCOL_ID) {
                val bean = SmartSceneBaseBean(
                    requestCode,
                    protocolId,
                    requestAuthor,
                    messageType,
                    BuildConfig.VERSION_NAME,
                    HashMap<String, Int>()
                )
                val map: HashMap<String, Int> = java.util.HashMap()
                map["result"] = 0
                bean.data = map
                val responseJson = mGson!!.toJson(bean).replace("\\\\".toRegex(), "")
                doResponseStringToClient(responseJson, requestCookie)
            }
            if (protocolId == SmartSceneCustomConstant.SMART_SCENE_NAME_LIST_PROTOCOL_ID) {
                val bean = SmartSceneBaseBean(
                    requestCode,
                    protocolId,
                    requestAuthor,
                    messageType,
                    BuildConfig.VERSION_NAME,
                    ArrayList<SceneNameBean>()
                )
                val list: ArrayList<SceneNameBean> = SceneVoiceManager.getSceneList()
                bean.data = list
                val responseJson = mGson!!.toJson(bean).replace("\\\\".toRegex(), "")
                doResponseStringToClient(responseJson, requestCookie)
            }
            if (protocolId == SmartSceneCustomConstant.SMART_SCENE_VOICE_RUN_SCENE_PROTOCOL_ID) {
                val bean = SmartSceneBaseBean(
                    requestCode,
                    protocolId,
                    requestAuthor,
                    messageType,
                    BuildConfig.VERSION_NAME,
                    HashMap<String, Int>()
                )
                val data = JsonUtils.getString(json, DATA)
                val sceneId = JsonUtils.getString(data, "sceneId")
                val result = SceneVoiceManager.startScene(sceneId)
                val map = java.util.HashMap<String, Int>()
                map["result"] = if (result) 0 else 1
                bean.data = map
                val responseJson = mGson!!.toJson(bean).replace("\\\\".toRegex(), "")
                doResponseStringToClient(responseJson, requestCookie)
            }
            //			if (protocolId == SmartSceneCustomConstant.SMART_SCENE_DATA_LIST_PROTOCOL_ID) {
            //				val bean =
            //					SmartSceneBaseBean(
            //						requestCode, protocolId, requestAuthor, messageType,
            //						BuildConfig.VERSION_NAME,
            //						HashMap<String, Int>()
            //					)
            //				val data = SceneVoiceManager.getSceneInfo()
            //				bean.data = data
            //				val responseJson = mGson!!.toJson(bean).replace("\\\\".toRegex(), "")
            //				doResponseStringToClient(responseJson, requestCookie)
            //			}
            if (protocolId == SmartSceneCustomConstant.SMART_SCENE_CONTROL_AUTO_RUN_PROTOCOL_ID) {
                val bean = SmartSceneBaseBean(
                    requestCode,
                    protocolId,
                    requestAuthor,
                    messageType,
                    BuildConfig.VERSION_NAME,
                    HashMap<String, Int>()
                )
                val data = JsonUtils.getString(json, DATA)
                val state = JsonUtils.getInt(data, "state")
                val result = SceneVoiceManager.setMonitor(state == 1, requestAuthor)
                val map: HashMap<String, Int> = java.util.HashMap()
                map["result"] = if (result) 0 else 1
                bean.data = map
                val responseJson = mGson!!.toJson(bean).replace("\\\\".toRegex(), "")
                doResponseStringToClient(responseJson, requestCookie)
            }
            if (protocolId == SmartSceneCustomConstant.SMART_SCENE_TRY_RUN_SCENE_PROTOCOL_ID) {
                val bean = SmartSceneBaseBean(
                    requestCode,
                    protocolId,
                    requestAuthor,
                    messageType,
                    BuildConfig.VERSION_NAME,
                    HashMap<String, Int>()
                )
                val data = JsonUtils.getString(json, DATA)
                val sceneJson = JsonUtils.getString(data, "sceneJson")
                val result = SceneVoiceManager.tryScene(sceneJson)
                val map: HashMap<String, Int> = java.util.HashMap()
                map["result"] = if (result) 0 else 1
                bean.data = map
                val responseJson = mGson!!.toJson(bean).replace("\\\\".toRegex(), "")
                doResponseStringToClient(responseJson, requestCookie)
                if (!result) {
                    mProtocolMap.remove(requestCookie)
                }
            }
            //打开编辑界面 10007
            if (protocolId == SmartSceneCustomConstant.SMART_SCENE_OPEN_EDIT) {

                val data = JsonUtils.getString(json, DATA)
                val scenarioInfoJson = JsonUtils.getString(data, "scenarioInfoJson")
                //                val scenarioInfoJson = "{\"autoExeFlag\": 0,\"conditions\": [{\"category\": \"空⽓质量\",\"desc\": \"差\",\"id\": 0,\"input\": [{\"desc\": null,\"name\": \"status\",\"type\": \"INT32\",\"value\": \"0\"}],\"skillId\": 89}],\"edgeCondition\": {\"category\": \"时间点\",\"desc\": \"09:00\",\"id\": 0,\"input\": [{\"desc\": \"\",\"name\": \"hour\",\"type\": \"INT32\",\"value\": \"09\"},{\"desc\": \"\",\"name\": \"minute\",\"type\": \"INT32\",\"value\": \"00\"},{\"desc\": \"\",\"name\": \"second\",\"type\": \"INT32\",\"value\": \"0\"}],\"skillId\": 65286},\"imgPath\": \"\",\"scenarioDesc\": \"\",\"scenarioId\": \"VEHICLE_1723825036319\",\"scenarioName\": \"场景⼀⼈⼈\",\"scenarioTimeStamp\": 0,\"secondAskeFlag\": 1,\"sequence\": [{\"action\": {\"category\": \"播放多媒体\",\"desc\": \"QQ⾳乐\",\"input\": [],\"skillId\": 88},\"delay\": 0,\"id\": 0,\"type\": 1},{\"action\": {\"category\": \"关闭多媒体\",\"desc\": \"关闭多媒体\",\"input\": [],\"skillId\": 41},\"delay\": 0,\"id\": 1,\"type\": 1}],\"version\": 30}"
                CommonLogUtils.logI(TAG, "外部通过10007协议进入编辑界面时的json:" + scenarioInfoJson)
                val scenarioInfo = GsonUtils.fromJson(scenarioInfoJson, ScenarioInfo::class.java)

                //1、过滤能力并打开编辑页
                val imRes = IntermediateUtils.filterAndOpenEdit(applicationContext, scenarioInfo)

                //5、返回
                val map = java.util.HashMap<String, Int>()
                map["result"] = 0
                map["conditionMatch"] = imRes.conditionMatch
                map["sequenceMatch"] = imRes.sequenceMatch
                val bean = SmartSceneBaseBean(
                    requestCode,
                    protocolId,
                    requestAuthor,
                    messageType,
                    BuildConfig.VERSION_NAME,
                    data = HashMap<String, Int>()
                )
                bean.data = map
                val responseJson = mGson!!.toJson(bean).replace("\\\\".toRegex(), "")
                doResponseStringToClient(responseJson, requestCookie)
            }
            //打开编辑界面 10008
            if (protocolId == SmartSceneCustomConstant.SMART_SCENE_CONVERT_AND_OPEN_EDIT) {
                val startTime = System.currentTimeMillis()
                val data = JsonUtils.getString(json, DATA)
                val scenarioInfoJson = JsonUtils.getString(data, "scenarioInfoJson")
//                val scenarioInfoJson =IntermediateUtils.getTestDataJson()
                CommonLogUtils.logI(TAG, "外部通过10008协议进入")

                //1、语义转换
                val req2NodeARule = IntermediateUtils.semanticTransformation(applicationContext, scenarioInfoJson)
                if (req2NodeARule == null) { //已经有打印
                    val imRes = IMRes(
                        result = 1, failReason = "场景中间层转义错误", resList = IntermediateUtils.resList
                    )
                    CommonLogUtils.logE(
                        TAG, "10008协议，语义转换失败：${imRes.toString()},总用时：" + TimeUtils.millis2String(
                            System.currentTimeMillis() - startTime, "ss:SSS"
                        )
                    )
                    //失败返回数据
                    returnDoResponseStringToClient(
                        requestCode, protocolId, requestAuthor, messageType, requestCookie, imRes
                    )
                    return
                }
                lifecycleScope.launch(Dispatchers.IO) {
                    val job = launch {
                        //2、参数转换
                        IntermediateUtils.paramTransformation(req2NodeARule.reqJsonRootNode!!, req2NodeARule.ruleJson!!)
                        withContext(Dispatchers.Main) {
                            //3、转换成功 得出ScenarioInfo
                            val scenarioInfo = GsonUtils.fromJson(
                                req2NodeARule.reqJsonRootNode.toString(), ScenarioInfo::class.java
                            )
                            //4、过滤能力，和不规范的条件动作，并打开编辑页
                            val imRes = IntermediateUtils.filterAndOpenEdit(applicationContext, scenarioInfo)

                            CommonLogUtils.logI(
                                TAG, "10008协议，在${IMScenarioInfo.timeOutSecond}秒内完成，总用时：${
                                    TimeUtils.millis2String(
                                        System.currentTimeMillis() - startTime, "ss:SSS"
                                    )
                                },最终数据为：${req2NodeARule.reqJsonRootNode.toString()}，服务返回：${imRes.toString()}"
                            )
                            //5、返回数据
                            returnDoResponseStringToClient(
                                requestCode, protocolId, requestAuthor, messageType, requestCookie, imRes
                            )
                        }
                    }
                    job.start()
                    kotlinx.coroutines.delay(IMScenarioInfo.timeOutSecond * 1000L)
                    if (!job.isCompleted) { //超过5秒 结束
                        job.cancel()
                        withContext(Dispatchers.Main) {
                            //3、转换不成功 得出ScenarioInfo
                            val scenarioInfo = GsonUtils.fromJson(
                                req2NodeARule.reqJsonRootNode.toString(), ScenarioInfo::class.java
                            )
                            //4、过滤能力，和不规范的条件动作，并打开编辑页
                            val imRes = IntermediateUtils.filterAndOpenEdit(applicationContext, scenarioInfo)
                            imRes.result = 1
                            imRes.failReason = "超时${IMScenarioInfo.timeOutSecond}秒"

                            CommonLogUtils.logI(
                                TAG, "10008协议，超过${IMScenarioInfo.timeOutSecond}秒没完成，总用时：${
                                    TimeUtils.millis2String(
                                        System.currentTimeMillis() - startTime, "ss:SSS"
                                    )
                                },最终数据为：${req2NodeARule.reqJsonRootNode.toString()}，服务返回：${imRes.toString()}"
                            )
                            //5、返回数据
                            returnDoResponseStringToClient(
                                requestCode, protocolId, requestAuthor, messageType, requestCookie, imRes
                            )
                        }
                    }
                }
            }
        } catch (tr: Throwable) {
            CommonLogUtils.logE(TAG, "智慧场景提供的协议方法[$requestCookie]报错：" + tr.message)
            tr.printStackTrace()

            if (protocolId == SmartSceneCustomConstant.SMART_SCENE_OPEN_EDIT) {
                //5、返回
                val map = java.util.HashMap<String, Int>()
                map["result"] = 1
                map["conditionMatch"] = -1
                map["sequenceMatch"] = -1
                val bean = SmartSceneBaseBean(
                    requestCode,
                    protocolId,
                    requestAuthor,
                    messageType,
                    BuildConfig.VERSION_NAME,
                    data = HashMap<String, Int>()
                )
                bean.data = map
                val responseJson = mGson!!.toJson(bean).replace("\\\\".toRegex(), "")
                doResponseStringToClient(responseJson, requestCookie)
            }
        } finally {
            mLock.unlock()
        }
    }

    private fun <T> returnDoResponseStringToClient(
        requestCode: String, protocolId: Int, requestAuthor: String, messageType: String, requestCookie: String?, obj: T
    ) {
        //5、返回
        val bean = SmartSceneBaseBean(
            requestCode, protocolId, requestAuthor, messageType, BuildConfig.VERSION_NAME, data = obj
        )
        val responseJson = mGson!!.toJson(bean).replace("\\\\".toRegex(), "")
        doResponseStringToClient(responseJson, requestCookie)
    }

    override fun doResponseStringToClient(response: String?, requestCookie: String?) {
        CommonLogUtils.logD(TAG, "response:$response,requestCookie:$requestCookie")
        super.doResponseStringToClient(response, requestCookie)
    }


}