package com.dfl.smartscene.ccs.util;



import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class DateUtil {

    public static int compareTime(String date, String date2) {

        //创建日期转换对象HH:mm:ss为时分秒，年月日为yyyy-MM-dd
        DateFormat df = new SimpleDateFormat("HH:mm", Locale.CHINA);
        try {
            Date dt1 = df.parse(date);//将字符串转换为date类型
            Date dt2 = df.parse(date2);
            //比较时间大小,如果dt1大于dt2
            return Long.compare(dt1.getTime(), dt2.getTime());
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return -2;
    }

}
