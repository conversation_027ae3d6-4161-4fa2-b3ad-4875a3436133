package com.dfl.smartscene.ccs.wrapper;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dfl.android.common.customapi.AppCommonManager;
import com.dfl.smartscene.BuildConfig;
import com.dfl.smartscene.ccs.bean.AdBean;
import com.dfl.smartscene.ccs.bean.CarConfig;
import com.dfl.smartscene.ccs.bean.InputCheckResult;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SceneCategory;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.db.CarConfigUtils;
import com.dfl.smartscene.ccs.http.HttpResult;
import com.dfl.smartscene.ccs.http.InputCheckBean;
import com.dfl.smartscene.ccs.http.SceneDelete;
import com.dfl.smartscene.ccs.http.SceneSorts;
import com.dfl.smartscene.ccs.http.UploadAddBean;
import com.dfl.smartscene.ccs.http.UploadDelBean;
import com.dfl.smartscene.ccs.model.DAModel;
import com.dfl.smartscene.ccs.model.DriveModel;
import com.dfl.smartscene.ccs.model.SceneDataModel;
import com.dfl.smartscene.ccs.util.CarConfigUtil;
import com.dfl.smartscene.ccs.util.DataTypeFormatUtil;
import com.dfl.smartscene.ccs.util.Logger;
import com.dfl.smartscene.ccs.wrapper.LanYouRequestWrapper;
import com.iauto.uibase.utils.MLog;
import com.szlanyou.gwsdk.RequestCallback;
import com.tencent.mmkv.MMKV;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.alibaba.fastjson.TypeReference;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.ObservableSource;
import io.reactivex.functions.Function;
import kotlin.coroutines.CoroutineContext;
import kotlinx.coroutines.BuildersKt;
import kotlinx.coroutines.CoroutineScope;
import kotlinx.coroutines.CoroutineStart;
import kotlinx.coroutines.Dispatchers;
import kotlinx.coroutines.GlobalScope;

/**
 * <AUTHOR> Zezheng
 * @date ：2022/8/10 11:02
 * @description ：数据请求类
 */
public class HttpWrapper {
    private MMKV mMMKV = MMKV.defaultMMKV();
    private static final String TAG = "HttpWrapper";
    private static volatile HttpWrapper sInstance;

    //请求首页预设场景
    private static final String SQUARE_SCENE_API = "ly.ccsv.da.scene.recommend.list";
    //请求用户场景
    private static final String USER_SCENE_API = "ly.ccsv.da.scene.userScene.list";
    //请求车辆能力
    private static final String CAR_CONFIG_API = "ly.ccsv.da.scene.carConfig.get";
    //添加用户场景
    private static final String ADD_USER_SCENE_API = "ly.ccsv.da.scene.add";
    //替换用户场景
    private static final String REPLACE_USER_SCENE_API = "ly.ccsv.da.scene.update";
    //删除用户场景
    private static final String DELETE_USER_SCENE_API = "ly.ccsv.da.scene.delete";

    //上传用户场景
    private static final String UPLOAD_USER_SCENES_API = "ly.ccsv.da.scene.upload";
    //检查当日节假日
    private static final String CHECK_HOLIDAY = "ly.ccsv.da.holiday.list";
    //排序用户场景
    private static final String SORT_USER_SCENES_API = "ly.ccsv.da.scene.sort";

    //敏感词检查
    private static final String CHECK_INPUT_LEGAL_API = "ly.ccsv.da.content.audit";
    //请求所有节假日
    private static final String ALL_HOLIDAY = "ly.ccsv.da.holiday.list.all";

    //请求广告，需验签
    private static final String AD_CONTENT = "ly.ccsv.da.advertising.getadvertisingcommon";

    private static final String AD_LOGIN = "ly.dfn.da.advertising.login.getadvertisingcommon";
    private static final String AD_UNLOGIN = "ly.dfn.da.advertising.getadvertisingcommon";

    public static final String HTTP_RESPONSE_OK = "1";

    public static HttpWrapper getInstance() {
        if (null == sInstance) {
            synchronized (HttpWrapper.class) {
                if (null == sInstance) {
                    sInstance = new HttpWrapper();
                }
            }
        }
        return sInstance;
    }


    /**
     * 请求首页的场景数据
     * 使用AppCommonManager异步获取DAID，替代原有的DriveModel方式
     *
     * @return Observable<List<SceneCategory>> 场景分类列表的Observable对象
     */
    public Observable<List<SceneCategory>> requestSquareSceneList() {
        LogUtil.d(TAG, "requestSquareSceneList: ");
        //判断有无本地有效期的数据，如有无需请求
        List<SceneCategory> localSceneCategories = getStorageLocalSquareScene();
        if(null != localSceneCategories){
            LogUtil.d(TAG, "local data is valid");
            return Observable.create(new ObservableOnSubscribe<List<SceneCategory>>() {
                @Override
                public void subscribe(ObservableEmitter<List<SceneCategory>> emitter) throws Exception {
                    emitter.onNext(localSceneCategories);
                }
            });
        }

        if (ConstantModelValue.DEBUG_REQUEST_TYPE.equals(ConstantModelValue.DEBUG_REQUEST_TYPE_CLOUD)) {
            boolean userLogin = DAModel.getInstance().isUserCenterLogin();
            MLog.d(TAG, "userlogin:" + userLogin);
            return Observable.create(new ObservableOnSubscribe<List<SceneCategory>>() {
                @Override
                public void subscribe(ObservableEmitter<List<SceneCategory>> emitter) throws Exception {
                    // 使用协程异步获取DAID
                    BuildersKt.launch(GlobalScope.INSTANCE, (CoroutineContext)Dispatchers.getIO(), CoroutineStart.DEFAULT, (coroutineScope, continuation) -> {
                        // 调用AppCommonManager获取DAID
                        AppCommonManager.INSTANCE.getDAId(new kotlin.coroutines.Continuation<String>() {
                            @Override
                            public kotlin.coroutines.CoroutineContext getContext() {
                                return kotlin.coroutines.EmptyCoroutineContext.INSTANCE;
                            }
                            
                            @Override
                            public void resumeWith(Object result) {
                                // 处理获取DAID的结果
                                if (result instanceof kotlin.Result.Failure) {
                                    LogUtil.d(TAG, "requestSquareSceneList: get daid failed");
                                    if (!emitter.isDisposed()) {
                                        emitter.onError(new Exception("Failed to get DAID"));
                                    }
                                    return;
                                }
                                
                                String daid = (String) result;
                                // 验证DAID的有效性
                                if (daid == null || daid.equals("-1") || daid.isEmpty()) {
                                    LogUtil.d(TAG, "requestSquareSceneList: daid null or invalid: " + daid);
                                    if (!emitter.isDisposed()) {
                                        emitter.onError(new Exception("DAID is null or invalid"));
                                    }
                                    return;
                                }
                                
                                // 构建请求参数
                                HashMap<String, Object> param = new HashMap<>();
                                param.put("api", SQUARE_SCENE_API);
                                param.put("daid", daid);
                                LogUtil.d(TAG, "requestSquareSceneList: LanYouRequestWrapper.safeRequest with daid: " + daid);
                                
                                // 发起网络请求
                                LanYouRequestWrapper.safeRequest(param, new RequestCallback() {
                                    @Override
                                    public void onStart() {
                                        // 请求开始回调
                                    }

                                    @Override
                                    public void onComplete() {
                                        // 请求完成回调
                                    }

                                    @Override
                                    public void onSuccess(@NonNull String s) {
                                        // 请求成功处理
                                        Logger.printJson(TAG,"requestSquareSceneList success return : " + s);
                                        HttpResult httpResult = JSON.parseObject(s, HttpResult.class);
                                        if (HTTP_RESPONSE_OK.equals(httpResult.getResult())) {
                                            List<SceneCategory> sceneCategories = JSON.parseObject(JSON.toJSONString(httpResult.getRows()), new TypeReference<List<SceneCategory>>() {
                                            });
                                            emitter.onNext(sceneCategories);
                                            //本地保存数据
                                            saveLocalSquareScene(sceneCategories);
                                        } else {
                                            emitter.onError(new Exception(s));
                                        }
                                    }

                                    @Override
                                    public void onError(@NonNull Throwable throwable) {
                                        // 请求错误处理
                                        throwable.printStackTrace();
                                        MLog.d(TAG, "requestSquareSceneList:onError:" + throwable.getMessage());
                                        if (!emitter.isDisposed()) {
                                            emitter.onNext(null);
                                            emitter.onError(new Exception(throwable));
                                        }
                                    }

                                    @Override
                                    public void onFail(@NonNull String s) {
                                        // 请求失败处理
                                        MLog.d(TAG, "requestSquareSceneList:onFail:" + s);
                                        if (!emitter.isDisposed()) {
                                            emitter.onNext(null);
                                            emitter.onError(new Exception(s));
                                        }
                                    }
                                });
                            }
                        });
                        return kotlin.Unit.INSTANCE;
                    });
                }
            });

        } else if (ConstantModelValue.DEBUG_REQUEST_TYPE.equals(ConstantModelValue.DEBUG_REQUEST_TYPE_LOCAL)) {
            if(CarConfigUtil.isCCS()){
                return CarConfigUtils.querySceneByCarType(ConstantModelValue.DEBUG_CAR_TYPE_CCS5);
            }else if(CarConfigUtil.isCCU()){
                return CarConfigUtils.querySceneByCarType(ConstantModelValue.DEBUG_CAR_TYPE_CCU);
            }else if(CarConfigUtil.isPhone()){
                return CarConfigUtils.querySceneByCarType(ConstantModelValue.DEBUG_CAR_TYPE_PHONE);
            }
        }
        return Observable.error(new Exception());
    }

    /**
     * 请求用户场景数据
     *
     * @return
     */
    public Observable<List<SceneCategory>> requestUserSceneList() {

        LogUtil.d(TAG,"requestUserSceneList");
        if (ConstantModelValue.DEBUG_REQUEST_TYPE.equals(ConstantModelValue.DEBUG_REQUEST_TYPE_CLOUD)) {
            return Observable.create(new ObservableOnSubscribe<List<SceneCategory>>() {
                @Override
                public void subscribe(ObservableEmitter<List<SceneCategory>> emitter) throws Exception {
                    if(DriveModel.getInstance().getDaid() == null){
                        emitter.onError(new Exception());
                        LogUtil.d(TAG,"requestUserSceneList daid is null");
                        return;
                    }

                    HashMap<String, Object> param = new HashMap<>();
                    param.put("api", USER_SCENE_API);
                    param.put("daid", DriveModel.getInstance().getDaid());
                    LanYouRequestWrapper.safeRequest(param, new RequestCallback() {
                        @Override
                        public void onStart() {

                        }

                        @Override
                        public void onComplete() {

                        }

                        @Override
                        public void onSuccess(@NonNull String s) {
                            MLog.d(TAG, "requestUserSceneList:onSuccess:" + s);
                            HttpResult httpResult = JSON.parseObject(s, HttpResult.class);
                            if (HTTP_RESPONSE_OK.equals(httpResult.getResult())) {
                                List<SceneCategory> sceneCategories = JSON.parseObject(JSON.toJSONString(httpResult.getRows()), new TypeReference<List<SceneCategory>>() {
                                });
                                emitter.onNext(sceneCategories);
                            } else {
                                emitter.onError(new Exception(s));
                            }
                        }

                        @Override
                        public void onError(@NonNull Throwable throwable) {
                            MLog.d(TAG, "requestUserSceneList:onError:" + throwable.getMessage());
                            if (!emitter.isDisposed()) {
                                List<SceneCategory> emptyList = new ArrayList<>();
                                emitter.onNext(emptyList);
                                emitter.onError(new Exception(throwable));
                            }

                        }

                        @Override
                        public void onFail(@NonNull String s) {
                            MLog.d(TAG, "requestUserSceneList:onFail:" + s);
                            if (!emitter.isDisposed()) {
                                List<SceneCategory> emptyList = new ArrayList<>();
                                emitter.onNext(emptyList);
                                emitter.onError(new Exception(s));
                            }
                        }
                    });
                }
            });
        } else if (ConstantModelValue.DEBUG_REQUEST_TYPE.equals(ConstantModelValue.DEBUG_REQUEST_TYPE_LOCAL)) {
            return Observable.error(new Exception());

        }
        return Observable.error(new Exception());

    }

    /**
     * 请求车辆配置数据
     *
     * @return
     */
    public Observable<CarConfig> requestCarConfig() {
        //判断有无本地有效期的数据，如有无需请求
        CarConfig carConfig = getStorageLocalCarConfig();
        if(null != carConfig){
            LogUtil.d(TAG, "local car config data is valid");
            return Observable.create(new ObservableOnSubscribe<CarConfig>() {
                @Override
                public void subscribe(ObservableEmitter<CarConfig> emitter) throws Exception {
                    emitter.onNext(carConfig);
                }
            });
        }

        if (ConstantModelValue.DEBUG_REQUEST_TYPE.equals(ConstantModelValue.DEBUG_REQUEST_TYPE_CLOUD)) {
            return Observable.create(new ObservableOnSubscribe<CarConfig>() {
                @Override
                public void subscribe(ObservableEmitter<CarConfig> emitter) throws Exception {
                    if(DriveModel.getInstance().getDaid() == null){
                        emitter.onError(new Exception());
                        return;
                    }

                    HashMap<String, Object> param = new HashMap<>();
                    param.put("api", CAR_CONFIG_API);
                    param.put("daid", DriveModel.getInstance().getDaid());
                    LanYouRequestWrapper.safeRequest(param, new RequestCallback() {
                        @Override
                        public void onStart() {

                        }

                        @Override
                        public void onComplete() {

                        }

                        @Override
                        public void onSuccess(@NonNull String s) {
                            HttpResult httpResult = JSON.parseObject(s, HttpResult.class);
                            if (HTTP_RESPONSE_OK.equals(httpResult.getResult())) {
                                String dataJson = JSON.toJSONString(httpResult.getRows());
                                CarConfig carConfig = JSON.parseObject(dataJson, CarConfig.class);
                                emitter.onNext(carConfig);
                                //本地持久化保存数据
                                saveLocalCarConfig(dataJson);
                            } else {
                                emitter.onError(new Exception(s));
                            }
                        }

                        @Override
                        public void onError(@NonNull Throwable throwable) {
                            MLog.d(TAG, "requestCarConfig:onError:" + throwable.getMessage());
                            if (!emitter.isDisposed()) {
                                emitter.onNext(new CarConfig());
                                emitter.onError(new Exception());
                            }

                        }

                        @Override
                        public void onFail(@NonNull String s) {
                            MLog.d(TAG, "requestCarConfig:onFail:" + s);
                            if (!emitter.isDisposed()) {
                                emitter.onNext(new CarConfig());
                                emitter.onError(new Exception());
                            }
                        }
                    });
                }
            });
        } else if (ConstantModelValue.DEBUG_REQUEST_TYPE.equals(ConstantModelValue.DEBUG_REQUEST_TYPE_LOCAL)) {
            if(BuildConfig.platform.equals(BuildConfig.platform_ccs5)){
                return CarConfigUtils.queryCarConfig(ConstantModelValue.DEBUG_CAR_TYPE_CCS5);
            }else if(BuildConfig.platform.equals(BuildConfig.platform_ccu)){
                return CarConfigUtils.queryCarConfig(ConstantModelValue.DEBUG_CAR_TYPE_CCU);
            }else if(BuildConfig.platform.equals(BuildConfig.platform_phone)){
                return CarConfigUtils.queryCarConfig(ConstantModelValue.DEBUG_CAR_TYPE_PHONE);
            }

        }
        return Observable.error(new Exception());

    }


    /**
     * 请求添加用户场景
     * @param categoryId
     * @param sceneBean
     * @return
     */
    public Observable<HttpResult> addUserScene(String categoryId, SceneBean sceneBean) {
        MLog.d(TAG, "request addUserScene() : categoryId: " + categoryId + ", scene name: " + sceneBean.getSceneName());

        if(DriveModel.getInstance().getDaid() == null){
            return Observable.error(new Exception());
        }

        HashMap<String, Object> param = new HashMap<>();
        param.put("api", ADD_USER_SCENE_API);
        param.put("daid", DriveModel.getInstance().getDaid());
        param.put("category", categoryId);
        param.put("sceneBean", sceneBean);
        return requestAndReturnHttpResult(param);
    }

    public Observable<HttpResult> deleteUserScene(String categoryId, String sceneId) {
        return deleteUserScene(categoryId, Collections.singletonList(sceneId));
    }

    /**
     * 请求删除用户场景
     * @param categoryId
     * @param sceneIds
     * @return
     */
    public Observable<HttpResult> deleteUserScene(String categoryId, List<String> sceneIds) {

        if(DriveModel.getInstance().getDaid() == null){
            return Observable.error(new Exception());
        }

        HashMap<String, Object> param = new HashMap<>();
        param.put("api", DELETE_USER_SCENE_API);
        param.put("daid", DriveModel.getInstance().getDaid());
        List<SceneDelete> sceneDeletes = new ArrayList<>();
        for(String sceneId : sceneIds){
            sceneDeletes.add(new SceneDelete(categoryId , sceneId));
        }
        param.put("scenes", sceneDeletes);
        return requestAndReturnHttpResult(param);
    }

    /**
     * 请求替换用户场景
     * @param categoryId
     * @param sceneBean
     * @return
     */
    public Observable<HttpResult> replaceUserScene(String categoryId, SceneBean sceneBean) {
        MLog.d(TAG, "request replaceUserScene()");

        if(DriveModel.getInstance().getDaid() == null){
            return Observable.error(new Exception());
        }

        HashMap<String, Object> param = new HashMap<>(4);
        param.put("api", REPLACE_USER_SCENE_API);
        param.put("daid", DriveModel.getInstance().getDaid());
        param.put("category", categoryId);
        param.put("sceneBean", sceneBean);
        return requestAndReturnHttpResult(param);
    }

    /**
     * 请求对用户场景进行换序
     * @param sceneCategory
     * @return
     */
    public Observable<HttpResult> syncSceneSort(SceneCategory sceneCategory){
        MLog.d(TAG, "request syncSceneSort()");

        if(DriveModel.getInstance().getDaid() == null){
            return Observable.error(new Exception());
        }

        HashMap<String, Object> param = new HashMap<>(3);
        param.put("api", SORT_USER_SCENES_API);
        param.put("daid", DriveModel.getInstance().getDaid());
        SceneSorts[] sceneSorts = new SceneSorts[sceneCategory.getSceneBeanList().size()];
        for(int i = 0 ; i < sceneCategory.getSceneBeanList().size() ; i ++){
            SceneBean sceneBean = sceneCategory.getSceneBeanList().get(i);
            sceneSorts[i] = new SceneSorts(sceneBean.getSceneId(),sceneBean.getSort(),sceneBean.getTimestamp());
        }
        LogUtil.d(TAG,"syncSceneSort params : " + JSON.toJSONString(sceneSorts));
        param.put("sorts", sceneSorts);
        return requestAndReturnHttpResult(param);
    }

    /**
     * 上传本地场景
     * @param sceneCategories
     * @param addBeans
     * @param uploadDelBeans
     * @return
     */
    public Observable<List<SceneCategory>> uploadLocalScenes(List<SceneCategory> sceneCategories, List<UploadAddBean> addBeans , List<UploadDelBean> uploadDelBeans) {
        MLog.d(TAG, "request uploadLocalScenes()");

        if(DriveModel.getInstance().getDaid() == null){
            MLog.d(TAG, "request uploadLocalScenes but da id is null");
            return Observable.error(new Exception());
        }
        if(null != sceneCategories){
            Iterator<SceneCategory> iterator = sceneCategories.iterator();
            while (iterator.hasNext()) {
                SceneCategory next = iterator.next();
                List<SceneBean> sceneBeanList = next.getSceneBeanList();
                if(sceneBeanList == null || sceneBeanList.isEmpty()) {
                    iterator.remove();
                }
            }
        }

        LogUtil logUtil = new LogUtil();
        logUtil.printJsonLogger(TAG,"uploadLocalScenes PARAM = " + JSON.toJSONString(sceneCategories));

        HashMap<String, Object> param = new HashMap<>(3);
        param.put("api", UPLOAD_USER_SCENES_API);
        param.put("daid", DriveModel.getInstance().getDaid());
        param.put("categories", sceneCategories);
        param.put("add",addBeans);
        LogUtil.d(TAG , "uploadLocalScenes uploadDelBeans : " + JSON.toJSONString(uploadDelBeans));
        param.put("del",uploadDelBeans);

        return requestAndReturnHttpResult(param).flatMap(new Function<HttpResult, ObservableSource<? extends List<SceneCategory>>>() {
            @Override
            public ObservableSource<? extends List<SceneCategory>> apply(HttpResult httpResult) throws Exception {
                LogUtil.d(TAG,"uploadLocalScenes return : " + JSON.toJSONString(httpResult));
                if(httpResult != null && httpResult.getRows() != null){
                    return Observable.just(JSON.parseObject(JSON.toJSONString(httpResult.getRows()) ,  new TypeReference<List<SceneCategory>>() {
                    }));
                }else {
                    return Observable.error(new Exception());
                }
            }
        });
    }

    /**
     * true:节假日
     * false:工作日
     */
    public Observable<Boolean> requestHolidayOrWorkday(String time){
        HashMap<String , Object> params = new HashMap<>(2);
        params.put("api",CHECK_HOLIDAY);
        params.put("time",time);
        return requestAndReturnHttpResult(params).flatMap(new Function<HttpResult, ObservableSource<? extends Boolean>>() {
            @Override
            public ObservableSource<? extends Boolean> apply(HttpResult httpResult) throws Exception {
                if(HTTP_RESPONSE_OK.equals(httpResult.getResult())){
                    JSONObject jsonObject = (JSONObject) httpResult.getRows();
                    return Observable.just(DataTypeFormatUtil.string2Boolean(jsonObject.getString("code"),false));
                }else {
                    return Observable.error(new Exception());
                }
            }
        });
    }

    /**
     * 获取所有节假日日期
     * @return
     */
    public Observable<List<String>> requestAllHoliday(){
        HashMap<String , Object> params = new HashMap<>(1);
        params.put("api",ALL_HOLIDAY);
        return requestAndReturnHttpResult(params).flatMap(new Function<HttpResult, ObservableSource<? extends List<String>>>() {
            @Override
            public ObservableSource<? extends List<String>> apply(HttpResult httpResult) throws Exception {
                if(HTTP_RESPONSE_OK.equals(httpResult.getResult())){
                    JSONArray jsonArray = (JSONArray) httpResult.getRows();
                    List<String> result = new ArrayList<>();
                    for(Object jsonObj : jsonArray){
                        result.add(((JSONObject)jsonObj).getString("today"));
                    }
                    return Observable.just(result);
                }else {
                    return Observable.error(new Exception());
                }
            }
        });

    }

    private Observable<HttpResult> requestAndReturnHttpResult(HashMap<String, Object> param) {
        LogUtil.d(TAG,"is user login : " + DAModel.getInstance().isUserCenterLogin());
        return Observable.create(emitter -> LanYouRequestWrapper.safeRequest(param, new RequestCallback() {
            @Override
            public void onStart() {
                MLog.d(TAG, "request onStart:");
            }

            @Override
            public void onComplete() {
                MLog.d(TAG, "request onComplete:");
            }

            @Override
            public void onSuccess(@NonNull String dataJson) {
                MLog.d(TAG, "request onSuccess:" + param.get("api"));
                HttpResult result = JSON.parseObject(dataJson, HttpResult.class);
                if (!emitter.isDisposed()) {
                    emitter.onNext(result);
                }
            }

            @Override
            public void onError(@NonNull Throwable throwable) {
                MLog.d(TAG, "request onError:" + param.get("api") + " , :" + throwable.getMessage());
                if (!emitter.isDisposed()) {
                    emitter.onNext(new HttpResult());
                    emitter.onError(new Exception(throwable));
                }
            }

            @Override
            public void onFail(@NonNull String s) {
                MLog.d(TAG, "request onFail:" + param.get("api"));

                MLog.d(TAG, "request onFail:" + param.get("api") + " , :" + s);
                if (!emitter.isDisposed()) {
                    emitter.onNext(new HttpResult());
                    emitter.onError(new Exception(s));
                }
            }
        },true));
    }

    /**
     * 检查词汇合法性
     * @param input
     * @return
     */
    public Observable<InputCheckResult> checkInputLegal(String input){
        HashMap<String, Object> param = new HashMap<>();
        param.put("api", CHECK_INPUT_LEGAL_API);
        param.put("data", new InputCheckBean[]{new InputCheckBean(input)});
        return requestAndReturnHttpResult(param)
                .map(new Function<HttpResult, InputCheckResult>() {
                    @Override
                    public InputCheckResult apply(HttpResult httpResult) throws Exception {
                        if(httpResult == null){
                            return new InputCheckResult(input,InputCheckResult.RESULT_NONETWORK);
                        }
                        JSONArray resultArray = (JSONArray) httpResult.getRows();
                        if(resultArray == null || resultArray.size() == 0){
                            return new InputCheckResult(input,InputCheckResult.RESULT_NONETWORK);
                        }
                        JSONObject result = (JSONObject)resultArray.get(0);
                        if(result.getIntValue("conclusionType") == 1){
                            return new InputCheckResult(input,InputCheckResult.RESULT_PASS);
                        }else {
                            return new InputCheckResult(input,InputCheckResult.RESULT_UNPASS);
                        }
                    }
                });

    }

    public Observable<AdBean> requestAdInfo(){
        HashMap<String, Object> param = new HashMap<>();
//        if(LanYouRequestWrapper.isInitialized()){
//            param.put("api", AD_LOGIN);
//        }else {
//            param.put("api", AD_UNLOGIN);
//        }
        param.put("api" , AD_CONTENT);
        param.put("appCode","nissan");
        param.put("spaceTypeId", "12");

        param.put("sign", true);
        return requestAndReturnHttpResult(param).map(new Function<HttpResult, AdBean>() {
            @Override
            public AdBean apply(HttpResult httpResult) throws Exception {
                LogUtil.d(TAG,"requestAdInfo callback : " + httpResult.toString());
                return null;
            }
        });

    }

    /**
     * 保存广场场景列表数据
     * */
    public void saveLocalSquareScene(List<SceneCategory> sceneCategories){
        if(sceneCategories != null){
            Map<String, Object> map = new HashMap<String, Object>();
            map.put(SceneDataModel.UPDATE_TIME_KEY, System.currentTimeMillis());
            map.put(SceneDataModel.DATA_KEY, JSON.toJSONString(sceneCategories));
            mMMKV.encode(SceneDataModel.KEY_MMKV_SQUARE_SCENE, JSON.toJSONString(map));
        }
    }

    /**
     * 返回本地保存的广场模式列表数据，内置有效期判断
     * @return
     */
    public @Nullable
    List<SceneCategory> getStorageLocalSquareScene(){
        String key = SceneDataModel.KEY_MMKV_SQUARE_SCENE;
        if(mMMKV.containsKey(key)){
            //可能存在历史数据，需进行下主动清理
            try{
                JSONObject jsonObject = JSONObject.parseObject(mMMKV.decodeString(key));
                long updateTime = (Long) jsonObject.get(SceneDataModel.UPDATE_TIME_KEY);
                String jsonString = JSON.parse(jsonObject.get(SceneDataModel.DATA_KEY).toString()).toString();
                //数据有效期判断
                boolean periodValidity = (System.currentTimeMillis() - updateTime) < SceneDataModel.UPDATE_INTERVAL;
                if(periodValidity){
                    return JSON.parseObject(jsonString, new TypeReference<List<SceneCategory>>() {});
                }
            }catch (Exception e){
                LogUtil.d(TAG,"old data change ,need clear first");
                mMMKV.remove(key);
                return null;
            }

        }
        return null;
    }

    /**
     * 保存车辆配置动作数据
     * */
    private void saveLocalCarConfig(String carConfigJson){
        if(carConfigJson != null){
            Map<String, Object> map = new HashMap<String, Object>();
            map.put(SceneDataModel.UPDATE_TIME_KEY, System.currentTimeMillis());
            map.put(SceneDataModel.DATA_KEY, carConfigJson);
            mMMKV.encode(SceneDataModel.KEY_MMKV_CAR_CONFIG, JSON.toJSONString(map));
        }
    }

    /**
     * 返回本地保存的广场模式列表数据，内置有效期判断
     * @return
     */
    public @Nullable CarConfig getStorageLocalCarConfig(){
        String key = SceneDataModel.KEY_MMKV_CAR_CONFIG;
        if(mMMKV.containsKey(key)){
            //可能存在历史数据，需进行下主动清理
            try{
                JSONObject jsonObject = JSONObject.parseObject(mMMKV.decodeString(key));
                long updateTime = (Long) jsonObject.get(SceneDataModel.UPDATE_TIME_KEY);
                String jsonString = JSON.parse(jsonObject.get(SceneDataModel.DATA_KEY).toString()).toString();
                //数据有效期判断
                boolean periodValidity = (System.currentTimeMillis() - updateTime) < SceneDataModel.UPDATE_INTERVAL;
                if(periodValidity){
                    return JSON.parseObject(jsonString, new TypeReference<CarConfig>() {});
                }
            }catch (Exception e){
                LogUtil.d(TAG,"old data change ,need clear first");
                mMMKV.remove(key);
                return null;
            }
        }
        return null;
    }

}
