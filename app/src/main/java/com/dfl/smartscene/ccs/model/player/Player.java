package com.dfl.smartscene.ccs.model.player;


import com.dfl.smartscene.ccs.bean.SettingOperation;

/**
 * <AUTHOR>
 * @date ：2023/5/30 11:41
 * @description ：
 */
public interface Player {

    String METHOD_SPECIAL = "special";//特殊的操作如播放每日推荐
    String METHOD_SCENE = "scene";//音乐的某一场景，书籍的某一分类
    String METHOD_LOCAL = "local";//本地音乐资源播放
    String PARAM_RESUME = "resume";//继续播放
    String PARAM_PAUSE = "pause";//暂停
    String PARAM_RECOMMEND = "recommend";//推荐
    String PARAM_COLLECT = "collect";//收藏

    String KEY_MUSIC_RECOMMEND = "MediaSessionExtraPlayDailyRecommendMusic";
    String KEY_MUSIC_COLLECT = "MediaSessionExtraPlayCollectMusic";

    String getTag();

    void start(SettingOperation settingOperation);

    void pause();

    void resume();

    void destroy();
}
