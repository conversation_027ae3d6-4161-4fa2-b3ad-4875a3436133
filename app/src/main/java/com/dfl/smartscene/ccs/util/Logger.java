package com.dfl.smartscene.ccs.util;

import android.util.Log;

import com.dfl.dflcommonlibs.log.LogUtil;

/**
 * <AUTHOR>
 * @date ：2023/4/17 14:32
 * @description ：
 */
public class Logger {
    private static LogUtil sLogUtil = new LogUtil();
    private static String sAppTag = "NewScenePattern";
    public static void init(){
        LogUtil.setAppTag(sAppTag);
    }
    public static void printJson(String tag , String msg){
        if (tag != null && tag.length() != 0 && msg != null && msg.length() != 0) {
            int segmentSize = 3072;
            long length = (long)msg.length();
            if (length <= (long)segmentSize) {
                Log.d(sAppTag, tag + ":" + msg);
            } else {
                while(msg.length() > segmentSize) {
                    String logContent = msg.substring(0, segmentSize);
                    msg = msg.replace(logContent, "");
                    Log.d(tag, "-------------------" + logContent);
                }
                Log.d(sAppTag, tag + ":" + msg);
            }

        }
    }
}
