package com.dfl.smartscene.ccs.util;

import android.content.Context;

import androidx.fragment.app.FragmentActivity;

import com.dfl.dflcommonlibs.commonclass.ActivityManager;
import com.dfl.dflcommonlibs.dialog.LibDialog;
import com.dfl.smartscene.R;
import com.iauto.uibase.utils.MLog;
import com.iauto.uicontrol.ProgressBar;
import com.iauto.uicontrol.TextBase;


/*
 * DialogProcessPopupView class
 * 彩蛋模式list删除Dialog类
 * <AUTHOR>
 * @date 2022/5/24
 *
 */
public class DialogProcessPopupView {
    private static final String    TAG       = "DialogProcessPopupView";
    private LibDialog mLibDialog = null;
    private ProgressBar mProgressBar = null;
    private TextBase mTextBase = null;
    private Context mContext = null;
    private int mDelCount = 0;


    /**
     * DialogProcessPopupView object constructor
     * @param context
     */
    public DialogProcessPopupView(Context context) {
        mContext = context;
    }

    /**
     * 打开一个带有删除progress的Dialog
     *
     * @param delCount 删除总数
     */
    public void showDelDialog(int delCount, LibDialog libDialog) {
        MLog.d(TAG, "showLoadingDialogNoBtn");
        mDelCount = delCount;
        // 关闭当前的dialog
        hideDialog();

        mLibDialog = libDialog;

        mLibDialog.showNow(((FragmentActivity) ActivityManager.getInstance().getCurrentActivity()).getSupportFragmentManager(), (String) null);

        mProgressBar = mLibDialog.getDialog().findViewById(R.id.del_progress_bar);
        if (null != mProgressBar) {
            mProgressBar.setCurPercentValue(0);
            mProgressBar.setTotalValue(delCount);
        }

        mTextBase = mLibDialog.getDialog().findViewById(R.id.del_info_text);
    }

    /**
     * 删除中更新删除数量
     *
     * @param deletedCount 已经删除的数量
     */
    public void updateDeleteInfo(int deletedCount) {
        if(null != mProgressBar) {
            mProgressBar.setCurPercentValue(deletedCount);
        }
        if (null != mTextBase) {
            String format = mContext.getString(R.string.string_surprise_egg_list_item_del_ing_message);
            String dspText= String.format(format,deletedCount,mDelCount);
            mTextBase.setText(dspText);
        }
    }

    /**
     * 是否表示
     *
     * @return boolean true:表示 false：非表示
     */
    public boolean isShow() {
        if (null == mLibDialog) {
            return false;
        }

        return mLibDialog.getShowsDialog();
    }

    /**
     * 关闭Dialog
     *
     */
    public void hideDialog() {
        MLog.d(TAG, "hideDialog ");

        if (null == mLibDialog) {
            return;
        }
        boolean isShowDialog = mLibDialog.getShowsDialog();
        MLog.d(TAG, "hideDialog isShowDialog: " + isShowDialog);

        if (isShowDialog) {
            mLibDialog.dismiss();
        }
    }
}