package com.dfl.smartscene.ccs.view.weight;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.CheckBox;

/**
 * <AUTHOR>
 * @date ：2022/12/6 11:30
 * @description ：
 */
public class CustomCheckBox extends CheckBox {

    public CustomCheckBox(Context context) {
        super(context);
    }

    public CustomCheckBox(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public CustomCheckBox(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public CustomCheckBox(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    private boolean enableToggle = true;

    public void setEnableToggle(boolean enableToggle) {
        this.enableToggle = enableToggle;
    }

    @Override
    public void toggle() {
        if(enableToggle){
            super.toggle();
        }
    }

}
