package com.dfl.smartscene.ccs.factory;

import android.view.ViewGroup;

import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseDialogFragment;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseView;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationMiddleSizeDialog;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationSmallSizeDialog;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationTimeStateDialog;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationVrSpeakDialog;
import com.dfl.smartscene.ccs.view.operationview.AdjustSlideOperation;
import com.dfl.smartscene.ccs.view.operationview.AdjustStepOperation;
import com.dfl.smartscene.ccs.view.operationview.ColorOperation;
import com.dfl.smartscene.ccs.view.operationview.DelayTimeOperation;
import com.dfl.smartscene.ccs.view.operationview.NaviCollectOperation;
import com.dfl.smartscene.ccs.view.operationview.NumberCompareOperation;
import com.dfl.smartscene.ccs.view.operationview.NumberPickerOperation;
import com.dfl.smartscene.ccs.view.operationview.RadioButtonOperation;
import com.dfl.smartscene.ccs.view.operationview.RecyclerButtonOperation;
import com.dfl.smartscene.ccs.view.operationview.RecyclerButtonRouteOperation;
import com.dfl.smartscene.ccs.view.operationview.RecyclerStateDayOperation;
import com.dfl.smartscene.ccs.view.operationview.RelationVerticalOperation;
import com.dfl.smartscene.ccs.view.operationview.SeatAdjustOperation;
import com.dfl.smartscene.ccs.view.operationview.SeatMemoryOperation;
import com.dfl.smartscene.ccs.view.operationview.StateTimeClockOperation;
import com.dfl.smartscene.ccs.view.operationview.TextOperation;
import com.dfl.smartscene.ccs.view.operationview.TextPickerOperation;
import com.dfl.smartscene.ccs.view.operationview.TimePickerOperation;
import com.dfl.smartscene.ccs.view.operationview.TriangleLayoutOperation;
import com.dfl.smartscene.ccs.view.operationview.VerticalLayoutOperation;
import com.iauto.uibase.utils.MLog;

import java.util.List;

/**
 * <AUTHOR> Zezheng
 * @date ：2022/9/2 9:21
 * @description ：操作视图工厂类
 */
public class OperationBaseViewFactory {

    private static final String TAG = "OperationBaseViewFactory";

    /**
     * 操作视图的容器类，一个带确定按钮的dialog
     * 用于新建操作
     * @param operationType
     * @param singleOperation
     * @return
     */
    public static OperationBaseDialogFragment creatOperationContainer(int operationType , SingleOperation singleOperation){
        MLog.d(TAG, "creatOperationContainer: " +singleOperation.getOperationId());
        switch (singleOperation.getOperationId()){
            case ConstantModelValue.OPERATION_ID_STATE_TIME_DAY:
            case ConstantModelValue.OPERATION_ID_STATE_TIME_CLOCK:
                return new OperationTimeStateDialog(operationType,singleOperation);
            case ConstantModelValue.OID_ACT_APP_VR_USER:
                return new OperationVrSpeakDialog(operationType,singleOperation);
            case ConstantModelValue.OID_CDT_EVENT_TIME:
            case ConstantModelValue.OID_CDT_DRIVE_RANGE_FUEL:
            case ConstantModelValue.OID_CDT_DRIVE_ELECTRICITY:
            case ConstantModelValue.OID_CDT_DRIVE_RANGE_POWER:
            case ConstantModelValue.OID_STA_ENV_OUT_TEMP:
            case ConstantModelValue.OID_STA_ENV_WEATHER:
            case ConstantModelValue.OID_STA_ENV_IN_AIR_QUALITY:
            case ConstantModelValue.OID_STA_ENV_OUT_AIR_QUALITY:
            case ConstantModelValue.OID_STA_DRIVE_RANGE_POWER:
            case ConstantModelValue.OID_STA_DRIVE_ELECTRICITY:
            case ConstantModelValue.OID_STA_DRIVE_SPEED:
            case ConstantModelValue.OPERATION_ID_ACTION_HVAC_TEMP:
            case ConstantModelValue.OPERATION_ID_ACTION_HVAC_CYCLE:
            case ConstantModelValue.OID_ACT_APP_MEDIA_EFFECT:
            case ConstantModelValue.OPERATION_ID_ACTION_MORE_TIME_DELAY:
                return new OperationMiddleSizeDialog(operationType,singleOperation);
            case ConstantModelValue.OID_CDT_DOOR_BACK:
            case ConstantModelValue.OID_STA_DOOR_FRONT_LEFT:
            case ConstantModelValue.OID_STA_DOOR_FRONT_RIGHT:
            case ConstantModelValue.OID_STA_DOOR_REAR_LEFT:
            case ConstantModelValue.OID_STA_DOOR_REAR_RIGHT:
            case ConstantModelValue.OID_STA_DOOR_BACK:
            case ConstantModelValue.OID_STA_WINDOW_FRONT_LEFT:
            case ConstantModelValue.OID_STA_WINDOW_FRONT_RIGHT:
            case ConstantModelValue.OID_STA_WINDOW_REAR_LEFT:
            case ConstantModelValue.OID_STA_WINDOW_REAR_RIGHT:
            case ConstantModelValue.OPERATION_ID_STATE_DRIVE_GEAR:
            case ConstantModelValue.OPERATION_ID_ACTION_HVAC_SWITCH:
            case ConstantModelValue.OPERATION_ID_ACTION_HVAC_BLOW_LEVEL:
            case ConstantModelValue.OPERATION_ID_ACTION_HVAC_DEFOG:
            case ConstantModelValue.OPERATION_ID_ACTION_HVAC_AC:
            case ConstantModelValue.OID_ACT_APP_NAVI_VOLUME:
            case ConstantModelValue.OID_ACT_APP_MEDIA_RADIO:
            case ConstantModelValue.OID_ACT_APP_MEDIA_END:
            case ConstantModelValue.OID_ACT_APP_VR_WEATHER:
            case ConstantModelValue.OID_ACT_MORE_SCREEN_LIGHT:
                return new OperationSmallSizeDialog(operationType,singleOperation);
            default:
                return new OperationBaseDialogFragment(operationType,singleOperation);
        }
    }

    /**
     * 操作视图的容器类，一个带确定按钮的dialog
     * 用于编辑操作
     * @param operationType
     * @param singleOperation
     * @return
     */
    public static OperationBaseDialogFragment creatOperationContainer(int operationType, int pos, SettingOperation settingOperation, SingleOperation singleOperation){
        MLog.d(TAG, "creatOperationContainer: " +singleOperation.getOperationId());
        switch (singleOperation.getOperationId()){
            case ConstantModelValue.OPERATION_ID_STATE_TIME_DAY:
            case ConstantModelValue.OPERATION_ID_STATE_TIME_CLOCK:
                return new OperationTimeStateDialog(operationType,pos,settingOperation,singleOperation);
            case ConstantModelValue.OID_ACT_APP_VR_USER:
                return new OperationVrSpeakDialog(operationType,pos,settingOperation,singleOperation);
            case ConstantModelValue.OID_CDT_EVENT_TIME:
            case ConstantModelValue.OID_CDT_DRIVE_RANGE_FUEL:
            case ConstantModelValue.OID_CDT_DRIVE_ELECTRICITY:
            case ConstantModelValue.OID_CDT_DRIVE_RANGE_POWER:
            case ConstantModelValue.OID_STA_ENV_OUT_TEMP:
            case ConstantModelValue.OID_STA_ENV_WEATHER:
            case ConstantModelValue.OID_STA_ENV_IN_AIR_QUALITY:
            case ConstantModelValue.OID_STA_ENV_OUT_AIR_QUALITY:
            case ConstantModelValue.OID_STA_DRIVE_RANGE_POWER:
            case ConstantModelValue.OID_STA_DRIVE_ELECTRICITY:
            case ConstantModelValue.OID_STA_DRIVE_SPEED:
            case ConstantModelValue.OPERATION_ID_ACTION_HVAC_TEMP:
            case ConstantModelValue.OPERATION_ID_ACTION_HVAC_CYCLE:
            case ConstantModelValue.OID_ACT_APP_MEDIA_EFFECT:
            case ConstantModelValue.OPERATION_ID_ACTION_MORE_TIME_DELAY:
                return new OperationMiddleSizeDialog(operationType,pos,settingOperation,singleOperation);
            case ConstantModelValue.OID_CDT_DOOR_BACK:
            case ConstantModelValue.OID_STA_DOOR_FRONT_LEFT:
            case ConstantModelValue.OID_STA_DOOR_FRONT_RIGHT:
            case ConstantModelValue.OID_STA_DOOR_REAR_LEFT:
            case ConstantModelValue.OID_STA_DOOR_REAR_RIGHT:
            case ConstantModelValue.OID_STA_DOOR_BACK:
            case ConstantModelValue.OID_STA_WINDOW_FRONT_LEFT:
            case ConstantModelValue.OID_STA_WINDOW_FRONT_RIGHT:
            case ConstantModelValue.OID_STA_WINDOW_REAR_LEFT:
            case ConstantModelValue.OID_STA_WINDOW_REAR_RIGHT:
            case ConstantModelValue.OPERATION_ID_STATE_DRIVE_GEAR:
            case ConstantModelValue.OPERATION_ID_ACTION_HVAC_SWITCH:
            case ConstantModelValue.OPERATION_ID_ACTION_HVAC_BLOW_LEVEL:
            case ConstantModelValue.OPERATION_ID_ACTION_HVAC_DEFOG:
            case ConstantModelValue.OPERATION_ID_ACTION_HVAC_AC:
            case ConstantModelValue.OID_ACT_APP_NAVI_VOLUME:
            case ConstantModelValue.OID_ACT_APP_MEDIA_RADIO:
            case ConstantModelValue.OID_ACT_APP_MEDIA_END:
            case ConstantModelValue.OID_ACT_APP_VR_WEATHER:
            case ConstantModelValue.OID_ACT_MORE_SCREEN_LIGHT:
                return new OperationSmallSizeDialog(operationType,pos,settingOperation,singleOperation);
            default:
                return new OperationBaseDialogFragment(operationType,pos,settingOperation,singleOperation);
        }
    }
    /**
     * 根据传入操作的视图类型，给parent挂载不同的操作视图
     * @param singleOperation
     * @param parent
     * @return
     */
    public static OperationBaseView addOperationBaseView(SingleOperation singleOperation, ViewGroup parent) {

        MLog.d(TAG, "ViewType = " + singleOperation.getViewType() + ", name = " + singleOperation.getOperationName() + ", deviceId = " + singleOperation.getDeviceId() + ", operationId = " + singleOperation.getOperationId() );
        switch (singleOperation.getViewType()) {
            case ConstantModelValue.VIEW_TYPE_SINGLE_CHOOSE:
                if(singleOperation.getOperationId().equals(ConstantModelValue.OID_ACT_APP_NAVI_START_TRAFFIC)){
                    return new RecyclerButtonRouteOperation(singleOperation, parent);
                }
                return new RecyclerButtonOperation(singleOperation, parent);

            case ConstantModelValue.VIEW_TYPE_RADIO_TEXT_GROUP:
            case ConstantModelValue.VIEW_TYPE_RADIO_PIC_GROUP:
            case ConstantModelValue.VIEW_TYPE_RADIO_COLOR_GROUP:
//            case ConstantModelValue.VIEW_TYPE_COLOR_BAR:
                return new RadioButtonOperation(singleOperation, parent);

//            case ConstantModelValue.VIEW_TYPE_RADIO_COLOR_GROUP:
            case ConstantModelValue.VIEW_TYPE_COLOR_BAR:
                return new ColorOperation(singleOperation, parent);

            case ConstantModelValue.VIEW_TYPE_ADJUST_SLIDE:
                return new AdjustSlideOperation(singleOperation, parent);

            case ConstantModelValue.VIEW_TYPE_TIME_HOUR_MINUTE:
                return new TimePickerOperation(singleOperation, parent, true);

            case ConstantModelValue.VIEW_TYPE_TIME_MINUTE_SECOND:
                return new TimePickerOperation(singleOperation, parent, false);

            case ConstantModelValue.VIEW_TYPE_NUM_PICKER:
                return new NumberPickerOperation(singleOperation, parent);
            case ConstantModelValue.VIEW_TYPE_NUM_COMPARE:
                return new NumberCompareOperation(singleOperation, parent);
            case ConstantModelValue.VIEW_TYPE_LAYOUT_VERTICAL:
                List<SingleOperation> subOperations = singleOperation.getSubOperations();
                if(null == subOperations) {
                    return null;
                } else {
                    MLog.d(TAG, "subOperations.size():" + subOperations.size());
                    if(subOperations.size() == 3){
                        return new TriangleLayoutOperation(singleOperation, parent);
                    }
                    return new VerticalLayoutOperation(singleOperation, parent);
                }
            case ConstantModelValue.VIEW_TYPE_ADJUST_STEP:
                return new AdjustStepOperation(singleOperation, parent);

            case ConstantModelValue.VIEW_TYPE_STATE_TIME_DAY:
                return new RecyclerStateDayOperation(singleOperation, parent);
            case ConstantModelValue.VIEW_TYPE_LAYOUT_VERTICAL_RELATED:
                return new RelationVerticalOperation(singleOperation, parent);

            case ConstantModelValue.VIEW_TYPE_STATE_TIME_CLOCK:
                return new StateTimeClockOperation(singleOperation, parent);

            case ConstantModelValue.VIEW_TYPE_TEXT:
                return new TextOperation(singleOperation, parent);

            case ConstantModelValue.VIEW_TYPE_NAVI_COLLECT_POINT:
                MLog.d(TAG, "NaviCollectOperation" );
                return new NaviCollectOperation(singleOperation, parent);

            case ConstantModelValue.VIEW_TYPE_MINUTE_SECOND:
                return new DelayTimeOperation(singleOperation, parent);

            case ConstantModelValue.VIEW_TYPE_SEAT_MEMORY:
                return new SeatMemoryOperation(singleOperation, parent);

            case ConstantModelValue.VIEW_TYPE_SEAT_ADJUST:
                return new SeatAdjustOperation(singleOperation, parent);
            case ConstantModelValue.VIEW_TYPE_TEXT_PICKER:
                return new TextPickerOperation(singleOperation, parent);
            default:
                return null;
        }
    }
}
