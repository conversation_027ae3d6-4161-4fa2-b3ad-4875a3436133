package com.dfl.smartscene.ccs.db.dao.scene;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;


import com.dfl.smartscene.ccs.db.entity.scene.SceneActionListEntity;

import java.util.List;

import io.reactivex.Observable;

@Dao
public interface SceneActionListDao {

    @Insert
    void insertStudent(SceneActionListEntity item);

    @Delete
    void deleteStudent(SceneActionListEntity item);

    @Update
    void updateStudent(SceneActionListEntity item);

    @Query("SELECT * FROM scene_action_list")
    Observable<List<SceneActionListEntity>> queryAll();

    @Query("SELECT * FROM scene_action_list WHERE :mSceneId='' or mSceneId= :mSceneId")
    Observable<List<SceneActionListEntity>> queryBySceneId(String mSceneId);

    @Query("SELECT * FROM scene_action_list WHERE :mDeviceId='' or mSceneId= :mDeviceId")
    Observable<List<SceneActionListEntity>> queryByDeviceId(String mDeviceId);

    @Query("SELECT * FROM scene_action_list WHERE :mOperationId='' or mOperationId= :mOperationId")
    Observable<List<SceneActionListEntity>> queryByOperationId(String mOperationId);

    @Query("SELECT * FROM scene_action_list WHERE mSceneId in (:mSceneIds)")
    Observable<List<SceneActionListEntity>> queryBySceneIds(String[] mSceneIds);
}
