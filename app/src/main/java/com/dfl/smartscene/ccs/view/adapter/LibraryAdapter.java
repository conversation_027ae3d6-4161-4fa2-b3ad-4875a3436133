package com.dfl.smartscene.ccs.view.adapter;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.DimenRes;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;
import androidx.navigation.fragment.NavHostFragment;
import androidx.recyclerview.widget.RecyclerView;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseAdapter;
import com.dfl.smartscene.ccs.base.BaseHolder;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SceneCategory;
import com.dfl.smartscene.ccs.busevent.CollectEvent;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.constant.ConstantViewValue;
import com.dfl.smartscene.ccs.constant.ScenePatternFuncDef;
import com.dfl.smartscene.ccs.eventTrack.BigDataManager;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.model.DAModel;
import com.dfl.smartscene.ccs.model.SceneDataModel;
import com.dfl.smartscene.ccs.model.manager.SceneCornerManager;
import com.dfl.smartscene.ccs.util.CarConfigUtil;
import com.dfl.smartscene.ccs.util.DialogManager;
import com.dfl.smartscene.ccs.util.HandlerUtil;
import com.dfl.smartscene.ccs.util.RecyclerViewUtil;
import com.dfl.smartscene.ccs.view.ViewControlManager;
import com.dfl.smartscene.ccs.viewmodel.CollectViewModel;
import com.dfl.smartscene.ccs.viewmodel.MainFragmentViewModel;

/**
 * 发现页面内容
 */
public class LibraryAdapter extends BaseAdapter<SceneCategory> {
    private static final String TAG = LibraryAdapter.class.getSimpleName();
    //轻松一刻
    private static final int VIEW_TYPE_RELAX_MOMENT = 1;
    //快捷场景
    private static final int VIEW_TYPE_QUICK_SCENE = 2;
    private static final int SHOW_SCENE_COUNT = 40;
    private static final @DimenRes
    int SHOW_SPACE = ConstantViewValue.COMMON_SHOW_SPACE;//item之间的间距
    private final CollectViewModel mCollectViewModel;
    private final MainFragmentViewModel mMainFragmentViewModel;
    private final LibrarySceneAdapter mLibrarySceneAdapter = new LibrarySceneAdapter();
    public LibraryAdapter(Fragment fragment){
        mCollectViewModel = new ViewModelProvider(fragment).get(CollectViewModel.class);
        mMainFragmentViewModel = new ViewModelProvider(fragment.requireActivity()).get(MainFragmentViewModel.class);
        initObserver(fragment);
    }
    @Override
    public int getItemViewType(int position) {
        if (ConstantModelValue.SCENE_CATEGORY_ID_LIBRARY_CARD.equals(getDataList().get(position).getCategoryId())
                && CarConfigUtil.isCCS()){
            return VIEW_TYPE_RELAX_MOMENT;
        }else {
            return VIEW_TYPE_QUICK_SCENE;
        }
    }

    @Override
    protected BaseHolder<SceneCategory> getViewHolder(View view, int viewType) {
        if (viewType == VIEW_TYPE_RELAX_MOMENT){
            return new ViewHolderRelaxCard(view);
        }else {
            return new ViewHolderQuickCard(view);
        }
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.module_library;
    }

    //快捷场景
    public class ViewHolderQuickCard extends BaseHolder<SceneCategory> {
        private final TextView mTextViewTitle;//标题
        private final ConstraintLayout mConstraintLayoutTitle;
        public ViewHolderQuickCard(View itemView) {
            super(itemView);
            RecyclerView recyclerView = itemView.findViewById(R.id.recycleview_library_module);
            mTextViewTitle = itemView.findViewById(R.id.textview_library_module_title);
            mConstraintLayoutTitle = itemView.findViewById(R.id.layout_library_module_title);
            RecyclerViewUtil.setGridRecycleView(recyclerView, RecyclerView.VERTICAL,2, SHOW_SPACE, R.dimen.y_px_24,false);
            recyclerView.setAdapter(mLibrarySceneAdapter);
        }

        @Override
        public void setupData(SceneCategory sceneCategory, int position) {
            mTextViewTitle.setText(sceneCategory.getCategoryName());
            int count = Math.min(sceneCategory.getSceneBeanList().size(),SHOW_SCENE_COUNT);
            mLibrarySceneAdapter.setDataList(sceneCategory.getSceneBeanList().subList(0,count));
            //设置标题的可见即可说
//            DescriptionUtil.generateButtonDescriptionVisibleToSay(sceneCategory.getCategoryName(), mConstraintLayoutTitle);
            //收藏按钮点击监听
            mLibrarySceneAdapter.setOnButtonClickListener(new BaseAdapter.OnButtonClickListener<SceneBean>() {
                @Override
                public void onItemClick(View view, int viewType, SceneBean sceneBean, int position) {

                    SceneCornerManager.recordClickTime(sceneBean.getSceneId(),sceneBean.getSceneCorner());
                    mLibrarySceneAdapter.notifyItemChanged(position);

                    if(view.getId() == R.id.imagebutton_item_library_scene_collect){
                        if(sceneBean.isCollectState()){
                            ViewControlManager.uncollectScene(sceneCategory.getCategoryId(),sceneBean);
                            //埋点：点击首页收藏
                            LogUtil.d(TAG, "点击取消收藏");
                            BigDataManager.getInstance().writeEventMod3(view.getContext().getString(R.string.scene_click_un_collect));
                        }else {
                            LogUtil.d(TAG, "点击收藏");
                            ViewControlManager.collectScene(sceneCategory.getCategoryId(),sceneBean);
                            //埋点：点击首页取消收藏
                            BigDataManager.getInstance().writeEventMod3(view.getContext().getString(R.string.scene_click_collect));
                        }
                    }else if(view.getId() == R.id.image_view_item_library_scene_excute){
                        DialogManager.showExcuteSceneDialog(sceneBean);
                        //埋点：点击首页播放按钮
                        LogUtil.d(TAG, "点击播放");
                        BigDataManager.getInstance().writeEventMod3(view.getContext().getString(R.string.scene_click_play));
                    }
                }
            });
            mLibrarySceneAdapter.setOnItemClickListener(new BaseAdapter.OnItemClickListener<SceneBean>() {
                @Override
                public void onItemClick(View view, int viewType, SceneBean sceneBean, int position) {
                    SceneCornerManager.recordClickTime(sceneBean.getSceneId(),sceneBean.getSceneCorner());
                    mLibrarySceneAdapter.notifyItemChanged(position);

                    //埋点：点击场景模式
                    LogUtil.d(TAG, TextUtils.isEmpty(sceneBean.getSceneName()) ? "" : sceneBean.getSceneName());
                    BigDataManager.getInstance().writeEventMod3(TextUtils.isEmpty(sceneBean.getSceneName()) ? "" : sceneBean.getSceneName());

                    Bundle bundle = new Bundle();
                    bundle.putSerializable(ConstantViewValue.FRAGMENT_KEY_SCENE_EDITOR_SCENE_BEAN,sceneBean);
                    bundle.putString(ConstantViewValue.FRAGMENT_KEY_SCENE_EDITOR_CATEGORY_ID, ConstantModelValue.CATEGORY_ID_COLLECT);
                    bundle.putString(ConstantViewValue.FRAGMENT_KEY_SCENE_EDITOR_TYPE,ConstantViewValue.SCENE_EDITOR_TYPE_SCENE_SHOW);
                    Navigation.findNavController(view).navigate(R.id.action_mainFragment_to_sceneDetailFragment,bundle);
                }
            });
            //点击标题跳转子页面
//            mConstraintLayoutTitle.setOnClickListener(v -> {
//                NavController navController = Navigation.findNavController(v);
//                Bundle bundle = new Bundle();
//                bundle.putSerializable(ConstantViewValue.FRAGMENT_KEY_LIBRARY_CHILD_DATA,sceneCategory);
//                navController.navigate(R.id.action_mainFragment_to_libraryChildFragment,bundle);
//            });
        }
    }
    //轻松一刻
    public static class ViewHolderRelaxCard extends BaseHolder<SceneCategory> {
        private final ConstraintLayout mConstraintLayoutTitle;//标题栏，用于点击跳转
        private final TextView mTextViewTitle;//卡片标题
        private final LibraryCardAdapter mLibraryCardAdapter;
        public ViewHolderRelaxCard(View itemView) {
            super(itemView);
            RecyclerView recyclerView = itemView.findViewById(R.id.recycleview_library_module);
            mTextViewTitle = itemView.findViewById(R.id.textview_library_module_title);
            mConstraintLayoutTitle = itemView.findViewById(R.id.layout_library_module_title);

            RecyclerViewUtil.setGridRecycleView(recyclerView, RecyclerView.VERTICAL,2, SHOW_SPACE, R.dimen.y_px_24,false);
            mLibraryCardAdapter = new LibraryCardAdapter();
            recyclerView.setAdapter(mLibraryCardAdapter);
        }

        @Override
        public void setupData(SceneCategory category, int position) {
            //设置标题的可见即可说
//            DescriptionUtil.generateButtonDescriptionVisibleToSay(category.getCategoryName(), mConstraintLayoutTitle);
            mLibraryCardAdapter.setDataList(category.getSceneBeanList());
            mTextViewTitle.setText(category.getCategoryName());
            //点击标题跳转子页面
//            mConstraintLayoutTitle.setOnClickListener(v -> {
//                NavController navController = Navigation.findNavController(v);
//                Bundle bundle = new Bundle();
//                bundle.putSerializable(ConstantViewValue.FRAGMENT_KEY_LIBRARY_CHILD_DATA, category);
//                navController.navigate(R.id.action_mainFragment_to_libraryCardChildFragment, bundle);
//
//                //埋点：点击去首页列表标题
//                LogUtil.d("LibraryCardModule", TextUtils.isEmpty(mTextViewTitle.getText().toString()) ? "" : mTextViewTitle.getText().toString());
//                BigDataManager.getInstance().writeEventMod3(TextUtils.isEmpty(mTextViewTitle.getText().toString()) ? "" : mTextViewTitle.getText().toString());
//            });
            //点击执行场景，或者跳转菜单页面
            mLibraryCardAdapter.setOnItemClickListener(new BaseAdapter.OnItemClickListener<SceneBean>() {
                @Override
                public void onItemClick(View view, int viewType, SceneBean sceneBean, int position) {
                    //埋点：点击场景模式
                    LogUtil.d("LibraryCardModule", TextUtils.isEmpty(sceneBean.getSceneName()) ? "" : sceneBean.getSceneName());
                    BigDataManager.getInstance().writeEventMod3(TextUtils.isEmpty(sceneBean.getSceneName()) ? "" : sceneBean.getSceneName());
                    if (sceneBean.getSceneId().equals(ConstantModelValue.SCENE_ID_OLD_EGG)) {
                        if (!DAModel.getInstance().isUserCenterLogin()) {
                            Intent intent = new Intent();
                            intent.setClassName(ScenePatternFuncDef.CENTER_PACKAGE_NAME,
                                    ScenePatternFuncDef.CENTER_ACTIVITY_NAME);
                            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED);
                            ScenePatternApp.getInstance().startActivity(intent);
                        } else {
                            try {
                                NavController navController = Navigation.findNavController(view);
                                navController.navigate(R.id.action_mainFragment_to_surpriseEggListFragment);
                            } catch (Exception e) {
                                LogUtil.e("TAG", "e:" + e.getMessage());
                            }
                        }
                    } else if (sceneBean.getSceneId().equals(ConstantModelValue.SCENE_ID_OLD_RELAX)) {
                        NavController navController = Navigation.findNavController(view);
                        navController.navigate(R.id.action_mainFragment_to_relieveStressPatternFragment);
                    } else {
                        ViewControlManager.executeCardScene(sceneBean,false);
                    }
                }
            });

        }
    }

    private void initObserver(Fragment fragment){
        //收藏状态返回监听
        mCollectViewModel.getCollectEventMutableLiveData().observe(fragment.getViewLifecycleOwner(), new Observer<CollectEvent>() {
            @Override
            public void onChanged(CollectEvent collectEvent) {
                if(null != collectEvent){
                    LogUtil.d(TAG, "on change collect event : "+collectEvent.getCategoryId());
                    if(collectEvent.getCategoryId().equals(ConstantModelValue.SCENE_CATEGORY_ID_LIBRARY_SERVICE) || ConstantModelValue.CATEGORY_ID_COLLECT.equals(collectEvent.getCategoryId())){
                        if(CollectEvent.COLLECT_EVENT_TYPE_SINGLE.equals(collectEvent.getEventType())){
                            mLibrarySceneAdapter.onCollectChange(collectEvent.getSceneId(), collectEvent.isStatus());
                            if(collectEvent.isStatus()){
                                NavController navController = NavHostFragment.findNavController(fragment);
                                if (navController.getCurrentDestination()!=null&&
                                        navController.getCurrentDestination().getDisplayName().contains("sceneDetailFragment")) {
//                                    ToastManager.showToast("已收藏至\"我的" + "\"");
                                    HandlerUtil.getMainHandler().postDelayed(new Runnable() {
                                        @Override
                                        public void run() {
                                            mMainFragmentViewModel.setMainPagePos(1);
                                            mMainFragmentViewModel.setMyPageCategoryId(collectEvent.getCategoryId());
                                            navController.navigateUp();
                                        }
                                    },500);
                                    return;
                                }
//                                String toastText = "已收藏至\"我的" + "\",点击查看";
//                                ToastManager.showSpanToast(toastText,"点击查看", new MyClickableSpan() {
//                                    @SuppressLint("RestrictedApi")
//                                    @Override
//                                    public void onClick(@NonNull View widget) {
//                                        if(AntiShakeUtil.check(widget)){
//                                            return;
//                                        }
//                                        mMainFragmentViewModel.setMainPagePos(1);
//                                        mMainFragmentViewModel.setMyPageCategoryId(collectEvent.getCategoryId());
//                                        //20240528:fixCSS5-9449  推荐-便捷服务更多页面，收藏至我的，点击查看更多无法跳转
//                                        LogUtil.d(TAG, "collectEvent click");
//                                        try {
//                                            if (navController.getCurrentDestination()!= null) {
//                                                String currentFragment = navController.getCurrentDestination().getDisplayName();
//                                                if(currentFragment.contains("libraryChildFragment")){
//                                                    navController.navigateUp();
//                                                } else if (currentFragment.contains("sceneDetailFragment")) {
//                                                    navController.popBackStack(R.id.mainFragment, false);
//                                                }
//                                            }
//                                        }catch (Exception e){
//                                            LogUtil.e(TAG, "findNavController throw exception : " +e.getMessage());
//                                        }
//
//                                    }
//                                });
                            }else{
//                                ToastManager.showToast("已取消收藏");
                            }
                        }else {
                            SceneDataModel.getInstance().addSquareSceneCollectState(ConstantModelValue.SCENE_CATEGORY_ID_LIBRARY_SERVICE,mLibrarySceneAdapter.getDataList());
                            mLibrarySceneAdapter.notifyDataSetChanged();
                        }
                    }
                }
            }
        });
    }
}
