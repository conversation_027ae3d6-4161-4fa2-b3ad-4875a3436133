package com.dfl.smartscene.ccs.viewmodel;

import androidx.lifecycle.MutableLiveData;


import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.model.AiotModel;
import com.dfl.smartscene.ccs.viewmodel.base.BaseViewModel;

import java.util.List;

import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR>
 * @date ：2023/3/6 16:17
 * @description ：
 */
public class LibraryDetailViewModel extends BaseViewModel {

    private MutableLiveData<String> aiotDesc = new MutableLiveData<>();

    public void requestSceneUnbuyDevices(List<SettingOperation> ops){
        AiotModel.getInstance().requestSceneUnbuyDevices(ops).observeOn(AndroidSchedulers.mainThread()).subscribe(new Observer<String>() {
            @Override
            public void onSubscribe(Disposable d) {
                addDisposable(d);
            }

            @Override
            public void onNext(String s) {
                if(s != null){
                    aiotDesc.postValue(s);
                }
            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onComplete() {

            }
        });
    }
    public MutableLiveData<String> getAiotDesc() {
        return aiotDesc;
    }
}
