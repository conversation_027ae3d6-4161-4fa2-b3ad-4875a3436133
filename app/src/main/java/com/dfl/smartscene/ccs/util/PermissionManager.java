package com.dfl.smartscene.ccs.util;

import android.Manifest;
import android.app.Activity;

import com.dfl.dflcommonlibs.permission.PermissionUtil;
import com.dfl.dflcommonlibs.rxbus.RxBus;
import com.dfl.smartscene.ccs.busevent.PermissionEvent;
import com.iauto.uibase.utils.MLog;
import com.tencent.mmkv.MMKV;

/**
 * <AUTHOR>
 * @date ：2023/4/1 16:18
 * @description ：
 */
public class PermissionManager {
    private static final String TAG = "PermissionManager";
    private static final String KEY_MMKV_PERMISSION = "KEY_MMKV_PERMISSION";

    public static boolean checkLocalPermission(){
        MMKV mMMKV = MMKV.defaultMMKV();
        return mMMKV.containsKey(KEY_MMKV_PERMISSION) && mMMKV.decodeBool(KEY_MMKV_PERMISSION);
    }

    public static void requestPermission(Activity activity){
        MMKV mMMKV = MMKV.defaultMMKV();
        String[] permissions = {Manifest.permission.INTERNET,
                Manifest.permission.ACCESS_NETWORK_STATE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE,
                Manifest.permission.READ_EXTERNAL_STORAGE,
                "android.permission.INTERNET_CONTROL",
                "android.permission.VEHICLE_DATA"
        };

        PermissionUtil.getInstance().requestPermissions(permissions, new PermissionUtil.PermissionCallBack() {
            @Override
            public void onPermissionPassed() {
                MLog.d(TAG, "权限通过");
                if(mMMKV.containsKey(KEY_MMKV_PERMISSION) && mMMKV.decodeBool(KEY_MMKV_PERMISSION)){
                    return;
                }
                mMMKV.encode(KEY_MMKV_PERMISSION,true);
                RxBus.getDefault().post(new PermissionEvent(true));
            }

            @Override
            public void onPermissionFailure(String[] strings) {
                mMMKV.encode(KEY_MMKV_PERMISSION,false);

                for (String permission : permissions) {
                    MLog.d(TAG, "权限未通过" + permission);
                }
                activity.finish();
            }
        }, activity);

    }
}
