package com.dfl.smartscene.ccs.util;

import com.dfl.smartscene.ccs.constant.SurpriseEggFuncDef;
import com.dfl.smartscene.ccs.model.SurpriseEgg;
import com.dfl.smartscene.ccs.model.SurpriseEggList;
import com.iauto.uibase.utils.MLog;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/*
 * json文件解析 class
 * 只提供静态方法
 * <AUTHOR>
 * @date 2022/5/24
 */
public class SurpriseEggJsonDataManager {

    private static final String sDataFormat = "yyyy-MM-dd HH:mm:ss";
    /**
     * 将指定的json字符串转换到彩蛋list中
     * @param jsonString 指定的json字符串
     * @param list 指定的彩蛋list
     * @return boolean true 解析成功 false 解析失败
     * 
     */
    public static boolean getSurpriseEggListReqResult(String jsonString, SurpriseEggList list) {
        list.clearSurpriseEggList();

        try {
            JSONObject jsonAllObject = new JSONObject(jsonString);
            int result = jsonAllObject.getInt(SurpriseEggFuncDef.JSON_ITEM_RESULT);
            MLog.d("getSurpriseEggListReqResult","jsonString："+jsonString);
            MLog.d("getSurpriseEggListReqResult","result："+result);
            // 彩蛋list取得结果判断
            if (SurpriseEggFuncDef.SURPRISE_EGG_LIST_REQ_SUCCESS != result) {
                String errMsg = jsonAllObject.getString(SurpriseEggFuncDef.JSON_ITEM_MESSAGE);
                if (null != errMsg) {
                    MLog.d("getSurpriseEggListReqResult", errMsg);
                }
                return false;
            }

            // 解析json中的彩蛋list
            JSONArray jsonRowArray = jsonAllObject.getJSONArray(SurpriseEggFuncDef.JSON_ITEM_ROW);
            for(int i = 0; i < jsonRowArray.length(); i++) {
                JSONObject jsonRowItem = jsonRowArray.getJSONObject(i);
                SurpriseEgg egg = new SurpriseEgg();
                egg.setEggID(jsonRowItem.getInt(SurpriseEggFuncDef.JSON_ITEM_ID));
                MLog.d("getSurpriseEggListReqResult","ID："+jsonRowItem.getInt(SurpriseEggFuncDef.JSON_ITEM_ID));

                String tempString = jsonRowItem.getString(SurpriseEggFuncDef.JSON_ITEM_TITLE);
                if (null != tempString) {
                    egg.setName(tempString);
                } else {
                    egg.setName("");
                }
                MLog.d("getSurpriseEggListReqResult","Title："+tempString);


                tempString = jsonRowItem.getString(SurpriseEggFuncDef.JSON_ITEM_URL);
                if (null != tempString) {
                    egg.setURL(tempString);
                } else {
                    egg.setURL("");
                }
                MLog.d("getSurpriseEggListReqResult","URL："+tempString);

                tempString = jsonRowItem.getString(SurpriseEggFuncDef.JSON_ITEM_CREATE_TIME);
                if (null != tempString) {
                    egg.makeCreateDate(tempString);
                } else {
                    egg.makeCreateDate("");
                }
                MLog.d("getSurpriseEggListReqResult","TIME："+tempString);


                int voiceType = jsonRowItem.getInt(SurpriseEggFuncDef.JSON_ITEM_VOICE_TYPE);
                MLog.d("getSurpriseEggListReqResult","voice type："+jsonRowItem.getInt(SurpriseEggFuncDef.JSON_ITEM_VOICE_TYPE));
                egg.setVoiceType(voiceType);
                if (SurpriseEggFuncDef.JSON_ITEM_VOICE_TYPE_TEXT == voiceType) {
                    tempString = jsonRowItem.getString(SurpriseEggFuncDef.JSON_ITEM_VOICE_TEXT);
                    if (null != tempString) {
                        egg.setTTSText(tempString);
                    } else {
                        egg.setTTSText("");
                    }
                    MLog.d("getSurpriseEggListReqResult","TTS TEXT："+tempString);
                    egg.setVoiceFile("");
                } else {
                    tempString = jsonRowItem.getString(SurpriseEggFuncDef.JSON_ITEM_VOICE_FILE);
                    if (null != tempString) {
                        egg.setVoiceFile(tempString);
                    } else {
                        egg.setVoiceFile("");
                    }
                    MLog.d("getSurpriseEggListReqResult","VOICE FILE ："+tempString);
                    egg.setTTSText("");
                }
                JSONArray jsonSurpriseInfoPaths = jsonRowItem.getJSONArray(SurpriseEggFuncDef.JSON_ITEM_SURPRISE_INFO_FILES);
                MLog.d("getSurpriseEggListReqResult","paths length："+jsonSurpriseInfoPaths.length());
                if (jsonSurpriseInfoPaths.length() < 1) {

                } else {
                    JSONObject jsonSurpriseInfoPath = jsonSurpriseInfoPaths.getJSONObject(0);
                    MLog.d("getSurpriseEggListReqResult","jsonSurpriseInfoPath"+jsonSurpriseInfoPath.toString());

                    tempString = jsonSurpriseInfoPath.getString(SurpriseEggFuncDef.JSON_ITEM_FILE_PATH);
                    if (null != tempString) {
                        egg.setImageURL(tempString);
                    } else {
                        egg.setImageURL("");
                    }
                    MLog.d("getSurpriseEggListReqResult","Image file path："+tempString);
                }

                list.addSurpriseEgg(egg);
            }
            list.sortSurpriseEgg();

        } catch (JSONException e) {
            e.printStackTrace();
            MLog.d("getSurpriseEggListReqResult","JSONException"+e.getMessage());
            return false;
        }
        return true;
    }

    /**
     * 解析删除彩蛋item请求结果的json
     * @param jsonString 指定的json字符串
     * @return boolean true 删除成功 false 删除失败
     */
    public static boolean getSurpriseEggDelReqResult(String jsonString) {
        try {
            JSONObject jsonAllObject = new JSONObject(jsonString);
            int result = jsonAllObject.getInt(SurpriseEggFuncDef.JSON_ITEM_RESULT);
            if (SurpriseEggFuncDef.SURPRISE_EGG_LIST_REQ_SUCCESS != result) {
                String errMsg = jsonAllObject.getString(SurpriseEggFuncDef.JSON_ITEM_MESSAGE);
                if (null != errMsg) {
                    MLog.d("getSurpriseEggDelReqResult", errMsg);
                }
                return false;
            }
        } catch (JSONException e) {
            e.printStackTrace();
            return false;
        }

        return true;
    }

    /**
     * 将检索结果json字符串转换到彩蛋list中
     * @param jsonString 指定的json字符串
     * @param egg 检索结果
     * @return boolean true 解析成功 false 解析失败
     *
     */
    public static boolean getSurpriseEggKeywordReqResult(String jsonString, SurpriseEgg egg) {

        try {
            JSONObject jsonAllObject = new JSONObject(jsonString);
            int result = jsonAllObject.getInt(SurpriseEggFuncDef.JSON_ITEM_RESULT);
            MLog.d("getSurpriseEggKeywordReqResult","result："+result);
            // 彩蛋取得结果判断
            if (SurpriseEggFuncDef.SURPRISE_EGG_LIST_REQ_SUCCESS != result) {
                String errMsg = jsonAllObject.getString(SurpriseEggFuncDef.JSON_ITEM_MESSAGE);
                if (null != errMsg) {
                    MLog.d("getSurpriseEggKeywordReqResult", errMsg);
                }
                return false;
            }

            // 解析json中的彩蛋
            JSONObject jsonRowItem = jsonAllObject.getJSONObject(SurpriseEggFuncDef.JSON_ITEM_ROW);

//            Date startTime = null;
//            Date endTime = null;
//            String tempString = jsonRowItem.getString(SurpriseEggFuncDef.JSON_ITEM_TIME_START);
//            if (null != tempString) {
//                startTime = ChangeCreateDate(tempString);
//                if(null == startTime) {
//                    return false;
//                }
//            } else {
//                return false;
//            }
//            MLog.d("getSurpriseEggKeywordReqResult","startTime:"+tempString);
//
//            tempString = jsonRowItem.getString(SurpriseEggFuncDef.JSON_ITEM_TIME_END);
//            if (null != tempString) {
//                endTime = ChangeCreateDate(tempString);
//                if(null == endTime) {
//                    return false;
//                }
//            } else {
//                return false;
//            }
//            MLog.d("getSurpriseEggKeywordReqResult","endTime:"+tempString);
//
//            Calendar calendar = Calendar.getInstance();
//            calendar.setTimeInMillis(System.currentTimeMillis());
//            Date currentTime = calendar.getTime();
//
//            if ((currentTime.getTime() >= startTime.getTime()) && (currentTime.getTime() <= endTime.getTime())) {

            String tempString = jsonRowItem.getString(SurpriseEggFuncDef.JSON_ITEM_URL);
            if (null != tempString) {
                MLog.d("getSurpriseEggListReqResult","URL："+tempString);
                egg.setURL(tempString);
            } else {
                return false;
            }

            int voiceType = jsonRowItem.getInt(SurpriseEggFuncDef.JSON_ITEM_VOICE_TYPE);
            MLog.d("getSurpriseEggListReqResult","voice type："+jsonRowItem.getInt(SurpriseEggFuncDef.JSON_ITEM_VOICE_TYPE));
            egg.setVoiceType(voiceType);
            if (SurpriseEggFuncDef.JSON_ITEM_VOICE_TYPE_TEXT == voiceType) {
                tempString = jsonRowItem.getString(SurpriseEggFuncDef.JSON_ITEM_VOICE_TEXT);
                if (null != tempString) {
                    MLog.d("getSurpriseEggListReqResult","voice Text："+tempString);
                    egg.setTTSText(tempString);
                } else {
                    egg.setTTSText("");
                }
            }else {
                tempString = jsonRowItem.getString(SurpriseEggFuncDef.JSON_ITEM_VOICE_FILE);
                if (null != tempString) {
                    MLog.d("getSurpriseEggListReqResult","voice file："+tempString);
                    egg.setVoiceFile(tempString);
                } else {
                    egg.setVoiceFile("");
                }
            }
//                return true;
//            }
        } catch (JSONException e) {
            e.printStackTrace();
            MLog.d("getSurpriseEggListReqResult","JSONException"+e.getMessage());
            return false;
        }
        return true;
    }

    /**
     * 将彩蛋做成时间更改成固定格式的表示时间
     * @param strDate 从json取得的做成时间
     */
    private static Date ChangeCreateDate(String strDate) {
        Date tempDate = null;
        String tempStrDate = strDate.replace("T", " ");
        DateFormat formatter = new SimpleDateFormat(sDataFormat, Locale.getDefault());
        try {
            tempDate = formatter.parse(tempStrDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return tempDate;
    }

    /**
     * 解析删除彩蛋item请求结果的json
     * @param jsonString 指定的json字符串
     * @return boolean true 删除成功 false 删除失败
     */
    public static String getSurpriseEggTokenResult(String jsonString) {
        String token = "";
        try {
            JSONObject jsonAllObject = new JSONObject(jsonString);
            int result = jsonAllObject.getInt(SurpriseEggFuncDef.JSON_ITEM_RESULT);
            if (SurpriseEggFuncDef.SURPRISE_EGG_LIST_REQ_SUCCESS != result) {
                String errMsg = jsonAllObject.getString(SurpriseEggFuncDef.JSON_ITEM_MESSAGE);
                if (null != errMsg) {
                    MLog.d("getSurpriseEggTokenResult", errMsg);
                }
            } else {
                token = jsonAllObject.getString(SurpriseEggFuncDef.JSON_TOKEN);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return token;
    }


}
