package com.dfl.smartscene.ccs.model;


import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.model.manager.SceneEditorInterface;

/**
 * <AUTHOR>
 * @date ：2022/7/12 18:19
 * @description ：
 */
public class SceneEditor implements SceneEditorInterface {
    private SceneBean mSceneBean;

    public SceneEditor(SceneBean sceneBean) {
        mSceneBean = sceneBean;
    }

    public SceneBean getSceneBean() {
        return mSceneBean;
    }

    @Override
    public void addSingerOperation(String deviceId, SettingOperation settingOperation) {

    }

    @Override
    public void deleteSingleOperation(String deviceId, SettingOperation settingOperation) {

    }

    @Override
    public void modifySingleOperation(String deviceId, SettingOperation settingOperation) {

    }

    @Override
    public void setDeviceOperationEnable(String deviceId, boolean enable) {

    }

    @Override
    public void setSceneEnable(boolean enable) {

    }
}
