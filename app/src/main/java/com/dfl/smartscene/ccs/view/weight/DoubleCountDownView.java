package com.dfl.smartscene.ccs.view.weight;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.TextView;
import com.dfl.dflcommonlibs.ui.MaskLayout;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.bean.SceneBean;

import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR>
 * @date ：2024/2/14 10:57
 * @description ：两个按钮带倒计时的控件
 */
@SuppressLint("ViewConstructor")
public class DoubleCountDownView extends FrameLayout {

    private static final String TAG = "LibDialog";
    private DialogUtilInterface mDialogUtilInterface;
    public int m_width = 0;
    private View view;
    public Disposable disposable;
    TextView titleView;
    private SceneBean mSceneBean;
    public DoubleCountDownView(DialogUtilInterface dialogUtilInterface, Context context) {
        super(context);
        this.mDialogUtilInterface = dialogUtilInterface;
        initView(context);
    }

    private void initView(Context context) {
        view = LayoutInflater.from(context).inflate(R.layout.dialog_double_title_content_layout, this);
        onViewCreated(view);
    }

    @SuppressLint("SetTextI18n")
    public void onViewCreated(View view) {
        Log.d("LibDialog", "onViewCreated: onViewCreated");
        TextView messageView = view.findViewById(R.id.text_view_default_double_dialog_message);
//        messageView.setText(this.mDialogUtilInterface.getDialogMessage());
        messageView.setText("场景条件已触发，是否执行");
        messageView.post(() -> {
            if (messageView.getLineCount() >= 2) {
                messageView.setGravity(3);
            }
        });
        View negativeMaskLayout;
        Button closeButton;
        Button negaButton;

        if (this.mDialogUtilInterface instanceof DoubleDialogListener) {
            if (((DoubleDialogListener)this.mDialogUtilInterface).getPositiveButtonId() != 0) {
                negativeMaskLayout = view.findViewById(((DoubleDialogListener)this.mDialogUtilInterface).getPositiveFrameId());
                if (negativeMaskLayout instanceof MaskLayout) {
                    ((MaskLayout)negativeMaskLayout).setNeedForeground(true);
                    ((MaskLayout)negativeMaskLayout).setForegroundDrawable(R.drawable.bg_button_mark);
                }
            }

            closeButton = view.findViewById(((DoubleDialogListener)this.mDialogUtilInterface).getPositiveButtonId());
            closeButton.setOnClickListener(((DoubleDialogListener)this.mDialogUtilInterface).getPositiveButtonListener(this));
//            closeButton.setText(((DoubleDialogListener)this.mDialogUtilInterface).getPositiveButtonName());
            closeButton.setText("立即执行");

            negaButton = view.findViewById(((DoubleDialogListener)this.mDialogUtilInterface).getNegativeButtonId());
            negaButton.setOnClickListener(((DoubleDialogListener)this.mDialogUtilInterface).getNegativeButtonListener(this));
//            negaButton.setText(((DoubleDialogListener)this.mDialogUtilInterface).getNegativeButtonName());
            negaButton.setText("本次不执行");
        }

        if (this.mDialogUtilInterface instanceof DefaultDoubleDialogListeners && ((DefaultDoubleDialogListeners)this.mDialogUtilInterface).showCloseButton()) {
            closeButton = view.findViewById(R.id.button_default_double_close);
            if (closeButton != null) {
                closeButton.setOnClickListener(((DefaultDoubleDialogListeners)this.mDialogUtilInterface).getCloseButtonListener(this));
                closeButton.setVisibility(View.VISIBLE);
            }
        }

        if (!TextUtils.isEmpty(this.mDialogUtilInterface.getDialogTitle(this))) {
            titleView = view.findViewById(R.id.text_view_default_double_dialog_title);
            if (titleView != null) {
                titleView.setText(this.mDialogUtilInterface.getDialogTitle(this));
            }
        }

        if (this.mDialogUtilInterface instanceof CountDownInterface) {
            titleView = view.findViewById(R.id.button_default_double_positive);
            String oldString = titleView.getText().toString();
            int count = ((CountDownInterface)this.mDialogUtilInterface).getCountDownNumber();
            TextView finalTextView = titleView;
            this.disposable = Observable.intervalRange(0L, (long)(count + 1), 0L, 1L, TimeUnit.SECONDS).observeOn(AndroidSchedulers.mainThread()).doOnComplete(() -> {
                ((CountDownInterface)this.mDialogUtilInterface).onCountDownFinish(this);
            }).subscribe((aLong) -> {
                if ((long)count - aLong > 0L) {
                    finalTextView.setText(oldString + "(" + ((long)count - aLong) + "s)");
                } else {
                    finalTextView.setText(oldString);
                }
            });
        }

        if (this.m_width != 0) {
            ViewGroup layout = view.findViewById(R.id.layout_all);
            ViewGroup.LayoutParams layoutParams = layout.getLayoutParams();
            layoutParams.width = this.m_width;
            layout.setLayoutParams(layoutParams);
        }
    }

    public void setSceneBean(SceneBean sceneBean){
        mSceneBean = sceneBean;
        titleView = view.findViewById(R.id.text_view_default_double_dialog_title);
        titleView.setText("即将执行 " + sceneBean.getSceneName());
    }


    public void clearTimerDisposable() {
        Log.d("LibDialog", "onDestroyView: onDestroyView");
        this.mDialogUtilInterface = null;
        if (this.disposable != null && !this.disposable.isDisposed()) {
            this.disposable.dispose();
            this.disposable = null;
        }
    }

    public SceneBean getSceneBean(){
        return mSceneBean;
    }

    public Disposable getDisposable() {
        return disposable;
    }

    public interface CountDownInterface {
        int getCountDownTextId();

        int getCountDownNumber();

        void onCountDownFinish(DoubleCountDownView var1);
    }

    public interface DialogUtilInterface {
        int getDialogLayout();

        int getMessageTextId();

//        String getDialogMessage();

        String getDialogTitle(DoubleCountDownView dialog);

        int getTitleId();
    }

    public interface DoubleDialogListener extends DialogUtilInterface {
        int getPositiveButtonId();
        int getNegativeButtonId();

        int getPositiveFrameId();

        String getPositiveButtonName();

        OnClickListener getPositiveButtonListener(DoubleCountDownView var1);
        OnClickListener getNegativeButtonListener(DoubleCountDownView libDialog);
    }

}
