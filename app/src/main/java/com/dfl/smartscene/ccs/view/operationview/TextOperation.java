package com.dfl.smartscene.ccs.view.operationview;

import android.graphics.Rect;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.ViewTreeObserver;
import android.widget.EditText;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseView;

import java.util.ArrayList;
import java.util.List;

/**
 * 自定义语音控件
 */
public class TextOperation implements OperationBaseView {

    private SingleOperation mSingleOperation;

    private EditText mEditTextInput;
    private View mEnsureView;
    private View rl_root;

    private float unEnableAlpha = 0.75f;

    @Override
    public List<String> extractArgs() {
        ArrayList<String> objects = new ArrayList<>(1);
        String s = mEditTextInput.getText().toString();
        if(!TextUtils.isEmpty(s)) {
            objects.add(s);
        }
        return objects;
    }

    @Override
    public String extractDesc() {
        String valueStr = mEditTextInput.getText().toString();
        String desc = mSingleOperation.getArg(ConstantModelValue.OUTPUT_DESC_TEXT);
        if (!TextUtils.isEmpty(desc)) {
            valueStr = desc.replaceFirst("&", valueStr);
        }
        return valueStr;
    }

    public TextOperation(SingleOperation singleOperation, ViewGroup parent) {
        mSingleOperation = singleOperation;

        String hintText = singleOperation.getArg(ConstantModelValue.VIEW_DESC_TEXT);

        LayoutInflater.from(parent.getContext()).inflate(R.layout.layout_custom_text, parent, true);

        ViewParent parent1 = parent.getParent();

        if (null != parent1) {
            mEnsureView = ((View) parent1).findViewById(R.id.button_operation_base_dialog_positive);
            if (mEnsureView != null) {
                mEnsureView.setEnabled(false);
                mEnsureView.setAlpha(unEnableAlpha);
            }
        }


        mEditTextInput = parent.findViewById(R.id.edittext_layout_custom_text_input);
        mEditTextInput.setHint(hintText);
        mEditTextInput.setFilters(new InputFilter[]{new InputFilter.LengthFilter(30)});
        mEditTextInput.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                if (mEnsureView != null) {
                    mEnsureView.setEnabled(!TextUtils.isEmpty(s));
                    mEnsureView.setAlpha(!TextUtils.isEmpty(s) ? 1f : unEnableAlpha);
                }
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                // 过滤非中文和数字的字符
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < s.length(); i++) {
                    char c = s.charAt(i);
                    if (Character.toString(c).matches("[\\u4E00-\\u9FA5]")) {
                        sb.append(c);
                    }
                }
                // 如果过滤后有变化，则更新EditText的内容
                if (!sb.toString().equals(s.toString())) {
                    mEditTextInput.setText(sb.toString());
                    mEditTextInput.setSelection(sb.length()); // 保持光标位置
                }
                if (mEnsureView != null) {
                    mEnsureView.setEnabled(!TextUtils.isEmpty(s));
                    mEnsureView.setAlpha(!TextUtils.isEmpty(s) ? 1f : unEnableAlpha);
                }
            }
        });
        String defaultStr = singleOperation.getArg(ConstantModelValue.VIEW_DEFAULT_POS);
        if(defaultStr != null && !defaultStr.isEmpty()){
            LogUtil.d("0221", "TextOperation: update "+defaultStr);
            mEditTextInput.setText(defaultStr);
            mEditTextInput.requestFocus();
        }
        rl_root = parent.findViewById(R.id.rl_root);
        rl_root.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                Rect rect = new Rect();
                // 获取root在窗体的可视区域
                parent.getWindowVisibleDisplayFrame(rect);
                // 当前视图最外层的高度减去现在所看到的视图的最底部的y坐标
                int rootInvisibleHeight = parent.getRootView().getHeight() - rect.bottom;
                ViewGroup.LayoutParams params = rl_root.getLayoutParams();
                if (rootInvisibleHeight > 45){
                    rl_root.scrollTo(0, 35);
                    params.height = 130;
                    mEditTextInput.setHint("输入30个以内中文字符");
                }else {
                    //输入法软键盘隐藏
                    rl_root.scrollTo(0,0);
                    params.height = 222;
                    mEditTextInput.setHint(hintText);
                }
                rl_root.setLayoutParams(params);
            }
        });

    }

    @Override
    public void onConfigurationChanged() {
        if(mEditTextInput != null){
            mEditTextInput.setTextColor(mEditTextInput.getContext().getResources().getColor(R.color.color_text_first_label));
            mEditTextInput.setHintTextColor(mEditTextInput.getContext().getResources().getColor(R.color.color_text_tertiary_label));
        }
    }

}
