package com.dfl.smartscene.ccs.db.entity.scene;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;

@Entity(tableName = "scene_category_list")
public class SceneCategoryListEntity {

    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "pId", typeAffinity = ColumnInfo.INTEGER)
    public int pId;

    @ColumnInfo(name = "mSceneCategoryId", typeAffinity = ColumnInfo.TEXT)
    public String mSceneCategoryId;

    @ColumnInfo(name = "mCategoryName", typeAffinity = ColumnInfo.TEXT)
    public String mCategoryName;


    @Ignore
    public SceneCategoryListEntity() {

    }

    public SceneCategoryListEntity(int pId, String mSceneCategoryId, String mCategoryName) {
        this.pId = pId;
        this.mSceneCategoryId = mSceneCategoryId;
        this.mCategoryName = mCategoryName;
    }

    @Override
    public String toString() {
        return "SceneCategoryListEntity{" +
                "pId=" + pId +
                ", mSceneCategoryId='" + mSceneCategoryId + '\'' +
                ", mCategoryName='" + mCategoryName + '\'' +
                '}';
    }
}
