package com.dfl.smartscene.ccs.view.dialogfragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.bean.SingleOperation;


/**
 * <AUTHOR>
 * @date ：2022/12/31 8:39
 * @description ：和普通操作ui不同
 */
public class OperationSmallSizeDialog extends OperationBaseDialogFragment{
    public OperationSmallSizeDialog(int operationType, SingleOperation singleOperation) {
        super(operationType, singleOperation);
    }

    public OperationSmallSizeDialog(int operationType, int pos, SettingOperation settingOperation, SingleOperation singleOperation) {
        super(operationType, pos, settingOperation, singleOperation);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
       return inflater.inflate(R.layout.dialog_fragment_operation_base_small,container,false);
    }

}
