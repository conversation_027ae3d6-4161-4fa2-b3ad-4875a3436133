package com.dfl.smartscene.ccs.model.manager;

/**
 * <AUTHOR>
 * @date ：2022/12/31 9:13
 * @description ：从xml文件中解析子模块实现类名，并保存至map中待用
 */
public class LibraryModelParser {

    /**
     * 解析模型名称
     * 现在这个方法不需要做任何事情，因为模型已经在ModelRegistry中静态注册了
     * 保留这个方法是为了保持与原有代码的兼容性
     */
    public void parserModelName() {
        // 不需要解析XML，模型已经在ModelRegistry中静态注册
        // 这个方法保留是为了兼容性，实际上什么都不做
        System.out.println("使用静态注册方式，无需解析XML配置文件");
    }

    /**
     * 根据通用模型名称获取具体实现类的全类名
     * 现在委托给ModelRegistry处理
     *
     * @param normalClassName 通用模型名称
     * @return 具体实现类的全类名
     */
    public String getModelName(String normalClassName) {
        return ModelRegistry.getModelName(normalClassName);
    }

    /**
     * 检查是否包含指定的模型名称
     * 现在委托给ModelRegistry处理
     *
     * @param normalClassName 通用模型名称
     * @return 如果包含返回true，否则返回false
     */
    public boolean containModelName(String normalClassName) {
        return ModelRegistry.containModelName(normalClassName);
    }

    /**
     * 获取所有已注册的模型信息（新增方法）
     *
     * @return 包含所有模型信息的字符串
     */
    public String getAllModelsInfo() {
        StringBuilder info = new StringBuilder();
        info.append("已注册的模型数量: ").append(ModelRegistry.getModelCount()).append("\n");

        String[] modelNames = ModelRegistry.getAllModelNames();
        for (String modelName : modelNames) {
            String className = ModelRegistry.getModelName(modelName);
            info.append("模型名称: ").append(modelName)
                    .append(" -> 实现类: ").append(className).append("\n");
        }

        return info.toString();
    }
}