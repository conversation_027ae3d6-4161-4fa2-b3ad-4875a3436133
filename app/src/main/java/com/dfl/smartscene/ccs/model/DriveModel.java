package com.dfl.smartscene.ccs.model;

import android.os.RemoteException;

import com.dfl.api.base.NullServiceException;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.BuildConfig;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.constant.DebugValue;
import com.dfl.smartscene.ccs.model.manager.CustomApiManager;
import com.dfl.smartscene.ccs.util.UniCodeUtil;
import com.tencent.mmkv.MMKV;

/**
 * 驾驶相关类的model层
 *
 * <AUTHOR>
 * @date ：2022/9/30 9:11
 * @description ：
 */
public class DriveModel implements StateBaseModel {

    private static final String TAG = "DriveModel";
    private static volatile DriveModel sInstance;

    private DriveModel() {
    }

    public static DriveModel getInstance() {
        if (null == sInstance) {
            synchronized (DriveModel.class) {
                if (null == sInstance) {
                    sInstance = new DriveModel();
                }
            }
        }
        return sInstance;
    }

    private static final String KEY_MMKV_DAID = "KEY_MMKV_DAID";
    private String mDaid;

    public String getDaid() {

        if (DebugValue.Debug_daid && BuildConfig.DEBUG) {
            LogUtil.d(TAG, "use default DA ID");
            return "NH42TA2201010016";
        }

        if (mDaid != null && mDaid.matches("^[0-9a-zA-Z]+$")) {
            LogUtil.d(TAG, "current DA ID = "+mDaid);
            return mDaid;
        }

        MMKV mmkv = MMKV.defaultMMKV();
        assert mmkv != null;
        mDaid = mmkv.decodeString(KEY_MMKV_DAID);

        if (mDaid != null && mDaid.matches("^[0-9a-zA-Z]+$")) {
            LogUtil.d(TAG, "getLocalDaid" + UniCodeUtil.mixStrToString(mDaid));

            return mDaid;
        }
        if (CustomApiManager.getInstance().getIAppCommon() == null) {
            LogUtil.d(TAG, "getIAppCommon() == null");
            return null;
        }
        try {
            String daid = CustomApiManager.getInstance().getIAppCommon().getDAId();
            if (daid != null && daid.matches("^[0-9a-zA-Z]+$")) {
                mmkv.encode(KEY_MMKV_DAID, daid);
                mDaid = daid;
                LogUtil.d(TAG, "getDaid" + mDaid);
                return mDaid;
            }
        } catch (NullServiceException | RemoteException e) {
            LogUtil.d(TAG, "NullServiceException() == " + e);
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 监听车辆状态
     */
    public void listenCarStatus() {
    }

    @Override
    public boolean checkStateOperationMeet(SettingOperation settingOperation) {
        try {
            //检查状态条件是否满足
            switch (settingOperation.getOperationId()) {
                //燃油续航
                case ConstantModelValue.OID_STA_DRIVE_RANGE_FUEL:
                    int fuelDrivingRange = CustomApiManager.getInstance().getIAppCommon().getFuelDrivingRange();
                    return simpleRangeStateCheck(settingOperation.getListArgs(), String.valueOf(fuelDrivingRange));
                //电量续航
                case ConstantModelValue.OID_STA_DRIVE_RANGE_POWER:
                    int powerDrivingRange = CustomApiManager.getInstance().getIAppCommon().getPowerDrivingRange();
                    return simpleRangeStateCheck(settingOperation.getListArgs(), String.valueOf(powerDrivingRange));
                //电量百分比
                case ConstantModelValue.OID_STA_DRIVE_ELECTRICITY:
                    int percentRemainPower = CustomApiManager.getInstance().getIAppCommon().getPercentRemainPower();
                    return simpleRangeStateCheck(settingOperation.getListArgs(), String.valueOf(percentRemainPower));
                //车辆挡位
                case ConstantModelValue.OPERATION_ID_STATE_DRIVE_GEAR:
                    return simpleStateCheck(settingOperation.getListArgs(), getCarGear());
                //车速
                case ConstantModelValue.OID_STA_DRIVE_SPEED:
                    float carSpeed = CustomApiManager.getInstance().getIAppCommon().getMeterSpeed();
                    return simpleRangeStateCheck(settingOperation.getListArgs(), String.valueOf(carSpeed));
                default:
            }

        } catch (NullServiceException | RemoteException e) {
            e.printStackTrace();
            return false;
        }
        return false;
    }

    public String getCarGear() {
        if(DebugValue.DEBUG_P_LIMIT){
            return "0";
        }
        try {
            int carGear = CustomApiManager.getInstance().getIAppCommon().getCarGear();
            return String.valueOf(carGear);
        } catch (Exception e) {
            e.printStackTrace();
            return "-99";
        }
    }

    public String getGearPValue() {
        return "0";
    }

    public boolean isPGear() {
        String carGear = getCarGear();
        return getGearPValue().equals(carGear);
    }
}
