package com.dfl.smartscene.ccs.constant;

/*
 * 舒压模式功能定义
 * <AUTHOR>
 * @date 2022/4/15
 */
public class RelieveStressFuncDef {
    public static final int Relieve_Stress_none = -1;
    public static final int Relieve_Stress_MaxVolume = 40;
    public static final int Relieve_Stress_Default_Volume = 20;
    //timer id
    public static final int Relieve_Stress_VolumeSeekSlip = 1;

    // 舒压模式播放状态
    public static final int Relieve_Stress_Status_Stop = 0;
    public static final int Relieve_Stress_Status_Play = 1;
    public static final int Relieve_Stress_Status_Pause = 2;
    public static final int Relieve_Stress_Status_Phone_Stop = 3;

    public static final int Relieve_Stress_Phone_Interrupt_None = -1;
    public static final int Relieve_Stress_Phone_Interrupt_True = 1;
    public static final int Relieve_Stress_Phone_Interrupt_False = 0;

    // online music info
    public static final String MEDIA_SOURCE_MUSIC_TAG             = "OnlineMusic";
    public static final String MEDIA_SOURCE_MUSIC_PACKAGE_NAME    = "com.dfl.onlinemusic";
    public static final int    MEDIA_SOURCE_MUSIC_INDEX           = 0;
    public static final String MEDIA_SOURCE_MUSIC_ACTIVITY        = "com.dfl.onlinemusic.MainActivity";
    public static final String MEDIA_SOURCE_MUSIC_EXPAND_ACTIVITY = "com.dfl.onlinemusic.ExpandActivity";
    public static final String MEDIA_SOURCE_MUSIC_SERVICE         = "com.dfl.onlinemusic.service.MusicService";

    public static final String MEDIASESSIONEXTRA_MEDIA_ID  = "MediaSessionExtraMediaID";            //当前Media ID
    public static final String MEDIASESSIONEXTRA_PARENT_ID = "MediaSessionExtraParentMediaID";      //父Media ID
    public static final String MEDIASESSIONEXTRA_INDEX     = "MediaSessionExtraIndex";

    public static final String MEDIASESSIONEXTRA_SONG_LIST  = "MediaSessionExtraSong_List";     // 单曲检索列表MediaID
    public static final String MEDIASESSIONEXTRA_IN_ADDITIONAL_PLAY_ACTION = "MediaSessionExtraInAdditionalPlayAction"; //进入不影响原播放状态的模式
    public static final String MEDIASESSIONEXTRA_OUT_ADDITIONAL_PLAY_ACTION = "MediaSessionExtraOutAdditionalPlayAction"; //退出不影响原播放状态的模式
    public static final String MEDIASESSIONEXTRA_EXTRA_SCENE_LIST_MEDIA_ID = "MediaSessionExtraSceneListMediaId";

    public static final String MEDIA_SOURCE_BTPHONE_PACKAGE_NAME    = "com.dfl.btphone";

    public static final String SCENE_ID = "17"; // 舒压模式曲库ID

    // 舒压模式画面迁移方式定义，用于区分全屏半屏的不同处理
    public static final int Relieve_Stress_Transfer_Origin_None = -1;
    public static final int Relieve_Stress_Transfer_Origin_Start = 0;
    public static final int Relieve_Stress_Transfer_Origin_Open = 1;
    public static final int Relieve_Stress_Transfer_Origin_Narrow = 2;
    public static final int Relieve_Stress_Transfer_Origin_Expand = 3;
}
