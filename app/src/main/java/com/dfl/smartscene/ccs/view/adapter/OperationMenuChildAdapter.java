package com.dfl.smartscene.ccs.view.adapter;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseAdapter;
import com.dfl.smartscene.ccs.base.BaseHolder;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.util.ImageUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/9/5 14:04
 * @description ：操作菜单窗口下操作开片的adapter
 */
public class OperationMenuChildAdapter extends BaseAdapter<SingleOperation> {
    private static final String TAG = "OperationMenuChildAdapt";
    public static final int VIEW_TITLE = 0;//标题
    public static final int VIEW_NORMAL = 1;//普通操作卡片
    public static final int VIEW_SALE = 2;//aiot介绍卡片
    private List<String> unableOperation = new ArrayList<>();

    public void setUnableOperation(List<String> unableOperation) {
        this.unableOperation = unableOperation;
    }

    @Override
    protected BaseHolder<SingleOperation> getViewHolder(View view, int viewType) {
        if(viewType == VIEW_TITLE){
            return new OperationMenuChildTitleHolder(view);
        }else if(viewType == VIEW_SALE){
            return new OperationAiotSaleHolder(view);
        }
        else {
            return new OperationMenuChildHolder(view);
        }
    }

    @Override
    public int getItemViewType(int position) {

        if(getItemData(position).getViewType().equals(ConstantModelValue.VIEW_TYPE_TITLE)){
            return VIEW_TITLE;
        }else if(getItemData(position).getViewType().equals(ConstantModelValue.VIEW_TYPE_AIOT_SALE_DEVICE)){
            return VIEW_SALE;
        }else {
            return VIEW_NORMAL;
        }
    }

    @Override
    protected int getLayoutId(int viewType) {
        if(viewType == VIEW_TITLE){
            return R.layout.item_operation_menu_child_title;
        }else if(viewType == VIEW_SALE){
            return R.layout.item_operation_menu_aiot_sale;
        }
        else {
            return R.layout.item_operation_menu_child;

        }
    }

    class OperationMenuChildHolder extends BaseHolder<SingleOperation> {
        private TextView mTextViewTitle;
        private ImageView mImageView;
        public OperationMenuChildHolder(View itemView) {
            super(itemView);
            mTextViewTitle = itemView.findViewById(R.id.textview_item_operation_menu_child);
            mImageView = itemView.findViewById(R.id.imageview_item_operation_menu_child);
        }

        @Override
        public void setupData(SingleOperation singleOperation, int position) {
            if(unableOperation.contains(singleOperation.getOperationId())){
                itemView.setTag("unclick");
                itemView.setClickable(false);
                itemView.setAlpha(0.5f);
            }else if(singleOperation.getShowMode() == ConstantModelValue.CAR_OPERATION_SHOW_TYPE_AIOT_OFFLINE){
                itemView.setAlpha(0.5f);
            }else {
                itemView.setTag(null);
                itemView.setClickable(true);
                itemView.setAlpha(1.0f);
            }
            mTextViewTitle.setText(singleOperation.getOperationName());
            ImageUtil.loadIcon(singleOperation.getOperationIcon(),mImageView);
        }
    }

    class OperationMenuChildTitleHolder extends BaseHolder<SingleOperation> {
        private TextView mTextViewTitle;
        public OperationMenuChildTitleHolder(View itemView) {
            super(itemView);
            mTextViewTitle = itemView.findViewById(R.id.textview_item_operation_menu_child_title);
        }

        @Override
        public void setupData(SingleOperation singleOperation, int position) {
            mTextViewTitle.setText(singleOperation.getOperationName());
        }
    }

    class OperationAiotSaleHolder extends BaseHolder<SingleOperation> {
        TextView mTextViewName;
        ImageView mImageViewPic;
        public OperationAiotSaleHolder(View itemView) {
            super(itemView);
            mTextViewName = itemView.findViewById(R.id.textview_item_aiot_sale_name);
            mImageViewPic = itemView.findViewById(R.id.imageview_item_operation_aiot_sale_pic);
        }

        @Override
        public void setupData(SingleOperation singleOperation, int position) {
            mTextViewName.setText(singleOperation.getOperationName());
            ImageUtil.loadImageUrl(mImageViewPic,singleOperation.getOperationIcon());
        }
    }

}
