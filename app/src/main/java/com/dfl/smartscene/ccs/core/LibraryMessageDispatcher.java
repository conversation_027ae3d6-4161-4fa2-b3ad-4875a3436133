package com.dfl.smartscene.ccs.core;

import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.model.SceneDataModifyListener;
/**
 * <AUTHOR>
 * @date ：2023/3/3 10:27
 * @description ：由主模块下发到子模块的信息
 */
public class LibraryMessageDispatcher implements SceneDataModifyListener  , ConditionBaseModel{
    @Override
    public void onSceneAdd(SceneBean sceneBean) {

    }

    @Override
    public void onSceneReplace(SceneBean newScene) {

    }

    @Override
    public void onSceneDelete(SceneBean sceneBean) {

    }

    public void initModelListerner(String apiName){

    }

    public boolean excuteOperation(SettingOperation settingOperation){
        return false;
    }

    public int resetSingleConfig(SingleOperation singleOperation){
        return ConstantModelValue.CONFIG_RESET_RESULT_NORELATIVE;
    }

    @Override
    public void registerSceneCondition(SceneBean sceneBean, SettingOperation condition) {

    }

    @Override
    public void unRegisterSceneCondition(SceneBean sceneBean, SettingOperation condition) {

    }
}
