package com.dfl.smartscene.ccs.viewmodel.base;

import android.annotation.SuppressLint;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.dflcommonlibs.rxbus.RxBus;
import com.dfl.dflcommonlibs.rxbus.Subscribe;
import com.dfl.smartscene.ccs.busevent.LoginStatusEvent;
import com.dfl.smartscene.ccs.busevent.NetWorkEvent;
import com.dfl.smartscene.ccs.busevent.PermissionEvent;
import com.dfl.smartscene.ccs.busevent.VrStatusEvent;

/**
 * 存储各页面都需要使用数据的ViewModel，包括网络状态，播放信息，登录信息，Vip状态，语音唤醒状态。绑定时采用requireActivity进行绑定。
 * @author: huangzezheng
 * @date: 2021/10/18
 */
public class MainViewModel extends ViewModel {
    private static final String TAG = "MainViewModel";
    private MutableLiveData<Boolean> netWorkStatus = new MutableLiveData<>();
    private MutableLiveData<Boolean> loginStatus = new MutableLiveData<>();
    private MutableLiveData<Boolean> vrWakeUpStatus = new MutableLiveData<>();
    private MutableLiveData<Boolean> permissionStatus = new MutableLiveData<>();

    public MutableLiveData<Boolean> getNetWorkStatus() {
        return netWorkStatus;
    }

    public MutableLiveData<Boolean> getLoginStatus() {
        return loginStatus;
    }

    public MutableLiveData<Boolean> getVrWakeUpStatus() {
        return vrWakeUpStatus;
    }

    @SuppressLint("CheckResult")
    public MainViewModel(){
        RxBus.getDefault().register(this);
    }

    @Override
    protected void onCleared() {
        RxBus.getDefault().unRegister(this);
        super.onCleared();
    }

    @Subscribe
    public void loginStatusListener(LoginStatusEvent loginStatusEvent) {
    }

    @Subscribe
    public void vrStatusListener(VrStatusEvent vrStatusEvent) {
        vrWakeUpStatus.setValue(vrStatusEvent.isVrStatus());
    }

    @Subscribe
    public void networkListener(NetWorkEvent netWorkEvent) {
        netWorkStatus.setValue(netWorkEvent.isNetStatus());
    }

    @Subscribe
    public void permissionListener(PermissionEvent permissionEvent){
        permissionStatus.setValue(permissionEvent.isStatus());
        LogUtil.d(TAG, "permission status = "+permissionEvent.isStatus());
    }



}
