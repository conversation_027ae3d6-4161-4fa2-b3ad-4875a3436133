package com.dfl.smartscene.ccs.bean.aiot;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/8/3 17:03
 * @description ：aiot操作的原生信息
 */
public class AiotOperationBean {

    /**
     * 方法id
     */
    private int aiid;

    /**
     * 服务id
     */
    private int siid;
    /**
     * 操作名
     */
    private String opName;
    /**
     * 0：枚举
     * 1：范围调节
     */
    private int viewType;

    /**
     * 操作类型 1：属性  2：方法
     */
    private String cmd;
    /**
     * 参数列表
     */
    private List<AiotPropBean> propList;

    public int getAiid() {
        return aiid;
    }

    public void setAiid(int aiid) {
        this.aiid = aiid;
    }

    public int getSiid() {
        return siid;
    }

    public void setSiid(int siid) {
        this.siid = siid;
    }

    public String getOpName() {
        return opName;
    }

    public void setOpName(String opName) {
        this.opName = opName;
    }

    public int getViewType() {
        return viewType;
    }

    public void setViewType(int viewType) {
        this.viewType = viewType;
    }

    public String getCmd() {
        return cmd;
    }

    public void setCmd(String cmd) {
        this.cmd = cmd;
    }

    public List<AiotPropBean> getPropList() {
        return propList;
    }

    public void setPropList(List<AiotPropBean> propList) {
        this.propList = propList;
    }
}
