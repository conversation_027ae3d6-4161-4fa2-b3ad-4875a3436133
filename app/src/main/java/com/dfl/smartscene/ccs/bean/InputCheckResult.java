package com.dfl.smartscene.ccs.bean;

/**
 * <AUTHOR>
 * @date ：2022/12/29 16:31
 * @description ：敏感词检测返回结果类
 */
public class InputCheckResult {
    public static final int RESULT_PASS = 0;
    public static final int RESULT_UNPASS = 1;
    public static final int RESULT_NONETWORK = 2;
    private String checkString = "";
    private int checkResult = 0;
    private String resultDesc = "";

    public InputCheckResult(String checkString, int checkResult) {
        this.checkString = checkString;
        this.checkResult = checkResult;
    }

    public void setResultDesc(String resultDesc) {
        this.resultDesc = resultDesc;
    }

    public String getCheckString() {
        return checkString;
    }

    public int getCheckResult() {
        return checkResult;
    }

    public String getResultDesc() {
        return resultDesc;
    }
}
