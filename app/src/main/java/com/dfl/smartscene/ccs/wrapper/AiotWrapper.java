package com.dfl.smartscene.ccs.wrapper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dfl.api.app.aiot.IAiotCommandCallback;
import com.dfl.api.app.aiot.IAiotDevicesCallback;
import com.dfl.api.app.aiot.ISaleProductCallback;
import com.dfl.api.base.NullServiceException;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.ccs.bean.aiot.AiotCategoryBean;
import com.dfl.smartscene.ccs.bean.aiot.AiotCategorySaleBean;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.model.manager.CustomApiManager;
import com.dfl.smartscene.ccs.util.HandlerUtil;


import java.util.ArrayList;
import java.util.List;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;

/**
 * <AUTHOR>
 * @date ：2022/9/21 14:43
 * @description ：已弃用，被CCUAiotModel取代
 */
public class AiotWrapper {
    private static final String TAG = "AiotWrapper";

    private static volatile AiotWrapper sInstance;

    public static AiotWrapper getInstance(){
        if (null == sInstance){
            synchronized (AiotWrapper.class){
                if (null == sInstance){
                    sInstance = new AiotWrapper();
                }
            }
        }
        return sInstance;
    }

    public Observable<List<AiotCategoryBean>> requestAiotDevices() {
        return Observable.create(new ObservableOnSubscribe<List<AiotCategoryBean>>() {
            @Override
            public void subscribe(ObservableEmitter<List<AiotCategoryBean>> emitter) throws Exception {
                if(CustomApiManager.getInstance().getIAiotScene() == null){
                    emitter.onError(new Exception());
                    return;
                }
                CustomApiManager.getInstance().getIAiotScene().getAiotDevices(new IAiotDevicesCallback() {
                    @Override
                    public void onDevices(String devices) {
                        LogUtil.d(TAG, "getAiotDevices : " + devices);

                        emitter.onNext(JSON.parseObject(devices, new TypeReference<List<AiotCategoryBean>>() {
                        }));
                    }

                    @Override
                    public void onCallbackCompleted() {

                    }
                });
            }
        });

    }
    private String onSaleProduct = "errorNothing";

    private Runnable mRunnable = null;



    public Observable<List<AiotCategorySaleBean>> requestAiotSaleDevices() {
        LogUtil.d(TAG, "requestAiotSaleDevices: ");
        return Observable.create(new ObservableOnSubscribe<List<AiotCategorySaleBean>>() {
            @Override
            public void subscribe(ObservableEmitter<List<AiotCategorySaleBean>> emitter) throws Exception {
                if(CustomApiManager.getInstance().getIAiotScene() == null){
                    LogUtil.d(TAG, "subscribe: null customapi");
                    emitter.onError(new Exception());
                    return;
                }
                onSaleProduct = "errorNothing";
                LogUtil.d(TAG, "getOnSaleProducts start ");
                CustomApiManager.getInstance().getIAiotScene().getOnSaleProducts(null, new ISaleProductCallback() {
                    @Override
                    public void onSale(String products) {
                        LogUtil.d(TAG, "getOnSaleProducts : " + products);
                        onSaleProduct = products;
                        emitter.onNext(JSON.parseObject(products, new TypeReference<List<AiotCategorySaleBean>>() {
                        }));

                    }

                    @Override
                    public void onCallbackCompleted() {
                        LogUtil.d(TAG, "onCallbackCompleted: "+onSaleProduct);
                    }
                });
                mRunnable = new Runnable() {
                    @Override
                    public void run() {
                        if(onSaleProduct.equals("errorNothing")){
                            List<AiotCategorySaleBean> list = new ArrayList<>();
                            emitter.onNext(list);
                        }
                    }
                };
                HandlerUtil.getActionHandler().removeCallbacks(mRunnable);
                HandlerUtil.getActionHandler().excuteDelay(mRunnable,3000);
            }
        });
    }

    public void sendUserCommand(String json) {
        String inputJson = json;
        inputJson = "{\"type\":0,\"data\":[" + inputJson + "]}";
        LogUtil.d(TAG, "sendUserCommand input : " + inputJson);
        if (CustomApiManager.getInstance().getIAiotScene() != null) {
            try {
                CustomApiManager.getInstance().getIAiotScene().sendCommand(inputJson, new IAiotCommandCallback() {
                    @Override
                    public void onResult(String result) {
                        LogUtil.d(TAG, "sendUserCommand" + result);
                    }

                    @Override
                    public void onCallbackCompleted() {

                    }
                });
            } catch (NullServiceException e) {
                e.printStackTrace();
            }
        }

    }

    public void registerAirQualityCallback() {
    }

    public void registerDeviceListener() {
//        try {
//            LogUtil.d(TAG,"registerDeviceListener");
//            CCUCustomApiManager.getInstance().getAiotService().registerDevicePropCallback(new IDevicePropCallback.Stub() {
//                @Override
//                public void onUpload(String data) throws RemoteException {
//                    LogUtil.d(TAG,"data : " + data);
//                    JSONObject aiotDeviceBackBean = JSON.parseObject(data);
//                    if("23".equals(aiotDeviceBackBean.getString("pid"))){
//                        List<AiotPropBean> aiotPropBeans = JSON.parseObject(aiotDeviceBackBean.getString("propList"),new TypeReference<List<AiotPropBean>>(){});
//                        if(aiotPropBeans != null && aiotPropBeans.size()!=0){
//                            if(aiotPropBeans.get(0).getPiid() == 2 && aiotPropBeans.get(0).getSiid() == 1){
//                                LogUtil.d(TAG,"air quality change : " + aiotPropBeans.get(0).getValue());
//                                carAirQuality = aiotPropBeans.get(0).getValue();
//                            }
//                        }
//                    }
//                }
//            });
//        } catch (RemoteException e) {
//            LogUtil.d(TAG,e.getMessage());
//            e.printStackTrace();
//        }
    }

    public void sendCloudCommand(String json) {
        String inputJson = json;
        inputJson = "{\"type\":1,\"data\":[" + inputJson + "]}";
        LogUtil.d(TAG, "sendCloudCommand input : " + inputJson);

        if (CustomApiManager.getInstance().getIAiotScene() != null) {
            try {
                CustomApiManager.getInstance().getIAiotScene().sendCommand(inputJson, new IAiotCommandCallback() {
                    @Override
                    public void onResult(String result) {
                        LogUtil.d(TAG, "sendPresetSceneCommand" + result);
                    }

                    @Override
                    public void onCallbackCompleted() {

                    }
                });
            } catch (NullServiceException e) {
                e.printStackTrace();
            }
        }
    }

    private String carAirQuality = ConstantModelValue.CAR_AIR_QUELITY_GOOD;


}
