package com.dfl.smartscene.ccs.util;


import com.dfl.smartscene.ccs.bean.SettingOperation;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2023/6/2 13:58
 * @description ：
 */
public class OpeUtil {

    public static boolean checkOpeArgLegal(SettingOperation settingOperation ){
        return checkOpeArgLegal(settingOperation,1);
    }


    public static boolean checkOpeArgLegal(SettingOperation settingOperation , int argNum){
        if(settingOperation == null){
            return false;
        }
        List<String> args = settingOperation.getListArgs();
        return ListUtil.checkIndex(args,argNum - 1);
    }
}
