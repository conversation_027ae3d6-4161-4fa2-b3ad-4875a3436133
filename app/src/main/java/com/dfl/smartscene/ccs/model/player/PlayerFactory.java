package com.dfl.smartscene.ccs.model.player;

import android.content.ComponentName;


import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.constant.ScenePatternFuncDef;
import com.dfl.smartscene.ccs.model.manager.CarConfigManager;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2023/5/30 15:38
 * @description ：
 */
public class PlayerFactory {
    private static volatile PlayerFactory sInstance;

    public static PlayerFactory getInstance() {
        if (null == sInstance) {
            synchronized (PlayerFactory.class) {
                if (null == sInstance) {
                    sInstance = new PlayerFactory();
                }
            }
        }
        return sInstance;
    }

    private BookPlayer mBookPlayer;
    private MusicPlayer mMusicPlayer;
    private RadioPlayer mRadioPlayer;
    private FullMusicPlayer mFullMusicPlayer;

    private ComponentName radioComponent;//电台的包名与服务名
    private ComponentName musicComponent = new ComponentName(ScenePatternFuncDef.MEDIA_SOURCE_ONLINE_PACKAGE_NAME,ScenePatternFuncDef.MEDIA_SOURCE_ONLINE_SERVICE);//音乐的包名与服务名
    private ComponentName bookComponent = new ComponentName(ScenePatternFuncDef.MEDIA_SOURCE_TINGSHU_PACKAGE_NAME,ScenePatternFuncDef.MEDIA_SOURCE_TINGSHU_SERVICE);//书籍的包名与服务名

    public Player creatCommonPlayer(ComponentName componentName){
        if(componentName == null){
            return null;
        }
        if(componentName.getPackageName() == null || "".equals(componentName.getPackageName())){
            return null;
        }
        if(radioComponent != null && radioComponent.getPackageName().equals(componentName.getPackageName())){
            if(mRadioPlayer == null){
                mRadioPlayer = new RadioPlayer(radioComponent);
            }
            return mRadioPlayer;
        }else if(bookComponent != null && bookComponent.getPackageName().equals(componentName.getPackageName())){
            if(mBookPlayer == null){
                mBookPlayer = new BookPlayer(bookComponent);
            }
            return mBookPlayer;

        }else if(musicComponent != null && musicComponent.getPackageName().equals(componentName.getPackageName())){
            if(mMusicPlayer != null){
                return mMusicPlayer;
            }else if(mFullMusicPlayer != null){
                mMusicPlayer = new MusicPlayer(mFullMusicPlayer);
            }else {
                mMusicPlayer = new MusicPlayer(musicComponent);
            }
            return mMusicPlayer;
        }else {
            return new CommonPlayer(componentName);
        }
    }

    public Player creatFullMusicPlayer(){
        if(mFullMusicPlayer != null){
            return mFullMusicPlayer;
        }
        if(mMusicPlayer != null){
            mFullMusicPlayer = new FullMusicPlayer(mMusicPlayer);
        }else {
            mFullMusicPlayer = new FullMusicPlayer(musicComponent);
        }
        return mFullMusicPlayer;
    }

    public void init(){
        SingleOperation radioOperation = CarConfigManager.getInstance().findSingleOperationById(ConstantModelValue.OID_ACT_APP_MEDIA_RADIO);
        if (radioOperation != null) {
            List<String> radioNames = radioOperation.getListArg(ConstantModelValue.API_NAME_LIST);
            radioComponent = new ComponentName(radioNames.get(0), radioNames.get(1));
        }


        SingleOperation bookOperation = CarConfigManager.getInstance().findSingleOperationById(ConstantModelValue.OID_ACT_APP_MEDIA_BOOK);
        if (bookOperation != null) {
            List<String> bookNames = bookOperation.getListArg(ConstantModelValue.API_NAME_LIST);
            bookComponent = new ComponentName(bookNames.get(0), bookNames.get(1));
        }


        SingleOperation musicOperation = CarConfigManager.getInstance().findSingleOperationById(ConstantModelValue.OID_ACT_APP_MEDIA_MUSIC);
        if (musicOperation != null) {
            List<String> musicNames = musicOperation.getListArg(ConstantModelValue.API_NAME_LIST);
            musicComponent = new ComponentName(musicNames.get(0), musicNames.get(1));
        }


    }

    public ComponentName getRadioComponent() {
        return radioComponent;
    }

    public ComponentName getMusicComponent() {
        return musicComponent;
    }

    public ComponentName getBookComponent() {
        return bookComponent;
    }
}
