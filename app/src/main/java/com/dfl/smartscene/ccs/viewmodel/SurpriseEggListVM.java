package com.dfl.smartscene.ccs.viewmodel;

import android.app.Application;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.constant.ScenePatternFuncDef;
import com.dfl.smartscene.ccs.constant.SurpriseEggFuncDef;
import com.dfl.smartscene.ccs.model.ScenePatternInterrupt;
import com.dfl.smartscene.ccs.model.SurpriseEgg;
import com.dfl.smartscene.ccs.model.SurpriseEggList;
import com.dfl.smartscene.ccs.model.SurpriseEggListModel;
import com.dfl.smartscene.ccs.model.SurpriseEggPlayModel;
import com.iauto.uibase.lifecycle.MViewModelBase;
import com.iauto.uibase.utils.MLog;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.Locale;

/*
 * SurpriseEggListVM class
 * 彩蛋list view model
 * <AUTHOR>
 * @date 2022/5/26
 */
public class SurpriseEggListVM extends MViewModelBase {

    private static final String TAG = "SurpriseEggListVM";
    private static final String sDataFormat = "yyyy年MM月dd日";
    protected SurpriseEggListChangeObserver mSurpriseEggListChangeObserver = new SurpriseEggListChangeObserver(); // 彩蛋list变更观察者
    private  final SurpriseEggListModel mSurpriseEggListModel;                                                   // 菜单list model
    private MutableLiveData<SurpriseEggList>  mSurpriseEggList = new MutableLiveData<>();
    private final MutableLiveData<Integer> mListSize = new MutableLiveData<>();                 // 彩蛋list size
    private final MutableLiveData<Integer> mDelListSize      = new MutableLiveData<>();         // 要删除的彩蛋list size
    private final MutableLiveData<Boolean> mIsDelListItemSel = new MutableLiveData<>();         // 删除按钮的状态
    private final MutableLiveData<Boolean> mSurpriseEggListChanged = new MutableLiveData<>();   // 彩蛋list变更状态
    private final MutableLiveData<Boolean> mIsListEdit           = new MutableLiveData<>();     // 彩蛋list编辑状态
    private final MutableLiveData<Boolean> mIsDelListItemAllSel = new MutableLiveData<>();      // 是否全选
    private final MutableLiveData<Boolean> mIsEditButtonEnable   = new MutableLiveData<>();     // 彩蛋list编辑按钮可用不可用状态
    private final MutableLiveData<Integer> mListEditVisibility = new MutableLiveData<>();       // 彩蛋list编辑按钮表示状态
    private final MutableLiveData<Integer> mListControlVisibility = new MutableLiveData<>();    // list控件表示状态
    private final MutableLiveData<Integer> mListViewVisibility = new MutableLiveData<>();       // list view表示状态
    private final MutableLiveData<Integer> mNetDisconnectVisibility = new MutableLiveData<>();  // 网络断开相关view表示状态
    private final MutableLiveData<Integer> mNoFilesVisibility = new MutableLiveData<>();        // 没有彩蛋的场合view的表示状态
    private final MutableLiveData<Integer> mLoadingVisibility = new MutableLiveData<>();        // 没有彩蛋的场合view的表示状态

    private SurpriseEggListItemVM mSurpriseEggListItemViewModel = null;
    private ArrayList<SurpriseEggItemInfo> mList = new ArrayList<SurpriseEggItemInfo>();

    /**
     * 彩蛋list变更观察者，变更后更新彩蛋表示list
     */
    public class SurpriseEggListChangeObserver implements Observer<Boolean> {
        @Override
        public void onChanged(Boolean changed) {
            MLog.d(TAG,"SurpriseEggListChange ");
            if(SurpriseEggFuncDef.SURPRISE_EGG_DSP_STATUS_COMMON == mSurpriseEggListModel.getSurpriseEggListDspStatus().getValue()) {
                setSurpriseEggList();
            }
        }
    }

    /**
     * SeatSettingVM object constructor
     * @param application object
     */
    public SurpriseEggListVM(Application application) {
        super(application);
        mSurpriseEggListModel = SurpriseEggListModel.getInstance();
        initLiveData();
    }

    /**
     * init seat live data
     */
    public void initLiveData() {
        MLog.d(TAG,"initLiveData ");
        mSurpriseEggListChanged.setValue(false);
        mListSize.setValue(0);
        mDelListSize.setValue(0);
        mSurpriseEggList.setValue(null);
        mIsDelListItemSel.setValue(false);
        mIsDelListItemAllSel.setValue(false);
        mListEditVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
        mListControlVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_Visible);
        mListViewVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
        mIsListEdit.setValue(false);
        initDspVisibility();
        addObserver();
    }

    public MutableLiveData<Boolean> getIsListEdit() {
        return mIsListEdit;
    }
    public MutableLiveData<Boolean> getIsDelListItemAllSel() {
        return mIsDelListItemAllSel;
    }
    public MutableLiveData<Boolean> getIsEditButtonEnable() {
        return mIsEditButtonEnable;
    }
    public MutableLiveData<Integer> getListEditVisibility() {
        return mListEditVisibility;
    }
    public MutableLiveData<Integer> getListControlVisibility() {
        return mListControlVisibility;
    }
    public MutableLiveData<Boolean> getIsDelListItemSel() {
        return mIsDelListItemSel;
    }

    public void setIsEditButtonEnable(boolean isEnable) {
        mIsEditButtonEnable.setValue(isEnable);
    }

    public void setIsListEdit(Boolean isEdit) {
        mIsListEdit.setValue(isEdit);
        if (isEdit) {
            getListEditVisibility().setValue(ScenePatternFuncDef.Scene_Pattern_Visible);
            getListControlVisibility().setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            setItemEditStatus(true);
        } else {
            getListEditVisibility().setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            getListControlVisibility().setValue(ScenePatternFuncDef.Scene_Pattern_Visible);
            setItemEditStatus(false);
        }
    }

    public MutableLiveData<Boolean> getSurpriseEggListChanged() {
        return mSurpriseEggListChanged;
    }

    public MutableLiveData<Integer> getSurpriseEggListDspStatus() {
        return mSurpriseEggListModel.getSurpriseEggListDspStatus();
    }

    public MutableLiveData<Integer> getListViewVisibility() {
        return mListViewVisibility;
    }

    public MutableLiveData<Integer> getNetDisconnectVisibility() {
        return mNetDisconnectVisibility;
    }

    public MutableLiveData<Integer> getNoFilesVisibility() {
        return mNoFilesVisibility;
    }

    public MutableLiveData<Integer> getLoadingVisibility() {
        return mLoadingVisibility;
    }

    public MutableLiveData<Integer> getSurpriseEggListDelStatus() {
        return mSurpriseEggListModel.getSurpriseEggListDelStatus();
    }

    public MutableLiveData<Integer> getSurpriseEggListDelCount() {
        return mSurpriseEggListModel.getSurpriseEggListDelCount();
    }

    public void setSurpriseEggListDspStatus(int status) {
        mSurpriseEggListModel.setSurpriseEggListDspStatus(status);
    }
    /**
     * 追加观察者
     */
    public void addObserver() {
        MLog.d(TAG,"RelieveStressPatternVM:addObserver ");
        mSurpriseEggListModel.getSurpriseEggListChanged().observeForever(mSurpriseEggListChangeObserver);
    }

    /**
     * 移除观察者
     */
    public void removeObserver() {
        MLog.d(TAG,"RelieveStressPatternVM:removeObserver ");
        mSurpriseEggListModel.getSurpriseEggListChanged().removeObserver(mSurpriseEggListChangeObserver);
    }

    /**
     * 移除list中的title
     */
    public void removeTitleFromList(SurpriseEggItemInfo title) {

        MLog.d(TAG,"removeTitleFromList ");

        boolean isToday = false;
        if (title.mTime.equals(getApplication().getResources().getString(R.string.string_surprise_egg_list_title_today))) {
            isToday = true;
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(System.currentTimeMillis());
            DateFormat formatter = new SimpleDateFormat(sDataFormat, Locale.getDefault());
            title.mTime = formatter.format(calendar.getTime());
        }

        boolean hasData = false;
        for (int i = 0; i < mList.size(); i++) {
            if(SurpriseEggFuncDef.ITEM_TYPE_DATA == mList.get(i).itemType){
                if (title.mTime.equals(mList.get(i).mTime)) {
                    hasData = true;
                    break;
                }
            }
        }

        if (isToday) {
            title.mTime = getApplication().getResources().getString(R.string.string_surprise_egg_list_title_today);
        }

        if (!hasData) {
            for (int i = 0; i < mList.size(); i++) {
                if(SurpriseEggFuncDef.ITEM_TYPE_TITLE == mList.get(i).itemType){
                    if (title.mTime.equals(mList.get(i).mTime)) {
                        mList.remove(i);
                        break;
                    }
                }
            }
        }
    }

    /**
     * 设定彩蛋list表示内容
     */
    private void setSurpriseEggList() {

        // 取得彩蛋数据
        SurpriseEggList list = mSurpriseEggListModel.getSurpriseEggList();
        // 没有彩蛋数据的场合，设定size为0,更新彩蛋list，退出编辑模式。
        if (0 == list.getSurpriseEggListSize()) {
            mListSize.setValue(0);
            mSurpriseEggList.setValue(null);
            changeSurpriseEggListChanged();
            mIsListEdit.setValue(false);
            return;
        }

        if (mIsListEdit.getValue()) {
            // 删除彩蛋数据
            Iterator<SurpriseEggItemInfo> iterator = mList.iterator();
            while(iterator.hasNext()){
                SurpriseEggItemInfo tempInfo = iterator.next();
                if (SurpriseEggFuncDef.ITEM_TYPE_DATA == tempInfo.itemType && tempInfo.mIsSel) {
                    if(null == list.getSurpriseEggFromEggID(tempInfo.mID)){
                        iterator.remove();
                    }
                }
            }
            // 删除title数据
            ArrayList<SurpriseEggItemInfo> titleList = new ArrayList<SurpriseEggItemInfo>();
            for (int i = 0; i < mList.size(); i++) {
                if(SurpriseEggFuncDef.ITEM_TYPE_TITLE == mList.get(i).itemType){
                    titleList.add(mList.get(i));
                }
            }

            for (int i = 0; i < titleList.size(); i++) {
                removeTitleFromList(titleList.get(i));
            }

        } else {
            // 清空list
            mList.clear();

            // 取得当前时间
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(System.currentTimeMillis());

            // 时间格式做成
            DateFormat formatter = new SimpleDateFormat(sDataFormat, Locale.getDefault());
            String makeDate = "";

            // 做成彩蛋表示数据
            for (int i = 0; i < list.getSurpriseEggListSize(); i++) {
                SurpriseEgg egg = list.getSurpriseEggFromIndex(i);
                Date tempDate = egg.getTime();

                // 彩蛋第一个数据做成（时间title）
                if (0 == i) {
                    makeDate = formatter.format(tempDate);
                    String tempMakeDate = makeDate;
                    if (makeDate.equals(formatter.format(calendar.getTime()))) {
                        tempMakeDate = getApplication().getResources().getString(R.string.string_surprise_egg_list_title_today);
                    }
                    // 追加title
                    mList.add(makeTitleItemInfo(tempMakeDate));
                    // 追加数据
                    mList.add(makeItemInfo(egg, makeDate));
                } else {
                    // 如果当前的彩蛋做成时间（tempDate）和上一个彩蛋做成时间（makeDate）不相等
                    if (makeDate.compareTo(formatter.format(tempDate)) != 0) {
                        // 将当前彩蛋的组成时间赋值给makeDate，做成新的title。
                        makeDate = formatter.format(tempDate);
                        mList.add(makeTitleItemInfo(makeDate));
                        mList.add(makeItemInfo(egg, makeDate));
                    } else {
                        // 继续在原来的title值嗯添加数据
                        mList.add(makeItemInfo(egg, makeDate));
                    }
                }
            }
        }
        mSurpriseEggList.setValue(list);
        // 设定表示长度
        mListSize.setValue(mList.size());

        // 设定表示变更
        changeSurpriseEggListChanged();
    }

    /**
     * 表示变更
     */
    public void changeSurpriseEggListChanged() {
        if (mSurpriseEggListChanged.getValue()) {
            mSurpriseEggListChanged.setValue(false);
        } else {
            mSurpriseEggListChanged.setValue(true);
        }
    }


    /**
     * 彩蛋list表示title数据做成
     */
    private SurpriseEggItemInfo makeTitleItemInfo(String makeDate) {
        SurpriseEggItemInfo titleItemInfo = new SurpriseEggItemInfo();
        titleItemInfo.itemType = SurpriseEggFuncDef.ITEM_TYPE_TITLE;
        titleItemInfo.mTime = makeDate;

        return titleItemInfo;
    }

    /**
     * 彩蛋list数据做成
     *
     * @param egg model中取得的彩蛋数据
     * @param makeDate 彩蛋做成时间
     *
     */
    private SurpriseEggItemInfo makeItemInfo(SurpriseEgg egg, String makeDate) {
        SurpriseEggItemInfo itemInfo = new SurpriseEggItemInfo();
        itemInfo.itemType = SurpriseEggFuncDef.ITEM_TYPE_DATA;
        itemInfo.mID = egg.getEggID();
        itemInfo.mTime = makeDate;
        itemInfo.mName = egg.getName();
        itemInfo.mURL = egg.getURL();
        itemInfo.TTSText = egg.getTTSText();
        itemInfo.mImageURL = egg.getImageURL();
        itemInfo.voiceFile = egg.getVoiceFile();
        itemInfo.voiceType = egg.getVoiceType();
        if (mIsListEdit.getValue()) {
            itemInfo.mIsEdit = true;
        }

        return itemInfo;
    }

    public MutableLiveData<Integer> getListSize() {
        if (mListSize.getValue() == null) {
            mListSize.setValue(0);
        }
        return mListSize;
    }

    public MutableLiveData<SurpriseEggList> getSurpriseEggList() {
        return mSurpriseEggList;
    }

    /**
     * 获取要删除的彩蛋list大小
     *
     * @return MutableLiveData<Integer> 要删除的彩蛋list大小
     *
     */
    public MutableLiveData<Integer> getDelListSize() {
        if (mIsListEdit.getValue()) {
            int size = 0;
            for (int i = 0; i < mList.size(); i++) {
                if (SurpriseEggFuncDef.ITEM_TYPE_DATA == mList.get(i).itemType && mList.get(i).mIsSel) {
                    size++;
                }
            }
            mDelListSize.setValue(size);
        } else {
            mDelListSize.setValue(0);
        }
        return mDelListSize;
    }

    /**
     * 取得彩蛋itemview
     *
     * @param position 彩蛋位置
     *
     */
    public SurpriseEggListItemVM getSurpriseEggListItemViewModel(int position) {
        if (mSurpriseEggListItemViewModel == null) {
            mSurpriseEggListItemViewModel = new SurpriseEggListItemVM(getApplication());
        }

        if (mList != null && mList.size() > 0 && position >= 0 && position < mList.size()) {
            mSurpriseEggListItemViewModel.setItemData(mList.get(position));
        } else {

        }
        return mSurpriseEggListItemViewModel;
    }

    /**
     * 设定H5播放信息
     *
     * @param position 彩蛋位置
     *
     */
    public void setSurpriseEggPlayData(int position) {
        MLog.d(TAG,"setSurpriseEggPlayData");
        String url = getSurpriseEggListItemViewModel(position).getURL();
        String TTSText = getSurpriseEggListItemViewModel(position).getTTSText();
        String voiceFile = getSurpriseEggListItemViewModel(position).getVoiceFile();
        int voiceType = getSurpriseEggListItemViewModel(position).getVoiceType();
        MLog.d(TAG,"url:"+url);
        MLog.d(TAG,"TTSText:"+TTSText);
        MLog.d(TAG,"voiceFile:"+voiceFile);
        MLog.d(TAG,"voiceType:"+voiceType);
//       String url = "https://h5zh.venucia.com/surpriseMode/home";
//       String TTSText = "祝你生日快乐";
        SurpriseEggPlayModel.getInstance().setSurpriseEggURL(url);
        SurpriseEggPlayModel.getInstance().setSurpriseEggTTSText(TTSText);
        SurpriseEggPlayModel.getInstance().setSurpriseEggVoiceFile(voiceFile);
        SurpriseEggPlayModel.getInstance().setSurpriseEggVoiceTyp(voiceType);
    }

    /**
     * 变更彩蛋编辑状态
     *
     * @param position 彩蛋位置
     *
     */
    public void changeListItemEdit(int position) {
        if (position >=0 && position < mList.size()) {
            if (SurpriseEggFuncDef.ITEM_TYPE_DATA == mList.get(position).itemType) {
                if (mList.get(position).mIsSel) {
                    mList.get(position).mIsSel = false;
                } else {
                    mList.get(position).mIsSel = true;
                }
            } else {
                return;
            }
        }
        int delCount = getDelListSize().getValue();
        if (delCount > 0){
            mIsDelListItemSel.setValue(true);
            if(mSurpriseEggListModel.getSurpriseEggList().getSurpriseEggListSize() == delCount) {
                mIsDelListItemAllSel.setValue(true);
            }
            else {
                mIsDelListItemAllSel.setValue(false);
            }
        } else {
            mIsDelListItemSel.setValue(false);
            mIsDelListItemAllSel.setValue(false);
        }
    }

    /**
     * 设定编辑模式下全list彩蛋选择状态
     *
     * @param isSel ture：编辑 false：非编辑
     *
     */
    public void setAllListItemEdit(Boolean isSel) {
        for (int i = 0; i < mList.size(); i++) {
            if (SurpriseEggFuncDef.ITEM_TYPE_DATA == mList.get(i).itemType ) {
                mList.get(i).mIsSel = isSel;
            }
        }
        if (isSel) {
            mIsDelListItemSel.setValue(true);
            mIsDelListItemAllSel.setValue(true);
        } else {
            mIsDelListItemSel.setValue(false);
            mIsDelListItemAllSel.setValue(false);
        }
    }

    /**
     * 设定全list彩蛋编辑状态
     *
     * @param isEdit ture：编辑 false：非编辑
     *
     */
    public void setItemEditStatus(Boolean isEdit) {
        for (int i = 0; i < mList.size(); i++) {
            if (SurpriseEggFuncDef.ITEM_TYPE_DATA == mList.get(i).itemType ) {
                mList.get(i).mIsEdit = isEdit;
            }
        }
    }

    /**
     * 删除指定的彩蛋list
     */
    public void deleteSelListItem() {
        if (mIsListEdit.getValue()) {
            ArrayList<Integer> listID = new ArrayList<Integer>();
            for (int i = 0; i < mList.size(); i++) {
                if (mList.get(i).mIsSel) {
                    listID.add(mList.get(i).mID);
                }
            }
            MLog.d(TAG, "delete sel list item");
            mSurpriseEggListModel.deleteSurpriseEggList(listID);
        }
    }

    public class SurpriseEggItemInfo {
        public int itemType = 0;
        public int mID = -1;
        public String mName = "";
        public String mTime = "";
        public String mURL = "";
        public Boolean mIsEdit = false;
        public Boolean mIsSel = false;
        public String TTSText = "";
        public String mImageURL = "";
        public String voiceFile = "";
        public int voiceType = 1;

    }

    /**
     * 初始化彩蛋list表示状态
     *
     */
    public void initDspVisibility() {
        int dspStatus = getSurpriseEggListDspStatus().getValue();
        mIsListEdit.setValue(false);
        mIsDelListItemSel.setValue(false);
        mIsDelListItemAllSel.setValue(false);
        if (dspStatus == SurpriseEggFuncDef.SURPRISE_EGG_DSP_STATUS_DISCONNECT) {
            mListViewVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            mNetDisconnectVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_Visible);
            mNoFilesVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            mLoadingVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            mListEditVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            mListControlVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_Visible);
            mIsEditButtonEnable.setValue(false);
        } else if (dspStatus == SurpriseEggFuncDef.SURPRISE_EGG_DSP_STATUS_NO_FILES) {
            mListViewVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            mNetDisconnectVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            mNoFilesVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_Visible);
            mLoadingVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            mListEditVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            mListControlVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_Visible);
            mIsEditButtonEnable.setValue(false);
        } else if (dspStatus == SurpriseEggFuncDef.SURPRISE_EGG_DSP_STATUS_LOADING) {
            mListViewVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            mNetDisconnectVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            mNoFilesVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            mLoadingVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_Visible);
            mListEditVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            mListControlVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_Visible);
            mIsEditButtonEnable.setValue(false);
        } else {
            mListViewVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_Visible);
            mNetDisconnectVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            mLoadingVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            mNoFilesVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            mListEditVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            mListControlVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_Visible);
            if (dspStatus == SurpriseEggFuncDef.SURPRISE_EGG_DSP_STATUS_REFRESH_LOADING) {
                mIsEditButtonEnable.setValue(false);
            } else {
                mIsEditButtonEnable.setValue(true);
            }
            setSurpriseEggList();
        }
    }

    public void setStopDel(boolean stopDel) {
        mSurpriseEggListModel.setStopDel(stopDel);
    }
    
    /**
     * 获取P挡信息
     */
    public MutableLiveData<Integer> getGearType() {
        return ScenePatternInterrupt.getInstance().getGearType();
    }

    /**
     * 请求彩蛋list
     * demo
     */
    public void requestSurpriseEggListData() {
        mSurpriseEggListModel.requestSurpriseEggListData();
    }

    /**
     * 初始化联友SDK
     *
     */
    public void deInitLanYouRequest() {
        MLog.d(TAG,"deInitLanYouRequest");
        mSurpriseEggListModel.deInitLanYouRequest();
    }
}