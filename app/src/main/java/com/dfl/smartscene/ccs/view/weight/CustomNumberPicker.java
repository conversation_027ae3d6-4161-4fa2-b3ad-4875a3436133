package com.dfl.smartscene.ccs.view.weight;

import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;
import android.widget.EditText;
import android.widget.NumberPicker;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.R;

import java.lang.reflect.Field;


public class CustomNumberPicker extends NumberPicker {

    private static final String TAG = CustomNumberPicker.class.getSimpleName();

    private Paint mSelectorWheelPaint;
    private Paint mSelectorWheelPaintCheck;

    private String[] mCacheValues;
    private final int[] mSelectorIndices = new int[3];
    private static final int SELECTOR_MIDDLE_ITEM_INDEX = 3 / 2;

    private int mMinValue = -1;
    private int mMaxValue = -1;

    int mCheckPaintColor = getResources().getColor(R.color.color_custom_number_picker_text_n, null);
    int mWheelPaintColor = getResources().getColor(R.color.color_custom_number_picker_text_s, null);

    public CustomNumberPicker(Context context) {
        super(context);
        initView();
    }

    public CustomNumberPicker(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView();

    }

    public CustomNumberPicker(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();

    }

    private void initView() {
        for (int i = 0; i < getChildCount(); i++) {
            View childAt = getChildAt(i);
            if (childAt instanceof EditText) {
                childAt.setVisibility(View.INVISIBLE);
            }
        }

        mSelectorWheelPaint = new Paint();
        mSelectorWheelPaint.setAntiAlias(true);
        mSelectorWheelPaint.setTextAlign(Paint.Align.CENTER);
        mSelectorWheelPaint.setTextSize(26);
        mSelectorWheelPaint.setColor(mCheckPaintColor);

        mSelectorWheelPaintCheck = new Paint();
        mSelectorWheelPaintCheck.setAntiAlias(true);
        mSelectorWheelPaintCheck.setTextAlign(Paint.Align.CENTER);
        mSelectorWheelPaintCheck.setTextSize(32);
        mSelectorWheelPaintCheck.setColor(mWheelPaintColor);
        initNumberPickerEdit();
    }

    @Override
    public void setEnabled(boolean enabled) {
        super.setEnabled(enabled);
        if (mSelectorWheelPaintCheck != null) {
            mSelectorWheelPaintCheck.setColor(enabled ? mWheelPaintColor : mCheckPaintColor);
        }
        initNumberPickerEdit();
    }

    public void setDisplayValues(String value) {
        mCacheValues = value.split(",");
        setMinValue(0);
        setMaxValue(mCacheValues.length - 1);
        setDisplayedValues(mCacheValues);
    }

    @Override
    public void setDisplayedValues(String[] displayedValues) {
        super.setDisplayedValues(displayedValues);
        mCacheValues = displayedValues;
    }


    @Override
    public void setMinValue(int minValue) {
        mMinValue = minValue;
        generateDisplayedValues();
    }

    @Override
    public void setMaxValue(int maxValue) {
        mMaxValue = maxValue;
        generateDisplayedValues();
    }

    private void generateDisplayedValues() {
        if (null != mCacheValues) {
            return;
        }

        if (mMinValue == -1) {
            return;
        }
        if (mMaxValue == -1) {
            return;
        }

        String[] cacheValues = new String[mMaxValue - mMinValue + 1];
        int curValue = mMinValue;
        for (int i = 0; i < cacheValues.length; i++) {
            cacheValues[i] = String.valueOf(curValue);
            curValue++;
        }

        super.setMinValue(0);
        super.setMaxValue(cacheValues.length - 1);
        setDisplayedValues(cacheValues);
    }


    @Override
    protected void onDraw(Canvas canvas) {

        float x = (float) ((getRight() - getLeft()) / 2);
        float y = computeVerticalScrollOffset();

        int[] selectorIndices = mSelectorIndices;

        int current = getValue();
        for (int i = 0; i < mSelectorIndices.length; i++) {
            int selectorIndex = current + (i - SELECTOR_MIDDLE_ITEM_INDEX);
            selectorIndices[i] = selectorIndex;
        }

        for (int i = 0; i < selectorIndices.length; i++) {
            int selectorIndex = selectorIndices[i];

            String scrollSelectorValue;
            if (selectorIndex >= getMinValue() && selectorIndex <= getMaxValue()) {
                scrollSelectorValue = mCacheValues[selectorIndex];
            } else {
                scrollSelectorValue = "";
            }

            if (i == SELECTOR_MIDDLE_ITEM_INDEX) {
                y += 12;
                canvas.drawText(scrollSelectorValue, x, y, mSelectorWheelPaintCheck);
            } else {
                canvas.drawText(scrollSelectorValue, x, y, mSelectorWheelPaint);
            }
            y += getmSelectorElementHeight();
        }

    }

    private int getmSelectorElementHeight() {
        return computeVerticalScrollRange() / (getMaxValue() - getMinValue() + 1);
    }

    @Override
    protected void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (mSelectorWheelPaint != null && mSelectorWheelPaintCheck != null) {
            LogUtil.d("CustomNumberPicker", "onConfigurationChanged:yes");
            mCheckPaintColor = getResources().getColor(R.color.color_custom_number_picker_text_n, null);
            mWheelPaintColor = getResources().getColor(R.color.color_custom_number_picker_text_s, null);
            mSelectorWheelPaint.setColor(mCheckPaintColor);
            mSelectorWheelPaintCheck.setColor(mWheelPaintColor);
            invalidate();
        }
    }
    private void initNumberPickerEdit() {
        try {
            Field field = NumberPicker.class.getDeclaredField("mInputText");
            field.setAccessible(true);
            EditText editText = (EditText) field.get(this);
            if(editText!=null){
                editText.setVisibility(View.GONE);
                editText.setFocusable(false);
                editText.setEnabled(false);
                editText.setTextColor(getResources().getColor(R.color.transparency));
                editText.setKeyListener(null);
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            LogUtil.d(TAG, "initNumberPickerEdit: "+e.getMessage());
        }
    }

}
