package com.dfl.smartscene.ccs.view.operationview;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;


import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.bean.SeatMemoryDataBean;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.util.DataTypeFormatUtil;
import com.dfl.smartscene.ccs.view.adapter.SeatMemoryRecyclerAdapter;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseView;

import java.util.ArrayList;
import java.util.List;

public class SeatMemoryOperation implements OperationBaseView {

    private static final String TAG = SeatMemoryOperation.class.getSimpleName();

    private RecyclerView mRecyclerView;

    private SeatMemoryRecyclerAdapter mSeatMemoryRecyclerAdapter;

    private final SingleOperation mSingleOperation;

    @Override
    public List<String> extractArgs() {
        ArrayList<String> values = new ArrayList<>(1);
        SeatMemoryDataBean selectData = mSeatMemoryRecyclerAdapter.getSelectData();
        if (null != selectData) {
            values.add(selectData.getValue());
        }
        return values;
    }

    @Override
    public String extractDesc() {
        String desc = mSingleOperation.getArg(ConstantModelValue.OUTPUT_DESC_TEXT);
        SeatMemoryDataBean selectData = mSeatMemoryRecyclerAdapter.getSelectData();
        if (null != selectData) {
            desc = desc.replaceFirst("&", selectData.getDesc());
        }
        return desc;
    }


    public SeatMemoryOperation(SingleOperation singleOperation, ViewGroup parent) {

        mSingleOperation = singleOperation;

        List<String> descList = singleOperation.getListArg(ConstantModelValue.DATA_DESC_LIST);
        List<String> destTextList = singleOperation.getListArg(ConstantModelValue.VIEW_DESC_TEXT_LIST);
        String defaultPosStr = singleOperation.getArg(ConstantModelValue.VIEW_DEFAULT_POS);

        // 判断主副驾
        boolean isDriver = "OID_ACT_SEAT_DRIVER_MEMORY".equals(mSingleOperation.getOperationId());

        // TODO: 从CustomApi中获取座椅记忆数据是否设置，用来判断是否显示“＋”号

        ArrayList<SeatMemoryDataBean> seatMemoryDataBeans = new ArrayList<>(descList.size());
        for (int i = 0; i < descList.size(); i++) {
            String value = descList.get(i);
            String desc = destTextList.get(i);
            seatMemoryDataBeans.add(new SeatMemoryDataBean(desc, value, true));
        }

        LayoutInflater.from(parent.getContext()).inflate(R.layout.item_operation_seat_memory_layout, parent, true);

        View hintView = parent.findViewById(R.id.textview_item_operation_seat_memory_hint);
        hintView.setVisibility(isDriver ? View.VISIBLE : View.GONE);

        mRecyclerView = parent.findViewById(R.id.recyclerview_item_operation_seat_memory_container);
        GridLayoutManager lg = new GridLayoutManager(parent.getContext(), 2, RecyclerView.VERTICAL, false);
        mRecyclerView.setLayoutManager(lg);
        mSeatMemoryRecyclerAdapter = new SeatMemoryRecyclerAdapter();
        mSeatMemoryRecyclerAdapter.addDataList(seatMemoryDataBeans);
        mRecyclerView.setAdapter(mSeatMemoryRecyclerAdapter);

        int selectPos = DataTypeFormatUtil.string2Int(defaultPosStr, 0);
        mSeatMemoryRecyclerAdapter.setSelectedPos(selectPos);

        mSeatMemoryRecyclerAdapter.setOnItemClickListener((view, viewType, seatMemoryDataBean, position) -> {
            mSeatMemoryRecyclerAdapter.setSelectedPos(position);
        });


    }

    @Override
    public void onConfigurationChanged() {

    }
}
