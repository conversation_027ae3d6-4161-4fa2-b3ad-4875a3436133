package com.dfl.smartscene.ccs.view.commonview;

import android.annotation.SuppressLint;
import android.content.Context;
import android.net.Uri;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.viewpager.widget.ViewPager;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.model.DAModel;
import com.dfl.smartscene.ccs.model.manager.SceneControlManager;
import com.dfl.smartscene.ccs.model.manager.SceneController;
import com.dfl.smartscene.ccs.model.manager.VideoManager;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2023/3/10 15:49
 * @description ：舒压模式全屏播放的视图类
 */
public class VideoFullView extends ImageOrVideoBaseView {
    private static final String TAG = VideoFullView.class.getSimpleName();

    private View mBtnFoldView;


    public VideoFullView(@NonNull Context context) {
        super(context);
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    protected void initView(Context context) {
        LogUtil.d(TAG,"initView");
        super.initView(context);
        mBtnFoldView = findViewById(R.id.imageview_scene_full_fold);
        mBtnFoldView.setVisibility(VISIBLE);
        // 按钮：退出全屏
        mBtnFoldView.setOnClickListener(v -> {
            hideImageView();
            DAModel.getInstance().exitFullScreenVideo();
        });
        mViewPager.setOnTouchListener(mOnTouchListener);
    }

    public void hideImageView(){
        for(View videoHolder : mContainerViews){
            ((CustomVideoHolder)videoHolder).hideImage();
        }
        mButtonLayout.setVisibility(GONE);
    }


    @Override
    public void setData(List<String> data , int index) {
        super.setData(data,index);
        if(SceneControlManager.getInstance().getSceneController().getsState() == SceneController.STATE_START){
            startVideo();
        }
        mButtonLayout.setVisibility(VISIBLE);

    }

    @Override
    protected void stopVideo() {
        hideImageView();
        super.stopVideo();
    }

    @Override
    protected void onAttachedToWindow() {
        LogUtil.d(TAG,"onAttachedToWindow");

        super.onAttachedToWindow();
        startImmersion();
    }

    @Override
    protected void onDetachedFromWindow() {
        LogUtil.d(TAG,"onDetachedFromWindow");

        super.onDetachedFromWindow();
    }

    @Override
    protected void onViewPagerScrollStateChanged(int state) {
        if (!isFullScreen()) {
            return;
        }
        if (mViewPager.getChildCount() < 2) {
            return;
        }

        if (state == ViewPager.SCROLL_STATE_DRAGGING) {
            pauseVideo();
        } else if (state == ViewPager.SCROLL_STATE_IDLE && SceneControlManager.getInstance().getSceneController().getsState() == SceneController.STATE_START) {
            resumeVideo();
        }

    }

    @Override
    protected boolean isShow() {
        return isFullScreen();
    }

    @Override
    protected void onFullScreenStateChange(boolean status) {
//        if (status && SceneControlManager.getInstance().getSceneController().getsState() == SceneController.STATE_START) {
//            resumeVideo();
//        } else {
//            pauseVideo();
//            DAModel.getInstance().getVideoController().setPlayProgress(getCurrentPosition());
//        }
    }

    @Override
    protected Uri getVideoUrl(String name) {
        return VideoManager.getInstance().getUriByVideoName(name,VideoManager.TYPE_VIDEO_FULL);
    }

    @Override
    protected void startVideo() {
        resumeVideo();
    }

    @Override
    public void onSceneResume() {
        if (isFullScreen()) {
            resumeVideo();
        }

    }

    @Override
    public void onSceneDestroy() {
        LogUtil.d(TAG,"onSceneDestroy");
        DAModel.getInstance().exitFullScreenVideo();
    }


}
