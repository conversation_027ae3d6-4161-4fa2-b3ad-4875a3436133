package com.dfl.smartscene.ccs.view.dialogfragment;

import android.content.res.Configuration;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.DimenRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseAdapter;
import com.dfl.smartscene.ccs.base.BaseDialogFragment;
import com.dfl.smartscene.ccs.bean.CategoryBean;
import com.dfl.smartscene.ccs.bean.DeviceOperation;
import com.dfl.smartscene.ccs.eventTrack.BigDataManager;
import com.dfl.smartscene.ccs.fragment.SceneEditorFragment;
import com.dfl.smartscene.ccs.util.RecyclerViewUtil;
import com.dfl.smartscene.ccs.view.adapter.MenuClassifyAdapter;
import com.dfl.smartscene.ccs.view.adapter.OperationMenuFragmentAdapter;
import com.dfl.smartscene.ccs.view.baseadapter.ClassifyAdapter;
import com.iauto.uibase.utils.MLog;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Zezheng
 * @date ：2022/9/5 13:41
 * @description ：操作菜单弹窗
 */
public class OperationMenuDialogFragment extends BaseDialogFragment {
    private static final String TAG = "OperationMenuDialogFragment";
    private int mOperationType;//操作类型，触发/状态/动作
    private List<DeviceOperation> mDeviceOperations;

    private TextView mTextViewTitle;
    private ImageView mImageViewClose;
    private static final @DimenRes
    int CLASSIFY_SPACE = R.dimen.y_px_0;
    private RecyclerView mRecyclerViewClassify;
    private ClassifyAdapter mClassifyAdapter;
    private ConstraintLayout mLayoutPageContent;
    protected int mSelectedPosition;
    private ViewPager2 mViewPager2Content;
    private OperationMenuFragmentAdapter operationMenuFragmentAdapter;

    private OperationBaseDialogFragment.OperationChangeListerner mOperationChangeListerner;

    private List<String> mUnableList = new ArrayList<>();

    public OperationMenuDialogFragment(int operationType, List<DeviceOperation> operations, List<String> unableList) {
        mOperationType = operationType;
        mDeviceOperations = operations;
        mUnableList = unableList;
    }

    public OperationMenuDialogFragment(int operationType, List<DeviceOperation> operations) {
        this(operationType, operations, new ArrayList<>());
    }

    @Override
    protected void init() {

    }

    public void setData(int operationType, List<DeviceOperation> operations, List<String> unableList) {
        mOperationType = operationType;
        mDeviceOperations = operations;
        mUnableList = unableList;
        initData();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        LogUtil.d(TAG, "onCreateView");
        return inflater.inflate(R.layout.dialog_operation_menu, container, false);
    }

    @Override
    protected void initView(View view) {
        mRecyclerViewClassify = view.findViewById(R.id.recycleview_base_category_classify);
        mLayoutPageContent = view.findViewById(R.id.layout_page_content);
        mViewPager2Content = view.findViewById(R.id.viewpager2_base_category_content);
        mTextViewTitle = view.findViewById(R.id.textview_operation_menu_dialog_title);
        mImageViewClose = view.findViewById(R.id.imageview_operation_menu_dialog_close);
        RecyclerViewUtil.setLinearRecycleView(mRecyclerViewClassify, RecyclerView.VERTICAL, CLASSIFY_SPACE);
        mViewPager2Content.setOrientation(ViewPager2.ORIENTATION_VERTICAL);
        operationMenuFragmentAdapter = new OperationMenuFragmentAdapter(getChildFragmentManager(), getLifecycle(), mOperationType);
        operationMenuFragmentAdapter.setOperationChangeListerner(mOperationChangeListerner);
        mViewPager2Content.setAdapter(operationMenuFragmentAdapter);
        mClassifyAdapter = new MenuClassifyAdapter();
        mRecyclerViewClassify.setAdapter(mClassifyAdapter);
        mClassifyAdapter.setSelectedPos(mSelectedPosition);

        mViewPager2Content.setUserInputEnabled(false);
        //分类点击相应
        mClassifyAdapter.setOnItemClickListener(new BaseAdapter.OnItemClickListener<CategoryBean>() {
            @Override
            public void onItemClick(View view, int viewType, CategoryBean categoryBean, int position) {
                mClassifyAdapter.setSelectedPos(position);
                mSelectedPosition = position;
                mViewPager2Content.setCurrentItem(position);
            }
        });
        //分类高亮与viewpager保持一致
        mViewPager2Content.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                super.onPageSelected(position);
                mClassifyAdapter.setSelectedPos(position);
                mSelectedPosition = position;
            }
        });
        mViewPager2Content.setSaveEnabled(false);

        mImageViewClose.setOnClickListener(v -> dismiss());
        switch (mOperationType) {
            case SceneEditorFragment.OPERATION_TYPE_CONDITION:
                mTextViewTitle.setText("触发条件");
                break;
            case SceneEditorFragment.OPERATION_TYPE_ACTION:
                mTextViewTitle.setText("添加操作");
                break;
            case SceneEditorFragment.OPERATION_TYPE_STATE:
                mTextViewTitle.setText("状态条件");
                break;
            default:
                MLog.e(TAG, "invalid operation type " + mOperationType);
                break;
        }
    }

    @Override
    protected void initObserver() {

    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        operationMenuFragmentAdapter.setDeviceOperations(new ArrayList<>());
//        if (mOnDismissListener != null) {
//            mOnDismissListener.onDismiss();
//        }
//        mOnDismissListener = null;

    }

    @Override
    protected void initData() {
        List<CategoryBean> categoryBeans = new ArrayList<>();
        for (DeviceOperation deviceOperation : mDeviceOperations) {
            categoryBeans.add(new CategoryBean(deviceOperation.getDeviceId(), deviceOperation.getDeviceName(), deviceOperation.getDeviceIcon()));
        }
        mClassifyAdapter.setDataList(categoryBeans);
        operationMenuFragmentAdapter.setData(mDeviceOperations, mUnableList);


        //埋点：弹窗出现
        LogUtil.d(TAG, "埋点：浏览弹窗");
        BigDataManager.getInstance().writeEventMod4("OperationMenuDialogFragment", mTextViewTitle.getText().toString());
    }

    public void setOperationChangeListerner(OperationBaseDialogFragment.OperationChangeListerner operationChangeListerner) {
        mOperationChangeListerner = operationChangeListerner;
        if (operationMenuFragmentAdapter != null) {
            operationMenuFragmentAdapter.setOperationChangeListerner(operationChangeListerner);
        }
    }

//    public interface OnDismissListener {
//        void onDismiss();
//    }
//
//    private OnDismissListener mOnDismissListener;
//
//    public void setOnDismissListener(OnDismissListener onDismissListener) {
//        mOnDismissListener = onDismissListener;
//    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (mRecyclerViewClassify != null) {
            mRecyclerViewClassify.removeAllViews();
            mRecyclerViewClassify.removeAllViewsInLayout();
            mRecyclerViewClassify.setAdapter(mClassifyAdapter);
            mRecyclerViewClassify.getRecycledViewPool().clear();
            List<CategoryBean> categoryBeans = new ArrayList<>();
            for (DeviceOperation deviceOperation : mDeviceOperations) {
                categoryBeans.add(new CategoryBean(deviceOperation.getDeviceId(), deviceOperation.getDeviceName(), deviceOperation.getDeviceIcon()));
            }
            mClassifyAdapter.setDataList(categoryBeans);
        }

    }


}