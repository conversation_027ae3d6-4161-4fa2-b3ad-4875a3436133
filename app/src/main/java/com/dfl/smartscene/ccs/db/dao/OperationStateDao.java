package com.dfl.smartscene.ccs.db.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;


import com.dfl.smartscene.ccs.db.entity.OperationStateEntity;

import java.util.List;

import io.reactivex.Observable;

@Dao
public interface OperationStateDao {

    @Insert
    void insertStudent(OperationStateEntity item);

    @Delete
    void deleteStudent(OperationStateEntity item);

    @Update
    void updateStudent(OperationStateEntity item);

    @Query("SELECT * FROM operation_state")
    Observable<List<OperationStateEntity>> queryAll();

    @Query("SELECT * FROM operation_state WHERE :id='' or id= :id")
    Observable<List<OperationStateEntity>> queryById(String id);

    @Query("SELECT * FROM operation_state WHERE id in (:ids)")
    Observable<List<OperationStateEntity>> queryByIds(String[] ids);
}
