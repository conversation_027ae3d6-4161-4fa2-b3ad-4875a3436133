package com.dfl.smartscene.ccs.util;

import com.iauto.uibase.utils.MLog;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * Timer池管理
 */
public class TimerProcessor {
    private static final String CLASSNAME = "TimerProcessor";

    private static final int CORE_POOL_SIZE = 3;
    private static TimerProcessor sInstance = null;
    private ScheduledExecutorService mScheduledExecutorService = null;
    private ConcurrentHashMap<Integer, ScheduledFuture> mTimerMap =
            new ConcurrentHashMap<Integer, ScheduledFuture>();

    private TimerProcessor() {
        MLog.d(CLASSNAME, "Construct");
    }

    public synchronized static TimerProcessor getInstance() {
        if (null == sInstance) {
            sInstance = new TimerProcessor();
            sInstance.startScheduledService(CORE_POOL_SIZE);
        }
        return sInstance;
    }

    /**
     * Creates a thread pool that can schedule commands to run after a
     * given delay, or to execute periodically.
     *
     * @param corePoolSize the number of threads to keep in the pool,
     *                     even if they are idle
     */
    private void startScheduledService(int corePoolSize) {
        try {
            if (null == mScheduledExecutorService) {
                mScheduledExecutorService = Executors.newScheduledThreadPool(corePoolSize);
                MLog.d(CLASSNAME, "mScheduledExecutorService newScheduledThreadPool: " + corePoolSize);
            }
        } catch (Exception e) {
            MLog.d(CLASSNAME, "startScheduledService exception: " + e.getMessage());
        }
    }

    /**
     * Attempts to stop all actively executing tasks, halts the
     * processing of waiting tasks, and returns a list of the tasks
     * that were awaiting execution.
     */
    public void stopScheduledService() {
        MLog.d(CLASSNAME, "stopScheduledService");

        try {
            if (null == mScheduledExecutorService) {
                MLog.d(CLASSNAME, "mScheduledExecutorService is null, return");
                return;
            }
            if (!mScheduledExecutorService.isShutdown()) {
                mScheduledExecutorService.shutdownNow();
                mScheduledExecutorService = null;
                MLog.d(CLASSNAME, "mScheduledExecutorService shutdownNow");
            }
        } catch (Exception e) {
            MLog.d(CLASSNAME, "stopScheduledService exception: " + e.getMessage());
        }
    }

    /**
     * Submits a one-shot task that becomes enabled after the given delay.
     *
     * @param timerId  the Timer Name
     * @param timerVal the time from now to delay execution
     * @param runnable the task to execute
     */
    public void startTimer(int timerId, long timerVal, boolean isLoop, Runnable runnable) {
        MLog.d(CLASSNAME, "startTimer timerId: " + timerId + ", timerVal: " + timerVal);
        try {
            ScheduledFuture scheduledFuture = findTimer(timerId);
            if (null != scheduledFuture) {
                if (!scheduledFuture.isCancelled()) {
                    scheduledFuture.cancel(false);
                }
            }
            if (null == mScheduledExecutorService) {
                MLog.d(CLASSNAME, "mScheduledExecutorService is null, startTimer return");
                return;
            }
            if (isLoop) {
                scheduledFuture = mScheduledExecutorService.scheduleAtFixedRate(runnable, timerVal, timerVal, TimeUnit.MILLISECONDS);
            } else {
                scheduledFuture = mScheduledExecutorService.schedule(runnable, timerVal, TimeUnit.MILLISECONDS);
            }

            mTimerMap.put(timerId, scheduledFuture);
        } catch (Exception e) {
            MLog.d(CLASSNAME, "startTimer exception: " + e.getMessage());
        }
    }

    /**
     * Attempts to cancel execution of this task.
     *
     * @param timerId the Timer Name
     */
    public void stopTimer(int timerId) {
        MLog.d(CLASSNAME, "stopTimer timerId: " + timerId);
        try {
            ScheduledFuture scheduledFuture = findTimer(timerId);
            if (null != scheduledFuture) {
                if (!scheduledFuture.isCancelled()) {
                    MLog.d(CLASSNAME, "stop timer succeed");
                    scheduledFuture.cancel(false);
                }
            }
        } catch (Exception e) {
            MLog.d(CLASSNAME, "stopTimer exception: " + e.getMessage());

        }
    }

    private ScheduledFuture findTimer(int timerId) {
        return mTimerMap.get(timerId);
    }
}
