package com.dfl.smartscene.ccs.view.commonview;

import android.content.Context;
import android.media.session.PlaybackState;
import android.net.Uri;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.lifecycle.Observer;
import androidx.viewpager.widget.ViewPager;

import com.dfl.dflcommonlibs.dialog.DefaultDoubleCountDownListener;
import com.dfl.dflcommonlibs.dialog.DialogUtil;
import com.dfl.dflcommonlibs.dialog.LibDialog;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.model.DAModel;
import com.dfl.smartscene.ccs.model.DriveModel;
import com.dfl.smartscene.ccs.model.MediaSyncModel;
import com.dfl.smartscene.ccs.model.manager.CarConfigManager;
import com.dfl.smartscene.ccs.model.manager.SceneControlManager;
import com.dfl.smartscene.ccs.model.manager.SceneController;
import com.dfl.smartscene.ccs.model.manager.VideoManager;
import com.dfl.smartscene.ccs.util.DataTypeFormatUtil;
import com.dfl.smartscene.ccs.util.ToastManager;
import com.dfl.smartscene.ccs.view.ViewControlManager;
import com.dfl.smartscene.ccs.view.base.SceneSmallViewViewModel;
import com.iauto.uicontrol.ANumberPicker;

import java.util.List;

/**
 * <AUTHOR> Zezheng
 * @date ：2023/3/10 15:16
 * @description ：舒压模式当中的视频播放器模块
 */
public class VideoServiceView extends ImageOrVideoBaseView{
    private static final String TAG = VideoServiceView.class.getSimpleName();

    private View mBtnExpendView;
    private ANumberPicker mTimerPicker;
    private View layoutPrepareButton;
    public View buttonStart;
    private SingleOperation mSingleOperation;

    private SceneSmallViewViewModel mSceneSmallViewViewModel;

    public VideoServiceView(@NonNull Context context) {
        super(context);
        initObserver();
    }

    @Override
    protected void initView(Context context) {
        super.initView(context);
        isFullScreenStatus = false;
        mBtnExpendView = findViewById(R.id.imageview_scene_full_expand);
        layoutPrepareButton = findViewById(R.id.layout_video_service_prepare);
        buttonStart = findViewById(R.id.textview_scene_small_video_start);
        mBtnExpendView.setVisibility(VISIBLE);
        // 按钮：全屏
        mBtnExpendView.setOnClickListener(v -> {
            DAModel.getInstance().requestFullScreenVideo();
        });
        mTimerPicker = findViewById(R.id.time_picker_video_service);
        initTimePicker();
        buttonStart.setOnClickListener(v -> {
            if(!DriveModel.getInstance().isPGear()){
                ToastManager.showToast("请在P挡下开启舒压模式");
                return;
            }
            try {
                DialogUtil.showDefaultDoubleDialog(new DefaultDoubleCountDownListener() {
                    @Override
                    public int getCountDownNumber() {
                        return 5;
                    }

                    @Override
                    public OnClickListener getPositiveButtonListener(LibDialog libDialog) {
                        return v -> {
                            if(mSingleOperation != null){
                                List<String> data = mSingleOperation.getListArg(ConstantModelValue.DATA_DESC_LIST);
                                SceneControlManager.getInstance().getSceneController().setTotalTimeSecond(DataTypeFormatUtil.string2Int(data.get(mTimerPicker.getValue()),-1));
                                SceneControlManager.getInstance().startScene();
                            }
                            libDialog.dismissAllowingStateLoss();
                        };
                    }

                    @Override
                    public int getDialogLayout() {
                        return R.layout.dialog_double_content_layout;
                    }

                    @Override
                    public int getCountDownTextId() {
                        return R.id.button_default_double_positive;
                    }

                    @Override
                    public String getPositiveButtonName() {
                        return "立即开启";
                    }

                    @Override
                    public String getDialogMessage() {
                        return "模式即将开启，请确保车辆及车内人员处于安全状态";
                    }

                    @Override
                    public void onCountDownFinish(LibDialog dialog) {
                        //倒计时结束时，直接开始
                        if(mSingleOperation != null){
                            List<String> data = mSingleOperation.getListArg(ConstantModelValue.DATA_DESC_LIST);
                            SceneControlManager.getInstance().getSceneController().setTotalTimeSecond(DataTypeFormatUtil.string2Int(data.get(mTimerPicker.getValue()),-1));
                            SceneControlManager.getInstance().startScene();
                        }
                        dialog.dismissAllowingStateLoss();
                    }
                });

            }catch (Exception e){
                LogUtil.e(TAG, "buttonStart click: "+e.getMessage());
            }

        });

        layoutPrepareButton.setVisibility(VISIBLE);
        mButtonLayout.setVisibility(GONE);

        OverTopContentView overTopContentView = new OverTopContentView();
        overTopContentView.initView(this);
        overTopContentView.getBackButton().setOnClickListener(v -> {
            SceneControlManager.getInstance().exitScene();
            if (mListener != null) {
                mListener.onCloseScene();
            }
        });

        overTopContentView.getCloseButton().setOnClickListener( v-> {
            SceneControlManager.getInstance().exitScene();
            if (mListener != null) {
                mListener.onCloseScene();
            }
            ViewControlManager.closeApp();
        });

//        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) mCountDownView.getLayoutParams();
//        layoutParams.topMargin = getResources().getDimensionPixelOffset(R.dimen.y_px_20);
//        mCountDownView.setLayoutParams(layoutParams);
        setDefaultNightMode();
    }

    private void initObserver(){
        LogUtil.d(TAG, "initObserver: ");
        indexObserver = this::setViewPagerIndex;
        if(DAModel.getInstance().getVideoController()!=null){
            DAModel.getInstance().getVideoController().getPlayIndexLiveData().observeForever(indexObserver);
        }
        MediaSyncModel.getInstance().getPlayStatus().observeForever(playStatusObserver);
        mSceneSmallViewViewModel = SceneSmallViewViewModel.getInstance(ScenePatternApp.getInstance());
        
    }
    private Observer<Integer> indexObserver;

    private Observer<Integer> playStatusObserver = new Observer<Integer>() {
        @Override
        public void onChanged(Integer status) {
            String activeName = MediaSyncModel.getInstance().getActiveSource();
            int videoStatus = SceneControlManager.getInstance().getSceneController().getsState();
            LogUtil.d(TAG, "playStatus onChanged: "+activeName+" ,, videoStatus "+videoStatus+" ,, "+status );
            LogUtil.d(TAG, "playStatus onChanged: "+mBtnPlay.getVisibility() +" "+mBtnPause.getVisibility());
//            if(!activeName.equals(ScenePatternFuncDef.MEDIA_SOURCE_ONLINE_PACKAGE_NAME) ){
//                return;
//            }
            if(videoStatus == SceneController.STATE_START && status == PlaybackState.STATE_PAUSED && mBtnPause.getVisibility()== View.VISIBLE){
                mBtnPause.performClick();
            }else if(videoStatus == SceneController.STATE_PAUSE && status == PlaybackState.STATE_PLAYING && mBtnPlay.getVisibility()== View.VISIBLE){
                mBtnPlay.performClick();
            }

        }
    };

    @Override
    protected int getLayoutId() {
        return R.layout.layout_video_service;
    }

    private void initTimePicker(){

        mSingleOperation = CarConfigManager.getInstance().findSingleOperationById(ConstantModelValue.OID_ACT_MORE_FULL_SCREEN_TIME);
        if(mSingleOperation == null){
            mTimerPicker.setVisibility(GONE);
            return;
        }
        List<String> descs = mSingleOperation.getListArg(ConstantModelValue.VIEW_DESC_TEXT_LIST);
        int defaultPos = DataTypeFormatUtil.string2Int(mSingleOperation.getArg(ConstantModelValue.VIEW_DEFAULT_POS),0);

        StringBuilder newDescs = new StringBuilder();
        for(String desc : descs){
            if(descs.indexOf(desc) != 0){
                newDescs.append(",");
            }
            newDescs.append(desc);
        }
        mTimerPicker.setOutlineAmbientShadowColor(getResources().getColor(R.color.color_video_time_picker_bg));
        mTimerPicker.setDisplayValues(newDescs.toString());
        mTimerPicker.setCurValue(defaultPos);
    }

    @Override
    protected void onViewPagerScrollStateChanged(int state) {
        if(isFullScreen()){
            return;
        }
        if (mViewPager.getChildCount() < 2) {
            return;
        }

        if (state == ViewPager.SCROLL_STATE_DRAGGING) {
            pauseVideo();
        } else if (state == ViewPager.SCROLL_STATE_IDLE && SceneControlManager.getInstance().getSceneController().getsState() == SceneController.STATE_START) {
            resumeVideo();
        }

    }

    @Override
    protected boolean isShow() {
        return !isFullScreen();
    }

    @Override
    protected void onFullScreenStateChange(boolean status) {
        LogUtil.d(TAG,"onFullScreenStateChange  status " + status);
        if(SceneControlManager.getInstance().getSceneController().getsState() == SceneController.STATE_START){
            DAModel.getInstance().getVideoController().setPlayProgress(getCurrentPosition());
//            pauseVideo();
            resumeVideo();
        }

        if(!status){
//            for(View view : mContainerViews){
//                view.setVisibility(VISIBLE);
//            }

            int playIndex = DAModel.getInstance().getVideoController().getPlayIndex();
            setViewPagerIndex(playIndex);
            if(!MediaSyncModel.getInstance().getPlayStatus().hasObservers()){
                removeObservers();
                initObserver();
            }

        }
//        if(status){
//            for(View view : mContainerViews){
//                view.setVisibility(GONE);
//            }
//        }

    }

    @Override
    public void setData(List<String> data , int index) {
        VideoManager.getInstance().initVideoBitmap(data);
        super.setData(data , index);
    }

    @Override
    protected Uri getVideoUrl(String name) {
        return VideoManager.getInstance().getUriByVideoName(name,VideoManager.TYPE_VIDEO_APP);
    }

    @Override
    public void onSceneStart() {
        LogUtil.d(TAG, "onSceneStart: ");
        super.onSceneStart();
        layoutPrepareButton.setVisibility(GONE);
        mButtonLayout.setVisibility(VISIBLE);
        startVideo();
        //式样要求一开始就是全屏模式播放
        DAModel.getInstance().requestFullScreenVideo();
        mSceneSmallViewViewModel.addObservers();
    }

    @Override
    public void onSceneResume() {
        if(!isFullScreen()){
            resumeVideo();
        }
    }

    @Override
    public void onSceneDestroy() {
        LogUtil.d(TAG,"onSceneDestroy");
        VideoManager.getInstance().clearVideoBitmap();
        // 20240822更新：不关闭当前页面，播放完毕回到舒压模式首页
        if (mListener != null) {
//            mListener.onCloseScene();
        }
        layoutPrepareButton.setVisibility(VISIBLE);
        mButtonLayout.setVisibility(GONE);
        removeObservers();
        mSceneSmallViewViewModel.removeObservers();
    }

    private void removeObservers() {
        DAModel.getInstance().getVideoController().getPlayIndexLiveData().removeObserver(indexObserver);
        MediaSyncModel.getInstance().getPlayStatus().removeObserver(playStatusObserver);
    }

    private IImageOrVideoViewListener mListener;

    public void setIImageOrVideoViewListener(IImageOrVideoViewListener listener) {
        mListener = listener;
    }

    public interface IImageOrVideoViewListener {
        void onCloseScene();
    }

    /**为了解决返回键和关闭键的可视性的问题，需要单独设置一次资源显示*/
    public void setDefaultNightMode(){
        ImageView closeImageView = layoutPrepareButton.findViewById(R.id.button_content_top_close);
        closeImageView.setImageResource(R.drawable.icon_video_close);
        ImageView backImageView = layoutPrepareButton.findViewById(R.id.button_content_top_back);
        backImageView.setImageResource(R.drawable.icon_video_back);
    }

}
