package com.dfl.smartscene.ccs.view.weight;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.SeekBar;

import androidx.core.content.ContextCompat;

import com.bumptech.glide.Glide;
import com.dfl.dflcommonlibs.uimodeutil.UITextView;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.library.app.ContextUtil;
import com.iauto.uibase.utils.MLog;

public class AdjustSlideView extends LinearLayout {


    private ImageView startImageView;
    private ImageView endImageView;
    private SeekBar seekBar;
    //增加一个进度条数值显示，测试指摘对应
    private UITextView mSeekProgress;

    private int imgViewWidth = 40;
    private int imgViewHeight = 40;

    private int mProgressMax = 100;
    private int mProgressMin = 0;

    private String mStartPicStr;
    private String mEndPicStr;

    private int mStepValue = 1;

    public AdjustSlideView(Context context) {
        this(context, null);
    }

    public AdjustSlideView(Context context, AttributeSet attrs) {
        super(context, attrs);

        initView(context);

    }

    @SuppressLint("UseCompatLoadingForDrawables")
    private void initView(Context context) {

        setOrientation(VERTICAL);
        setGravity(Gravity.CENTER_VERTICAL);

        mSeekProgress = new UITextView(context);
        mSeekProgress.setTextColor(context.getColor(R.color.color_text_first_label));
        mSeekProgress.setTextSize(28);

        ViewGroup.LayoutParams params1 = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,40);
        mSeekProgress.setGravity(Gravity.CENTER);
        mSeekProgress.setLayoutParams(params1);

        LinearLayout container = new LinearLayout(context);
        container.setGravity(Gravity.CENTER_VERTICAL);
        ViewGroup.LayoutParams params2 = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,46);
        container.setLayoutParams(params2);

        startImageView = new ImageView(context);
        startImageView.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
        startImageView.setOnClickListener(v -> {
            reduceProgress();
        });
        container.addView(startImageView, new LayoutParams(46,46));

        seekBar = new SeekBar(context);
        seekBar.setIndeterminate(false);
        seekBar.setProgressDrawable(getResources().getDrawable(R.drawable.seekbar_progress_drawable, null));
        seekBar.setThumb(ContextCompat.getDrawable(getContext(), R.drawable.ic_volume_seekbar_thumb));
        seekBar.setBackground(null);
        seekBar.setMin(mProgressMin);
        seekBar.setMax(mProgressMax);
        seekBar.setSplitTrack(false);
        seekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if(fromUser){
                    mSeekProgress.setText(String.valueOf(progress));
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                mSeekProgress.setText(String.valueOf(seekBar.getProgress()));
            }
        });

        LayoutParams params = new LayoutParams(0, 46, 1);
        params.setMargins(-5, 0, -5, 0);
        container.addView(seekBar, params);

        endImageView = new ImageView(context);
        endImageView.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
        endImageView.setOnClickListener(v -> {
            addProgress();
        });
        container.addView(endImageView, new LayoutParams(46,46));

        addView(mSeekProgress);
        addView(container);
//        setBackgroundResource(R.drawable.ic_adjust_step_bg);
    }

    public void setStartAndEndIconSize(int w, int h) {
        imgViewWidth = w;
        imgViewHeight = h;
    }

    public void setProgressMinMax(int min, int max) {
        mProgressMax = max;
        mProgressMin = min;
    }

    public void setPicName(String startPicStr, String endPicStr) {
        mStartPicStr = startPicStr;
        mEndPicStr = endPicStr;
    }

    public void setProgress(int value) {
        if (null != seekBar) {
            seekBar.setProgress(value);
        }
        if (null != mSeekProgress) {
            mSeekProgress.setText(String.valueOf(value));
        }
    }

    public void setStepValue(int value) {
        mStepValue = value;
    }

    public int getProgress() {
        if (seekBar != null) {
            return seekBar.getProgress();
        }
        return -1;
    }

    public void refreshView() {
        if (null != startImageView) {
            if (!TextUtils.isEmpty(mStartPicStr)) {
                if (mStartPicStr.startsWith("http")) {
                    Glide.with(this).load(mStartPicStr).into(startImageView);
                } else {
                    Drawable drawable = ContextUtil.getDrawableByName(mStartPicStr);
                    MLog.d("AdjustSlideView","mStartPicStr:"+mStartPicStr);
                    if (null != drawable) {
                        startImageView.setImageDrawable(drawable);
                    }
                }
            } else {
                startImageView.setBackgroundResource(R.drawable.ic48_system_reduce_n);
            }

        }

        if (null != endImageView) {
            if (!TextUtils.isEmpty(mEndPicStr)) {
                if (mEndPicStr.startsWith("http")) {
                    Glide.with(this).load(mEndPicStr).into(startImageView);
                } else {
                    Drawable drawable = ContextUtil.getDrawableByName(mEndPicStr);
                    MLog.d("AdjustSlideView","mEndPicStr:"+mEndPicStr);
                    if (null != drawable) {
                        endImageView.setImageDrawable(drawable);
                    }
                }
            } else {
                endImageView.setBackgroundResource(R.drawable.ic48_system_plus_n);
            }

        }

        if (null != seekBar) {
            seekBar.setMin(mProgressMin);
            seekBar.setMax(mProgressMax);
            seekBar.setThumb(ContextCompat.getDrawable(getContext(), R.drawable.ic_volume_seekbar_thumb));
        }

        if (mSeekProgress != null){
            mSeekProgress.setTextColor(getContext().getColor(R.color.color_text_first_label));
            mSeekProgress.setTextSize(28);
        }

    }

    private void addProgress() {
        if (null != seekBar) {
            int progress = seekBar.getProgress() + mStepValue;
            if (progress >= mProgressMax) {
                progress = mProgressMax;
            }
            mSeekProgress.setText(String.valueOf(progress));
            seekBar.setProgress(progress);
        }
    }

    private void reduceProgress() {
        if (null != seekBar) {
            int progress = seekBar.getProgress() - mStepValue;
            if (progress <= mProgressMin) {
                progress = mProgressMin;
            }
            mSeekProgress.setText(String.valueOf(progress));
            seekBar.setProgress(progress);
        }
    }

}
