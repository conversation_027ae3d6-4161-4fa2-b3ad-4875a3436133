package com.dfl.smartscene.ccs.util;

import android.graphics.Rect;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

public class HorizontalItemDecorationUtil extends RecyclerView.ItemDecoration {
    private int space;

    public HorizontalItemDecorationUtil(int space) {
        this.space = space;
    }


    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        super.getItemOffsets(outRect, view, parent, state);
        outRect.right = 0;
        if(parent.getChildLayoutPosition(view) == 0){
            outRect.left = 0;
        }else {
            outRect.left = space;
        }
    }
}

