package com.dfl.smartscene.ccs.model.player;

import android.content.ComponentName;
import android.os.Bundle;
import android.support.v4.media.session.MediaControllerCompat;

import com.iauto.uibase.utils.MLog;

/**
 * <AUTHOR>
 * @date ：2023/5/30 11:39
 * @description ：
 */
public class MusicPlayer extends CommonPlayer{
    private static final String TAG = "MusicPlayer";

    public MusicPlayer(CommonPlayer commonPlayer) {
        super(commonPlayer);
    }

    public MusicPlayer(ComponentName componentName) {
        super(componentName);
    }

    @Override
    public void play(String arg) {
        String[] split = arg.split("@");
        MediaControllerCompat controller = getController();
        if (controller == null) {
            return;
        }
        if (METHOD_SPECIAL.equals(split[0])) {
            if (PARAM_RESUME.equals(split[1])) {
                controller.getTransportControls().play();
            } else if (PARAM_RECOMMEND.equals(split[1])) {
                MLog.d(TAG, "play recommend music");
                controller.getTransportControls().sendCustomAction(KEY_MUSIC_RECOMMEND, new Bundle());
            } else if (PARAM_COLLECT.equals(split[1])) {
                MLog.d(TAG, "play collect music");
                controller.getTransportControls().sendCustomAction(KEY_MUSIC_COLLECT, new Bundle());
            }
        } else if (METHOD_SCENE.equals(split[0])) {
            MLog.d(TAG, "play scene music");

            Bundle bundle = new Bundle();
            bundle.putString("MediaSessionExtraParentMediaID", "MediaSessionExtraSceneListMediaId");
            controller.getTransportControls().playFromMediaId(split[1], bundle);//场景id
        }

    }
}
