package com.dfl.smartscene.ccs.model;

import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.model.manager.CustomApiManager;

/**
 * <AUTHOR>
 * @date ：2022/10/9 15:04
 * @description ：车窗管理类
 */
public class WindowModel implements StateBaseModel{
    private static final String TAG = "WindowModel";
    private static volatile WindowModel sInstance;

    public static WindowModel getInstance() {
        if (null == sInstance) {
            synchronized (WindowModel.class) {
                if (null == sInstance) {
                    sInstance = new WindowModel();
                }
            }
        }
        return sInstance;
    }

    @Override
    public boolean checkStateOperationMeet(SettingOperation settingOperation) {
        try {
            //1:左前 2：右前 3：左后 4：右后
            switch (settingOperation.getOperationId()){
                case ConstantModelValue.OID_STA_WINDOW_FRONT_LEFT:
                    int frontLeftWindowPosition = CustomApiManager.getInstance().getIWindow().getWindowPosition(1);
                    return simpleStateCheck(settingOperation.getListArgs(),String.valueOf(5 * (frontLeftWindowPosition - 1)));
                case ConstantModelValue.OID_STA_WINDOW_FRONT_RIGHT:
                    int frontRightWindowPosition = CustomApiManager.getInstance().getIWindow().getWindowPosition(2);
                    return simpleStateCheck(settingOperation.getListArgs(),String.valueOf(5 * (frontRightWindowPosition - 1)));

                case ConstantModelValue.OID_STA_WINDOW_REAR_LEFT:
                    int rearLeftWindowPosition = CustomApiManager.getInstance().getIWindow().getWindowPosition(3);
                    return simpleStateCheck(settingOperation.getListArgs(),String.valueOf(5 * (rearLeftWindowPosition - 1)));
                case ConstantModelValue.OID_STA_WINDOW_REAR_RIGHT:
                    int rearRightWindowPosition = CustomApiManager.getInstance().getIWindow().getWindowPosition(4);
                    return simpleStateCheck(settingOperation.getListArgs(),String.valueOf(5 * (rearRightWindowPosition - 1)));
                default:
                    return false;
            }
        }catch (Exception e){
            e.printStackTrace();
            return false;
        }

    }
}
