package com.dfl.smartscene.ccs.viewmodel;

import android.app.Application;

import androidx.lifecycle.MutableLiveData;

import com.dfl.smartscene.ccs.model.SurpriseEggPlayModel;
import com.iauto.uibase.lifecycle.MViewModelBase;
import com.iauto.uibase.utils.MLog;

/**
 * 惊喜菜单播放view model
 * <AUTHOR>
 * @date 2022/06/10
 */
public class SurpriseEggPlayVM extends MViewModelBase {
    private static final String               TAG = "SurpriseEggPlayVM";
    private  final SurpriseEggPlayModel mSurpriseEggPlayModel;

    /**
     * SeatSettingVM object constructor
     * @param application object
     */
    public SurpriseEggPlayVM(Application application) {
        super(application);
        mSurpriseEggPlayModel = SurpriseEggPlayModel.getInstance();
        initLiveData();
    }

    /**
     * init seat live data
     */
    public void initLiveData() {
        MLog.d(TAG,"initLiveData ");
    }

    public MutableLiveData<String> getSurpriseEggURL() {
        return mSurpriseEggPlayModel.getSurpriseEggURL();
    }

    public MutableLiveData<String> getSurpriseEggTTSText() {
        return mSurpriseEggPlayModel.getSurpriseEggTTSText();
    }

    public MutableLiveData<Integer> getSurpriseEggPlaybackStatus() {
        return mSurpriseEggPlayModel.getSurpriseEggPlaybackStatus();
    }

    public void setSurpriseEggPlaybackStatus(int playStatus) {
        mSurpriseEggPlayModel.setSurpriseEggPlaybackStatus(playStatus);
    }

    public MutableLiveData<Integer> getTTSPlayStatus() {
        return mSurpriseEggPlayModel.getTTSPlayStatus();
    }

    public void resetTTSPlayStatus() {
        mSurpriseEggPlayModel.resetTTSPlayStatus();
    }

    public MutableLiveData<String> getSurpriseEggVoiceFile() {
        return mSurpriseEggPlayModel.getSurpriseEggVoiceFile();
    }

    public MutableLiveData<Integer> getSurpriseEggVoiceType() {
        return mSurpriseEggPlayModel.getSurpriseEggVoiceType();
    }

    public MutableLiveData<Integer> getVoiceFilePlayStatus() {
        return mSurpriseEggPlayModel.getVoiceFilePlayStatus();
    }
    public void resetVoiceFilePlayStatus() {
        mSurpriseEggPlayModel.resetVoiceFilePlayStatus();

    }
}
