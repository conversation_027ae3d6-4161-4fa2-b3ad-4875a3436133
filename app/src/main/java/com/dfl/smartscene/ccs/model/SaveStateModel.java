package com.dfl.smartscene.ccs.model;


import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SettingOperation;

/**
 * <AUTHOR>
 * @date ：2022/9/27 9:38
 * @description ：
 */
public interface SaveStateModel {
    /**
     * 保存当前车辆状态到场景
     * @param sceneEditor
     * @param deviceOperation
     * @return
     */
    public abstract SceneBean saveCurrentActionToSceneBean(SceneEditor sceneEditor, SettingOperation deviceOperation);

}
