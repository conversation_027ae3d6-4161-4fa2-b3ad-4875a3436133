package com.dfl.smartscene.ccs.util;

import android.content.Context;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.net.Uri;

import com.dfl.smartscene.ccs.constant.SurpriseEggFuncDef;
import com.dfl.smartscene.ccs.library.app.ScenePattern;
import com.dfl.smartscene.ccs.model.SurpriseEggPlayModel;
import com.iauto.uibase.utils.MLog;

public class AudioPlayer {

    private static final String TAG = "AudioPlayer";
    private static AudioPlayer instance;
    private MediaPlayer mMediaPlayer;
    private Context context = ScenePattern.getInstance().getContext();

    private AudioPlayer() {
    }

    public static AudioPlayer getInstance() {
        if (instance == null) {
            instance = new AudioPlayer();
        }
        return instance;
    }

    private MediaPlayer.OnErrorListener mErrorListener = new MediaPlayer.OnErrorListener() {
        @Override
        public boolean onError(MediaPlayer mp, int what, int extra) {
            MLog.d(TAG, "onError: ");
            SurpriseEggPlayModel.getInstance().getVoiceFilePlayStatus().setValue(SurpriseEggFuncDef.VOICE_FILE_Playback_Status_Interrupt);
            return false;
        }
    };

    private MediaPlayer.OnPreparedListener mPrepareListener = new MediaPlayer.OnPreparedListener() {
        @Override
        public void onPrepared(MediaPlayer mp) {
            MLog.d(TAG, "onPrepared: ");
            mp.start();
            SurpriseEggPlayModel.getInstance().getVoiceFilePlayStatus().setValue(SurpriseEggFuncDef.VOICE_FILE_Playback_Status_Play);
        }
    };

    private MediaPlayer.OnCompletionListener mCompletionListener  =new MediaPlayer.OnCompletionListener() {
        @Override
        public void onCompletion(MediaPlayer mp) {
            MLog.d(TAG, "onCompletion: ");
            SurpriseEggPlayModel.getInstance().getVoiceFilePlayStatus().setValue(SurpriseEggFuncDef.VOICE_FILE_Playback_Status_Stop);
        }
    };



    public void start(String audioUrl) {
        MLog.d(TAG, "startPlaying: " + audioUrl + " ,, " + mMediaPlayer);
        if(audioUrl == null || audioUrl.isEmpty()){
            return;
        }
//        if (!AudioFocusManager.getInstance().requestMainAudioFocus()) {
//            return;
//        }
        if (mMediaPlayer != null) {
            stop();
        }
        if (mMediaPlayer == null) {
            mMediaPlayer = new MediaPlayer();
            mMediaPlayer.setOnPreparedListener(mPrepareListener);
            mMediaPlayer.setOnErrorListener(mErrorListener);
            mMediaPlayer.setOnCompletionListener(mCompletionListener);
            mMediaPlayer.setAudioStreamType(AudioManager.STREAM_MUSIC);
        }
        // 如果MediaPlayer已经存在，先停止并重置
        try {
            mMediaPlayer.setDataSource(context, Uri.parse(audioUrl));
            mMediaPlayer.prepareAsync();
//            mMediaPlayer.start();
        } catch (Exception e) {
            MLog.e(TAG, "startPlaying throw exception: " + e.getMessage());
        }

    }

    public void stop() {
        MLog.d(TAG, "stopPlaying: " + mMediaPlayer);
        if (mMediaPlayer != null) {
            mMediaPlayer.stop();
            mMediaPlayer.release();
            mMediaPlayer = null;
        }
    }
    public boolean isPlaying() {
        return mMediaPlayer != null && mMediaPlayer.isPlaying();
    }
}
