package com.dfl.smartscene.ccs.bean;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/7/12 11:41
 * @description ：一个设备及其具备的操作
 */
public class DeviceOperation {
    /**
     * 设备id
     */
    private String deviceId = "";
    /**
     * 设备名
     */
    private String deviceName = "";
    /**
     * 设备icon
     */
    private String deviceIcon = "";
    /**
     * 操作列表
     */
    List<SingleOperation> singleOperations = new ArrayList<>();
    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceIcon() {
        return deviceIcon;
    }

    public void setDeviceIcon(String deviceIcon) {
        this.deviceIcon = deviceIcon;
    }

    public List<SingleOperation> getSingleOperations() {
        return singleOperations;
    }

    public void setSingleOperations(List<SingleOperation> singleOperations) {
        this.singleOperations = singleOperations;
    }

}
