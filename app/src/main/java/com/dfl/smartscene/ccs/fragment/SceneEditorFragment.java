package com.dfl.smartscene.ccs.fragment;

import android.content.res.Configuration;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.method.LinkMovementMethod;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.Switch;
import android.widget.TextView;

import androidx.annotation.DimenRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.widget.NestedScrollView;
import androidx.fragment.app.DialogFragment;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.Navigation;
import androidx.navigation.fragment.NavHostFragment;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.RecyclerView;

import com.dfl.dflcommonlibs.dialog.DefaultDoubleDialogListener;
import com.dfl.dflcommonlibs.dialog.DialogUtil;
import com.dfl.dflcommonlibs.dialog.LibDialog;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseAdapter;
import com.dfl.smartscene.ccs.base.ItemTouchHelperCallBack;
import com.dfl.smartscene.ccs.base.MyClickableSpan;
import com.dfl.smartscene.ccs.bean.DeviceOperation;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.constant.ConstantViewValue;
import com.dfl.smartscene.ccs.factory.OperationBaseViewFactory;
import com.dfl.smartscene.ccs.factory.SpecialBeanFactory;
import com.dfl.smartscene.ccs.model.SceneDataModel;
import com.dfl.smartscene.ccs.model.manager.CarConfigManager;
import com.dfl.smartscene.ccs.util.CloneUtil;
import com.dfl.smartscene.ccs.util.ImageUtil;
import com.dfl.smartscene.ccs.util.ListUtil;
import com.dfl.smartscene.ccs.util.RecyclerViewUtil;
import com.dfl.smartscene.ccs.util.ToastManager;
import com.dfl.smartscene.ccs.view.ViewControlManager;
import com.dfl.smartscene.ccs.view.adapter.SceneEditorActionAdapter;
import com.dfl.smartscene.ccs.view.adapter.SceneEditorAdapter;
import com.dfl.smartscene.ccs.view.adapter.SceneEditorStateAdapter;
import com.dfl.smartscene.ccs.view.adapter.SceneEditorTimeStateAdapter;
import com.dfl.smartscene.ccs.view.adapter.SceneEditorVrAdapter;
import com.dfl.smartscene.ccs.view.base.SimpleBaseFragment;
import com.dfl.smartscene.ccs.view.commonview.OverTopContentView;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseDialogFragment;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationMenuDialogFragment;
import com.dfl.smartscene.ccs.view.fragment.SceneChangeUtil;
import com.dfl.smartscene.ccs.view.fragment.SceneNameTextLayout;
import com.dfl.smartscene.ccs.view.weight.CustomSwitch;
import com.dfl.smartscene.ccs.viewmodel.MenuAiotViewModel;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Zezheng
 * @date ：2022/8/29 10:48
 * @description ：场景编辑页
 */
public class SceneEditorFragment extends SimpleBaseFragment implements OperationBaseDialogFragment.OperationChangeListerner {
    private static final String TAG = SceneEditorFragment.class.getSimpleName();
    private SceneEditorConditionHolder mSceneEditorConditionHolder;//触发操作卡片
    private ImageView mImageViewSceneIcon;//场景图标
    private Switch mSwitchCompatAutoMeet;//是否自动触发的view
    private View mAdvancedSwitchOpen, mAdvancedSwitchClose,mAutoSwitchOpen,mAutoSwitchClose;//用来触发可见即可说
    private CustomSwitch mSwitchCompatAutoNotify;//自动触发前是否需要询问
    private TextView mTextViewTitle;//场景名称
    private SceneBean mSceneBean;//当前展示的场景
    private SceneBean mSceneBeanLibrary;//场景广场所对应的场景信息
    private TextView mTextViewButtonCoplete;//完成 按钮
    private TextView mTextViewClear;//清楚全部按钮
    private TextView mTextViewRestore;//恢复预设按钮
    private TextView mTextViewAiotNotice;//未购买设备提示语
    private View mViewAiotNotice;//未购买设备提示语-可见即可说触发

    RecyclerView recyclerViewState;//且满足全部条件列表
    private ViewGroup.LayoutParams params;
    private ConstraintLayout mConstraintLayoutConditionState;//触发和状态条件的layout，可被收起
    private String categoryId;//场景所属分类id
    private String categoryName;//场景所属分类名称
    private SceneEditorAdapter mAdapterTimeState;//包括日期和时间的状态条件
    private SceneEditorAdapter mAdapterState;//状态条件
    private SceneEditorActionAdapter mAdapterAction;//执行动作
    private SceneEditorAdapter mAdapterVr;

    private SettingOperation conditionOperation;//触发条件数据
    private SettingOperation dayOperation;//日期状态条件数据
    private SettingOperation timeOperation;//时间状态条件数据
    List<SettingOperation> unEditActionOperation = new ArrayList<>();//不可见的执行操作

    private VrOperationTextLayout mVrOperationTextLayout;
    private EditTextLayout mTitleEditTextLayout;
    private SceneNameTextLayout mSceneNameTextLayout;
    private String editType;//编辑类型
    private int editPos;
    private MenuAiotViewModel mMenuAiotViewModel;
    private static final @DimenRes
    int COLUMN_SPACE = ConstantViewValue.COMMON_SHOW_SPACE;
    private static final @DimenRes
    int LINE_SPACE = R.dimen.y_px_20;

    public static final int OPERATION_TYPE_VR = 1;
    public static final int OPERATION_TYPE_CONDITION = 2;
    public static final int OPERATION_TYPE_TIME_STATE = 3;
    public static final int OPERATION_TYPE_STATE = 4;
    public static final int OPERATION_TYPE_ACTION = 5;


    private static final String TAG_MENU = "TAG_OPE_MENU";
//    OperationMenuDialogFragment operationMenuDialogFragment;

    private SceneChangeUtil mSceneChangeUtil;
    private final MutableLiveData<Boolean> isSceneEdited = new MutableLiveData<>();

    public MutableLiveData<Boolean> getIsSceneEdited() {
        return isSceneEdited;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_scene_editor, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mSceneChangeUtil = new SceneChangeUtil(extructSceneBean());
    }

    @Override
    protected void init() {
        if (getArguments() == null) {
            categoryId = "";
            mSceneBean = new SceneBean();
        } else {
            mSceneBean = (SceneBean) CloneUtil.cloneObject(getArguments().getSerializable(ConstantViewValue.FRAGMENT_KEY_SCENE_EDITOR_SCENE_BEAN));
            if (mSceneBean == null) {
                mSceneBean = new SceneBean();
            } else {
                if(ConstantModelValue.CATEGORY_ID_CUSTOM.equals(mSceneBean.getSceneCategoryId())){
                    mSceneBeanLibrary = SceneDataModel.getInstance().findUserSceneBeanById(mSceneBean.getSceneId());
                }else {
                    mSceneBeanLibrary = SceneDataModel.getInstance().findLibrarySceneBeanById(mSceneBean.getSceneId());
                }
            }
            categoryId = getArguments().getString(ConstantViewValue.FRAGMENT_KEY_SCENE_EDITOR_CATEGORY_ID);
            editType = getArguments().getString(ConstantViewValue.FRAGMENT_KEY_SCENE_EDITOR_TYPE);
            editPos = getArguments().getInt(ConstantViewValue.FRAGMENT_KEY_SCENE_EDITOR_POS);
            categoryName = getArguments().getString(ConstantViewValue.FRAGMENT_KEY_SCENE_EDITOR_CATEGORY_TITLE);
        }
        mMenuAiotViewModel = new ViewModelProvider(this).get(MenuAiotViewModel.class);

    }

    @Override
    protected void initView(View view) {
        OverTopContentView overTopContentView = new OverTopContentView();
        overTopContentView.initView(view);
        overTopContentView.setTitle(Objects.nonNull(categoryName) ? categoryName : "创建场景");
        overTopContentView.getBackButton().setOnClickListener(v -> {
            if (!checkSceneChanged()) {
                Navigation.findNavController(v).navigateUp();
                return;
            }
            DialogUtil.showDefaultDoubleDialog(new DefaultDoubleDialogListener() {
                @Override
                public View.OnClickListener getPositiveButtonListener(LibDialog libDialog) {
                    return v1 -> {
                        Navigation.findNavController(v).navigateUp();
                        libDialog.dismiss();
                    };
                }

                @Override
                public String getPositiveButtonName() {
                    return getString(R.string.string_edit_scene_fragment_back_notice_confirm);
                }

                @Override
                public String getDialogTitle() {
                    if (editType.equals(ConstantViewValue.SCENE_EDITOR_TYPE_SCENE_ADD)) {
                        return getString(R.string.string_create_scene_fragment_back_notice_title);
                    } else {
                        return getString(R.string.string_edit_scene_fragment_back_notice_title);
                    }
                }

                @Override
                public String getDialogMessage() {
                    return getString(R.string.string_edit_scene_fragment_back_notice);
                }
            });
        });
        mVrOperationTextLayout = view.findViewById(R.id.vr_operation_text_layout);
        mSceneNameTextLayout = view.findViewById(R.id.scene_name_text_layout);
        mTitleEditTextLayout = view.findViewById(R.id.scene_editor_title_text_layout);
        mTextViewTitle = view.findViewById(R.id.textview_scene_editor_name);
        mSwitchCompatAutoMeet = view.findViewById(R.id.switch_scene_editor_automeet);
        mAdvancedSwitchOpen = view.findViewById(R.id.view_open_switch);
        mAdvancedSwitchClose = view.findViewById(R.id.view_close_switch);
        mAutoSwitchOpen = view.findViewById(R.id.view_open_auto_switch);
        mAutoSwitchClose = view.findViewById(R.id.view_close_auto_switch);
        mTextViewButtonCoplete = view.findViewById(R.id.textview_scene_editor_complete);
        mConstraintLayoutConditionState = view.findViewById(R.id.layout_scene_editor_condition_state);
        mTextViewClear = view.findViewById(R.id.textview_scene_editor_clear);
        mTextViewRestore = view.findViewById(R.id.textview_scene_edoitor_restore);
        mTextViewAiotNotice = view.findViewById(R.id.textview_scene_editor_buy_notice);
        mViewAiotNotice = view.findViewById(R.id.view_buy_notice);
        mSwitchCompatAutoNotify = view.findViewById(R.id.switch_scene_editor_auto_notify);
        mImageViewSceneIcon = view.findViewById(R.id.imageview_scene_editor_icon);
        initVrRecyclerView(view);
        initConditionHolder(view);
        initTimeStateRecyclerView(view);
        initStateRecyclerView(view);
        initSwitchAutoNotify();
        initActionRecyclerView(view);
        initAutoMeetSwitch();
        initSwitchContent();
        mMenuAiotViewModel.requestSceneUnbuyDevices(mSceneBean.getActionOperations());


        mTextViewTitle.setOnClickListener(v -> {
            if (categoryId.equals(ConstantModelValue.CATEGORY_ID_CUSTOM)) {
                mSceneNameTextLayout.show(mTextViewTitle.getText().toString());
                mSceneNameTextLayout.setConfirmClickListener(mConfirmClickListener);
            }
        });
        //完成按钮点击监听，提取场景后根据编辑类型来判断是修改场景还是添加场景
        mTextViewButtonCoplete.setOnClickListener(v -> {
            if(!mTextViewButtonCoplete.isSelected()){
                if(mAdapterAction.getItemCount() <= 1){
                    ToastManager.showToast(getString(R.string.string_notice_lack_execution_operation));
                }else {
                    ToastManager.showToast(getString(R.string.string_notice_lack_trigger_condition));
                }
                return;
            }
            if (editType.equals(ConstantViewValue.SCENE_EDITOR_TYPE_SCENE_ADD)) {
                SceneDataModel.getInstance().addUserScene(categoryId, extructSceneBean());
            } else {
                SceneDataModel.getInstance().replaceUserScene(editPos,categoryId, extructSceneBean());
            }

            if (editType.equals(ConstantViewValue.SCENE_EDITOR_TYPE_SCENE_ADD)) {
                ToastManager.showToast(getString(R.string.string_editor_fragment_add_notice));
            }
            NavHostFragment.findNavController(SceneEditorFragment.this).navigateUp();
        });

        //如果该场景不具备广场预设数据，展示清空操作按钮
        if (mSceneBeanLibrary == null) {
            mTextViewRestore.setVisibility(View.GONE);
            mTextViewClear.setVisibility(View.VISIBLE);
            mTextViewClear.setOnClickListener(v -> {
                DialogUtil.showDefaultDoubleDialog(new DefaultDoubleDialogListener() {
                    @Override
                    public View.OnClickListener getPositiveButtonListener(LibDialog libDialog) {
                        return v -> {
                            SceneBean emptyBean = new SceneBean();
                            emptyBean.setSceneId(mSceneBean.getSceneId());
                            emptyBean.setSceneCategoryId(mSceneBean.getSceneCategoryId());
                            emptyBean.setSceneDesc(mSceneBean.getSceneDesc());
                            mSceneBean = emptyBean;
                            conditionOperation = null;
                            mSwitchCompatAutoMeet.setChecked(false);
                            initData();
                            libDialog.dismiss();
                            isSceneEdited.setValue(checkSceneChanged());
                        };
                    }

                    @Override
                    public String getDialogMessage() {
                        return "是否重置已编辑的内容";
                    }

                    @Override
                    public String getPositiveButtonName() {
                        return "重置";
                    }
                });
            });
        } else {
            //如果场景具有广场场景预设，展示恢复预设按钮
            mTextViewRestore.setVisibility(View.VISIBLE);
            mTextViewClear.setVisibility(View.GONE);
            mTextViewRestore.setOnClickListener(v -> {
                DialogUtil.showDefaultDoubleDialog(new DefaultDoubleDialogListener() {
                    @Override
                    public View.OnClickListener getPositiveButtonListener(LibDialog libDialog) {
                        return v -> {
                            mSceneBean = (SceneBean) CloneUtil.cloneObject(mSceneBeanLibrary);
                            initData();
                            initSwitchAutoNotify();
                            initAutoMeetSwitch();
                            libDialog.dismiss();
                            isSceneEdited.setValue(checkSceneChanged());
                        };
                    }

                    @Override
                    public String getDialogMessage() {
                        return "是否重置已编辑的内容";
                    }

                    @Override
                    public String getPositiveButtonName() {
                        return "重置";
                    }
                });
            });
        }
        getIsSceneEdited().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                LogUtil.d(TAG,"场景是否有编辑 " + aBoolean);
                mTextViewRestore.setEnabled(aBoolean);
                mTextViewClear.setEnabled(aBoolean);
                if (aBoolean){
                    mTextViewRestore.setTextColor(getResources().getColor(R.color.color_text_indicator));
                    mTextViewClear.setTextColor(getResources().getColor(R.color.color_text_indicator));
                }else {
                    mTextViewRestore.setTextColor(getResources().getColor(R.color.color_text_indicator_alpha));
                    mTextViewClear.setTextColor(getResources().getColor(R.color.color_text_indicator_alpha));
                }
            }
        });
        ((NestedScrollView)view.findViewById(R.id.layout_content)).setOnScrollChangeListener(new View.OnScrollChangeListener() {
            @Override
            public void onScrollChange(View v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
//                LogUtil.d(TAG, "====onScrollChange scrollY = " + scrollY + ",oldScrollY:" + oldScrollY);
            }
        });

    }

    /**
     * 设置switch按钮 可见即可说标签
     */
    private void initSwitchContent() {
        mAdvancedSwitchOpen.setOnClickListener(v -> {mSwitchCompatAutoMeet.setChecked(true);});
        mAdvancedSwitchClose.setOnClickListener(v -> {mSwitchCompatAutoMeet.setChecked(false);});
        mAutoSwitchOpen.setOnClickListener(v -> {mSwitchCompatAutoNotify.setChecked(true);});
        mAutoSwitchClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(mSwitchCompatAutoNotify.isChecked()){
                    mSwitchCompatAutoNotify.performClick();
                }
            }
        });
    }


    /**
     * 初始化自动触发按钮视图
     */
    private void initAutoMeetSwitch() {
        mSwitchCompatAutoMeet.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    mConstraintLayoutConditionState.setVisibility(View.VISIBLE);
                } else {
                    mConstraintLayoutConditionState.setVisibility(View.GONE);
                }
                refreshCompleteButtonAble();
                isSceneEdited.setValue(checkSceneChanged());
            }
        });

        if (mSceneBean.isAutoEnable()) {
            mConstraintLayoutConditionState.setVisibility(View.VISIBLE);
            mSwitchCompatAutoMeet.setChecked(true);
        } else {
            mConstraintLayoutConditionState.setVisibility(View.GONE);
            mSwitchCompatAutoMeet.setChecked(false);
        }
    }

    /**
     * 初始化出触发条件视图，如果操作卡片是具体的操作，点击打开修改弹窗，如果是添加触发条件卡片，点击打开操作菜单弹窗
     *
     * @param view
     */
    private void initConditionHolder(View view) {
        mSceneEditorConditionHolder = new SceneEditorConditionHolder(view.findViewById(R.id.item_scene_editor_condition));
        mSceneEditorConditionHolder.itemView.setOnClickListener(v -> {
            if (conditionOperation.getOperationId().equals(ConstantModelValue.OPERATION_ID_ADD)) {
                List<DeviceOperation> datas = CarConfigManager.getInstance().getLocalConditionOperations();

                openMenuDialog(OPERATION_TYPE_CONDITION, datas, getUnableOperationIdList(datas, mAdapterState.getDataList()));
            } else {
                openModifyDialog(OPERATION_TYPE_CONDITION, 0, conditionOperation);
            }
        });
    }

    /**
     * 初始化执行动作视图
     *
     * @param view
     */
    private void initActionRecyclerView(View view) {
        RecyclerView recyclerViewAction = view.findViewById(R.id.recyclerview_scene_editor_action);
        RecyclerViewUtil.setGridRecycleView(recyclerViewAction, RecyclerView.VERTICAL, 4, R.dimen.x_px_24, R.dimen.y_px_24);
        mAdapterAction = new SceneEditorActionAdapter();
        recyclerViewAction.setAdapter(mAdapterAction);
        recyclerViewAction.setHasFixedSize(true);
        //卡片点击监听，区分开是修改场景还是添加场景
        mAdapterAction.setOnItemClickListener(new BaseAdapter.OnItemClickListener<SettingOperation>() {
            @Override
            public void onItemClick(View view, int viewType, SettingOperation settingOperation, int position) {
                if (mAdapterAction.isEditMode()) {
                    return;
                }

                if (settingOperation.getOperationId().equals(ConstantModelValue.OPERATION_ID_ADD)) {
                    openMenuDialog(OPERATION_TYPE_ACTION, CarConfigManager.getInstance().getLocalActionOperations(), new ArrayList<>());
                } else {
                    openModifyDialog(OPERATION_TYPE_ACTION, position, settingOperation);
                }
            }
        });

        //删除场景按钮监听
        mAdapterAction.setOnButtonClickListener(new BaseAdapter.OnButtonClickListener<SettingOperation>() {
            @Override
            public void onItemClick(View view, int viewType, SettingOperation settingOperation, int position) {
                if (mAdapterAction.isEditMode()) {
                    return;
                }
                if (view.getId() == R.id.imagebutton_item_scene_editor_delete) {
                    mAdapterAction.removeData(position);
                    checkDeletePLimit(settingOperation);
//                    resetRecyclerViewHeight(recyclerViewAction);
                }
                isSceneEdited.setValue(checkSceneChanged());
            }
        });
        //添加拖拽换序功能
        mActionItemTouchHelperCallBack = new ItemTouchHelperCallBack(mAdapterAction);
        ItemTouchHelper itemTouchHelper = new ItemTouchHelper(mActionItemTouchHelperCallBack);
        itemTouchHelper.attachToRecyclerView(recyclerViewAction);
        mAdapterAction.setOnItemCountChangeListener(new SceneEditorActionAdapter.OnItemCountChangeListener() {
            @Override
            public void onItemCountChange(int count) {
                refreshCompleteButtonAble();
//                if(count <= 1){
//                    mTextViewButtonCoplete.setEnabled(false);
//                    mTextViewButtonCoplete.setTextColor(getResources().getColor(R.color.color_text_tertiary_label));
//                }else {
//                    mTextViewButtonCoplete.setEnabled(true);
//                    mTextViewButtonCoplete.setTextColor(getResources().getColor(R.color.color_text_secondary_label));
//                }
            }
        });
        mAdapterAction.setItemLongClickListener(new BaseAdapter.OnItemLongClickListener<SettingOperation>() {
            @Override
            public void onItemLongClick(View view, int viewType, SettingOperation settingOperation, int position) {
                //需求不需要编辑态了
//                if(!mAdapterAction.isEditMode()){
//                    changeActionEditMode(true);
//                }
            }
        });
    }

    /**
     * 根据item数量动态显示recyclerview的高度，执行操作
     */
    private void resetRecyclerViewHeight(RecyclerView recyclerView) {
        ViewGroup.LayoutParams params = recyclerView.getLayoutParams();
        if (mAdapterAction.getItemCount() % 4 == 0){//4、8、12
            params.height = (mAdapterAction.getItemCount() / 4 ) * 180;
        }else {//1、2、3、5、6、7
            params.height = (mAdapterAction.getItemCount() / 4 + 1 ) * 180;
        }
        recyclerView.setLayoutParams(params);
    }

    private ItemTouchHelperCallBack mActionItemTouchHelperCallBack;

    public void changeActionEditMode(boolean mode) {
        mAdapterAction.setEditMode(mode);
        if (mode) {
            mActionItemTouchHelperCallBack.setDragEnabled(true);
        } else {
            mActionItemTouchHelperCallBack.setDragEnabled(false);
        }

    }


    /**
     * 初始化时间状态条件视图
     *
     * @param view
     */
    private void initTimeStateRecyclerView(View view) {
        RecyclerView recyclerViewTimeState = view.findViewById(R.id.recyclerview_scene_editor_time_state);
        RecyclerViewUtil.setGridRecycleView(recyclerViewTimeState, RecyclerView.VERTICAL, 2, COLUMN_SPACE, LINE_SPACE);
        mAdapterTimeState = new SceneEditorTimeStateAdapter();
        mAdapterTimeState.setSceneType(ConstantViewValue.SCENE_EDITOR_TYPE_SCENE_ADD);
        recyclerViewTimeState.setAdapter(mAdapterTimeState);
        mAdapterTimeState.setOnItemClickListener(new BaseAdapter.OnItemClickListener<SettingOperation>() {
            @Override
            public void onItemClick(View view, int viewType, SettingOperation settingOperation, int position) {
                openModifyDialog(OPERATION_TYPE_TIME_STATE, position, settingOperation);
            }
        });

    }

    private void initVrRecyclerView(View view) {
        RecyclerView recyclerViewVr = view.findViewById(R.id.recyclerview_scene_editor_vr);
        RecyclerViewUtil.setGridRecycleView(recyclerViewVr, RecyclerView.VERTICAL, 2, COLUMN_SPACE, LINE_SPACE);
        mAdapterVr = new SceneEditorVrAdapter();
        mAdapterVr.setSceneType(ConstantViewValue.SCENE_EDITOR_TYPE_SCENE_ADD);
        recyclerViewVr.setAdapter(mAdapterVr);

        //卡片点击监听，区分开是修改操作还是添加操作
        mAdapterVr.setOnItemClickListener(new BaseAdapter.OnItemClickListener<SettingOperation>() {
            @Override
            public void onItemClick(View view, int viewType, SettingOperation settingOperation, int position) {
                //拿到当前页面所有的语音指令，需要去重
                List<String> mList = null;
                if (mAdapterVr.getDataList() != null){
                    mList = new ArrayList<>();
                    for (SettingOperation item : mAdapterVr.getDataList()){
                        mList.add(item.getDesc());
                    }
                }
                if (settingOperation.getOperationId().equals(ConstantModelValue.OPERATION_ID_ADD)) {
                    if (null == mList) {
                        mVrOperationTextLayout.show();
                    }else {
                        mVrOperationTextLayout.showList(mList);
                    }
                    mVrOperationTextLayout.setOperationChangeListerner(SceneEditorFragment.this);
                } else {
                    if (null == mList) {
                        mVrOperationTextLayout.show(position, settingOperation);
                    }else {
                        mVrOperationTextLayout.show(position, settingOperation, mList);
                    }
                    mVrOperationTextLayout.setOperationChangeListerner(SceneEditorFragment.this);
                }

            }
        });

        mAdapterVr.setOnButtonClickListener(new BaseAdapter.OnButtonClickListener<SettingOperation>() {
            @Override
            public void onItemClick(View view, int viewType, SettingOperation settingOperation, int position) {
                if (view.getId() == R.id.imagebutton_item_scene_editor_delete) {
                    mAdapterVr.removeData(position);
                    isSceneEdited.setValue(checkSceneChanged());
                }
            }
        });


    }

    /**
     * 初始化状态条件视图
     *
     * @param view
     */
    private void initStateRecyclerView(View view) {
        recyclerViewState = view.findViewById(R.id.recyclerview_scene_editor_state);
        RecyclerViewUtil.setGridRecycleView(recyclerViewState, RecyclerView.VERTICAL, 2, COLUMN_SPACE, LINE_SPACE);
        mAdapterState = new SceneEditorStateAdapter();
        mAdapterState.setSceneType(ConstantViewValue.SCENE_EDITOR_TYPE_SCENE_ADD);
        recyclerViewState.setAdapter(mAdapterState);

        //卡片点击监听，区分开是修改操作还是添加操作
        mAdapterState.setOnItemClickListener(new BaseAdapter.OnItemClickListener<SettingOperation>() {
            @Override
            public void onItemClick(View view, int viewType, SettingOperation settingOperation, int position) {
                if(settingOperation instanceof AutoPStateOperation) {
                    return;
                }
                if (settingOperation.getOperationId().equals(ConstantModelValue.OPERATION_ID_ADD)) {
                    List<DeviceOperation> datas = CarConfigManager.getInstance().getLocalStateOperations();
                    List<SettingOperation> users = new ArrayList<>(mAdapterState.getDataList());
                    users.add(conditionOperation);
                    openMenuDialog(OPERATION_TYPE_STATE, datas, getUnableOperationIdList(datas, users));
                } else {
                    openModifyDialog(OPERATION_TYPE_STATE, position, settingOperation);
                }

            }
        });

        //删除按钮点击监听，如果删除的是P挡条件，还需判断是否要进行连带删除
        mAdapterState.setOnButtonClickListener(new BaseAdapter.OnButtonClickListener<SettingOperation>() {
            @Override
            public void onItemClick(View view, int viewType, SettingOperation settingOperation, int position) {
//                if (view.getId() == R.id.imagebutton_item_scene_editor_delete) {
//                    if (settingOperation.getOperationId().equals(ConstantModelValue.OPERATION_ID_STATE_DRIVE_GEAR) && "0".equals(settingOperation.getListArgs().get(0))) {
//                        showPGearStateChangedDialog(() -> {
//                            mAdapterState.removeData(position);
////                            mAdapterState.getDataList().remove(position);
////                            mAdapterState.setDataList(mAdapterState.getDataList());
//                        });
//                    } else {
                        mAdapterState.removeData(position);
//                }
                resetRecyclerViewHeight();
//                        mAdapterState.getDataList().remove(position);
//                        mAdapterState.setDataList(mAdapterState.getDataList());
//                    }
//                }
                isSceneEdited.setValue(checkSceneChanged());
            }
        });

    }

    /**
     * 初始化触发是否提醒的按钮
     */
    public void initSwitchAutoNotify() {
        mSwitchCompatAutoNotify.setChecked(mSceneBean.isAutoNotify());
        mSwitchCompatAutoNotify.setEnableToggle(false);
        mSwitchCompatAutoNotify.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mSwitchCompatAutoNotify.isChecked()) {
                    DialogUtil.showDefaultDoubleDialog(new DefaultDoubleDialogListener() {
                        @Override
                        public View.OnClickListener getPositiveButtonListener(LibDialog libDialog) {
                            return v -> {
                                mSwitchCompatAutoNotify.setChecked(false);
                                libDialog.dismiss();
                                isSceneEdited.setValue(checkSceneChanged());
                            };
                        }

                        @Override
                        public String getDialogMessage() {
                            return "关闭后，在场景自动执行前将不再询问，可能存在风险";
                        }

                        @Override
                        public String getDialogTitle(){
                            return "是否关闭自动执行前询问";
                        }

                        @Override
                        public String getPositiveButtonName(){
                            return "关闭询问";
                        }

                        @Override
                        public int getDialogLayout() {
                            return R.layout.dialog_double_title_layout_2;
                        }
                    });
                } else {
                    mSwitchCompatAutoNotify.setChecked(true);
                }
                isSceneEdited.setValue(checkSceneChanged());
            }
        });
    }

    /**
     * 展示P挡条件删除弹窗
     *
     * @param confirmRun 点击确定后要执行什么操作
     */
    private void showPGearStateChangedDialog(Runnable confirmRun) {
        DialogUtil.showDefaultDoubleDialog(new DefaultDoubleDialogListener() {
            @Override
            public View.OnClickListener getPositiveButtonListener(LibDialog libDialog) {
                return v -> {
                    //触发条件判断，如果P挡关联，则删除
                    if (CarConfigManager.getInstance().checkOperationPLimit(conditionOperation.getOperationId())) {
                        mSceneEditorConditionHolder.onClickDeleteButton(0);
                    }
                    //执行动作判断，如果P挡关联，则删除
                    mAdapterAction.getDataList().forEach(actionOperation -> {
                        if (CarConfigManager.getInstance().checkOperationPLimit(actionOperation.getOperationId())) {
                            mAdapterAction.getDataList().remove(actionOperation);
                        }
                    });
                    mAdapterAction.setDataList(mAdapterAction.getDataList());
                    isSceneEdited.setValue(checkSceneChanged());
                    confirmRun.run();
                    libDialog.dismiss();
                };
            }

            @Override
            public String getDialogMessage() {
                return "当前触发条件或操作需求需在车辆P挡时使用，删除该条件会同步删除，是否继续？";
            }

        });
    }

    /**
     * 打开修改操作弹窗
     *
     * @param operationType
     * @param pos
     * @param settingOperation
     */
    private void openModifyDialog(int operationType, int pos, SettingOperation settingOperation) {
        //TODO 确认产品仕样
        if (settingOperation.getDeviceId().equals(ConstantModelValue.DEVICE_ID_AIOT)) {
            mMenuAiotViewModel.requestSingleOperationBySettingOpe(pos,settingOperation);
            return;
        }
        //需要深度拷贝一次操作数据，以免对添加模式的场景发生污染
        SingleOperation singleOperationById = CarConfigManager.getInstance().findSingleOperationById(settingOperation.getOperationId());
        if (Objects.nonNull(singleOperationById)) {
            SingleOperation singleOperation = (SingleOperation) CloneUtil.cloneObject(singleOperationById);
            OperationBaseDialogFragment operationBaseDialogFragment = OperationBaseViewFactory.creatOperationContainer(operationType, pos, settingOperation, singleOperation);
            operationBaseDialogFragment.setOperationChangeListerner(this);
            operationBaseDialogFragment.show(getChildFragmentManager(), null);
        }
    }

    /**
     * 打开操作菜单弹窗
     *
     * @param operationType
     * @param operations
     */
    private void openMenuDialog(int operationType, List<DeviceOperation> operations, List<String> unableIds) {
//        if (operationMenuDialogFragment == null) {
//            operationMenuDialogFragment = new OperationMenuDialogFragment(operationType, operations, unableIds);
//            operationMenuDialogFragment.setOnDismissListener(() -> operationMenuDialogFragment = null);
//        } else {
//            operationMenuDialogFragment.setData(operationType, operations, unableIds);
//        }
//        operationMenuDialogFragment.setOperationChangeListerner(this);
//        operationMenuDialogFragment.show(getChildFragmentManager(), null);

        OperationMenuDialogFragment operationMenuDialogFragment = new OperationMenuDialogFragment(operationType, operations, unableIds);
        operationMenuDialogFragment.setOperationChangeListerner(this);
        operationMenuDialogFragment.show(getChildFragmentManager(), TAG_MENU);


    }


    /**
     * 获取菜单中因为冲突而无法点击的操作id
     *
     * @param menuOperations 菜单操作
     * @param userOperations 用户已选的操作
     */
    private List<String> getUnableOperationIdList(List<DeviceOperation> menuOperations, List<SettingOperation> userOperations) {
        List<String> conflictOID = new ArrayList<>();
        for (DeviceOperation deviceOperation : menuOperations) {
            conflictOID.addAll(getUnableOperationIdList2(deviceOperation.getSingleOperations(), userOperations));
        }
        return conflictOID;
    }


    /**
     * 获取菜单中因为冲突而无法点击的操作id
     *
     * @param menuOperations 菜单操作
     * @param userOperations 用户已选的操作
     */
    private List<String> getUnableOperationIdList2(List<SingleOperation> menuOperations, List<SettingOperation> userOperations) {
        List<String> conflictOID = new ArrayList<>();
        for (SettingOperation userOp : userOperations) {
            SingleOperation userSOp = CarConfigManager.getInstance().findSingleOperationById(userOp.getOperationId());
            if (userSOp != null) {
                for (SingleOperation menuOp : menuOperations) {
                    if (checkOperationsConflict(menuOp, userSOp)) {
                        conflictOID.add(menuOp.getOperationId());
                    }
                }
            }
        }
        return conflictOID;
    }

    /**
     * 检查新操作是否和老操作冲突
     *
     * @param newSet 菜单操作
     * @param oldSet 用户操作
     * @return
     */
    private boolean checkOperationsConflict(SingleOperation newSet, SingleOperation oldSet) {
        SingleOperation newOPeration = newSet;
        SingleOperation oldOperation = oldSet;

        if (oldOperation == null) {
            return false;
        }

        if (newOPeration.getOperationId().equals(oldOperation.getOperationId())) {
            return true;
        }
        List<String> newConfIds = newOPeration.getListArg(ConstantModelValue.VIEW_CONFLICT_ID_LIST);
        List<String> newConfArgs = newOPeration.getListArg(ConstantModelValue.VIEW_CONFLICT_ARG_LIST);
        List<String> oldConfIds = oldOperation.getListArg(ConstantModelValue.VIEW_CONFLICT_ID_LIST);
        List<String> oldConfArgs = oldOperation.getListArg(ConstantModelValue.VIEW_CONFLICT_ARG_LIST);
        //先检查新操作是否在老操作的冲突id里
        if (!ListUtil.isEmpty(newConfIds) && ListUtil.isSameSize(newConfIds, newConfArgs)) {
            for (int i = 0; i < newConfIds.size(); i++) {
                if (newConfIds.get(i).equals(oldOperation.getOperationId())) {
                    if (newConfArgs.get(i).equals("all")) {
                        return true;
                    }
                }
            }
        }

        //再检查老操作是否在新操作的冲突id里
        if (!ListUtil.isEmpty(oldConfIds) && ListUtil.isSameSize(oldConfIds, oldConfArgs)) {
            for (int i = 0; i < oldConfIds.size(); i++) {
                if (oldConfIds.get(i).equals(newOPeration.getOperationId())) {
                    if (oldConfArgs.get(i).equals("all")) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    @Override
    protected void initObserver() {
        mMenuAiotViewModel.getAiotModifyOperation().observe(getViewLifecycleOwner(), new Observer<MenuAiotViewModel.PosSingleOperation>() {
            @Override
            public void onChanged(MenuAiotViewModel.PosSingleOperation posSingleOperation) {
                if (posSingleOperation == null){
                    String toastText = "您还没有此设备" + ",前往购买";
                    ToastManager.showSpanToast(toastText, "前往购买", new MyClickableSpan() {
                        @Override
                        public void onClick(@NonNull View widget) {
                            ViewControlManager.openAiotStorePage();
                        }
                    },getView());

                    return;
                }
                OperationBaseDialogFragment operationBaseDialogFragment = OperationBaseViewFactory.creatOperationContainer(OPERATION_TYPE_ACTION, posSingleOperation.getPos(), posSingleOperation.getSettingOperation(), posSingleOperation.getSingleOperation());
                operationBaseDialogFragment.setOperationChangeListerner(SceneEditorFragment.this);
                operationBaseDialogFragment.show(getChildFragmentManager(), null);

            }
        });

        mMenuAiotViewModel.getAiotDesc().observe(getViewLifecycleOwner(), new Observer<String>() {
            @Override
            public void onChanged(String s) {
                if(s != null && !"".equals(s)){
                    SpannableString spannableString = ToastManager.getSpanString("您还没有" + s + "点击前往购买,或打开手机端智联日产APP选购", "前往购买", new MyClickableSpan() {
                        @Override
                        public void onClick(@NonNull View widget) {
                            ViewControlManager.openAiotStorePage();
                        }
                    });
                    mTextViewAiotNotice.setVisibility(View.VISIBLE);
                    mViewAiotNotice.setVisibility(View.VISIBLE);
                    mViewAiotNotice.setOnClickListener(v -> {ViewControlManager.openAiotStorePage();});
                    mTextViewAiotNotice.setText(spannableString);
                    mTextViewAiotNotice.setMovementMethod(LinkMovementMethod.getInstance());
                }
            }
        });
    }

    @Override
    protected void initData() {
        if (mSceneBean.getSceneName() != null && !mSceneBean.getSceneName().equals("")) {
            mTextViewTitle.setText(mSceneBean.getSceneName());
        } else {
            mTextViewTitle.setText(SpecialBeanFactory.productSceneName());
        }

        ImageUtil.loadImageUrlNoCorner(mImageViewSceneIcon,"",R.drawable.icon_edit_scene_64x64);

        List<SettingOperation> vrOperations = new ArrayList<>();

        //初始化展示触发条件，筛选出语音触发条件
        for (SettingOperation settingOperation : mSceneBean.getConditionOperations()) {
            if (settingOperation.getOperationId().equals(ConstantModelValue.OPERATION_ID_CONDITION_VR)) {
                vrOperations.add(settingOperation);
            } else {
                conditionOperation = settingOperation;
            }
        }
        mAdapterVr.setDataList(vrOperations);

        List<String> mList = null;
        if (mAdapterVr.getDataList() != null){
            mList = new ArrayList<>();
            for (SettingOperation item : mAdapterVr.getDataList()){
                mList.add(item.getDesc());
            }
        }
        if(mList!=null){
            mVrOperationTextLayout.setOriginalWords(mList);
        }
        //如果没有触发条件，添加一个“添加”卡片
        if (conditionOperation == null) {
            conditionOperation = SpecialBeanFactory.productAddConditionOperation();
        }
        mSceneEditorConditionHolder.setupData(conditionOperation, 0);

        List<SettingOperation> stateOperations = new ArrayList<>();
        List<SettingOperation> timeStateOperations = new ArrayList<>();
        //筛选出时间状态条件和日期状态条件
        for (SettingOperation settingOperation : mSceneBean.getStateOperations()) {
            if (settingOperation.getOperationId().equals(ConstantModelValue.OPERATION_ID_STATE_TIME_CLOCK)) {
                timeOperation = settingOperation;
            } else if (settingOperation.getOperationId().equals(ConstantModelValue.OPERATION_ID_STATE_TIME_DAY)) {
                dayOperation = settingOperation;
            } else {
                stateOperations.add(settingOperation);
            }
        }
        //如果时间状态为空，如新建自定义时，添加默认的时间和日期状态，全天+每天
        if (timeOperation == null) {
            timeOperation = SpecialBeanFactory.productTimeOperation();
        }
        if (dayOperation == null) {
            dayOperation = SpecialBeanFactory.productDayOperation();
        }
        timeStateOperations.add(dayOperation);
        timeStateOperations.add(timeOperation);
        mAdapterState.setDataList(stateOperations);
        mAdapterTimeState.setDataList(timeStateOperations);

        setActionDataList(mSceneBean.getActionOperations());
        //恢复询问开关设定
        if(null != mSwitchCompatAutoNotify){
            mSwitchCompatAutoNotify.setChecked(mSceneBean.isAutoNotify());
        }
        firstCheckAllPLimit(mSceneBean);
    }

    private void firstCheckAllPLimit(SceneBean sceneBean){
        if(sceneBean == null){
            return;
        }
        if(!ListUtil.isEmpty(sceneBean.getStateOperations())){
            for (SettingOperation settingOperation :sceneBean.getStateOperations()){
                checkAddPLimit(settingOperation);
            }
        }
        if(!ListUtil.isEmpty(sceneBean.getConditionOperations())){
            for (SettingOperation settingOperation :sceneBean.getConditionOperations()){
                checkAddPLimit(settingOperation);
            }
        }
        if(!ListUtil.isEmpty(sceneBean.getActionOperations())){
            for (SettingOperation settingOperation :sceneBean.getActionOperations()){
                checkAddPLimit(settingOperation);
            }
        }
    }

    /**
     * 向执行动作视图设置数据时需添加“添加操作”卡片
     *
     * @param operations
     */
    private void setActionDataList(List<SettingOperation> operations) {
        unEditActionOperation = new ArrayList<>();
        List<SettingOperation> editOperations = new ArrayList<>();
        for (SettingOperation settingOperation : operations) {
            if (settingOperation.getShowType() == ConstantModelValue.SCENE_OPERATION_SHOW_TYPE_NORMAL) {
                editOperations.add(settingOperation);
            } else {
                unEditActionOperation.add(settingOperation);
            }
        }
        editOperations.add(SpecialBeanFactory.productAddActionOperation());
        mAdapterAction.setDataList(editOperations);
    }

    /**
     * 从执行动作视图获取数据时，需去除“添加”数据
     *
     * @return
     */
    private List<SettingOperation> getActionDataList() {
        List<SettingOperation> settingOperations = new ArrayList<>(mAdapterAction.getDataList());
        try{
            if(settingOperations.size() > 0){
                settingOperations.remove(settingOperations.size() - 1);
            }
            settingOperations.addAll(0, unEditActionOperation);
        }catch (Exception e){
            LogUtil.e(TAG, "getActionDataList: " + e.getMessage());
        }
        return settingOperations;
    }

    /**
     * 获取当前的触发条件时，结合语音触发和其他触发
     *
     * @return
     */
    private List<SettingOperation> getConditionDataList() {
        List<SettingOperation> settingOperations = mAdapterVr.getValidData();
        if (conditionOperation != null && !ConstantModelValue.OPERATION_ID_ADD.equals(conditionOperation.getOperationId())) {
            settingOperations.add(conditionOperation);
        }
        return settingOperations;
    }

    /**
     * 获取状态条件时，结合时间触发和普通触发
     *
     * @return
     */
    private List<SettingOperation> getStateDataList() {
        List<SettingOperation> operations = new ArrayList<>(mAdapterTimeState.getValidData());
        operations.addAll(mAdapterState.getValidData());
        return operations;
    }

    /**
     * 提取当前页面中的场景
     *
     * @return
     */
    private SceneBean extructSceneBean() {
        SceneBean sceneBean = new SceneBean();
        try {
            if (editType.equals(ConstantViewValue.SCENE_EDITOR_TYPE_SCENE_ADD)) {
                sceneBean.setSceneId(SpecialBeanFactory.productSceneId());
            } else {
                sceneBean.setSceneId(mSceneBean.getSceneId());
            }
            sceneBean.setSort(mSceneBean.getSort());
            sceneBean.setSceneName(mTextViewTitle.getText().toString());
            sceneBean.setSceneCategoryId(categoryId);
            sceneBean.setActionOperations(getActionDataList());
            sceneBean.setStateOperations(getStateDataList());
            sceneBean.setConditionOperations(getConditionDataList());
            sceneBean.setAutoEnable(mSwitchCompatAutoMeet.isChecked());
            sceneBean.setAutoNotify(mSwitchCompatAutoNotify.isChecked());
            sceneBean.setEditType(mSceneBean.getEditType());
        } catch (Exception e) {
            LogUtil.d(TAG, "extructSceneBean: throw Exception" + e.getMessage());
        }
        return sceneBean;
    }

    @Override
    protected void onNetWorkChanged(Boolean netWorkStatus) {

    }

    @Override
    protected void onLoginStatusChanged(boolean loginStatus) {

    }

    @Override
    protected void onVrWakeUpChanged(boolean vrStatus) {

    }

    @Override
    protected void onPermissionChanged(boolean permissionStatus) {

    }

    /**
     * 接收从操作弹窗返回的修改操作的回调
     *
     * @param operationType
     * @param pos
     * @param settingOperation
     */
    @Override
    public void onOperationModify(int operationType, int pos, SettingOperation settingOperation) {
        checkAddPLimit(settingOperation);
        switch (operationType) {
            case OPERATION_TYPE_ACTION:
                //如果执行动作和已添加的动作发生冲突，提示用户
                String conflictResult = checkActionOperationConflict(pos, settingOperation);
                if (conflictResult == null) {
                    mAdapterAction.modifyData(pos, settingOperation);
                } else {
                    notifyConflict(conflictResult);
                }
                break;
            case OPERATION_TYPE_CONDITION:
                conditionOperation = settingOperation;
                mSceneEditorConditionHolder.setupData(conditionOperation, 0);
                break;
            case OPERATION_TYPE_STATE:
                //如果修改P挡状态条件，需提示用户是否要连带删除
//                if (settingOperation.getOperationId().equals(ConstantModelValue.OPERATION_ID_STATE_DRIVE_GEAR) && !"0".equals(settingOperation.getListArgs().get(0)) && "0".equals(mAdapterState.getItemData(pos).getListArgs().get(0))) {
//                    showPGearStateChangedDialog(() -> {
//                        mAdapterState.modifyData(pos, settingOperation);
//                    });
//                } else {
                    mAdapterState.modifyData(pos, settingOperation);
//                }
                resetRecyclerViewHeight();
                break;
            case OPERATION_TYPE_VR:
                mAdapterVr.modifyData(pos, settingOperation);
                break;
            case OPERATION_TYPE_TIME_STATE:
                mAdapterTimeState.modifyData(pos, settingOperation);
                break;
            default:
                break;
        }
        isSceneEdited.setValue(checkSceneChanged());
    }

    public void notifyConflict(String text) {
        ToastManager.showToast("操作冲突 " + text);
    }

    /**
     * 接收操作弹窗的添加操作回调
     *
     * @param operationType
     * @param settingOperation
     */
    @Override
    public void onOperationAdd(int operationType, SettingOperation settingOperation) {
        checkAddPLimit(settingOperation);
        switch (operationType) {
            case OPERATION_TYPE_ACTION:
                //需判断添加的操作和已添加的操作是否发生冲突
                String conflictResult = checkActionOperationConflict(mAdapterAction.getItemCount() - 1, settingOperation);
                if (conflictResult == null) {
                    mAdapterAction.getDataList().add(mAdapterAction.getItemCount() - 1, settingOperation);
                    mAdapterAction.setDataList(mAdapterAction.getDataList());
                } else {
                    notifyConflict(conflictResult);
                }

                break;
            case OPERATION_TYPE_STATE:
                if(null == settingOperation.getListArgs() || 0 == settingOperation.getListArgs().size()){
                    //未设置有效参数的情况，不应当添加
                    LogUtil.d(TAG, "not set valid state params");
                }else{
                    //在倒数第二个位置插入数据
                    mAdapterState.addData(settingOperation);//修改为限定3条
                }
                break;
            case OPERATION_TYPE_CONDITION:
                if(null == settingOperation.getListArgs() || 0 == settingOperation.getListArgs().size()){
                    //未设置有效参数的情况，不应当添加
                    LogUtil.d(TAG, "not set valid condition params");
                }else{
                    conditionOperation = settingOperation;
                    mSceneEditorConditionHolder.setupData(conditionOperation, 0);
                }
                break;
            case OPERATION_TYPE_VR:
                mAdapterVr.addData(settingOperation);
                break;
            default:
                break;
        }
        isSceneEdited.setValue(checkSceneChanged());
        DialogFragment fragmentByTag = (DialogFragment) getChildFragmentManager().findFragmentByTag(TAG_MENU);
        if(fragmentByTag != null){
            fragmentByTag.dismiss();
        }
//        if (operationMenuDialogFragment != null) {
//            operationMenuDialogFragment.dismiss();
//        }
        isSceneEdited.setValue(checkSceneChanged());
    }

    /**
     * 根据item数量动态显示recyclerview的高度，且全部满足状态
     */
    private void resetRecyclerViewHeight() {
        /*params = recyclerViewState.getLayoutParams();
        if (mAdapterState.getItemCount() % 2 == 0){//2、4、6、8
            params.height = (mAdapterState.getItemCount() / 2 ) * 90;
        }else {//1、3、5、7
            params.height = ((mAdapterState.getItemCount() + 1) / 2 ) * 90;
        }
        recyclerViewState.setLayoutParams(params);*/
    }

    /**
     * 检查添加的操作是否与P挡关联，如果关联，自定添加P挡状态，或修改已有的挡位状态为P挡
     *
     * @param settingOperation 添加的触发条件数据
     */
    private void checkAddPLimit(SettingOperation settingOperation) {
        if (CarConfigManager.getInstance().checkOperationPLimit(settingOperation.getOperationId())) {
            for (int i = 0; i < mAdapterState.getDataList().size(); i++) {
                //删除原有的P挡条件
                if (mAdapterState.getDataList().get(i).getOperationId().equals(ConstantModelValue.OPERATION_ID_STATE_DRIVE_GEAR)) {
                    if(mAdapterState.getDataList().get(i) instanceof AutoPStateOperation){
                        return;
                    }else {
                        mAdapterState.removeData(i);
                        break;
                    }
                }
            }
            //因为关联添加增加的P挡条件
            mAdapterState.addData(SpecialBeanFactory.productPGearStateOperation());
            isSceneEdited.setValue(checkSceneChanged());
        }
    }
    /**当P挡关联的触发条件删除时，同步删除P挡的状态条件*/
    private void checkDeletePLimit(SettingOperation settingOperation){
        if (CarConfigManager.getInstance().checkOperationPLimit(settingOperation.getOperationId())) {
            if(!CarConfigManager.getInstance().checkOperationPLimit(mAdapterAction.getDataList()) && !CarConfigManager.getInstance().checkOperationPLimit(conditionOperation.getOperationId())){
                List<SettingOperation> settingOperationList = mAdapterState.getDataList();
                for(SettingOperation settingOperation1 : settingOperationList){
                    if(ConstantModelValue.OPERATION_ID_STATE_DRIVE_GEAR.equals(settingOperation1.getOperationId())){
                        mAdapterState.removeData(settingOperation1);
                        isSceneEdited.setValue(checkSceneChanged());
                        return;
                    }
                }
            }
        }
    }


    /**
     * 检查传入的操作是否和已添加的执行动作相冲突
     *
     * @param pos
     * @param settingOperation
     * @return
     */
    public String checkActionOperationConflict(int pos, SettingOperation settingOperation) {
        List<SettingOperation> oldActions = new ArrayList<>(mAdapterAction.getDataList());
        String result = null;
        for (int i = pos - 1; i > -1; i--) {
            if (oldActions.get(i).getOperationId().equals(ConstantModelValue.OPERATION_ID_ACTION_MORE_TIME_DELAY)) {
                break;
            }
            result = checkOperationsConflict(settingOperation, oldActions.get(i));
            if (result != null) {
                return result;
            }
        }
        for (int i = pos + 1; i < oldActions.size(); i++) {
            if (oldActions.get(i).getOperationId().equals(ConstantModelValue.OPERATION_ID_ACTION_MORE_TIME_DELAY)) {
                break;
            }
            result = checkOperationsConflict(settingOperation, oldActions.get(i));
            if (result != null) {
                return result;
            }
        }
        return null;
    }

    /**
     * 检查新操作是否和老操作冲突
     *
     * @param newSet
     * @param oldSet
     * @return
     */
    private String checkOperationsConflict(SettingOperation newSet, SettingOperation oldSet) {
        SingleOperation newOPeration = CarConfigManager.getInstance().findSingleOperationById(newSet.getOperationId());
        SingleOperation oldOperation = CarConfigManager.getInstance().findSingleOperationById(oldSet.getOperationId());

        if (newOPeration == null || oldOperation == null) {
            return null;
        }

        if (newOPeration.getOperationId().equals(oldOperation.getOperationId())) {
            return oldSet.getDesc();
        }
        List<String> newConfIds = newOPeration.getListArg(ConstantModelValue.VIEW_CONFLICT_ID_LIST);
        List<String> newConfArgs = newOPeration.getListArg(ConstantModelValue.VIEW_CONFLICT_ARG_LIST);
        List<String> oldConfIds = oldOperation.getListArg(ConstantModelValue.VIEW_CONFLICT_ID_LIST);
        List<String> oldConfArgs = oldOperation.getListArg(ConstantModelValue.VIEW_CONFLICT_ARG_LIST);
        //先检查新操作是否在老操作的冲突id里
        if (!ListUtil.isEmpty(newConfIds) && ListUtil.isSameSize(newConfIds, newConfArgs)) {
            for (int i = 0; i < newConfIds.size(); i++) {
                if (newConfIds.get(i).equals(oldOperation.getOperationId())) {
                    if (newConfArgs.get(i).equals("all") || newConfArgs.get(i).equals(oldSet.getListArgs().get(0))) {
                        return oldSet.getDesc();
                    }
                }
            }
        }

        //再检查老操作是否在新操作的冲突id里
        if (!ListUtil.isEmpty(oldConfIds) && ListUtil.isSameSize(oldConfIds, oldConfArgs)) {
            for (int i = 0; i < oldConfIds.size(); i++) {
                if (oldConfIds.get(i).equals(newOPeration.getOperationId())) {
                    if (oldConfArgs.get(i).equals("all") || oldConfArgs.get(i).equals(newSet.getListArgs().get(0))) {
                        return oldSet.getDesc();
                    }
                }
            }
        }
        return null;
    }

    /**触发场景的视图Holder*/
    class SceneEditorConditionHolder extends SceneEditorAdapter.SceneEditorBaseHolder {
        public SceneEditorConditionHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void onClickDeleteButton(int position) {
            super.onClickDeleteButton(position);
            SettingOperation temp = conditionOperation;
            conditionOperation = SpecialBeanFactory.productAddConditionOperation();
            mSceneEditorConditionHolder.setupData(conditionOperation, 0);
            checkDeletePLimit(temp);
            isSceneEdited.setValue(checkSceneChanged());
        }

        @Override
        public void setupData(SettingOperation settingOperation, int position) {
            super.setupData(settingOperation, position);
            refreshCompleteButtonAble();
        }

    }

    /**
     * 未添加执行操作或者打开高级设置却未设置触发条件时完成按钮需要置灰
     */
    private void refreshCompleteButtonAble() {
        int actionCount = mAdapterAction.getItemCount();
//        if (actionCount <= 1) {
        if(actionCount <= 1 || (mSwitchCompatAutoMeet.isChecked() && conditionOperation.getOperationId().equals(ConstantModelValue.OPERATION_ID_ADD))){
            mTextViewButtonCoplete.setSelected(false);
            mTextViewButtonCoplete.setTextColor(getResources().getColor(R.color.color_text_indicator_alpha));
        } else {
            mTextViewButtonCoplete.setSelected(true);
            mTextViewButtonCoplete.setTextColor(getResources().getColor(R.color.color_text_indicator));
        }
    }

    /**
     * 只有场景有修改，返回才出现提示弹窗
     *
     * @return
     */
    private boolean checkSceneChanged() {
        if(mSceneChangeUtil != null){
            return mSceneChangeUtil.checkSceneChange(extructSceneBean());
        }else {
            return false;
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if(getView() == null){
            return;
        }
        RecyclerView recyclerViewVr = getView().findViewById(R.id.recyclerview_scene_editor_vr);
        RecyclerView recyclerViewTimeState = getView().findViewById(R.id.recyclerview_scene_editor_time_state);
        RecyclerView recyclerViewState = getView().findViewById(R.id.recyclerview_scene_editor_state);
        RecyclerView recyclerViewAction = getView().findViewById(R.id.recyclerview_scene_editor_action);

        if (recyclerViewVr!=null) {
            recyclerViewVr.removeAllViews();
            recyclerViewVr.removeAllViewsInLayout();
            recyclerViewVr.setAdapter(mAdapterVr);
            recyclerViewVr.getRecycledViewPool().clear();
            mAdapterVr.setDataList(mAdapterVr.getDataList());
        }

        if (recyclerViewTimeState!=null) {
            recyclerViewTimeState.removeAllViews();
            recyclerViewTimeState.removeAllViewsInLayout();
            recyclerViewTimeState.setAdapter(mAdapterTimeState);
            recyclerViewTimeState.getRecycledViewPool().clear();
            mAdapterTimeState.setDataList(mAdapterTimeState.getDataList());
        }

        if (recyclerViewState!=null) {
            recyclerViewState.removeAllViews();
            recyclerViewState.removeAllViewsInLayout();
            recyclerViewState.setAdapter(mAdapterState);
            recyclerViewState.getRecycledViewPool().clear();
            mAdapterState.setDataList(mAdapterState.getDataList());
        }

        if (recyclerViewAction!=null) {
            recyclerViewAction.removeAllViews();
            recyclerViewAction.removeAllViewsInLayout();
            recyclerViewAction.setAdapter(mAdapterAction);
            recyclerViewAction.getRecycledViewPool().clear();
            mAdapterAction.setDataList(mAdapterAction.getDataList());
        }

        if(mSceneEditorConditionHolder != null){
            mSceneEditorConditionHolder.setupData(conditionOperation,0);
            mSceneEditorConditionHolder.refreshBackground();
        }
        ConstraintLayout titleLayout = getView().findViewById(R.id.textview_scene_editor_bar);
        if(titleLayout!=null){
            titleLayout.setBackground(getResources().getDrawable(R.drawable.drawable_bg_scene_editor_operation));
        }


    }

    private EditTextLayout.ConfirmClickListener mConfirmClickListener = new EditTextLayout.ConfirmClickListener() {
        @Override
        public void onClickConfirm(String text) {
            mTextViewTitle.setText(text);
            isSceneEdited.setValue(checkSceneChanged());
        }
    };
}
