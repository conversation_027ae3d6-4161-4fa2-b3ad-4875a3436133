package com.dfl.smartscene.ccs.model;

import android.os.RemoteException;

import com.alibaba.fastjson.JSON;
import com.dfl.api.app.navi.account.IAccount;
import com.dfl.api.app.navi.navi.INaviScene;
import com.dfl.api.app.navi.navi.INaviSceneCallback;
import com.dfl.api.app.navi.route.IRoute;
import com.dfl.api.base.NullServiceException;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.core.ConditionBaseModel;
import com.dfl.smartscene.ccs.core.DeviceBaseModel;
import com.dfl.smartscene.ccs.model.manager.CarConfigManager;
import com.dfl.smartscene.ccs.model.manager.CustomApiManager;
import com.dfl.smartscene.ccs.model.manager.SceneConditionManager;
import com.dfl.smartscene.ccs.navi.ConditionBean;
import com.dfl.smartscene.ccs.navi.FavoriteItemBean;
import com.dfl.smartscene.ccs.navi.FenceTriggerBean;
import com.dfl.smartscene.ccs.navi.RequestRouteExInfo;
import com.dfl.smartscene.ccs.util.DataTypeFormatUtil;
import com.dfl.smartscene.ccs.util.ListUtil;
import com.iauto.uibase.utils.MLog;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class NaviModel implements ConditionBaseModel, StateBaseModel, DeviceBaseModel {

    private static final String TAG = "NaviModel";
    private static volatile NaviModel sInstance;

    private final ArrayList<FenceTriggerBean> mRegisterFenceTriggerBeanList = new ArrayList<>(6);
    private final ArrayList<FenceTriggerBean> mUnregisterFenceTriggerBeanList = new ArrayList<>(6);

    public static NaviModel getInstance() {
        if (null == sInstance) {
            synchronized (NaviModel.class) {
                if (null == sInstance) {
                    sInstance = new NaviModel();
                }
            }
        }
        return sInstance;
    }

    private NaviModel() {

    }


    public void initListener() {
        LogUtil.d(TAG, "initListener()");
        // 注册触发回调
        INaviScene iNaviSceneSetting = CustomApiManager.getInstance().getINaviScene();
        if (null != iNaviSceneSetting) {
            try {
                iNaviSceneSetting.registerNaviSceneCallback(iNaviSceneCallback);
                LogUtil.d(TAG, "registerNaviSceneCallback");
            } catch (NullServiceException e) {
                e.printStackTrace();
            }
        }
        // 向导航注册场景
        unRegisterSceneToNavi();
        registerSceneToNavi();
    }

    private void registerSceneToNavi() {
        String jsonString = JSON.toJSONString(mRegisterFenceTriggerBeanList);
        INaviScene iNaviSceneSetting = CustomApiManager.getInstance().getINaviScene();
        if (null != iNaviSceneSetting) {
            try {
                iNaviSceneSetting.addSceneList(jsonString);
                LogUtil.d(TAG, "registerSceneToNavi = " + jsonString);
                mRegisterFenceTriggerBeanList.clear();
            } catch (NullServiceException | RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    private void unRegisterSceneToNavi() {
        String jsonString = JSON.toJSONString(mUnregisterFenceTriggerBeanList);
        INaviScene iNaviSceneSetting = CustomApiManager.getInstance().getINaviScene();
        if (null != iNaviSceneSetting) {
            try {
                iNaviSceneSetting.removeSceneList(jsonString);
                LogUtil.d(TAG, "unRegisterSceneToNavi = " + jsonString);
                mUnregisterFenceTriggerBeanList.clear();
            } catch (NullServiceException | RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    private FenceTriggerBean packageFenceTriggerBeanJson(SceneBean sceneBean, SettingOperation settingOperation) {
        return packageFenceTriggerBeanJson(sceneBean.getSceneId(),sceneBean.getSceneName(),settingOperation);
    }

    private FenceTriggerBean packageFenceTriggerBeanJson(String sceneId , String sceneName, SettingOperation settingOperation) {
        List<String> listArgs = settingOperation.getListArgs();
        if (null != listArgs && !listArgs.isEmpty()) {
            String json = listArgs.get(0);
            FenceTriggerBean fenceTriggerBean = new FenceTriggerBean();
            fenceTriggerBean.setSceneId(sceneId);
            fenceTriggerBean.setSceneName(sceneName);

            ConditionBean conditionBean = new ConditionBean();

            if(ConstantModelValue.OID_STA_NAVI_TRAFFIC.equals(settingOperation.getOperationId())){
                conditionBean.setTrafficStatus(DataTypeFormatUtil.string2Int(json));
                fenceTriggerBean.setConditionData(conditionBean);
                return fenceTriggerBean;
            }

            FavoriteItemBean favoriteItemBean = JSON.parseObject(json, FavoriteItemBean.class);

            int driveCategory = ConstantModelValue.OID_CDT_EVENT_LEAVE.equals(settingOperation.getOperationId())
                    ? ConditionBean.DriveActionCategory.LEAVE : ConditionBean.DriveActionCategory.ARRIVE;
            conditionBean.setPoi(favoriteItemBean.getPoi());
            conditionBean.setPoiType(favoriteItemBean.getPoiType());
            conditionBean.setDriveCategory(driveCategory);
            if(ConstantModelValue.OID_STA_NAVI_DESTINATION.equals(settingOperation.getOperationId())){
                fenceTriggerBean.setConditionData(conditionBean);
            }else {
                fenceTriggerBean.setConditionBean(conditionBean);
            }
            return fenceTriggerBean;
        }
        return null;
    }


    @Override
    public void registerSceneCondition(SceneBean sceneBean, SettingOperation settingOperation) {
        if (null == settingOperation) {
            LogUtil.d(TAG, "registerSceneCondition settingOperation = null");
            return;
        }
        LogUtil.d(TAG, "registerSceneCondition settingOperation " + settingOperation.toString());
        switch (settingOperation.getOperationId()) {
            case ConstantModelValue.OID_CDT_EVENT_LEAVE:// 离开某地
            case ConstantModelValue.OID_CDT_EVENT_ARRIVE:// 到达某地
                FenceTriggerBean target = packageFenceTriggerBeanJson(sceneBean, settingOperation);
                if (null == target) {
                    return;
                }
                mUnregisterFenceTriggerBeanList.removeIf(next -> next.getSceneId().equals(target.getSceneId()));
                mRegisterFenceTriggerBeanList.add(target);
                registerSceneToNavi();
                break;
            default:
                LogUtil.e(TAG, "registerSceneCondition has no " + settingOperation.getOperationId());
                break;
        }
    }

    @Override
    public void unRegisterSceneCondition(SceneBean sceneBean, SettingOperation settingOperation) {
        if (null == settingOperation) {
            LogUtil.d(TAG, "unRegisterSceneCondition settingOperation = null");
            return;
        }
        LogUtil.d(TAG, "unRegisterSceneCondition settingOperation " + settingOperation.toString());
        switch (settingOperation.getOperationId()) {
            case ConstantModelValue.OID_CDT_EVENT_LEAVE:  // 离开某地
            case ConstantModelValue.OID_CDT_EVENT_ARRIVE: // 到达某地
                FenceTriggerBean target = packageFenceTriggerBeanJson(sceneBean, settingOperation);
                if (null == target) {
                    return;
                }
                mRegisterFenceTriggerBeanList.removeIf(next -> next.getSceneId().equals(target.getSceneId()));
                mUnregisterFenceTriggerBeanList.add(target);
                unRegisterSceneToNavi();
                break;
            default:
                LogUtil.e(TAG, "unRegisterSceneCondition has no " + settingOperation.getOperationId());
                break;
        }
    }

    INaviSceneCallback iNaviSceneCallback = new INaviSceneCallback() {
        @Override
        public void onUpdateNaviCollectedPlaces(String s) {
            LogUtil.d(TAG, "onUpdateNaviCollectedPlaces : " + s);
            if (mNaviCollectPlaceListener != null) {
                mNaviCollectPlaceListener.onCollectPlaceChange(s);
            }
        }

        @Override
        public void onSceneConditionMeet(String sceneId) {
            LogUtil.d(TAG, "onSceneConditionMeet = " + sceneId);
            SceneConditionManager.getInstance().onSceneConditionMeet(sceneId);
        }
    };


    /**
     * 判断是否满足条件
     *
     * @param settingOperation 状态条件及参数
     * @return
     */
    @Override
    public boolean checkStateOperationMeet(SettingOperation settingOperation) {
        if (null == settingOperation || ListUtil.isEmpty(settingOperation.getListArgs())) {
            LogUtil.d(TAG, "checkStateOperationMeet settingOperation = null");
            return false;
        }
        LogUtil.d(TAG,"checkStateOperationMeet : sceneid = " + settingOperation.getSceneId() + " , arg = " + settingOperation.getListArgs().get(0));
        INaviScene iNaviSceneSetting = CustomApiManager.getInstance().getINaviScene();
        if (null != iNaviSceneSetting) {
            try {
                FenceTriggerBean fenceTriggerBean = null;
                String json = null;
                switch (settingOperation.getOperationId()) {
                    case ConstantModelValue.OID_STA_NAVI_DESTINATION: // 目的地
                    case ConstantModelValue.OID_STA_NAVI_TRAFFIC:     // 路况
                        fenceTriggerBean = packageFenceTriggerBeanJson(settingOperation.getSceneId(),"",settingOperation);
                        json = JSON.toJSONString(Arrays.asList(fenceTriggerBean));
                        LogUtil.d(TAG,"checkStateOperationMeet : " + json);
                        iNaviSceneSetting.addSceneList(json);

                        break;
                    default:
                        return false;
                }

                boolean result = iNaviSceneSetting.checkSceneConditionMeet(settingOperation.getSceneId()) == 1;
                iNaviSceneSetting.removeSceneList(json);
                return result;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return false;
    }

    /**
     * 执行相应的操作
     *
     * @param settingOperation
     */
    @Override
    public boolean dispatchSingleActionOperation(SettingOperation settingOperation) {

        if (null == settingOperation) {
            MLog.d(TAG, "dispatchSingleActionOperation settingOperation = null");
            return false;
        }

        // 发起导航
        if (ConstantModelValue.OID_ACT_APP_NAVI_START.equals(settingOperation.getOperationId())) {
            requestRoute(settingOperation);
            return true;
        }
        return false;
    }


    private void requestRoute(SettingOperation settingOperation) {

        SingleOperation singleOperation = CarConfigManager.getInstance().findSingleOperationById(settingOperation.getOperationId());
        if (singleOperation == null) {
            MLog.e(TAG, "this car has not the operation!!! id = " + settingOperation.getOperationId());
            return;
        }

        MLog.d(TAG, "requestRoute settingOperation = " + settingOperation);

        List<String> listArgs = settingOperation.getListArgs();
        if (null == listArgs ) {
            MLog.e(TAG, "this car operation id = " + settingOperation.getOperationId() + ", values is error.");
            return;
        }
        if (listArgs.size() == 1){
            //只有一个路线偏好，未选择导航目的地
            String stategyStr = listArgs.get(0);
            try {
                com.dfl.api.app.navi.setting.ISetting iRoute = CustomApiManager.getInstance().getNaviSetting();
                if (iRoute != null){
                    //路线偏好
                    iRoute.RoutePreferences(Integer.parseInt(stategyStr));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            return;
        }

        String stategyStr = listArgs.get(0);
        String poiFavoriteJson = listArgs.get(1);

        FavoriteItemBean commonFavoriteBean = JSON.parseObject(poiFavoriteJson, FavoriteItemBean.class);
        int type = -1;
        if (commonFavoriteBean.getPoiType() == 1){
            //家
            type = 10;
        }else if (commonFavoriteBean.getPoiType() == 2){
            //公司
            type = 11;
        }else if (commonFavoriteBean.getPoiType() == 3){
            //其他收藏点
            IRoute iRoute = CustomApiManager.getInstance().getIRoute();
            if (null == iRoute) {
                MLog.e(TAG, "IRoute is null");
                return;
            }
            RequestRouteExInfo.ProtoocolPoi endProtocolPoi = new RequestRouteExInfo.ProtoocolPoi();
            endProtocolPoi.setPoiName(commonFavoriteBean.getFavoriteItemName());
            if (commonFavoriteBean.getPoi() !=  null){
                endProtocolPoi.setLongitude(commonFavoriteBean.getPoi().lon);
                endProtocolPoi.setLatitude(commonFavoriteBean.getPoi().lat);
                endProtocolPoi.setEntryLongitude(commonFavoriteBean.getPoi().lon);
                endProtocolPoi.setEntryLatitude(commonFavoriteBean.getPoi().lat);
            }
            endProtocolPoi.setAddress(commonFavoriteBean.getAddress());
            RequestRouteExInfo requestRouteExInfo = new RequestRouteExInfo();
            requestRouteExInfo.setActionType(0); // 立即开始导航
//                    requestRouteExInfo.setStartProtocolPoi();
            requestRouteExInfo.setEndProtocolPoi(endProtocolPoi);
            requestRouteExInfo.setStrategy(Integer.parseInt(stategyStr));
            try {
                MLog.d(TAG, "iRoute.requestRoute : " + JSON.toJSONString(requestRouteExInfo));
                iRoute.requestRoute(JSON.toJSONString(requestRouteExInfo));
                return;
            } catch (NullServiceException | RemoteException e) {
                e.printStackTrace();
            }
        }
        IAccount iAccount = CustomApiManager.getInstance().getIAccount();
        if (null != iAccount) {
            try {
                iAccount.showView(type);
                com.dfl.api.app.navi.setting.ISetting iRoute = CustomApiManager.getInstance().getNaviSetting();
                MLog.d(TAG, "show Navi Collection View error, iAccount = null." + iRoute);
                if (iRoute != null){
                    MLog.d(TAG, "show Navi Collection View error, iAccount = null执行路线偏好.");
                    //路线偏好
                    iRoute.RoutePreferences(Integer.parseInt(stategyStr));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            MLog.d(TAG, "show Navi Collection View error, iAccount = null.");
        }
    }

    private LibraryNaviModel.NaviCollectPlaceListener mNaviCollectPlaceListener;

    public void registerNaviCollectPlacesCallback(LibraryNaviModel.NaviCollectPlaceListener naviCollectPlaceListener) {
        mNaviCollectPlaceListener = naviCollectPlaceListener;
    }

    public void unregisterNaviCollectPlacesCallback() {
        mNaviCollectPlaceListener = null;
    }

    public String getNaviCollectPlace() {
        try {
            INaviScene iNaviSceneSetting = CustomApiManager.getInstance().getINaviScene();
            if (null == iNaviSceneSetting) {
                LogUtil.d(TAG, "INaviSceneSetting is null while get collect place");
                return null;
            }
            return iNaviSceneSetting.getNaviCollectedPlaces();
        } catch (NullServiceException | RemoteException | NullPointerException e) {
            e.printStackTrace();
            return null;
        }
    }
}
