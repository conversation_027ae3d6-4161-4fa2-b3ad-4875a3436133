package com.dfl.smartscene.ccs.bean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/8/10 10:26
 * @description ：场景分类，包含场景列表
 */
public class SceneCategory implements Serializable {
    /**
     * 分类id
     */
    private String categoryId = "";
    /**
     * 分类名
     */
    private String categoryName = "";
    /**
     * 场景列表
     */
    private List<SceneBean> sceneBeanList = new ArrayList<>();

    public SceneCategory(){

    }
    public SceneCategory(String categoryId, String categoryName) {
        this.categoryId = categoryId;
        this.categoryName = categoryName;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public List<SceneBean> getSceneBeanList() {
        return sceneBeanList;
    }

    public void setSceneBeanList(List<SceneBean> sceneBeanList) {
        this.sceneBeanList = sceneBeanList;
    }

    public void addScene(SceneBean sceneBean){
        sceneBeanList.add(0,sceneBean);
    }

    public void addScene(int poi,SceneBean sceneBean){
        sceneBeanList.add(poi,sceneBean);
    }

    public SceneBean findScene(String sceneId){
        int pos = -1;
        for(int i = 0; i < sceneBeanList.size() ; i++){
            if(sceneBeanList.get(i).getSceneId().equals(sceneId)){
                pos = i;
            }
        }
        if(pos > -1){
          return  sceneBeanList.get(pos);
        }else {
            return null;
        }

    }

    public void replaceScene(SceneBean sceneBean){
        int pos = -1;
        for(int i = 0; i < sceneBeanList.size() ; i++){
            if(sceneBeanList.get(i).getSceneId().equals(sceneBean.getSceneId())){
                pos = i;
            }
        }
        if(pos > -1){
            sceneBeanList.remove(pos);
        }
        sceneBeanList.add(0,sceneBean);
    }

    public SceneBean removeScene(String sceneId){
        int pos = -1;
        for(int i = 0; i < sceneBeanList.size() ; i++){
            if(sceneBeanList.get(i).getSceneId().equals(sceneId)){
                pos = i;
                break;
            }
        }
        if(pos > -1){
            SceneBean oldBean = sceneBeanList.get(pos);
            sceneBeanList.remove(pos);
            return oldBean;
        }else {
            return null;
        }
    }

    @Override
    public String toString() {
        return "SceneCategory{" +
                "mCategoryId='" + categoryId + '\'' +
                ", mCategoryName='" + categoryName + '\'' +
                ", mSceneBeanList=" + sceneBeanList +
                '}';
    }
}
