package com.dfl.smartscene.ccs.library.app;

import android.app.Application;
import android.content.res.Configuration;

import com.dfl.smartscene.ccs.base.MediaGroupManager;
import com.dfl.smartscene.ccs.model.RelieveStressPatternModel;
import com.dfl.smartscene.ccs.model.ScenePatternInterrupt;
import com.dfl.smartscene.ccs.model.SurpriseEggListModel;
import com.dfl.smartscene.ccs.model.SurpriseEggPlayModel;
import com.dfl.smartscene.ccs.util.BigDataControl;
import com.dfl.smartscene.ccs.util.SysViewControl;
import com.iauto.uibase.thememanager.ThemeManager;
import com.iauto.uibase.utils.MLog;

/*
 * ScenePattern application class
 * <AUTHOR>
 * @date 2022/4/15
 */
public class ScenePattern {
    private static final String TAG = "ScenePattern";
    private boolean mIsForeground = false;
    private Application sInstance = null;

    private static ScenePattern sPattern;



    /**
     * 构造函数，在这里进行Logtag的初始化操作
     */
    private ScenePattern() {
        super();
        // 初始化logtag，在所有log输出之前进行初始化操作，每个应用都有一个固定的logtag
        MLog.init("App_ScenePattern");
    }

    public static ScenePattern getInstance(){
        if(null == sPattern){
            synchronized (ScenePattern.class){
                if(null == sPattern){
                    sPattern = new ScenePattern();
                }
            }
        }
        return sPattern;
    }

    public void onCreate(Application a) {
        MLog.d(TAG, "onCreate");
       sInstance = a;
       onInitialize();
    }

    /**
     * 初始化操作
     * 需要在该方法中，创建该应用中使用到的所有Model单例
     */
    public void onInitialize() {
        MLog.d(TAG,"onInitialize");
        SysViewControl.getInstance().init(sInstance);
        BigDataControl.create(sInstance);
        // 初始化Model
        MediaGroupManager.create(sInstance);
        ScenePatternInterrupt.create(sInstance);
        RelieveStressPatternModel.create(sInstance);
        SurpriseEggListModel.create(sInstance);
        SurpriseEggPlayModel.create(sInstance);
    }


    public boolean isForeground() {
        return mIsForeground;
    }

    public void setIsForeground(boolean isForeground) {
        mIsForeground = isForeground;
    }

    public Application getContext() {
        return sInstance;
    }

    /**
     * 监听uiMode变化，实现换肤
     */
    public void onConfigurationChanged(Configuration newConfig) {
        ThemeManager.getInstance().setConfiguration(newConfig);
    }


}
