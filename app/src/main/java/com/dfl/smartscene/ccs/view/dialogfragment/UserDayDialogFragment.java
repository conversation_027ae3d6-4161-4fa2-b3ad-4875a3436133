package com.dfl.smartscene.ccs.view.dialogfragment;

import android.content.res.Configuration;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseAdapter;
import com.dfl.smartscene.ccs.base.BaseDialogFragment;
import com.dfl.smartscene.ccs.util.RecyclerViewUtil;
import com.dfl.smartscene.ccs.view.weight.UserDayDialogAdapter;
import com.iauto.uibase.utils.MLog;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date ：2022/9/16 13:38
 * @description ：周一、周二-周日的选择弹窗
 */
public class UserDayDialogFragment extends BaseDialogFragment {
    private static final String TAG = "UserDayDialogFragment";

    private UserDayClickListener mUserDayClickListener;
    private UserDayDialogAdapter mUserDayDialogAdapter;
    private ConstraintLayout mLayoutConfirm;
    int COLUMN_SPACE = R.dimen.x_px_20;//item之间的间距
    int LINE_SPACE = R.dimen.y_px_20;//item之间的间距

    private int defaultInitValue = 127;

    public void setUserDayClickListener(UserDayClickListener userDayClickListener) {
        mUserDayClickListener = userDayClickListener;
    }

    public void setInitValue(int value){
        defaultInitValue = value;
        if(mUserDayDialogAdapter != null){
            MLog.d(TAG, "setInitValue: "+value);
            mUserDayDialogAdapter.setDayValue(value);
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.dialog_state_user_day,container,false);

    }





    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mUserDayClickListener = null;
    }

    @Override
    protected void init() {

    }

    private RecyclerView mRecycleView;
    @Override
    protected void initView(View view) {
        mRecycleView = view.findViewById(R.id.recyclerview_user_day);
        mLayoutConfirm = view.findViewById(R.id.imagebutton_user_day_dialog_confirm);
        RecyclerViewUtil.setGridRecycleView(mRecycleView,RecyclerView.VERTICAL,4,COLUMN_SPACE,LINE_SPACE);
        mUserDayDialogAdapter = new UserDayDialogAdapter();
        mRecycleView.setAdapter(mUserDayDialogAdapter);

        //返回、确定、取消的点击回调
        view.findViewById(R.id.imagebutton_user_day_dialog_back).setOnClickListener(v ->{
            if(mUserDayClickListener != null){
                mUserDayClickListener.onClickBack(String.valueOf(mUserDayDialogAdapter.getDayValue()), mUserDayDialogAdapter.getDesc());
                dismiss();
            }
        });
        view.findViewById(R.id.imagebutton_user_day_dialog_cancel).setOnClickListener(v ->{
            if(mUserDayClickListener != null){
                mUserDayClickListener.onClickCancel();
                dismiss();
            }
        });
        mLayoutConfirm.setOnClickListener(v ->{
            if(mUserDayClickListener != null){
                mUserDayClickListener.onClickConfirm(String.valueOf(mUserDayDialogAdapter.getDayValue()), mUserDayDialogAdapter.getDesc());
                dismiss();
            }
        });

        mUserDayDialogAdapter.setOnItemClickListener(new BaseAdapter.OnItemClickListener<String>() {
            @Override
            public void onItemClick(View view, int viewType, String s, int position) {
                mUserDayDialogAdapter.changeCheckStatus(position);
                if(mUserDayDialogAdapter.getDayValue() == 0){
                    mLayoutConfirm.setClickable(false);
                    mLayoutConfirm.setAlpha(0.5f);
                }else {
                    mLayoutConfirm.setClickable(true);
                    mLayoutConfirm.setAlpha(1f);

                }
            }
        });


    }

    @Override
    protected void initObserver() {

    }

    @Override
    protected void initData() {
        mUserDayDialogAdapter.setDataList(Arrays.asList("周一","周二","周三","周四","周五","周六","周日"));
        mUserDayDialogAdapter.setDayValue(defaultInitValue);
    }

    public interface UserDayClickListener{
        void onClickCancel();
        void onClickConfirm(String value,String desc);
        void onClickBack(String value,String desc);
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (mRecycleView != null) {
            mRecycleView.removeAllViews();
            mRecycleView.removeAllViewsInLayout();
            mRecycleView.setAdapter(mUserDayDialogAdapter);
            mRecycleView.getRecycledViewPool().clear();
            mUserDayDialogAdapter.setDataList(Arrays.asList("周一","周二","周三","周四","周五","周六","周日"));
        }
    }
}
