package com.dfl.smartscene.ccs.model;


import com.dfl.smartscene.ccs.bean.aiot.AiotCategoryBean;
import com.dfl.smartscene.ccs.bean.aiot.AiotCategorySaleBean;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;

import java.util.List;

import io.reactivex.Observable;

/**
 * description:
 * author:huangzezheng
 * Data:2023/1/6
 **/
public class LibraryAiotModel{

    /**
     * 请求aiot连接设备数据
     * @return
     */
    public Observable<List<AiotCategoryBean>> requestAiotDevices(){
        return Observable.error(new Exception());
    }

    /**
     * 获取aiot售卖设备数据
     * @return
     */
    public Observable<List<AiotCategorySaleBean>> requestAiotSaleDevices(){
        return Observable.error(new Exception());

    }

    //用户本地添加场景能力json
    public void sendUserCommand(String json){

    }

    //云端预设场景能力json
    public void sendCloudCommand(String json){

    }

    /**
     * 获取车内空气质量
     * @return
     */
    public String getCarAirQuality(){
        return ConstantModelValue.CAR_AIR_QUELITY_GOOD;
    }

    /**
     * 注册设备监听
     */
    public void registerDeviceListener(){

    }

}
