package com.dfl.smartscene.ccs.model;

import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.util.DataTypeFormatUtil;
import com.dfl.smartscene.ccs.util.ListUtil;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/9/23 14:05
 * @description ：
 */
public interface StateBaseModel {

    /**
     * 检查状态条件是否满足
     * @param settingOperation 状态条件及参数
     * @return
     */
    boolean checkStateOperationMeet(SettingOperation settingOperation);

    /**
     * 判断传入参数的第一个值是否与当前值相同
     * @param args
     * @param value
     * @return
     */
    default boolean simpleStateCheck(List<String> args, String value){
        if(ListUtil.isEmpty(args)){
            return false;
        }
        if(value.contains("|")){
            String[] split = value.split("\\|");
            return Arrays.asList(split).contains(args.get(0));
        }else {
            return args.get(0).equals(value);

        }
    }

    /**
     * 比较类型的状态值，有大于 小于两种作为第一个参数
     * @param args
     * @param value
     * @return
     */
    default boolean simpleRangeStateCheck(List<String> args , String value){
        if(!ListUtil.checkIndex(args,1)){
            return false;
        }
        if(ConstantModelValue.SYMBOL_GREATER.equals(args.get(0))){
            if(args.get(1).contains(".")){
                return DataTypeFormatUtil.string2Float(value) > DataTypeFormatUtil.string2Float(args.get(1));
            }else {
                return DataTypeFormatUtil.string2Int(value) > DataTypeFormatUtil.string2Int(args.get(1));
            }
        }else if(ConstantModelValue.SYMBOL_LESS.equals(args.get(0))){
            if(args.get(1).contains(".")){
                return DataTypeFormatUtil.string2Float(value) < DataTypeFormatUtil.string2Float(args.get(1));
            }else {
                return DataTypeFormatUtil.string2Int(value) < DataTypeFormatUtil.string2Int(args.get(1));
            }
        }else{
            return false;
        }
    }


}
