package com.dfl.smartscene.ccs.view.surpriseegg;

import android.content.Intent;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.net.Uri;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.Navigation;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.dfl.dflcommonlibs.commonclass.ActivityManager;
import com.dfl.dflcommonlibs.dialog.DefaultDoubleDialogListener;
import com.dfl.dflcommonlibs.dialog.DefaultSingerDialogListener;
import com.dfl.dflcommonlibs.dialog.LibDialog;
import com.dfl.dflcommonlibs.network.NetworkUtils;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.constant.ScenePatternFuncDef;
import com.dfl.smartscene.ccs.constant.SurpriseEggFuncDef;
import com.dfl.smartscene.ccs.model.ScenePatternInterrupt;
import com.dfl.smartscene.ccs.util.DialogManager;
import com.dfl.smartscene.ccs.util.DialogProcessPopupView;
import com.dfl.smartscene.ccs.util.ScenePatternViewManager;
import com.dfl.smartscene.ccs.util.SysViewControl;
import com.dfl.smartscene.ccs.util.ToastManager;
import com.dfl.smartscene.ccs.view.ScenePatternBaseFragment;
import com.dfl.smartscene.ccs.viewmodel.SurpriseEggListVM;
import com.dfl.smartscene.databinding.FragmentSurpriseEggListBinding;
import com.dfl.smartscene.databinding.FragmentSurpriseEggListItemBinding;
import com.dfl.smartscene.databinding.FragmentSurpriseEggListItemTitleBinding;
import com.iauto.uibase.utils.MLog;
import com.iauto.uicontrol.ButtonBase;
import com.iauto.uicontrol.ButtonView;
import com.iauto.uicontrol.ListItemUnit;
import com.iauto.uicontrol.RecyclerListView;
import com.iauto.uicontrol.SimpleLayoutManager;
import com.iauto.uicontrol.SimpleListAdapter;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshListener;

/*
 * SurpriseEggListFragment class
 * 彩蛋模式fragment
 * <AUTHOR>
 * @date 2022/5/26
 */
public class SurpriseEggListFragment extends ScenePatternBaseFragment {
    private static final String TAG = "SurpriseEggListFragment";

    //定义一个View的数组
    private FragmentSurpriseEggListBinding mBinding    = null;
    private SurpriseEggListVM mViewModel = null;

    protected SurpriseEggAdapter mSurpriseEggAdapter = new SurpriseEggAdapter();
    private SmartRefreshLayout mRefreshLayout;
    private RecyclerListView mSurpriseEggRecyclerListView;
    private SpacesItemDecoration mSpacesItemDecoration;
    private ButtonView mEditCheckAll;
    private ButtonView mEditDel;
    private DialogProcessPopupView mDialogProcessPopupView = null;
    protected DelStatusChangeObserver mDelStatusChange = new DelStatusChangeObserver();
    protected DelCountChangeObserver  mDelCountChange  = new DelCountChangeObserver();
    protected DspStatusChangeObserver mDspStatusChangeObserver = new DspStatusChangeObserver();                   // 菜单表示状态观察者

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        MLog.d(TAG, "onCreateView");
        mBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_surprise_egg_list, container, false);
        mBinding.buttonContentTopBack.setOnClickListener(v -> Navigation.findNavController(v).navigateUp());
        mBinding.buttonContentTopClose.setOnClickListener(v -> {
                Intent intent = new Intent();
                intent.setPackage("com.dfl.launcher");
                intent.setAction("ExitNormal");
                ActivityManager.getInstance().getCurrentActivity().sendBroadcast(intent);
            });

        mViewModel = new ViewModelProvider(requireActivity()).get(SurpriseEggListVM.class);
        mBinding.layoutPageLoading.loadingTv1.setVisibility(View.GONE);
        mBinding.layoutPageLoading.homeItemLoadingGif.setVisibility(View.GONE);
        // list设定
        mSurpriseEggAdapter.setListChange(mViewModel.getSurpriseEggListChanged());
        mSurpriseEggRecyclerListView = (RecyclerListView) mBinding.getRoot().findViewById(R.id.recycler_list_view_surprise_egg_list);
        mSurpriseEggRecyclerListView.setLayoutManager(new SimpleLayoutManager(getContext(), 4));
        mSpacesItemDecoration = new SpacesItemDecoration();
        mSurpriseEggRecyclerListView.addItemDecoration(mSpacesItemDecoration);
        mSurpriseEggRecyclerListView.setAdapter(mSurpriseEggAdapter);
        mSurpriseEggRecyclerListView.setScrollBar(mBinding.scrollbarViewSurpriseEggList);

        // refreshLayout设定
        mRefreshLayout = mBinding.getRoot().findViewById(R.id.refresh_layout_surprise_egg_list);
        mRefreshLayout.setEnableLoadMore(false);
        // popup设定
        mDialogProcessPopupView = new DialogProcessPopupView(getContext());

        // edit全选设定
        mEditCheckAll = mBinding.getRoot().findViewById(R.id.button_surprise_egg_list_edit_check_all);
        mEditCheckAll.setOnBtnClickListener(new ButtonView.OnBtnClickListener() {
            @Override
            public void onBtnClick(View view) {
                if (mEditCheckAll.isSelected()) {
                    mViewModel.setAllListItemEdit(false);
                } else {
                    mViewModel.setAllListItemEdit(true);
                }
                mSurpriseEggAdapter.notifyDataSetChanged();
            }
        });

        // 删除按钮设定
        mEditDel = mBinding.getRoot().findViewById(R.id.button_surprise_egg_list_edit_del);
        mEditDel.setOnBtnClickListener(new ButtonView.OnBtnClickListener() {
            @Override
            public void onBtnClick(View view) {
                // 删除dialog处理
                DialogManager.showDefaultDoubleDialog(new DefaultDoubleDialogListener() {
                    @Override
                    public View.OnClickListener getPositiveButtonListener(LibDialog libDialog) {
                        return new View.OnClickListener() {
                            public void onClick(View v) {
                                mDialogProcessPopupView.showDelDialog(mViewModel.getDelListSize().getValue(), new LibDialog(new DefaultSingerDialogListener() {
                                    @Override
                                    public String getDialogTitle() {
                                        return "";
                                    }

                                    @Override
                                    public int getDialogLayout() {
                                        return R.layout.dialog_del_progress;
                                    }

                                    @Override
                                    public int getMessageTextId() {
                                        return R.id.del_info_text;
                                    }

                                    @Override
                                    public String getDialogMessage() {
                                        String text = getResources().getString(R.string.string_surprise_egg_list_item_del_ing_message);
                                        return (String) String.format(text, 0, mViewModel.getDelListSize().getValue());
                                    }

                                    @Override
                                    public int getNegativeButtonId(){
                                        return R.id.dialog_stop_button;
                                    }

                                    @Override
                                    public String getNegativeButtonName() {
                                        return (String) getResources().getString(R.string.string_surprise_egg_list_item_del_stop_button);
                                    }

                                    @Override
                                    public View.OnClickListener getNegativeButtonListener(LibDialog libDialog) {
                                        return new View.OnClickListener() {
                                            @Override
                                            public void onClick(View v) {
                                                mViewModel.setStopDel(true);
                                                libDialog.dismiss();
                                            }
                                        };
                                    }
                                }));
                                mViewModel.deleteSelListItem();
                                libDialog.dismiss();
                            }
                        };
                    }

                    @Override
                    public String getDialogMessage() {
                        String msg = getResources().getString(R.string.string_surprise_egg_list_item_del_message);
                        return String.format(msg,mViewModel.getDelListSize().getValue());
                    }

                    @Override
                    public String getPositiveButtonName() {
                        return getResources().getString(R.string.string_surprise_egg_list_item_del_button);
                    }
                });
            }
        });
        refreshListLayout();
        mBinding.setVm(mViewModel);
        mBinding.setLifecycleOwner(getActivity());
        initCCEventReceiver((ViewGroup) mBinding.getRoot());
        MLog.d(TAG, "onCreateView end");
        return mBinding.getRoot();
    }

    // 已删除数量观察者设定，根据此对删除dialog中的内容进行更新
    public class DelCountChangeObserver implements Observer<Integer> {
        @Override
        public void onChanged(Integer count) {
            MLog.d(TAG, "DelCountChangeObserver " + count);
            if (null != mDialogProcessPopupView) {
                if (count > 0) {
                    if (mDialogProcessPopupView.isShow()) {
                        mDialogProcessPopupView.updateDeleteInfo(count);
                    }
                }
            }
        }
    }

    // 删除状态变更dialog，根据删除状态表示不同的toast
    public class DelStatusChangeObserver implements Observer<Integer> {
        @Override
        public void onChanged(Integer DelStatus) {
            MLog.d(TAG, "DelStatusChangeObserver " + DelStatus);
            if (null != mDialogProcessPopupView) {
                // 删除结束时，关闭删除dialog，表示删除成功toast
                if (DelStatus == SurpriseEggFuncDef.SURPRISE_EGG_DEL_STATUS_END) {
                    if (mDialogProcessPopupView.isShow()) {
                        mDialogProcessPopupView.hideDialog();
                        onCCMethod_SurpriseEggEditEndClick(null);
                        ScenePatternViewManager.getInstance().showToast(getParentFragmentManager(),SurpriseEggFuncDef.SURPRISE_EGG_DELETE_SUCCESS, getContext());
                    }
                } else if(DelStatus == SurpriseEggFuncDef.SURPRISE_EGG_DEL_STATUS_ERR) {
                    if (mDialogProcessPopupView.isShow()) {
                        mDialogProcessPopupView.hideDialog();
                        onCCMethod_SurpriseEggEditEndClick(null);
                        ScenePatternViewManager.getInstance().showToast(getParentFragmentManager(),SurpriseEggFuncDef.SURPRISE_EGG_DELETE_FAILED, getContext());
                    }
                }
            }
        }
    }

    /**
     * 彩蛋表示变更观察者
     */
    public class DspStatusChangeObserver implements Observer<Integer> {
        private boolean mIsFirst = true;
        @Override
        public void onChanged(Integer changed) {
            MLog.d(TAG,"DspStatusChangeObserver changed:"+changed);
            mViewModel.initDspVisibility();

            if (SurpriseEggFuncDef.SURPRISE_EGG_DSP_STATUS_COMMON == changed) {
                mRefreshLayout.finishRefresh(true);
                mRefreshLayout.setEnableRefresh(true);
            } else if (SurpriseEggFuncDef.SURPRISE_EGG_DSP_STATUS_LOADING == changed) {
                if(!mIsFirst) {
                    mViewModel.requestSurpriseEggListData();
                }
                mIsFirst = false;
            } else if(SurpriseEggFuncDef.SURPRISE_EGG_DSP_STATUS_DISCONNECT == changed){
                // do nothing
                mRefreshLayout.finishRefresh(false);
            }
        }

        public void setIsFirst(boolean status) {
            mIsFirst = status;
        }
    }

    /**
     * 刷新list的布局
     */
    public void refreshListLayout() {
        mRefreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(@NonNull com.scwang.smart.refresh.layout.api.RefreshLayout refreshLayout) {
                mViewModel.setSurpriseEggListDspStatus(SurpriseEggFuncDef.SURPRISE_EGG_DSP_STATUS_REFRESH_LOADING);
                mViewModel.setIsEditButtonEnable(false);
                mViewModel.requestSurpriseEggListData();
            }
        });
    }

    /*
     * 控件消息接收初始化处理
     * @param view 控件 view
     */
    private void initCCEventReceiver(@NonNull ViewGroup view) {

        for (int i = 0; i < view.getChildCount(); i++) {
            View childView = view.getChildAt(i);
            if (childView instanceof ButtonBase) {
                ButtonBase child = (ButtonBase) childView;
                child.setEventReceiver(this);
            }
            if (childView instanceof ButtonView) {
                ButtonView child = (ButtonView) childView;
                child.setEventReceiver(this);
            }

            if (childView instanceof ListItemUnit) {
                ListItemUnit child = (ListItemUnit) childView;
                child.setEventReceiver(this);
            }

            if (childView instanceof ViewGroup) {
                initCCEventReceiver((ViewGroup) childView);
            }
        }

    }


    @Override
    public void onStop() {
        super.onStop();
//        mViewModel.deInitLanYouRequest();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        int connectStatus = ScenePatternInterrupt.getInstance().getNetWorkStatus().getValue();
        if (connectStatus == ScenePatternFuncDef.INTERNET_STATUS_CONNECTED) {
            mViewModel.setSurpriseEggListDspStatus(SurpriseEggFuncDef.SURPRISE_EGG_DSP_STATUS_NO_FILES);
        } else {
            mViewModel.setSurpriseEggListDspStatus(SurpriseEggFuncDef.SURPRISE_EGG_DSP_STATUS_DISCONNECT);
        }
    }

    /*
     * 添加观察者
     */
    public void addObserver() {
        MLog.d(TAG,"addObserver ");
        mViewModel.getSurpriseEggListDelStatus().observeForever(mDelStatusChange);
        mViewModel.getSurpriseEggListDelCount().observeForever(mDelCountChange);
        mDspStatusChangeObserver.setIsFirst(true);
        mViewModel.getSurpriseEggListDspStatus().observeForever(mDspStatusChangeObserver);
        mViewModel.getNetDisconnectVisibility().observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                if(integer == ScenePatternFuncDef.Scene_Pattern_Visible){
                    mRefreshLayout.setEnableRefresh(false);
                }
            }
        });
        mViewModel.getLoadingVisibility().observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                if(integer == ScenePatternFuncDef.Scene_Pattern_Visible){
                    mRefreshLayout.setEnableRefresh(false);
                }
            }
        });
    }

    /*
     * 移除观察者
     */
    public void removeObserver() {
        MLog.d(TAG,"removeObserver");
        mViewModel.getSurpriseEggListDelStatus().removeObserver(mDelStatusChange);
        mViewModel.getSurpriseEggListDelCount().removeObserver(mDelCountChange);
        mViewModel.getSurpriseEggListDspStatus().removeObserver(mDspStatusChangeObserver);
    }

    @Override
    public void onResume() {
        super.onResume();
        MLog.d(TAG,"onResume");
        int connectStatus = ScenePatternInterrupt.getInstance().getNetWorkStatus().getValue();
        int dspStatus = mViewModel.getSurpriseEggListDspStatus().getValue();
        if (connectStatus == ScenePatternFuncDef.INTERNET_STATUS_CONNECTED &&
                dspStatus != SurpriseEggFuncDef.SURPRISE_EGG_DSP_STATUS_COMMON) {
            MLog.d(TAG,"onResume:loading");
            mViewModel.setSurpriseEggListDspStatus(SurpriseEggFuncDef.SURPRISE_EGG_DSP_STATUS_LOADING);
            mViewModel.requestSurpriseEggListData();
        }
        mViewModel.initDspVisibility();
        addObserver();
    }

    @Override
    public void onPause() {
        super.onPause();
        removeObserver();
    }

    /**
     * 彩蛋list item压下处理
     *
     * @param id 彩蛋list item的id
     */
    public void onCCMethod_SurpriseEggItemClick(int id) {
        MLog.d(TAG, "onCCMethod_SurpriseEggItemClick: ");
        if (mViewModel.getIsListEdit().getValue()) {
            mViewModel.changeListItemEdit(id);
            mSurpriseEggAdapter.notifyDataSetChanged();
        } else {
            if (ScenePatternFuncDef.CAR_GEAR_TYPE_P != mViewModel.getGearType().getValue()) {
                mViewModel.setSurpriseEggPlayData(id);
                if (NetworkUtils.getNetworkAvailable(getContext())){
                    SysViewControl.getInstance().reqOpenSurpriseEggPlayFullView(ScenePatternFuncDef.SYS_VIEW_CTRL_PATTERN_SURPRISE);
                }else {
                    ToastManager.getInstance().showToast(getContext().getString(R.string.string_entwork_error),
                            Toast.LENGTH_SHORT,
                            false);
                }
            } else {
                ToastManager.getInstance().showToast(getContext().getString(R.string.string_surprise_egg_list_toast_gear_type_p),
                        Toast.LENGTH_SHORT,
                        false);
            }
        }
    }

    /**
     * 彩蛋list编辑按钮压下处理
     *
     * @param view
     */
    public void onCCMethod_SurpriseEggEditClick(View view) {
        MLog.d(TAG, "onCCMethod_SurpriseEggEditClick: ");
        mViewModel.setIsListEdit(true);
        mRefreshLayout.setEnableRefresh(false);
        mSurpriseEggAdapter.notifyDataSetChanged();
    }

    /**
     * 彩蛋list编辑结束按钮压下处理
     *
     * @param view
     */
    public void onCCMethod_SurpriseEggEditEndClick(View view) {
        MLog.d(TAG, "onCCMethod_SurpriseEggEditEndClick: ");
        mViewModel.setIsListEdit(false);
        mRefreshLayout.setEnableRefresh(true);
        mViewModel.setAllListItemEdit(false);
        mSurpriseEggAdapter.notifyDataSetChanged();
    }

    /*
     * SurpriseEggAdapter class
     * 彩蛋list adapter
     * <AUTHOR>
     * @date 2022/5/26
     */
    public class SurpriseEggAdapter extends SimpleListAdapter {

        FragmentSurpriseEggListItemTitleBinding itemTitleBinding;
        FragmentSurpriseEggListItemBinding itemDataBinding;
        int mListSize = 0;

        /**
         * list变更处理
         *
         * @param liveData 观察者
         */
        public void setListChange(final MutableLiveData<Boolean> liveData) {
            liveData.observe(SurpriseEggListFragment.this, new Observer<Boolean>() {
                @Override
                public void onChanged(Boolean change) {
                    mListSize = mViewModel.getListSize().getValue();
                    MLog.d(TAG, "setListChange: ");
                    notifyDataSetChanged();
                    mSurpriseEggRecyclerListView.scrollToPosition(0);
                }
            });
        }


        @NonNull
        @Override
        public RecyclerListView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            SurpriseEggViewHolder holder = null;
            FragmentSurpriseEggListItemTitleBinding itemBinding0;
            FragmentSurpriseEggListItemBinding itemBinding1;

            // list title（时间）
            if (viewType == 0) {
                itemBinding0 = DataBindingUtil.inflate(LayoutInflater.from(getContext()), R.layout.fragment_surprise_egg_list_item_title, parent, false);
                holder = new SurpriseEggViewHolder(itemBinding0.getRoot());

            } else {
                // list item
                itemBinding1 = DataBindingUtil.inflate(LayoutInflater.from(getContext()), R.layout.fragment_surprise_egg_list_item, parent, false);
                holder = new SurpriseEggViewHolder(itemBinding1.getRoot());

            }
            return holder;
        }

        @Override
        public void onBindViewHolder(RecyclerListView.ViewHolder holder, int position) {
            super.onBindViewHolder(holder, position);

            if (getItemViewType(position) == 0) {
                // list title vm设定
                itemTitleBinding = DataBindingUtil.getBinding(holder.itemView);
                itemTitleBinding.setVm(mViewModel.getSurpriseEggListItemViewModel(position));
                itemTitleBinding.executePendingBindings();
            } else if (getItemViewType(position) == 1) {
                // list item vm设定
                itemDataBinding = DataBindingUtil.getBinding(holder.itemView);
                itemDataBinding.setVm(mViewModel.getSurpriseEggListItemViewModel(position));

                Uri URI = Uri.parse(mViewModel.getSurpriseEggListItemViewModel(position).getImageURL());
                Glide.with(SurpriseEggListFragment.this).load(URI).placeholder(R.drawable.bg_scenmode_defulte)
                        .fallback(R.drawable.bg_scenmode_defulte).error(R.drawable.bg_scenmode_defulte).into(itemDataBinding.surpriseEggItemImage);
                itemDataBinding.surpriseEggItemText.setTypeface(itemDataBinding.surpriseEggItemText.getTypeface(), Typeface.BOLD);
                itemDataBinding.executePendingBindings();
            } else {
                // do nothing
            }

            setChildItemEvent(holder.itemView, SurpriseEggListFragment.this);

        }

        @Override
        public int getItemViewType(int position) {
            return mViewModel.getSurpriseEggListItemViewModel(position).getItemViewType();
        }

        @Override
        public int getItemCount() {
            return mListSize;
        }

        @Override
        public void onAttachedToRecyclerView(RecyclerView recyclerView) {
            super.onAttachedToRecyclerView(recyclerView);
            RecyclerView.LayoutManager manager = recyclerView.getLayoutManager();
            if (manager instanceof SimpleLayoutManager) {
                final SimpleLayoutManager simpleLayoutManager = (SimpleLayoutManager) manager;
                simpleLayoutManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
                    @Override
                    public int getSpanSize(int position) {
                        return getItemViewType(position) == 0 ? simpleLayoutManager.getSpanCount() : 1;
                    }
                });
            }
        }

        public class SurpriseEggViewHolder extends RecyclerListView.ViewHolder {
            public SurpriseEggViewHolder(View itemView) {
                super(itemView);
            }
        }
    }

    /**
     * 如果RecyclerView中item的间距不都是一样的，需要自定义不同的item之间不同的间距
     */
    public static class SpacesItemDecoration extends RecyclerView.ItemDecoration {

        @Override
        public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, RecyclerView parent, @NonNull RecyclerView.State state) {
            MLog.d(TAG, " getItemOffsets:type=" + parent.getChildAdapterPosition(view) + view);
            if (parent.getChildLayoutPosition(view) == 0) {
                outRect.top = 16;
            }
            if(parent.getAdapter().getItemViewType(parent.getChildLayoutPosition(view)) == SurpriseEggFuncDef.ITEM_TYPE_DATA){
                outRect.bottom = 40;
            }
            else if(parent.getAdapter().getItemViewType(parent.getChildLayoutPosition(view)) == SurpriseEggFuncDef.ITEM_TYPE_TITLE){
                outRect.bottom = 16;
            }
            else {
                outRect.bottom = 40;
            }

            outRect.left = 0;
            outRect.right = 40;
            MLog.d(TAG, " getItemOffsets:outRect=(left,top,right,bottom)" + outRect.left+","+outRect.top+","+outRect.right +","+outRect.bottom);
        }
    }

}