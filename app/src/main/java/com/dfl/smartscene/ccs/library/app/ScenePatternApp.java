package com.dfl.smartscene.ccs.library.app;

import android.app.Application;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.res.Configuration;
import android.os.IBinder;

import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.multidex.MultiDex;

import com.dfl.api.app.eventtrack.ClientId;
import com.dfl.dflcommonlibs.LibApplication;
import com.dfl.dflcommonlibs.commonclass.ActivityManager;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.dflcommonlibs.rxbus.RxBus;
import com.dfl.dflcommonlibs.rxbus.Subscribe;
import com.dfl.dflcommonlibs.uimodeutil.ResourceTagLoadUtil;
import com.dfl.smartscene.BuildConfig;
import com.dfl.smartscene.ccs.base.UIListRefreshHeader;
import com.dfl.smartscene.ccs.busevent.PermissionEvent;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.db.CarConfigUtils;
import com.dfl.smartscene.ccs.eventTrack.BigDataManager;
import com.dfl.smartscene.ccs.model.DriveModel;
import com.dfl.smartscene.ccs.model.manager.LibraryManager;
import com.dfl.smartscene.ccs.service.ScenePatternService;
import com.dfl.smartscene.ccs.util.GlideUtil;
import com.dfl.smartscene.ccs.util.HotFixManager;
import com.dfl.smartscene.ccs.util.Logger;
import com.dfl.smartscene.ccs.util.PermissionManager;
import com.dfl.smartscene.ccs.view.base.UIListRefreshFooter;
import com.iauto.uibase.utils.MLog;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.DefaultRefreshFooterCreator;
import com.scwang.smartrefresh.layout.api.DefaultRefreshHeaderCreator;
import com.scwang.smartrefresh.layout.api.RefreshFooter;
import com.scwang.smartrefresh.layout.api.RefreshHeader;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.secneo.sdk.Helper;
import com.dfl.smartscene.ccs.wrapper.LanYouRequestWrapper;
import com.szlanyou.gwsdk.RequestEnvironment;
import com.szlanyou.gwsdk.bean.AppInfo;
import com.tencent.mmkv.MMKV;
import com.tencent.tinker.entry.ApplicationLike;

import io.reactivex.functions.Consumer;
import io.reactivex.plugins.RxJavaPlugins;

/*
 * ScenePatternApp application class
 * <AUTHOR>
 * @date 2022/4/15
 */
public class ScenePatternApp extends ApplicationLike {
    private static final String TAG = "ScenePatternApp";
    private static Application instance;
    private static ScenePatternApp sScenePatternApp;

    private boolean isGrayEnv = true;

    public ScenePatternApp(Application application, int tinkerFlags, boolean tinkerLoadVerifyFlag, long applicationStartElapsedTime, long applicationStartMillisTime, Intent tinkerResultIntent) {
        super(application, tinkerFlags, tinkerLoadVerifyFlag, applicationStartElapsedTime, applicationStartMillisTime, tinkerResultIntent);
    }

    private Application getApp(){
        return getApplication();
    }

    public static Application getInstance(){
        return instance;
    }

    public static ScenePatternApp getScenePatternApp(){
        return sScenePatternApp;
    }

    @Override
    public void onCreate() {
        MLog.d(TAG, "App_NewScenePattern onCreate()");
        super.onCreate();
        MLog.init("App_NewScenePattern");
        Logger.init();
        instance = getApp();
        sScenePatternApp = this;
        LibApplication.getInstance(getApp()).onCreate();
        //对车型模块传入application
        LibraryApp.setApplication(getApp());
        MMKV.initialize(getApp());
        //本地车型及首页场景数据库的初始化
        CarConfigUtils.copyDBToLocal();
        if(PermissionManager.checkLocalPermission()){
            LogUtil.d(TAG,"init service");
            bindSceneService();
        }

        setRxJavaErrorHandler();
        LibraryManager.getInstance();
        //设置glide缓存大小
        GlideUtil.initGlide();
        //埋点数据
        BigDataManager.create(getApp(), ClientId.CLIENT_ID_DFL_SMARTSCENE);
        //初始化彩蛋模块
        ScenePattern.getInstance().onCreate(instance);

        RxBus.getDefault().register(this);
        HotFixManager.initHotFix(getApplication());
    }

    //联友SDK初始化参数
    private AppInfo mAppInfo;
    /**
     * 联友sdk的参数
     */
    public void initLanYouParas() {
        if(isGrayEnv){
            mAppInfo = new AppInfo(ConstantModelValue.LANYOU_APP_ID,
                    ConstantModelValue.LANYOU_APP_KEY,
                    ConstantModelValue.LANYOU_APP_CODE_NISSAN,
                    ConstantModelValue.LANYOU_PROJECT_TYPE_NISSAN,
                    BuildConfig.VERSION_NAME,
                    DriveModel.getInstance().getDaid(),//DAID，可以为空
                    ConstantModelValue.LIANYOU_APP_SERVER_PUBLIC_KEY,//ServerPubKey
                    ConstantModelValue.LIANYOU_APP_CLIENT_PRIVATE_KEY);//ClientPriKey
            LanYouRequestWrapper.safeInit(getApp(), mAppInfo, RequestEnvironment.GRAY);
        }else {
            mAppInfo = new AppInfo(ConstantModelValue.TEST_LANYOU_APP_ID,
                    ConstantModelValue.TEST_LANYOU_APP_KEY,
                    ConstantModelValue.LANYOU_APP_CODE_NISSAN,
                    ConstantModelValue.LANYOU_PROJECT_TYPE_NISSAN,
                    BuildConfig.VERSION_NAME,
                    DriveModel.getInstance().getDaid(),//DAID，可以为空
                    ConstantModelValue.TEST_LIANYOU_APP_SERVER_PUBLIC_KEY,//ServerPubKey
                    ConstantModelValue.TEST_LIANYOU_APP_CLIENT_PRIVATE_KEY);//ClientPriKey
            LanYouRequestWrapper.safeInit(getApp(), mAppInfo, RequestEnvironment.TEST);
        }
    }

    private void setRxJavaErrorHandler() {
        RxJavaPlugins.setErrorHandler(new Consumer<Throwable>() {
            @Override
            public void accept(Throwable throwable) throws Exception {
                if (null != throwable) {
                    MLog.e(TAG, "rxjava error = " + throwable.getMessage());
                }
            }
        });
    }


    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        ResourceTagLoadUtil.refreshAllView();
        //通知彩蛋模式换肤
        ScenePattern.getInstance().onConfigurationChanged(newConfig);
    }


    @Override
    public void onBaseContextAttached(Context base) {
        super.onBaseContextAttached(base);
        LogUtil.d(TAG,"attachBaseContext");
        MultiDex.install(getApp());
        Helper.install(getApp());

    }

    public static FragmentManager getFragmentManager() {
        if (ActivityManager.getInstance().getCurrentActivity() != null) {
            if (ActivityManager.getInstance().getCurrentActivity() instanceof FragmentActivity) {
                return ((FragmentActivity) ActivityManager.getInstance().getCurrentActivity()).getSupportFragmentManager();
            } else {
                return null;
            }

        } else {
            return null;
        }
    }

    //static 代码段可以防止内存泄露
    static {
        //设置全局的Header构建器
        SmartRefreshLayout.setDefaultRefreshHeaderCreator(new DefaultRefreshHeaderCreator() {
            @Override
            public RefreshHeader createRefreshHeader(Context context, RefreshLayout layout) {
                return new UIListRefreshHeader(context);
            }
        });
        //设置全局的Footer构建器
        SmartRefreshLayout.setDefaultRefreshFooterCreator(new DefaultRefreshFooterCreator() {
            @Override
            public RefreshFooter createRefreshFooter(Context context, RefreshLayout layout) {
                return new UIListRefreshFooter(context);
            }
        });
    }

    @Subscribe
    public void PermissionListener(PermissionEvent permissionEvent){
        if(permissionEvent.isStatus()){
            LogUtil.d(TAG,"permission init service");
            bindSceneService();
        }
    }

    private void bindSceneService(){
        Intent intent = new Intent(getApp(), ScenePatternService.class);
        intent.setAction("com.dfl.newscenepattern.actionScene");
        getApp().bindService(intent, mServiceConnection, Context.BIND_AUTO_CREATE);
    }

    ServiceConnection mServiceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {

        }

        @Override
        public void onServiceDisconnected(ComponentName name) {

        }
    };

}
