package com.dfl.smartscene.ccs.view.operationview;

import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.fastjson.JSON;
import com.dfl.api.app.navi.account.IAccount;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.model.NaviModel;
import com.dfl.smartscene.ccs.model.manager.CustomApiManager;
import com.dfl.smartscene.ccs.navi.FavoriteItemBean;
import com.dfl.smartscene.ccs.util.ListUtil;
import com.dfl.smartscene.ccs.view.adapter.NaviCollectionRecyclerAdapter;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseDialogFragment;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseView;
import com.dfl.smartscene.ccs.viewmodel.NaviViewModel;
import com.google.gson.Gson;
import com.iauto.uibase.utils.MLog;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/9/20
 * @description ：导航地点收藏页面
 */
public class NaviCollectOperation implements OperationBaseView {

    private static final String TAG = NaviCollectOperation.class.getSimpleName();
    private final FavoriteItemBean mDeafultFavoriteItemBean;
    private View mMissingDataView;
    private RecyclerView mRecyclerView;
    private NaviCollectionRecyclerAdapter mAdapter;

    private final SingleOperation mSingleOperation;

    private NaviViewModel mNaviViewModel;

    private Fragment container;


    private ConstraintLayout mEnsureView;
    private TextView mEnsureTextView;


    @Override
    public List<String> extractArgs() {
        ArrayList<String> values = new ArrayList<>();
        FavoriteItemBean selectData = mAdapter.getSelectData();
        if (null != selectData) {
            values.add(new Gson().toJson(selectData));
            MLog.d(TAG, "values:" + selectData);
        }
        return values;
    }

    @Override
    public String extractDesc() {
        String desc = mSingleOperation.getArg(ConstantModelValue.OUTPUT_DESC_TEXT);
        if (!TextUtils.isEmpty(desc)) {
            FavoriteItemBean selectData = mAdapter.getSelectData();
            if (null != selectData) {
                MLog.d(TAG, "desc:" + desc);
                desc = desc.replaceFirst("&", selectData.getFavoriteItemName());
            }
        }
        return desc;
    }

    public NaviCollectOperation(SingleOperation singleOperation, ViewGroup parent) {
        mSingleOperation = singleOperation;
        mEnsureView = (ConstraintLayout) findButtonOperationBaseDialogPositive(parent);
        if (mEnsureView != null) {
            mEnsureTextView = (TextView) mEnsureView.getChildAt(0);
            MLog.d(TAG, " find mEnsureView: " + mEnsureTextView);
        } else {
            MLog.d(TAG, "not find mEnsureView: ");
        }
        container = FragmentManager.findFragment(parent);
        mNaviViewModel = new ViewModelProvider(container).get(NaviViewModel.class);
        LayoutInflater.from(parent.getContext()).inflate(R.layout.item_operation_navi_collection_layout, parent, true);
        mMissingDataView = parent.findViewById(R.id.relativelayout_item_operation_navi_collection_layout_data_missing_layout);

        parent.findViewById(R.id.textview_item_operation_navi_collection_layout_data_missing_btn).setOnClickListener(view -> {
            IAccount iAccount = CustomApiManager.getInstance().getIAccount();
            if (null != iAccount) {
                try {
                    iAccount.showView(6);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                MLog.d(TAG, "show Navi Collection View error, iAccount = null.");
            }

        });

        mRecyclerView = parent.findViewById(R.id.recyclerview_item_operation_navi_collection_destination_container);
        mAdapter = new NaviCollectionRecyclerAdapter();
        mRecyclerView.setLayoutManager(new LinearLayoutManager(parent.getContext()));
        mRecyclerView.setAdapter(mAdapter);

        mAdapter.setOnItemClickListener((view, viewType, commonFavoriteBean, position) -> {
            mAdapter.setSelectedPos(position);
        });

        MLog.d(TAG, "iNaviSceneSetting = null");
        setConfirmBtnTextAndClickEventIsToNavi(true);
        mMissingDataView.setVisibility(View.VISIBLE);
        mRecyclerView.setVisibility(View.GONE);
        initObservable();
        try {
            MLog.d(TAG, "iNaviSceneSetting()");

            String naviCollectedPlaces = NaviModel.getInstance().getNaviCollectPlace();
            MLog.d(TAG, "getNaviCollectedPlaces() = " + naviCollectedPlaces);

        } catch (Exception e) {
            MLog.e(TAG, "naviCollectedPlaces = " + e);
            e.printStackTrace();
            setConfirmBtnTextAndClickEventIsToNavi(true);
            mMissingDataView.setVisibility(View.VISIBLE);
            mRecyclerView.setVisibility(View.GONE);
        }
        String defaultValueStr = singleOperation.getArg(ConstantModelValue.VIEW_DEFAULT_POS);
        mDeafultFavoriteItemBean = JSON.parseObject(defaultValueStr, FavoriteItemBean.class);
        setDefaultCheckItem();
    }

    private void initObservable() {
        mNaviViewModel.getCollectPlace().observe(container.getViewLifecycleOwner(), new Observer<String>() {
            @Override
            public void onChanged(String json) {
                try {
                    if (!TextUtils.isEmpty(json)) {
                        MLog.d(TAG, "naviCollectedPlaces = " + json);

                        List<FavoriteItemBean> favoriteList = JSON.parseArray(json, FavoriteItemBean.class);

                        if (favoriteList != null && !favoriteList.isEmpty()) {
                            favoriteList.sort((o1, o2) -> {
                                // 家、公司、普通点
                                int common_name1 = o1.getPoiType();
                                int common_name2 = o2.getPoiType();
                                return common_name2 - common_name1;
                            });

                            new Handler(Looper.getMainLooper()).post(() -> {
                                setConfirmBtnTextAndClickEventIsToNavi(false);
                                mMissingDataView.setVisibility(View.GONE);
                                mRecyclerView.setVisibility(View.VISIBLE);
                                mAdapter.setDataList(favoriteList);
                                setDefaultCheckItem();
                            });
                            return;
                        }
                    }

                    new Handler(Looper.getMainLooper()).post(() -> {
                        setConfirmBtnTextAndClickEventIsToNavi(true);
                        mMissingDataView.setVisibility(View.VISIBLE);
                        mRecyclerView.setVisibility(View.GONE);
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        });
    }

    /**
     * 查找确定按钮，因为存在多布局的情况，所以采用遍历父布局查询
     * @param view
     * @return
     */
    private View findButtonOperationBaseDialogPositive(View view) {
        // 检查输入的 view 是否为 null
        if (view == null) {
            MLog.d(TAG, "findButtonOperationBaseDialogPositive: null");
            return null;
        }

        // 获取视图的父视图
        ViewParent parent = view.getParent();

        // 循环查找，直到没有父视图（到达根布局）
        while (parent != null && parent instanceof ViewParent) {
            View parentView = (View) parent;

            // 检查当前父视图是否是目标视图
            View targetView = parentView.findViewById(R.id.button_operation_base_dialog_positive);
            if (targetView != null) {
                return targetView; // 找到目标视图，返回
            }

            // 继续向上查找
            parent = parent.getParent();
        }

        // 如果没有找到，返回 null
        return null;
    }


    /**
     * 更新按钮文言和点击事件是否是前往导航
     * @param isToNavi
     */
    private void setConfirmBtnTextAndClickEventIsToNavi(boolean isToNavi){
        if(container == null || mEnsureView == null || mEnsureTextView == null){
            return;
        }
        if(isToNavi){
            mEnsureTextView.setText(ScenePatternApp.getInstance().getText(R.string.string_item_operation_navi_collection_layout_data_missing_btn));
            mEnsureView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    IAccount iAccount = CustomApiManager.getInstance().getIAccount();
                    if (null != iAccount) {
                        try {
                            iAccount.showView(6);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    } else {
                        MLog.d(TAG, "show Navi Collection View error, iAccount = null.");
                    }
                }
            });
        }else {
            mEnsureTextView.setText("确定");
            mEnsureView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    ((OperationBaseDialogFragment)container).onPositiveButtonClick();
                }
            });
        }
    }

    private int findIndexByAddress(String address){
        int index = -1;
        if(mAdapter == null || address.isEmpty()){
            return index;
        }
        if(ListUtil.isEmpty(mAdapter.getDataList())){
            return index;
        }
        for (int i = 0; i < mAdapter.getDataList().size(); i++) {
            if(address.equals(mAdapter.getDataList().get(i).getAddress())){
                index = i;
                break;
            }
        }
        return index;
    }
    private int findIndexByPoiType(int poiType){
        int index = -1;
        if(mAdapter == null ){
            return index;
        }
        if(ListUtil.isEmpty(mAdapter.getDataList())){
            return index;
        }
        for (int i = 0; i < mAdapter.getDataList().size(); i++) {
            if(poiType==(mAdapter.getDataList().get(i).getPoiType())){
                index = i;
                break;
            }
        }
        return index;
    }

    private void setDefaultCheckItem() {
        if (mDeafultFavoriteItemBean != null && mAdapter != null && !ListUtil.isEmpty(mAdapter.getDataList())) {
            int index1 = findIndexByAddress(mDeafultFavoriteItemBean.getAddress());
            if (index1 != -1) {
                mAdapter.setSelectedPos(index1);
            } else {
                int index2 = findIndexByPoiType(mDeafultFavoriteItemBean.getPoiType());
                if (index2 != -1) {
                    mAdapter.setSelectedPos(index2);
                }
            }
        }
    }

    @Override
    public void onConfigurationChanged() {

    }
}
