package com.dfl.smartscene.ccs.wrapper;

import com.alibaba.fastjson.JSONObject;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.dflcommonlibs.rxbus.RxBus;
import com.dfl.smartscene.ccs.busevent.PhoneUpdateEvent;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.model.DriveModel;

import org.eclipse.paho.android.service.MqttAndroidClient;
import org.eclipse.paho.client.mqttv3.IMqttActionListener;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.IMqttToken;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.Mqtt<PERSON>;

import java.util.UUID;

/**
 * <AUTHOR>
 * @date ：2023/3/21 15:15
 * @description ：
 */
public class MqttWrapper {
    private static final String TAG = "MqttWrapper";
    private static volatile MqttWrapper sInstance;

    public static MqttWrapper getInstance() {
        if (null == sInstance) {
            synchronized (MqttWrapper.class) {
                if (null == sInstance) {
                    sInstance = new MqttWrapper();
                }
            }
        }
        return sInstance;
    }

    private String host = "tcp://vitmqttzh.dongfeng-nissan.com.cn:1883"; //服务器地址（协议+地址+端口号）

    private String mTopic;//订阅得1话题
    private String clientId;

    private MqttAndroidClient client;//mqtt客户端对象
    private MqttConnectOptions conOpt;//MQTT连接选项对象

    //创建连接（初始化MQTT客户端）
    public void createConnect() {
        //客户端ID(设备唯一识别号，需要先给权限)
        clientId = DriveModel.getInstance().getDaid();

        mTopic = "da/" + DriveModel.getInstance().getDaid().toLowerCase() + "/down/scene";

        //创建MQTT连接选项对象，并初始化
        conOpt = new MqttConnectOptions();

        conOpt.setCleanSession(true);// 清除缓存
        conOpt.setConnectionTimeout(10);//设置超时时间，单位：秒
        conOpt.setAutomaticReconnect(true);//设置如果连接丢失，客户端是否会自动尝试重新连接到服务器

        //创建MQTT客户端对象
        client = new MqttAndroidClient(ScenePatternApp.getInstance(), host, UUID.randomUUID().toString());

        // 设置MQTT监听并且接受消息
        client.setCallback(mqttCallback);

        //连接MQTT服务器
        doConnect();
    }

    public boolean isConnected(){
        return null != client && client.isConnected();
    }


    //连接MQTT服务器
    public void doConnect() {
        if (client != null && !client.isConnected()) {
            try {
                /**
                 * options：用来携带连接服务器的一系列参数，例如用户名、密码等
                 * userContext：可选对象，用于向回调传递上下文。一般传null即可
                 * callback：用来监听MQTT是否连接成功的回调
                 * */
                client.connect(conOpt, ScenePatternApp.getInstance(), iMqttActionListener);

            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }

    // MQTT监听并且接受消息
    private MqttCallback mqttCallback = new MqttCallback() {
        @Override
        public void connectionLost(Throwable cause) {
            LogUtil.d(TAG, "connectionLost: 连接断开");
        }

        @Override
        public void messageArrived(String topic, MqttMessage message) throws Exception {
            String msg = new String(message.getPayload());
            LogUtil.d(TAG, "消息到达 : topic : " + topic + " , message : " + msg);
            if(topic.equals(mTopic)){
                JSONObject jsonObject = JSONObject.parseObject(msg);
                boolean update = jsonObject.getBoolean("update");
                if(update){
                    RxBus.getDefault().post(new PhoneUpdateEvent());
                }
            }
        }

        @Override
        public void deliveryComplete(IMqttDeliveryToken token) {

        }
    };

    //连接服务器的回调
    private IMqttActionListener iMqttActionListener = new IMqttActionListener() {

        @Override
        //连接成功
        public void onSuccess(IMqttToken arg0) {
            LogUtil.d(TAG, "连接成功 ");
            try {
                // 订阅话题
                client.subscribe(mTopic, 0, ScenePatternApp.getInstance(), new IMqttActionListener() {
                    @Override
                    public void onSuccess(IMqttToken asyncActionToken) {
                        LogUtil.d(TAG,"订阅成功");
                    }

                    @Override
                    public void onFailure(IMqttToken asyncActionToken, Throwable exception) {

                    }
                });
            } catch (MqttException e) {
                e.printStackTrace();
            }
        }

        @Override
        // 连接失败，重连
        public void onFailure(IMqttToken arg0, Throwable arg1) {

//            doConnect()	//连接失败，重连（可关闭服务器进行模拟）

            LogUtil.d(TAG, "连接失败 ");
        }
    };



    //取消连接
    public void disConnect() throws MqttException {
        if (client != null && client.isConnected()) {
            client.disconnect();
        }
    }

    //断开连接
    public void close() {
        if (client != null && client.isConnected()) {
            try {
                client.disconnect();
            } catch (MqttException e) {
                e.printStackTrace();
            }
        }
    }
}
