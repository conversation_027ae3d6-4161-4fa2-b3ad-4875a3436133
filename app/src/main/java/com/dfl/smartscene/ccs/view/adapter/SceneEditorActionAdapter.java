package com.dfl.smartscene.ccs.view.adapter;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;


import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseAdapter;
import com.dfl.smartscene.ccs.base.BaseHolder;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.constant.ConstantViewValue;
import com.dfl.smartscene.ccs.factory.SpecialBeanFactory;
import com.dfl.smartscene.ccs.model.AiotModel;
import com.dfl.smartscene.ccs.model.manager.CarConfigManager;
import com.dfl.smartscene.ccs.util.ImageUtil;
import com.dfl.smartscene.ccs.util.ListUtil;
import com.dfl.smartscene.ccs.view.AnimManager;
import com.dfl.smartscene.ccs.view.base.AdapterItemDrag;
import com.iauto.uibase.utils.MLog;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Zezheng
 * @date ：2022/12/7 14:21
 * @description ：场景编辑页面，执行操作item的adapter
 */
public class SceneEditorActionAdapter extends BaseAdapter<SettingOperation> implements AdapterItemDrag {

    private String mSceneType = ConstantViewValue.SCENE_EDITOR_TYPE_SCENE_ADD;

    public void setSceneType(String sceneType) {
        this.mSceneType = sceneType;
    }

    public static final int VIEW_TYPE_NORMAL = 0;
    public static final int VIEW_TYPE_ADD = 1;

    private boolean editMode = false;

    public boolean isEditMode() {
        return editMode;
    }

    public void setEditMode(boolean editMode) {
        this.editMode = editMode;
        if(editMode){
            if(checkAddExist(mDataList)){
                mDataList.remove(getItemCount() - 1);
            }
        }else {
            if(checkAddExist(mDataList)){
                mDataList.add(SpecialBeanFactory.productAddActionOperation());
            }
        }
        notifyDataSetChanged();
    }

    private boolean checkAddExist(List<SettingOperation> settingOperations){
        return !ListUtil.isEmpty(settingOperations) && settingOperations.get(settingOperations.size() - 1).getOperationId().equals(ConstantModelValue.OPERATION_ID_ADD);
    }


    public void onItemMove(RecyclerView.ViewHolder source, RecyclerView.ViewHolder target) {
        int fromPosition = source.getAdapterPosition();
        int toPosition = target.getAdapterPosition();
        if (fromPosition < getDataList().size() && toPosition < getDataList().size()) {
            if(fromPosition < toPosition){
                for(int i = fromPosition ; i < toPosition ; i++){
                    Collections.swap(getDataList(), i, i+1);
                }
            }else if(fromPosition > toPosition){
                for(int i = fromPosition ; i > toPosition ; i--){
                    Collections.swap(getDataList(), i, i-1);
                }
            }
            notifyItemMoved(fromPosition, toPosition);
            notifyItemChanged(fromPosition);
            notifyItemChanged(toPosition);

        }
    }

    @Override
    public void onItemClear(RecyclerView.ViewHolder source) {
        notifyDataSetChanged();
    }

    public interface OnItemCountChangeListener {
        void onItemCountChange(int count);
    }


    private OnItemCountChangeListener mOnItemCountChangeListener;

    public void setOnItemCountChangeListener(OnItemCountChangeListener onItemCountChangeListener) {
        mOnItemCountChangeListener = onItemCountChangeListener;
    }

    @Override
    public void setDataList(List<SettingOperation> dataList) {
        super.setDataList(dataList);
        notifyItemCountChange();
    }

    private void notifyItemCountChange(){
        if(mOnItemCountChangeListener != null){
            mOnItemCountChangeListener.onItemCountChange(getItemCount());
        }
    }



    @Override
    public void addData(SettingOperation data) {
        super.addData(data);
        notifyItemCountChange();
    }

    @Override
    public void removeData(int position) {
        super.removeData(position);
        notifyItemCountChange();
        notifyItemRangeChanged(position,getItemCount());
    }

    @Override
    protected BaseHolder<SettingOperation> getViewHolder(View view, int viewType) {
        return VIEW_TYPE_ADD == viewType ? new SceneEditorActionAddHolder(view) : new SceneEditorActionHolder(view);
    }

    @Override
    protected int getLayoutId(int viewType) {
        return VIEW_TYPE_ADD == viewType ? R.layout.item_scene_editor_action_add : R.layout.item_scene_editor_action_normal;
    }

    @Override
    public int getItemViewType(int position) {
        return getItemData(position).getOperationId().equals(ConstantModelValue.OPERATION_ID_ADD) ? VIEW_TYPE_ADD : VIEW_TYPE_NORMAL;
    }

    class SceneEditorActionHolder extends BaseHolder<SettingOperation>{

        private TextView mTextViewDesc;
        protected ImageView mImageButtonDelete;
        private TextView mTextViewOrder;
        private TextView mTextViewTitle;

        public SceneEditorActionHolder(View itemView) {
            super(itemView);
            mTextViewDesc = itemView.findViewById(R.id.textview_item_scene_editor_desc);
            mImageButtonDelete = itemView.findViewById(R.id.imagebutton_item_scene_editor_delete);
            mTextViewOrder = itemView.findViewById(R.id.textview_item_scene_editor_order);
            mTextViewTitle = itemView.findViewById(R.id.textview_item_scene_editor_title);
            mImageButtonDelete.setOnClickListener(v -> {
                onClickDeleteButton(getAdapterPosition());
            });

        }

        @Override
        public void setupData(SettingOperation settingOperation, int position) {
            mTextViewOrder.setText(String.valueOf(position + 1));
            if(settingOperation.getDesc() != null){
                String[] descs = settingOperation.getDesc().split(" ",2);
                mTextViewTitle.setText(descs[0]);
                if(descs.length == 2){
                    String trimmedString = descs[1].replaceAll("^\\s+", "");
                    MLog.d("wqm", "setupData: "+descs[1]+", ,"+trimmedString);
                    mTextViewDesc.setText(trimmedString);
                }else {//不加上判断会显示上一次的内容
                    mTextViewDesc.setText("");
                }
            }
            if (Objects.equals(mSceneType,ConstantViewValue.SCENE_EDITOR_TYPE_SCENE_SHOW)){
                mImageButtonDelete.setVisibility(View.GONE);
            }else {
                if (editMode) {
                    mImageButtonDelete.setVisibility(View.GONE);
                    mImageButtonDelete.setEnabled(false);
                    AnimManager.startShakeAnim(itemView);
                } else {
                    mImageButtonDelete.setVisibility(View.VISIBLE);
                    mImageButtonDelete.setEnabled(true);
                    AnimManager.stopShakeAnim(itemView);
                }
            }
            if(settingOperation.getDeviceId().equals(ConstantModelValue.DEVICE_ID_AIOT)){
                ImageUtil.loadActionBg( AiotModel.getInstance().findAiotIconByDesc(settingOperation.getDesc()), itemView);
            }else {
                ImageUtil.loadActionBg( CarConfigManager.getInstance().findOperationIconById(settingOperation.getOperationId()),itemView);
            }
        }

        protected void onClickDeleteButton(int position) {
            if(mOnButtonClickListener != null){
                mOnButtonClickListener.onItemClick(mImageButtonDelete,getItemViewType(),getItemData(position),position);
            }
        }

    }

    class SceneEditorActionAddHolder extends BaseHolder<SettingOperation>{

        public SceneEditorActionAddHolder(View itemView) {
            super(itemView);
        }

        @Override
        public void setupData(SettingOperation settingOperation, int position) {
            if(editMode){
                itemView.setVisibility(View.GONE);
            }else {
                itemView.setVisibility(View.VISIBLE);
            }
        }
    }
}
