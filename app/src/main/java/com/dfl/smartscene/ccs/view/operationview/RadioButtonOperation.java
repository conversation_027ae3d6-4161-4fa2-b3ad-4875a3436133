package com.dfl.smartscene.ccs.view.operationview;

import android.content.Context;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RadioButton;

import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.util.DataTypeFormatUtil;
import com.dfl.smartscene.ccs.util.ListUtil;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseView;
import com.dfl.smartscene.ccs.view.weight.CustomRadioGroup;
import com.iauto.uibase.utils.MLog;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/9/19
 * @description ：单选页面
 */
public class RadioButtonOperation implements OperationBaseView, ConflictViewClient, ConflictViewService {

    private static final String TAG = RadioButtonOperation.class.getSimpleName();

    private final SingleOperation mSingleOperation;
    private final ViewGroup mParentView;
    private CustomRadioGroup mCustomRadioGroup;

    @Override
    public List<String> extractArgs() {
        ArrayList<String> values = new ArrayList<>(1);
        int checkedRadioButtonId = mCustomRadioGroup.getCheckedRadioButtonId();
        List<String> args = mSingleOperation.getListArg(ConstantModelValue.DATA_DESC_LIST);

        if (checkedRadioButtonId > -1 && args != null) {
            RadioButton childAt = (RadioButton) mCustomRadioGroup.getChildAt(checkedRadioButtonId);
            int id = childAt.getId();
            if (id >= 0 && id < args.size()) {
                values.add(args.get(id));
            }
        }
        return values;
    }

    @Override
    public String extractDesc() {
        String desc = mSingleOperation.getArg(ConstantModelValue.OUTPUT_DESC_TEXT);
        if(desc == null || !desc.contains("&")){
            return desc;
        }
        if (mCustomRadioGroup != null) {
            int checkedRadioButtonId = mCustomRadioGroup.getCheckedRadioButtonId();
            if (checkedRadioButtonId > -1) {
                RadioButton childAt = (RadioButton) mCustomRadioGroup.getChildAt(checkedRadioButtonId);
                List<String> descs = mSingleOperation.getListArg(ConstantModelValue.VIEW_DESC_TEXT_LIST);
                desc = desc.replaceFirst("&", descs.get(mCustomRadioGroup.getCurPosition()));
            }
        }
        return desc;
    }

    public RadioButtonOperation(SingleOperation singleOperation, ViewGroup parent) {
        mParentView = parent;
        mSingleOperation = singleOperation;
        initRadioButton(singleOperation, parent);

    }

    private void initRadioButton(SingleOperation singleOperation, ViewGroup parent) {

        String defaultStr = singleOperation.getArg(ConstantModelValue.VIEW_DEFAULT_POS);

        MLog.d(TAG, "defaultStr = " + defaultStr);

        Context context = parent.getContext();

        LinearLayout linearLayout = new LinearLayout(context);
        linearLayout.setGravity(LinearLayout.VERTICAL);
        linearLayout.setGravity(Gravity.CENTER);
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, 56);
        params.gravity = Gravity.CENTER;
        parent.addView(linearLayout, params);

        mCustomRadioGroup = new CustomRadioGroup(context);
        linearLayout.addView(mCustomRadioGroup, new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, 56));

        if (ConstantModelValue.VIEW_TYPE_RADIO_TEXT_GROUP.equals(singleOperation.getViewType())) {
            List<String> view_desc_text_list = singleOperation.getListArg(ConstantModelValue.VIEW_DESC_TEXT_LIST);
            mCustomRadioGroup.setTextList(view_desc_text_list, singleOperation.getViewType());
        } else {
            List<String> view_desc_pic_list = singleOperation.getListArg(ConstantModelValue.VIEW_DESC_PIC_LIST);
            mCustomRadioGroup.setTextList(view_desc_pic_list, singleOperation.getViewType());
        }
        int defaultPos = DataTypeFormatUtil.stringFindIndex(defaultStr, singleOperation.getListArg(ConstantModelValue.DATA_DESC_LIST));
        if(defaultPos < 0){
            defaultPos = 0;
        }

        List<String> args = mSingleOperation.getListArg(ConstantModelValue.DATA_DESC_LIST);
        mCustomRadioGroup.setCheckedCallBack(new CustomRadioGroup.CheckedCallBack() {
            @Override
            public void radioButtonChecked(int position, String name) {
                if (mArgChangeListeners != null) {
                    mArgChangeListeners.forEach(item -> item.onArgChanged(args.get(position)));
                }
            }
        });
        // 设置默认选中值
        mCustomRadioGroup.setRadioButtonChecked(defaultPos);

    }

    private List<ArgChangeListener> mArgChangeListeners;

    @Override
    public void setViewUnabled(boolean status) {
        mCustomRadioGroup.setEnabled(!status);
        unAbleStatus = status;
    }

    boolean unAbleStatus = false;

    @Override
    public boolean isViewUnable() {
        return unAbleStatus;
    }


    @Override
    public void addArgChangeLintener(ArgChangeListener argChangeLintener) {
        if (mArgChangeListeners == null) {
            mArgChangeListeners = new ArrayList<>();
        }
        mArgChangeListeners.add(argChangeLintener);

        if (null != mCustomRadioGroup) {
            int curPosition = mCustomRadioGroup.indexOfChild(mCustomRadioGroup.findViewById(mCustomRadioGroup.getCheckedRadioButtonId()));
//            List<String> strings = extractArgs();
            List<String> strings = mSingleOperation.getListArg(ConstantModelValue.DATA_DESC_LIST);
            if (ListUtil.checkIndex(strings, curPosition)) {
                argChangeLintener.onArgChanged(strings.get(curPosition));
            }
        }
    }

    @Override
    public void onConfigurationChanged() {
        if (mParentView != null && mSingleOperation != null) {
            mParentView.removeAllViews();
            initRadioButton(mSingleOperation, mParentView);

            List<String> args = mSingleOperation.getListArg(ConstantModelValue.DATA_DESC_LIST);
            if (mArgChangeListeners != null) {
                mArgChangeListeners.forEach(item -> item.onArgChanged(args.get(0)));
            }

        }

    }

}
