package com.dfl.smartscene.ccs.navi;

import java.io.Serializable;

/**
 * 收藏点信息数据实体<P/>
 * <AUTHOR>
 * @date 2022-10-13
 */
public class FavoriteItemBean implements Serializable {
    /**
     * 收藏点类型：家、公司、其他
     */
    private @ConditionBean.PoiCategory int poiType;

    /**
     * 收藏点类型描述
     */
    private @PoiTypeCategory String poiCategory;

    /**
     * 收藏点地址描述
     */
    private String address;

    /**
     * 收藏点坐标信息
     */
    private POI poi;

    /**
     * 收藏点名称(由用户自定义)
     */
    private String favoriteItemName;

    public int getPoiType() {
        return poiType;
    }

    public void setPoiType(int poiType) {
        this.poiType = poiType;
    }

    public String getPoiCategory() {
        return poiCategory;
    }

    public void setPoiCategory(String poiCategory) {
        this.poiCategory = poiCategory;
    }

    public POI getPoi() {
        return poi;
    }

    public void setPoi(POI poi) {
        this.poi = poi;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getFavoriteItemName() {
        return favoriteItemName;
    }

    public void setFavoriteItemName(String favoriteItemName) {
        this.favoriteItemName = favoriteItemName;
    }

    /**
     * 收藏点类型描述的枚举
     */
    public @interface PoiTypeCategory{
        String HOME = "家";
        String COMPANY = "公司";
        String OTHERS = "其他收藏点";
    }

    /**
     * 收藏点信息加载状态
     */
    public @interface FavoriteLoadStatus{
        /**
         * 状态：加载中
         */
        String LOADING = "0";
    }
}
