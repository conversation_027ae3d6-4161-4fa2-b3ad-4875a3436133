package com.dfl.smartscene.ccs.view.fragment;

import android.content.res.Configuration;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.dflcommonlibs.network.NetworkUtils;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.bean.SceneCategory;
import com.dfl.smartscene.ccs.constant.ConstantViewValue;
import com.dfl.smartscene.ccs.eventTrack.BigDataManager;
import com.dfl.smartscene.ccs.model.SceneDataModel;
import com.dfl.smartscene.ccs.model.SurpriseEggList;
import com.dfl.smartscene.ccs.model.VRModel;
import com.dfl.smartscene.ccs.util.DescriptionUtil;
import com.dfl.smartscene.ccs.util.ExpandTouchArea;
import com.dfl.smartscene.ccs.util.HandlerUtil;
import com.dfl.smartscene.ccs.util.NightUtil;
import com.dfl.smartscene.ccs.view.MyHeaderView;
import com.dfl.smartscene.ccs.view.ViewControlManager;
import com.dfl.smartscene.ccs.view.adapter.LibraryAdapter;
import com.dfl.smartscene.ccs.view.base.BaseFragment;
import com.dfl.smartscene.ccs.viewmodel.LibraryViewModel;
import com.dfl.smartscene.ccs.viewmodel.MyViewModel;
import com.dfl.smartscene.ccs.viewmodel.SurpriseEggListVM;
import com.iauto.uicontrol.RecyclerListView;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshListener;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Zezheng
 * @date ：2022/8/29 10:44
 * @description ：发现页面
 */
public class LibraryFragment extends BaseFragment {
    private static final String TAG = "LibraryFragment";
    private LibraryViewModel mLibraryViewModel;
    private MyViewModel mMyViewModel;

    private RecyclerListView mLinearLayoutContent;
    private TextView adButton;
    private SmartRefreshLayout mSmartRefreshLayout;
    private List<LibraryModuleInterface> mLibraryModuleInterfaces = new ArrayList<>();
    private List<SceneCategory> mListSceneCategory;
    private ScrollView mScrollView;
    private View adView;
    private float previousY;
    private float currentY;
    private LibraryAdapter libraryAdapter;
    private SurpriseEggListVM surpriseEggListVM;
    @Override
    protected View getContentPageView() {
        return getView().findViewById(R.id.layout_content);
    }

    @Override
    protected void init() {
        mLibraryViewModel = new ViewModelProvider(this).get(LibraryViewModel.class);
        surpriseEggListVM = new ViewModelProvider(requireActivity()).get(SurpriseEggListVM.class);
        mMyViewModel = new ViewModelProvider(this).get(MyViewModel.class);
        mMyViewModel.requestUserScenes(true);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_library, container, false);
    }

    @Override
    protected void initView(View view) {
        adView = view.findViewById(R.id.layout_library_fragment_ad);
        mScrollView = view.findViewById(R.id.layout_content);
        mLinearLayoutContent = view.findViewById(R.id.layout_library_content);
        adButton = view.findViewById(R.id.button_library_fragment_ad);
        libraryAdapter = new LibraryAdapter(this);
        mLinearLayoutContent.setAdapter(libraryAdapter);
        //设置去购买按键的可见即可说标签
        DescriptionUtil.generateButtonDescriptionVisibleToSay(adButton.getText().toString(), adButton);
        mSmartRefreshLayout = view.findViewById(R.id.smartrefresh_library);
        ExpandTouchArea.expandTouchArea(view.findViewById(R.id.imageview_library_fragment_ad_close), 20);
        view.findViewById(R.id.imageview_library_fragment_ad_close).setOnClickListener(v -> {
            if (adView.getAlpha() < 1){
                return;
            }
            adView.setVisibility(View.GONE);
            //设置广告位的屏蔽不显示的时间
            mLibraryViewModel.encodeAdDisplayIgnoreTime();
        });
        //如果广告忽略仍在有效期内，不显示
        if(mLibraryViewModel.isAdDisplayIgnore()){
            adView.setVisibility(View.VISIBLE);
        }
        adButton.setOnClickListener(v -> {
            if (adView.getAlpha() < 1){
                return;
            }
            ViewControlManager.openAiotStorePage();
            //埋点：点击去购买
            BigDataManager.getInstance().writeEventMod3(getString(R.string.scene_click_buy));
        });
        mSmartRefreshLayout.setRefreshHeader(new MyHeaderView(getContext()));
        mSmartRefreshLayout.setEnableLoadMore(true);
        mSmartRefreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                mLibraryViewModel.refreshLibraryScenes();
            }
        });
        mScrollView.setOnScrollChangeListener(new View.OnScrollChangeListener() {
            @Override
            public void onScrollChange(View v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
                LogUtil.d(TAG, "onScrollChange " + scrollY);
                float alpha = 1 - (Math.min(scrollY, 140) / 140f);
                adView.setAlpha(alpha);
            }
        });
    }

    @Override
    protected void initObserver() {
        //监听广场数据，添加分类模块
        mLibraryViewModel.getSceneCategories().observe(getViewLifecycleOwner(), new Observer<List<SceneCategory>>() {
            @Override
            public void onChanged(List<SceneCategory> sceneCategories) {
                LogUtil.d(TAG, "sceneCategories.size ----刷新 = " + sceneCategories);
                if (sceneCategories == null) {
                    mSmartRefreshLayout.finishRefresh(false);
                    showErrorPage();
                    return;
                }
                if (!NetworkUtils.getNetworkAvailable(getContext())){
                    showErrorPage();
                    return;
                }
                if (sceneCategories.size() == 0) {
                    mSmartRefreshLayout.finishRefreshWithNoMoreData();
                    showNoContentPage();
                    return;
                }
                mSmartRefreshLayout.finishRefresh(true);
                LogUtil.d(TAG, "sceneCategories.size = " + sceneCategories.size());
                SceneDataModel.getInstance().addSquareSceneCollectState(sceneCategories);
                mLibraryModuleInterfaces.clear();
                libraryAdapter.setDataList(sceneCategories);
                /*mLinearLayoutContent.removeAllViews();
                for (SceneCategory sceneCategory : sceneCategories) {
                    LibraryModuleInterface libraryModuleInterface = null;
                    //区分卡片模块与普通模块
                    if (ConstantModelValue.SCENE_CATEGORY_ID_LIBRARY_CARD.equals(sceneCategory.getCategoryId()) && CarConfigUtil.isCCS()) {
                        LogUtil.d(TAG,"LibraryCardModule, scene size = "+sceneCategory.getSceneBeanList().size());
                        libraryModuleInterface = new LibraryCardModule(LibraryFragment.this, mLinearLayoutContent, sceneCategory);
                    } else {
                        LogUtil.d(TAG,"LibraryModule, scene size = "+sceneCategory.getSceneBeanList().size());
                        libraryModuleInterface = new LibraryModule(LibraryFragment.this, mLinearLayoutContent, sceneCategory);
                    }
                    mLinearLayoutContent.addView(libraryModuleInterface.getContentView());
                    mLibraryModuleInterfaces.add(libraryModuleInterface);
                }*/
                showContentPage();
            }
        });

        surpriseEggListVM.getSurpriseEggListDspStatus().observe(getViewLifecycleOwner(), new Observer<Integer>() {
            @Override
            public void onChanged(Integer status) {
                LogUtil.d(TAG, "getSurpriseEggListDspStatus onChanged: " +status);
                surpriseEggListVM.initDspVisibility();
            }
        });
        surpriseEggListVM.getSurpriseEggList().observe(getViewLifecycleOwner(), new Observer<SurpriseEggList>() {
            @Override
            public void onChanged(SurpriseEggList eggList) {
                LogUtil.d(TAG, "getSurpriseEggList onChanged: " +eggList);
                if (eggList != null && eggList.getSurpriseEggs() != null){
                    VRModel.getInstance().registerSupriseEggs(eggList);
                }
            }
        });
    }

    @Override
    protected void initData() {
        LogUtil.d(TAG, "requestLibraryScenes");
        showLoadingPage();
        HandlerUtil.getMainHandler().postDelayed(new Runnable() {
            @Override
            public void run() {
                mLibraryViewModel.requestLibraryScenes();
                surpriseEggListVM.requestSurpriseEggListData();
            }
        }, ConstantViewValue.REFRESH_LOADING_TIMER);
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if(!hidden){
            //以防用户在手机上更新了惊喜彩蛋，每次在此页面刷新下
            surpriseEggListVM.requestSurpriseEggListData();
        }
    }

    @Override
    protected void refreshData() {
        initData();
    }

    @Override
    protected void onNetWorkChanged(Boolean netWorkStatus) {

    }

    @Override
    protected void onLoginStatusChanged(boolean loginStatus) {

    }

    @Override
    protected void onVrWakeUpChanged(boolean vrStatus) {

    }

    @Override
    protected void onPermissionChanged(boolean permissionStatus) {
//        if(permissionStatus){
//            refreshData();
//        }
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        LogUtil.d(TAG,"onConfigurationChanged");
        for(LibraryModuleInterface libraryModuleInterface : mLibraryModuleInterfaces){
            libraryModuleInterface.onConfigurationChanged(newConfig);
        }
        NightUtil.notifyViewConfigChange(getView(),newConfig);

    }

    @Override
    protected void showContentPage() {
        super.showContentPage();
        mSmartRefreshLayout.setEnableRefresh(true);
        mSmartRefreshLayout.setEnableLoadMore(true);
        if(mLibraryViewModel.isAdDisplayIgnore()){
            adView.setVisibility(View.GONE);
        }else {
            adView.setVisibility(View.VISIBLE);
        }
    }

    @Override
    protected void showNoContentPage() {
        super.showNoContentPage();
        mSmartRefreshLayout.setEnableRefresh(true);
        mSmartRefreshLayout.setEnableLoadMore(false);
        adView.setVisibility(View.GONE);
    }

    @Override
    protected void showLoadingPage() {
        super.showLoadingPage();
        adView.setVisibility(View.GONE);
        mSmartRefreshLayout.setEnableRefresh(false);
        mSmartRefreshLayout.setEnableLoadMore(false);
    }

    @Override
    protected void showErrorPage() {
        super.showErrorPage();
        adView.setVisibility(View.GONE);
        mSmartRefreshLayout.setEnableRefresh(false);
        mSmartRefreshLayout.setEnableLoadMore(false);
    }
}
