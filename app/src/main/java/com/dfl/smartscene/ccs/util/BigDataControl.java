package com.dfl.smartscene.ccs.util;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.RemoteException;

import androidx.lifecycle.MutableLiveData;

import com.dfl.api.app.eventtrack.ClientId;
import com.dfl.api.app.eventtrack.CommonInfo;
import com.dfl.api.app.eventtrack.IEventTrack;
import com.dfl.api.app.eventtrack.IEventTrackCallback;
import com.dfl.api.base.APICreateCallback;
import com.dfl.api.base.APIStateType;
import com.dfl.api.base.BaseManager;
import com.dfl.api.base.Constants;
import com.dfl.api.base.IBaseAPI;
import com.dfl.api.base.NullServiceException;
import com.dfl.api.da.setting.ISetting;
import com.dfl.api.da.setting.ISettingCallback;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.constant.ScenePatternFuncDef;
import com.google.gson.Gson;
import com.iauto.uibase.utils.MLog;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class BigDataControl {
    private static final String                   TAG            = "BigDataControl";
    private              Context                  mContext;
    @SuppressLint("StaticFieldLeak")
    private static       BigDataControl           s_mInstance;
    private              ISetting                 mISettingManager;
    private final      byte[]                   mApiLock       = new byte[0];
    private IEventTrack              mIEventTrack = null;
    private int mBigDataHandle = 0;
    /**
     *数据埋点开关
     */
    private            MutableLiveData<Boolean> mBigDataEnable = new MutableLiveData<>();

    /**
     * 创建Model单例
     * @param context 应用上下文对象
     */
    public static synchronized void create(Context context) {
        MLog.d(TAG, "create");
        if(s_mInstance == null){
            s_mInstance = new BigDataControl(context);
        }
    }

    /**
     * 构造函数
     * @param context 应用上下文对象
     */
    private BigDataControl(Context context) {
        this.mContext = context;
        initLiveData();
        createCustomApi(context);
    }

    /**
     * 获取Model单例对象
     * @return model单例对象
     */
    public static BigDataControl getInstance(){
        if (s_mInstance == null) {
            throw new NullPointerException("RadioModel is not init.");
        }
        return s_mInstance;
    }

    /**
     * 初始化liveData
     */
    public void initLiveData() {
        mBigDataEnable.setValue(false);
    }

    /**
     * 创建CustomApi单例
     * @param context 应用上下文对象
     */
    public void createCustomApi(Context context) {
        
        //获取数据埋点handle
        BaseManager.create(context.getApplicationContext(), Constants.DFL_APP_EVENTTRACK_EVENTTRACK_SERVICE, new APICreateCallback() {
            @Override
            public void onAPICreateCallback(APIStateType.APIState apiState, String s, IBaseAPI iBaseAPI) {
                if (Constants.DFL_APP_EVENTTRACK_EVENTTRACK_SERVICE.equals(s)) {
                    synchronized (mApiLock) {
                        if (apiState == APIStateType.APIState.SUCCESS && iBaseAPI != null && iBaseAPI instanceof IEventTrack) {
                            mIEventTrack = (IEventTrack) iBaseAPI;
                            try {
                                mIEventTrack.registerEventTrace(ClientId.CLIENT_ID_SCENECONTROL, new IEventTrackCallback() {
                                    @Override
                                    public void onGetEventHandle(int i) {
                                        MLog.d(TAG,"onGetEventHandle handle = " + i);
                                        mBigDataHandle = i;
                                    }

                                    @Override
                                    public void onGetEventIsWrite(boolean b) {

                                    }

                                    @Override
                                    public void onGetAllAppSwitchState(Map map) {

                                    }

                                });
                            } catch (NullServiceException e) {
                                mIEventTrack = null;
                                MLog.e(TAG, "->registerEventTrace() NullServiceException!!!");
                            }
                        } else {
                            mIEventTrack = null;
                            MLog.d( TAG,"registerCustomApiCallBack failed !!");
                        }
                    }
                }
            }

            @Override
            public void onMethodStateCallback(APIStateType.APIState apiState, String s) {

            }
        });

        //创建setting service api
        BaseManager.create(context, Constants.DFL_DA_SETTING_SETTING_SERVICE, new APICreateCallback() {
            @Override
            public void onAPICreateCallback(APIStateType.APIState apiStateType, String s, IBaseAPI iBaseAPI) {
                MLog.i(TAG, "onAPICreateCallback state= " + apiStateType.ordinal() + ",apiInstance= " + iBaseAPI + ",requestName= " + s);
                if (Constants.DFL_DA_SETTING_SETTING_SERVICE.equals(s)) {
                    synchronized (mApiLock) {
                        if (apiStateType == APIStateType.APIState.SUCCESS && iBaseAPI != null && iBaseAPI instanceof ISetting) {
                            mISettingManager = (ISetting) iBaseAPI;

                            try {
                                mISettingManager.registerSettingCallback(new ISettingCallback() {
                                    @Override
                                    public void onScreenBrightnessStatusChange(int type, int status) {

                                    }

                                    @Override
                                    public void onNotifyStopCleanCache() {

                                    }

                                    @Override
                                    public void onAudioFrequencyUseStatusChanged(boolean b) {
                                        MLog.i(TAG, "onAudioFrequencyUseStatusChanged value = " + b);
                                        mBigDataEnable.postValue(b);
                                    }

                                    @Override
                                    public void onShowroomModeStatusChange(int i) {

                                    }

                                    @Override
                                    public void onTemporaryParkingModeStatusChange(int i) {

                                    }

                                    @Override
                                    public void onIVIScreenOnOffStatusChange(int type, int status) {

                                    }

                                    @Override
                                    public void onAppCacheCleanupCompleted(int i) {

                                    }

                                    @Override
                                    public void refreshSystemReady(int status) {

                                    }

                                    @Override
                                    public void notifyAppOperationChange(int type, int action) {

                                    }

                                    @Override
                                    public void onTimeSwitchChange(int state) {

                                    }

                                });
                                MLog.d(TAG, "create success");
                            } catch (NullServiceException e) {
                                mISettingManager = null;
                                MLog.e(TAG, "->registerSettingCallback() NullServiceException!!!");
                            }

                            if (null != mISettingManager) {
//                                try {
//                                    MLog.i(TAG, "onAPICreateCallback getCustomerExperienceStatus= " + mISettingManager.getCustomerExperienceStatus());
//                                    mBigDataEnable.postValue(mISettingManager.getCustomerExperienceStatus());
//                                } catch (NullServiceException | RemoteException e) {
//                                    mISettingManager = null;
//                                    MLog.e(TAG, "->getCustomerExperienceStatus() Exception!!!");
//                                }
                            } else {
                                mBigDataEnable.postValue(false);
                            }
                        } else {
                            // 不成功或者连接断开等情况设置成null
                            mISettingManager = null;
                            mBigDataEnable.postValue(false);
                            MLog.e(TAG, "create failed");
                        }
                    }
                }
            }

            @Override
            public void onMethodStateCallback(APIStateType.APIState apiState, String s) {
            }
        });
    }

    /**
     * 数据埋点
     */
    public void writeEventTrack(String eventId, String eventName, String eventType, Map map) {
        if(mIEventTrack == null) {
            MLog.d(TAG,"EventTrack register CustomApi Failed !!");
            return ;
        }
        MLog.d(TAG,"writeEventTrack eventId" + eventId + "eventName" + eventName + "eventType" + eventType);
        Gson gson = new Gson();
        @SuppressLint("SimpleDateFormat") SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        long now = System.currentTimeMillis();
        Date date = new Date(now);
        String str_now = simpleDateFormat.format(date);

        CommonInfo commonInfo = new CommonInfo(mContext.getString(R.string.app_name), ScenePatternFuncDef.VERSION, "test",
                "", eventId, eventName, eventType, str_now);
//        synchronized (mApiLock) {
//            try {
//                String json = "";
//                if(map != null ) {
//                    json = gson.toJson(map);
//                }
//                 mIEventTrack.writeEventTrack(mBigDataHandle, commonInfo, json, mBigDataEnable.getValue());
//
//            } catch (NullServiceException | RemoteException e) {
//                mIEventTrack = null;
//                MLog.e(TAG, "->writeEventTrack() Exception!!!");
//            }
//        }
    }

    public Map makeStartFrom(String from) {
        Map map = new HashMap();
        map.put("refpage_name",from);
        return map;
    }

    public Map makePlay(long time, String subject) {
        Map map = new HashMap();
        map.put("duration",time);
        map.put("subject",subject);
        return map;
    }

    public Map makeChangeTheme(String subject) {
        Map map = new HashMap();
        map.put("subject",subject);
        return map;
    }

    public Map makeStopReason(long useTime, String reason) {
        Map map = new HashMap();
        map.put("duration",useTime);
        map.put("exit_reason",reason);
        return map;
    }

    public Map makePatternSel(String pattern) {
        Map map = new HashMap();
        map.put("scene_mode",pattern);
        return map;
    }

}
