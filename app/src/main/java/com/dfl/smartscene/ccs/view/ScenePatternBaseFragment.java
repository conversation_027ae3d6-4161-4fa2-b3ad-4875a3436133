package com.dfl.smartscene.ccs.view;

import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.dfl.smartscene.R;
import com.iauto.uibase.utils.MLog;

public class ScenePatternBaseFragment extends Fragment {
    private static final String TAG = "ScenePatternBaseFragment";
    @Override
    public Animation onCreateAnimation(int transit, boolean enter, int nextAnim) {
        MLog.d(TAG, "onCreateAnimation transit = " + transit + " enter = " + enter + " nextAnim = " + nextAnim);
        //表示是一个进入动作，比如add.show等
        if (transit == FragmentTransaction.TRANSIT_FRAGMENT_OPEN) {
            if (enter) {
                return AnimationUtils.loadAnimation(getContext(), R.anim.fragment_fade_in);
            } else {
                return AnimationUtils.loadAnimation(getContext(), R.anim.fragment_fade_out);
            }
        }
        //表示一个退出动作，比如出栈，hide，detach等
        else if (transit == FragmentTransaction.TRANSIT_FRAGMENT_CLOSE) {
            if (enter) {
                AnimationUtils.loadAnimation(getContext(), R.anim.fragment_fade_in);
            } else {
                AnimationUtils.loadAnimation(getContext(), R.anim.fragment_fade_out);
            }
        }
        return null;
    }
}
