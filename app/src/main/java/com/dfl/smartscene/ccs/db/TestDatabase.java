package com.dfl.smartscene.ccs.db;

import androidx.annotation.NonNull;
import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.sqlite.db.SupportSQLiteDatabase;

import com.dfl.smartscene.ccs.db.dao.CarConfigDao;
import com.dfl.smartscene.ccs.db.dao.CarConfigOverviewDao;
import com.dfl.smartscene.ccs.db.dao.DeviceDao;
import com.dfl.smartscene.ccs.db.dao.OperationActionDao;
import com.dfl.smartscene.ccs.db.dao.OperationConditionDao;
import com.dfl.smartscene.ccs.db.dao.OperationStateDao;
import com.dfl.smartscene.ccs.db.dao.scene.SceneActionListDao;
import com.dfl.smartscene.ccs.db.dao.scene.SceneCarConfigDao;
import com.dfl.smartscene.ccs.db.dao.scene.SceneCategoryListDao;
import com.dfl.smartscene.ccs.db.dao.scene.SceneConditionListDao;
import com.dfl.smartscene.ccs.db.dao.scene.SceneListDao;
import com.dfl.smartscene.ccs.db.dao.scene.SceneStateListDao;
import com.dfl.smartscene.ccs.db.entity.CarConfigEntity;
import com.dfl.smartscene.ccs.db.entity.CarConfigOverviewEntity;
import com.dfl.smartscene.ccs.db.entity.DeviceEntity;
import com.dfl.smartscene.ccs.db.entity.OperationActionEntity;
import com.dfl.smartscene.ccs.db.entity.OperationConditionEntity;
import com.dfl.smartscene.ccs.db.entity.OperationStateEntity;
import com.dfl.smartscene.ccs.db.entity.scene.SceneActionListEntity;
import com.dfl.smartscene.ccs.db.entity.scene.SceneCarConfigEntity;
import com.dfl.smartscene.ccs.db.entity.scene.SceneCategoryListEntity;
import com.dfl.smartscene.ccs.db.entity.scene.SceneConditionListEntity;
import com.dfl.smartscene.ccs.db.entity.scene.SceneListEntity;
import com.dfl.smartscene.ccs.db.entity.scene.SceneStateListEntity;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;

@Database(entities = {
        CarConfigEntity.class,
        CarConfigOverviewEntity.class,
        DeviceEntity.class,
        OperationConditionEntity.class,
        OperationStateEntity.class,
        OperationActionEntity.class,
        SceneCarConfigEntity.class,
        SceneListEntity.class,
        SceneCategoryListEntity.class,
        SceneActionListEntity.class,
        SceneConditionListEntity.class,
        SceneStateListEntity.class
},
        version = 1, exportSchema = false)
public abstract class TestDatabase extends RoomDatabase {
    private static final String DATABASE_NAME = "vehicle_db";

    private static TestDatabase databaseInstance;

    public static synchronized TestDatabase getInstance()
    {
        if(databaseInstance == null)
        {
            databaseInstance = Room.databaseBuilder(ScenePatternApp.getInstance().getApplicationContext(), TestDatabase.class, DATABASE_NAME)
                    .addCallback(new Callback() {
                        @Override
                        public void onCreate(@NonNull SupportSQLiteDatabase db) {
                            super.onCreate(db);
                        }

                        @Override
                        public void onOpen(@NonNull SupportSQLiteDatabase db) {
                            super.onOpen(db);
                        }

                        @Override
                        public void onDestructiveMigration(@NonNull SupportSQLiteDatabase db) {
                            super.onDestructiveMigration(db);
                        }
                    })
                    .build();
        }

        return databaseInstance;
    }

    public abstract CarConfigDao carConfigDao();

    public abstract DeviceDao deviceDao();

    public abstract OperationConditionDao operationConditionDao();

    public abstract OperationStateDao operationStateDao();

    public abstract OperationActionDao operationActionDao();

    public abstract SceneCarConfigDao sceneCarConfigDao();

    public abstract SceneCategoryListDao sceneCategoryListDao();

    public abstract SceneListDao sceneListDao();

    public abstract SceneActionListDao sceneActionListDao();

    public abstract SceneConditionListDao sceneConditionListDao();

    public abstract SceneStateListDao sceneStateListDao();

    public abstract CarConfigOverviewDao carConfigOverviewDao();


}
