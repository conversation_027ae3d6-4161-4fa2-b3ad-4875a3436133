package com.dfl.smartscene.ccs.base;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.RecyclerView;

import com.dfl.smartscene.ccs.view.base.AdapterItemDrag;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date ：2022/10/21 17:01
 * @description ：用于实现recycleviewitem拖动换序
 */
public class ItemTouchHelperCallBack extends ItemTouchHelper.Callback{
    private AdapterItemDrag mAdapterItemDrag;
    private boolean dragEnabled = true;

    public void setDragEnabled(boolean dragEnabled) {
        this.dragEnabled = dragEnabled;
    }

    public ItemTouchHelperCallBack(AdapterItemDrag adapterItemDrag) {
        mAdapterItemDrag = adapterItemDrag;
    }

    @Override
    public int getMovementFlags(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder) {
        int dragFlags = ItemTouchHelper.UP | ItemTouchHelper.DOWN | ItemTouchHelper.LEFT | ItemTouchHelper.RIGHT;
        return makeMovementFlags(dragFlags, 0);
    }

    @Override
    public boolean onMove(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder, @NonNull RecyclerView.ViewHolder target) {
        int oldPosition = viewHolder.getAdapterPosition();
        int targetPosition = target.getAdapterPosition();
        int totalSize = Objects.requireNonNull(recyclerView.getAdapter()).getItemCount();
        if (oldPosition == totalSize - 1 || targetPosition == totalSize - 1) {
            return false;
        }
        mAdapterItemDrag.onItemMove(viewHolder, target);
        return true;
    }

    @Override
    public void onSwiped(@NonNull RecyclerView.ViewHolder viewHolder, int direction) {

    }

    @Override
    public void clearView(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder) {
        super.clearView(recyclerView, viewHolder);
        mAdapterItemDrag.onItemClear(viewHolder);
    }

    @Override
    public boolean isLongPressDragEnabled() {
        return dragEnabled;
    }
}
