package com.dfl.smartscene.ccs.model;


import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.core.ConditionBaseModelImpl;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/12/13 18:44
 * @description ：
 */
public class LibraryDriveModel extends ConditionBaseModelImpl {

    public void listenCarStatus(){

    }

    @Override
    protected String[] getConditionIds() {
        return new String[0];
    }

    public interface CarGearCallback{
        void onCarGearChange(@ConstantModelValue.CarGear int Gear);
    }

    private List<CarGearCallback> mCarGearCallbacks = new ArrayList<>();

    public void registerCarGearCallback(CarGearCallback carGearCallback){
        mCarGearCallbacks.add(carGearCallback);
    }

    public void unregisterCarGearCallback(CarGearCallback carGearCallback){
        mCarGearCallbacks.remove(carGearCallback);
    }

    protected void onCarGearChange(@ConstantModelValue.CarGear int carGear){
        for(CarGearCallback carGearCallback : mCarGearCallbacks){
            carGearCallback.onCarGearChange(carGear);
        }
    }

}
