package com.dfl.smartscene.ccs.view.weight;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.widget.Switch;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

public class CustomSwitch extends Switch {
    public CustomSwitch(@NonNull Context context) {
        super(context);
    }

    public CustomSwitch(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public CustomSwitch(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    private boolean enableToggle = true;

    public void setEnableToggle(boolean enableToggle) {
        this.enableToggle = enableToggle;
    }

    @Override
    public void toggle() {
        if(enableToggle){
            super.toggle();
        }
    }


    @Override
    protected void onDraw(Canvas canvas) {

        int trackHeight = 0;
        Drawable trackDrawable = getTrackDrawable();
        if (trackDrawable != null) {
            trackDrawable.draw(canvas);

            trackHeight = trackDrawable.getIntrinsicHeight();
        }

        final int saveCount = canvas.save();
        Drawable thumbDrawable = getThumbDrawable();

        if (thumbDrawable != null) {
            int paddingTop = (trackHeight - thumbDrawable.getIntrinsicHeight()) / 2;
            canvas.translate(0, paddingTop);
            thumbDrawable.draw(canvas);
        }
        canvas.restoreToCount(saveCount);

    }
}
