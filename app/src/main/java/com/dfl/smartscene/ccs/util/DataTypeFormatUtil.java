package com.dfl.smartscene.ccs.util;

import android.text.TextUtils;

import com.alibaba.fastjson.JSON;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/8/8 9:17
 * @description ：
 */
public class DataTypeFormatUtil {
    private static final String TAG = "DataTypeFormatUtil";

    public static int string2Int(String value) {
        int result = ConstantModelValue.ERROR_FORMAT_INT_FLOAT_DOUBLE;
        try {
            result = Integer.parseInt(value);
        } catch (NumberFormatException e) {
            LogUtil.e(TAG, "string to int error,value = " + value);
        }
        return result;
    }

    public static Boolean string2Boolean(String value) {
        if ("true".equals(value)) {
            return true;
        } else if ("false".equals(value)) {
            return false;
        } else {
            return null;
        }

    }

    public static Boolean string2Boolean(String value , boolean dafaultValue) {
        if ("true".equals(value)) {
            return true;
        } else if ("false".equals(value)) {
            return false;
        } else {
            return dafaultValue;
        }

    }

    public static int string2Int(String value, int defaultValue) {
        int result;
        try {
            if (TextUtils.equals("false",value)){
                result = 1;
            }else if (TextUtils.equals("false",value)){
                result = 0;
            }else {
                result = Integer.parseInt(value);
            }
        } catch (NumberFormatException e) {
            LogUtil.e(TAG, "string to float error,value = " + value);
            result = defaultValue;
        }
        LogUtil.e(TAG, "string to int,value = " + result);
        return result;
    }

    public static float string2Float(String value, float defaultValue) {
        float result;
        try {
            result = Float.parseFloat(value);
        } catch (NumberFormatException e) {
            LogUtil.e(TAG, "string to float error,value = " + value);
            result = defaultValue;
        }
        return result;
    }


    public static float string2Float(String value) {
        float result = ConstantModelValue.ERROR_FORMAT_INT_FLOAT_DOUBLE;
        try {
            result = Float.parseFloat(value);
        } catch (NumberFormatException e) {
            LogUtil.e(TAG, "string to float error,value = " + value);
        }
        return result;
    }

    public static double string2Double(String value) {
        double result = ConstantModelValue.ERROR_FORMAT_INT_FLOAT_DOUBLE;
        try {
            result = Double.parseDouble(value);
        } catch (NumberFormatException e) {
            LogUtil.e(TAG, "string to float error,value = " + value);
        }
        return result;
    }

    public static int[] string2ArrayInt(String value) {
        return JSON.parseObject(value,int[].class);
    }

    public static Class<?>[] string2ClassList(List<String> valueList) {
        if (valueList == null) {
            return null;
        }
        Class<?>[] classList = new Class[valueList.size()];
        for (int i = 0; i < valueList.size(); i++) {
            classList[i] = string2Class(valueList.get(i));
        }
        return classList;
    }

    public static Class<?> string2Class(String value) {
        switch (value) {
            case "int":
                return int.class;
            case "String":
                return String.class;
            case "boolean":
                return boolean.class;
            case "float":
                return float.class;
            case "double":
                return double.class;
            case "int[]":
                return int[].class;
            default:
                LogUtil.e(TAG, "String2Class invalid !! + value = " + value);
                return null;
        }
    }

    public static Object string2Object(String value, String type) {
        switch (type) {
            case "int":
                return string2Int(value);
            case "String":
                return value;
            case "boolean":
                return string2Boolean(value);
            case "float":
                return string2Float(value);
            case "double":
                return string2Double(value);
            case "int[]":
                return string2ArrayInt(value);
            default:
                LogUtil.e(TAG, "string2Object invalid !! + type = " + type);
                return null;

        }
    }

    public static String frontCompWithZero(int sourceDate, int formatLength) {
        return String.format("%0" + formatLength + "d", sourceDate);
    }


    /**Color的Int整型转Color的16进制颜色值【方案一】
     * colorInt - -12590395
     * return Color的16进制颜色值——#3FE2C5
     * */
    public static String int2Hex(int colorInt){
        String hexCode = "";
        hexCode = String.format("#%06X", 16777215 & colorInt);
        return hexCode;
    }

    public static List<String> parseOneDimensionalArray(String str) {
        if (null == str || str.length() == 0) {
            return null;
        }

        String[] longs = JSON.parseObject(str, String[].class);
        return Arrays.asList(longs);
    }

    /**
     * 从列表中返回目标字符串的位置，找不到则返回-1
     * <注意事项>value可能为空
     */
    public static int stringFindIndex(String value , List<String> list){
        if(null != list){
            for(int i = 0; i < list.size() ; i++){
                if(list.get(i).equals(value)){
                    return i;
                }
            }
        }
        if (list.contains("true") | list.contains("false") | list.contains("null")){
            if ("0".equals(value) | "true".equals(value)){
                return list.indexOf("true");
            }else if ("false".equals(value) | "1".equals(value)){
                return list.contains("false") ? list.indexOf("false") : list.indexOf("null");
            }else {
                return list.indexOf("null");
            }
        }
        return -1;
    }
}
