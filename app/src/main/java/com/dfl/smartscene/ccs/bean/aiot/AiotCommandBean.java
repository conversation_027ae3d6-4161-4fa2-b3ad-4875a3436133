package com.dfl.smartscene.ccs.bean.aiot;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/8/4 10:18
 * @description ：操作aiot命令，下发给aiot app
 */
public class AiotCommandBean {

    /**
     * 操作类型 1：属性  4：方法
     */
    private String cmd;

    /**
     * 服务ID(仅当 cmd=4 的情况下需要赋值)
     */
    private int siid;
    /**
     * 方法id
     */
    private int aiid;

    private String csId;
    /**
     * 产品id
     */
    private String pid;
    /**
     * 属性列表
     */
    private List<AiotPropBean> propList;

    public String getCmd() {
        return cmd;
    }

    public void setCmd(String cmd) {
        this.cmd = cmd;
    }

    public int getSiid() {
        return siid;
    }

    public void setSiid(int siid) {
        this.siid = siid;
    }

    public int getAiid() {
        return aiid;
    }

    public void setAiid(int aiid) {
        this.aiid = aiid;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public List<AiotPropBean> getPropList() {
        return propList;
    }

    public void setPropList(List<AiotPropBean> propList) {
        this.propList = propList;
    }

    public String getCsId() {
        return csId;
    }

    public void setCsId(String csId) {
        this.csId = csId;
    }
}
