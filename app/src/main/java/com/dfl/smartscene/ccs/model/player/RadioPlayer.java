package com.dfl.smartscene.ccs.model.player;

import android.content.ComponentName;

/**
 * <AUTHOR>
 * @date ：2023/5/30 14:31
 * @description ：
 */
public class RadioPlayer extends CommonPlayer{
    public RadioPlayer(ComponentName componentName) {
        super(componentName);
    }

    @Override
    public void play(String arg) {
        String[] split = arg.split("@");
        if (METHOD_SPECIAL.equals(split[0])) {
            if (PARAM_RESUME.equals(split[1])) {
                if(getController() != null){
                    getController().getTransportControls().play();
                }
            }
        }
    }
}
