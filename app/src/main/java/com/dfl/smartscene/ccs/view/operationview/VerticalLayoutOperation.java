package com.dfl.smartscene.ccs.view.operationview;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.factory.OperationBaseViewFactory;
import com.dfl.smartscene.ccs.model.manager.ViewConflictManager;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseView;
import com.iauto.uibase.utils.MLog;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/9/14 11:47
 * @description ：多操作视图在同一弹窗的容器
 */
public class VerticalLayoutOperation implements OperationBaseView {
    protected List<OperationBaseView> mOperationBaseViews = new ArrayList<>();
    private ViewConflictManager mViewConflictManager;
    private SingleOperation mSingleOperation;

    public VerticalLayoutOperation(SingleOperation singleOperation, ViewGroup parent){

        if("gone".equals(singleOperation.getArg(ConstantModelValue.VIEW_DEFAULT_POS))){
            mViewConflictManager = new ViewConflictManager(true);
            MLog.d("TAG", "ConstantModelValue.VIEW_DEFAULT_POS:visible");
        }else {
            MLog.d("TAG", "ConstantModelValue.VIEW_DEFAULT_POS:gone");
            mViewConflictManager = new ViewConflictManager(false);
        }
        mSingleOperation = singleOperation;
        ViewGroup container = creatViewContainer(parent);
        layoutChild(container,singleOperation);
    }

    protected ViewGroup creatViewContainer(ViewGroup parent){
        View root = LayoutInflater.from(parent.getContext()).inflate(R.layout.layout_vertical_operation,parent,true);
        LinearLayout container = root.findViewById(R.id.linearlayout_vertical_operation);
        return container;
    }

    protected void layoutChild(ViewGroup container , SingleOperation singleOperation){
        int topMargin = container.getResources().getDimensionPixelOffset(R.dimen.y_px_60);
        int divideSpace = container.getResources().getDimensionPixelOffset(R.dimen.y_px_40);

        //发起导航的间距不同
        if(singleOperation.getOperationId().equals(ConstantModelValue.OID_ACT_APP_NAVI_START)){
            topMargin = container.getResources().getDimensionPixelOffset(R.dimen.y_px_24);
            divideSpace = container.getResources().getDimensionPixelOffset(R.dimen.y_px_24);
        }

        container.addView(new View(container.getContext()), ViewGroup.LayoutParams.MATCH_PARENT,topMargin);
        int num = singleOperation.getSubOperations().size();
        for(int i = 0 ; i < num ; i++){
            int viewIndex = i + 1;
            SingleOperation child = singleOperation.getSubOperations().get(i);
            MLog.d("TAG", "childDefault: -- " + child.getArg(ConstantModelValue.VIEW_DEFAULT_POS) + ",parentDefault," + singleOperation.getArg(ConstantModelValue.VIEW_DEFAULT_POS));
            if (null == child.getArg(ConstantModelValue.VIEW_DEFAULT_POS)){
                //导航这里的默认值需要从上一级获取到再设置
                child.setMapArgs(ConstantModelValue.VIEW_DEFAULT_POS,singleOperation.getArg(ConstantModelValue.VIEW_DEFAULT_POS));
            }
            LayoutInflater.from(container.getContext()).inflate(R.layout.item_vertical_operation,container,true);
            TextView textViewTitle = container.getChildAt(viewIndex).findViewById(R.id.textview_item_vertical_operation_title);
            textViewTitle.setText(child.getOperationName());
            MLog.d("TAG", "name:"+child.getOperationName());
            FrameLayout itemContainer = container.getChildAt(viewIndex).findViewById(R.id.layout_vertical_operation_content);
            OperationBaseView baseView = OperationBaseViewFactory.addOperationBaseView(child, itemContainer);

            onItemViewAdd(child,baseView, (ViewGroup) container.getChildAt(viewIndex));
            if(i != num-1){
                ((ViewGroup) container.getChildAt(viewIndex)).addView(new View(container.getContext()), ViewGroup.LayoutParams.MATCH_PARENT,divideSpace);
            }

        }
    }

    protected void onItemViewAdd(SingleOperation childData , OperationBaseView baseView , ViewGroup itemContainer){
        mOperationBaseViews.add(baseView);
        mViewConflictManager.registerOperation(childData,baseView, itemContainer);
    }

    @Override
    public List<String> extractArgs() {
        List<String> result = new ArrayList<>();
        for(OperationBaseView operationBaseView : mOperationBaseViews){
            if(operationBaseView instanceof ConflictViewClient && ((ConflictViewClient) operationBaseView).isViewUnable()){
                result.add("null");
            }else {
                result.addAll(operationBaseView.extractArgs());
            }
        }
        return result;
    }

    @Override
    public String extractDesc() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(mSingleOperation.getArg(ConstantModelValue.OUTPUT_DESC_TEXT,""));
        for(OperationBaseView operationBaseView : mOperationBaseViews){
            if(operationBaseView instanceof ConflictViewClient && ((ConflictViewClient) operationBaseView).isViewUnable()){
                continue;
            }
            if(null == operationBaseView.extractArgs() || operationBaseView.extractArgs().size() == 0 || null == operationBaseView.extractArgs().get(0) || operationBaseView.extractArgs().get(0).equals("null")){
                continue;
            }
            String childDesc = operationBaseView.extractDesc();
            if(childDesc != null){
                stringBuilder.append(operationBaseView.extractDesc());
            }
        }
        return stringBuilder.toString();
    }

    @Override
    public void onConfigurationChanged() {
        for(OperationBaseView operationBaseView : mOperationBaseViews){
            operationBaseView.onConfigurationChanged();
        }
    }
}
