package com.dfl.smartscene.ccs.view.operationview;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.NumberPicker;
import android.widget.TextView;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.util.DataTypeFormatUtil;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseView;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/9/19
 * @description ：延时执行操作
 */
public class DelayTimeOperation implements OperationBaseView {

    private final SingleOperation mSingleOperation;

    // 时间数值
    private final NumberPicker mNumberPickerValue;
    // 时间单位
    private final NumberPicker mNumberPickerUnit;

    private static final String STR_TIME_SECOND = "秒";
    private static final String STR_TIME_MINUTE = "分";
    private static final String STR_TIME_HOUR = "时";

    @Override
    public List<String> extractArgs() {
        ArrayList<String> values = new ArrayList<>(1);

        int value = mNumberPickerValue.getValue() + 1;
        String[] displayedValues = mNumberPickerUnit.getDisplayedValues();
        int value2 = mNumberPickerUnit.getValue();
        if (STR_TIME_MINUTE.equals(displayedValues[value2])) {
            value *= 60;
        } else if (STR_TIME_HOUR.equals(displayedValues[value2])) {
            value = value * 60 * 60;
        }
        values.add(value + "");
        return values;
    }

    @Override
    public String extractDesc() {
        String desc = mSingleOperation.getArg(ConstantModelValue.OUTPUT_DESC_TEXT);
        if (desc != null) {
            int value = mNumberPickerValue.getValue() + 1;
            String[] displayedValues = mNumberPickerUnit.getDisplayedValues();
            int value2 = mNumberPickerUnit.getValue();
            desc = desc.replaceFirst("&", value+"");
            desc = desc.replaceFirst("&", displayedValues[value2]);

        }
        return desc;
    }

    public DelayTimeOperation(SingleOperation singleOperation, ViewGroup parent) {
        mSingleOperation = singleOperation;

        String descText = singleOperation.getArg(ConstantModelValue.VIEW_DESC_TEXT);
        String descTextEnd = singleOperation.getArg(ConstantModelValue.VIEW_DESC_TEXT_END);

        LayoutInflater.from(parent.getContext()).inflate(R.layout.item_operation_delay_time, parent, true);

        TextView tvTip1 = parent.findViewById(R.id.textview_item_operation_delay_time_action_tip_start);
        TextView tvTip2 = parent.findViewById(R.id.textview_item_operation_delay_time_action_tip_end);
        tvTip1.setText(descText);
        tvTip2.setText(descTextEnd);

        mNumberPickerValue = parent.findViewById(R.id.numberpicker_item_operation_delay_time_value);
        mNumberPickerValue.setMinValue(1);
        mNumberPickerValue.setMaxValue(60);
        mNumberPickerUnit = parent.findViewById(R.id.numberpicker_item_operation_delay_time_unit);
        mNumberPickerUnit.setMinValue(0);
        mNumberPickerUnit.setMaxValue(1);
        mNumberPickerUnit.setDisplayedValues(new String[]{STR_TIME_SECOND, STR_TIME_MINUTE});
        String defaultValueStr = singleOperation.getArg(ConstantModelValue.VIEW_DEFAULT_POS);
        int defaultValue = 1;
        if (TextUtils.equals(singleOperation.getOperationId(),ConstantModelValue.OPERATION_ID_ACTION_MORE_TIME_DELAY)){
            defaultValue = 5;
        }
        if(null != defaultValueStr){
            defaultValue = DataTypeFormatUtil.string2Int(defaultValueStr);
        }
        if(defaultValue >= 60){
            mNumberPickerValue.setValue(defaultValue/60 - 1);
            mNumberPickerUnit.setValue(1);
        }else{
            mNumberPickerValue.setValue(defaultValue - 1);
            mNumberPickerUnit.setValue(0);
        }
    }

    @Override
    public void onConfigurationChanged() {

    }

}
