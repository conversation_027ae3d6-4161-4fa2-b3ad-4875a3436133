package com.dfl.smartscene.ccs.constant;

/*
 * 情景模式功能定义
 * <AUTHOR>
 * @date 2022/4/15
 */
public class ScenePatternFuncDef {
    public static final String TAG = "App_ScenePattern";

    //Visibility Define
    public static final int Scene_Pattern_Visible = 0X00000000;
    public static final int Scene_Pattern_inVisible = 0X00000004;

    //舒压模式打开关闭状态
    public static final int Relieve_Stress_Status_Open = 1;
    public static final int Relieve_Stress_Status_Close = 0;

    //网络连接状态
    public static final int INTERNET_STATUS_CONNECTED = 1;
    public static final int INTERNET_STATUS_DISCONNECTED = 0;

    //蓝牙电话状态
    public static final int CALL_STATUS_IDLE = 0;
    public static final int CALL_STATUS_ACTIVE = 1;

    //ECall状态 情景模式内部状态
    public static final int E_CALL_STATUS_IDLE = 0;
    public static final int E_CALL_STATUS_ACTIVE = 1;

    // E_CALL状态 实际状态
    public static final int E_CALL_CHANGE_HANG_UP = 0;
    public static final int E_CALL_CHANGE_DIAL_OUT = 1;
    public static final int E_CALL_CHANGE_DIALING_OUT = 2;
    public static final int E_CALL_CHANGE_DIALING_OUT_FAILED_RETRY = 3;
    public static final int E_CALL_CHANGE_ACNCALL_DIAL_OUT = 4;
    public static final int E_CALL_CHANGE_ACNCALL_ON_CALL = 5;
    public static final int E_CALL_CHANGE_ACNCALL_DIALING_OUT_FAILED_RETRY = 6;
    public static final int E_CALL_CHANGE_ABNORMAL_TERMINATION = 7;
    public static final int E_CALL_CHANGE_INCOMING = 8;

    //外部的中断状态值，除SCENE_PATTERN_INTERRUPT_NONE外，都可能导致播放退出
    public static final int SCENE_PATTERN_INTERRUPT_NONE = 0;
    public static final int SCENE_PATTERN_INTERRUPT_GEAR_TYPE_NOT_P = 1;
    public static final int SCENE_PATTERN_INTERRUPT_BLUETOOTH_CALL = 2;
    public static final int SCENE_PATTERN_INTERRUPT_VR = 3;
    public static final int SCENE_PATTERN_INTERRUPT_ECALL = 4;
    public static final int SCENE_PATTERN_INTERRUPT_INSTRUMENT_WARNING = 5;
    public static final int SCENE_PATTERN_INTERRUPT_SOURCE_CHANGE = 6;
    public static final int SCENE_PATTERN_INTERRUPT_DISCONNECT = 7;
    public static final int SCENE_PATTERN_STOP_REASON_MANUAL = 8;
    public static final int SCENE_PATTERN_STOP_REASON_TIMEOUT = 9;


    //P挡位状态值
    public static final int CAR_GEAR_TYPE_P = 0;

    public static final int VR_STATE_ACTIVE = 0;
    public static final int VR_STATE_INACTIVE = 1;

    public static final String FINISH_LAUNCHER = "com.dfl.launcher";
    public static final String FINISH_ACTION = "ExitNormal";

    public static final String PACKAGE_NAME = "com.dfl.scenepattern";
    public static final String CENTER_PACKAGE_NAME = "com.dfl.usercenter";
    public static final String CENTER_ACTIVITY_NAME = "com.dfl.usercenter.view.login.LoginActivity";

    //transType
    public class TransType{
        public static final int ScenePattern_Top = 0;
        public static final int ScenePattern_Normal = 1;
        public static final int ScenePattern_Back = 2;
        public static final int ScenePattern_Reset = 3;
        public static final int ScenePattern_BackTo = 4;
        public static final int ScenePattern_GoTo = 5;
    }

    public static final String VERSION = "V0.1.0.0";

    //Screen TAG
    public static final String ScenePatternMenuFragment_TAG = "ScenePatternMenuFragment";
    public static final String RelieveStressPatternFragment_TAG = "RelieveStressPatternFragment";
    public static final String RelieveStressPatternFullFragment_TAG = "RelieveStressPatternFullFragment";
    public static final String SurpriseEggListFragment_TAG = "SurpriseEggListFragment";
    public static final String SurpriseEggPlayFragment_TAG = "SurpriseEggPlayFragment";

    //当前的pattern
    public static final int RELIEVE_STRESS_PATTERN = 1;
    public static final int SURPRISE_EGG_PATTERN = 2;
    public static final int NONE_PATTERN = 0;

    public static final int START_MENU           = 0;
    public static final int START_RELIEVE_STRESS = 1;
    public static final int START_RELIEVE_STRESS_FOREGROUND = 2;

    public static final String STARTUP_PATTERN_SCENE = "com.dfl.scenepattern.view.startup_pattern_scene";
    
    // media Source online
    public static final String MEDIA_SOURCE_ONLINE_PACKAGE_NAME    = "com.dfl.onlinemusic";
    public static final String MEDIA_SOURCE_ONLINE_SERVICE         = "com.dfl.onlinemusic.service.MusicService";

    // media Source radio
    public static final String MEDIA_SOURCE_RADIO_PACKAGE_NAME    = "com.dfl.radio";
    public static final String MEDIA_SOURCE_RADIO_SERVICE         = "com.dfl.radio.service.RadioAppService";

    // media Source usb audio
    public static final String MEDIA_SOURCE_USBAUDIO_PACKAGE_NAME    = "com.dfl.usbaudio";
    public static final String MEDIA_SOURCE_USBAUDIO_SERVICE         = "com.dfl.usbaudio.service.Service";

    // media Source bluetooth
    public static final String MEDIA_SOURCE_BLUETOOTH_PACKAGE_NAME    = "com.android.bluetooth";
    public static final String MEDIA_SOURCE_BLUETOOTH_SERVICE         = "com.android.bluetooth.avrcpcontroller.BluetoothMediaBrowserService";

    // media Source tingshu
    public static final String MEDIA_SOURCE_TINGSHU_PACKAGE_NAME    = "com.dfl.tingshu";
    public static final String MEDIA_SOURCE_TINGSHU_SERVICE         = "com.dfl.tingshu.service.TingShuService";

    public static final int SYS_VIEW_CTRL_PATTERN_NONE = 0;
    public static final int SYS_VIEW_CTRL_PATTERN_RELIEVE = 1;
    public static final int SYS_VIEW_CTRL_PATTERN_SURPRISE = 2;

    public static final String TOAST_WITH_ICON = "ToastWithIcon"; // 带一个icon的toast

    public static final String BIGDATA_VALUE_START_FROM_MENU = "情景模式菜单"; // 带一个icon的toast
    public static final String BIGDATA_VALUE_START_FROM_LAUNCH = "Launch";  // 带一个icon的toast

    public static final String BIGDATA_VALUE_STOP_FROM_PHONE ="蓝牙电话";
    public static final String BIGDATA_VALUE_STOP_FROM_ECALL ="ECall";
    public static final String BIGDATA_VALUE_STOP_FROM_INSTRUMENT_WARNING ="仪表异常";
    public static final String BIGDATA_VALUE_STOP_FROM_TIMEOUT ="倒计时结束";
    public static final String BIGDATA_VALUE_STOP_FROM_MANUAL ="手动停止";
    public static final String BIGDATA_VALUE_STOP_FROM_NOT_P ="挂至非P挡";
    public static final String BIGDATA_VALUE_STOP_FROM_VR ="用户语音";
    public static final String BIGDATA_VALUE_STOP_FROM_SOURCE_CHANGE ="音乐播放";

    public static final String BIGDATA_VALUE_PATTERN_SEL_RELIEVE = "舒压模式";
    public static final String BIGDATA_VALUE_PATTERN_SEL_SURPRISE = "彩蛋模式";

    public static final String BIGDATA_NAME_START = "Scene_Start_From";
    public static final String BIGDATA_NAME_PLAY = "Scene_Play";
    public static final String BIGDATA_NAME_PAUSE = "Scene_Pause";
    public static final String BIGDATA_NAME_CHANGE_THEME = "Scene_Change_Theme";
    public static final String BIGDATA_NAME_STOP = "Scene_Stop";
    public static final String BIGDATA_NAME_PATTERN_SEL = "Scene_Pattern";

    public static final String BIGDATA_EVENT_ID_1 = "MOD00001";
    public static final String BIGDATA_EVENT_ID_2 = "MOD00002";
    public static final String BIGDATA_EVENT_ID_3 = "MOD00003";
    public static final String BIGDATA_EVENT_ID_4 = "MOD00004";
    public static final String BIGDATA_EVENT_ID_5 = "MOD00005";
    public static final String BIGDATA_EVENT_ID_6 = "MOD00006";

    public static final String BIGDATA_EVENT_TYPE_CLICK = "click";
    public static final String BIGDATA_EVENT_TYPE_PV = "PV";

    public static final int PERMISSIONS_REQUEST_VEHICLE_CODE = 9529;
    public static final int PERMISSIONS_REQUEST_INTERNET_CODE = 9527;

}

