package com.dfl.smartscene.ccs.bean.aiot;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/8/3 17:32
 * @description ：aiot售卖类别，原生数据
 */
public class AiotCategorySaleBean {
    /**
     * 类别id
     */
    private String csId;
    /**
     * 类别名
     */
    private String csName;
    /**
     * 该类别下售卖产品列表
     */
    private List<AiotProductSaleBean> productList;

    public String getCsId() {
        return csId;
    }

    public void setCsId(String csId) {
        this.csId = csId;
    }

    public String getCsName() {
        return csName;
    }

    public void setCsName(String csName) {
        this.csName = csName;
    }

    public List<AiotProductSaleBean> getProductList() {
        return productList;
    }

    public void setProductList(List<AiotProductSaleBean> productList) {
        this.productList = productList;
    }
}
