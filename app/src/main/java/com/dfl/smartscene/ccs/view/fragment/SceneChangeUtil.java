package com.dfl.smartscene.ccs.view.fragment;


import com.dfl.smartscene.ccs.bean.SceneBean;

/**
 * <AUTHOR>
 * @date ：2022/12/28 17:37
 * @description ：用于判断用户是否修改了数据的工具类，UX：如果用户修改了场景，点击退出后需弹出二次确定弹窗，如没修改，则无需弹出
 */
public class SceneChangeUtil {
    private SceneBean oldSceneBean;

    public SceneChangeUtil(SceneBean oldSceneBean) {
        this.oldSceneBean = oldSceneBean;
    }

    public boolean checkSceneChange(SceneBean newScene){
        if(!oldSceneBean.getSceneName().equals(newScene.getSceneName())){
            return true;
        }
        if(!oldSceneBean.isAutoEnable() == newScene.isAutoEnable()){
            return true;
        }
        if(!oldSceneBean.isAutoNotify() == newScene.isAutoNotify()){
            return true;
        }
        if(!(oldSceneBean.getActionOperations().size() == newScene.getActionOperations().size())){
            return true;
        }
        for(int i = 0 ;i < oldSceneBean.getActionOperations().size() ; i ++){
            if(!oldSceneBean.getActionOperations().get(i).getOperationId().equals(newScene.getActionOperations().get(i).getOperationId())){
                return true;
            }
            if(!oldSceneBean.getActionOperations().get(i).getDesc().equals(newScene.getActionOperations().get(i).getDesc())){
                return true;
            }
        }
        if(!(oldSceneBean.getConditionOperations().size() == newScene.getConditionOperations().size())){
            return true;
        }
        for(int i = 0 ;i < oldSceneBean.getConditionOperations().size() ; i ++){
            if(!oldSceneBean.getConditionOperations().get(i).getOperationId().equals(newScene.getConditionOperations().get(i).getOperationId())){
                return true;
            }
            if(!oldSceneBean.getConditionOperations().get(i).getDesc().equals(newScene.getConditionOperations().get(i).getDesc())){
                return true;
            }
        }

        if(!(oldSceneBean.getStateOperations().size() == newScene.getStateOperations().size())){
            return true;
        }
        for(int i = 0 ;i < oldSceneBean.getStateOperations().size() ; i ++){
            if(!oldSceneBean.getStateOperations().get(i).getOperationId().equals(newScene.getStateOperations().get(i).getOperationId())){
                return true;
            }
            if(!oldSceneBean.getStateOperations().get(i).getDesc().equals(newScene.getStateOperations().get(i).getDesc())){
                return true;
            }
        }
        return false;

    }
}
