package com.dfl.smartscene.ccs.view.weight;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Shader;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.widget.SeekBar;

import androidx.annotation.Nullable;
import androidx.core.content.res.ResourcesCompat;

import com.dfl.mediabar.util.LogUtil;
import com.dfl.smartscene.R;

@SuppressLint("AppCompatCustomView")
public class ColorSlideBar extends SeekBar {

    private static final String TAG = ColorSlideBar.class.getSimpleName();

    private Paint mPaint;
    private int[] mColors;
    private float[] mColorPositions;
//    private int mRectHeight;
    private RectF mColorRect;

    private int mProgressHalfHeight = 3;

    private int mWidth;
    private int mHeight;

    private Paint mCirclePaint;


    public ColorSlideBar(Context context) {
        super(context);
        initView();
    }

    public ColorSlideBar(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public ColorSlideBar(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }


    private void initView() {
        setMax(100);

        mPaint = new Paint();
        mPaint.setStyle(Paint.Style.FILL);

        mCirclePaint = new Paint();
        mCirclePaint.setStyle(Paint.Style.STROKE);
        mCirclePaint.setAntiAlias(true);
        mCirclePaint.setStrokeWidth(3);

        setProgressDrawable(null);

        Drawable drawable = ResourcesCompat.getDrawable(getResources(), R.drawable.ic_color_bar_thumb, null);
        setThumb(drawable);

//        setPadding(17, 0, 17, 0);
    }

    public void setColors(int[] colors) {
        mColors = colors.clone();
        float intervalPosition = 1f / (mColors.length - 1f);
        mColorPositions = new float[mColors.length];
        float result = 0;
        for (int i = 0; i < mColorPositions.length; i++) {
            mColorPositions[i] = result;
            result += intervalPosition;
        }
    }

    @Override
    protected synchronized void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        mWidth = getMeasuredWidth();
        mHeight = getMeasuredHeight();
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);

        if (null != mColors) {
            LinearGradient linearGradient = new LinearGradient(0, 0, getMeasuredWidth(), 0, mColors, mColorPositions, Shader.TileMode.CLAMP);
            mPaint.setShader(linearGradient);
        }

        LogUtil.d(TAG,"widthxheight = " + mWidth + "x" + mHeight);
        mColorRect = new RectF(getPaddingStart(), mHeight/2+mProgressHalfHeight, mWidth - getPaddingEnd(), mHeight/2-mProgressHalfHeight);// 设置个新的长方形
    }

    @Override
    protected void onDraw(Canvas canvas) {
        canvas.drawRoundRect(mColorRect, mProgressHalfHeight, mProgressHalfHeight, mPaint);//第二个参数是x半径，第三个参数是y半径

        Drawable thumb = getThumb();
        if (thumb != null) {
            final int saveCount = canvas.save();
            // Translate the padding. For the x, we need to allow the thumb to
            // draw in its extra space
            canvas.translate(getPaddingStart() - getThumbOffset(), getPaddingTop());

            Rect bounds = thumb.getBounds();
            thumb.draw(canvas);

            // 绘制圆环
//            mCirclePaint.setColor(getCurColor());
            mCirclePaint.setColor(mColors[getCurColorIndex()]);
            canvas.drawCircle((float) (bounds.left + bounds.right)/2, mHeight/2, (bounds.right - bounds.left)/2, mCirclePaint);
            canvas.restoreToCount(saveCount);
        }
    }

//    public int getCurColor() {
//        float percent = (float) getProgress() / (float) getMax();
//        return getColor(percent);
//    }

    public int getCurColorIndex() {
        float colorIndex = getProgress() * (mColors.length - 1) / 100f;
        return Math.round(colorIndex);
    }


    /**
     * 获取某个百分比位置的颜色
     *
     * @param radio 取值[0,1]
     */

    public int getColor(float radio) {
        int startColor;
        int endColor;
        if (radio >= 1) {
            return mColors[mColors.length - 1];
        }

        for (int i = 0; i < mColorPositions.length; i++) {
            if (radio <= mColorPositions[i]) {
                if (i == 0) {
                    return mColors[0];
                }
                startColor = mColors[i - 1];
                endColor = mColors[i];
                float areaRadio = getAreaRadio(radio, mColorPositions[i - 1], mColorPositions[i]);
                return getColorFrom(startColor, endColor, areaRadio);
            }
        }
        return -1;
    }


    public float getAreaRadio(float radio, float startPosition, float endPosition) {
        return (radio - startPosition) / (endPosition - startPosition);
    }


    /**
     * 取两个颜色间的渐变区间 中的某一点的颜色
     */

    public int getColorFrom(int startColor, int endColor, float radio) {
        int redStart = Color.red(startColor);
        int blueStart = Color.blue(startColor);
        int greenStart = Color.green(startColor);
        int redEnd = Color.red(endColor);
        int blueEnd = Color.blue(endColor);
        int greenEnd = Color.green(endColor);
        int red = (int) (redStart + ((redEnd - redStart) * radio + 0.5));
        int greed = (int) (greenStart + ((greenEnd - greenStart) * radio + 0.5));
        int blue = (int) (blueStart + ((blueEnd - blueStart) * radio + 0.5));
        return Color.argb(255, red, greed, blue);
    }


}
