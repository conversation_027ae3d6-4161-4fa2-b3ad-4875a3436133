package com.dfl.smartscene.ccs.db;

import android.text.TextUtils;

import com.alibaba.fastjson.JSON;
import com.dfl.smartscene.ccs.bean.CarConfig;
import com.dfl.smartscene.ccs.bean.DeviceOperation;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SceneCategory;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.db.entity.CarConfigEntity;
import com.dfl.smartscene.ccs.db.entity.CarConfigOverviewEntity;
import com.dfl.smartscene.ccs.db.entity.DeviceEntity;
import com.dfl.smartscene.ccs.db.entity.OperationActionEntity;
import com.dfl.smartscene.ccs.db.entity.OperationConditionEntity;
import com.dfl.smartscene.ccs.db.entity.OperationStateEntity;
import com.dfl.smartscene.ccs.db.entity.scene.SceneActionListEntity;
import com.dfl.smartscene.ccs.db.entity.scene.SceneCarConfigEntity;
import com.dfl.smartscene.ccs.db.entity.scene.SceneCategoryListEntity;
import com.dfl.smartscene.ccs.db.entity.scene.SceneConditionListEntity;
import com.dfl.smartscene.ccs.db.entity.scene.SceneListEntity;
import com.dfl.smartscene.ccs.db.entity.scene.SceneStateListEntity;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.util.FileUtils;
import com.iauto.uibase.utils.MLog;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;

import io.reactivex.Observable;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.ObservableSource;
import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Function;
import io.reactivex.schedulers.Schedulers;

public class CarConfigUtils {
    private static final String TAG = CarConfigUtils.class.getSimpleName();


    public static void copyDBToLocal() {

        Observable.create((ObservableOnSubscribe<Boolean>) emitter -> {
                    boolean copyState = true;
                    String absolutePath = ScenePatternApp.getInstance().getApplicationContext().getFilesDir().getParent() + File.separator + "databases";
                    try {
                        String[] list = ScenePatternApp.getInstance().getApplicationContext().getAssets().list("db");
                        if (list != null && list.length > 0) {
                            for (String fileName : list) {
                                boolean b = FileUtils.copyFileToLocal("db/" + fileName, absolutePath + File.separator + fileName);
                                if (!b) {
                                    copyState = false;
                                    break;
                                }
                            }
                        }

                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    if (copyState) {
                        emitter.onNext(true);
                    } else {
                        emitter.onError(new Throwable("copy file to local db error."));
                    }
                    emitter.onComplete();
                })
                .subscribeOn(Schedulers.io())
                .subscribe(new Observer<Boolean>() {
                    @Override
                    public void onSubscribe(Disposable d) {

                    }

                    @Override
                    public void onNext(Boolean aBoolean) {
                        MLog.d(TAG, "copy db to local success.");
                    }

                    @Override
                    public void onError(Throwable e) {
                        MLog.d(TAG, "copy db to local error.");
                    }

                    @Override
                    public void onComplete() {

                    }
                });
    }


    /**
     * 查询车辆配置信息
     *
     * @param carType 车辆型号
     * @return
     */
    public static Observable<CarConfig> queryCarConfig(String carType) {

        return TestDatabase.getInstance()
                .carConfigDao()
                .queryByCarType(carType)
                .subscribeOn(Schedulers.io())
                .flatMap(new Function<List<CarConfigEntity>, ObservableSource<CarConfig>>() {
                    @Override
                    public ObservableSource<CarConfig> apply(List<CarConfigEntity> carConfigEntities) throws Exception {

                        LinkedHashMap<String, List<String>> devicesMap = new LinkedHashMap<>();

                        for (CarConfigEntity item : carConfigEntities) {
                            if(carType.equals(item.car_type)) {
                                String operation_id = item.operation_id;
                                String device_id = item.device_id;
                                if (!TextUtils.isEmpty(operation_id)) {
                                    List<String> operationId = parseOneDimensionalArray(operation_id);
                                    if (operationId != null && !operationId.isEmpty()) {
                                        devicesMap.put(device_id, operationId);
                                    } else {
                                        devicesMap.put(device_id, new ArrayList<>(0));
                                    }
                                }
                            }
                        }

                        Observable<LinkedHashMap<String, List<String>>> just = Observable.just(devicesMap);

                        Observable<List<CarConfigOverviewEntity>> carConfigOverviewObservable = TestDatabase.getInstance().carConfigOverviewDao().queryByCarType(carType);

                        Observable<List<DeviceEntity>> listSingle1 = TestDatabase.getInstance().deviceDao().queryByIds(devicesMap.keySet().toArray(new String[0])).subscribeOn(Schedulers.io());
                        Observable<List<OperationConditionEntity>> listSingle2 = TestDatabase.getInstance().operationConditionDao().queryAll().subscribeOn(Schedulers.io());
                        Observable<List<OperationStateEntity>> listSingle3 = TestDatabase.getInstance().operationStateDao().queryAll().subscribeOn(Schedulers.io());
                        Observable<List<OperationActionEntity>> listSingle4 = TestDatabase.getInstance().operationActionDao().queryAll().subscribeOn(Schedulers.io());


                        return Observable.zip(just,carConfigOverviewObservable, listSingle1, listSingle2, listSingle3, listSingle4,
                                (devicesMap1, carConfigOverviewEntities, deviceEntities, operationConditionEntities, operationStateEntities, operationActionEntities) -> {

                                    MLog.d(TAG, "apply carConfigOverviewEntities = " + carConfigOverviewEntities);
                                    MLog.d(TAG, "apply deviceEntities = " + deviceEntities);
                                    MLog.d(TAG, "apply operationConditionEntities = " + operationConditionEntities);
                                    MLog.d(TAG, "apply operationStateEntities = " + operationStateEntities);
                                    MLog.d(TAG, "apply operationActionEntities = " + operationActionEntities);

                                    CarConfig carConfig = new CarConfig();
                                    List<DeviceOperation> conditionConfig = carConfig.getConditionConfig();
                                    List<DeviceOperation> stateConfig = carConfig.getStateConfig();
                                    List<DeviceOperation> actionConfig = carConfig.getActionConfig();

                                    devicesMap1.forEach((key, value) -> {

                                        DeviceEntity targetDeviceEntity = null;
                                        for (DeviceEntity deviceEntity : deviceEntities) {
                                            if (deviceEntity.id.equals(key)) {
                                                targetDeviceEntity = deviceEntity;
                                                break;
                                            }
                                        }
                                        String configType = null;
                                        String deviceId = null;
                                        DeviceOperation deviceOperation = new DeviceOperation();
                                        if (null != targetDeviceEntity) {
                                            deviceOperation.setDeviceId(targetDeviceEntity.mDeviceId);
                                            deviceOperation.setDeviceName(targetDeviceEntity.mDeviceName);
                                            deviceOperation.setDeviceIcon(targetDeviceEntity.mDeviceIcon);
                                            configType = targetDeviceEntity.configType;
                                            deviceId = targetDeviceEntity.mDeviceId;
                                        }

                                        List<SingleOperation> singleOperations = deviceOperation.getSingleOperations();

                                        if ("condition".equals(configType)) {
                                            for (OperationConditionEntity item : operationConditionEntities) {
                                                MLog.d(TAG,"condition ITEM " +item.toString());
                                                if (value.contains(item.id)) {
                                                    SingleOperation singleOperation = packetSingleOperation(operationConditionEntities, item, deviceId);
                                                    singleOperations.add(singleOperation);
                                                }
                                            }
                                            conditionConfig.add(deviceOperation);
                                        } else if ("state".equals(configType)) {
                                            for (OperationStateEntity item : operationStateEntities) {
//                                                MLog.d(TAG,"state ITEM " +item.toString());

                                                if (value.contains(item.id)) {
                                                    SingleOperation singleOperation = packetSingleOperation(operationStateEntities, item, deviceId);
                                                    singleOperations.add(singleOperation);
                                                }
                                            }
                                            stateConfig.add(deviceOperation);

                                        } else if ("action".equals(configType)) {
                                            for (OperationActionEntity item : operationActionEntities) {
                                                MLog.d(TAG,"action ITEM " +item.toString());

                                                if (value.contains(item.id)) {
                                                    SingleOperation singleOperation = packetSingleOperation(operationActionEntities, item, deviceId);
                                                    singleOperations.add(singleOperation);
                                                }
                                            }
                                            actionConfig.add(deviceOperation);
                                        } else {
                                            MLog.d(TAG, "device " + key + ", configType = " + configType);
                                        }

                                    });

                                    return carConfig;
                                });
                    }
                })
                .observeOn(AndroidSchedulers.mainThread());

    }

    private static SingleOperation packetSingleOperation(List<OperationConditionEntity> operationEntities, OperationConditionEntity item, String deviceId) {
        SingleOperation singleOperation = new SingleOperation();
        singleOperation.setDeviceId(deviceId);
        singleOperation.setOperationId(item.mOperationId);
        singleOperation.setOperationName(item.mOperationName);
        singleOperation.setOperationIcon(item.mOperationIcon);
        singleOperation.setShowMode(Integer.parseInt(item.mShowType));
        singleOperation.setViewType(item.mViewType);

        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_TEXT, item.VIEW_DESC_TEXT);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_TEXT_END, item.VIEW_DESC_TEXT_END);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_TEXT_BUTTOM, item.VIEW_DESC_TEXT_BUTTOM);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_PIC_START, item.VIEW_DESC_PIC_START);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_PIC_END, item.VIEW_DESC_PIC_END);
        singleOperation.setMapArgs(ConstantModelValue.OUTPUT_DESC_TEXT, item.OUTPUT_DESC_TEXT);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_PIC, item.VIEW_DESC_PIC);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DEFAULT_POS, item.VIEW_DEFAULT_POS);
        singleOperation.setMapArgs(ConstantModelValue.GEAR_P_LIMIT, item.GEAR_P_LIMIT);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_RANGE_HIGH, item.VIEW_DESC_RANGE_HIGH);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_RANGE_LOW, item.VIEW_DESC_RANGE_LOW);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_RANGE_STEP, item.VIEW_DESC_RANGE_STEP);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_RANGE_TYPE, item.VIEW_DESC_RANGE_TYPE);
        singleOperation.setMapArgs(ConstantModelValue.RELATED_DATA_CULCULATE, item.RELATED_DATA_CULCULATE);

        singleOperation.setMapArgs(ConstantModelValue.DATA_DESC_LIST, parseOneDimensionalArray(item.DATA_DESC_LIST));
        singleOperation.setMapArgs(ConstantModelValue.ACTION_DATA_POS_LIST, parseOneDimensionalArray(item.ACTION_DATA_POS_LIST));
        singleOperation.setMapArgs(ConstantModelValue.ACTION_METHOD_NAME_LIST, parseOneDimensionalArray(item.ACTION_METHOD_NAME_LIST));
        singleOperation.setMapArgs(ConstantModelValue.API_NAME_LIST, parseOneDimensionalArray(item.API_NAME_LIST));
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_TEXT_LIST, parseOneDimensionalArray(item.VIEW_DESC_TEXT_LIST));
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_PIC_LIST, parseOneDimensionalArray(item.VIEW_DESC_PIC_LIST));
        singleOperation.setMapArgs(ConstantModelValue.VIEW_CONFLICT_ARG_LIST, parseOneDimensionalArray(item.VIEW_CONFLICT_ARG_LIST));
        singleOperation.setMapArgs(ConstantModelValue.VIEW_CONFLICT_ID_LIST, parseOneDimensionalArray(item.VIEW_CONFLICT_ID_LIST));

        singleOperation.setDyadicMapArgs(ConstantModelValue.DATA_TYPE_DYADIC_LIST, parseTwoDimensionalArray(item.DATA_TYPE_DYADIC_LIST));
        singleOperation.setDyadicMapArgs(ConstantModelValue.DATA_DEFAULT_DYADIC_LIST, parseTwoDimensionalArray(item.DATA_DEFAULT_DYADIC_LIST));


        // 子操作列表
        String child_operation_id_list = item.CHILD_OPERATION_ID_LIST;
        if (child_operation_id_list != null && child_operation_id_list.length() > 0) {

            List<String> childOperationIds = parseOneDimensionalArray(child_operation_id_list);

            if (childOperationIds != null && !childOperationIds.isEmpty()) {
                List<SingleOperation> subOperations = new ArrayList<>(childOperationIds.size());

                for (String childOperationId : childOperationIds) {
                    for (OperationConditionEntity subItem : operationEntities) {
                        if (childOperationId.equals(subItem.id)) {

                            SingleOperation subSingleOperation = packetSingleOperation(operationEntities, subItem, deviceId);
                            subOperations.add(subSingleOperation);
                            break;
                        }
                    }
                }

                singleOperation.setSubOperations(subOperations);
            }
        }

        return singleOperation;
    }

    private static SingleOperation packetSingleOperation(List<OperationStateEntity> operationEntities, OperationStateEntity item, String deviceId) {
        SingleOperation singleOperation = new SingleOperation();
        singleOperation.setDeviceId(deviceId);
        singleOperation.setOperationId(item.mOperationId);
        singleOperation.setOperationName(item.mOperationName);
        singleOperation.setOperationIcon(item.mOperationIcon);
        singleOperation.setShowMode(Integer.parseInt(item.mShowType));
        singleOperation.setViewType(item.mViewType);

        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_TEXT, item.VIEW_DESC_TEXT);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_TEXT_END, item.VIEW_DESC_TEXT_END);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_TEXT_BUTTOM, item.VIEW_DESC_TEXT_BUTTOM);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_PIC_START, item.VIEW_DESC_PIC_START);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_PIC_END, item.VIEW_DESC_PIC_END);
        singleOperation.setMapArgs(ConstantModelValue.OUTPUT_DESC_TEXT, item.OUTPUT_DESC_TEXT);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_PIC, item.VIEW_DESC_PIC);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DEFAULT_POS, item.VIEW_DEFAULT_POS);
        singleOperation.setMapArgs(ConstantModelValue.GEAR_P_LIMIT, item.GEAR_P_LIMIT);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_RANGE_HIGH, item.VIEW_DESC_RANGE_HIGH);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_RANGE_LOW, item.VIEW_DESC_RANGE_LOW);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_RANGE_STEP, item.VIEW_DESC_RANGE_STEP);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_RANGE_TYPE, item.VIEW_DESC_RANGE_TYPE);
        singleOperation.setMapArgs(ConstantModelValue.RELATED_DATA_CULCULATE, item.RELATED_DATA_CULCULATE);

        singleOperation.setMapArgs(ConstantModelValue.DATA_DESC_LIST, parseOneDimensionalArray(item.DATA_DESC_LIST));
        singleOperation.setMapArgs(ConstantModelValue.ACTION_DATA_POS_LIST, parseOneDimensionalArray(item.ACTION_DATA_POS_LIST));
        singleOperation.setMapArgs(ConstantModelValue.ACTION_METHOD_NAME_LIST, parseOneDimensionalArray(item.ACTION_METHOD_NAME_LIST));
        singleOperation.setMapArgs(ConstantModelValue.API_NAME_LIST, parseOneDimensionalArray(item.API_NAME_LIST));
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_TEXT_LIST, parseOneDimensionalArray(item.VIEW_DESC_TEXT_LIST));
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_PIC_LIST, parseOneDimensionalArray(item.VIEW_DESC_PIC_LIST));
        singleOperation.setMapArgs(ConstantModelValue.VIEW_CONFLICT_ARG_LIST, parseOneDimensionalArray(item.VIEW_CONFLICT_ARG_LIST));
        singleOperation.setMapArgs(ConstantModelValue.VIEW_CONFLICT_ID_LIST, parseOneDimensionalArray(item.VIEW_CONFLICT_ID_LIST));

        singleOperation.setDyadicMapArgs(ConstantModelValue.DATA_TYPE_DYADIC_LIST, parseTwoDimensionalArray(item.DATA_TYPE_DYADIC_LIST));
        singleOperation.setDyadicMapArgs(ConstantModelValue.DATA_DEFAULT_DYADIC_LIST, parseTwoDimensionalArray(item.DATA_DEFAULT_DYADIC_LIST));


        // 子操作列表
        String child_operation_id_list = item.CHILD_OPERATION_ID_LIST;
        if (child_operation_id_list != null && child_operation_id_list.length() > 0) {

            List<String> childOperationIds = parseOneDimensionalArray(child_operation_id_list);

            if (childOperationIds != null && !childOperationIds.isEmpty()) {
                List<SingleOperation> subOperations = new ArrayList<>(childOperationIds.size());

                for (String childOperationId : childOperationIds) {
                    for (OperationStateEntity subItem : operationEntities) {
                        if (childOperationId.equals(subItem.id)) {

                            SingleOperation subSingleOperation = packetSingleOperation(operationEntities, subItem, deviceId);
                            subOperations.add(subSingleOperation);
                            break;
                        }
                    }
                }

                singleOperation.setSubOperations(subOperations);
            }
        }
        return singleOperation;
    }

    private static SingleOperation packetSingleOperation(List<OperationActionEntity> operationEntities, OperationActionEntity item, String deviceId) {
        SingleOperation singleOperation = new SingleOperation();
        singleOperation.setDeviceId(deviceId);
        singleOperation.setOperationId(item.mOperationId);
        singleOperation.setOperationName(item.mOperationName);
        singleOperation.setOperationIcon(item.mOperationIcon);
        singleOperation.setShowMode(Integer.parseInt(item.mShowType));
        singleOperation.setViewType(item.mViewType);

        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_TEXT, item.VIEW_DESC_TEXT);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_TEXT_END, item.VIEW_DESC_TEXT_END);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_TEXT_BUTTOM, item.VIEW_DESC_TEXT_BUTTOM);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_PIC_START, item.VIEW_DESC_PIC_START);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_PIC_END, item.VIEW_DESC_PIC_END);
        singleOperation.setMapArgs(ConstantModelValue.OUTPUT_DESC_TEXT, item.OUTPUT_DESC_TEXT);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_PIC, item.VIEW_DESC_PIC);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DEFAULT_POS, item.VIEW_DEFAULT_POS);
        singleOperation.setMapArgs(ConstantModelValue.GEAR_P_LIMIT, item.GEAR_P_LIMIT);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_RANGE_HIGH, item.VIEW_DESC_RANGE_HIGH);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_RANGE_LOW, item.VIEW_DESC_RANGE_LOW);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_RANGE_STEP, item.VIEW_DESC_RANGE_STEP);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_RANGE_TYPE, item.VIEW_DESC_RANGE_TYPE);
        singleOperation.setMapArgs(ConstantModelValue.RELATED_DATA_CULCULATE, item.RELATED_DATA_CULCULATE);

        singleOperation.setMapArgs(ConstantModelValue.DATA_DESC_LIST, parseOneDimensionalArray(item.DATA_DESC_LIST));
        singleOperation.setMapArgs(ConstantModelValue.ACTION_DATA_POS_LIST, parseOneDimensionalArray(item.ACTION_DATA_POS_LIST));
        singleOperation.setMapArgs(ConstantModelValue.ACTION_METHOD_NAME_LIST, parseOneDimensionalArray(item.ACTION_METHOD_NAME_LIST));
        singleOperation.setMapArgs(ConstantModelValue.API_NAME_LIST, parseOneDimensionalArray(item.API_NAME_LIST));
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_TEXT_LIST, parseOneDimensionalArray(item.VIEW_DESC_TEXT_LIST));
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_PIC_LIST, parseOneDimensionalArray(item.VIEW_DESC_PIC_LIST));
        singleOperation.setMapArgs(ConstantModelValue.VIEW_CONFLICT_ARG_LIST, parseOneDimensionalArray(item.VIEW_CONFLICT_ARG_LIST));
        singleOperation.setMapArgs(ConstantModelValue.VIEW_CONFLICT_ID_LIST, parseOneDimensionalArray(item.VIEW_CONFLICT_ID_LIST));

        singleOperation.setDyadicMapArgs(ConstantModelValue.DATA_TYPE_DYADIC_LIST, parseTwoDimensionalArray(item.DATA_TYPE_DYADIC_LIST));
        singleOperation.setDyadicMapArgs(ConstantModelValue.DATA_DEFAULT_DYADIC_LIST, parseTwoDimensionalArray(item.DATA_DEFAULT_DYADIC_LIST));


        // 子操作列表
        String child_operation_id_list = item.CHILD_OPERATION_ID_LIST;
        if (child_operation_id_list != null && child_operation_id_list.length() > 0) {

            List<String> childOperationIds = parseOneDimensionalArray(child_operation_id_list);

            if (childOperationIds != null && !childOperationIds.isEmpty()) {
                List<SingleOperation> subOperations = new ArrayList<>(childOperationIds.size());

                for (String childOperationId : childOperationIds) {
                    for (OperationActionEntity subItem : operationEntities) {
                        if (childOperationId.equals(subItem.id)) {

                            SingleOperation subSingleOperation = packetSingleOperation(operationEntities, subItem, deviceId);
                            subOperations.add(subSingleOperation);
                            break;
                        }
                    }
                }

                singleOperation.setSubOperations(subOperations);
            }
        }

        return singleOperation;
    }

    public static Observable<List<SceneCategory>> querySceneByCarType(String car_type) {

        MLog.d(TAG,"querySceneByCarType = " + car_type);

        return TestDatabase.getInstance()
                .sceneCarConfigDao()
                .queryByCarType(car_type)
                .subscribeOn(Schedulers.io())
                .flatMap((Function<List<SceneCarConfigEntity>, ObservableSource<List<SceneCategory>>>) sceneCarConfigEntitys -> {

                    if (sceneCarConfigEntitys == null) {
                        return null;
                    }

                    ArrayList<String> sceneListKeys = new ArrayList<>();
                    ArrayList<String> sceneCategoryListKeys = new ArrayList<>();

                    for (SceneCarConfigEntity sceneCarConfigEntity : sceneCarConfigEntitys) {

                        MLog.d(TAG,"sceneCarConfigEntity = " + sceneCarConfigEntity.toString());


                        List<String> sceneIds = parseOneDimensionalArray(sceneCarConfigEntity.scene_id);
                        if (sceneIds != null && !sceneIds.isEmpty()) {
                            sceneListKeys.addAll(sceneIds);
                        }

                        if(!TextUtils.isEmpty(sceneCarConfigEntity.category_id)) {
                            sceneCategoryListKeys.add(sceneCarConfigEntity.category_id);
                        }
                    }

                    Observable<List<SceneCarConfigEntity>> sceneCarConfigObservable = Observable.just(sceneCarConfigEntitys);
                    Observable<List<SceneListEntity>> sceneListObservable = TestDatabase.getInstance().sceneListDao().queryBySceneIds(sceneListKeys.toArray(new String[0]));
                    Observable<List<SceneCategoryListEntity>> sceneCategoryListObservable = TestDatabase.getInstance().sceneCategoryListDao().queryBySceneCategoryIds(sceneCategoryListKeys.toArray(new String[0]));
                    Observable<List<SceneConditionListEntity>> sceneConditionListObservable = TestDatabase.getInstance().sceneConditionListDao().queryBySceneIds(sceneListKeys.toArray(new String[0]));
                    Observable<List<SceneStateListEntity>> sceneStateListObservable = TestDatabase.getInstance().sceneStateListDao().queryBySceneIds(sceneListKeys.toArray(new String[0]));
                    Observable<List<SceneActionListEntity>> sceneActionListObservable = TestDatabase.getInstance().sceneActionListDao().queryBySceneIds(sceneListKeys.toArray(new String[0]));

                    return Observable.zip(sceneCarConfigObservable, sceneListObservable, sceneCategoryListObservable,
                            sceneConditionListObservable, sceneStateListObservable, sceneActionListObservable,
                            (sceneCarConfigEntityList, sceneListEntities, sceneCategoryListEntities, sceneConditionListEntities, sceneStateListEntities, sceneActionListEntities) -> {

                                MLog.d(TAG, "apply sceneCarConfigEntityList = " + sceneCarConfigEntityList);
                                MLog.d(TAG, "apply sceneListEntities = " + sceneListEntities);
                                MLog.d(TAG, "apply sceneCategoryListEntities = " + sceneCategoryListEntities);
                                MLog.d(TAG, "apply sceneConditionListEntities = " + sceneConditionListEntities);
                                MLog.d(TAG, "apply sceneStateListEntities = " + sceneStateListEntities);
                                MLog.d(TAG, "apply sceneActionListEntities = " + sceneActionListEntities);


                                ArrayList<SceneCategory> sceneCategories = new ArrayList<>();

                                for (SceneCarConfigEntity sceneCarConfigEntity : sceneCarConfigEntityList) {
                                    String categoryId = sceneCarConfigEntity.category_id;
                                    String scene_ids = sceneCarConfigEntity.scene_id;

                                    SceneCategory sceneCategory = new SceneCategory();
                                    sceneCategory.setCategoryId(categoryId);
                                    for (SceneCategoryListEntity sceneCategoryListEntity : sceneCategoryListEntities) {
                                        if(categoryId.equals(sceneCategoryListEntity.mSceneCategoryId)) {
                                            sceneCategory.setCategoryName(sceneCategoryListEntity.mCategoryName);
                                            break;
                                        }
                                    }

                                    ArrayList<SceneBean> sceneBeanArrayList = new ArrayList<>();

                                    List<String> sceneIds = parseOneDimensionalArray(scene_ids);
                                    if(sceneIds != null && !sceneIds.isEmpty()) {
                                        for (String sceneId : sceneIds) {
                                            SceneBean sceneBean = new SceneBean();

                                            for (SceneListEntity sceneListEntity : sceneListEntities) {
                                                if(sceneId.equals(sceneListEntity.mSceneId)) {
                                                    sceneBean.setSceneId(sceneListEntity.mSceneId);
                                                    sceneBean.setScenePic(sceneListEntity.mScenePic);
                                                    sceneBean.setSceneDesc(sceneListEntity.mSceneDesc);
                                                    sceneBean.setSceneName(sceneListEntity.mSceneName);
                                                    sceneBean.setSceneCategoryId(sceneListEntity.mSceneCategoryId);
                                                    sceneBean.setSceneIcon(sceneListEntity.mSceneIcon);
                                                    sceneBean.setNew("true".equalsIgnoreCase(sceneListEntity.mIsNew));
                                                    sceneBean.setEditType(Integer.parseInt(sceneListEntity.mEditType));
                                                    break;
                                                }
                                            }

                                            // State
                                            ArrayList<SettingOperation> stateSettingOperations = new ArrayList<>();
                                            for (SceneStateListEntity item : sceneStateListEntities) {
                                                if(sceneId.equals(item.mSceneId)) {
                                                    SettingOperation settingOperation = new SettingOperation();
                                                    settingOperation.setOperationId(item.mOperationId);
                                                    settingOperation.setDeviceId(item.mDeviceId);
                                                    settingOperation.setDesc(item.mDesc);
                                                    settingOperation.setShowType(Integer.parseInt(item.mShowType));
                                                    settingOperation.setListArgs(parseOneDimensionalArray(item.mListArgs));
                                                    stateSettingOperations.add(settingOperation);
                                                }
                                            }
                                            sceneBean.setStateOperations(stateSettingOperations);

                                            // Condition
                                            ArrayList<SettingOperation> conditionSettingOperations = new ArrayList<>();
                                            for (SceneConditionListEntity item : sceneConditionListEntities) {
                                                if(sceneId.equals(item.mSceneId)) {
                                                    SettingOperation settingOperation = new SettingOperation();
                                                    settingOperation.setOperationId(item.mOperationId);
                                                    settingOperation.setDeviceId(item.mDeviceId);
                                                    settingOperation.setDesc(item.mDesc);
                                                    settingOperation.setShowType(Integer.parseInt(item.mShowType));
                                                    settingOperation.setListArgs(parseOneDimensionalArray(item.mListArgs));
                                                    conditionSettingOperations.add(settingOperation);
                                                }
                                            }
                                            sceneBean.setConditionOperations(conditionSettingOperations);

                                            // Action
                                            ArrayList<SettingOperation> actionSettingOperations = new ArrayList<>();
                                            for (SceneActionListEntity item : sceneActionListEntities) {
                                                if(sceneId.equals(item.mSceneId)) {
                                                    SettingOperation settingOperation = new SettingOperation();
                                                    settingOperation.setOperationId(item.mOperationId);
                                                    settingOperation.setDeviceId(item.mDeviceId);
                                                    settingOperation.setDesc(item.mDesc);
                                                    settingOperation.setShowType(Integer.parseInt(item.mShowType));
                                                    settingOperation.setListArgs(parseOneDimensionalArray(item.mListArgs));
                                                    actionSettingOperations.add(settingOperation);
                                                }
                                            }
                                            sceneBean.setActionOperations(actionSettingOperations);

                                            sceneBeanArrayList.add(sceneBean);
                                        }
                                    }
                                    sceneCategory.setSceneBeanList(sceneBeanArrayList);

                                    sceneCategories.add(sceneCategory);
                                }

                                return sceneCategories;
                            });

                })
                .observeOn(AndroidSchedulers.mainThread());

    }

    public static List<String> parseOneDimensionalArray(String str) {
        if (null == str || str.length() == 0) {
            return null;
        }

        String[] longs = JSON.parseObject(str, String[].class);
        return Arrays.asList(longs);
    }

    public static List<List<String>> parseTwoDimensionalArray(String str) {
        if (null == str || str.length() == 0) {
            return null;
        }

        String[][] longs = JSON.parseObject(str, String[][].class);

        if (longs == null || longs.length == 0) {
            return null;
        }

        List<List<String>> list = new ArrayList<>(longs.length);
        for (String[] aLong : longs) {
            List<String> strings = Arrays.asList(aLong);
            list.add(strings);
        }

        return list;
    }

}
