package com.dfl.smartscene.ccs.model;

import android.graphics.Bitmap;

import com.iauto.uibase.utils.MLog;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

/*
 * 彩蛋item类
 * <AUTHOR>
 * @date 2022/5/15
 */
public class SurpriseEgg {
    private static final String TAG = "SurpriseEgg";
    private int    mEggID;      // 彩蛋ID
    private Date   mTime;       // 彩蛋创建时间
    private String mImageURL;    // 画像url
    private String mURL;        // 彩蛋URL
    private String mName;       // 彩蛋名称
    private String mTTSText;    //tts语音内容
    private String mVoiceFile;  //录音文件
    private int mVoiceType;      //音频类型 1-文本 2-文件

    private static final String sDataFormat = "yyyy-MM-dd HH:mm:ss";

    /**
     * ScenePatternInterrupt object constructor
     */
    public SurpriseEgg() {
        mEggID = -1;
        makeInitDate();
        mURL = "";
        mName = "";
        mTTSText = "";
        mImageURL = "";
        mVoiceFile = "";
        mVoiceType = 1;
    }

    /**
     * ScenePatternInterrupt object constructor
     * @param time 创建时间
     * @param imageData 缩略图bitmap
     * @param URL  彩蛋播放URL
     * @param name 彩蛋名称
     */
    public SurpriseEgg(Date time, Bitmap imageData, String URL, String name) {
        this.mTime = (Date) time.clone();
        mURL = URL;
        mName = name;
    }

    public int getEggID() {
        return mEggID;
    }

    public Date getTime() {
        return (Date) mTime.clone();
    }

    public String getURL() {
        return mURL;
    }

    public String getName() {
        return mName;
    }

    public String getTTSText() {
        return mTTSText;
    }

    public String getImageURL() {
        return mImageURL;
    }

    public void setEggID (int eggID) {
        this.mEggID = eggID;
    }

    public void setURL(String URL) {
        this.mURL = URL;
    }

    public void setTime(Date time) {
        this.mTime = (Date) time.clone();
    }

    public void setName(String name) {
        this.mName = name;
    }

    public void setTTSText(String TTSText) {
        this.mTTSText = TTSText;
    }

    public void setImageURL(String imageURL) {
        this.mImageURL = imageURL;
    }

    public String getVoiceFile() {
        return mVoiceFile;
    }

    public void setVoiceFile(String mVoiceFile) {
        this.mVoiceFile = mVoiceFile;
    }

    public int getVoiceType() {
        return mVoiceType;
    }

    public void setVoiceType(int mVoiceType) {
        this.mVoiceType = mVoiceType;
    }
//    /**
//     * 下载缩略图
//     * @param url 缩略图URL
//     */
//    private Bitmap downloadImage(String url) {
//        MLog.d(TAG,"downloadImage");
//        Bitmap targetBitmap = null;
//        if (url == null) {
//            return null;
//        }
//        InputStream stream = null;
//        BitmapFactory.Options bmOptions = new BitmapFactory.Options();
//        bmOptions.inJustDecodeBounds = true;
//        try {
//            stream = getHttpConnection(url);
//            if (null == stream) {
//                return null;
//            }
//
//            byte[] imageByte = readStream(stream);
//
//            MLog.d(TAG,"outHeight:"+bmOptions.outHeight);
//            MLog.d(TAG,"outHeight:"+bmOptions.outWidth);
//            int targetWidth = 320;
//            int targetHeight = 188;
//
//            final float zoomWidth = (float)bmOptions.outWidth/(float)targetWidth;
//            final float zoomHeight = (float)bmOptions.outHeight/(float)targetHeight;
//
//            MLog.d(TAG,"zoomWidth:"+zoomWidth);
//            MLog.d(TAG,"zoomHeight:"+zoomHeight);
//            if(zoomWidth>=zoomHeight) {
//                targetHeight = (int) (bmOptions.outHeight/zoomWidth);
//            } else {
//                targetWidth = (int) (bmOptions.outWidth/zoomHeight);
//            }
//            MLog.d(TAG,"changed outHeight:"+targetHeight);
//            MLog.d(TAG,"changed outWidth:"+targetWidth);
//
//            int inSampleSize = 1;
//            if (bmOptions.outHeight > targetHeight || bmOptions.outWidth > targetWidth) {
//                final int halfHeight = bmOptions.outHeight / 2;
//                final int halfWidth = bmOptions.outWidth / 2;
//                while ((halfHeight / inSampleSize) > targetHeight
//                        && (halfWidth / inSampleSize) > targetWidth) {
//                    inSampleSize *= 2;
//                }
//            }
//            bmOptions.inSampleSize = inSampleSize;
//            MLog.d(TAG,"inSampleSize:"+bmOptions.inSampleSize);
//            bmOptions.inJustDecodeBounds = false;
//            bmOptions.inPreferredConfig = null;
//            targetBitmap = BitmapFactory.decodeByteArray(imageByte, 0, imageByte.length, bmOptions);
//        } catch (IOException e1) {
//            e1.printStackTrace();
//            MLog.d(TAG,"IOException:"+e1.toString());
//        }
//        return targetBitmap;
//    }

//    private byte[] readStream(InputStream inStream) throws IOException {
//        byte[] buffer = new byte[1024];
//        int len = -1;
//        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
//        while ((len = inStream.read(buffer)) != -1) {
//            outStream.write(buffer, 0, len);
//        }
//        byte[] data = outStream.toByteArray();
//        outStream.close();
//        inStream.close();
//        return data;
//    }
//
//    /**
//     * 通过URL取得Http流数据
//     * @param urlString 缩略图URL
//     */
//    private InputStream getHttpConnection(String urlString) {
//        MLog.d(TAG,"downloadImage");
//        MLog.d(TAG,"urlString:"+urlString);
//        InputStream stream = null;
//
//        try {
//            URL url = new URL(urlString);
//            URLConnection connection = url.openConnection();
//            HttpURLConnection httpConnection = (HttpURLConnection) connection;
//            httpConnection.setRequestMethod("GET");
//            httpConnection.connect();
//            if (httpConnection.getResponseCode() == HttpURLConnection.HTTP_OK) {
//                MLog.d(TAG,"HTTP OK");
//                stream = httpConnection.getInputStream();
//            }
//        } catch (IOException ex) {
//            ex.printStackTrace();
//            MLog.d(TAG,"IOException:"+ex.toString());
//        }
//        return stream;
//    }

    /**
     * 将彩蛋做成时间更改成固定格式的表示时间
     * @param strDate 从json取得的做成时间
     */
    public boolean makeCreateDate(String strDate) {
        MLog.d(TAG,"makeCreateDate:strDate:"+strDate);
        Date tempDate = null;
        String tempStrDate = strDate.replace("T", " ");
        DateFormat formatter = new SimpleDateFormat(sDataFormat, Locale.getDefault());

        try {
            tempDate = formatter.parse(tempStrDate);
        } catch (ParseException e) {
            e.printStackTrace();
            return false;
        }
        this.mTime = tempDate;
        return true;
    }

    /**
     * 用当前时间初始化彩蛋做成时间
     */
    private void makeInitDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());
        mTime = calendar.getTime();
    }
}
