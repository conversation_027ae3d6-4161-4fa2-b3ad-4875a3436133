package com.dfl.smartscene.ccs.factory;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SceneCategory;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.fragment.AutoPStateOperation;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.model.DriveModel;
import com.dfl.smartscene.ccs.model.SceneDataModel;
import com.dfl.smartscene.ccs.util.CulculateUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/9/7 9:18
 * @description ：需本地创建的具有特殊意义的对象
 */
public class SpecialBeanFactory {

    /**
     * 用于展示添加更多场景按钮的scenebean
     * @return
     */
    public static SceneBean productAddSceneBean(){
        SceneBean sceneBean = new SceneBean();
        sceneBean.setSceneId(ConstantModelValue.SCENE_ID_ADD);
        sceneBean.setSceneName("添加场景");
        return sceneBean;
    }

    /**
     * 用于展示添加更多状态条件的bean
     * @return
     */
    public static SettingOperation productAddStateOperation(){
        SettingOperation addStateOperation = new SettingOperation();
        addStateOperation.setOperationId(ConstantModelValue.OPERATION_ID_ADD);
        addStateOperation.setDesc("添加状态条件");
        return addStateOperation;
    }

    /**
     * 用于展示是否在某一时间范围内的默认时间状态条件
     * @return
     */
    public static SettingOperation productTimeOperation(){
        SettingOperation addStateOperation = new SettingOperation();
        addStateOperation.setOperationId(ConstantModelValue.OPERATION_ID_STATE_TIME_CLOCK);
        addStateOperation.setDesc("时间: 全天");
        addStateOperation.setListArgs(Arrays.asList("00:00","00:00"));
        return addStateOperation;
    }

    /**
     * 用于展示是否是在某一天的默认时间状态条件
     * @return
     */
    public static SettingOperation productDayOperation(){
        SettingOperation addStateOperation = new SettingOperation();
        addStateOperation.setOperationId(ConstantModelValue.OPERATION_ID_STATE_TIME_DAY);
        addStateOperation.setDesc("重复: 每天");
        addStateOperation.setListArgs(Collections.singletonList(ConstantModelValue.VALUE_STATE_DAY_EVERYDAY));
        return addStateOperation;
    }

    /**
     * 用于展示添加更多执行动作的bean
     * @return
     */
    public static SettingOperation productAddActionOperation(){
        SettingOperation addStateOperation = new SettingOperation();
        addStateOperation.setOperationId(ConstantModelValue.OPERATION_ID_ADD);

        addStateOperation.setDesc("添加执行操作");
        return addStateOperation;

    }

    /**
     * 用于展示添加触发条件的bean
     * @return
     */
    public static SettingOperation productAddConditionOperation(){
        SettingOperation addConditionOperation = new SettingOperation();
        addConditionOperation.setOperationId(ConstantModelValue.OPERATION_ID_ADD);
        addConditionOperation.setDesc("添加触发条件");
        return addConditionOperation;
    }

    /**
     * 用于展示添加语音指令的bean
     * @return
     */
    public static SettingOperation productAddVrOperation(){
        SettingOperation addConditionOperation = new SettingOperation();
        addConditionOperation.setOperationId(ConstantModelValue.OPERATION_ID_ADD);
        addConditionOperation.setDesc("添加语音指令");
        return addConditionOperation;
    }

    /**
     * 用于展示p挡状态条件的操作
     * @return
     */
    public static AutoPStateOperation productPGearStateOperation(){
        AutoPStateOperation settingOperation = new AutoPStateOperation();
        settingOperation.setOperationId(ConstantModelValue.OPERATION_ID_STATE_DRIVE_GEAR);
        settingOperation.setDeviceId(ConstantModelValue.DEVICE_ID_DRIVE);
        settingOperation.setListArgs(Collections.singletonList(DriveModel.getInstance().getGearPValue()));
        settingOperation.setDesc("车辆挡位 P挡");
        return settingOperation;
    }

    /**
     * 用于展示自定义分类的分类
     * @return
     */
    public static SceneCategory productCustomCategory(){
        SceneCategory sceneCategory = new SceneCategory();
        sceneCategory.setCategoryId(ConstantModelValue.CATEGORY_ID_CUSTOM);
        sceneCategory.setCategoryName("自定义");
        return sceneCategory;
    }

    /**
     * 用于展示收藏分类的分类
     * @return
     */
    public static SceneCategory productCollectCategory(){
        SceneCategory sceneCategory = new SceneCategory();
        sceneCategory.setCategoryId(ConstantModelValue.CATEGORY_ID_COLLECT);
        sceneCategory.setCategoryName("我的收藏");
        return sceneCategory;
    }
    /**
     * 用于展示选取工作日/节假日/每日的视图展示的配置
     * @return
     */
    public static SingleOperation productStateDayOperation(){
        SingleOperation singleOperation = new SingleOperation();
        singleOperation.setOperationId(ConstantModelValue.OPERATION_ID_STATE_TIME_DAY);
        singleOperation.setViewType(ConstantModelValue.VIEW_TYPE_STATE_TIME_DAY);
        singleOperation.setOperationName("重复");
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_TEXT_LIST, Arrays.asList("每日","法定工作日","法定节假日","自定义"));
        singleOperation.setMapArgs(ConstantModelValue.DATA_DESC_LIST, Arrays.asList(ConstantModelValue.VALUE_STATE_DAY_EVERYDAY, ConstantModelValue.VALUE_STATE_DAY_WORKDAY, ConstantModelValue.VALUE_STATE_DAY_HOLIDAY, ConstantModelValue.VALUE_STATE_DAY_USERDAY));
        singleOperation.setMapArgs(ConstantModelValue.OUTPUT_DESC_TEXT,"重复: &");
        return singleOperation;
    }

    /**
     * 用于展示时间状态条件的配置操作
     * @return
     */
    public static SingleOperation productStateClockOperation(){
        SingleOperation singleOperation = new SingleOperation();
        singleOperation.setOperationId(ConstantModelValue.OPERATION_ID_STATE_TIME_CLOCK);
        singleOperation.setViewType(ConstantModelValue.VIEW_TYPE_STATE_TIME_CLOCK);
        singleOperation.setMapArgs(ConstantModelValue.OUTPUT_DESC_TEXT,"重复：&");
        singleOperation.setOperationName("时间");

        return singleOperation;
    }


    /**
     * 根据传入分类复制一个空场景的分类
     * @param data
     * @return
     */
    public static SceneCategory productEmptyCategory(SceneCategory data){
        SceneCategory sceneCategory = new SceneCategory();
        sceneCategory.setCategoryId(data.getCategoryId());
        sceneCategory.setCategoryName(data.getCategoryName());
        return sceneCategory;
    }


//    private static final String KEY_NEW_SCENE_POS = "KEY_NEW_SCENE_POS";
//    public static SceneBean productNewSceneBean(){
//        MMKV mmkv = MMKV.defaultMMKV();
//        mmkv.decodeInt(KEY_NEW_SCENE_POS,0);
//    }

    /**
     * 随机生成一个用户的场景id
     * @return
     */
    public static String productSceneId(){
        return "userscene" + System.currentTimeMillis();
    }

    /**
     * 新建场景时自动赋予名字
     * 自定义场景x
     * 当前名称中未使用的最小数字
     * @return
     */
    public static String productSceneName(){
        String defaultName = ScenePatternApp.getInstance().getString(R.string.string_scene_editor_default_name);
        List<Integer> useInt = new ArrayList<>();
        int result = 1;
        SceneCategory sceneCategory = SceneDataModel.getInstance().findUserCategoryById(ConstantModelValue.CATEGORY_ID_CUSTOM);
        if(sceneCategory != null){
            sceneCategory.getSceneBeanList().forEach(sceneBean -> {
                String sceneName = sceneBean.getSceneName();
                if(sceneName.startsWith(defaultName) && CulculateUtil.isNumber(sceneName.substring(defaultName.length()))){
                    useInt.add(Integer.parseInt(sceneName.substring(defaultName.length())));
                }
            });
            result = useInt.size() + 1;
            for(int i = 1 ; 1<= useInt.size() ; i++){
                if(!useInt.contains(i)){
                    result = i;
                    break;
                }
            }
        }
        return defaultName + result;
    }

    /**
     * 新建一个title类型的操作
     * @param name
     * @return
     */
    public static SingleOperation productTitleOperation(String name){
        SingleOperation singleOperation = new SingleOperation();
        singleOperation.setOperationId(ConstantModelValue.OPERATION_ID_TITLE);
        singleOperation.setViewType(ConstantModelValue.VIEW_TYPE_TITLE);
        singleOperation.setOperationName(name);
        return singleOperation;
    }

}
