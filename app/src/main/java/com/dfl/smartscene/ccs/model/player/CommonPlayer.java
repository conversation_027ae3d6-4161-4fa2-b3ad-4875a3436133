package com.dfl.smartscene.ccs.model.player;

import android.content.ComponentName;
import android.os.Message;
import android.os.RemoteException;
import android.support.v4.media.MediaBrowserCompat;
import android.support.v4.media.session.MediaControllerCompat;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.util.HandlerUtil;
import com.dfl.smartscene.ccs.util.ListUtil;
import com.iauto.uibase.utils.MLog;

import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @date ：2023/5/30 11:39
 * @description ：通用播放器
 */
public class CommonPlayer implements Player {

    private SettingOperation mSettingOperation;
    private ComponentName mComponentName;
    private MediaBrowserCompat mMediaBrowserCompat;
    private MediaControllerCompat mMediaControllerCompat;
    private int mConnectStatus = CONNECT_STATE_DISCONNECTED;
    private static final String TAG = CommonPlayer.class.getSimpleName();
    private static ReentrantLock lock = new ReentrantLock();

    private static final int CONNECT_STATE_DISCONNECTING = 0;
    private static final int CONNECT_STATE_DISCONNECTED = 1;
    private static final int CONNECT_STATE_CONNECTING = 2;
    private static final int CONNECT_STATE_CONNECTED = 3;
    private static final int CONNECT_STATE_SUSPENDED = 4;

    public CommonPlayer(ComponentName componentName){
        mComponentName = componentName;
    }

    protected MediaControllerCompat getController(){
        if(mMediaControllerCompat == null){
            LogUtil.d(TAG, "getController: " + mMediaControllerCompat);
        }
        return mMediaControllerCompat;
    }
    public CommonPlayer(CommonPlayer commonPlayer){
        mComponentName = commonPlayer.mComponentName;
        mMediaBrowserCompat = commonPlayer.mMediaBrowserCompat;
        mMediaControllerCompat = commonPlayer.mMediaControllerCompat;
    }

    @Override
    public String getTag() {
        if(mComponentName!= null){
            return mComponentName.getPackageName();
        }else {
            return "";
        }
    }

    @Override
    public void start(SettingOperation settingOperation) {
        if(settingOperation != null && !ListUtil.isEmpty(settingOperation.getListArgs())){
            if(mMediaBrowserCompat !=null && mMediaBrowserCompat.isConnected()){
                play(settingOperation.getListArgs().get(0));
            }else {
                connectMediaSession(() -> play(settingOperation.getListArgs().get(0)));
            }

        }
    }

    public void play(String arg){

    }

    @Override
    public void pause() {
        if(mMediaBrowserCompat !=null && mMediaBrowserCompat.isConnected()){
            if(mMediaControllerCompat != null){
                mMediaControllerCompat.getTransportControls().pause();
            }else {
                LogUtil.d(TAG, "pause: mMediaControllerCompat is null ");
            }
        }else {
            connectMediaSession(() -> {
                if(mMediaControllerCompat != null){
                    mMediaControllerCompat.getTransportControls().pause();
                }else {
                    LogUtil.d(TAG, "pause: mMediaControllerCompat is null ");
                }
            });
        }
    }

    @Override
    public void resume() {
        if(mMediaBrowserCompat !=null && mMediaBrowserCompat.isConnected()){
            if(mMediaControllerCompat != null){
                mMediaControllerCompat.getTransportControls().play();
            }else {
                LogUtil.d(TAG, "resume: mMediaControllerCompat is null ");
            }
        }else {
            connectMediaSession(() -> {
                if(mMediaControllerCompat != null){
                    mMediaControllerCompat.getTransportControls().play();
                }else {
                    LogUtil.d(TAG, "resume: mMediaControllerCompat is null ");
                }
            });
        }
    }

    @Override
    public void destroy() {
        LogUtil.d(TAG,"destroy");
        if(mMediaBrowserCompat != null && mMediaBrowserCompat.isConnected()){
            mMediaBrowserCompat.disconnect();
        }
        if (mConnectStatus != CONNECT_STATE_SUSPENDED) {
            mConnectStatus = CONNECT_STATE_DISCONNECTED;
        }
        mMediaControllerCompat = null;
    }

    public void connectMediaSession(){
        connectMediaSession(null);
    }

    /**
     * 连接mediasession
     *
     */
    public void connectMediaSession(Runnable runnable) {
        String pkgName ;
        String serviceName;

        if(mComponentName != null){
             pkgName = mComponentName.getPackageName();
             serviceName= mComponentName.getClassName();
        } else {
            pkgName = null;
            serviceName = null;
        }


        MLog.d(TAG, "connectMediaSession :" + "NAME = " + pkgName + ", serviceName  = " + serviceName);

        if(mConnectStatus == CONNECT_STATE_CONNECTING){
            mBroserConnectCallback.mRunnable = runnable;
            return;
        }
        Message.obtain(HandlerUtil.getMainHandler(), () -> {
            MediaBrowserCompat mediaBrowserCompat = mMediaBrowserCompat;
            if (mediaBrowserCompat != null && mConnectStatus != CONNECT_STATE_SUSPENDED) {
                MLog.d(TAG, "connectMediaSession: mConnectStatus = " + mConnectStatus);
                if (mConnectStatus == CONNECT_STATE_DISCONNECTED || mConnectStatus == CONNECT_STATE_DISCONNECTING) {
                    mConnectStatus = CONNECT_STATE_CONNECTING;
                    mBroserConnectCallback.mRunnable = runnable;
                    mediaBrowserCompat.connect();
                }
            } else {
                if(serviceName != null && pkgName != null){
                    MLog.d(TAG, "try init MediaBrowserCompat : pkg name =" + pkgName + ",servicename = " + serviceName);
                    mBroserConnectCallback.mRunnable = runnable;
                    mediaBrowserCompat = new MediaBrowserCompat(ScenePatternApp.getInstance().getApplicationContext(), new ComponentName(pkgName, serviceName)
                            , mBroserConnectCallback, null);
                    mMediaBrowserCompat = mediaBrowserCompat;
                    mConnectStatus = CONNECT_STATE_CONNECTING;
                    mediaBrowserCompat.connect();
                }
            }
        }).sendToTarget();
    }

    private BroserConnectCallback mBroserConnectCallback = new BroserConnectCallback();

    class BroserConnectCallback extends MediaBrowserCompat.ConnectionCallback {
        private Runnable mRunnable;

        public void setRunnable(Runnable runnable) {
            mRunnable = runnable;
        }

        public BroserConnectCallback() {
        }

        public BroserConnectCallback(Runnable runnable) {
            mRunnable = runnable;
        }

        @Override
        public void onConnected() {
            if(mComponentName != null){
                MLog.d(TAG, "onConnected,PKG = " + mComponentName.getPackageName());
            }else {
                MLog.d(TAG, "onConnected,PKG  mComponentName is null" );
            }
            lock.lock();
            mConnectStatus = CONNECT_STATE_CONNECTED;

            try {
                super.onConnected();
                MediaBrowserCompat sBrowsercompat = mMediaBrowserCompat;
                if (sBrowsercompat != null && sBrowsercompat.isConnected()) {
                    try {
                        mMediaControllerCompat = new MediaControllerCompat(ScenePatternApp.getInstance().getApplicationContext(), sBrowsercompat.getSessionToken());
                        if(mRunnable != null){
                            mRunnable.run();
                        }
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
            } finally {
                lock.unlock();
            }
        }

        @Override
        public void onConnectionFailed() {
            if(mComponentName != null){
                MLog.d(TAG, "onConnectionFailed,PKG = " + mComponentName.getPackageName());
            }else {
                MLog.d(TAG, "onConnectionFailed,PKG  mComponentName is null" );
            }

            lock.lock();
            mConnectStatus = CONNECT_STATE_DISCONNECTED;

            try {
                mMediaControllerCompat = null;
                super.onConnectionFailed();
            } finally {
                lock.unlock();
            }

        }


        @Override
        public void onConnectionSuspended() {
            if(mComponentName != null){
                MLog.d(TAG, "onConnectionSuspended,PKG = " + mComponentName.getPackageName());
            }else {
                MLog.d(TAG, "onConnectionSuspended,PKG  mComponentName is null" );
            }

            lock.lock();
            mConnectStatus = CONNECT_STATE_SUSPENDED;

            try {
                mMediaControllerCompat = null;
                super.onConnectionSuspended();
            } finally {
                lock.unlock();
            }

        }

    }



}
