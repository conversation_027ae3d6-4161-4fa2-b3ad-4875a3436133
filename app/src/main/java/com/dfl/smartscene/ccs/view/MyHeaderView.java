package com.dfl.smartscene.ccs.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;

import androidx.annotation.NonNull;

import com.dfl.dflcommonlibs.uimodeutil.UITextView;
import com.dfl.smartscene.R;
import com.scwang.smart.refresh.layout.api.RefreshHeader;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.constant.RefreshState;
import com.scwang.smart.refresh.layout.simple.SimpleComponent;

/**
 * 自定义刷新头
 */
public class MyHeaderView extends SimpleComponent implements RefreshHeader {

    private final String REFRESH_HEADER_DOWN_TO_TRFRESH = "下拉刷新";
    private final String  REFRESH_HEADER_DOWN_CANCEL = "下拉取消";
    private final String  REFRESH_HEADER_RELEASE = "松开获取更多";
    private final String  REFRESH_HEADER_LOADING = "正在加载中...";
    private final String  REFRESH_HEADER_FINISH = "刷新成功";
    private final String  REFRESH_HEADER_FAILED = "刷新失败";

    private UITextView titleTv;
    private ImageView progressView;
    private ImageView imageView;
    private Animation rotateAnimation;
    
    public MyHeaderView(Context context) {
        this(context,null);
    }

    protected MyHeaderView(Context context, AttributeSet attrs) {
        this(context, attrs,0);
    }

    protected MyHeaderView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        View inflate = LayoutInflater.from(context).inflate(R.layout.libirary_refresh_head, this);
        titleTv = inflate.findViewById(R.id.header_tv);
        progressView = inflate.findViewById(R.id.header_progress);
        imageView = inflate.findViewById(R.id.header_icon);

        rotateAnimation = AnimationUtils.loadAnimation(context, R.anim.rotate_anim);
        LinearInterpolator lin = new LinearInterpolator();
        rotateAnimation.setInterpolator(lin);

    }

    @Override
    public int onFinish(@NonNull RefreshLayout refreshLayout, boolean success) {
        if (success){
            titleTv.setText(REFRESH_HEADER_FINISH);
        }else {
            //刷新失败
            imageView.setVisibility(View.VISIBLE);
            titleTv.setText(REFRESH_HEADER_FAILED);
            imageView.setImageResource(R.drawable.icon_refresh_failed);
        }
        progressView.clearAnimation();
        progressView.setVisibility(View.GONE);
        return 500;
    }

    @Override
    public void onStateChanged(@NonNull RefreshLayout refreshLayout, @NonNull RefreshState oldState, @NonNull RefreshState newState) {
        super.onStateChanged(refreshLayout, oldState, newState);
        switch (newState){
            case PullDownToRefresh:
                titleTv.setText(REFRESH_HEADER_DOWN_TO_TRFRESH);
                progressView.setVisibility(View.GONE);
                imageView.setVisibility(View.VISIBLE);
                imageView.setImageResource(R.drawable.icon_refresh_down);
                break;
            case PullDownCanceled:
                titleTv.setText(REFRESH_HEADER_DOWN_CANCEL);
                break;
            case ReleaseToRefresh:
                titleTv.setText(REFRESH_HEADER_RELEASE);
                imageView.setImageResource(R.drawable.icon_refresh_up);
                break;
            case Refreshing:
                imageView.setVisibility(View.GONE);
                titleTv.setText(REFRESH_HEADER_LOADING);
                progressView.setVisibility(View.VISIBLE);
                progressView.startAnimation(rotateAnimation);
                break;
            default:
                break;
        }
    }
}
