package com.dfl.smartscene.ccs.util;

import android.annotation.SuppressLint;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions;
import com.bumptech.glide.request.Request;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.SizeReadyCallback;
import com.bumptech.glide.request.target.Target;
import com.bumptech.glide.request.transition.Transition;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;

/**
 * <AUTHOR>
 * @date ：2022/10/12 11:39
 * @description ：
 */
public class ImageUtil {
    public static final String TAG = "ImageUtil";

    public static void loadIcon(String icon , ImageView imageView){
        loadIcon(icon, imageView,"icon_item_my_scene_default");
    }

    public static void loadIcon(String icon , ImageView imageView, String defaultIcon){
        if(icon == null || icon.equals("")){
            icon = defaultIcon;
        }
        if(icon == null || icon.equals("")){
            return;
        }
        if(icon.startsWith("http")){

        }else {
            int id = ScenePatternApp.getInstance().getResources().getIdentifier(icon,"mipmap",ScenePatternApp.getInstance().getPackageName());
            if(id == 0){
                return;
            }
            imageView.setImageResource(id);
        }
    }


    public static void loadActionBg(String icon , View imageView){
        loadActionBg(icon, imageView,"bg_scene_editor_action_default");
    }

    public static void loadActionBg(String icon , View imageView ,String defaultBg){
        if(icon == null || icon.equals("")){
            icon = defaultBg;
        }
        if(icon == null || icon.equals("")){
            return;
        }

        if(icon.startsWith("http")){

        }else {
            int id = ScenePatternApp.getInstance().getResources().getIdentifier(icon,"drawable",ScenePatternApp.getInstance().getPackageName());
            if(id == 0){
                return;
            }
            imageView.setBackgroundResource(id);
        }

    }

    @SuppressLint("CheckResult")
    public static void loadImageUrlNoCorner(ImageView imageView, String url, int defaultId){
        if(url == null || "".equals(url)){

            Glide.with(imageView)
                    .load(defaultId)
                    .into(imageView);
            return;
        }
        if(!url.startsWith("http") && !url.startsWith("content")){
            int id = ScenePatternApp.getInstance().getResources().getIdentifier(url,"drawable",ScenePatternApp.getInstance().getPackageName());
            if(id == 0){
                return;
            }
            imageView.setImageResource(id);
            return;
        }
        RequestOptions options = RequestOptions
                .noTransformation()
                .diskCacheStrategy(DiskCacheStrategy.ALL).override((int) imageView.getResources().getDimension(R.dimen.x_px_160), (int) imageView.getResources().getDimension(R.dimen.y_px_160))
                .error(defaultId);

        if (url.endsWith("gif")) {
            options.format(DecodeFormat.PREFER_ARGB_8888);
        } else {
            options.format(DecodeFormat.PREFER_RGB_565);
        }
        Glide.with(imageView)
                .load(url)
                .apply(options)
                .transition(DrawableTransitionOptions.withCrossFade())
                .into(imageView);

    }


    @SuppressLint("CheckResult")
    public static void loadImageUrlNoCorner(ImageView imageView, String url){
        loadImageUrlNoCorner(imageView, url, R.drawable.drawable_card_default_bg);
    }

    /**
     * 使用glide请求url数据
     * @param imageView
     * @param url
     */
    public static void loadImageUrl(ImageView imageView, String url){
        if(url == null || "".equals(url)){
            Glide.with(imageView)
                    .load(R.drawable.drawable_card_default_bg)
                    .into(imageView);
            return;
        }
        RequestOptions options = RequestOptions
                .bitmapTransform(new RoundedCorners(20))
                .diskCacheStrategy(DiskCacheStrategy.ALL).override((int) imageView.getResources().getDimension(R.dimen.x_px_160), (int) imageView.getResources().getDimension(R.dimen.y_px_160));

        if (url.endsWith("gif")) {
            options.format(DecodeFormat.PREFER_ARGB_8888);
        } else {
            options.format(DecodeFormat.PREFER_RGB_565);
        }
        Glide.with(imageView)
                .load(url)
                .apply(options)
                .transition(DrawableTransitionOptions.withCrossFade())
                .into(imageView);

    }

    public static void loadBackGroundUrl(View view , String url){
        if(url == null || "".equals(url)){
            return;
        }
        RequestOptions options = RequestOptions
                .bitmapTransform(new RoundedCorners(10))
                .diskCacheStrategy(DiskCacheStrategy.ALL).override((int) view.getResources().getDimension(R.dimen.x_px_160), (int) view.getResources().getDimension(R.dimen.y_px_160))
                .placeholder(R.drawable.drawable_card_default_bg);

        if (url.endsWith("gif")) {
            options.format(DecodeFormat.PREFER_ARGB_8888);
        } else {
            options.format(DecodeFormat.PREFER_RGB_565);
        }
        Glide.with(view)
                .load(url)
                .apply(options)
                .transition(DrawableTransitionOptions.withCrossFade())
                .into(new Target<Drawable>() {
                    @Override
                    public void onLoadStarted(@Nullable Drawable placeholder) {

                    }

                    @Override
                    public void onLoadFailed(@Nullable Drawable errorDrawable) {

                    }

                    @Override
                    public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                        view.setBackground(resource);
                    }

                    @Override
                    public void onLoadCleared(@Nullable Drawable placeholder) {

                    }

                    @Override
                    public void getSize(@NonNull SizeReadyCallback cb) {

                    }

                    @Override
                    public void removeCallback(@NonNull SizeReadyCallback cb) {

                    }

                    @Override
                    public void setRequest(@Nullable Request request) {

                    }

                    @Nullable
                    @Override
                    public Request getRequest() {
                        return null;
                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onStop() {

                    }

                    @Override
                    public void onDestroy() {

                    }
                });
    }

    public static void parseBitmapColor(Bitmap bitmap , int x , int y){
        Color color = bitmap.getColor(x, y);
        LogUtil.d(TAG,"x : " + x + ", y : " + y + " , " + Integer.toHexString((int) (color.alpha() * 255)) + Integer.toHexString((int) (color.red() * 255)) + Integer.toHexString((int) (color.green() * 255)) + Integer.toHexString((int) (color.blue() * 255)));
    }

}
