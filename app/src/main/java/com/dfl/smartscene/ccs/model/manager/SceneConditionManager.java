package com.dfl.smartscene.ccs.model.manager;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.core.ConditionBaseModel;
import com.dfl.smartscene.ccs.model.StateBaseModel;
import com.dfl.smartscene.ccs.model.VRModel;
import com.dfl.smartscene.ccs.model.factory.SceneModelFactory;
import com.dfl.smartscene.ccs.util.CarConfigUtil;
import com.dfl.smartscene.ccs.util.DialogManager;
import com.dfl.smartscene.ccs.view.ViewControlManager;
import com.iauto.uibase.utils.MLog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 触发条件的管理类
 *
 * <AUTHOR>
 * @date ：2022/7/13 17:18
 * @description ：
 */
public class SceneConditionManager {
    private static final String TAG = "SceneConditionManager";

    private static volatile SceneConditionManager sInstance;

    public static SceneConditionManager getInstance() {
        if (null == sInstance) {
            synchronized (SceneConditionManager.class) {
                if (null == sInstance) {
                    sInstance = new SceneConditionManager();
                }
            }
        }
        return sInstance;
    }

    /**
     * key : 操作id
     * key2 : 场景id
     */
    private Map<String,Map<String, SettingOperation>> registerOperation = new HashMap<>();

    /**
     * key : 场景id
     */
    private Map<String, SceneBean> registerScene = new HashMap<>();

    /**
     * 获取某一个场景的触发条件，非语音
     * @param sceneBean
     * @return
     */
    protected SettingOperation getConditionOperation(SceneBean sceneBean) {
        for (SettingOperation settingOperation : sceneBean.getConditionOperations()) {
            if (!settingOperation.getOperationId().equals(ConstantModelValue.OPERATION_ID_CONDITION_VR)) {
                return settingOperation;
            }
        }
        return null;
    }

    /**
     * 获取某一个场景的语音触发列表
     * @param sceneBean
     * @return
     */
    protected List<SettingOperation> getVrConditionOperation(SceneBean sceneBean) {
        List<SettingOperation> vrOpes = new ArrayList<>();
        for (SettingOperation settingOperation : sceneBean.getConditionOperations()) {
            if (settingOperation.getOperationId().equals(ConstantModelValue.OPERATION_ID_CONDITION_VR)) {
                vrOpes.add(settingOperation);
            }
        }
        return vrOpes;
    }

    /**
     * 应用开启后遍历已开启的场景，进行场景触发条件的注册
     * 只针对除了vr之外的触发条件
     * @param sceneBean
     */
    public void registerSceneNormalCondition(SceneBean sceneBean) {
        SettingOperation condition = getConditionOperation(sceneBean);
        if (condition == null) {
            return;
        }
        String operationId = condition.getOperationId();
        registerScene.put(sceneBean.getSceneId(),sceneBean);
        registerOperation.computeIfAbsent(operationId,k -> new HashMap<>()).put(sceneBean.getSceneId(),condition);

        ConditionBaseModel conditionModel = SceneModelFactory.productConditionBaseModel(condition.getDeviceId(), condition.getOperationId());
        if (conditionModel != null) {
            conditionModel.registerSceneCondition(sceneBean, condition);
        }
        LibraryManager.getInstance().getLibraryModel().registerSceneCondition(sceneBean,condition);

    }

    /**
     * 1.请求用户场景后
     * 2.收藏/新增场景
     * 3.替换场景
     * 4.vr的customapi注册成功，但是1在未注册成功的时候调用了
     * 5.获取轻松一刻场景后
     * 6.vr的customapi注册成功，但是5在未注册成功的时候调用了
     * @param sceneBean
     */
    public void registerVrCondition(SceneBean sceneBean){
        for(SettingOperation so : getVrConditionOperation(sceneBean)){
            VRModel.getInstance().registerSceneCondition(sceneBean,so);
        }
    }

    public void unregisterVrCondition(SceneBean sceneBean){
        for(SettingOperation so : getVrConditionOperation(sceneBean)){
            VRModel.getInstance().unRegisterSceneCondition(sceneBean,so);
        }
    }

    /**
     * 取消注册，如关闭某一个场景
     *
     * @param sceneBean
     */
    public void unregisterSceneCondition(SceneBean sceneBean) {
        SettingOperation condition = getConditionOperation(sceneBean);
        if (condition == null) {
            return;
        }
        String operationId = condition.getOperationId();
        registerScene.remove(sceneBean.getSceneId());
        registerOperation.computeIfAbsent(operationId,k -> new HashMap<>()).remove(sceneBean.getSceneId());

        ConditionBaseModel conditionModel = SceneModelFactory.productConditionBaseModel(condition.getDeviceId(), condition.getOperationId());
        //反注册普通触发条件
        if (conditionModel != null) {
            conditionModel.unRegisterSceneCondition(sceneBean, condition);
        }
        LibraryManager.getInstance().getLibraryModel().unRegisterSceneCondition(sceneBean,condition);

    }

    /**
     * 触发条件触发
     * 若状态条件满足
     * 执行动作
     */
    public void onSceneConditionMeet(SceneBean sceneBean , boolean checkState) {
        if(checkState){
            //如果需要检查状态条件
            for (SettingOperation stateOperation : sceneBean.getStateOperations()) {
                StateBaseModel stateBaseModel = SceneModelFactory.productStateBaseModel(stateOperation.getDeviceId(), stateOperation.getOperationId());
                if (stateBaseModel != null) {
                    stateOperation.setSceneId(sceneBean.getSceneId());
                    //如果有一个条件不满足，返回
                    if (!stateBaseModel.checkStateOperationMeet(stateOperation)) {
                        return;
                    }
                } else {
                    MLog.e(TAG, "onconditionmeet condition model == null , id = " + stateOperation.getDeviceId());
                    return;
                }
            }
        }
        LogUtil.d(TAG,"onscenemeet : " + sceneBean.getSceneName());
        if(!sceneBean.isAutoNotify()){
            //自动运行，不需要询问的情况
            ViewControlManager.executeScene(sceneBean, "即将执行 ");
        }else{
            if(CarConfigUtil.isCCS()){
                DialogManager.showSceneConditionNotifyDialog(sceneBean);
            }else if(CarConfigUtil.isCCU()){
                LibraryManager.getInstance().getLibraryVrModel().onSceneMeet(sceneBean.getSceneId(),sceneBean.getSceneName());
            }else if(CarConfigUtil.isPhone()){
                DialogManager.showSceneConditionNotifyDialog(sceneBean);
            }
        }
    }

    public void onSceneConditionMeet(SceneBean sceneBean){
        onSceneConditionMeet(sceneBean,true);
    }

    public SceneBean getSceneById(String sceneId){
        return registerScene.get(sceneId);
    }

    public void onSceneConditionMeet(String sceneId , boolean checkState){
        SceneBean sceneBean = getSceneById(sceneId);
        if(sceneBean == null){
            return;
        }
        onSceneConditionMeet(sceneBean,checkState);
    }

    public void onSceneConditionMeet(String sceneId){
        onSceneConditionMeet(sceneId,true);
    }

    public Map<String, SettingOperation> getOperationsById(String id){
        return registerOperation.get(id);
    }

    /**
     * 一些比较简单的触发条件判断
     * 根据操作id获取对应的操作列表，取操作列表的第一个值进行比较，如果相同则触发对应的场景
     * @param operationId 操作id
     * @param value 监听数值
     * @return 返回结果无有效含义，不需要使用
     */
    public boolean simpleConditionCheck(String operationId,String value){
        Map<String, SettingOperation> operationsMap = getOperationsById(operationId);
        if(operationsMap == null){
            return false;
        }
        for (Map.Entry<String, SettingOperation> next : operationsMap.entrySet()) {
            if (value.equals(next.getValue().getListArgs().get(0))) {
                onSceneConditionMeet(next.getKey());
            }
        }
        return false;

    }

}
