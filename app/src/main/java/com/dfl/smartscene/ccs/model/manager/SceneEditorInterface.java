package com.dfl.smartscene.ccs.model.manager;


import com.dfl.smartscene.ccs.bean.SettingOperation;

/**
 * <AUTHOR>
 * @date ：2022/7/12 16:34
 * @description ：
 */
public interface SceneEditorInterface {
    void addSingerOperation(String deviceId , SettingOperation settingOperation);

    void deleteSingleOperation(String deviceId , SettingOperation settingOperation);

    void modifySingleOperation(String deviceId , SettingOperation settingOperation);

    void setDeviceOperationEnable(String deviceId , boolean enable);

    void setSceneEnable(boolean enable);
}
