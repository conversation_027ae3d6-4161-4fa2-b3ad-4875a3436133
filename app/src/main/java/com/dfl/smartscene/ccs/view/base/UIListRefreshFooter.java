package com.dfl.smartscene.ccs.view.base;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dfl.dflcommonlibs.uimodeutil.UILottileView;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.constant.ConstantViewValue;
import com.scwang.smartrefresh.layout.api.RefreshFooter;
import com.scwang.smartrefresh.layout.api.RefreshKernel;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.constant.RefreshState;
import com.scwang.smartrefresh.layout.constant.SpinnerStyle;

/**
 * <AUTHOR>
 * @description 下拉刷新的头部视图，配合SmartRefreshLayout使用<P/>
 * @date 2023/11/17
 * @memo
 */
public class UIListRefreshFooter extends LinearLayout implements RefreshFooter {
    /**提示文言*/
    private TextView mMemoTextView;
    private UILottileView mLottileView;
    public UIListRefreshFooter(Context context) {
        super(context);
        initView(context);
    }

    public UIListRefreshFooter(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView(context);
    }

    public UIListRefreshFooter(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }

    private void initView(Context context){
        View view = View.inflate(context, R.layout.layout_refresh_header, this);
        mMemoTextView = view.findViewById(R.id.memo_text_view);
        mLottileView = view.findViewById(R.id.lottie_view);
    }

    @SuppressLint("RestrictedApi")
    @Override
    public void onStateChanged(@NonNull RefreshLayout refreshLayout, @NonNull RefreshState oldState, @NonNull RefreshState newState) {
        switch (newState){
            case None:
            case Loading:
                mLottileView.playAnimation();
                mMemoTextView.setText(getResources().getString(R.string.string_refresh_at_refreshing));
                break;
            case PullUpToLoad:
                mLottileView.pauseAnimation();
                mMemoTextView.setText(getResources().getString(R.string.string_load_success));
                break;
            default:
                break;
        }
    }

    @NonNull
    @Override
    public View getView() {
        return this;
    }

    @NonNull
    @Override
    public SpinnerStyle getSpinnerStyle() {
        return SpinnerStyle.Translate;
    }

    @SuppressLint("RestrictedApi")
    @Override
    public void setPrimaryColors(int... colors) {

    }

    @SuppressLint("RestrictedApi")
    @Override
    public void onInitialized(@NonNull RefreshKernel kernel, int height, int maxDragHeight) {

    }

    @SuppressLint("RestrictedApi")
    @Override
    public void onMoving(boolean isDragging, float percent, int offset, int height, int maxDragHeight) {

    }

    @SuppressLint("RestrictedApi")
    @Override
    public void onReleased(@NonNull RefreshLayout refreshLayout, int height, int maxDragHeight) {

    }

    @SuppressLint("RestrictedApi")
    @Override
    public void onStartAnimator(@NonNull RefreshLayout refreshLayout, int height, int maxDragHeight) {

    }

    @SuppressLint("RestrictedApi")
    @Override
    public int onFinish(@NonNull RefreshLayout refreshLayout, boolean success) {
        mLottileView.pauseAnimation();
        refreshLayout.setReboundDuration(ConstantViewValue.REFRESH_REBOUND_TIMER);
        if(success){
            mMemoTextView.setText(getResources().getString(R.string.string_load_success));
        }else{
            mMemoTextView.setText(getResources().getString(R.string.string_load_failed));
        }
        return 1;
    }

    @SuppressLint("RestrictedApi")
    @Override
    public void onHorizontalDrag(float percentX, int offsetX, int offsetMax) {

    }

    @Override
    public boolean isSupportHorizontalDrag() {
        return false;
    }

    @SuppressLint("RestrictedApi")
    @Override
    public boolean setNoMoreData(boolean noMoreData) {
        return false;
    }
}
