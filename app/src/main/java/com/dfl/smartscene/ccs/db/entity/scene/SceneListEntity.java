package com.dfl.smartscene.ccs.db.entity.scene;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;

@Entity(tableName = "scene_list")
public class SceneListEntity {

    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "pId", typeAffinity = ColumnInfo.INTEGER)
    public int pId;

    @ColumnInfo(name = "mSceneId", typeAffinity = ColumnInfo.TEXT)
    public String mSceneId;

    @ColumnInfo(name = "mSceneCategoryId", typeAffinity = ColumnInfo.TEXT)
    public String mSceneCategoryId;

    @ColumnInfo(name = "mSceneIcon", typeAffinity = ColumnInfo.TEXT)
    public String mSceneIcon;

    @ColumnInfo(name = "mScenePic", typeAffinity = ColumnInfo.TEXT)
    public String mScenePic;

    @ColumnInfo(name = "mSceneName", typeAffinity = ColumnInfo.TEXT)
    public String mSceneName;

    @ColumnInfo(name = "mSceneDesc", typeAffinity = ColumnInfo.TEXT)
    public String mSceneDesc;

    @ColumnInfo(name = "mIsNew", typeAffinity = ColumnInfo.TEXT)
    public String mIsNew;

    @ColumnInfo(name = "mTimestamp", typeAffinity = ColumnInfo.TEXT)
    public String mTimestamp;

    @ColumnInfo(name = "mEditType", typeAffinity = ColumnInfo.TEXT)
    public String mEditType;


    @Ignore
    public SceneListEntity() {

    }

    public SceneListEntity(int pId, String mSceneId, String mSceneCategoryId, String mSceneIcon, String mScenePic, String mSceneName, String mSceneDesc, String mIsNew, String mTimestamp, String mEditType) {
        this.pId = pId;
        this.mSceneId = mSceneId;
        this.mSceneCategoryId = mSceneCategoryId;
        this.mSceneIcon = mSceneIcon;
        this.mScenePic = mScenePic;
        this.mSceneName = mSceneName;
        this.mSceneDesc = mSceneDesc;
        this.mIsNew = mIsNew;
        this.mTimestamp = mTimestamp;
        this.mEditType = mEditType;
    }

    @Override
    public String toString() {
        return "SceneListEntity{" +
                "pId=" + pId +
                ", mSceneId='" + mSceneId + '\'' +
                ", mSceneCategoryId='" + mSceneCategoryId + '\'' +
                ", mScenePic='" + mScenePic + '\'' +
                ", mSceneName='" + mSceneName + '\'' +
                ", mSceneDesc='" + mSceneDesc + '\'' +
                ", mIsNew='" + mIsNew + '\'' +
                ", mTimestamp='" + mTimestamp + '\'' +
                ", mEditType='" + mEditType + '\'' +
                '}';
    }
}
