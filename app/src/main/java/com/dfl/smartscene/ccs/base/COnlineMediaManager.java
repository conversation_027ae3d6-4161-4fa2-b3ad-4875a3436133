package com.dfl.smartscene.ccs.base;

import android.annotation.SuppressLint;
import android.content.Context;
import android.media.AudioManager;
import android.media.session.PlaybackState;
import android.os.Bundle;
import android.support.v4.media.session.MediaControllerCompat.TransportControls;

import com.dfl.api.da.setting.StreamType;
import com.dfl.smartscene.ccs.constant.RelieveStressFuncDef;
import com.dfl.smartscene.ccs.model.ScenePatternInterrupt;
import com.iauto.uibase.utils.MLog;

/**
 * COnlineMediaManager
 * 用于连接MediaBrowserService
 * 用于MediaBrowserService和MediaController交互
 * <AUTHOR>
 * @date 2022/5/15
 */
public class COnlineMediaManager {
    private static final String   TAG = "COnlineMediaManager";
    @SuppressLint("StaticFieldLeak")
    private static COnlineMediaManager sOnlineMediaManager = null;
    protected String                m_ServiceName = RelieveStressFuncDef.MEDIA_SOURCE_MUSIC_SERVICE;
    protected String                m_PackageName = RelieveStressFuncDef.MEDIA_SOURCE_MUSIC_PACKAGE_NAME;

    private String mOriginPkgName   = "";
    private int    mOriginPlayState = PlaybackState.STATE_NONE;

    private Context mContext = null;

    /**
     * get COnlineMediaManager's instance
     */
    public static COnlineMediaManager getInstance(){
        return sOnlineMediaManager;
    }

    /**
     * COnlineMediaManager
     * 构造函数
     * service_name : MediaBrowserService类名
     */
    public COnlineMediaManager(Context context) {
        MLog.d(TAG, "Create COnlineMediaManager : " + m_ServiceName);
        mContext = context;
    }

    /**
     * create COnlineMediaManager's instance
     */
    public static synchronized void create(Context context) {
        if(sOnlineMediaManager == null){
            sOnlineMediaManager = new COnlineMediaManager(context);
        }
    }

    /**
     * sendCustomAction
     * 发送客户端动作
     */
    private void sendCustomAction(String action, Bundle extras) {
        TransportControls transportControls = MediaGroupManager.getInstance().getSessionTransportControls(m_PackageName);
        if (transportControls != null) {
            MLog.d(TAG, "Send Custom Action:" + action);
            transportControls.sendCustomAction(action, extras);
        }
        else {
            MLog.w(TAG, "Transport Controls is null");
        }
    }

    /**
     * searchMusic
     * 开始在线音乐播放
     */
    public void doPlayStart() {
        MLog.d(TAG, "doPlayStart");
        sendCustomAction(RelieveStressFuncDef.MEDIASESSIONEXTRA_IN_ADDITIONAL_PLAY_ACTION, null);
        TransportControls transportControls = MediaGroupManager.getInstance().getSessionTransportControls(m_PackageName);

        String activeSource = ScenePatternInterrupt.getInstance().getActiveSource();
        if (!activeSource.isEmpty()) {
            mOriginPkgName = activeSource;
            mOriginPlayState = MediaGroupManager.getInstance().getSessionPlayState(mOriginPkgName);
            MLog.d(TAG, "doPlayStart:mOriginPkgName:"+mOriginPkgName);
            MLog.d(TAG, "doPlayStart:mOriginPlayState:"+mOriginPlayState);
        }

        silentSwitch(true);

        if (transportControls != null) {
            Bundle extras = new Bundle();
            extras.putString(RelieveStressFuncDef.MEDIASESSIONEXTRA_PARENT_ID, RelieveStressFuncDef.MEDIASESSIONEXTRA_EXTRA_SCENE_LIST_MEDIA_ID);
            transportControls.playFromMediaId(RelieveStressFuncDef.SCENE_ID, extras);
        }
        else {
            MLog.w(TAG, "Transport controls is null");
        }
    }

    /**
     * searchMusic
     * 开始在线音乐播放
     */
    public void doPlayFromPause() {
        MLog.d(TAG, "doPlayFromPause");
        TransportControls transportControls = MediaGroupManager.getInstance().getSessionTransportControls(m_PackageName);
        if (transportControls != null) {
            transportControls.play();
        }
        else {
            MLog.w(TAG, "Transport controls is null");
        }
    }

    /**
     * doPause
     * pause music
     */
    public void doPause()
    {
        MLog.d(TAG, "doPause");
        TransportControls transportControls = MediaGroupManager.getInstance().getSessionTransportControls(m_PackageName);
        if (transportControls != null) {
            transportControls.pause();
        }
        else {
            MLog.w(TAG, "Transport controls is null");
        }
    }

    /**
     * doPause
     * pause music
     */
    public void doPlayNext()
    {
        MLog.d(TAG, "doPlayNext");
        TransportControls transportControls = MediaGroupManager.getInstance().getSessionTransportControls(m_PackageName);
        if (transportControls != null) {
            transportControls.skipToNext();
        }
        else {
            MLog.w(TAG, "Transport controls is null");
        }
    }

    /**
     * doStop
     * stop music
     */
    public void doStop(boolean reset)
    {
        MLog.d(TAG, "doStop");
        TransportControls transportControls = MediaGroupManager.getInstance().getSessionTransportControls(m_PackageName);

        if (transportControls != null) {
            transportControls.stop();
        }
        else {
            MLog.w(TAG, "Transport controls is null");
        }
        sendCustomAction(RelieveStressFuncDef.MEDIASESSIONEXTRA_OUT_ADDITIONAL_PLAY_ACTION, null);
        MLog.d(TAG, "doPlayStart:mOriginPkgName:"+mOriginPkgName);
        MLog.d(TAG, "doPlayStart:mOriginPlayState:"+mOriginPlayState);
        MLog.d(TAG, "doPlayStart:reset:"+reset);
        if (reset && (mOriginPlayState == PlaybackState.STATE_PLAYING)) {
            if(!mOriginPkgName.equals(m_PackageName)) {
                TransportControls originTransportControls = MediaGroupManager.getInstance().getSessionTransportControls(mOriginPkgName);
                originTransportControls.play();
            } else {
                transportControls.play();
            }
        }
        silentSwitch(false);
    }

    private void silentSwitch(boolean isMute) {
        MLog.d(TAG, "silentSwitch: " + isMute);
        try {
            boolean muteFlag = false;
            AudioManager audioManager = (AudioManager) mContext.getSystemService(Context.AUDIO_SERVICE);
            //获取当前音乐多媒体是否静音
            muteFlag = audioManager.isStreamMute(StreamType.STREAM_TTS);
            MLog.d(TAG, "muteFlag: " + muteFlag);
            if (isMute) {
                if (!muteFlag) {
                    MLog.d(TAG, "mute set:");
                    audioManager.adjustStreamVolume(StreamType.STREAM_TTS,
                            AudioManager.ADJUST_MUTE, 0);//设为静音
                }
            } else {
                if (muteFlag) {
                    MLog.d(TAG, "unmute set:");
                    audioManager.adjustStreamVolume(StreamType.STREAM_TTS,
                            AudioManager.ADJUST_UNMUTE, 0);//取消静音

                }
            }

        } catch (Exception e) {
            MLog.d(TAG, "silentSwitch: " + e);
        }
    }

}
