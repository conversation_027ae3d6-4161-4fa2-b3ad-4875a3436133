package com.dfl.smartscene.ccs.core;


import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SettingOperation;

/**
 * 某一设备的触发类
 * 某一触发条件状态回调时，获取对应操作id相关联的场景列表，判断列表内的场景是否符合条件
 * <AUTHOR>
 * @date ：2022/7/13 17:19
 * @description ：
 */
public interface ConditionBaseModel {

    /**
     * 注册对应的场景触发条件
     * 根据操作id分类存储
     */
    void registerSceneCondition(SceneBean sceneBean, SettingOperation condition);

    /**
     * 取消注册对应的场景触发条件
     *
     */
    void unRegisterSceneCondition(SceneBean sceneBean, SettingOperation condition);



}
