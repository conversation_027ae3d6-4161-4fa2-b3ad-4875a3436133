package com.dfl.smartscene.ccs.view.weight;

import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Configuration;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.graphics.drawable.NinePatchDrawable;
import android.util.AttributeSet;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.widget.CompoundButton;
import android.widget.RadioButton;
import android.widget.RadioGroup;

import androidx.core.content.ContextCompat;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.library.app.ContextUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 自定义联动滑动动效的RadioGroup
 *
 * <AUTHOR>
 * @date 2022/06/09
 */
public class CustomRadioGroup extends RadioGroup implements CompoundButton.OnCheckedChangeListener {
    private static final String TAG = "CustomRadioGroup";
    private static final int ANIM_TIMER = 300;/*动画持续时间*/
    private int mIndex = 0;
    private int width;/*单个radioButton的宽度*/
    private int height;/*单个radioButton的高度*/
    private final Context mContext;
    private final List<String> mList = new ArrayList<>();/*用户输入的radioButton的文本，也要靠它决定radioButton的数目*/
    private float mCanvasX = 0;/*滑动动画的控制参数*/
    private float textSizeOfRadioButton;/*RadioButton的textSize*/
    private int textColorChecked;/*RadioButton的选中态的text颜色*/
    private int textColorUnChecked;/*RadioButton的选中态的text颜色*/
    private int textColorCheckedEnable;
    private int textColorUnCheckedEnable;

    private CheckedCallBack mCheckedCallBack;/*radioButton的点击回调*/
    private int mOrientation;

    private Drawable mSlideDrawable;
    private Drawable mMaskDrawable;

    // true : 文字模式， false: 图片模式
    private String mViewType = ConstantModelValue.VIEW_TYPE_RADIO_TEXT_GROUP;

    private int mPaddingTop = 0;
    private int mPaddingBottom = 0;
    private int mPaddingStart = 0;
    private int mPaddingEnd = 0;

    private int mRadioBtnWidth = 110;
    private int mRadioBtnHeight = 56;

    public CustomRadioGroup(Context context) {
        this(context, null);
    }

    public CustomRadioGroup(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        init(context, attrs);
    }


    private void init(Context context, AttributeSet attrs) {
        setOrientation(HORIZONTAL);
        setGravity(Gravity.CENTER_VERTICAL);
        mOrientation = getOrientation();

        //       自定义属性参数
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.CustomRadioGroup);

        /*自定义RadioButton的textSize*/
        textSizeOfRadioButton = typedArray.getDimension(R.styleable.CustomRadioGroup_radio_button_text_size, 28);

        /*自定义RadioButton的选中态的text颜色*/
        textColorChecked = typedArray.getColor(R.styleable.CustomRadioGroup_radio_button_text_color_checked, getResources().getColor(R.color.color_text_selected, null));

        /*自定义RadioButton的未选中态的text颜色*/
        textColorUnChecked = typedArray.getColor(R.styleable.CustomRadioGroup_radio_button_text_color_unchecked, getResources().getColor(R.color.color_text_unselected, null));

        textColorCheckedEnable = getResources().getColor(R.color.color_text_selected_enable, null);
        textColorUnCheckedEnable = getResources().getColor(R.color.color_text_unselected_enable, null);


        /*RadioGroup的背景图片*/
        int backgroundId = typedArray.getResourceId(R.styleable.CustomRadioGroup_android_background, R.drawable.ic_radiogroup_bg);
        Bitmap maskBitmap = BitmapFactory.decodeResource(getResources(), backgroundId);
        mMaskDrawable = new NinePatchDrawable(context.getResources(), maskBitmap, maskBitmap.getNinePatchChunk(), new Rect(), null);
        setBackground(mMaskDrawable);

        /*自定义滑动动画的图片*/
        int drawId = typedArray.getResourceId(R.styleable.CustomRadioGroup_drawable_radio_button_checked, R.drawable.ic_radio_check_bg);
        Bitmap bitmap = BitmapFactory.decodeResource(getResources(), drawId);
        /*设置滑动动画的图片*/
        mSlideDrawable = new NinePatchDrawable(context.getResources(), bitmap, bitmap.getNinePatchChunk(), new Rect(), null);

        typedArray.recycle();

        setPadding(mPaddingStart, mPaddingTop, mPaddingEnd, mPaddingBottom);
    }

    @SuppressLint("DrawAllocation")
    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
//        获取单个RadioButton的宽高
        if (width == 0 || height == 0) {
            View childAt = getChildAt(0);
            if (childAt != null) {
                width = childAt.getMeasuredWidth();
                height = childAt.getMeasuredHeight();

                if (null != mSlideDrawable) {
                    mSlideDrawable.setBounds(0, 0, width, height);
                }
            }
        }
    }


    /**
     * 设置RadioButton文本内容(外抛使用）
     */
    public void setTextList(List<String> list, String viewType) {
        mViewType = viewType;

        mList.clear();
        removeAllViews();/*删除默认添加的RadioButton*/

        if (list != null && !list.isEmpty()) {
            mList.addAll(list);
        }
        setWeightSum(mList.size());
        for (int i = 0; i < mList.size(); i++) {
            createRadioButton(i, mList.get(i));
        }
    }

    /**
     * 设置RadioButton参数
     *
     * @param id RadioButton的id
     */
    private void createRadioButton(int id, String titleOrImage) {
        RadioButton mRadioButton = new RadioButton(mContext);
        mRadioButton.setId(id);

        // 横向
        LayoutParams params;
        if (mOrientation == HORIZONTAL) {
            params = new LayoutParams(mRadioBtnWidth, mRadioBtnHeight, 1);
        } else {
            params = new LayoutParams(LayoutParams.WRAP_CONTENT, 0, 1);
        }
        mRadioButton.setLayoutParams(params);
        mRadioButton.setGravity(Gravity.CENTER);

        if (ConstantModelValue.VIEW_TYPE_RADIO_TEXT_GROUP.equals(mViewType)) {
            mRadioButton.setText(titleOrImage);
            mRadioButton.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSizeOfRadioButton);
            mRadioButton.setButtonDrawable(null);
            mRadioButton.setTextColor(mRadioButton.isChecked() ? textColorChecked : textColorUnChecked);
        } else if (ConstantModelValue.VIEW_TYPE_RADIO_PIC_GROUP.equals(mViewType)) {
            mRadioButton.setTextSize(0);
            Drawable drawable = ContextUtil.getDrawableByName(titleOrImage);
            if (null != drawable) {
                drawable.setBounds(0, 0, drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight());
                mRadioButton.setGravity(Gravity.CENTER);
                int paddingTop = 8;
                paddingTop = (mRadioBtnHeight - drawable.getIntrinsicHeight()) / 2;

                mRadioButton.setPadding(0,paddingTop,0,0);
                mRadioButton.setButtonDrawable(null);
                mRadioButton.setCompoundDrawables(null, drawable, null, null);/*隐藏RadioButton的选中状态图标*/
            }
        } else {
            int color = Color.parseColor(titleOrImage);
            Drawable drawable = ContextCompat.getDrawable(getContext(), R.drawable.bg_color_shape);

            int paddingTop = 13;
            if(drawable instanceof GradientDrawable) {
                ((GradientDrawable) drawable).setColor(color);
                drawable.setBounds(0,0,drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight());
                paddingTop = (mRadioBtnHeight - drawable.getIntrinsicHeight()) / 2;
            }
            mRadioButton.setPadding(0, paddingTop,0,0);
            mRadioButton.setTextSize(0);
            mRadioButton.setButtonDrawable(null);
            mRadioButton.setCompoundDrawables(null, drawable, null, null);
        }

        mRadioButton.setWidth(mRadioBtnWidth);
        mRadioButton.setHeight(mRadioBtnHeight);

        mRadioButton.setOnCheckedChangeListener(this);

        addView(mRadioButton);
    }


    /**
     * 设置点击时滑动动画
     *
     * @param id RadioButton的id
     */
    private void setCheckedAnimation(int id) {
        int destIndex = indexOfChild(findViewById(id));
        setAnimation(mIndex, destIndex);/*通过点击前后button的Id参数设置滑动动画*/
        mIndex = destIndex;
    }

    public int getCurPosition() {
        return mIndex;
    }

    /**
     * 重写RadioButton选中状态变更的回调方法
     *
     * @param buttonView 变更的button
     * @param isChecked  选中状态
     */
    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        if (isChecked) {
            setCheckedAnimation(buttonView.getId());
            buttonView.setTextColor(textColorChecked);/*设置选中态的文本颜色*/
            LogUtil.d(TAG, "onCheckedChanged: Checked" + buttonView.getId());

            if(ConstantModelValue.VIEW_TYPE_RADIO_PIC_GROUP.equals(mViewType)){
                Drawable drawable = buttonView.getCompoundDrawables()[1];
                if(drawable != null){
                    drawable.setColorFilter(getContext().getColor(R.color.color_text_selected), PorterDuff.Mode.SRC_ATOP);
                    buttonView.setCompoundDrawables(null,drawable,null,null);
                }
            }

//            选中后的回调
            if (mCheckedCallBack != null) {
                mCheckedCallBack.radioButtonChecked(buttonView.getId(), mList.get(buttonView.getId()));
                Log.d(TAG, "mCheckedCallBack.RadioButtonChecked: " + buttonView.getId() + ":" + mList.get(buttonView.getId()));
            }
            for (int i = 0; i < getChildCount(); i++) {
                if (getChildAt(i).getId() !=  buttonView.getId()){
                    Log.d(TAG, "onCheckedChanged: Not Checked" + buttonView.getId());

                    ((RadioButton) getChildAt(i)).setTextColor(textColorUnChecked);/*设置未选中态的文本颜色*/
                    if(ConstantModelValue.VIEW_TYPE_RADIO_PIC_GROUP.equals(mViewType)) {
                        Drawable drawable = ((RadioButton) getChildAt(i)).getCompoundDrawables()[1];
                        if(drawable != null){
                            drawable.setColorFilter(getContext().getColor(R.color.color_text_secondary_label), PorterDuff.Mode.SRC_ATOP);
                            ((RadioButton) getChildAt(i)).setCompoundDrawables(null,drawable,null,null);
                        }
                    }
                }
            }
        }else {
            Log.d(TAG, "onCheckedChanged: Not Checked" + buttonView.getId());
            buttonView.setTextColor(textColorUnChecked);/*设置未选中态的文本颜色*/

            if(ConstantModelValue.VIEW_TYPE_RADIO_PIC_GROUP.equals(mViewType)) {
                Drawable drawable = buttonView.getCompoundDrawables()[1];
                if(drawable != null){
                    drawable.setColorFilter(getContext().getColor(R.color.color_text_secondary_label), PorterDuff.Mode.SRC_ATOP);
                    buttonView.setCompoundDrawables(null,drawable,null,null);
                }

            }
        }
    }

    /**
     * 设置button滑动动画
     *
     * @param originId 起始坐标
     * @param finalId  终点坐标
     */
    private void setAnimation(int originId, int finalId) {
        ValueAnimator valueAnimator = ValueAnimator.ofFloat(originId, finalId);/*设置位移变化动画*/
        valueAnimator.addUpdateListener(animation -> {
            mCanvasX = (float) animation.getAnimatedValue();/*同步滑块位置参数*/
            postInvalidate();/*同步OnDraw，重新绘制滑块*/
        });
        valueAnimator.setDuration(ANIM_TIMER);/*动画持续时间*/
        valueAnimator.start();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        if (null != mSlideDrawable) {
            int save = canvas.save();
            if (mOrientation == HORIZONTAL) {
                canvas.translate(mPaddingStart + mCanvasX * width, mPaddingTop);
                //                canvas.drawBitmap(bitmap, mPaddingStart + mCanvasX * width, mPaddingTop, null);/*绘制滑块的图片*/
            } else {
                canvas.translate(mPaddingTop, mCanvasX * height);
                //                canvas.drawBitmap(bitmap, mPaddingTop, mCanvasX * height, null);/*绘制滑块的图片*/
            }
            mSlideDrawable.draw(canvas);
            canvas.restoreToCount(save);
        }

        // 设置置灰的背景
//        if(!isEnabled()) {
//            if(null != mMaskDrawable) {
//                mMaskDrawable.setBounds(0, 0, getWidth(), getHeight());
//                mMaskDrawable.draw(canvas);
//            }
//        }

    }

    @Override
    public void setEnabled(boolean enabled) {
        super.setEnabled(enabled);
        if (!enabled){
            setRadioButtonChecked(2);
        }
        for (int i = 0; i < getChildCount(); i++) {
            View childAt = getChildAt(i);
            childAt.setEnabled(enabled);
        }
    }

    /**
     * 单选RadioButton方法（外抛使用）
     *
     * @param id
     */
    public void setRadioButtonChecked(int id) {
        RadioButton mRadioButton = (RadioButton) getChildAt(id);
        if (null != mRadioButton && !mRadioButton.isChecked()) {
            mRadioButton.setChecked(true);
            mIndex = indexOfChild(mRadioButton);
        }
    }

    /**
     * 设置RadioButton单选回调
     *
     * @param checkedCallBack 回调接口
     */
    public void setCheckedCallBack(CheckedCallBack checkedCallBack) {
        this.mCheckedCallBack = checkedCallBack;
    }

    /**
     * RadioButton单选后的回调
     */
    public interface CheckedCallBack {
        void radioButtonChecked(int position, String name);
    }

    @Override
    protected void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);

    }
}
