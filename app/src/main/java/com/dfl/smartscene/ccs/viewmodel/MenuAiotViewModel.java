package com.dfl.smartscene.ccs.viewmodel;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.model.AiotModel;

import java.util.List;

import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR>
 * @date ：2022/9/21 17:44
 * @description ：
 */
public class MenuAiotViewModel extends ViewModel {
    // 防止内存泄漏
    private CompositeDisposable mCompositeDisposable = new CompositeDisposable();

    private MutableLiveData<List<SingleOperation>> operationsData = new MutableLiveData<>();

    public MutableLiveData<List<SingleOperation>> getOperationsData() {
        return operationsData;
    }

    private MutableLiveData<PosSingleOperation> aiotModifyOperation = new MutableLiveData<>();
    /**AIOT设备列表异常*/
    public MutableLiveData<Boolean> mAiotDeviceListAbnormal = new MutableLiveData<>();
    private static final String TAG = "MenuAiotViewModel";

    public MutableLiveData<PosSingleOperation> getAiotModifyOperation() {
        return aiotModifyOperation;
    }

    private MutableLiveData<String> aiotDesc = new MutableLiveData<>();
    public MutableLiveData<String> getAiotDesc() {
        return aiotDesc;
    }

    public void requestAiotBindAndSaleOperation(){
        AiotModel.getInstance().requestAiotBindAndSaleDevices().observeOn(AndroidSchedulers.mainThread()).subscribe(new Observer<List<SingleOperation>>() {
            @Override
            public void onSubscribe(Disposable d) {
                mCompositeDisposable.add(d);
            }

            @Override
            public void onNext(List<SingleOperation> operations) {
                LogUtil.d(TAG, "onNext: "+operations.size());
                if(operations.size()==0){
                    mAiotDeviceListAbnormal.postValue(true);
                }else {
                    operationsData.setValue(operations);
                }
            }

            @Override
            public void onError(Throwable e) {
                LogUtil.d(TAG , e.getMessage());
                mAiotDeviceListAbnormal.postValue(true);
            }

            @Override
            public void onComplete() {

            }
        });
    }

    public void requestSingleOperationBySettingOpe(int pos,SettingOperation settingOperation){
        AiotModel.getInstance().requestOperationByOpe(settingOperation).observeOn(AndroidSchedulers.mainThread()).subscribe(new Observer<SingleOperation>() {
            @Override
            public void onSubscribe(Disposable d) {
                mCompositeDisposable.add(d);
            }

            @Override
            public void onNext(SingleOperation singleOperation) {
                if(singleOperation == null){
                    aiotModifyOperation.setValue(null);
                    return;
                }
                PosSingleOperation posSingleOperation = new PosSingleOperation(pos,singleOperation,settingOperation);
                aiotModifyOperation.setValue(posSingleOperation);
            }

            @Override
            public void onError(Throwable e) {
                aiotModifyOperation.setValue(null);
            }

            @Override
            public void onComplete() {

            }
        });
    }

    public void requestSceneUnbuyDevices(List<SettingOperation> ops){
        AiotModel.getInstance().requestSceneUnbuyDevices(ops).observeOn(AndroidSchedulers.mainThread()).subscribe(new Observer<String>() {
            @Override
            public void onSubscribe(Disposable d) {
                mCompositeDisposable.add(d);
            }

            @Override
            public void onNext(String s) {
                if(s != null){
                    aiotDesc.postValue(s);
                }
            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onComplete() {

            }
        });
    }


    @Override
    protected void onCleared() {
        super.onCleared();
        mCompositeDisposable.dispose();
    }

    public static class PosSingleOperation{
        private int pos;
        private SingleOperation mSingleOperation;
        private SettingOperation mSettingOperation;

        public PosSingleOperation(int pos, SingleOperation singleOperation, SettingOperation settingOperation) {
            this.pos = pos;
            mSingleOperation = singleOperation;
            mSettingOperation = settingOperation;
        }

        public int getPos() {
            return pos;
        }

        public SingleOperation getSingleOperation() {
            return mSingleOperation;
        }

        public SettingOperation getSettingOperation() {
            return mSettingOperation;
        }
    }
}
