package com.dfl.smartscene.ccs.view.commonview;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

import com.dfl.smartscene.R;


/**
 * 卡片切换圆点
 * @author: huang<PERSON>zheng
 * @date: 2021/11/25
 */
public class CardContainerMark extends View {
    private int mMeasuredH;//view高度
    private int mMeasuredW;//view宽度
    private Paint mPaint;
    private int mSpace = 22;//圆点间距
    private int mRadius = 4;//圆点半径
    private int mCount;//圆点数量
    private int mPosition;//高亮位置
    public CardContainerMark(Context context) {
        super(context);
        initView();
    }

    public CardContainerMark(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public CardContainerMark(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    private void initView(){
        mPaint = new Paint();
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setStrokeWidth(5);
        mPaint.setAntiAlias(true);
    }

    public void setCount(int count) {
        this.mCount = count;
        invalidate();
    }

    public void setPosition(int position) {
        this.mPosition = position;
        invalidate();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if(mCount > 0){
            int offset = (mMeasuredW - mSpace *(mCount - 1))/2;
            for(int i = 0; i < mCount; i++){
                if (mPosition == i){
                    //高亮圆点
                    mPaint.setColor(getResources().getColor(R.color.color_mark_indicator_select));
                }else {
                    mPaint.setColor(getResources().getColor(R.color.color_mark_indicator_unselect));
                }
                canvas.drawCircle(offset + mSpace * i, mMeasuredH /2 , mRadius,mPaint);
            }
        }

    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        mMeasuredH = getMeasuredHeight();
        mMeasuredW = getMeasuredWidth();
    }
}
