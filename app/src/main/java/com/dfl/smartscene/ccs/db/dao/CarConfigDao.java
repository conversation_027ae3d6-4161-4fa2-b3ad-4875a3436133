package com.dfl.smartscene.ccs.db.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;


import com.dfl.smartscene.ccs.db.entity.CarConfigEntity;

import java.util.List;

import io.reactivex.Observable;

@Dao
public interface CarConfigDao {

    @Insert
    void insertStudent(CarConfigEntity carConfigEntity);

    @Delete
    void deleteStudent(CarConfigEntity carConfigEntity);

    @Update
    void updateStudent(CarConfigEntity carConfigEntity);

    @Query("SELECT * FROM car_config")
    Observable<List<CarConfigEntity>> queryAll();

    @Query("SELECT * FROM car_config WHERE :car_type='' or car_type= :car_type")
    Observable<List<CarConfigEntity>> queryByCarType(String car_type);

}
