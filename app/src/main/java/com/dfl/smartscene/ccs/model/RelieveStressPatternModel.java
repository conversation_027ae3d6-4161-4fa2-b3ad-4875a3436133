package com.dfl.smartscene.ccs.model;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.SharedPreferences;
import android.media.AudioManager;
import android.os.CountDownTimer;
import android.os.RemoteException;
import android.widget.Toast;

import androidx.lifecycle.MutableLiveData;

import com.dfl.api.base.APICreateCallback;
import com.dfl.api.base.APIStateType;
import com.dfl.api.base.BaseManager;
import com.dfl.api.base.Constants;
import com.dfl.api.base.IBaseAPI;
import com.dfl.api.base.NullServiceException;
import com.dfl.api.da.setting.ISettingSoundEffect;
import com.dfl.api.da.setting.ISettingSoundEffectCallback;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.COnlineMediaManager;
import com.dfl.smartscene.ccs.callback.IScenePatternServiceCallback;
import com.dfl.smartscene.ccs.constant.RelieveStressFuncDef;
import com.dfl.smartscene.ccs.constant.ScenePatternFuncDef;
import com.dfl.smartscene.ccs.util.BigDataControl;
import com.dfl.smartscene.ccs.util.HandlerThreadProcessor;
import com.dfl.smartscene.ccs.util.SysViewControl;
import com.dfl.smartscene.ccs.util.ToastManager;
import com.iauto.uibase.utils.MLog;

import java.util.ArrayList;
import java.util.Locale;
import java.util.Map;

/*
 * 情景模式Model
 * <AUTHOR>
 * @date 2022/4/15
 */
public class RelieveStressPatternModel {
    private static final String TAG = "RelieveStressPatternModel";
    @SuppressLint("StaticFieldLeak")
    private static RelieveStressPatternModel        sRelieveStressPatternModel = null;
    private        Context                          mContext                   = null;
    private        byte[]                           mApiLock                   = new byte[0];
    private HandlerThreadProcessor handlerThreadProcessor = HandlerThreadProcessor.getInstance();
    private final  ArrayList<IScenePatternServiceCallback> mCallbacks             = new ArrayList<>();
    private        AudioManager                     mAudioManager;
    private ISettingSoundEffect mISettingSoundEffect = null;
    private        ScenePatternInterrupt            mScenePatternInterrupt     = null;
    private final MutableLiveData<Integer> mInterrupt     = new MutableLiveData<>();        // 中断类型

    InterruptListenerImpl                                            mInterruptListener = new InterruptListenerImpl();
    RelieveStressCountDownTimer mPlayTimer         = null;


    // liveData数据
    private final MutableLiveData<Integer> mRelieveStressTimerIndex = new MutableLiveData<>();   // 倒计时timer list index
    private final MutableLiveData<Integer> mRelieveStressStatus     = new MutableLiveData<>();   // 舒压模式播放状态
    private final MutableLiveData<Integer> mRelieveStressPattern = new MutableLiveData<>();      // 舒压模式当前模式
    private final MutableLiveData<Integer> mRelieveStressStartStatus = new MutableLiveData<>();  // 舒压模式打开关闭状态
    private final MutableLiveData<Long> mRelieveStressTimer = new MutableLiveData<>();           // 舒压模式timer值
    private final MutableLiveData<String> mRelieveStressDspTimer = new MutableLiveData<>();           // 舒压模式timer值
    private final MutableLiveData<Integer> mVolumeValue = new MutableLiveData<>();               // 音量
    private final MutableLiveData<Integer> mPhoneInterruptStatus = new MutableLiveData<>();      // 蓝牙电话中断
    private final MutableLiveData<Integer> mTransferOrigin = new MutableLiveData<>();            // 迁移出发点定义
    private final MutableLiveData<Integer> mAnimateStatus = new MutableLiveData<>();            // 迁移出发点定义
    private final MutableLiveData<Boolean> mFullDSPStatus = new MutableLiveData<>();            // 迁移出发点定义

    public MutableLiveData<Boolean> getFullDSPStatus() {
        return mFullDSPStatus;
    }

    public MutableLiveData<Integer> getRelieveStressTimerIndex() {
        return mRelieveStressTimerIndex;
    }

    public MutableLiveData<Integer> getRelieveStressStatus() {
        return mRelieveStressStatus;
    }

    public MutableLiveData<Integer> getRelieveStressPattern() {
        return mRelieveStressPattern;
    }

    public MutableLiveData<Integer> getRelieveStressStartStatus() {
        return mRelieveStressStartStatus;
    }

    public MutableLiveData<Long> getRelieveStressTimer() {
        return mRelieveStressTimer;
    }

    public MutableLiveData<Integer> getVolumeValue() {
        return mVolumeValue;
    }

    /**
     * get RelieveStressPatternModel's instance
     */
    public static RelieveStressPatternModel getInstance(){
        return sRelieveStressPatternModel;
    }

    public MutableLiveData<Integer> getPhoneInterruptStatus() {
        return mPhoneInterruptStatus;
    }

    public MutableLiveData<Integer> getTransferOrigin() {
        return mTransferOrigin;
    }

    public void setTransferOrigin(int transferOrigin) {
        mTransferOrigin.setValue(transferOrigin);
    }

    public MutableLiveData<String> getRelieveStressDspTimer() {
        return mRelieveStressDspTimer;
    }

    public void setRelieveStressDspTimer(String timer) {
        mRelieveStressDspTimer.setValue(timer);
    }

    public MutableLiveData<Integer> getAnimateStatus() {
        return mAnimateStatus;
    }

    public void setAnimateStatus(int status) {
        mAnimateStatus.setValue(status);
    }

    public void setFullDSPStatus(boolean status) {
        mFullDSPStatus.setValue(status);
    }

    /**
     * RelieveStressPatternModel object constructor
     * @param context
     */
    private RelieveStressPatternModel(Context context) {
        mContext = context;
        
        // 为了变更音量获取Audio Manager 
        mAudioManager = (AudioManager) mContext.getSystemService(Context.AUDIO_SERVICE);

        // 中断实例获并注册回调
        mScenePatternInterrupt = ScenePatternInterrupt.getInstance();
        mScenePatternInterrupt.registerScenePatternInterruptListener(mInterruptListener);

        // 网络播放管理实例创建
        COnlineMediaManager.create(context);
        // init livedata
        initLiveData();
    }

    /**
     * create RelieveStressPatternModel's instance
     * @param context
     */
    public static synchronized void create(Context context) {
        if(sRelieveStressPatternModel == null){
            sRelieveStressPatternModel = new RelieveStressPatternModel(context);
        }
    }

    /**
     * 取得初始化数据
     */
    private void getInitDataFromCustomAPI() {
        setRelieveStressTimerIndex(getRelieveStressTimerFromMemory());
        mRelieveStressStatus.setValue(RelieveStressFuncDef.Relieve_Stress_Status_Stop);
        mRelieveStressPattern.setValue(getRelieveStressPatternFromMemory());
        mRelieveStressStartStatus.setValue(ScenePatternFuncDef.Relieve_Stress_Status_Close);
        mVolumeValue.setValue(RelieveStressFuncDef.Relieve_Stress_Default_Volume);
        mPhoneInterruptStatus.setValue(RelieveStressFuncDef.Relieve_Stress_Phone_Interrupt_None);
        mTransferOrigin.setValue(RelieveStressFuncDef.Relieve_Stress_Transfer_Origin_None);
        mInterrupt.setValue(ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_NONE);
        mFullDSPStatus.setValue(true);
    }

    /**
     * 初始化live data
     */
    private void initLiveData() {
        getInitDataFromCustomAPI();
        BaseManager.create(mContext, Constants.DFL_DA_SETTING_SETTINGSOUNDEFFECT_SERVICE, new APICreateCallback() {
            @Override
            public void onAPICreateCallback(APIStateType.APIState apiStateType, String s, IBaseAPI iBaseAPI) {
                MLog.d(TAG, "onAPICreateCallback state= " + apiStateType.ordinal() + ",apiInstance= " + iBaseAPI + ",requestName= " + s);
                if (Constants.DFL_DA_SETTING_SETTINGSOUNDEFFECT_SERVICE.equals(s)) {
                    synchronized (mApiLock) {
                        if (apiStateType == APIStateType.APIState.SUCCESS && iBaseAPI != null && iBaseAPI instanceof ISettingSoundEffect) {
                            mISettingSoundEffect = (ISettingSoundEffect) iBaseAPI;
                            try {
                                mISettingSoundEffect.registerSettingSoundEffectCallback(new ISettingSoundEffectCallback() {
                                    public void onVolumeChanged(int i, int i1) {
                                        handlerThreadProcessor.postToMainThread(new Runnable() {
                                            @Override
                                            public void run() {
                                                MLog.d(TAG, "onVolumeChanged i = " + i + " i1 = " + i1);
                                                switch (i) {
                                                    case 3:
                                                        getVolumeValue().setValue(i1);
                                                        break;
                                                    default:
                                                        break;
                                                }
                                            }
                                        });
                                    }
                                    @Override
                                    public void onMessageToneEnableChanged(boolean b) {

                                    }

                                    @Override
                                    public void onBeepEnableChanged(boolean b) {

                                    }


                                    @Override
                                    public void onSpeedSensitiveVolumeResponse(int i) {

                                    }

                                    @Override
                                    public void onTrebleEffectResponse(int i) {

                                    }

                                    @Override
                                    public void onBassEffectResponse(int i) {

                                    }

                                    @Override
                                    public void onAltoEffectChange(int i) {

                                    }

                                    @Override
                                    public void onMegaBassEnableChanged(boolean b) {

                                    }
                                    @Override
                                    public void onSoundFieldChange(int i) {

                                    }

                                    @Override
                                    public void onBoundlessSoundFieldChange(int xfield, int yfield) {

                                    }

                                    @Override
                                    public void onHeadrestSoundFieldChange(int field) {

                                    }

                                    @Override
                                    public void onHeadrestModeStatusChange(int mode, int status) {

                                    }

                                    @Override
                                    public void onEffectModeByPositionChange(int i, int i1) {

                                    }

                                    @Override
                                    public void onWelcomeAndFarewellSoundEffectsChange(int i, int i1) {

                                    }

                                    @Override
                                    public void onCurrentSpeakerChange(int i, int i1) {

                                    }

                                    @Override
                                    public void onSoundEffectStatusChange(int i) {

                                    }

                                    @Override
                                    public void onSoundWaveVolumeTypeChange(int type) {

                                    }


                                    @Override
                                    public void onIntelligentSoundAdjustmentScenariosStatusChange(int i, int i1) {

                                    }

                                    @Override
                                    public void onAISoundEffectChange(int status) {

                                    }

                                    @Override
                                    public void onSoundOptimizationChange(int status) {

                                    }

                                    @Override
                                    public void onSoundRailSeparationChange(int type) {

                                    }

                                    @Override
                                    public void onKTVSingModeChange(int status) {

                                    }

                                    @Override
                                    public void onBluetoothMusicVolumeChange(int volume) {

                                    }

                                    @Override
                                    public void onEQValuesChange(float[] values) {

                                    }

                                    @Override
                                    public void onBeforeAfterBalanceValueChange(int[] values) {

                                    }

                                    @Override
                                    public void onResetChange(int result) {

                                    }


                                });
                            } catch (NullServiceException e) {
                                mISettingSoundEffect = null;
                                MLog.e(TAG, "->registerSettingSoundEffectCallback() NullServiceException!!!");
                            }
                        } else {
                            mISettingSoundEffect = null;
                            MLog.e(TAG, "create failed");
                        }
                    }
                }
            }

            @Override
            public void onMethodStateCallback(APIStateType.APIState apiState, String s) {
            }

        });

        if (null != mVolumeValue) {
            mVolumeValue.setValue(RelieveStressFuncDef.Relieve_Stress_Default_Volume);
        }

        if (null != mVolumeValue) {
            if (null != mAudioManager) {
                mVolumeValue.setValue(mAudioManager.getStreamVolume(AudioManager.STREAM_MUSIC));
            } else {
                mVolumeValue.setValue(0);
            }
        }
    }

    /**
     * 设定舒压模式的倒计时index
     * @param index Timer Index 0~10
     */
    public void setRelieveStressTimerIndex(int index) {
        MLog.d(TAG, "setRelieveStressTimer:"+index);

        mRelieveStressTimerIndex.setValue(index);
        setRelieveStressTimerToMemory(index);
        if (0 != index) {
            setRelieveStressDspTimer(makeDspTimer(getTimerValue(index)*1000*60));
            setRelieveStressTimer(getTimerValue(index)*1000*60);
        } else {
            setRelieveStressTimer(0);
            setRelieveStressDspTimer("");
        }
    }

    /**
     * 设定舒压模式的播放状态，并进行相应处理
     * @param value Status 0~2
     */
    public void setRelieveStressStatus(int value) {
        MLog.d(TAG, "setRelieveStressStatus:"+value);
        if (value == RelieveStressFuncDef.Relieve_Stress_Status_Play) {
            if (RelieveStressFuncDef.Relieve_Stress_Status_Pause == mRelieveStressStatus.getValue()) {
                COnlineMediaManager.getInstance().doPlayFromPause();
            } else if (RelieveStressFuncDef.Relieve_Stress_Status_Stop == mRelieveStressStatus.getValue()) {
                COnlineMediaManager.getInstance().doPlayStart();
            } else {
                MLog.d(TAG, "Relieve_Stress_Status_Phone_Stop:"+mRelieveStressStatus.getValue());
            }
            Map map = BigDataControl.getInstance().makePlay(getRelieveStressTimer().getValue(), getPatternText(getRelieveStressPattern().getValue()));
            BigDataControl.getInstance().writeEventTrack(ScenePatternFuncDef.BIGDATA_EVENT_ID_2,
                                                        ScenePatternFuncDef.BIGDATA_NAME_PLAY,
                                                        ScenePatternFuncDef.BIGDATA_EVENT_TYPE_CLICK,
                                                        map);
            newDspTimer();
            setAnimateStatus(RelieveStressFuncDef.Relieve_Stress_Status_Play);

        } else if (RelieveStressFuncDef.Relieve_Stress_Status_Pause == value) {
            COnlineMediaManager.getInstance().doPause();
            cancelDspTimer();
            setAnimateStatus(RelieveStressFuncDef.Relieve_Stress_Status_Pause);

            BigDataControl.getInstance().writeEventTrack(ScenePatternFuncDef.BIGDATA_EVENT_ID_3,
                    ScenePatternFuncDef.BIGDATA_NAME_PAUSE,
                    ScenePatternFuncDef.BIGDATA_EVENT_TYPE_CLICK,
                    null);
        } else if (RelieveStressFuncDef.Relieve_Stress_Status_Stop == value) {
            stopRelieveStress();
            setAnimateStatus(RelieveStressFuncDef.Relieve_Stress_Status_Stop);
            mInterrupt.setValue(ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_NONE);
        } else if(RelieveStressFuncDef.Relieve_Stress_Status_Phone_Stop == value) {
            cancelDspTimer();
            setAnimateStatus(RelieveStressFuncDef.Relieve_Stress_Status_Pause);
        } else {
            // do nothing
        }
        mRelieveStressStatus.setValue(value);
    }

    /**
     * 播放下一曲处理
     */
    public void setRelieveStressPlayNext() {
        COnlineMediaManager.getInstance().doPlayNext();
        if(getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Play ||
                getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Pause) {
            setAnimateStatus(RelieveStressFuncDef.Relieve_Stress_Status_Play);
            if (getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Pause) {
                newDspTimer();
            }
        }
        mRelieveStressStatus.setValue(RelieveStressFuncDef.Relieve_Stress_Status_Play);
    }

    /**
     * 设定舒压模式的模式，模式切换的时候需要停止音乐播放
     * @param value Patter Index 0~2
     */
    public void setRelieveStressPattern(int value) {
        MLog.d(TAG, "setRelieveStressTimer:"+value);
        if( value == mRelieveStressPattern.getValue()) {
            return;
        }

        Map map = BigDataControl.getInstance().makeChangeTheme(getPatternText(value));
        BigDataControl.getInstance().writeEventTrack(ScenePatternFuncDef.BIGDATA_EVENT_ID_4,
                ScenePatternFuncDef.BIGDATA_NAME_CHANGE_THEME,
                ScenePatternFuncDef.BIGDATA_EVENT_TYPE_CLICK,
                map);

        mRelieveStressPattern.setValue(value);
        setRelieveStressPatternToMemory(value);
    }

    /**
     * 设定舒压模式的启动状态
     * @param value Patter Index 0 or 1
     */
    public void setRelieveStressStartStatus(int value) {
        MLog.d(TAG, "setRelieveStressStartStatus:"+value);
        notifyRelaxStartStatus(value);
    }

    /**
     * set Relieve Stress Patter
     * @param value Patter Index 0 or 1
     */
    public void setRelieveStressTimer(long value) {
        MLog.d(TAG, "setRelieveStressTimer:"+value);
        mRelieveStressTimer.setValue(value);
    }

    /**
     * set Relieve Stress Patter
     */
    public void resetRelieveStressTimer() {
        int index = getRelieveStressTimerIndex().getValue();
        if(0 != index) {
            mRelieveStressTimer.setValue(getTimerValue(index)*1000*60);
        }
    }

    /**
     * 设置多媒体音量
     * @param value 音量值
     */
    public void reqVolumeto(int value) {
        MLog.d(TAG, "reqVolumeto() = " + value);
        if (null != mAudioManager) {
            mAudioManager.setStreamVolume(AudioManager.STREAM_MUSIC, value ,0);
        }
    }

    /**
     * @method registerCallback
     * @description 注册回调
     * @param callback 回调对象
     */
    public void registerScenePatternCallback(IScenePatternServiceCallback callback) {
        MLog.d(TAG, "registerScenePatternCallback");
        int status = mRelieveStressStartStatus.getValue();
        if (null != callback) {
            try {
                callback.onRelaxStatusChange(status);
            }
            catch (RemoteException e) {
                MLog.e(TAG, "->registerSenceCallback() Exception!!!");
            }
            mCallbacks.add(callback);
        }
    }


    /**
     * @method unregisterSenceCallback
     * @description 注销回调
     * @param callback 回调对象
     */
    public void unregisterScenePatternCallback(IScenePatternServiceCallback callback) {
        MLog.d(TAG, "unregisterScenePatternCallback");
        if (null != callback) {
            mCallbacks.remove(callback);
        }
    }

    /**
     * @method notifyRelaxStatus
     * @description Ehp 输出消息回调函数
     * @param status 状态变更值
     */
    public void notifyRelaxStartStatus(int status) {
        MLog.d(TAG, "notifyRelaxStatus status: " + status);
        mRelieveStressStartStatus.setValue(status);
        if (mCallbacks.size() == 0) {
            MLog.d(TAG, "mCallbacks.size() == 0");
            return;
        }
        for (IScenePatternServiceCallback callback : mCallbacks) {
            try {
                if (callback != null) {
                    MLog.d(TAG, "IScenePatternServiceCallback callback : mCallbacks");
                    callback.onRelaxStatusChange(status);
                }
            } catch (RemoteException e) {
                MLog.e(TAG, "->notifyRelaxStartStatus() Exception!!!");
            }
        }
    }

    /**
     * 取得保存的舒压模式的模式
     * @return int 模式值
     */
    public int getRelieveStressPatternFromMemory() {
        SharedPreferences sp = mContext.getSharedPreferences("ScenePattern", mContext.MODE_PRIVATE);
        int relieveStressPattern = sp.getInt("relieveStressPattern", 0);
        MLog.d(TAG, "relieveStressPattern: " + relieveStressPattern);
        return relieveStressPattern;
    }

    /**
     * 保存舒压模式的模式
     * @param relieveStressPattern 模式值
     */
    public void setRelieveStressPatternToMemory(int relieveStressPattern) {
        MLog.d(TAG, "setRelieveStressPatternToMemory: " + relieveStressPattern);
        SharedPreferences sp = mContext.getSharedPreferences("ScenePattern", mContext.MODE_PRIVATE);
        SharedPreferences.Editor et = sp.edit();
        // first call flag
        et.putInt("relieveStressPattern", relieveStressPattern);
        et.commit();
    }

    /**
     * 取得保存的舒压模式的倒计时index
     * @return int 模式值
     */
    public int getRelieveStressTimerFromMemory() {
        SharedPreferences sp = mContext.getSharedPreferences("ScenePattern", mContext.MODE_PRIVATE);
        int relieveStressTimer = sp.getInt("relieveStressTimer", 2);
        if( relieveStressTimer < 0 || relieveStressTimer > 3) {
            relieveStressTimer = 2;
        }

        MLog.d(TAG, "relieveStressTimer: " + relieveStressTimer);
        return relieveStressTimer;
    }

    /**
     * 保存舒压模式的倒计时index
     * @param relieveStressTimer 倒计时index
     */
    public void setRelieveStressTimerToMemory(int relieveStressTimer) {
        MLog.d(TAG, "setRelieveStressTimerToMemory: " + relieveStressTimer);

        SharedPreferences sp = mContext.getSharedPreferences("ScenePattern", mContext.MODE_PRIVATE);
        SharedPreferences.Editor et = sp.edit();
        // first call flag
        et.putInt("relieveStressTimer", relieveStressTimer);
        et.commit();
    }

    /*
    * 中断通知接口类
    * <AUTHOR>
    * @date 2022/5/15
    */
    public class InterruptListenerImpl implements ScenePatternInterrupt.ScenePatternInterruptListener {
        @Override
        public void onInterrupt(int interruptType) {
            MLog.d(TAG, "onInterrupt: " + interruptType);
            if(getRelieveStressStatus().getValue()  == RelieveStressFuncDef.Relieve_Stress_Status_Stop){
                MLog.d(TAG, "not play");
                mInterrupt.setValue(ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_NONE);
                return;
            }
            switch (interruptType) {
                case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_GEAR_TYPE_NOT_P:
                case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_VR:
                case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_ECALL:
                case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_INSTRUMENT_WARNING:
                    MLog.d(TAG, "onInterrupt:other");
                    mInterrupt.setValue(interruptType);
                    makeBigDataStopReason(interruptType);
                    setRelieveStressStatus(RelieveStressFuncDef.Relieve_Stress_Status_Stop);
                    if(mFullDSPStatus.getValue()) {
                        SysViewControl.getInstance().reqCloseRelieveStressPatternFullView();
                    }
                    mPhoneInterruptStatus.setValue(RelieveStressFuncDef.Relieve_Stress_Phone_Interrupt_None);
                    break;
                case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_SOURCE_CHANGE:
                    MLog.d(TAG, "onInterrupt:source change");
                    String ChangedSource = ScenePatternInterrupt.getInstance().getChangedSource().getValue();
                    MLog.d(TAG, "change source:" + ChangedSource);
                    if ((getRelieveStressStatus().getValue() != RelieveStressFuncDef.Relieve_Stress_Status_Phone_Stop) &&
                            !ChangedSource.equals(RelieveStressFuncDef.MEDIA_SOURCE_BTPHONE_PACKAGE_NAME)) {
                        String originalSource = ScenePatternInterrupt.getInstance().getOriginalSource().getValue();

                        MLog.d(TAG, "onInterrupt:originalSource " + originalSource);
                        if(originalSource.equals(RelieveStressFuncDef.MEDIA_SOURCE_MUSIC_PACKAGE_NAME)) {
                            mInterrupt.setValue(interruptType);
                            makeBigDataStopReason(interruptType);
                            setRelieveStressStatus(RelieveStressFuncDef.Relieve_Stress_Status_Stop);
                            if(mFullDSPStatus.getValue()) {
                                SysViewControl.getInstance().reqCloseRelieveStressPatternFullView();
                            }
                        }
                        mPhoneInterruptStatus.setValue(RelieveStressFuncDef.Relieve_Stress_Phone_Interrupt_None);
                    }
                    break;
                case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_BLUETOOTH_CALL:
                    MLog.d(TAG, "onInterrupt:bluetooth call");
                    if (getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Play
                            || getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Pause) {
                        MLog.d(TAG, "bluetooth interrupt play or pause");
                        mInterrupt.setValue(interruptType);
                        makeBigDataStopReason(interruptType);
                        setRelieveStressStatus(RelieveStressFuncDef.Relieve_Stress_Status_Phone_Stop);
                        mPhoneInterruptStatus.setValue(RelieveStressFuncDef.Relieve_Stress_Phone_Interrupt_True);
                        if(mFullDSPStatus.getValue()) {
                            SysViewControl.getInstance().reqCloseRelieveStressPatternFullView();
                        }
                    }
                    break;

                case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_NONE:
                    mInterrupt.setValue(ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_NONE);
                    if(mPhoneInterruptStatus.getValue() == RelieveStressFuncDef.Relieve_Stress_Phone_Interrupt_True &&
                            mRelieveStressStatus.getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Phone_Stop) {
                        mPhoneInterruptStatus.setValue(RelieveStressFuncDef.Relieve_Stress_Phone_Interrupt_False);
                        setRelieveStressStatus(RelieveStressFuncDef.Relieve_Stress_Status_Play);
                        SysViewControl.getInstance().reqOpenRelieveStressPatternFullView(ScenePatternFuncDef.SYS_VIEW_CTRL_PATTERN_RELIEVE);
                    }
                    break;
                default:
                    break;
            }
        }
    }

    /*
     * 倒计时处理
     * <AUTHOR>
     * @date 2022/4/24
     */
    class RelieveStressCountDownTimer extends CountDownTimer {
        public RelieveStressCountDownTimer(long millisInFuture, long countDownInterval) {
            super(millisInFuture,countDownInterval);
        }

        @Override
        public void onTick(long l) {
            setRelieveStressTimer(l);
            setRelieveStressDspTimer(makeDspTimer(l));
        }

        @Override
        public void onFinish() {
            makeBigDataStopReason(ScenePatternFuncDef.SCENE_PATTERN_STOP_REASON_TIMEOUT);
            setRelieveStressStatus(RelieveStressFuncDef.Relieve_Stress_Status_Stop);
            if(mFullDSPStatus.getValue()) {
                SysViewControl.getInstance().reqCloseRelieveStressPatternFullView();
            }
        }
    }

    /**
     * 做成倒计时表示数据
     *
     * @param countTimer 表示的timer
     */
    private String makeDspTimer(long countTimer) {
        MLog.d(TAG, "displayRelieveStressTimer: ");
        float countSec = (float)(countTimer)/1000;
        int min = Math.round(countSec)/60;
        int sec = Math.round(countSec)%60;
        String formatTime = String.format(Locale.getDefault(),"%02d:%02d",min,sec);
        MLog.d(TAG, formatTime);
        return formatTime;
    }

    /**
     * 舒压模式播放停止
     *
     */
    private void stopRelieveStress() {
        MLog.d(TAG, "stopRelieveStress");
        cancelDspTimer();
        resetRelieveStressTimer();
        MLog.d(TAG, "stopRelieveStress:mInterrupt:"+mInterrupt.getValue());
        if (ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_NONE == mInterrupt.getValue()) {
            COnlineMediaManager.getInstance().doStop(true);
        }else {
            COnlineMediaManager.getInstance().doStop(false);
        }

        ToastManager mToastManager = ToastManager.getInstance();
        mToastManager.showToast(mContext.getString(R.string.string_relieve_stress_toast_stop),
                Toast.LENGTH_SHORT,
                false);
    }

    /**
     * 新建倒计时
     *
     */
    private void newDspTimer() {
        MLog.d(TAG, "newDspTimer");
        cancelDspTimer();
        setRelieveStressDspTimer(makeDspTimer(getRelieveStressTimer().getValue().longValue()));
        if (0 != getRelieveStressTimer().getValue()) {
            mPlayTimer = new RelieveStressCountDownTimer(getRelieveStressTimer().getValue(), 1000);
            mPlayTimer.start();
        }
    }

    /**
     * 取消倒计时
     *
     */
    private void cancelDspTimer() {
        MLog.d(TAG, "cancelDspTimer");
        if (mPlayTimer != null) {
            mPlayTimer.cancel();
            mPlayTimer = null;
        }
    }

    /**
     * 做成埋点数据-停止原因
     * @param type 停止原因
     *
     */
    public void makeBigDataStopReason(int type) {
        MLog.d(TAG, "makeBigDataStopReason"+type);
        long duration = 0;

        int index = getRelieveStressTimerIndex().getValue();
        if(0 != index) {
            duration = getTimerValue(index)*60*1000-getRelieveStressTimer().getValue();
        }
        String reason = "";
        switch (type) {
            case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_GEAR_TYPE_NOT_P:
                reason = ScenePatternFuncDef.BIGDATA_VALUE_STOP_FROM_NOT_P;
                break;
            case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_VR:
                reason = ScenePatternFuncDef.BIGDATA_VALUE_STOP_FROM_VR;
                break;
            case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_ECALL:
                reason = ScenePatternFuncDef.BIGDATA_VALUE_STOP_FROM_ECALL;
                break;
            case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_INSTRUMENT_WARNING:
                reason = ScenePatternFuncDef.BIGDATA_VALUE_STOP_FROM_INSTRUMENT_WARNING;
                break;
            case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_SOURCE_CHANGE:
                reason = ScenePatternFuncDef.BIGDATA_VALUE_STOP_FROM_SOURCE_CHANGE;
                break;
            case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_BLUETOOTH_CALL:
                reason = ScenePatternFuncDef.BIGDATA_VALUE_STOP_FROM_PHONE;
                break;
            case ScenePatternFuncDef.SCENE_PATTERN_STOP_REASON_MANUAL:
                reason = ScenePatternFuncDef.BIGDATA_VALUE_STOP_FROM_MANUAL;
                break;
            case ScenePatternFuncDef.SCENE_PATTERN_STOP_REASON_TIMEOUT:
                reason = ScenePatternFuncDef.BIGDATA_VALUE_STOP_FROM_TIMEOUT;
                break;
            default:
                // do nothing
                break;
        }

        Map map = BigDataControl.getInstance().makeStopReason(duration,reason);
        BigDataControl.getInstance().writeEventTrack(ScenePatternFuncDef.BIGDATA_EVENT_ID_5,
                                                    ScenePatternFuncDef.BIGDATA_NAME_STOP,
                                                    ScenePatternFuncDef.BIGDATA_EVENT_TYPE_CLICK,
                                                    map);
    }

    /**
     * 从resource取得Timer的分钟值
     * @param index 停止原因
     *
     */
    private long getTimerValue(int index) {
        MLog.d(TAG, "getTimerValue："+index);
        String timerText = "";
        String []timerList = mContext.getResources().getStringArray(R.array.relieve_timer_list_value);
        if(index > 0 && index < timerList.length) {
            timerText = timerList[index];
        }

        long timerValue = 0;
        if(!timerText.isEmpty()) {
            timerValue = Long.parseLong(timerText);
        }
        return timerValue;
    }

    /**
     * 从resource取得模式的text
     * @param index 停止原因
     *
     */
    private String getPatternText(int index) {
        String patternText = "";
        String []patternTextList = mContext.getResources().getStringArray(R.array.relieve_pattern_list_text);
        if(index >= 0 && index < patternTextList.length) {
            patternText = patternTextList[index];
        }
        return patternText;
    }

    private int relaxStatus;

    public int getRelaxStatus() {
        return relaxStatus;
    }

    public void setRelaxStatus(int relaxStatus) {
        this.relaxStatus = relaxStatus;
    }
}
