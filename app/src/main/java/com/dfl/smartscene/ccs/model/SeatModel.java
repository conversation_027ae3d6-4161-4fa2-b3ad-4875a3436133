package com.dfl.smartscene.ccs.model;

import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.core.ConditionBaseModel;
import com.dfl.smartscene.ccs.core.DeviceBaseModel;
import com.dfl.smartscene.ccs.model.manager.CustomApiManager;
import com.iauto.uibase.utils.MLog;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/9/30 13:24
 * @description ：座椅相关类
 */
public class SeatModel implements StateBaseModel, ConditionBaseModel, CarConfigResetModel, DeviceBaseModel {

    private static final String TAG = "SeatModel";
    private static volatile SeatModel sInstance;

    public static SeatModel getInstance() {
        if (null == sInstance) {
            synchronized (SeatModel.class) {
                if (null == sInstance) {
                    sInstance = new SeatModel();
                }
            }
        }
        return sInstance;
    }


    /**
     * 监听座椅相关数据
     */
    public void registerSeatCallBack() {
    }

    @Override
    public boolean checkStateOperationMeet(SettingOperation settingOperation) {
        return false;
    }

    @Override
    public void registerSceneCondition(SceneBean sceneBean, SettingOperation condition) {
    }

    @Override
    public void unRegisterSceneCondition(SceneBean sceneBean, SettingOperation condition) {
    }

    @Override
    public int resetSingleConfig(SingleOperation singleOperation) {
        switch (singleOperation.getOperationId()) {
            case ConstantModelValue.OID_ACT_SEAT_DRIVER_MEMORY:
            case ConstantModelValue.OID_ACT_SEAT_PASSAGER_MEMORY:
                return resetMemoryConfig(singleOperation);
            default:
                return ConstantModelValue.CONFIG_RESET_RESULT_NOCHANGE;
        }
    }

    private int resetMemoryConfig(SingleOperation singleOperation) {
        int memoryType = 0;
        //1:主驾，2：副驾
//        try {
//            if (ConstantModelValue.OID_ACT_SEAT_DRIVER_MEMORY.equals(singleOperation.getOperationId())) {
//                memoryType = CustomApiManager.getInstance().getISeat().getSeatMemoryMode(1);
//            } else if (ConstantModelValue.OID_ACT_SEAT_PASSAGER_MEMORY.equals(singleOperation.getOperationId())) {
//                memoryType = CustomApiManager.getInstance().getISeat().getSeatMemoryMode(2);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            return ConstantModelValue.CONFIG_RESET_RESULT_NOCHANGE;
//        }catch (NoSuchMethodError e){
//            e.printStackTrace();
//            MLog.d(TAG,"customapi version error !!!");
//            return ConstantModelValue.CONFIG_RESET_RESULT_NOCHANGE;
//        }
        List<String> descList = new ArrayList<>();
        List<String> valueList = new ArrayList<>();

        switch (memoryType){
            case ConstantModelValue.CONFIG_SEAR_MEMORY_MODE0:
                return ConstantModelValue.CONFIG_RESET_RESULT_NOCONFIG;
            case ConstantModelValue.CONFIG_SEAR_MEMORY_MODE1:
                descList.add("记忆位置1");
                descList.add("记忆位置2");
                descList.add("记忆位置3");
                descList.add("记忆位置4");
                descList.add("休闲位置");
                descList.add("睡眠位置");

                valueList.add("1");
                valueList.add("2");
                valueList.add("3");
                valueList.add("4");
                valueList.add("5");
                valueList.add("6");

                singleOperation.getListArgsMap().put(ConstantModelValue.VIEW_DESC_TEXT_LIST,descList);
                singleOperation.getListArgsMap().put(ConstantModelValue.DATA_DESC_LIST,valueList);

                return ConstantModelValue.CONFIG_RESET_RESULT_RESET;
            case ConstantModelValue.CONFIG_SEAR_MEMORY_MODE2:
                descList.add("记忆位置1");
                descList.add("记忆位置2");
                descList.add("记忆位置3");
                descList.add("休闲位置");
                descList.add("睡眠位置");

                valueList.add("1");
                valueList.add("2");
                valueList.add("3");
                valueList.add("5");
                valueList.add("6");

                singleOperation.getListArgsMap().put(ConstantModelValue.VIEW_DESC_TEXT_LIST,descList);
                singleOperation.getListArgsMap().put(ConstantModelValue.DATA_DESC_LIST,valueList);

                return ConstantModelValue.CONFIG_RESET_RESULT_RESET;

            default:
                return ConstantModelValue.CONFIG_RESET_RESULT_NOCHANGE;

        }


    }

    @Override
    public boolean dispatchSingleActionOperation(SettingOperation settingOperation) {
        return CommonActionModel.getInstance().dispatchSingleActionOperation(settingOperation);
    }
}
