package com.dfl.smartscene.ccs.navi;

import java.io.Serializable;

/**
 * 场景模式状态条件的数据实体
 * <AUTHOR>
 * @date 2022-9-24
 */
public class ConditionBean implements Serializable {
    /**
     * POI类别
     */
    private @PoiCategory
    int poiType;
    /**
     * 驾驶行为类别
     */
    private @DriveActionCategory
    int driveCategory;
    /**
     * 具体POI信息
     */
    private POI poi;

    /**
     * 道路状态信息
     */
    private int trafficStatus;

    public @PoiCategory
    int getPoiType() {
        return poiType;
    }

    public void setPoiType(@PoiCategory int poiType) {
        this.poiType = poiType;
    }

    public POI getPoi() {
        return poi;
    }

    public void setPoi(POI poi) {
        this.poi = poi;
    }

    public int getDriveCategory() {
        return driveCategory;
    }

    public void setDriveCategory(int driveCategory) {
        this.driveCategory = driveCategory;
    }

    public int getTrafficStatus() {
        return trafficStatus;
    }

    public void setTrafficStatus(int trafficStatus) {
        this.trafficStatus = trafficStatus;
    }

    /**
     * 收藏点类型的枚举<P/>
     * 注意家和公司类型的情景模式的执行需要根据该类型作为判断依据，即该类型的坐标变更后仍应执行相应逻辑<P/>
     * 普通收藏点的执行只需要根据该类型的坐标为判断依据，即存在该坐标的收藏点应执行相应逻辑<P/>
     */
    public @interface PoiCategory{
        /**
         * 家
         */
        int HOME = 1;
        /**
         * 公司
         */
        int COMPANY = 2;
        /**
         * 其他
         */
        int OTHER = 3;
    }

    /**
     * 驾驶事件的类型<P/>
     * 情景模式的判断条件分为离开和靠近2种模式，故设定相应模式
     */
    public @interface DriveActionCategory{
        /**
         * 离开
         */
        int LEAVE = 1;
        /**
         * 靠近
         */
        int ARRIVE = 2;
    }

    /**
     * 查询id对应的情景模式的满足情况
     */
    public @interface SceneMeetType{
        /**
         * 满足
         */
        int VALID = 1;
        /**
         * 不满足
         */
        int INVALID = 2;
    }
}

