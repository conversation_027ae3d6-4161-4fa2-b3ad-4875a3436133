package com.dfl.smartscene.ccs.viewmodel.base;

import androidx.lifecycle.ViewModel;

import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR>
 * @date ：2023/3/6 16:18
 * @description ：
 */
public class BaseViewModel extends ViewModel {
    // 防止内存泄漏
    private CompositeDisposable mCompositeDisposable = new CompositeDisposable();

    protected void addDisposable(Disposable disposable){
        mCompositeDisposable.add(disposable);
    }
    @Override
    protected void onCleared() {
        super.onCleared();
        mCompositeDisposable.dispose();
    }

}
