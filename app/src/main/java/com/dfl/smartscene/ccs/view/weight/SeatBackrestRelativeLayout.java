package com.dfl.smartscene.ccs.view.weight;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.RelativeLayout;

import com.dfl.smartscene.R;
import com.iauto.uibase.utils.MLog;
import com.iauto.uicontrol.ButtonBase;
import com.iauto.uicontrol.ImageBase;

import java.util.Timer;
import java.util.TimerTask;

/**
 * The SeatAdjust control class of seat
 */
public class SeatBackrestRelativeLayout extends RelativeLayout {
    private static final String TAG = "SeatBackrestAbsLayoutLayout";

    private static final String DRIVER = "driver";
    private static final String COPILOT = "copilot";

    private Handler mHandler = null;
    private Timer mLongPressTimer = null; // 200ms

    private RelativeLayout mSeatDriverLayout = null;
    private ImageBase mSeatBackrestImg;
    private ButtonBase mSeatBackrestFront;
    private ButtonBase mSeatBackrestRear;
    private ButtonBase mSeatBackrestUp;
    private ButtonBase mSeatBackrestDown;

    private int mLongPressBtnViewId = 0;

    String strDriverOrCopilot = DRIVER;

    SeatBackrestFrontRearPressListener seatBackrestFrontRearPressListener;
    SeatBackrestUpDownPressListener seatBackrestUpDownPressListener;


    /**
     * SeatBackrestRelativeLayout object constructor
     *
     * @param attrs   control's attrs
     * @param context view's context
     */
    public SeatBackrestRelativeLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        LayoutInflater.from(context).inflate(R.layout.layout_seat_backrest_relative, this, true);
        mHandler = new Handler(Looper.getMainLooper());

        strDriverOrCopilot = (String) this.getTag();

        RelativeLayout seatLayout;
        if (strDriverOrCopilot.equals(DRIVER)) {
            seatLayout = findViewById(R.id.seat_driver_backrest_layout);
            mSeatDriverLayout = seatLayout;

            mSeatBackrestImg = findViewById(R.id.image_driver_seat_backrest_part);
            mSeatBackrestImg.setVisibility(View.INVISIBLE);

            mSeatBackrestFront = findViewById(R.id.button_driver_seat_backrest_front);
            mSeatBackrestRear = findViewById(R.id.button_driver_seat_backrest_rear);
            mSeatBackrestUp = findViewById(R.id.button_driver_seat_backrest_up);
            mSeatBackrestDown = findViewById(R.id.button_driver_seat_backrest_down);

        } else {
            seatLayout = findViewById(R.id.seat_copilot_backrest_layout);

            mSeatBackrestImg = findViewById(R.id.image_copilot_seat_backrest_part);
            mSeatBackrestImg.setVisibility(View.INVISIBLE);

            mSeatBackrestFront = findViewById(R.id.button_copilot_seat_backrest_front);
            mSeatBackrestRear = findViewById(R.id.button_copilot_seat_backrest_rear);
            mSeatBackrestUp = findViewById(R.id.button_copilot_seat_backrest_up);
            mSeatBackrestDown = findViewById(R.id.button_copilot_seat_backrest_down);

        }
        seatLayout.setVisibility(View.VISIBLE);

        // button that backrest move to front
        mSeatBackrestFront.setOnBtnPressListener(new ButtonBase.OnBtnPressListener() {
            @Override
            public void onBtnPress(View view) {
                MLog.d(TAG,"onBtnPress");
                makeSeatAnimation(true, view.getId());
                buttonTouchUp(view.getId());
            }
        });
        mSeatBackrestFront.setOnBtnLongPressListener(new ButtonBase.OnBtnLongPressListener() {
            @Override
            public void onBtnLongPress(View view) {
                MLog.d(TAG,"onBtnLongPress");
                mLongPressBtnViewId = view.getId();
                startLongPressTimer();
            }
        });
        mSeatBackrestFront.setOnBtnReleaseListener(new ButtonBase.OnBtnReleaseListener() {
            @Override
            public void onBtnRelease(View view) {
                MLog.d(TAG,"onBtnRelease");
                makeSeatAnimation(false, view.getId());
                stopLongPressTimer();
                mLongPressBtnViewId = 0;
            }
        });
        mSeatBackrestFront.setOnBtnClickListener(new ButtonBase.OnBtnClickListener() {
            @Override
            public void onBtnClick(View view) {
                MLog.d(TAG,"onBtnClick");
                makeSeatAnimation(false, view.getId());
            }
        });

        // button that backrest move to rear
        mSeatBackrestRear.setOnBtnPressListener(new ButtonBase.OnBtnPressListener() {
            @Override
            public void onBtnPress(View view) {
                makeSeatAnimation(true, view.getId());
                buttonTouchUp(view.getId());
            }
        });
        mSeatBackrestRear.setOnBtnLongPressListener(new ButtonBase.OnBtnLongPressListener() {
            @Override
            public void onBtnLongPress(View view) {
                mLongPressBtnViewId = view.getId();
                startLongPressTimer();
            }
        });
        mSeatBackrestRear.setOnBtnReleaseListener(new ButtonBase.OnBtnReleaseListener() {
            @Override
            public void onBtnRelease(View view) {
                makeSeatAnimation(false, view.getId());
                stopLongPressTimer();
                mLongPressBtnViewId = 0;
            }
        });
        mSeatBackrestRear.setOnBtnClickListener(new ButtonBase.OnBtnClickListener() {
            @Override
            public void onBtnClick(View view) {
                makeSeatAnimation(false, view.getId());
            }
        });

        // button that backrest move to up
        mSeatBackrestUp.setOnBtnPressListener(new ButtonBase.OnBtnPressListener() {
            @Override
            public void onBtnPress(View view) {
                makeSeatAnimation(true, view.getId());
                buttonTouchUp(view.getId());
            }
        });
        mSeatBackrestUp.setOnBtnLongPressListener(new ButtonBase.OnBtnLongPressListener() {
            @Override
            public void onBtnLongPress(View view) {
                mLongPressBtnViewId = view.getId();
                startLongPressTimer();
            }
        });
        mSeatBackrestUp.setOnBtnReleaseListener(new ButtonBase.OnBtnReleaseListener() {
            @Override
            public void onBtnRelease(View view) {
                makeSeatAnimation(false, view.getId());
                stopLongPressTimer();
                mLongPressBtnViewId = 0;
            }
        });
        mSeatBackrestUp.setOnBtnClickListener(new ButtonBase.OnBtnClickListener() {
            @Override
            public void onBtnClick(View view) {
                makeSeatAnimation(false, view.getId());
            }
        });

        // button that backrest move to down
        mSeatBackrestDown.setOnBtnPressListener(new ButtonBase.OnBtnPressListener() {
            @Override
            public void onBtnPress(View view) {
                makeSeatAnimation(true, view.getId());
                buttonTouchUp(view.getId());
            }
        });
        mSeatBackrestDown.setOnBtnLongPressListener(new ButtonBase.OnBtnLongPressListener() {
            @Override
            public void onBtnLongPress(View view) {
                mLongPressBtnViewId = view.getId();
                startLongPressTimer();
            }
        });
        mSeatBackrestDown.setOnBtnReleaseListener(new ButtonBase.OnBtnReleaseListener() {
            @Override
            public void onBtnRelease(View view) {
                makeSeatAnimation(false, view.getId());
                stopLongPressTimer();
                mLongPressBtnViewId = 0;
            }
        });
        mSeatBackrestDown.setOnBtnClickListener(new ButtonBase.OnBtnClickListener() {
            @Override
            public void onBtnClick(View view) {
                makeSeatAnimation(false, view.getId());
            }
        });
    }

    private void makeSeatAnimation(boolean isBtnDown, int viewId) {
        MLog.d(TAG, "makeSeatAnimation isBtnDown = " + isBtnDown);
        MLog.d(TAG, "makeSeatAnimation viewId = " + viewId);

        if (isBtnDown) {
            mSeatBackrestImg.setVisibility(View.VISIBLE);

            mSeatBackrestFront.setEnabled(false);
            mSeatBackrestRear.setEnabled(false);
            mSeatBackrestUp.setEnabled(false);
            mSeatBackrestDown.setEnabled(false);

            ButtonBase touchButton = findViewById(viewId);
            touchButton.setEnabled(true);
        } else {
            mSeatBackrestImg.setVisibility(View.INVISIBLE);

            mSeatBackrestFront.setEnabled(true);
            mSeatBackrestRear.setEnabled(true);
            mSeatBackrestUp.setEnabled(true);
            mSeatBackrestDown.setEnabled(true);
        }
    }

    public void updateDriverState(boolean state) {
        MLog.d(TAG, "updateDriverState state = " + state);
        if (!strDriverOrCopilot.equals(DRIVER)) {
            MLog.d(TAG, "updateDriverState is not driver ");
            return;
        }
        if (mSeatDriverLayout == null) {
            MLog.d(TAG, "updateDriverState mSeatDriverLayout is null ");
            return;
        }

        if (!state) {
            // reset position
            int buttonPressedViewId = 0;
            int count = mSeatDriverLayout.getChildCount();
            for (int iLoop = 0; iLoop < count; ++iLoop) {
                View child = mSeatDriverLayout.getChildAt(iLoop);
                if (child instanceof ButtonBase) {
                    if (child.isPressed()) {
                        buttonPressedViewId = child.getId();
                        break;
                    }
                }
            }
            MLog.d(TAG, "updateDriverState buttonPressedViewId = " + buttonPressedViewId);
            if (0 != buttonPressedViewId) {
                makeSeatAnimation(false, buttonPressedViewId);
                stopLongPressTimer();
                mLongPressBtnViewId = 0;
            }
        }

        // update state
        mSeatBackrestFront.setEnabled(state);
        mSeatBackrestRear.setEnabled(state);
        mSeatBackrestUp.setEnabled(state);
        mSeatBackrestDown.setEnabled(state);
    }

    private ButtonBase getLongPressView(int viewId) {
        if (mSeatBackrestFront.getId() == viewId) {
            return mSeatBackrestFront;
        } else if (mSeatBackrestRear.getId() == viewId) {
            return mSeatBackrestRear;
        } else if (mSeatBackrestUp.getId() == viewId) {
            return mSeatBackrestUp;
        } else if (mSeatBackrestDown.getId() == viewId) {
            return mSeatBackrestDown;
        } else {
            return null;
        }
    }

    private void buttonTouchUp(int viewId) {
        MLog.d(TAG, "buttonTouchUp viewId= " + viewId);
        if (mSeatBackrestFront.getId() == viewId) {
            int frontRear = 1; //front is 1,rear is 2;do nothing is 0
            if (null != seatBackrestFrontRearPressListener) {
                seatBackrestFrontRearPressListener.onTouchUp(frontRear);
            }
        } else if (mSeatBackrestRear.getId() == viewId) {
            int frontRear = 2; //front is 1,rear is 2;do nothing is 0
            if (null != seatBackrestFrontRearPressListener) {
                seatBackrestFrontRearPressListener.onTouchUp(frontRear);
            }
        } else if (mSeatBackrestUp.getId() == viewId) {
            int upDown = 1; //up is 1;down is 2;do nothing is 0
            if (null != seatBackrestUpDownPressListener) {
                seatBackrestUpDownPressListener.onTouchUp(upDown);
            }
        } else if (mSeatBackrestDown.getId() == viewId) {
            int upDown = 2; //up is 1;down is 2;do nothing is 0
            if (null != seatBackrestUpDownPressListener) {
                seatBackrestUpDownPressListener.onTouchUp(upDown);
            }
        } else {
            MLog.d(TAG, "buttonTouchUp viewId is invalid");
        }
    }

    private void startLongPressTimer() {
        MLog.d(TAG, "startStartingTimer");
        if (mLongPressTimer != null) {
            mLongPressTimer.cancel();
            mLongPressTimer = null;
        }

        mLongPressTimer = new Timer();
        mLongPressTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                ButtonBase viewById = getLongPressView(mLongPressBtnViewId);
                if (null != viewById) {
                    if (viewById.isPressed()) {
                        mHandler.post(() -> buttonTouchUp(mLongPressBtnViewId));
                    } else {
                        // 手指长按时，移出到了有效区域外，无法接收暂停回调，该位置就是为了规避一直执行timer的操作
                        MLog.d(TAG, "Touch moves out of the valid area.");
                        mHandler.post(() -> makeSeatAnimation(false, mLongPressBtnViewId));
                        stopLongPressTimer();
                    }
                }
            }
        }, 0, 200); // 200ms timer
    }

    private void stopLongPressTimer() {
        MLog.d(TAG, "stopLongPressTimer");
        if (mLongPressTimer != null) {
            mLongPressTimer.cancel();
            mLongPressTimer = null;
        }
    }


    /**
     * set seat backrest's Touch up listener
     *
     * @param listen control's attrs
     */
    public void setSeatBackrestFrontRearPressListen(SeatBackrestFrontRearPressListener listen) {
        seatBackrestFrontRearPressListener = listen;
    }

    /**
     * SeatFrontRearPressListener interface
     */
    public interface SeatBackrestFrontRearPressListener {
        void onTouchUp(int frontRear);
    }

    /**
     * set seat backrest's Touch up listener
     *
     * @param listen control's attrs
     */
    public void setSeatBackrestUpDownPressListen(SeatBackrestUpDownPressListener listen) {
        seatBackrestUpDownPressListener = listen;
    }

    /**
     * SeatFrontRearPressListener interface
     */
    public interface SeatBackrestUpDownPressListener {
        void onTouchUp(int upDown);
    }
}
