package com.dfl.smartscene.ccs.view.operationview;

import android.view.ViewGroup;
import android.widget.FrameLayout;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.util.DataTypeFormatUtil;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseView;
import com.dfl.smartscene.ccs.view.weight.NumberCompareView;

import java.util.ArrayList;
import java.util.List;

public class NumberCompareOperation implements OperationBaseView {

    private final SingleOperation mSingleOperation;
    private final NumberCompareView mNumberCompareView;

    @Override
    public List<String> extractArgs() {
        ArrayList<String> arrayList = new ArrayList<>(2);
        arrayList.add("大于".equals(mNumberCompareView.getValue1()) ? ">" : "<");
        arrayList.add(mNumberCompareView.getValue2());
        return arrayList;
    }

    @Override
    public String extractDesc() {
        String desc = mSingleOperation.getArg(ConstantModelValue.OUTPUT_DESC_TEXT);
        desc = desc.replaceFirst("&", mNumberCompareView.getValue1());
        desc = desc.replaceFirst("&", mNumberCompareView.getValue2());
        return desc;
    }

    public NumberCompareOperation(SingleOperation singleOperation, ViewGroup parent) {
        mSingleOperation = singleOperation;
        mNumberCompareView = new NumberCompareView(parent.getContext());

        String unitStr = singleOperation.getArg(ConstantModelValue.VIEW_DESC_TEXT_END);
        String defaultStr = singleOperation.getArg(ConstantModelValue.VIEW_DEFAULT_POS);
        String minStr = singleOperation.getArg(ConstantModelValue.VIEW_DESC_RANGE_LOW);
        String maxStr = singleOperation.getArg(ConstantModelValue.VIEW_DESC_RANGE_HIGH);
        String stepStr = singleOperation.getArg(ConstantModelValue.VIEW_DESC_RANGE_STEP);
        String valueTypeStr = singleOperation.getArg(ConstantModelValue.VIEW_DESC_RANGE_TYPE);
        LogUtil.d("0221", "NumberCompareOperation: "+defaultStr);
        int compareDefaultValue = 0;
        boolean haveDefaultValue = false;
        String originalDefaultStr = defaultStr;
        if ("VIEW_TYPE_NUM_COMPARE".equals(singleOperation.getViewType()) && "OID_STA_DRIVE_RANGE_POWER".equals(singleOperation.getOperationId())){
            defaultStr = "20";
        }
        if ("VIEW_TYPE_NUM_COMPARE".equals(singleOperation.getViewType()) && "OID_STA_DRIVE_ELECTRICITY".equals(singleOperation.getOperationId())){
            defaultStr = "30";
        }

        if(originalDefaultStr.contains("@")){
            haveDefaultValue = true;
            String[] split = originalDefaultStr.split("@");
            if(split[0].equals("<")){
                compareDefaultValue = 1;
            }
            defaultStr = split[1];
        }

        mNumberCompareView.setValue(new String[]{"大于", "小于"},compareDefaultValue);
        if(haveDefaultValue){
            if ("int".equals(valueTypeStr)) {
                mNumberCompareView.setValue2WithDefaultValue(valueTypeStr, DataTypeFormatUtil.string2Int(minStr, 0), DataTypeFormatUtil.string2Int(maxStr, 0),
                        DataTypeFormatUtil.string2Int(stepStr, 0), defaultStr, unitStr);
            } else {
                mNumberCompareView.setValue2WithDefaultValue(valueTypeStr, DataTypeFormatUtil.string2Float(minStr, 0), DataTypeFormatUtil.string2Float(maxStr, 0),
                        DataTypeFormatUtil.string2Float(stepStr, 0), defaultStr, unitStr);
            }
        }else {
            if ("int".equals(valueTypeStr)) {
                mNumberCompareView.setValue2(valueTypeStr, DataTypeFormatUtil.string2Int(minStr, 0), DataTypeFormatUtil.string2Int(maxStr, 0),
                        DataTypeFormatUtil.string2Int(stepStr, 0), DataTypeFormatUtil.string2Int(defaultStr, 0), unitStr);
            } else {
                mNumberCompareView.setValue2(valueTypeStr, DataTypeFormatUtil.string2Float(minStr, 0), DataTypeFormatUtil.string2Float(maxStr, 0),
                        DataTypeFormatUtil.string2Float(stepStr, 0), DataTypeFormatUtil.string2Int(defaultStr, 0), unitStr);
            }
        }

        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        parent.addView(mNumberCompareView, layoutParams);
    }

    @Override
    public void onConfigurationChanged() {

    }

}
