package com.dfl.smartscene.ccs.core;


import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SettingOperation;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2023/3/3 9:41
 * @description ：
 */
public abstract class ConditionBaseModelImpl implements ConditionBaseModel{

    public ConditionBaseModelImpl(){
        for(String operationId : getConditionIds()){
            conditionMap.put(operationId , 0);
        }
    }

    protected void onCarDataChange(String operationId , String value){
        Integer num = conditionMap.get(operationId);
        if(num != null && num > 0){
            LibraryModel.getInstance().onCarDataChange(operationId, value);
        }
    }

    @Override
    public void registerSceneCondition(SceneBean sceneBean, SettingOperation condition) {
        Integer num = conditionMap.get(condition.getOperationId());
        if(num != null){
            conditionMap.put(condition.getOperationId() , num + 1);
        }
    }

    @Override
    public void unRegisterSceneCondition(SceneBean sceneBean, SettingOperation condition) {
        Integer num = conditionMap.get(condition.getOperationId());
        if(num != null){
            conditionMap.put(condition.getOperationId() , num - 1);
        }

    }

    protected abstract String[] getConditionIds();

    /**
     * key : operationId
     * map : num of registerScene
     */
    private Map<String , Integer> conditionMap = new HashMap<>();
}
