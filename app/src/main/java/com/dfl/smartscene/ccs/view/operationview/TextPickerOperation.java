package com.dfl.smartscene.ccs.view.operationview;

import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.NumberPicker;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.util.DataTypeFormatUtil;
import com.dfl.smartscene.ccs.view.adapter.OpeRecyclerAdapter;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseView;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2024/12/6
 * @description ：文本picker操作
 */
public class TextPickerOperation implements OperationBaseView {
    private static final String TAG = "TextPickerOperation";

    private final SingleOperation mSingleOperation;

    private final NumberPicker mCommonPicker;
    private List<OpeRecyclerAdapter.DataBean> dataBeanList;


    @Override
    public List<String> extractArgs() {
        if(dataBeanList != null && !dataBeanList.isEmpty()){
            return Arrays.asList(dataBeanList.get(mCommonPicker.getValue()).getValue());
        }else {
            ArrayList<String> values = new ArrayList<>(1);
            return values;
        }
    }

    @Override
    public String extractDesc() {
        String desc = mSingleOperation.getArg(ConstantModelValue.OUTPUT_DESC_TEXT);
        if(desc !=null){
            String[] displayedValues = mCommonPicker.getDisplayedValues();
            int value = mCommonPicker.getValue();
            desc = desc.replaceFirst("&",displayedValues[value]);
        }
        return desc;
    }

    public TextPickerOperation(SingleOperation singleOperation, ViewGroup parent) {
        mSingleOperation = singleOperation;
        LayoutInflater.from(parent.getContext()).inflate(R.layout.item_operation_common_picker, parent, true);

        mCommonPicker = parent.findViewById(R.id.common_picker_item_operation_value);

        dataBeanList = new ArrayList<>();
        List<String> descs = singleOperation.getListArg(ConstantModelValue.VIEW_DESC_TEXT_LIST);
        List<String> values = singleOperation.getListArg(ConstantModelValue.DATA_DESC_LIST);
        String defaultValueStr = singleOperation.getArg(ConstantModelValue.VIEW_DEFAULT_POS);
        int defaultValue = 1;
        defaultValue = DataTypeFormatUtil.string2Int(defaultValueStr, 1);
        LogUtil.d(TAG, "defaultPos-----:" + defaultValue);

        if (null != descs && null != values) {
            for (int i = 0; i < descs.size(); i++) {
                LogUtil.d(TAG, "values.get(i):" + values.get(i));
                LogUtil.d(TAG, "descs.get(i):" + descs.get(i));
                dataBeanList.add(new OpeRecyclerAdapter.DataBean(descs.get(i), values.get(i)));
                if (defaultValueStr != null && defaultValueStr.equals(values.get(i))) {
                    defaultValue = i;
                }
            }
        }
        if (descs == null || descs.isEmpty()) {
            return;
        }
        mCommonPicker.setMinValue(0);
        mCommonPicker.setMaxValue(descs.size() - 1);
        mCommonPicker.setDisplayedValues(descs.toArray(new String[0]));
        mCommonPicker.setValue(defaultValue);
    }


    @Override
    public void onConfigurationChanged() {

    }

}
