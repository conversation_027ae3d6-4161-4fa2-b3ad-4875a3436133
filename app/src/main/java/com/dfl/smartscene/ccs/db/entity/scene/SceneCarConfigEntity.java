package com.dfl.smartscene.ccs.db.entity.scene;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;

@Entity(tableName = "scene_car_config")
public class SceneCarConfigEntity {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "pId", typeAffinity = ColumnInfo.INTEGER)
    public int pId;

    @ColumnInfo(name = "car_type", typeAffinity = ColumnInfo.TEXT)
    public String car_type;


    @ColumnInfo(name = "category_id", typeAffinity = ColumnInfo.TEXT)
    public String category_id;


    @ColumnInfo(name = "scene_id", typeAffinity = ColumnInfo.TEXT)
    public String scene_id;

    @Ignore
    public SceneCarConfigEntity() {

    }

    public SceneCarConfigEntity(int pId, String car_type, String category_id, String scene_id) {
        this.pId = pId;
        this.car_type = car_type;
        this.category_id = category_id;
        this.scene_id = scene_id;
    }

    @NonNull
    @Override
    public String toString() {
        return "SceneCarConfigEntity{" +
                "pId=" + pId +
                ", car_type='" + car_type + '\'' +
                ", category_id='" + category_id + '\'' +
                ", scene_id='" + scene_id + '\'' +
                '}';
    }
}
