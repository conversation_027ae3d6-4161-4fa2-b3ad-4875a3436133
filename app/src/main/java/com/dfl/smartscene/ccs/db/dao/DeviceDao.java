package com.dfl.smartscene.ccs.db.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;


import com.dfl.smartscene.ccs.db.entity.DeviceEntity;

import java.util.List;

import io.reactivex.Observable;

@Dao
public interface DeviceDao {

    @Insert
    void insertStudent(DeviceEntity deviceEntity);

    @Delete
    void deleteStudent(DeviceEntity deviceEntity);

    @Update
    void updateStudent(DeviceEntity deviceEntity);

    @Query("SELECT * FROM device")
    Observable<List<DeviceEntity>> queryAll();
    /**
     * 根据用户id数组查询到一批用户
     */
    @Query("SELECT * FROM device WHERE id in (:ids)")
    Observable<List<DeviceEntity>> queryByIds(String[] ids);

}
