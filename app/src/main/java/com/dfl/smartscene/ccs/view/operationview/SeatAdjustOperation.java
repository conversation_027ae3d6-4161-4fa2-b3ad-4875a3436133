package com.dfl.smartscene.ccs.view.operationview;

import android.os.RemoteException;
import android.util.ArrayMap;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.dfl.api.base.NullServiceException;
import com.dfl.api.vehicle.seat.ISeat;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.model.manager.CustomApiManager;
import com.dfl.smartscene.ccs.util.CarConfigUtil;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseView;
import com.dfl.smartscene.ccs.view.weight.CustomRadioGroup;
import com.dfl.smartscene.ccs.view.weight.SeatAdjustCopilotLayout;
import com.dfl.smartscene.ccs.view.weight.SeatAdjustDriverLayout;
import com.dfl.smartscene.ccs.view.weight.SeatBackrestRelativeLayout;
import com.iauto.uibase.utils.MLog;

import java.util.ArrayList;
import java.util.List;

public class SeatAdjustOperation implements OperationBaseView {

    private static final String TAG = SeatAdjustOperation.class.getSimpleName();

    //    private SeatAdjustRelativeLayout mSeatAdjustRelativeLayout;
    private SeatBackrestRelativeLayout mSeatBackrestRelativeLayout;
    private final View mSeatAdjustLayout;

    private final SingleOperation mSingleOperation;

    private final boolean mIsDriver;

    private final ArrayMap<String, Integer> mValueMap = new ArrayMap<>(6);

    @Override
    public List<String> extractArgs() {
        // 前后,上下,靠背,坐垫,腰托上下,腰托前后
        List<SingleOperation> subOperations = mSingleOperation.getSubOperations();
        if (null != subOperations && !subOperations.isEmpty()) {
            ArrayList<String> values = new ArrayList<>(subOperations.size());

            for (SingleOperation subOperation : subOperations) {
                String operationId = subOperation.getOperationId();
                Integer value = mValueMap.get(operationId);
                if (null != value) {
                    values.add(String.valueOf(value));
                }
            }
            return values;
        }
        return new ArrayList<>(0);
    }

    @Override
    public String extractDesc() {
        return mSingleOperation.getArg(ConstantModelValue.OUTPUT_DESC_TEXT);
    }

    //TODO return 值需后续优化
    private int getValue(String operationId, boolean isDriver) {
        int index = 0;
        if (ConstantModelValue.OID_ACT_SEAT_DRIVER_ADJUST_FRONT_REAR.equals(operationId)) {
            index = 1;
            return 30;
        } else if (ConstantModelValue.OID_ACT_SEAT_DRIVER_ADJUST_UP_DOWN.equals(operationId)) {
            index = 2;
            return 7;
        } else if (ConstantModelValue.OID_ACT_SEAT_DRIVER_ADJUST_LAZYBACK.equals(operationId)) {
            return 30;
//            index = 3;
        }
        ISeat iSeat = CustomApiManager.getInstance().getISeat();
        if (null != iSeat) {
            try {
                return iSeat.getSeatMovementPosition(isDriver ? 1 : 2, index);
            } catch (NullServiceException | RemoteException e) {
                e.printStackTrace();
            }
        }
        return 0;
    }

    // OID_ACT_SEAT_DRIVER_ADJUST_FRONT_REAR 前后
    // OID_ACT_SEAT_DRIVER_ADJUST_UP_DOWN 上下
    // OID_ACT_SEAT_DRIVER_ADJUST_LAZYBACK 靠背
    // OID_ACT_SEAT_DRIVER_ADJUST_CUSHION  坐垫
    // OID_ACT_SEAT_DRIVER_ADJUST_LUMBAR_LEFT_RIGHT 腰脱左右
    // OID_ACT_SEAT_DRIVER_ADJUST_LUMBAR_FRONT_REAR 腰脱前后

    public SeatAdjustOperation(SingleOperation singleOperation, ViewGroup parent) {
        mSingleOperation = singleOperation;


        LayoutInflater.from(parent.getContext()).inflate(R.layout.item_operation_seat_adjust_layout, parent, true);
        TextView driverHint = parent.findViewById(R.id.textview_item_operation_seat_adjust_driver_hint);
        if (ConstantModelValue.OID_ACT_SEAT_PASSAGER_ADJUST.equals(singleOperation.getOperationId())) {
            mIsDriver = false;
            mSeatAdjustLayout = parent.findViewById(R.id.seat_adjust_copilot_layout_item_operation_seat_adjust_layout_copilot);
            mSeatBackrestRelativeLayout = parent.findViewById(R.id.seat_back_rest_layout_item_operation_seat_adjust_layout_copilot);
            driverHint.setVisibility(View.GONE);
        } else {
            mIsDriver = true;
            mSeatAdjustLayout = parent.findViewById(R.id.seat_adjust_driver_layout_item_operation_seat_adjust_layout_driver);
            mSeatBackrestRelativeLayout = parent.findViewById(R.id.seat_back_rest_layout_item_operation_seat_adjust_layout_driver);
            driverHint.setVisibility(View.VISIBLE);
        }
        mSeatAdjustLayout.setVisibility(View.VISIBLE);

        //TODO 概念仓项目结束后删除
        if(CarConfigUtil.isCCU()){
            ArrayList<String> strings = new ArrayList<>(2);
            strings.add("位置");
            strings.add("腰托");
            CustomRadioGroup mCustomRadioGroup = parent.findViewById(R.id.custom_radio_group_item_operation_seat_adjust_layout);
            mCustomRadioGroup.setTextList(strings, ConstantModelValue.VIEW_TYPE_RADIO_TEXT_GROUP);
            mCustomRadioGroup.setAlpha(0.5f);
            mCustomRadioGroup.setVisibility(View.VISIBLE);
            mCustomRadioGroup.setEnabled(false);
            TextView textView = parent.findViewById(R.id.textview_item_operation_seat_adjust_driver_title);
            textView.setVisibility(View.VISIBLE);
            textView.setAlpha(0.5f);
        }
        // 获取子操作列表
        List<SingleOperation> subOperations = mSingleOperation.getSubOperations();
        if (null != subOperations && !subOperations.isEmpty()) {
            for (SingleOperation subOperation : subOperations) {
                String operationId = subOperation.getOperationId();
                if (ConstantModelValue.OID_ACT_SEAT_DRIVER_ADJUST_FRONT_REAR.equals(operationId)) {
                    mSeatAdjustLayout.findViewById(R.id.button_layout_seat_adjust_driver_whole_right).setVisibility(View.VISIBLE);
                    mSeatAdjustLayout.findViewById(R.id.button_layout_seat_adjust_driver_whole_left).setVisibility(View.VISIBLE);

                    int value = getValue(operationId, mIsDriver);
                    mValueMap.put(operationId, value);

                } else if (ConstantModelValue.OID_ACT_SEAT_DRIVER_ADJUST_UP_DOWN.equals(operationId)) {
                    mSeatAdjustLayout.findViewById(R.id.button_layout_seat_adjust_driver_whole_up).setVisibility(View.VISIBLE);
                    mSeatAdjustLayout.findViewById(R.id.button_layout_seat_adjust_driver_whole_down).setVisibility(View.VISIBLE);
                    int value = getValue(operationId, mIsDriver);
                    mValueMap.put(operationId, value);
                } else if (ConstantModelValue.OID_ACT_SEAT_DRIVER_ADJUST_LAZYBACK.equals(operationId)) {
                    mSeatAdjustLayout.findViewById(R.id.button_layout_seat_adjust_driver_back_front).setVisibility(View.VISIBLE);
                    mSeatAdjustLayout.findViewById(R.id.button_layout_seat_adjust_driver_back_rear).setVisibility(View.VISIBLE);
                    int value = getValue(operationId, mIsDriver);
                    mValueMap.put(operationId, value);
                } else if (ConstantModelValue.OID_ACT_SEAT_DRIVER_ADJUST_LUMBAR_LEFT_RIGHT.equals(operationId) || ConstantModelValue.OID_ACT_SEAT_DRIVER_ADJUST_LUMBAR_FRONT_REAR.equals(operationId)) {
                    ArrayList<String> strings = new ArrayList<>(2);
                    strings.add("位置");
                    strings.add("腰托");
                    CustomRadioGroup mCustomRadioGroup = parent.findViewById(R.id.custom_radio_group_item_operation_seat_adjust_layout);
                    mCustomRadioGroup.setVisibility(View.VISIBLE);
                    mCustomRadioGroup.setTextList(strings, ConstantModelValue.VIEW_TYPE_RADIO_TEXT_GROUP);
                    mCustomRadioGroup.setCheckedCallBack((position, name) -> {
                        MLog.e(TAG,"name1:"+name);
                        if (position == 0) {
                            mSeatAdjustLayout.setVisibility(View.VISIBLE);
                            mSeatBackrestRelativeLayout.setVisibility(View.GONE);
                        } else {
                            mSeatAdjustLayout.setVisibility(View.GONE);
                            mSeatBackrestRelativeLayout.setVisibility(View.VISIBLE);
                        }
                    });

                    int value = getValue(operationId, mIsDriver);
                    mValueMap.put(operationId, value);
                }
            }
        }

        addSeatAdjustListener();
    }

    private void addSeatAdjustListener() {

        if (null == mSeatAdjustLayout) {
            return;
        }

        if (mSeatAdjustLayout instanceof SeatAdjustDriverLayout) {
            ((SeatAdjustDriverLayout) mSeatAdjustLayout).setISeatDriverTouchListener(new SeatAdjustDriverLayout.ISeatDriverTouchListener() {
                @Override
                public void onBackFrontRear(int frontRear) {
                    Integer value = mValueMap.get(ConstantModelValue.OID_ACT_SEAT_DRIVER_ADJUST_LAZYBACK);
                    if (null != value) {
                        value += (frontRear == 1 ? 1 : -1);
                        mValueMap.put(ConstantModelValue.OID_ACT_SEAT_DRIVER_ADJUST_LAZYBACK, value);
                    }
                }

                @Override
                public void onWholeFrontRear(int frontRear) {
                    Integer value = mValueMap.get(ConstantModelValue.OID_ACT_SEAT_DRIVER_ADJUST_FRONT_REAR);
                    if (null != value) {
                        value += (frontRear == 1 ? 1 : -1);
                        mValueMap.put(ConstantModelValue.OID_ACT_SEAT_DRIVER_ADJUST_FRONT_REAR, value);
                    }
                }

                @Override
                public void onWholeUpDown(int upDown) {
                    Integer value = mValueMap.get(ConstantModelValue.OID_ACT_SEAT_DRIVER_ADJUST_UP_DOWN);
                    if (null != value) {
                        value += (upDown == 1 ? 1 : -1);
                        mValueMap.put(ConstantModelValue.OID_ACT_SEAT_DRIVER_ADJUST_UP_DOWN, value);
                    }
                }
            });
        }

        if (mSeatAdjustLayout instanceof SeatAdjustCopilotLayout) {
            ((SeatAdjustCopilotLayout) mSeatAdjustLayout).setISeatCopilotTouchListener(new SeatAdjustCopilotLayout.ISeatCopilotTouchListener() {
                @Override
                public void onBackFrontRear(int frontRear) {
                    Integer value = mValueMap.get(ConstantModelValue.OID_ACT_SEAT_DRIVER_ADJUST_LAZYBACK);
                    if (null != value) {
                        value += (frontRear == 1 ? 1 : -1);
                        mValueMap.put(ConstantModelValue.OID_ACT_SEAT_DRIVER_ADJUST_LAZYBACK, value);
                    }
                }

                @Override
                public void onWholeFrontRear(int frontRear) {
                    Integer value = mValueMap.get(ConstantModelValue.OID_ACT_SEAT_DRIVER_ADJUST_FRONT_REAR);
                    if (null != value) {
                        value += (frontRear == 1 ? 1 : -1);
                        mValueMap.put(ConstantModelValue.OID_ACT_SEAT_DRIVER_ADJUST_FRONT_REAR, value);
                    }
                }

                @Override
                public void onWholeUpDown(int upDown) {
                    Integer value = mValueMap.get(ConstantModelValue.OID_ACT_SEAT_DRIVER_ADJUST_UP_DOWN);
                    if (null != value) {
                        value += (upDown == 1 ? 1 : -1);
                        mValueMap.put(ConstantModelValue.OID_ACT_SEAT_DRIVER_ADJUST_UP_DOWN, value);
                    }
                }
            });
        }

    }


//    private void addListener() {
//        // 整体 up is 1;down is 2;front is 3,rear is 4;do nothing is 0
//        mSeatAdjustRelativeLayout.setSeatAllTouchUpListen(upDownFrontBack -> {
//            MLog.d(TAG, "setSeatAllTouchUpListen, upDownFrontBack = " + upDownFrontBack);
//            int moveId;
//            int diff;
//            if (upDownFrontBack == 1) {
//                moveId = 2;
//                diff = 1;
//            } else if (upDownFrontBack == 2) {
//                moveId = 2;
//                diff = -1;
//            } else if (upDownFrontBack == 3) {
//                moveId = 1;
//                diff = 1;
//            } else if (upDownFrontBack == 4) {
//                moveId = 1;
//                diff = -1;
//            } else {
//                moveId = 0;
//                diff = 0;
//            }
//
//            ISeat iSeat = CustomApiManager.getInstance().getISeat();
//            if (null != iSeat) {
//                try {
//                    int i = mIsDriver ? 1 : 2;
//                    int curValue = iSeat.getSeatMovementPosition(i, moveId);
//                    curValue += diff;
//                    if (curValue < 0) {
//                        curValue = 0;
//                    }
//                    if (curValue > 100) {
//                        curValue = 100;
//                    }
//                    iSeat.setSeatMovementPosition(i, moveId, curValue);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
//
//        });
//
//        // 靠背 //front is 0, rear is 1, do nothing is 2
//        mSeatAdjustRelativeLayout.setSeatBackTouchUpListen(frontBack -> {
//            MLog.d(TAG, "setSeatAllTouchUpListen, setSeatBackTouchUpListen = " + frontBack);
//            int moveId;
//            int diff;
//            if (frontBack == 0) {
//                moveId = 3;
//                diff = 1;
//            } else if (frontBack == 1) {
//                moveId = 3;
//                diff = -1;
//            } else {
//                moveId = 0;
//                diff = 0;
//            }
//            ISeat iSeat = CustomApiManager.getInstance().getISeat();
//            if (null != iSeat) {
//                try {
//                    int i = mIsDriver ? 1 : 2;
//                    int curValue = iSeat.getSeatMovementPosition(i, moveId);
//                    curValue += diff;
//                    if (curValue < 0) {
//                        curValue = 0;
//                    }
//                    if (curValue > 100) {
//                        curValue = 100;
//                    }
//                    iSeat.setSeatMovementPosition(i, moveId, curValue);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
//
//        });
//
//        // 坐垫 up is 0;down is 1;do nothing is 2
//        mSeatAdjustRelativeLayout.setSeatCushionTouchUpListen(upDown -> {
//            MLog.d(TAG, "setSeatAllTouchUpListen, setSeatCushionTouchUpListen = " + upDown);
//            int moveId;
//            int diff;
//            if (upDown == 0) {
//                moveId = 5;
//                diff = 1;
//            } else if (upDown == 1) {
//                moveId = 5;
//                diff = -1;
//            } else {
//                moveId = 0;
//                diff = 0;
//            }
//            ISeat iSeat = CustomApiManager.getInstance().getISeat();
//            if (null != iSeat) {
//                try {
//                    int i = mIsDriver ? 1 : 2;
//                    int curValue = iSeat.getSeatMovementPosition(i, moveId);
//                    curValue += diff;
//                    if (curValue < 0) {
//                        curValue = 0;
//                    }
//                    if (curValue > 100) {
//                        curValue = 100;
//                    }
//                    iSeat.setSeatMovementPosition(i, moveId, curValue);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
//        });
//
//        //front is 1,rear is 2;do nothing is 0
//        mSeatBackrestRelativeLayout.setSeatBackrestFrontRearPressListen(frontRear -> {
//            MLog.d(TAG, "setSeatAllTouchUpListen, setSeatBackrestFrontRearPressListen = " + frontRear);
//            int moveId;
//            int diff;
//            if (frontRear == 1) {
//                moveId = 6;
//                diff = 1;
//            } else if (frontRear == 2) {
//                moveId = 6;
//                diff = -1;
//            } else {
//                moveId = 0;
//                diff = 0;
//            }
//            ISeat iSeat = CustomApiManager.getInstance().getISeat();
//            if (null != iSeat) {
//                try {
//                    int i = mIsDriver ? 1 : 2;
//                    int curValue = iSeat.getSeatMovementPosition(i, moveId);
//                    curValue += diff;
//                    if (curValue < 0) {
//                        curValue = 0;
//                    }
//                    if (curValue > 100) {
//                        curValue = 100;
//                    }
//                    iSeat.setSeatMovementPosition(i, moveId, curValue);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
//        });
//
//        //up is 1;down is 2;do nothing is 0
//        mSeatBackrestRelativeLayout.setSeatBackrestUpDownPressListen(upDown -> {
//            MLog.d(TAG, "setSeatAllTouchUpListen, setSeatBackrestUpDownPressListen = " + upDown);
//            int moveId;
//            int diff;
//            if (upDown == 1) {
//                moveId = 4;
//                diff = 1;
//            } else if (upDown == 2) {
//                moveId = 4;
//                diff = -1;
//            } else {
//                moveId = 0;
//                diff = 0;
//            }
//            ISeat iSeat = CustomApiManager.getInstance().getISeat();
//            if (null != iSeat) {
//                try {
//                    int i = mIsDriver ? 1 : 2;
//                    int curValue = iSeat.getSeatMovementPosition(i, moveId);
//                    curValue += diff;
//                    if (curValue < 0) {
//                        curValue = 0;
//                    }
//                    if (curValue > 100) {
//                        curValue = 100;
//                    }
//                    iSeat.setSeatMovementPosition(i, moveId, curValue);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
//        });
//
//    }

    @Override
    public void onConfigurationChanged() {

    }

}
