package com.dfl.smartscene.ccs.wrapper;

import android.app.Application;
import android.util.Log;

import com.szlanyou.gwsdk.AppInfo;
import com.szlanyou.gwsdk.LanYouRequest;
import com.szlanyou.gwsdk.RequestCallback;
import com.szlanyou.gwsdk.RequestEnvironment;

import java.util.HashMap;

/**
 * LanYouRequest 的安全包装器
 * 用于解决 VerifyError 问题
 */
public class LanYouRequestWrapper {
    private static final String TAG = "LanYouRequestWrapper";
    private static boolean isInitialized = false;
    private static boolean hasVerifyError = false;

    /**
     * 安全初始化 LanYouRequest
     */
    public static synchronized boolean safeInit(Application application, AppInfo appInfo, RequestEnvironment environment) {
        if (isInitialized) {
            Log.d(TAG, "LanYouRequest already initialized");
            return true;
        }

        if (hasVerifyError) {
            Log.e(TAG, "LanYouRequest has VerifyError, skipping initialization");
            return false;
        }

        try {
            // 预先加载类以检测 VerifyError
            Class<?> clazz = Class.forName("com.szlanyou.gwsdk.LanYouRequest");
            Log.d(TAG, "LanYouRequest class loaded successfully");

            // 尝试初始化
            LanYouRequest.init(application, appInfo, environment);
            isInitialized = true;
            Log.d(TAG, "LanYouRequest initialized successfully");
            return true;

        } catch (VerifyError e) {
            Log.e(TAG, "VerifyError when initializing LanYouRequest: " + e.getMessage(), e);
            hasVerifyError = true;
            return false;
        } catch (ClassNotFoundException e) {
            Log.e(TAG, "LanYouRequest class not found: " + e.getMessage(), e);
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Exception when initializing LanYouRequest: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 安全发起请求
     */
    public static boolean safeRequest(HashMap<String, Object> params, RequestCallback callback, boolean needSign) {
        if (hasVerifyError) {
            Log.e(TAG, "LanYouRequest has VerifyError, cannot make request");
            if (callback != null) {
                callback.onFail("LanYouRequest has VerifyError");
            }
            return false;
        }

        if (!isInitialized) {
            Log.e(TAG, "LanYouRequest not initialized, cannot make request");
            if (callback != null) {
                callback.onFail("LanYouRequest not initialized");
            }
            return false;
        }

        try {
            LanYouRequest.request(params, callback, needSign);
            return true;
        } catch (VerifyError e) {
            Log.e(TAG, "VerifyError when making request: " + e.getMessage(), e);
            hasVerifyError = true;
            if (callback != null) {
                callback.onFail("VerifyError: " + e.getMessage());
            }
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Exception when making request: " + e.getMessage(), e);
            if (callback != null) {
                callback.onFail("Exception: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * 安全发起请求（不需要签名）
     */
    public static boolean safeRequest(HashMap<String, Object> params, RequestCallback callback) {
        return safeRequest(params, callback, false);
    }

    /**
     * 安全反初始化
     */
    public static synchronized void safeDeInit() {
        if (!isInitialized || hasVerifyError) {
            Log.d(TAG, "LanYouRequest not initialized or has error, skipping deInit");
            return;
        }

        try {
            LanYouRequest.deInit();
            isInitialized = false;
            Log.d(TAG, "LanYouRequest deInitialized successfully");
        } catch (VerifyError e) {
            Log.e(TAG, "VerifyError when deinitializing LanYouRequest: " + e.getMessage(), e);
            hasVerifyError = true;
        } catch (Exception e) {
            Log.e(TAG, "Exception when deinitializing LanYouRequest: " + e.getMessage(), e);
        }
    }

    /**
     * 检查是否有 VerifyError
     */
    public static boolean hasVerifyError() {
        return hasVerifyError;
    }

    /**
     * 检查是否已初始化
     */
    public static boolean isInitialized() {
        return isInitialized && !hasVerifyError;
    }

    /**
     * 重置状态（用于测试）
     */
    public static synchronized void resetState() {
        isInitialized = false;
        hasVerifyError = false;
        Log.d(TAG, "LanYouRequestWrapper state reset");
    }
}
