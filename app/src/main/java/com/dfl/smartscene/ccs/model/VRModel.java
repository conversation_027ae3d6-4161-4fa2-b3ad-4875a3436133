package com.dfl.smartscene.ccs.model;

import android.content.Intent;
import android.media.AudioAttributes;
import android.os.RemoteException;
import android.text.TextUtils;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.dfl.api.app.navi.location.ILocation;
import com.dfl.api.app.usercenter.weather.CityBean;
import com.dfl.api.app.usercenter.weather.IWeather;
import com.dfl.api.app.usercenter.weather.IWeatherCallback;
import com.dfl.api.app.usercenter.weather.WeatherItems;
import com.dfl.api.app.vr.control.ISpeechControl;
import com.dfl.api.app.vr.tts.ITtsAgentAidlCallback;
import com.dfl.api.app.vr.tts.ITtsAidl;
import com.dfl.api.app.vr.uploadhotwords.IUploadAgentAidlCallback;
import com.dfl.api.app.vr.uploadhotwords.IUploadHotwords;
import com.dfl.api.base.NullServiceException;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.dflcommonlibs.network.NetworkUtils;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SceneCategory;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.bean.navi.CarLocationInfo;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.constant.ScenePatternFuncDef;
import com.dfl.smartscene.ccs.constant.WeatherValue;
import com.dfl.smartscene.ccs.core.ConditionBaseModel;
import com.dfl.smartscene.ccs.core.DeviceBaseModel;
import com.dfl.smartscene.ccs.library.app.ScenePattern;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.model.manager.CustomApiManager;
import com.dfl.smartscene.ccs.model.manager.SceneConditionManager;
import com.dfl.smartscene.ccs.util.GsonUtil;
import com.dfl.smartscene.ccs.util.HandlerUtil;
import com.dfl.smartscene.ccs.util.ListUtil;
import com.dfl.smartscene.ccs.util.SysViewControl;
import com.dfl.smartscene.ccs.util.ToastManager;
import com.google.gson.Gson;
import com.iauto.uibase.utils.MLog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 语音model层
 */
public class VRModel implements DeviceBaseModel, ConditionBaseModel {
    private static final String TAG = "VRModel";

    private static volatile VRModel sInstance;

    public static VRModel getInstance() {
        if (null == sInstance) {
            synchronized (VRModel.class) {
                if (null == sInstance) {
                    sInstance = new VRModel();
                }
            }
        }
        return sInstance;
    }

    /**
     * 向子模块注册语音返回的打开某一场景的请求
     * 用于概念仓项目
     */
    private VRModel() {

    }

    /**
     * 初始化tts播报服务
     */
    public void initTtsListener() {
        LogUtil.d(TAG,"initTtsListener");
        try {
            if (CustomApiManager.getInstance().getITtsAidl() != null) {
                CustomApiManager.getInstance().getITtsAidl().registerClientCallback(new ITtsAgentAidlCallback() {
                    @Override
                    public void onPlayBegin() {

                    }

                    @Override
                    public void onPlayCompleted() {
                        hideVr();
                    }

                    @Override
                    public void onPlayInterrupted() {

                    }

                    @Override
                    public void onProgressReturn(int i, int i1) {

                    }

                    @Override
                    public void onTtsInited(boolean b, int i) {

                    }

                    @Override
                    public void onPlayPause() {

                    }

                    @Override
                    public String getClientId() {
                        LogUtil.d(TAG, ScenePatternApp.getInstance().getPackageName());
                        return ScenePatternApp.getInstance().getPackageName();
                    }

                    @Override
                    public void onPlayResume() {

                    }
                }, AudioAttributes.USAGE_ASSISTANT, false);
            }
        } catch (NullServiceException e) {
            e.printStackTrace();
            LogUtil.e(TAG,"initTtsListener error");
        }
    }

    /**
     * 初始化可见即可说服务
     */
    public void initVrListener() {
        try {
            if (CustomApiManager.getInstance().getIUploadHotwords() != null) {
                CustomApiManager.getInstance().getIUploadHotwords().registerUploadHotwordsCallback(new IUploadAgentAidlCallback() {
                    @Override
                    public void onInit(boolean b) {

                    }

                    @Override
                    public void onRelease(boolean b) {

                    }

                    @Override
                    public boolean onDispatchHotwordsSrResult(String s) {
                        return onVrHotWordCallback(s);
                    }

                    @Override
                    public int getClientId() {
                        return 0;
                    }


                });
            }
        } catch (NullServiceException  |SecurityException e) {
            MLog.e(TAG, "registerUploadHotwordsCallback:" + e.getMessage());
        }
        if (SceneDataModel.getInstance().getUserScenes() != null) {
            for (SceneCategory sceneCategory : SceneDataModel.getInstance().getUserScenes()) {
                for (SceneBean sceneBean : sceneCategory.getSceneBeanList()) {
                    SceneConditionManager.getInstance().registerVrCondition(sceneBean);
                }
            }
        }

        List<SceneCategory> cardCategoryList = SceneDataModel.getInstance().getSquareScenes();
        if(cardCategoryList != null){
            for(SceneCategory sceneCategory :cardCategoryList){
                if(sceneCategory != null){
                    for (SceneBean sceneBean : sceneCategory.getSceneBeanList()) {
                        SceneConditionManager.getInstance().registerVrCondition(sceneBean);
                    }
                }
            }
        }


    }

    public boolean onVrHotWordCallback(String s){
        MLog.d(TAG, "onDispatchHotwordsSrResult : " + s + ",vrCondition " + vrCondition);

        UploadResultBean uploadResultBean = GsonUtil.strToBean(s,UploadResultBean.class);

        String text = uploadResultBean.getViewCmd();
        LogUtil.d(TAG, "onVrHotWordCallback: text "+ text +" uploadResultBean " +uploadResultBean.toString());
        if(text.isEmpty() && !s.isEmpty()){
            int lastQuoteIndex = s.lastIndexOf("\"");
            int secondLastQuoteIndex = s.lastIndexOf("\"", lastQuoteIndex - 1);
            text = s.substring(secondLastQuoteIndex + 1, lastQuoteIndex);
            LogUtil.d(TAG, "onVrHotWordCallback: after extract " +text);
        }

        List<String> sceneIdList = vrCondition.get(text);
        if (eggCondition.containsKey(text)){
            Intent intent3 = new Intent();
            intent3.setClassName("com.dfl.newscenepattern", "com.dfl.newscenepattern.MainActivity");
            intent3.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            ScenePatternApp.getInstance().startActivity(intent3);

            //惊喜彩蛋中有这个热词
            SurpriseEggPlayModel.getInstance().postSurpriseEggURL(Objects.requireNonNull(eggCondition.get(text)).getURL());
            SurpriseEggPlayModel.getInstance().postSurpriseEggTTSText(Objects.requireNonNull(eggCondition.get(text)).getTTSText());
            SurpriseEggPlayModel.getInstance().postSurpriseEggVoiceFile(Objects.requireNonNull(eggCondition.get(text)).getVoiceFile());
            SurpriseEggPlayModel.getInstance().postSurpriseEggVoiceTyp(Objects.requireNonNull(eggCondition.get(text)).getVoiceType());
            if (DriveModel.getInstance().isPGear()) {
                if (NetworkUtils.getNetworkAvailable(ScenePattern.getInstance().getContext())) {
                    HandlerUtil.getMainHandler().post(new Runnable() {
                        @Override
                        public void run() {
                            SysViewControl.getInstance().reqOpenSurpriseEggPlayFullView(ScenePatternFuncDef.SYS_VIEW_CTRL_PATTERN_SURPRISE);
                        }
                    });
                    return true;
                } else {
                    HandlerUtil.getMainHandler().post(new Runnable() {
                        @Override
                        public void run() {
                            ToastManager.getInstance().showToast("网络异常，请检查网络后重试",
                                    Toast.LENGTH_SHORT,
                                    false);
                        }
                    });
                    return false;
                }
            } else {
                HandlerUtil.getMainHandler().post(new Runnable() {
                    @Override
                    public void run() {
                        ToastManager.getInstance().showToast("请在P挡时播放惊喜彩蛋",
                                Toast.LENGTH_SHORT,
                                false);
                    }
                });
                return false;
            }
        }
        MLog.d(TAG, "onDispatchHotwordsSrResult : " +sceneIdList);
        if (ListUtil.isEmpty(sceneIdList)) {
            MLog.d(TAG, "onDispatchHotwordsSrResult : " + "sceneId == null");
            return findSceneFromModelList(text);
//            return false;
        } else {
            MLog.d(TAG, "onDispatchHotwordsSrResult : " + "find it");
            checkSceneContainsTTsOperation(sceneIdList.get(0));
            Intent intent3 = new Intent();
            intent3.setClassName("com.dfl.newscenepattern", "com.dfl.newscenepattern.MainActivity");
            intent3.putExtra("ViewType", "Scene");
            intent3.putExtra("INTENT_KEY_SCENE_ID", sceneIdList.get(0));
            intent3.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            ScenePatternApp.getInstance().startActivity(intent3);
            return true;
        }
    }

    /**
     * 从model保存的数据查询场景
     * @param text 热词
     * @return
     */
    private boolean findSceneFromModelList(String text){
        if(TextUtils.isEmpty(text)){
            LogUtil.d(TAG, "findSceneFromModelList: text is null");
            return false;
        }
        LogUtil.d(TAG, "findSceneFromModelList: text " +text);
        List<SceneCategory> list = SceneDataModel.getInstance().getUserScenes();
        LogUtil.d(TAG, "findSceneFromModelList: list " + list);
        for(SceneCategory sceneCategory :list){
            for(SceneBean sceneBean : sceneCategory.getSceneBeanList()){
                for (int i = 0; i < sceneBean.getConditionOperations().size(); i++) {
                    if (ConstantModelValue.OPERATION_ID_CONDITION_VR.equals(sceneBean.getConditionOperations().get(i).getOperationId())) {
                        if(text.equals(sceneBean.getConditionOperations().get(i).getListArgs().get(0))){
                            checkSceneContainsTTsOperation(sceneBean.getSceneId());
                            Intent intent3 = new Intent();
                            intent3.setClassName("com.dfl.newscenepattern", "com.dfl.newscenepattern.MainActivity");
                            intent3.putExtra("ViewType", "Scene");
                            intent3.putExtra("INTENT_KEY_SCENE_ID", sceneBean.getSceneId());
                            intent3.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                            ScenePatternApp.getInstance().startActivity(intent3);
                            LogUtil.d(TAG, "findSceneFromModelList: find it ");
                            return true;
                        }
                    }
                }
            }
        }
        LogUtil.d(TAG, "findSceneFromModelList: no find  ");

        return false;
    }

    public static class UploadResultBean {
        private String viewCmd = "";

        public String getViewCmd() {
            return viewCmd;
        }

        public void setViewCmd(String viewCmd) {
            this.viewCmd = viewCmd;
        }

        public UploadResultBean() {
        }

        public UploadResultBean(String viewCmd) {
            this.viewCmd = viewCmd;
        }

        @NonNull
        @Override
        public String toString() {
            return "UploadResultBean{" +
                    "viewCmd='" + viewCmd + '\'' +
                    '}';
        }
    }

    @Override
    public boolean dispatchSingleActionOperation(SettingOperation settingOperation) {

        if (settingOperation == null) {
            MLog.d(TAG, "dispatchSingleActionOperation settingOperation = null");
            return false;
        }
        switch (settingOperation.getOperationId()) {
            // 在DAModel中实现
//            case ConstantModelValue.OID_ACT_APP_VR_VOLUME:
//                break;
            case ConstantModelValue.OID_ACT_APP_VR_WEATHER: {

                List<String> listArgs = settingOperation.getListArgs();
                if (null != listArgs && !listArgs.isEmpty()) {
                    String weatherSpeakSwitch = listArgs.get(0);

                    if ("true".equals(weatherSpeakSwitch)) {
                        speakWeatherInfo(settingOperation);
                    }


                } else {
                    MLog.e(TAG, "dispatchSingleActionOperation vr user, listArgs = null");
                }

            }
            return true;
            case ConstantModelValue.OID_ACT_APP_VR_USER: {

                List<String> listArgs = settingOperation.getListArgs();
                if (null != listArgs && !listArgs.isEmpty()) {
                    String speakText = listArgs.get(0);
                    startTTS(speakText);
                } else {
                    MLog.e(TAG, "dispatchSingleActionOperation vr user, listArgs = null");
                }

            }
            return true;
            default:
                return false;
        }

    }

    /**
     * 开始tts播报
     * @param content 播报内容
     */
    public void startTTS(String content) {

        ITtsAidl iTtsAidl = CustomApiManager.getInstance().getITtsAidl();
        if (null != iTtsAidl) {
            try {
                LogUtil.d(TAG,"startTTS : " + content);
                iTtsAidl.startSpeak(ScenePatternApp.getInstance().getPackageName(), content, 1);

            } catch (NullServiceException | RemoteException e) {
                e.printStackTrace();
            }
        } else {
            MLog.e(TAG, "dispatchSingleActionOperation vr user, iTtsAidl = null");
        }
    }

    /**
     * 播报天气信息
     * @param settingOperation
     * @return
     */
    private boolean speakWeatherInfo(SettingOperation settingOperation) {

        WeatherItems weatherItems = WeatherModel.getmWeatherItems();
        if(weatherItems != null){
            String speakText = WeatherValue.packageWeatherInfo(weatherItems);
            startTTS(speakText);
            return true;
        }
        final boolean[] weatherState = {false};

        IWeather iWeather = null;
        IWeatherCallback iWeatherCallback = null;
        try {
            iWeather = CustomApiManager.getInstance().getIWeather();
            // 天气服务未连接
            if (iWeather == null) {
                MLog.d(TAG, "checkWeather iWeather = null");
                return false;
            }

            ILocation iLocation = CustomApiManager.getInstance().getILocation();
            if (iLocation == null) {
                MLog.d(TAG, "checkWeather iLocation = null");
                return false;
            }

            iWeatherCallback = new IWeatherCallback() {
                @Override
                public void onReqWeather(int i, WeatherItems weatherItems) {
                    if (i == 1) {// 获取数据成功
                        weatherState[0] = true;

                        String speakText = WeatherValue.packageWeatherInfo(weatherItems);
                        startTTS(speakText);
                    } else {// 获取数据失败
                        weatherState[0] = false;
                        MLog.d(TAG, "dispatchSingleActionOperation iWeatherCallback data acquisition failure");
                    }
                }

                @Override
                public void replayCityList(int i, List<CityBean> list) {
                }
            };
            iWeather.registerCallback(iWeatherCallback);

            String carLocationInfoJson = iLocation.getCarLocationInfo();
            if (TextUtils.isEmpty(carLocationInfoJson)) {
                MLog.d(TAG, "dispatchSingleActionOperation car location info json = null");
                return false;
            }

            CarLocationInfo carLocationInfo = new Gson().fromJson(carLocationInfoJson, CarLocationInfo.class);
            if (null == carLocationInfo) {
                MLog.d(TAG, "dispatchSingleActionOperation car location info = null");
                return false;
            }
            // 省，城市，区
            iWeather.reqWeather(carLocationInfo.getProvince(), carLocationInfo.getCityName(), carLocationInfo.getDistrictName());


        } catch (NullServiceException | RemoteException e) {
            e.printStackTrace();
        }

        return weatherState[0];
    }

    /**
     * 延时倾听
     */
    public void hideVr() {
        ISpeechControl iSpeechControl = CustomApiManager.getInstance().getISpeechControl();
        if (null != iSpeechControl) {
            try {
                LogUtil.d(TAG,"hideVr : " );
                iSpeechControl.hideVr();
            } catch (NullServiceException | RemoteException e) {
                LogUtil.e(TAG, "hideVr, throw exception "+e.getMessage());
            }
        } else {
            LogUtil.e(TAG, "hideVr, iSpeechControl = null");
        }
    }

    /**
     * 退出语音交互
     */
    public void exitVr() {
        ISpeechControl iSpeechControl = CustomApiManager.getInstance().getISpeechControl();
        if (null != iSpeechControl) {
            try {
                LogUtil.d(TAG,"exitVr : " );
                iSpeechControl.exitVr();
            } catch (NullServiceException | RemoteException e) {
                LogUtil.e(TAG, "exitVr, throw exception "+e.getMessage());
            }
        } else {
            LogUtil.e(TAG, "exitVr, iSpeechControl = null");
        }
    }

    /**
     * key:语音指令
     * value:场景id
     */
    private final Map<String, List<String>> vrCondition = new HashMap<>();
    private final Map<String, SurpriseEgg> eggCondition = new HashMap<>();

    //获取所有的热词，热词不能重复
    public List<String> getVRHotWords(){
        List<String> nonEmptyKeys = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : vrCondition.entrySet()) {
            List<String> value = entry.getValue();
            if (value != null && !value.isEmpty()) {
                nonEmptyKeys.add(entry.getKey());
            }
        }
        return nonEmptyKeys;
    }

    @Override
    public void registerSceneCondition(SceneBean sceneBean, SettingOperation condition) {
        MLog.d(TAG,"registerSceneCondition : " + condition.getListArgs().get(0));
        MLog.d(TAG,"registerSceneCondition ------: " + sceneBean.toString());
        List<String> hotWords = new ArrayList<>();
        for (int i = 0; i < sceneBean.getConditionOperations().size(); i++) {
            if (ConstantModelValue.OPERATION_ID_CONDITION_VR.equals(sceneBean.getConditionOperations().get(i).getOperationId())) {
                hotWords.add(sceneBean.getConditionOperations().get(i).getListArgs().get(0));
            }
        }
        if (ConstantModelValue.OPERATION_ID_CONDITION_VR.equals(condition.getOperationId())) {
            List<String> ids = vrCondition.get(condition.getListArgs().get(0));
            if(ids == null){
                ids = new ArrayList<>();
            }
            if(!ids.contains(sceneBean.getSceneId())){
                ids.add(sceneBean.getSceneId());
            }
            MLog.d(TAG,"registerSceneCondition 热词: " + condition.getListArgs().get(0) + " ,,,,场景ids = " + ids);
            vrCondition.put(condition.getListArgs().get(0), ids);
            hotWords.addAll(getVRHotWords());
            hotWords.addAll(eggCondition.keySet());
//            registerVrUploadWord(removeDuplicates(hotWords));
        }
    }

    public void registerSupriseEggs(SurpriseEggList eggList){
        LogUtil.d(TAG, "registerSupriseEggs: " +eggList);
        if (eggList != null && eggList.getSurpriseEggs() != null && !eggList.getSurpriseEggs().isEmpty()){
            eggCondition.clear();
            for (SurpriseEgg egg : eggList.getSurpriseEggs()){
                eggCondition.put(egg.getName(), egg);
            }
            List<String> allWordlist = new ArrayList<>(eggCondition.keySet());
            allWordlist.addAll(getVRHotWords());
//            registerVrUploadWord(removeDuplicates(allWordlist));
        }
    }

    /**
     * List去重
     * @param list
     * @return
     * @param <T>
     */
    public  <T> List<T> removeDuplicates(List<T> list) {
        Set<T> set = new HashSet<>(list);
        return new ArrayList<>(set);
    }


    /**
     * 向vr注册可见即可说词汇
     * @param
     */
//    public void registerVrUploadWord(List<String> words) {
//        MLog.d(TAG, "registerVrUploadWord : " + words.size() + ",,," + words);
//
//        try {
//            IUploadHotwords uploadHotwords = CustomApiManager.getInstance().getIUploadHotwords();
//            if(null == uploadHotwords){
//                return;
//            }
//            uploadHotwords.uploadHotwordsDataList(words, UploadHotWordsConstant.CLIENT_ID_SCENEAPP);
//        } catch (NullServiceException | NullPointerException e) {
//            LogUtil.d(TAG, "throw exception while registerVrUploadWord");
//        }
//    }


    @Override
    public void unRegisterSceneCondition(SceneBean sceneBean, SettingOperation condition) {
        MLog.d(TAG,"unRegisterSceneCondition : " + condition.getListArgs().get(0));
        MLog.d(TAG,"unRegisterSceneCondition ------: " + sceneBean.toString());
        if (ConstantModelValue.OPERATION_ID_CONDITION_VR.equals(condition.getOperationId())) {
            if(sceneBean.getSceneCategoryId().equals(ConstantModelValue.CATEGORY_ID_COLLECT) || sceneBean.getSceneCategoryId().equals(ConstantModelValue.SCENE_CATEGORY_ID_LIBRARY_SERVICE)){
                List<String> squareList  = getSquareVrConditionList(sceneBean.getSceneId());
                LogUtil.d(TAG, "unRegisterSceneCondition: squareList " +squareList);
                if(!squareList.contains(condition.getListArgs().get(0))){
                    LogUtil.d(TAG, "unRegisterSceneCondition: squareList " +squareList);
                    vrCondition.remove(condition.getListArgs().get(0));
                }
            }else {
                vrCondition.remove(condition.getListArgs().get(0));
            }
        }
    }

    private List<String> getSquareVrConditionList(String sceneId){
        List<String> list  = new ArrayList<>();
        SceneBean sceneBean = SceneDataModel.getInstance().findLibrarySceneBeanById(sceneId);
        if(sceneBean == null){
            return list;
        }
        for (SettingOperation settingOperation : sceneBean.getConditionOperations()) {
            if (settingOperation.getOperationId().equals(ConstantModelValue.OPERATION_ID_CONDITION_VR)) {
                list.add(settingOperation.getListArgs().get(0));
            }
        }
        return list;
    }

    /**
     * 检查热词触发的场景中是否包含播报类的操作
     * @param sceneId
     */
    private void checkSceneContainsTTsOperation(String sceneId){
        SceneBean sceneBean;
        sceneBean = SceneDataModel.getInstance().findLibrarySceneBeanById(sceneId);
        if(sceneBean == null){
            sceneBean = SceneDataModel.getInstance().findUserSceneBeanById(sceneId);
        }
        if (sceneBean == null) {
            return;
        }
        boolean hasTTsOperation  = false;
        for(SettingOperation settingOperation:sceneBean.getActionOperations()){
            if(settingOperation == null)return;
            if (settingOperation.getOperationId().equals(ConstantModelValue.OID_ACT_APP_VR_USER)) {
                List<String> listArgs = settingOperation.getListArgs();
                if (null != listArgs && !listArgs.isEmpty()) {
                    hasTTsOperation = true;
                    break;
                }
            } else if (settingOperation.getOperationId().equals(ConstantModelValue.OID_ACT_APP_VR_WEATHER)) {
                List<String> listArgs = settingOperation.getListArgs();
                if (null != listArgs && !listArgs.isEmpty()) {
                    String weatherSpeakSwitch = listArgs.get(0);
                    if ("true".equals(weatherSpeakSwitch)) {
                        hasTTsOperation = true;
                        break;
                    }
                }
            }
        }
        if(!hasTTsOperation){
            exitVr();
        }
    }

}
