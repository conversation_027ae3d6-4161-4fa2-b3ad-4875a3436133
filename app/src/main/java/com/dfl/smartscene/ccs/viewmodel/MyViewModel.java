package com.dfl.smartscene.ccs.viewmodel;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.dflcommonlibs.rxbus.RxBus;
import com.dfl.dflcommonlibs.rxbus.Subscribe;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SceneCategory;
import com.dfl.smartscene.ccs.busevent.PhoneUpdateEvent;
import com.dfl.smartscene.ccs.model.SceneDataModel;

import java.util.List;

import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR>
 * @date ：2022/8/30 17:50
 * @description ：
 */
public class MyViewModel extends ViewModel {
    private static final String TAG = "MyViewModel";

    // 防止内存泄漏
    private CompositeDisposable mCompositeDisposable = new CompositeDisposable();

    private MutableLiveData<List<SceneCategory>> mSceneCategories = new MutableLiveData<>();

    private MutableLiveData<Boolean> editMode = new MutableLiveData<>();
    private MutableLiveData<Integer> allSelected = new MutableLiveData<>();
    private MutableLiveData<String> deleteDesc = new MutableLiveData<>();
    private MutableLiveData<SceneBean> executeSceneBean = new MutableLiveData<>();

    public MyViewModel(){
        editMode.setValue(false);
        RxBus.getDefault().register(this);
    }

    /**
     * 请求用户收藏的场景数据<P/>
     * */
    public void requestUserScenes(boolean refresh){
        LogUtil.d(TAG,"requestUserScenes : " + refresh);
        SceneDataModel.getInstance().requestUserSceneList(refresh).observeOn(AndroidSchedulers.mainThread()).subscribe(new Observer<List<SceneCategory>>() {
            @Override
            public void onSubscribe(Disposable d) {
                mCompositeDisposable.add(d);
            }

            @Override
            public void onNext(List<SceneCategory> sceneCategories) {
                LogUtil.d(TAG,"requestUserScenes on onNext");
                mSceneCategories.setValue(sceneCategories);
            }

            @Override
            public void onError(Throwable e) {
                mSceneCategories.setValue(null);
                LogUtil.d(TAG,"requestUserScenes on error");
            }

            @Override
            public void onComplete() {

            }
        });
    }

    @Subscribe
    public void phoneUpdateEvent(PhoneUpdateEvent phoneUpdateEvent) {
        requestUserScenes(true);
    }

    public void requestExecuteSceneData(){
        mCompositeDisposable.add(SceneDataModel.getInstance().getExecuteSceneBeanObservable().subscribe(sceneBean -> executeSceneBean.setValue(sceneBean)));
    }
    public void requestLocalUserSceneData(){
        mCompositeDisposable.add(SceneDataModel.getInstance().getLocalUserSceneObservable().subscribe(list -> mSceneCategories.setValue(list)));
    }

    public MutableLiveData<Boolean> getEditMode() {
        return editMode;
    }

    public void setEditMode(boolean status) {
        if(editMode.getValue() == null || status != editMode.getValue()){
            editMode.setValue(status);
        }
    }

    public MutableLiveData<Integer> getAllSelected() {
        return allSelected;
    }

    public void setAllSelected(Integer status) {
        allSelected.setValue(status);
    }

    public MutableLiveData<List<SceneCategory>> getSceneCategories() {
        return mSceneCategories;
    }

    public MutableLiveData<String> getDeleteDesc() {
        return deleteDesc;
    }

    public MutableLiveData<SceneBean> getExecuteSceneBean() {
        return executeSceneBean;
    }

    public List<SceneCategory> getUserScenes() {
        return SceneDataModel.getInstance().getUserScenes();
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        mCompositeDisposable.dispose();
        RxBus.getDefault().unRegister(this);

    }

}
