package com.dfl.smartscene.ccs.util;


import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;

/*
 * HandlerThreadProcessor class
 * 线程池管理
 * <AUTHOR>
 * @date 2022/5/24
 */
public class HandlerThreadProcessor {
    private static final String TAG = "HandlerThreadProcessor";

    private Handler mainThreadHandler;

    private HandlerThread thread;

    /**
     * HandlerThreadProcessor object constructor
     */
    private HandlerThreadProcessor() {
        thread = new HandlerThread(TAG);
        mainThreadHandler = new Handler(Looper.getMainLooper());
        thread.start();
    }

    /**
     * create HandlerThreadProcessor object
     */
    private static class HandlerThreadProcessHolder {
        private static final HandlerThreadProcessor INSTANCE = new HandlerThreadProcessor();
    }

    /**
     * get HandlerThreadProcessor object Instance
     */
    public static HandlerThreadProcessor getInstance() {
        return HandlerThreadProcessHolder.INSTANCE;
    }

    /**
     * post HandlerThreadProcessor thread
     */
    public void postToMainThread(Runnable runnable) {
        if (null != mainThreadHandler) {
            mainThreadHandler.post(runnable);
        }
    }

    /**
     * postDelayed HandlerThreadProcessor thread
     */
    public void postDelayedToMainThread(Runnable runnable, long millis) {
        if (null != mainThreadHandler) {
            mainThreadHandler.postDelayed(runnable,millis);
        }
    }

}
