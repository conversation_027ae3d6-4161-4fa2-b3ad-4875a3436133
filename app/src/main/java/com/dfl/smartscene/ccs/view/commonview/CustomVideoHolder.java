package com.dfl.smartscene.ccs.view.commonview;

import android.content.Context;
import android.net.Uri;
import android.view.LayoutInflater;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.MediaController;

import androidx.annotation.NonNull;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.model.manager.VideoManager;
import com.dfl.smartscene.ccs.view.weight.CustomVideoView;

import tv.danmaku.ijk.media.player.IMediaPlayer;

/**
 * <AUTHOR>
 * @date ：2023/3/13 15:05
 * @description ：
 */
public class CustomVideoHolder extends FrameLayout implements MediaController.MediaPlayerControl {
    private CustomVideoView mCustomVideoView;
    private ImageView mImageView;
    private Uri mUri;
    private static final String TAG = "CustomVideoHolder";

    public CustomVideoHolder(@NonNull Context context) {
        super(context);
        LayoutInflater.from(context).inflate(R.layout.layout_video_holder , this , true);
        mCustomVideoView = findViewById(R.id.customvideoview);
        mImageView = findViewById(R.id.imageview_video_holder);
    }


    public void setVideoPath(Uri videoUrl){
        if(videoUrl == null){
            findViewById(R.id.imageview_video_holder_notice).setVisibility(VISIBLE);
            return;
        }
        mUri = videoUrl;
        mCustomVideoView.setVideoURI(videoUrl);
    }

    public void hideImage(){
        mImageView.setVisibility(GONE);
    }

    @Override
    public void start() {
        mCustomVideoView.start();
    }

    @Override
    protected void onAttachedToWindow() {
        LogUtil.d(TAG,"onAttachedToWindow");
        super.onAttachedToWindow();
        if(mUri != null){
            mImageView.setVisibility(VISIBLE);
            mImageView.setImageBitmap(VideoManager.getInstance().getBitMapByUri(mUri));
        }

    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
    }

    @Override
    public void pause() {
        mCustomVideoView.pause();
    }

    @Override
    public int getDuration() {
        return mCustomVideoView.getDuration();
    }

    @Override
    public int getCurrentPosition() {
        return mCustomVideoView.getCurrentPosition();
    }

    @Override
    public void seekTo(int pos) {
        mCustomVideoView.seekTo(pos);
    }

    @Override
    public boolean isPlaying() {
        return mCustomVideoView.isPlaying();
    }

    @Override
    public int getBufferPercentage() {
        return mCustomVideoView.getBufferPercentage();
    }

    @Override
    public boolean canPause() {
        return mCustomVideoView.canPause();
    }

    @Override
    public boolean canSeekBackward() {
        return mCustomVideoView.canSeekBackward();
    }

    @Override
    public boolean canSeekForward() {
        return mCustomVideoView.canSeekForward();
    }

    @Override
    public int getAudioSessionId() {
        return mCustomVideoView.getAudioSessionId();
    }

    public void setOnPreparedListener(IMediaPlayer.OnPreparedListener l) {
        mCustomVideoView.setOnPreparedListener(l);
    }

    public void suspend() {
        mCustomVideoView.suspend();
    }

}
