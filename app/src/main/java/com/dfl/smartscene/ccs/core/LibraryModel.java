package com.dfl.smartscene.ccs.core;

import android.os.Handler;

import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.model.SceneDataModifyListener;


/**
 * <AUTHOR>
 * @date ：2022/12/13 15:31
 * @description ：
 */
public class LibraryModel implements SceneDataModifyListener, ConditionBaseModel{

    private static volatile LibraryModel sInstance;

    public static LibraryModel getInstance() {
        if (null == sInstance) {
            synchronized (LibraryModel.class) {
                if (null == sInstance) {
                    sInstance = new LibraryModel();
                }
            }
        }
        return sInstance;
    }
    private LibraryMessageDispatcher mLibraryMessageDispatcher;
    private LibraryMessageUploader mLibraryMessageUploader;

    protected LibraryModel(){
        sInstance = this;
        mLibraryMessageDispatcher = creatMessageDispatcher();
        mLibraryMessageUploader = createMessageUploader();
    }



    private Handler actionHandler;

    public void setActionHandler(Handler actionHandler) {
        this.actionHandler = actionHandler;
    }

    public Handler getActionHandler() {
        return actionHandler;
    }

    protected LibraryMessageDispatcher creatMessageDispatcher(){
        return new LibraryMessageDispatcher();
    }
    protected LibraryMessageUploader createMessageUploader(){
        return new LibraryMessageUploader();
    }
    public final void initModelListener(String apiName){
        mLibraryMessageDispatcher.initModelListerner(apiName);
    }

    public final boolean executeOperation(SettingOperation settingOperation){
        return mLibraryMessageDispatcher.excuteOperation(settingOperation);
    }

    public final int resetSingleConfig(SingleOperation singleOperation){
        return mLibraryMessageDispatcher.resetSingleConfig(singleOperation);
    }

    public void registerLibraryListener(LibraryModelCallback libraryModelCallback){
        mLibraryMessageUploader.registerLibraryListener(libraryModelCallback);
    }

    @Override
    public final void onSceneAdd(SceneBean sceneBean) {
        mLibraryMessageDispatcher.onSceneAdd(sceneBean);
    }

    @Override
    public final void onSceneReplace(SceneBean newScene) {
        mLibraryMessageDispatcher.onSceneReplace(newScene);
    }

    @Override
    public final void onSceneDelete(SceneBean sceneBean) {
        mLibraryMessageDispatcher.onSceneDelete(sceneBean);
    }

    @Override
    public final void registerSceneCondition(SceneBean sceneBean, SettingOperation condition) {
        mLibraryMessageDispatcher.registerSceneCondition(sceneBean, condition);
    }

    @Override
    public final void unRegisterSceneCondition(SceneBean sceneBean, SettingOperation condition) {
        mLibraryMessageDispatcher.unRegisterSceneCondition(sceneBean, condition);
    }



    public void onSceneMeet(String sceneId){
        mLibraryMessageUploader.onSceneConditionMeet(sceneId);
    }

    public void onStartScene(String sceneId){
        mLibraryMessageUploader.onStartScene(sceneId);

    };

    public void onCarDataChange(String operationId , String value){
        mLibraryMessageUploader.onCarDataChange(operationId, value);
    };

}
