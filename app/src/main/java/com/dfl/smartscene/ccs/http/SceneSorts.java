package com.dfl.smartscene.ccs.http;

/**
 * <AUTHOR>
 * @date ：2022/12/9 16:00
 * @description ：换序接口的传参
 */
public class SceneSorts {
    private String sceneId = "";
    private int sort;
    private long timestamp;

    public SceneSorts(String sceneId, int sort, long timestamp) {
        this.sceneId = sceneId;
        this.sort = sort;
        this.timestamp = timestamp;
    }

    public String getSceneId() {
        return sceneId;
    }

    public void setSceneId(String sceneId) {
        this.sceneId = sceneId;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
}
