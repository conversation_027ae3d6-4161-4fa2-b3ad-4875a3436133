package com.dfl.smartscene.ccs.model.player;

import android.content.ComponentName;
import android.os.Bundle;

import com.dfl.smartscene.ccs.constant.RelieveStressFuncDef;


/**
 * <AUTHOR>
 * @date ：2023/5/30 15:43
 * @description ：
 */
public class FullMusicPlayer extends CommonPlayer{
    public FullMusicPlayer(ComponentName componentName) {
        super(componentName);
    }

    public FullMusicPlayer(CommonPlayer commonPlayer) {
        super(commonPlayer);
    }

    @Override
    public void play(String arg) {
        String[] split = arg.split("@");
        if(getController() == null){
            return;
        }
        getController().getTransportControls().sendCustomAction(RelieveStressFuncDef.MEDIASESSIONEXTRA_IN_ADDITIONAL_PLAY_ACTION, null);
        Bundle extras = new Bundle();
        extras.putString(RelieveStressFuncDef.MEDIASESSIONEXTRA_PARENT_ID, RelieveStressFuncDef.MEDIASESSIONEXTRA_EXTRA_SCENE_LIST_MEDIA_ID);
        getController().getTransportControls().playFromMediaId(split[1], extras);

    }

    @Override
    public void destroy() {
        if(getController() != null){
            getController().getTransportControls().sendCustomAction(RelieveStressFuncDef.MEDIASESSIONEXTRA_OUT_ADDITIONAL_PLAY_ACTION, null);
        }
        super.destroy();
    }

    public void playNext(){
        if (getController() != null){
            getController().getTransportControls().skipToNext();
        }
    }
}
