package com.dfl.smartscene.ccs.model;

import androidx.lifecycle.MutableLiveData;

public class MediaSyncModel {
    private static final String TAG = "MediaSyncModel";

    private static volatile MediaSyncModel sInstance;

    private final MutableLiveData<Integer> mPlayStatus  = new MutableLiveData<>();


    public static MediaSyncModel getInstance() {
        if (null == sInstance) {
            synchronized (MediaSyncModel.class) {
                if (null == sInstance) {
                    sInstance = new MediaSyncModel();
                }
            }
        }
        return sInstance;
    }


    public MutableLiveData<Integer> getPlayStatus() {
        return mPlayStatus;
    }

    public String getActiveSource(){
        String activeSource = ScenePatternInterrupt.getInstance().getActiveSource();
        return activeSource;
    }
}
