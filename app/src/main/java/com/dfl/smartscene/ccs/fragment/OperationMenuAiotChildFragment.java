package com.dfl.smartscene.ccs.fragment;

import android.text.SpannableString;
import android.text.method.LinkMovementMethod;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.MyClickableSpan;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.util.ToastManager;
import com.dfl.smartscene.ccs.view.ViewControlManager;
import com.dfl.smartscene.ccs.view.adapter.OperationMenuChildAdapter;
import com.dfl.smartscene.ccs.viewmodel.MenuAiotViewModel;
import com.iauto.uibase.utils.MLog;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/9/21 17:39
 * @description ：
 */
public class OperationMenuAiotChildFragment extends OperationMenuChildFragment {
    private static final String TAG = "OperationMenuAiotChildFragment";
    private MenuAiotViewModel mMenuAiotViewModel;

    private TextView mTextView;


    public OperationMenuAiotChildFragment(int operationType, List<SingleOperation> operations) {
        super(operationType, operations);
    }

    @Override
    protected void init() {
        super.init();
        mMenuAiotViewModel = new ViewModelProvider(this).get(MenuAiotViewModel.class);
    }

    @Override
    protected void initView(View view) {
        super.initView(view);
        mTextView = view.findViewById(R.id.textview_go_to_mall);
        TextView mTextViewEmpty = view.findViewById(R.id.text_go_to_mall);
        SpannableString spannableString = ToastManager.getSpanString("更多好物请前往商城", "前往商城", new MyClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                ViewControlManager.openAiotStorePage();
            }
        });
        mTextViewEmpty.setOnClickListener(v -> ViewControlManager.openAiotStorePage());
        mTextViewEmpty.setVisibility(View.VISIBLE);
        mTextView.setVisibility(View.VISIBLE);
        mTextView.setText(spannableString);
        mTextView.setMovementMethod(LinkMovementMethod.getInstance());
        changeMargin(mErrorLayout);
        changeMargin(mNoContentLayout);
        changeMargin(mLoadingLayout);
    }

    private void changeMargin(View view){
        ViewGroup.MarginLayoutParams layoutParams;
        layoutParams = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
        int originalMargin = layoutParams.topMargin;
        layoutParams.topMargin = originalMargin-24;
        view.setLayoutParams(layoutParams);
    }

    @Override
    protected void initData() {
        showLoadingPage();
        mMenuAiotViewModel.requestAiotBindAndSaleOperation();
    }

    @Override
    public void onResume() {
        super.onResume();
        MLog.d("OperationMenuAiotChildFragment", "onResume");
    }

    @Override
    protected void initObserver() {
        super.initObserver();
        mMenuAiotViewModel.getOperationsData().observe(getViewLifecycleOwner(), new Observer<List<SingleOperation>>() {
            @Override
            public void onChanged(List<SingleOperation> operations) {
                showContentPage();
                mOperationMenuChildAdapter.setDataList(operations);
            }
        });
        /**监听AIOT设备异常并且显示异常画面*/
        mMenuAiotViewModel.mAiotDeviceListAbnormal.observe(getViewLifecycleOwner(), new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if(null != aBoolean && aBoolean){
                    showErrorPage();
                }
            }
        });
    }

    @Override
    public void onItemClick(View view, int viewType, SingleOperation singleOperation, int position) {
        if (viewType == OperationMenuChildAdapter.VIEW_SALE) {
            String toastText = "您还没有" + singleOperation.getOperationName() + ",前往购买";
            ToastManager.showSpanToast(toastText, "前往购买", new MyClickableSpan() {
                @Override
                public void onClick(@NonNull View widget) {
                    ViewControlManager.openAiotStorePage();
                }
            },getView());
            return;
        }

        if (viewType == OperationMenuChildAdapter.VIEW_NORMAL && singleOperation.getShowMode() == ConstantModelValue.CAR_OPERATION_SHOW_TYPE_AIOT_OFFLINE) {
            //如果设备离线
            String toastText = singleOperation.getOperationName() + "离线啦,前往连接";
            ToastManager.showSpanToast(toastText, "前往连接", new MyClickableSpan() {
                @Override
                public void onClick(@NonNull View widget) {
                    ViewControlManager.openAiotDevicePage();
                }
            },getView());
            return;
        }
        super.onItemClick(view, viewType, singleOperation, position);
    }

    @Override
    protected View getContentPageView() {
        return getView().findViewById(R.id.layout_content);
    }
}
