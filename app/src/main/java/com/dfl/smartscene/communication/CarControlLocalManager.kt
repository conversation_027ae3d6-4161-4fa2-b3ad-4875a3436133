package com.dfl.smartscene.communication

import android.annotation.SuppressLint
import com.dfl.androhermas.constant.ServiceConstant
import com.dfl.androhermas.constant.StatusCode
import com.dfl.androhermas.remote.client.ConnectionClient
import com.dfl.androhermas.remote.client.RequestCallback
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.util.*
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.bean.communication.*
import com.jeremyliao.liveeventbus.LiveEventBus

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2023/01/12
 * desc : 在主进程与车控的应用间通讯
 * version: 1.0
 */
object CarControlLocalManager {
    private const val TAG = GlobalConstant.GLOBAL_TAG.plus("CarControlManager")

    @SuppressLint("StaticFieldLeak")
    private var iCarControlProtocol: ConnectionClient? = null

    @SuppressLint("StaticFieldLeak")
    private var iCarControlProtocol1: ConnectionClient? = null
    private val mCarControlProtocolCallback = CarControlProtocolCallback()
    private val mSmartDeviceStateList = ArrayList<String>()
    private var mFragranceLinkState = false //香氛连接状态（临时缓存）
    private var mFragranceDeviceId = -1 //香氛设备ID

    //记忆座椅主副驾挡位状态
    private var mSeatStateBean = CarSeatStateBean()

    fun init() {
        CustomApiUtils.requestCustomApi({

            iCarControlProtocol = ConnectionClient.Builder(
                CommonUtils.getApp(), ServiceConstant.Package.PACKAGE_CAR_CONTROL
            ).setReconnectMode(ConnectionClient.CONNECT_MODE_LIMITED_RECONNECT).setThreadPool(1, 1).build()
            iCarControlProtocol1 = ConnectionClient.Builder(
                CommonUtils.getApp(), ServiceConstant.Package.PACKAGE_CAR_CONTROL
            ).setReconnectMode(ConnectionClient.CONNECT_MODE_LIMITED_RECONNECT).setConnectTimeout(5000)
                .setRequestTimeout(5000).setThreadPool(1, 1).build()

        }, {
            handleException("carControl init", it)
        })
    }

    fun unregisterCarControl() {
        CustomApiUtils.requestCustomApi({
            iCarControlProtocol?.unBindServer()
        }, {
            handleException("unregisterCarControl", it)
        })
    }

    //获取智能设备状态接口
    fun getAiDeviceState(): ArrayList<String> {
        CustomApiUtils.requestCustomApi(iCarControlProtocol != null, {
            val bean = CarControlRequestBean(
                "",
                "dispatch",
                1002,
                GlobalConstant.APPLICATION_ID,
                System.currentTimeMillis().toString(),
                "",
                AppUtils.getAppVersionName()
            )
            val json = GsonUtils.toJson(bean)
            CommonLogUtils.logI(TAG, "SOA方法：getAiDeviceState -dispatch- json===>${json}")
            iCarControlProtocol?.sendMessage(json, mCarControlProtocolCallback)
        }, {
            handleException("getAiDeviceState", it)
        }, {
            init()
        })
        return mSmartDeviceStateList
    }

    /**
     *  调整主副驾至记忆位
     *  @param position 座椅位置1：主驾 2：副驾
     *  @param mode  1 驾驶 2 离车 3 休息 4 备用 5放平 6尊享
     */
    fun setSeatMemoryState(position: Int, mode: Int) {
        if (iCarControlProtocol1 == null) {
            init()
            return
        }
        val index = if (mode == -1 && position == 1) {
            mSeatStateBean.mMainDriveSeatState ?: -1 //这里需要确定如果获取到的值为-1或者没有获取到值的时候怎么办
        } else if (mode == -1 && position == 2) {
            mSeatStateBean.mCopilotSeatState ?: -1
        } else {
            mode
        }
        CustomApiUtils.requestCustomApi({
            val bean: CarControlRequestBean<SeatMemoryBean> = CarControlRequestBean(
                SeatMemoryBean(position, index),
                "request",
                1011,
                GlobalConstant.APPLICATION_ID,
                System.currentTimeMillis().toString(),
                "",
                AppUtils.getAppVersionName()
            )
            val json = GsonUtils.toJson(bean)
            CommonLogUtils.logE(TAG, "sendMessage====>$json")
            iCarControlProtocol1?.sendMessage(json, object : RequestCallback {
                override fun onMessage(respJson: String?, statusCode: Int) {
                    CommonLogUtils.logE(
                        TAG, "setSeatMemoryState===>$respJson==statusCode==>$statusCode"
                    )
                    respJson?.let {
                        iCarControlProtocol1?.unRegisterCallback(this)
                        if (statusCode == StatusCode.OK) {
                            try {
                                val result = GsonUtils.fromJson(it, CarControlRequestBean::class.java)
                                if (result.protocolId == 1011) {
                                    val data = JsonUtils.getString(it, "data")
                                    val resultState = GsonUtils.fromJson(data, ResultState::class.java)
                                    if (resultState.result == 1) {
                                        CommonLogUtils.logD(TAG, "setPosition:$position===success!")
                                    } else {
                                        CommonLogUtils.logD(TAG, "setPosition:$position====fail!")
                                    }
                                }
                            } catch (e: Throwable) {
                                e.printStackTrace()
                            }
                        }
                    }
                }
            })
        }, {
            handleException("setSeatMemoryState", it)
        })
    }

    /**
     * 注销香氛状态监听
     */
    fun unregisterFragranceDevice() {
        iCarControlProtocol?.unBindServer()
    }

    /**
     * 设置香氛打开关闭
     */
    fun setFragranceSwitch(isOpen: Boolean) {
        if (iCarControlProtocol1 == null) {
            init()
            return
        }
        if (mFragranceDeviceId != -1) {
            CustomApiUtils.requestCustomApi({
                val bean = CarControlRequestBean(
                    QueryFragranceSwitch(mFragranceDeviceId, 1, if (isOpen) 1 else 2),
                    "request",
                    1004,
                    GlobalConstant.APPLICATION_ID,
                    System.currentTimeMillis().toString(),
                    "",
                    AppUtils.getAppVersionName()
                )
                val json = GsonUtils.toJson(bean)
                CommonLogUtils.logD(TAG, "setFragranceSwitch--message==>$json")
                iCarControlProtocol1?.sendMessage(json, object : RequestCallback {
                    override fun onMessage(json: String?, statusCode: Int) {
                        json?.let {
                            iCarControlProtocol1?.unRegisterCallback(this)
                            if (statusCode == StatusCode.OK) {
                                try {
                                    if (bean.protocolId == 1004) {
                                        val data = JsonUtils.getString(it, "data")
                                        val result = GsonUtils.fromJson(data, ResultState::class.java)
                                        if (result.result != 1) {
                                            CommonToastUtils.show("设置失败")
                                            LiveEventBus.get<Int>(GlobalLiveEventConstants.KEY_FRAGRANCE_SWITCH_STATE)
                                                .post(
                                                    if (!isOpen) 1 else 0
                                                )
                                        } else {
                                            LiveEventBus.get<Int>(GlobalLiveEventConstants.KEY_FRAGRANCE_SWITCH_STATE)
                                                .post(
                                                    if (isOpen) 1 else 0
                                                )
                                        }
                                    }
                                } catch (e: Throwable) {
                                    e.printStackTrace()
                                }
                            }
                        }
                    }
                })
            }, {
                handleException("setFragranceSwitch=isOpen=>$isOpen", it)
            })
        }
    }

    class CarControlProtocolCallback : RequestCallback {
        override fun onMessage(json: String?, statusCode: Int) {
            CommonLogUtils.logI(TAG, "SOA方法：CarControlProtocolCallback -response- Json==>${json}")
            json?.let {
                if (statusCode == StatusCode.OK) {
                    try {
                        val bean = GsonUtils.fromJson(it, CarControlRequestBean::class.java)

                        if (bean.protocolId == 1002) {
                            val data = JsonUtils.getString(it, "data")
                            val deviceStateBean = GsonUtils.fromJson(data, SmartDeviceStateBean::class.java)
                            val list = deviceStateBean.list?.filter { item -> item.pid == "19" }
                            list?.let { device ->
                                if (device.isEmpty()) {
                                    mFragranceLinkState = false
                                } else {
                                    mFragranceLinkState = device[0].online == 1
                                    mFragranceDeviceId = device[0].id
                                }
                            }
                            if (list?.isEmpty() == true) mFragranceLinkState = false
                            LiveEventBus.get<Boolean>(GlobalLiveEventConstants.KEY_FRAGRANCE_LINK_STATE).post(
                                mFragranceLinkState
                            )
                            mSmartDeviceStateList.clear()
                            deviceStateBean.list?.forEach { aiDeviceStateBean ->
                                mSmartDeviceStateList.add(aiDeviceStateBean.pid)
                            }
                            LiveEventBus.get<ArrayList<String>>(
                                GlobalLiveEventConstants.CAR_CONTROL_SMART_DEVICE_STATE
                            ).post(
                                mSmartDeviceStateList
                            )

                        }
                    } catch (e: Throwable) {
                        e.printStackTrace()
                    }
                }
            }
        }
    }

    /**
     * 处理异常场景
     */
    private fun handleException(tag: String, e: Exception) {
        //        if (e is NullServiceException || e is RemoteException) {
        //            iCarControlProtocol = null
        //            init()
        //        }
        e.printStackTrace()
        CommonLogUtils.logD(TAG, "$tag,  $e")
    }
}