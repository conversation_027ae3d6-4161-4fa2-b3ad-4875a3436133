package com.dfl.smartscene.communication

import android.annotation.SuppressLint
import android.os.RemoteException
import com.dfl.androhermas.constant.ServiceConstant
import com.dfl.androhermas.constant.StatusCode
import com.dfl.androhermas.remote.client.ConnectionClient
import com.dfl.androhermas.remote.client.RequestCallback
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.util.AppUtils
import com.dfl.android.common.util.CustomApiUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.bean.communication.navi.KeywordBean
import com.dfl.smartscene.bean.communication.navi.LocalAddressInfoBean
import com.dfl.smartscene.bean.communication.navi.RequestMapBean
import com.dfl.smartscene.bean.communication.navi.RequestMessageType
import com.dfl.soacenter.entity.PoiItem
import com.dfl.soacenter.entity.SearchMapAddressBean
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.jeremyliao.liveeventbus.LiveEventBus

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/10/21
 * desc :在主进程与导航 搜索地址 应用间通讯
 * version: 1.0
 */
@Deprecated("重写了,先不要删除，这个是原来的SearchAddressManager，当如果出现问题替换回去")
object SearchAddressManager3 {
    private const val TAG = GlobalConstant.GLOBAL_TAG.plus("SearchAddressManager")

    //搜索
    @SuppressLint("StaticFieldLeak")
    private var iSearch: ConnectionClient? = null

    private var mNaviProtocolCallback = NaviProtocolCallback()

    val mGson = Gson()
    var mLocalAddressInfoBean: LocalAddressInfoBean? = null

    fun init() {
        CustomApiUtils.requestCustomApi({
            iSearch = ConnectionClient.Builder(CommonUtils.getApp(), ServiceConstant.Package.PACKAGE_NAVI)
                    .setReconnectMode(ConnectionClient.CONNECT_MODE_NOT_RECONNECT)
                    .setThreadPool(1, 1)
                    .build()
        }, {
            handleException("Search Address init", it)
        })
    }

    fun sendKeyWordToNavi(key: String) {
        CustomApiUtils.requestCustomApi(checkService(), {
            val bean = RequestMapBean(
                RequestMapBean.PRE_SEARCH_KEY_PROTOCOL_ID,
                System.currentTimeMillis().toString(),
                "",
                AppUtils.getAppVersionName(),
                "com.dfl.smartscene",
                RequestMessageType.REQUEST.content,
                KeywordBean(key)
            )
            val json = Gson().toJson(bean).replace("\\\\", "")
            iSearch?.sendMessage(json, mNaviProtocolCallback)
            CommonLogUtils.logE(TAG, "sendMessage=====>$json")

        }, {
            handleException("getKeyWordTaskIdByCustomApi", it)
        }, {
            CommonLogUtils.logE(TAG, "iSearch is null, reInit!")
            init()
        })
    }

    private fun checkService(): Boolean {
        if (iSearch == null) {
            return false
        }
        return true
    }

    /**
     * 处理异常场景
     */
    private fun handleException(tag: String, e: java.lang.Exception) {
        if (e is RemoteException) {
            iSearch = null
            init()
        }
        e.printStackTrace()
        CommonLogUtils.logD(TAG, "$tag,  $e")
    }

    class NaviProtocolCallback : RequestCallback {
        override fun onMessage(json: String?, statusCode: Int) {
            //            CommonLogUtils.logE(TAG, "json====>$json")
            json?.let {
                iSearch?.unBindServer()
                if (statusCode == StatusCode.OK) {
                    try {
                        val bean = mGson.fromJson(json, RequestMapBean::class.java)
                        if (bean.protocolId == RequestMapBean.PRE_SEARCH_KEY_PROTOCOL_ID) { //搜索地址接口信息
                            val type = object : TypeToken<RequestMapBean<SearchMapAddressBean>>() {}.type
                            val data = mGson.fromJson<RequestMapBean<SearchMapAddressBean>>(json, type)
                            val searchResult = data.data
                            LiveEventBus.get<List<PoiItem>>(GlobalLiveEventConstants.KEY_SEARCH_ADDRESS_RESULT_CALLBACK)
                                    .post(searchResult.Pois)
                        } else if (bean.protocolId == RequestMapBean.LOCAL_INFO_PROTOCOL_ID) { //获取当前地址
                            val type = object : TypeToken<RequestMapBean<LocalAddressInfoBean>>() {}.type
                            val data = mGson.fromJson<RequestMapBean<LocalAddressInfoBean>>(json, type)
                            mLocalAddressInfoBean = data.data
                            mLocalAddressInfoBean?.let {
                                mCarLocationListener?.carLocationChanged(it)
                            }
                        }
                    } catch (e: Throwable) {
                        e.printStackTrace()
                    }
                }
            }
        }
    }


    interface OnCarLocationCityListener {
        fun carLocationChanged(addressInfoBean: LocalAddressInfoBean)
    }

    private var mCarLocationListener: OnCarLocationCityListener? = null
    fun setOnCarLocationCityListener(listener: OnCarLocationCityListener) {
        mCarLocationListener = listener
    }
}