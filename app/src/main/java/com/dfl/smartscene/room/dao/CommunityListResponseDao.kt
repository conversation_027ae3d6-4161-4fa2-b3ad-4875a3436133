package com.dfl.smartscene.room.dao

import androidx.room.*
import com.dfl.smartscene.bean.community.reponse.CommunityListResponse


/**
 *Created by 钟文祥 on 2023/11/18.
 *Describer: 社区场景 列表响应类 dao
 */
@Dao
interface CommunityListResponseDao {

	//找出某个tab的第一页
	@Query(
		"select * from CommunityListResponse where tabId=:tabId and pageIndex=1"
	)
	fun findFirstPageByTabId(tabId: Int): CommunityListResponse?

	//找出所有tab的第一页
	@Query("select * from CommunityListResponse where pageIndex=1")
	fun findAllByFirstPage(): List<CommunityListResponse>?

	//找出所有
	@Query("select * from CommunityListResponse")
	fun findAll(): List<CommunityListResponse>?

	@Insert(onConflict = OnConflictStrategy.REPLACE)
	fun insert(item: CommunityListResponse?)

	@Query("DELETE FROM CommunityListResponse  where tabId=:tabId")
	fun deleteAllByTabId(tabId: Int): Int


	@Update
	fun update(items: List<CommunityListResponse>?): Int
}