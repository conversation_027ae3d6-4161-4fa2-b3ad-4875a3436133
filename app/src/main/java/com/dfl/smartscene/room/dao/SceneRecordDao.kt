package com.dfl.smartscene.room.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.dfl.smartscene.bean.main.SceneRecordEntity
import com.dfl.smartscene.room.SceneRecordTable

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/06/08
 * desc   :
 * version: 1.0
 */
@Dao
interface SceneRecordDao {
    /**
     * 插入
     * @param sceneRecord SceneRecordEntity
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(sceneRecord: SceneRecordEntity): Long

    /**
     * 查询未完成的场景执行记录
     *
     * @return List
     */
    @Query("SELECT * FROM ${SceneRecordTable.TABLE_NAME} WHERE ${SceneRecordTable.FIELD_START_SCENE_RESULT} = ${SceneRecordTable.VALUE_START_SCENE_RESULT_FAIL} ORDER BY ${SceneRecordTable.FIELD_FINISH_SCENE_TIME} DESC")
    fun queryNoFinish(): List<SceneRecordEntity>?

    /**
     * 分页查询所有场景执行记录
     * @param limit 查询多少条数据
     * @param offset 从第几条开始查询
     * @return List
     */
    @Query("SELECT * FROM ${SceneRecordTable.TABLE_NAME} ORDER BY ${SceneRecordTable.FIELD_FINISH_SCENE_TIME} DESC Limit:limit Offset:offset")
    fun queryAllData(limit: Int, offset: Int): List<SceneRecordEntity>?

    /**
     * 根据id删除记录.
     *
     * @param srId Long
     */
    @Query("DELETE FROM ${SceneRecordTable.TABLE_NAME} WHERE ${SceneRecordTable.FIELD_ID} = :srId")
    fun delete(srId: Long)

    /**
     * 根据时间删除记录.
     *
     * @param timeMillis Long
     */
    @Query("DELETE FROM ${SceneRecordTable.TABLE_NAME} WHERE ${SceneRecordTable.FIELD_FINISH_SCENE_TIME} < :timeMillis")
    fun deleteUntil(timeMillis: Long)

    /**
     * 查询所有场景执行记录
     *
     * @return List
     */
    @Query("DELETE FROM ${SceneRecordTable.TABLE_NAME}")
    fun deleteAllData()
}