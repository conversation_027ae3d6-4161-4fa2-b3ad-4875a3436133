package com.dfl.smartscene.room

import androidx.room.Room
import androidx.room.migration.Migration
import androidx.room.withTransaction
import androidx.sqlite.db.SupportSQLiteDatabase
import com.dfl.android.commonlib.CommonUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.transform
import kotlinx.coroutines.launch

/**
 * Created with Android Studio.
 *
 * <p>
 * {@author} ly-huangas
 * 2022/4/19
 * </p>
 */
object DbManager {
	// common
	private const val DB_NAME = "smart_scene_v1"
	private val MIGRATIONS = arrayOf(MigrationV1)

	// db instance
	@Volatile
	private var db: AppDatabase? = null

	fun initAsync() {
		CoroutineScope(Dispatchers.IO).launch {
			synchronized(DB_NAME) {
				db = Room.databaseBuilder(
					CommonUtils.getApp(),
					AppDatabase::class.java, DB_NAME
				)
					//.addCallback(CreatedCallBack)
					.addMigrations(*MIGRATIONS)
					.build()
			}
		}
	}

	fun getDBMaster(): AppDatabase? {
		if (db == null) {
			synchronized(DbManager::class.java) {
				if (db == null) {
					db = Room.databaseBuilder(
						CommonUtils.getApp(),
						AppDatabase::class.java,
						DB_NAME
					).addMigrations(*MIGRATIONS)
						.build()
				}
			}
		}
		return db
	}

	fun <T, R> Flow<T>.dbTransform(
		block: suspend (value: T, db: AppDatabase) -> R
	): Flow<R> {
		return transform { value ->
			if (db == null) {
				synchronized(DB_NAME) {
					if (db == null) {
						throw IllegalAccessException("Please call initAsync(Application) first")
					}
				}
			}
			val database = db
			database ?: throw IllegalAccessError("init DatabaseManager first")
			emit(database.withTransaction {
				block.invoke(value, database)
			})
		}
	}

	fun <R> db(
		block: suspend (db: AppDatabase) -> R
	): Flow<R> {
		if (db == null) {
			synchronized(DB_NAME) {
				if (db == null) {
					throw IllegalAccessException("Please call initAsync(Application) first")
				}
			}
		}
		val database = db
		database ?: throw IllegalAccessError("init DatabaseManager first")
		return flow {
			emit(database.withTransaction {
				block.invoke(database)
			})
		}
	}

	/*private object CreatedCallBack : RoomDatabase.Callback() {
		override fun onCreate(db: SupportSQLiteDatabase) {
			//在新装app时会调用，调用时机为数据库build()之后，数据库升级时不调用此函数
			MIGRATIONS.map {
				MigrationV1.migrate(db)
			}
		}
	}*/

	private object MigrationV1 : Migration(1, 2) {
		override fun migrate(database: SupportSQLiteDatabase) {
			// 1、 创建临时表
			database.execSQL(
				"CREATE TABLE `temp_${SceneRecordTable.TABLE_NAME}` ("
						+ "`${SceneRecordTable.FIELD_ID}` INTEGER PRIMARY KEY AUTOINCREMENT, "
						+ "`${SceneRecordTable.FIELD_SCENARIO_ID}` TEXT NOT NULL, "
						+ "`${SceneRecordTable.FIELD_SCENE_NAME}` TEXT NOT NULL, "
						+ "`${SceneRecordTable.FIELD_FINISH_SCENE_TIME}` INTEGER NOT NULL,"
						+ "`${SceneRecordTable.FIELD_START_SCENE_RESULT}` INTEGER NOT NULL"
						+ ")"
			)

			//2 复制数据
			database.execSQL(
				"INSERT INTO `temp_${SceneRecordTable.TABLE_NAME}` " +
						"(${SceneRecordTable.FIELD_SCENARIO_ID}," +
						"${SceneRecordTable.FIELD_SCENE_NAME}," +
						"${SceneRecordTable.FIELD_FINISH_SCENE_TIME}," +
						"${SceneRecordTable.FIELD_START_SCENE_RESULT}) " +
						"SELECT " +
						"scenario_group_id," +
						"${SceneRecordTable.FIELD_SCENE_NAME}," +
						"${SceneRecordTable.FIELD_FINISH_SCENE_TIME}," +
						"${SceneRecordTable.FIELD_START_SCENE_RESULT} " +
						"FROM ${SceneRecordTable.TABLE_NAME}"
			)

			//3 删除原来表
			database.execSQL("DROP TABLE ${SceneRecordTable.TABLE_NAME}")

			//4 重命名
			database.execSQL("ALTER TABLE temp_${SceneRecordTable.TABLE_NAME} RENAME TO ${SceneRecordTable.TABLE_NAME}")
		}
	}
}