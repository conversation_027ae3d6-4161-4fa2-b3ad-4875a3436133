package com.dfl.smartscene.room

/**
 * Created with Android Studio.
 *
 * <p>
 * {@author} ly-huangas
 * 2022/4/18
 * </p>
 */
object SceneRecordTable {
    // table scene_record
    const val TABLE_NAME = "scene_record"
    // == field
    const val FIELD_ID = "sr_id"// 表id
    const val FIELD_SCENARIO_ID = "scenario_id"// 场景id
    const val FIELD_SCENE_ICON = "scene_icon"// 场景图标
    const val FIELD_SCENE_NAME = "scene_name"// 场景名称
    const val FIELD_FINISH_SCENE_TIME = "finish_scene_time"// 时间
    const val FIELD_START_SCENE_RESULT = "start_scene_result"// 执行结果
    // == == value
    const val VALUE_START_SCENE_RESULT_UNKNOW = -1// 场景正在执行
    const val VALUE_START_SCENE_RESULT_SUCCESS = 0// 场景执行结果为成功
    const val VALUE_START_SCENE_RESULT_FAIL = 1// 场景执行结果为失败
}