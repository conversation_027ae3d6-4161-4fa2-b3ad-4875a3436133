package com.dfl.smartscene.room.dao

import androidx.room.*
import com.dfl.smartscene.bean.community.reponse.CommunityUserInfo

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/05/10
 * desc : 账号页响应Dao
 * version: 1.0
 */
@Dao
interface CommunityUserInfoDao {
	@Insert(onConflict = OnConflictStrategy.REPLACE)
	fun insert(communityUserInfo: CommunityUserInfo)

	@Update
	fun update(communityUserInfo: CommunityUserInfo): Int

	@Query("SELECT * FROM CommunityUserInfo")
	fun queryAll(): List<CommunityUserInfo>?

	@Query("SELECT * FROM CommunityUserInfo where uuid=:uuid")
	fun queryByUuid(uuid: String): CommunityUserInfo?

	@Query("DELETE FROM CommunityUserInfo where uuid=:uuid")
	fun deleteByUuid(uuid: String): Int

	@Query("DELETE FROM CommunityUserInfo")
	fun deleteAll(): Int
}