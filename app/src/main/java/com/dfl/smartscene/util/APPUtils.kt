package com.dfl.smartscene.util

import android.app.ActivityManager
import android.content.Context
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.bean.community.datasource.CommunityRepository
import com.dfl.smartscene.bean.community.datasource.UserCenterRepository
import com.dfl.smartscene.bean.community.reponse.CommunityListResponse
import com.dfl.smartscene.bean.community.reponse.CommunityUserInfo
import com.dfl.smartscene.bean.community.request.CommunityTabType
import com.dfl.smartscene.room.DbManager
import com.dfl.smartscene.room.helper.DBHelper
import com.dfl.smartscene.room.helper.OnDaoFindsListener

/**
 *Created by 钟文祥 on 2023/12/5.
 *Describer: app上一些操作
 */
object APPUtils {
    private val TAG = GlobalConstant.GLOBAL_TAG.plus("APPUtils")
    fun initCommunityCachedData() {
        val mRvRepository = CommunityRepository.getInstance()
        val dao = DbManager.getDBMaster()?.CommunityListResponseDao()

        DBHelper.daoFinds(object : OnDaoFindsListener<List<CommunityListResponse>?> {
            override fun call(): List<CommunityListResponse>? {
                return dao?.findAllByFirstPage()
            }

            override fun onSuccess(values: List<CommunityListResponse>?) {
                values?.forEachIndexed { index, item ->
                    mRvRepository.mCachedDataMap[CommunityTabType.values()[item.tabId]] = item
                }
            }

            override fun onError(e: Throwable?) {
                CommonLogUtils.logE(TAG, "initCommunityCachedData error:$e")
            }
        })
    }

    /**
     * 初始化本地用户数据到内存
     */
    fun initUserCenterCachedData() {
        val userCenterRepository = UserCenterRepository.getInstance()
        val dao = DbManager.getDBMaster()?.CommunityUserInfoDao()
        DBHelper.daoFinds(object : OnDaoFindsListener<List<CommunityUserInfo>> {
            override fun call(): List<CommunityUserInfo>? {
                return dao?.queryAll()
            }

            override fun onError(e: Throwable?) {
                CommonLogUtils.logE(TAG, "initUserCenterCachedData error:$e")
            }

            override fun onSuccess(values: List<CommunityUserInfo>?) {
                values?.forEach { item ->
                    userCenterRepository.cachedDataMap[item.uuid] = item
                }
            }
        })
    }

    /**获取当前的activity是哪个*/
    @JvmStatic
    fun getCurrentActivityName(context: Context): String {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val runningTasks = activityManager.appTasks

        if (runningTasks.isNotEmpty()) {
            val topActivity = runningTasks[0].taskInfo.topActivity
            val name = topActivity?.className ?: ""
            CommonLogUtils.logI(TAG, "getCurrentActivityName（）当前的activity是：" + name)
            return name
        }
        return ""
    }
}