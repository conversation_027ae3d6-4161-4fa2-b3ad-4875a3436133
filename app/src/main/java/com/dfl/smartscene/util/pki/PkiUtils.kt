package com.dfl.smartscene.util.pki

import android.annotation.SuppressLint
import android.content.Context
import cn.com.jit.ida.util.pki.encoders.Base64
import cn.com.jit.mctk.cert.PNXCertContext
import cn.com.jit.mctk.cert.pojo.CertEntry
import cn.com.jit.mctk.cert.support.CertSupport
import cn.com.jit.mctk.common.constant.PNXConfigConstant
import cn.com.jit.mctk.common.init.PNXClientContext
import cn.com.jit.mctk.common.support.PnxKeySupport
import cn.com.jit.mctk.crypto.PNXCryptoContext
import cn.com.jit.mctk.crypto.constant.CryptoConfigConstant
import cn.com.jit.mctk.os.context.PnxContext
import com.dfl.android.common.util.FileUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.BuildConfig
import com.dfl.smartscene.apiweb.service.PKIService
import com.dfl.smartscene.apiweb.utils.ApiException
import com.dfl.smartscene.apiweb.utils.RxSubscriber
import com.dfl.android.common.customapi.AppCommonManager
import com.dfl.android.common.global.GlobalConstant
import io.reactivex.disposables.Disposable
import kotlinx.coroutines.*
import java.io.File
import java.io.FileInputStream
import java.io.InputStream
import java.security.KeyStore
import java.security.MessageDigest
import java.security.cert.CertificateFactory
import java.util.*
import javax.net.ssl.*

/**
 *Created by 钟文祥 on 2024/8/27.
 *Describer:
 */
@SuppressLint("StaticFieldLeak")
object PkiUtils {

    /** PKI API服务 测试  */
    const val TEST_PKI_APPID = "appiNjyyL63ckG9vXFcXKubdJy3Q4FZpJYC"
    const val TEST_PKI_APPKEY =
        "YR08tV9CqcB539T3g4uT9LZbFhqawEJ1OzGyDa8gSJKNTF2FvOksdM5VnGjFOE0h1dkNYdYc2zWGmEZrmVM7DW1MrWnVEeScLpT6Mh6WoAisGHjWI14DnK798UMZPNAn"
    const val TEST_PKI_PUBLIC_KEY =
        "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEm+NnzplARGQDoh863ZMEI5cCMd/jSAcqqVlBcp4cbhqtoaG1NlRaSF9Ps2S/BqEmhj+/yE4mkSZJu5f5+i79sA=="
    const val TEST_PKI_PRIVATE_KEY =
        "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgbDF4taOHIJMeA/uh2yjGsC+XYZt8idrF2J/gVXBLGGmgCgYIKoEcz1UBgi2hRANCAARLbCKYbDwos6x1OEFVc1PNcDKDoHRZ2wzx5ZeT7Fnn6UwBAyIK9wf4/nNOpTjcWo892UqbIavakl6x6Ji5nOW/"

    /** PKI API服务 商用  */
    const val BUSINESS_PKI_APPID = "appiNjyyL63ckG9vXFcXKubdJy3Q4FZpJYC"
    const val BUSINESS_PKI_APPKEY =
        "YR08tV9CqcB539T3g4uT9LZbFhqawEJ1OzGyDa8gSJKNTF2FvOksdM5VnGjFOE0h1dkNYdYc2zWGmEZrmVM7DW1MrWnVEeScLpT6Mh6WoAisGHjWI14DnK798UMZPNAn"
    const val BUSINESS_PKI_PUBLIC_KEY =
        "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAES2wimGw8KLOsdThBVXNTzXAyg6B0WdsM8eWXk+xZ5+lMAQMiCvcH+P5zTqU43FqPPdlKmyGr2pJeseiYuZzlvw=="
    const val BUSINESS_PKI_PRIVATE_KEY =
        "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgbDF4taOHIJMeA/uh2yjGsC+XYZt8idrF2J/gVXBLGGmgCgYIKoEcz1UBgi2hRANCAARLbCKYbDwos6x1OEFVc1PNcDKDoHRZ2wzx5ZeT7Fnn6UwBAyIK9wf4/nNOpTjcWo892UqbIavakl6x6Ji5nOW/"

    /** 社区场景API服务 最终  */
    const val FINAL_PKI_APPID = TEST_PKI_APPID
    const val FINAL_PKI_APP_KEY = TEST_PKI_APPKEY
    const val FINAL_PKI_PUBLIC_KEY = TEST_PKI_PUBLIC_KEY
    const val FINAL_PKI_PRIVATE_KEY = TEST_PKI_PRIVATE_KEY


    private const val TAG = GlobalConstant.GLOBAL_TAG.plus("PkiUtils")

    private var pnxClientContext: PNXClientContext? = null

    /**证书管理类*/
    private var mCertSupport: CertSupport? = null

    /**是否已经安装正式证书*/
    private var isInstall = false

    private lateinit var daId: String
    private lateinit var mContext: Context

    /**一、SDK初始化*/
    fun initPNX(context: Context) {
        mContext = context
        CoroutineScope(Dispatchers.IO).launch {
            try {
                daId = async {
                    AppCommonManager.getDAId()
                }.await()

                //1、获取证书管理对象
                //初始化运行环境
                pnxClientContext = PNXClientContext.getInstance(PnxContext.createPnxContext(context), "0")
                //初始化证书管理器  软证书存储区模式
                pnxClientContext?.initKeyStore(PNXConfigConstant.STORE_MODE_STORAGE, "")
                //获取证书管理模块初始化上下文对象 ,获得一个CertSupport 的实例，用以提供证书管理的相关接口
                mCertSupport = PNXCertContext.getInstance(pnxClientContext).createCertSupport()

                //2、设置PKI正式证书保存路径
                val certPath = getCertPath()
                // PKI正式证书存储路径
                if (FileUtils.createOrExistsDir(certPath)) {
                    CommonLogUtils.logI(TAG, "PKI初始化，设置正式证书存放路径：$certPath")
                    PnxKeySupport.getInstance(pnxClientContext).setCertFilePath(certPath)
                }

                if (isInstallCert()) {
                    CommonLogUtils.logE(TAG, "证书已经下载成功，则不需要再下载")
                    return@launch
                } else { //证书无效或不存在
                    //3、获取正式证书
                    downloadPkiCert2()
                }
            } catch (e: Exception) {
                CommonLogUtils.logE(TAG, "SDK初始化失败: " + e.message)
                e.printStackTrace()
            }
        }
    }


    //2、获取正式证书 ，null：无效，非空：有效
    private var userCertEntry: CertEntry? = null
        get() {
            if (null == field) {
                try {
                    for (certEntry in mCertSupport?.certList!!) {
                        val subject = certEntry.subject
                        //1.证书是否存在 2.证书主题是否匹配
                        if (subject.isNotEmpty() && subject.startsWith(getUserSubject(daId))) {
                            field = certEntry
                            return field
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            return field
        }

    //3、生成p10参数 ，用证书主题和证书密码作为参数
    private fun createP10Data(): String {
        CommonLogUtils.logI(TAG, "开始创建p10数据")
        val p10Data = mCertSupport?.requestP10WitchCredential(
            getUserSubject4P10(daId), getOfficialCertPassword(), 1024, "RSA"
        )
        CommonLogUtils.logI(TAG, "p10Data数据为：$p10Data")
        return p10Data!!
    }

    //4、请求接口下载PKI证书
    private fun downloadPkiCert2() {
        val p10 = createP10Data()
        CommonLogUtils.logI(TAG, "请求接口下载PKI证书时用的daId:" + daId)
        PKIService.downloadPkiCert(p10, daId).subscribe(object : RxSubscriber<PkiCertApplyResponse>() {
            override fun onAddDisposable(d: Disposable?) {
            }

            override fun onApiNext(value: PkiCertApplyResponse?) {
                // 生成PKI证书
                createPkiCert(value)
            }

            override fun onApiError(ex: ApiException?) {
                CommonLogUtils.logE(TAG, "api下载证书失败:" + ex?.message)
            }
        })
    }

    /** 5、生成PKI证书 */
    private fun createPkiCert(result: PkiCertApplyResponse?) {
        // 这里是会阻塞进程的，但是由于下载动作开始的时候已经切换到io线程了，所以这里不会造成anr
        runBlocking {
            try {
                val p7b = result?.p7b
                if (p7b.isNullOrEmpty()) {
                    CommonLogUtils.logE(TAG, "p7b为空，证书生成失败")
                } else {
                    CommonLogUtils.logE(TAG, "p7b = $p7b")
                    // 删除原来的证书
                    delCert()
                    clearCert()
                    val certResult = mCertSupport?.importCertWithP7b(
                        getOfficialCertPassword(), "RSA", p7b, "", "", "", ""
                    )
                    CommonLogUtils.logI(TAG, "吉大sdk导入证书 ： $certResult")
                    if (isInstallCert()) {
                        CommonLogUtils.logE(TAG, "证书生成成功，已存在")
                    } else {
                        CommonLogUtils.logE(TAG, "证书生成失败")
                    }

                }
            } catch (e: Exception) {
                CommonLogUtils.logE(TAG, "吉大sdk导入证书异常：" + e.message)
                e.printStackTrace()
            }
        }
    }

    @JvmStatic
    fun isInstallCert(): Boolean {
        return false // TODO: 先写死 没有证书，请求时候就不携带证书 

        if (!isInstall) {
            userCertEntry?.let { it ->
                val subject = it.subject
                if (subject.isNotEmpty()) {
                    subject.startsWith(getUserSubject(daId)).also { it2 -> isInstall = it2 }
                }
            } ?: run { isInstall = false }
        }
        CommonLogUtils.logI(TAG, "是否有证书: " + isInstall)
        return isInstall
    }

    private fun delCert(): Boolean {
        var isDel = false
        userCertEntry?.let {
            try {
                isDel = mCertSupport?.deleteCert(it.ailas, getOfficialCertPassword()) == true
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        return isDel
    }

    private fun clearCert() {
        userCertEntry = null
        isInstall = false
    }


    /**
     * @param stairIn Stair.cer证书的输入流
     * @param secondIn Second.cer证书的输入流
     * */
    @JvmStatic
    fun initTrustManagers(
        stairIn: InputStream?, secondIn: InputStream?
    ): Array<out TrustManager> {
        val certificateFactory = CertificateFactory.getInstance("X.509")
        val trustStore = KeyStore.getInstance(KeyStore.getDefaultType())
        trustStore.load(null)
        trustStore.setCertificateEntry("stair", certificateFactory.generateCertificate(stairIn))
        trustStore.setCertificateEntry("second", certificateFactory.generateCertificate(secondIn))
        val tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm())
        tmf.init(trustStore)
        val trustManagers = tmf.trustManagers
        if (trustManagers.size != 1 || trustManagers[0] !is X509TrustManager) {
            throw IllegalStateException("Unexpected default trust managers:$trustManagers")
        }
//        val trustManager = trustManagers[0] as X509TrustManager
        return trustManagers
    }

    /**使用正式证书构建SSLSocketFactory*/
    @JvmStatic
    fun createSSLSocketFactory4PKI(
        trustManagers: Array<TrustManager>?
    ): SSLSocketFactory {
        val keyStore = KeyStore.getInstance("PKCS12") //证书类型
        //获取正式证书流
        keyStore.load(getOfficialPfxInputStream(), getOfficialCertPassword().toCharArray())

        val keyManagerFactory = KeyManagerFactory.getInstance("X509")
        keyManagerFactory.init(keyStore, getOfficialCertPassword().toCharArray())

        val sslContext = SSLContext.getInstance("TLS")
        sslContext.init(keyManagerFactory.keyManagers, trustManagers, null)
        return sslContext.socketFactory
    }

    /**
     * 获取正式证书的输入流
     *
     * @return InputStream
     */
    @SuppressLint("StaticFieldLeak")
    private fun getOfficialPfxInputStream(): InputStream? {
        userCertEntry?.let {
            CommonLogUtils.logI(
                TAG, "certEntry.getSubject() = ${it.subject}; certEntry.getUserSubject() = " + getUserSubject(
                    daId
                )
            )
            val fileName = mCertSupport?.getCertFilePath(it.ailas, getOfficialCertPassword())
            if (fileName?.isNotBlank() == true) {
                return FileInputStream(File(fileName))
            }
        }
        return null
    }

    /**数据加签 产生PKCS7格式签名数据*/
    suspend fun sign(plainData: ByteArray): ByteArray {
        val pKCS7Signer = PNXCryptoContext.getInstance(pnxClientContext).createPKCS7Signer()
        pKCS7Signer.setDigestAlg(CryptoConfigConstant.SHA256)
        return pKCS7Signer.sign(plainData)
    }

    /**
     * 验签,只是验签
     *
     * @param org     原始参数字符串
     * @param dString 加签后的字符串
     * @return true表示验签通过
     */
    suspend fun verify(org: String, dString: String): Boolean {
        var isSuccess = false
        try {
            val pKCS7Signer = PNXCryptoContext.getInstance(pnxClientContext).createPKCS7Signer()
            pKCS7Signer.setDigestAlg(CryptoConfigConstant.SHA256)
            //签名结果  + 签名原文
            isSuccess = pKCS7Signer.verify(Base64.decode(dString.toByteArray()), org.toByteArray())
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return isSuccess
    }


    /**
     * 获取PKI正式证书保存路径
     *
     * @return PKI正式证书保存路径
     */
    fun getCertPath(): String {
        return "${mContext.filesDir}/cert"
    }

    /**获取证书主题，用于筛选出有效的正式证书 ,，由于后台主题规则变更，所以我们这边修改为startsWith(getUserSubject())来比较*/
    fun getUserSubject(daId: String?): String { // TODO: 钟文祥
        return if (BuildConfig.DEBUG) {
            "CN=TEST_${daId}_${mContext.packageName}"
        } else {
            "CN=${daId}_${mContext.packageName}"
        }
    }

    /**获取证书主题，用于获取p10数据*/
    fun getUserSubject4P10(daId: String?): String {
        return getUserSubject(daId)
    }

    /**获取KPI正式证书密码*/
    fun getOfficialCertPassword(): String {
        return "" // TODO: 钟文祥 
    }

    /**获取预置证书Stair证书的输入流*/
    @JvmStatic
    fun getStairCerInputStream(): InputStream? {
        return mContext.assets?.open("stair.cer") // TODO: 钟文祥
    }

    /**获取预置证书Second证书的输入流*/
    @JvmStatic
    fun getSecondCerInputStream(): InputStream? {
        return mContext.assets?.open("second.cer") // TODO: 钟文祥
    }

    /**用于请求下载证书的参数，目前用的是包名，但是防止以后有变动，由各个项目传入*/
    fun getDeviceId(): String {
        return mContext.packageName
    }


    /**获取预置证书pfx证书的输入流*/
    @JvmStatic
    fun getDefaultPfxInputStream(): InputStream? {
        return mContext.assets?.open("nissan.pfx")
    }

    /**获取PKI预置证书密码*/
    @JvmStatic
    fun getPfxCertPassword(): String {
        return if (BuildConfig.DEBUG) {
            "20210305@pki"
        } else {
            "20201111@pki"
        }
    }


    /**
     * 获取字符串的 MD5
     */
    @JvmStatic
    fun encode(str: String): String {
        try {
            val md5 = MessageDigest.getInstance("MD5")
            md5.update(str.toByteArray(charset("UTF-8")))
            val messageDigest = md5.digest()
            val hexString = StringBuilder()
            for (b in messageDigest) {
                hexString.append(String.format("%02X", b))
            }
            return hexString.toString().lowercase(Locale.ROOT)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return ""
    }
}