package com.dfl.smartscene.util

import android.content.Context
import android.net.ConnectivityManager
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.application.SceneApplication
import com.dfl.smartscene.customapi.TcuManager
import com.dfl.android.common.global.GlobalConstant

/**
 *Created by 钟文祥 on 2023/11/9.
 *Describer: 网络相关
 */
object NetWorkUtils {

    private const val TAG = GlobalConstant.GLOBAL_TAG.plus("Community_NetWorkUtils")

    enum class NetWorkType(var value: Int) {
        HaveNetWork(0),                //有开wifi或有付费流量的tcu

        //		WeakNetwork(1),                //在HaveNetWork情况下 弱网
        //		NoFlow(2),                     //无开wifi和有开tcu 但没有付费流量
        DeviceNoNetWork(1);            //无开wifi和无开tcu

    }

    /** 是否联网  */
    @JvmStatic
    fun isConnectNetWork(): Boolean {
        return checkNetWork() == NetWorkType.HaveNetWork
    }


    /** 检查 网络状态  */
    @JvmStatic
    fun checkNetWork(): NetWorkType {
        var netWorkType: NetWorkType = NetWorkType.DeviceNoNetWork
        //获取当前网络的连接服务
        val connectivityManager =
            SceneApplication.getContextObject()?.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        if (connectivityManager == null) return netWorkType

        //获取活动的网络连接信息
        val info = connectivityManager.getActiveNetworkInfo()
        if (info != null) {
            CommonLogUtils.logI(TAG, "连接到的网络类型为:" + info.typeName)
            //只要wifi连接了，就认为是连上了网络
            if ("WIFI" == info.typeName) {
                netWorkType = NetWorkType.HaveNetWork
                CommonLogUtils.logI(TAG, "网络连接状态已连WIFI，网络正常")
                return netWorkType
            }
        }

        //无开wifi的情况下 判断是否连了tcu
        val isTcuConnect: Boolean = TcuManager.isConnect()
        if (isTcuConnect) {
            netWorkType = NetWorkType.HaveNetWork
            CommonLogUtils.logI(TAG, "网络连接状态已连TCU")

            //			netWorkType = if (getIsHavePayFlow()) {
            //				CommonLogUtils.logI(TAG, "网络连接状态已连TCU，且有付费流量")
            //				NetWorkType.HaveNetWork
            //			} else {
            //				CommonLogUtils.logI(TAG, "网络连接状态已连TCU，但无付费流量")
            //				NetWorkType.NoFlow
            //			}
            //
            //			if (netWorkType == NetWorkType.HaveNetWork && isTcuWeakNetwork()) {
            //				CommonLogUtils.logI(TAG, "网络连接状态已连TCU，且有付费流量，但TCU为弱网")
            //				netWorkType = NetWorkType.WeakNetwork
            //			}
        } else {
            netWorkType = NetWorkType.DeviceNoNetWork
            CommonLogUtils.logI(TAG, "车机设备没有连上网")
        }

        return netWorkType
    }

    /**
     * 判断是否有网络
     * @param haveNetwork 有网络回调
     * @param noNetwork 无网络回调
     * @return true有网
     */
    fun haveNetwork(haveNetwork: () -> Unit, noNetwork: () -> Unit = {}): Boolean {
        val type = checkNetWork()
        if (type == NetWorkType.HaveNetWork) {
            haveNetwork.invoke()
        } else {
            UIUtils.showToastNetError(type)
            noNetwork.invoke()
        }
        return type == NetWorkType.HaveNetWork
    }
    //	/**通过流量中心判断是否有付费流量*/
    //	private fun getIsHavePayFlow(): Boolean {
    //		return true;
    //	}
    //
    //	/**判断wifi是否弱网*/
    //	private fun isWifiWeakNetwork(): Boolean {
    //		return false;
    //	}
    //
    //	/**判断tcu是否弱网*/
    //	private fun isTcuWeakNetwork(): Boolean {
    //		return false
    //	}
}