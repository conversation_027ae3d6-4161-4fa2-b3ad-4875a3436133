package com.dfl.smartscene.util;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.os.Bundle;
import android.os.Handler;
import android.text.Html;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.MotionEvent;
import android.view.TouchDelegate;
import android.view.View;
import android.view.ViewParent;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.databinding.BindingAdapter;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SimpleItemAnimator;
import androidx.viewbinding.ViewBinding;

import com.airbnb.lottie.LottieAnimationView;
import com.airbnb.lottie.LottieComposition;
import com.airbnb.lottie.LottieCompositionFactory;
import com.bumptech.glide.Glide;
import com.bumptech.glide.Priority;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.dfl.android.animationlib.LoadingImageView;
import com.dfl.android.animationlib.ScaleImageButton;
import com.dfl.android.common.customapi.ConfigManager;
import com.dfl.android.common.util.CommonToastUtils;
import com.dfl.android.commonlib.CommonUtils;
import com.dfl.android.commonlib.KeyboardUtils;
import com.dfl.smartscene.R;
import com.dfl.smartscene.apiweb.utils.ApiException;
import com.dfl.smartscene.apiweb.utils.ExceptionEngine;
import com.dfl.smartscene.bean.edit.CustomSeekBarBean;
import com.dfl.smartscene.customapi.SoundManager;
import com.dfl.smartscene.ui.overlay.time.date.adapter.ArrayWheelAdapter;
import com.dfl.smartscene.widget.ClassicsFooter4All;
import com.dfl.smartscene.widget.ClassicsHeader4All;
import com.dfl.smartscene.widget.ClassicsHeader4Banner;
import com.dfl.smartscene.widget.seekbar.CustomSeekBar;
import com.dfl.smartscene.widget.wheel.OnItemScrollListener;
import com.dfl.smartscene.widget.wheel.WheelView;
import com.dfl.smartscene.widget.wheel.listener.OnItemSelectedListener;
import com.google.android.material.tabs.TabLayout;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.footer.ClassicsFooter;
import com.scwang.smartrefresh.layout.header.ClassicsHeader;
import com.scwang.smartrefresh.layout.internal.InternalClassics;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.autosize.utils.AutoSizeUtils;


/**
 * Created by 钟文祥 on 2018/7/31.
 * Describer: UI 工具类
 */
public class UIUtils {

    /**
     * @param context context
     * @param recyclerView 要初始化rv
     * @param layout RecyclerView.LayoutManager
     * @param isAddItemDecoration 是否添加Android自带的分割线
     */
    public static void initRecyclerView(Context context, RecyclerView recyclerView,
                                        RecyclerView.LayoutManager layout,
                                        boolean isAddItemDecoration) {
        recyclerView.setLayoutManager(layout);
        recyclerView.setOverScrollMode(View.OVER_SCROLL_NEVER);
        //如果可以确定每个item的高度是固定的，设置这个选项可以提高性能
        recyclerView.setHasFixedSize(true);
        if (isAddItemDecoration) {
            //添加Android自带的分割线
            recyclerView.addItemDecoration(new DividerItemDecoration(context, DividerItemDecoration
                    .VERTICAL));

            //            分割距离
            //            recyclerView.addItemDecoration(new EquallySpaceDecoration(
            //                    AutoSizeUtils.dp2px(requireContext(), 52f),
            //                    AutoSizeUtils.dp2px(requireContext(), 52f)));
        }
        RecyclerView.ItemAnimator animator = recyclerView.getItemAnimator();
        if (animator instanceof SimpleItemAnimator) {
            ((SimpleItemAnimator) animator).setSupportsChangeAnimations(false);
        }
    }

    /**
     * 显示异常和加载页面
     * 布局 scene_layout_comment_req_res
     *
     * @param apiException 异常原因
     * @param listener 重新加载点击事件
     */
    private static void show(ViewBinding VB, ApiException apiException,
                             View.OnClickListener listener) {
        if (VB == null) return;
        RelativeLayout rl_refresh = VB.getRoot().findViewById(R.id.rl_refresh);
        rl_refresh.setVisibility(View.VISIBLE);
        LoadingImageView lottieAnimationView = VB.getRoot().findViewById(R.id.lottie_loading);
        TextView tvReqLoading = VB.getRoot().findViewById(R.id.tv_req_loading);

        ImageView ivErrorIcon = VB.getRoot().findViewById(R.id.iv_error_icon);
        TextView tvError = VB.getRoot().findViewById(R.id.tv_error);
        ScaleImageButton tvReloading = VB.getRoot().findViewById(R.id.tv_reloading);

        //显示加载中
        if (apiException == null) {
            lottieAnimationView.setVisibility(View.VISIBLE);
            lottieAnimationView.playAnimation();
            tvReqLoading.setVisibility(View.VISIBLE);

            ivErrorIcon.setVisibility(View.GONE);
            tvError.setVisibility(View.GONE);
            tvReloading.setVisibility(View.GONE);
        } else {
            lottieAnimationView.setVisibility(View.GONE);
            tvReqLoading.setVisibility(View.GONE);

            ivErrorIcon.setVisibility(View.VISIBLE);
            tvError.setVisibility(View.VISIBLE);
            tvReloading.setVisibility(View.VISIBLE);
            tvReloading.setOnClickListener(listener);

            if (apiException.getHttpCode() == ExceptionEngine.CONNECT_NETWORD_ERROR) {
                ivErrorIcon.setImageResource(R.drawable.scene_img_common_noweb);
                tvError.setText(R.string.scene_text_web_network_unable);
            } else if (apiException.getHttpCode() == ExceptionEngine.DATA_SIZE_0) {
                ivErrorIcon.setImageResource(R.drawable.scene_img_common_nodata);
                tvError.setText(R.string.scene_text_community_unable);
            } else {
                ivErrorIcon.setImageResource(R.drawable.scene_img_common_error_data);
                tvError.setText(R.string.scene_text_web_data_unable);
            }
        }
    }

    /**
     * 当页面onDestroy清空req_res的动画
     *
     * @param VB 包含布局 scene_layout_comment_req_res
     */
    public static void clearReqResLottieOnDestroy(ViewBinding VB) {
        LoadingImageView lottieAnimationView = VB.getRoot().findViewById(R.id.lottie_loading);
        if (lottieAnimationView != null) {
            lottieAnimationView.cancelAnimation();
            lottieAnimationView.clearAnimation();
        }
    }

    /**
     * 预先同步加载加载中动效，防止显示时间过短导致没有动效显示
     * <a href="https://blog.csdn.net/ameryzhu/article/details/80047958">lottie加载动画，第一次有延迟问题</a>
     */
    public static void initLoadingLottieSync(ViewBinding VB) {
        LottieAnimationView lottieAnimationView = VB.getRoot().findViewById(R.id.lottie_loading);
        if (lottieAnimationView != null) {
            //判断白天黑夜
            int nightModeFlags =
                    CommonUtils.getApp().getResources().getConfiguration().uiMode & Configuration.UI_MODE_NIGHT_MASK;
            LottieComposition composition = nightModeFlags != Configuration.UI_MODE_NIGHT_YES ?
                    LottieCompositionFactory.fromRawResSync(CommonUtils.getApp(), R.raw.lk1a_120loading_light).getValue() :
                    LottieCompositionFactory.fromRawResSync(CommonUtils.getApp(), R.raw.lk1a_120loading_dark).getValue();
            if (composition != null) {
                lottieAnimationView.setComposition(composition);
            }
        }
    }

    /**
     * 显示 加载页面,设置加载文本
     *
     * @param VB ViewBinding
     * @param text 加载文本
     */
    public static void showLoadingWithText(ViewBinding VB, String text) {
        showLoading(VB);
        TextView tvReqLoading = VB.getRoot().findViewById(R.id.tv_req_loading);
        tvReqLoading.setText(text);
    }

    /**
     * 显示 加载界面
     * 布局 scene_layout_comment_req_res
     */
    public static void showLoading(ViewBinding VB) {
        show(VB, null, null);
    }

    /**
     * 显示 异常界面
     * 布局 scene_layout_comment_req_res
     */
    public static void showError(ViewBinding VB, ApiException apiException,
                                 View.OnClickListener listener) {
        show(VB, apiException, listener);
    }

    /**
     * 隐藏加载和异常界面
     */
    public static void disShow(ViewBinding VB) {
        if (VB == null || VB.getRoot().findViewById(R.id.rl_refresh) == null) return;
        VB.getRoot().findViewById(R.id.rl_refresh).setVisibility(View.GONE);
        LoadingImageView lottieAnimationView = VB.getRoot().findViewById(R.id.lottie_loading);
        lottieAnimationView.cancelAnimation();
        lottieAnimationView.clearAnimation();
    }

    /**
     * 是否显示异常界面
     */
    public static boolean isShowErrorView(ViewBinding VB) {
        if (VB == null) return false;
        return VB.getRoot().findViewById(R.id.tv_req_loading).getVisibility() == View.GONE;
    }


    public static void showToastNetError(NetWorkUtils.NetWorkType type) {
        if (type == NetWorkUtils.NetWorkType.DeviceNoNetWork) {
            CommonToastUtils.show(R.string.scene_toast_web_exception_device_no_network);
        }
        //        else if (type == NetWorkUtils.NetWorkType.WeakNetwork) {
        //            CommonToastUtils.show(R.string.scene_toast_web_exception_weak_network);
        //        } else if (type == NetWorkUtils.NetWorkType.NoFlow) {
        //            CommonToastUtils.show(R.string.scene_toast_web_exception_no_flow);
        //        }

    }

    /**
     * req_res背景透明
     *
     * @param VB 包含布局 scene_layout_comment_req_res
     */
    public static void setReqResLayoutTransparent(ViewBinding VB) {
        View root = VB.getRoot().findViewById(R.id.rl_refresh);
        if (root != null) {
            root.setBackgroundResource(R.color.transparent);
        }
    }

    /**
     * 获取glide的RequestOptions
     *
     * @param allRes 加载中 和 失败 都显示的图
     * @return RequestOptions
     */
    public static RequestOptions getGlideOptions(int allRes) {
        return getGlideOptions(allRes, allRes, 0, 0);
    }

    @SuppressLint("CheckResult")
    public static RequestOptions getGlideOptions() {
        RequestOptions mOptions = new RequestOptions();
        mOptions
                .fitCenter()
                .priority(Priority.HIGH)
                .skipMemoryCache(false) //优先级、不跳过内存缓存
                .dontAnimate()
                .dontTransform()
                .format(DecodeFormat.PREFER_RGB_565)
                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC);
        return mOptions;
    }

    /**
     * 获取glide的RequestOptions
     *
     * @param loadingRes 加载中默认图
     * @param errorRes 失败显示的图
     * @return RequestOptions
     */
    @SuppressLint("CheckResult")
    public static RequestOptions getGlideOptions(int loadingRes, int errorRes,
                                                 int width, int height) {
        RequestOptions mOptions = new RequestOptions();
        mOptions
                .fitCenter()
                .priority(Priority.HIGH)
                .skipMemoryCache(false) //优先级、不跳过内存缓存
                .dontAnimate().dontTransform()
                .format(DecodeFormat.PREFER_RGB_565)
                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)//智能地选择使用哪一种缓存策略
                .placeholder(loadingRes)
                .error(errorRes)
                .fallback(errorRes);
        if (width > 0 && height > 0)
            mOptions.override(width, height);
        return mOptions;
    }

    @SuppressLint("CheckResult")
    public static RequestOptions getGlideOptionsByImageView(ImageView imageView, int errorRes,
                                                            int width, int height) {
        RequestOptions mOptions = new RequestOptions();
        mOptions
                .fitCenter()
                .priority(Priority.HIGH)
                .skipMemoryCache(false) //优先级、不跳过内存缓存
                .dontAnimate().dontTransform()
                .format(DecodeFormat.PREFER_RGB_565)
                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)//智能地选择使用哪一种缓存策略
                .placeholder(imageView.getDrawable())
                .error(errorRes)
                .fallback(errorRes);
        if (width > 0 && height > 0)
            mOptions.override(width, height);
        return mOptions;
    }

    public static TextView getTab(TabLayout tabLayout, int position) {
        return (TextView) (((LinearLayout) ((LinearLayout) tabLayout.getChildAt(0)).getChildAt(position)).getChildAt(1));
    }

    /**
     * TabLayout取消长按
     */
    public static void setTabLayoutLongClickUnable(TabLayout tabLayout) {
        for (int i = 0; i < tabLayout.getTabCount(); i++) {
            TabLayout.Tab tab = tabLayout.getTabAt(i);
            if (tab != null) {
                tab.view.setLongClickable(false);
                tab.view.setTooltipText(null);   // 可以设置null也可以是""
                // tab.view.setTooltipText("");
            }
        }
    }

    @BindingAdapter({"imageId"})
    public static void glideLoadImage(ImageView imageView, int imgId) {
        Glide.with(imageView.getContext()).load(imgId).into(imageView);
    }

    /**
     * 初始化SmartRefreshLayout Footer通用设置
     * 前提:
     * SmartRefreshLayout.id=sl_update
     * ClassicsFooter.id=classicsFooter
     */
    public static void initRefreshLayout(@NotNull ViewBinding mViewBind) {
        SmartRefreshLayout srl = mViewBind.getRoot().findViewById(R.id.sl_update);
        //设置footer没有更多跟随内容
        //srl.setEnableFooterFollowWhenNoMoreData(true);
        //取消Footer自动加载下一页
        srl.setEnableAutoLoadMore(false);
        //srl.setEnableFooterTranslationContent(false);
        //srl.setEnableHeaderTranslationContent(false);
    }

    /**
     * 关键字提亮
     */
    public static void keywordEnhancement(TextView textView, String textContent, String keyword, Context context,
                                          int colorResId) {
        if (TextUtils.isEmpty(keyword)) return;
        if (!textContent.contains(keyword)) return;
        int start = textContent.indexOf(keyword);
        int end = start + keyword.length();
        if (end != 0 && start != -1) {
            SpannableStringBuilder style = new SpannableStringBuilder();
            style.append(textContent);
            //设置部分文字颜色
            ForegroundColorSpan foregroundColorSpan = new ForegroundColorSpan(
                    ContextCompat.getColor(context, colorResId)
            );
            style.setSpan(foregroundColorSpan, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            textView.setText(style);
        }
    }

    /**
     * 初始化WheelView 不带事件
     */
    public static <T> void initWheelView(Context context, WheelView wheelView, List<T> list, String label,
                                         int defaultIndex) {
        initWheelView(context, wheelView, list, label, defaultIndex, null,
                new OnItemScrollListener() {
                    @Override
                    public void onItemScrollStateChanged(WheelView wheelView, int currentPassItem, int oldScrollState
                            , int scrollState, float totalScrollY) {
                        if (scrollState == WheelView.SCROLL_STATE_IDLE && currentPassItem != wheelView.getInitPosition()) {
                            wheelView.setCurrentItem(currentPassItem);
                            SoundManager.INSTANCE.playSoundEffect(SoundManager.SoundType.ROLLER);
                        }
                    }

                    @Override
                    public void onItemScrolling(WheelView wheelView, int currentPassItem, int scrollState,
                                                float totalScrollY) {
                        if (scrollState == WheelView.SCROLL_STATE_DRAGGING && currentPassItem != wheelView.getInitPosition()) {
                            wheelView.setCurrentItem(currentPassItem);
                            SoundManager.INSTANCE.playSoundEffect(SoundManager.SoundType.ROLLER);
                        }
                    }
                });
    }

    /**
     * 初始化WheelView 带OnItemSelectedListener
     * label :单位
     */

    public static <T> void initWheelView(Context context, WheelView wheelView, List<T> list, String label,
                                         int defaultIndex, OnItemSelectedListener itemSelectedListener) {
        initWheelView(context, wheelView, list, label, defaultIndex, itemSelectedListener,
                new OnItemScrollListener() {
                    @Override
                    public void onItemScrollStateChanged(WheelView wheelView, int currentPassItem, int oldScrollState
                            , int scrollState, float totalScrollY) {
                        if (scrollState == WheelView.SCROLL_STATE_IDLE && currentPassItem != wheelView.getInitPosition()) {
                            wheelView.setCurrentItem(currentPassItem);
                            SoundManager.INSTANCE.playSoundEffect(SoundManager.SoundType.ROLLER);
                        }
                    }

                    @Override
                    public void onItemScrolling(WheelView wheelView, int currentPassItem, int scrollState,
                                                float totalScrollY) {
                        if (scrollState == WheelView.SCROLL_STATE_DRAGGING && currentPassItem != wheelView.getInitPosition()) {
                            wheelView.setCurrentItem(currentPassItem);
                            SoundManager.INSTANCE.playSoundEffect(SoundManager.SoundType.ROLLER);
                        }
                    }
                });
    }

    /**
     * 初始化WheelView 带OnItemSelectedListener和OnItemScrollListener
     * label :单位
     */
    public static <T> void initWheelView(Context context, WheelView wheelView, List<T> list, String label,
                                         int defaultIndex,
                                         OnItemSelectedListener itemSelectedListener,
                                         OnItemScrollListener itemScrollListener) {
        wheelView.setCyclic(true); // 是否循环
        wheelView.setItemWidth(740);//item宽度,会被wheelView宽度截断
        wheelView.setAdapter(new ArrayWheelAdapter<>(list));
        wheelView.label = label; // 单位
        wheelView.setCenterTextSize(40); // 中心区域内字体大小，里面自动转高低分屏
        wheelView.setSecondTextSize(36);// 中心区域外字体大小，里面自动转高低分屏
        //wheelView.setThirdTextSize(32);// 中心区域外字体大小，里面自动转高低分屏
        wheelView.setTextLabelSize(24);// label字体大小,不怎么用原生自带的单位
        wheelView.setCurrentItem(defaultIndex);//默认第几位
        wheelView.isEnableCurve = true;
        wheelView.setItemsVisibleCount(3); // 显示数目条数
        wheelView.setTextColorCenter(R.color.scene_primary_color_highlight);
        wheelView.setTextColorOut(R.color.scene_color_text_4);
        wheelView.setCenterBackgroundColor(R.color.transparent); //选中的item的背景色
        //wheelView.setItemHeight(AutoSizeUtils.dp2px(mContext, 100));
        wheelView.setLineSpacingMultiplier(3.0f);
        wheelView.isCenterLabel(true); // 是否只中间选中的item显示单位
        wheelView.setOnItemSelectedListener(itemSelectedListener);
        wheelView.setOnItemScrollListener(itemScrollListener);
        wheelView.setTypeface(Typeface.create("source-han", Typeface.NORMAL));
    }

    /**
     * 初始化WheelView 带OnItemSelectedListener
     * label :单位
     */

    public static <T> void initWheelViewP42R(Context context, WheelView wheelView, List<T> list, String label,
                                         int defaultIndex, OnItemSelectedListener itemSelectedListener) {
        initWheelView(context, wheelView, list, label, defaultIndex, itemSelectedListener,
                new OnItemScrollListener() {
                    @Override
                    public void onItemScrollStateChanged(WheelView wheelView, int currentPassItem, int oldScrollState
                            , int scrollState, float totalScrollY) {
                        if (scrollState == WheelView.SCROLL_STATE_IDLE && currentPassItem != wheelView.getInitPosition()) {
                            wheelView.setCurrentItem(currentPassItem);
                            SoundManager.INSTANCE.playSoundEffect(SoundManager.SoundType.ROLLER);
                        }
                    }

                    @Override
                    public void onItemScrolling(WheelView wheelView, int currentPassItem, int scrollState,
                                                float totalScrollY) {
                        if (scrollState == WheelView.SCROLL_STATE_DRAGGING && currentPassItem != wheelView.getInitPosition()) {
                            wheelView.setCurrentItem(currentPassItem);
                            SoundManager.INSTANCE.playSoundEffect(SoundManager.SoundType.ROLLER);
                        }
                    }
                });
    }

    /**
     * 初始化WheelView 带OnItemSelectedListener和OnItemScrollListener
     * label :单位
     */
    public static <T> void initWheelViewP42R(Context context, WheelView wheelView, List<T> list, String label,
                                         int defaultIndex,
                                         OnItemSelectedListener itemSelectedListener,
                                         OnItemScrollListener itemScrollListener) {
        wheelView.setCyclic(true); // 是否循环
        wheelView.setItemWidth(740);//item宽度,会被wheelView宽度截断
        wheelView.setAdapter(new ArrayWheelAdapter<>(list));
        wheelView.label = label; // 单位
        wheelView.setCenterTextSize(30); // 中心区域内字体大小，里面自动转高低分屏
        wheelView.setSecondTextSize(26);// 中心区域外字体大小，里面自动转高低分屏
        //wheelView.setThirdTextSize(32);// 中心区域外字体大小，里面自动转高低分屏
        wheelView.setTextLabelSize(20);// label字体大小,不怎么用原生自带的单位
        wheelView.setCurrentItem(defaultIndex);//默认第几位
        wheelView.isEnableCurve = true;
        wheelView.setItemsVisibleCount(3); // 显示数目条数
        wheelView.setTextColorCenter(R.color.scene_primary_color_highlight);
        wheelView.setTextColorOut(R.color.scene_color_text_4);
        wheelView.setCenterBackgroundColor(R.color.transparent); //选中的item的背景色
        //wheelView.setItemHeight(AutoSizeUtils.dp2px(mContext, 100));
        wheelView.setLineSpacingMultiplier(3.0f);
        wheelView.isCenterLabel(true); // 是否只中间选中的item显示单位
        wheelView.setOnItemSelectedListener(itemSelectedListener);
        wheelView.setOnItemScrollListener(itemScrollListener);
        wheelView.setTypeface(Typeface.create("source-han", Typeface.NORMAL));
    }

    /**
     * 初始化2D wheelView
     * label :单位
     */
    public static <T> void initWheelView2D(Context context, WheelView wheelView, List<T> list, String label,
                                           int defaultIndex, OnItemSelectedListener itemSelectedListener) {
        wheelView.setAdapter(new ArrayWheelAdapter<>(list));
        wheelView.label = label; // 单位
        wheelView.setCurrentItem(defaultIndex);//默认第几位
        wheelView.setItemsVisibleCount(5); // 显示数目条数
        wheelView.setOnItemSelectedListener(itemSelectedListener);
        wheelView.setTypeface(Typeface.create("source-han", Typeface.NORMAL));
        wheelView.setOnItemScrollListener(new OnItemScrollListener() {
            @Override
            public void onItemScrollStateChanged(WheelView wheelView, int currentPassItem, int oldScrollState
                    , int scrollState, float totalScrollY) {
                if (scrollState == WheelView.SCROLL_STATE_IDLE && currentPassItem != wheelView.getInitPosition()) {
                    wheelView.setCurrentItem(currentPassItem);
                    SoundManager.INSTANCE.playSoundEffect(SoundManager.SoundType.ROLLER);
                }
            }

            @Override
            public void onItemScrolling(WheelView wheelView, int currentPassItem, int scrollState,
                                        float totalScrollY) {
                if (scrollState == WheelView.SCROLL_STATE_DRAGGING && currentPassItem != wheelView.getInitPosition()) {
                    wheelView.setCurrentItem(currentPassItem);
                    SoundManager.INSTANCE.playSoundEffect(SoundManager.SoundType.ROLLER);
                }
            }
        });
    }


    public static <T> void initWheelView2D(Context context, WheelView wheelView, List<T> list, String label,
                                           int defaultIndex, OnItemSelectedListener itemSelectedListener, OnItemScrollListener itemScrollListener) {
        wheelView.setAdapter(new ArrayWheelAdapter<>(list));
        wheelView.label = label; // 单位
        wheelView.setCurrentItem(defaultIndex);//默认第几位
        wheelView.setItemsVisibleCount(5); // 显示数目条数
        wheelView.setOnItemSelectedListener(itemSelectedListener);
        wheelView.setTypeface(Typeface.create("source-han", Typeface.NORMAL));
        wheelView.setOnItemScrollListener(itemScrollListener);
    }

    /**
     * 使用默认宽度初始化seekBar,ui2.0为728dp
     */
    public static void initSeekBarDefaultWidth(Context context, CustomSeekBar seekBar, CustomSeekBarBean bean) {
        initSeekBar(context, seekBar, bean, 728);
    }

    /**
     * 初始化SeekBar width为UI图控件的宽度,方法里会自动转换高低分屏
     */
    public static void initSeekBar(Context context, CustomSeekBar seekBar, CustomSeekBarBean bean, int Width) {
        if (bean == null) return;
        if (!TextUtils.isEmpty(bean.getUnit())) {
            seekBar.setSeekBarUnit(bean.getUnit());
        }
        if (bean.getIcon() != -1) {
            seekBar.updateIcon(bean.getIcon());
        }
        if (!TextUtils.isEmpty(bean.getPre())) {
            seekBar.setLeftDesc(bean.getPre());
        }
        if (Width > 0) {
            seekBar.setSeekBarWidth(AutoSizeUtils.dp2px(context, Width));
        }
        //			if (bean.icon != -1) {
        //				viewBinding.sbDistance.updateIcon(bean.icon)
        //			}
        if (bean.getMultiple() != 0) {
            seekBar.setSeekBarMultiple(bean.getMultiple());

            if (bean.getMin() != -1) {
                seekBar.setMin(bean.getMin() / bean.getMultiple());
            }
            if (bean.getMax() != -1) {
                seekBar.setMax(bean.getMax() / bean.getMultiple());
            }
            if (bean.getDef() != null) {
                seekBar.setProgress(bean.getDef() / bean.getMultiple());
            }
        }

    }

    public static void playAnima(boolean isNeed, LottieAnimationView lottieView) {
        playAnima(isNeed, lottieView, null);
    }

    @SuppressLint("CheckResult")
    public static void playAnima(boolean isNeed, LottieAnimationView lottieView, OnItemSelectedListener listener) {
        if (!isNeed) return;
        if (lottieView == null) return;
        lottieView.setVisibility(View.VISIBLE);
        lottieView.setAnimation(R.raw.scene_likes_day);
        lottieView.playAnimation();

        //延时执行，4秒后还没有获取完数据，再重新获取多一次
        Observable.just(1).delay(1, TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io()) //指定被观察者subscribe()(发送事件的线程)在IO线程
                .observeOn(AndroidSchedulers.mainThread()) //指定观察者接收响应事件的线程在主线程
                .subscribe(integer -> {
                            lottieView.setVisibility(View.GONE);
                            lottieView.cancelAnimation();
                            lottieView.clearAnimation();
                            if (listener != null) {
                                listener.onItemSelected(0);
                            }
                        }
                );
    }

    /**
     * 获取条件卡片的内容的html格式
     */
    public static Spanned getConditionHtmlStr(Context context, String category, String desc) {
        if (category == null || desc == null) {
            return Html.fromHtml("", Html.FROM_HTML_MODE_COMPACT);
        }
        String htmlStr = context.getString(
                R.string.scene_text_skill_category_desc,
                context.getColor(R.color.scene_color_text_1) + "",
                category
        ) + "&nbsp;&nbsp;" + desc;

        return Html.fromHtml(htmlStr, Html.FROM_HTML_MODE_COMPACT);
    }

    /**
     * 当前页面切换日夜模式后手动设置SmartRefreshLayout头和尾的drawable
     *
     * @param header 下拉刷新 可空
     * @param footer 上拉加载 可空
     */
    public static void setSmartRefreshOnConfigurationChanged(InternalClassics header, InternalClassics footer) {
        if (header != null) {
            if (header instanceof ClassicsHeader) {
                header.setAccentColorId(R.color.scene_color_text_3);
                header.setArrowResource(R.drawable.scene_icon_common_refresh_down);
                header.setProgressResource(R.drawable.scene_img_common_refresh_loading);
            } else if (header instanceof ClassicsHeader4Banner) {
                header.setAccentColorId(R.color.scene_color_banner_header_bg_1);
                header.setArrowResource(R.drawable.scene_icon_common_refresh_down_4_banner);
                //                header.setProgressResource(R.drawable.scene_img_common_refresh_loading);
            } else if (header instanceof ClassicsHeader4All) {
                header.setAccentColorId(R.color.scene_color_text_3);
                header.setArrowResource(R.drawable.scene_icon_common_refresh_down);
            }
        }
        if (footer != null) {
            if (footer instanceof ClassicsFooter) {
                footer.setAccentColorId(R.color.scene_color_text_3);
                footer.setArrowResource(R.drawable.scene_icon_common_refresh_down);
                footer.setProgressResource(R.drawable.scene_img_common_refresh_loading);
            } else if (footer instanceof ClassicsFooter4All) {
                footer.setAccentColorId(R.color.scene_color_text_3);
                footer.setArrowResource(R.drawable.scene_icon_common_refresh_down);
            }
        }
    }

    /**
     * 是否白天模式
     */
    public static boolean isLightMode() {
        int nightModeFlags =
                CommonUtils.getApp().getResources().getConfiguration().uiMode & Configuration.UI_MODE_NIGHT_MASK;
        return nightModeFlags != Configuration.UI_MODE_NIGHT_YES;
    }

    /**
     * 实现修改 长按事件触发时间，默认是500ms
     *
     * @param handler 外界handler(为了减少handler的泛滥使用,最好全局传handler引用,如果没有就直接传 new Handler())
     * @param longClickView 被长按的视图(任意控件)
     * @param delayMillis 长按时间,毫秒，错误值使用默认值
     * @param longClickListener 长按回调的返回事件
     */
    public static void setLongClick(final Handler handler, final View longClickView, final long delayMillis,
                                    @Nullable final View.OnLongClickListener longClickListener) {
        longClickView.setOnTouchListener(new View.OnTouchListener() {
            /** 移动误差阈值 */
            private final static int TOUCH_MAX = 50;
            /** 默认长按时间 */
            private final static int LONG_TOUCH_TIME = 500;
            /** 长按Runnable回调 */
            private final Runnable longRun = () -> {
                // 回调给用户,用户可能传null,需要判断null
                if (longClickListener != null) {
                    longClickListener.onLongClick(longClickView);
                }
            };
            /**上次触摸位置*/
            private int mLastMotionX;
            private int mLastMotionY;

            private long downTime = 0;

            @SuppressLint("ClickableViewAccessibility")
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                int x = (int) event.getX();
                int y = (int) event.getY();
                switch (event.getAction()) {
                    case MotionEvent.ACTION_UP:
                    case MotionEvent.ACTION_CANCEL:
                        // 抬起时,移除已有Runnable回调,抬起就不算长按了(不需要考虑用户是否长按了超过预设的时间)
                        handler.removeCallbacks(longRun);
                        long duration = System.currentTimeMillis() - downTime;
                        if (duration > (delayMillis < 0 ? LONG_TOUCH_TIME : delayMillis)) { // 自定义时长判断
                            return true;
                        }
                        break;
                    case MotionEvent.ACTION_MOVE:
                        if (Math.abs(mLastMotionX - x) > TOUCH_MAX || Math.abs(mLastMotionY - y) > TOUCH_MAX) {
                            // xy方向判断，移动超过阈值，则表示移动了,就不是长按(看需求),移除 已有的Runnable回调
                            handler.removeCallbacks(longRun);
                        }
                        break;
                    case MotionEvent.ACTION_DOWN:
                        downTime = System.currentTimeMillis();
                        // 每次按下重新计时,移除已有的Runnable回调,防止用户多次单击导致多次回调长按事件的bug
                        handler.removeCallbacks(longRun);
                        mLastMotionX = x;
                        mLastMotionY = y;
                        // 开始计时,判断delayMillis是否正确，不正确使用默认值
                        handler.postDelayed(longRun, delayMillis < 0 ? LONG_TOUCH_TIME : delayMillis);
                        break;
                }
                //返回false事件往下传递
                return false;
            }
        });
    }

    /**
     * 设置输入框的最小输入数，否者置灰不可用
     */
    public static void setKeyBroadInputMinIfNotEnable(EditText etInput, int min) {
        Bundle bundle = etInput.getInputExtras(true);
        bundle.putInt("min_ems", min);
    }

    public static void hideKeyBroadInput(Activity activity) {
        InputMethodManager imm = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
        View v = activity.getWindow().peekDecorView();
        imm.hideSoftInputFromWindow(v.getWindowToken(), 0);
    }


    /**
     * TextView部分文字变色
     * keyword = 关键字、需要变色的文字   string = 包含变色文字的全部文字
     */
    public static void stringInterceptionChangeColor(Context context, TextView textView, String keyword, String string,
                                                     int colorId) {
        if (keyword == null || keyword.trim().isEmpty()) return;
        if (!string.contains(keyword)) return;
        int start = string.indexOf(keyword);
        int end = start + keyword.length();
        if (end != 0 && start != -1) {
            SpannableStringBuilder style = new SpannableStringBuilder();
            style.append(string);
            //设置部分文字颜色
            ForegroundColorSpan foregroundColorSpan = new ForegroundColorSpan(
                    ContextCompat.getColor(context, colorId)
            );
            style.setSpan(foregroundColorSpan, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            textView.setText(style);
        }
    }


    /***
     * 业务逻辑设置软键盘是否可用
     * @param isAllow true 可用
     * @param editTexts 输入框集合，默认第一个get(0)为需要显示键盘的 输入框
     * @param activity activity
     * @param dialog dialog或是dialogfragment页面 输入，当是activity页面传null
     */
    public static void setKeyBroadInputAllow(boolean isAllow, List<EditText> editTexts, Activity activity,
                                             Dialog dialog) {
        if (isAllow) { //可用
            editTexts.forEach(editText -> UIUtils.setKeyBroadInputMinIfNotEnable(editText, 0));
        } else { //不可用
            editTexts.forEach(editText -> UIUtils.setKeyBroadInputMinIfNotEnable(editText, 10000));
        }

        InputMethodManager imm = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
        editTexts.forEach(editText -> imm.restartInput(editText));

        if (ConfigManager.INSTANCE.getCarSystemType().equals("hsae")) { //8155 航盛，改变可用状态之后需要隐藏再显示
            if (dialog != null) {
                KeyboardUtils.hideSoftInput(dialog.getWindow());
            } else {
                KeyboardUtils.hideSoftInput(activity.getWindow());
            }
            KeyboardUtils.showSoftInput(editTexts.get(0));
        }
    }


    public static void expandTouchView(Context context, View view, int expandSize) {
        expandTouchView(context, view, expandSize, expandSize, expandSize, expandSize);
    }

    /** 扩展热区 受限于父view */
    public static void expandTouchView(Context context, View view, int leftExpandSize, int topExpandSize,
                                       int rightExpandSize,
                                       int bottomExpandSize) {
        ViewParent parent = view.getParent();
        if (parent != null) {
            View parentView = (View) parent;
            parentView.post(() -> {
                Rect rect = new Rect();
                parentView.getHitRect(rect);
                rect.left -= AutoSizeUtils.dp2px(context, leftExpandSize);
                rect.top -= AutoSizeUtils.dp2px(context, topExpandSize);
                rect.right += AutoSizeUtils.dp2px(context, rightExpandSize);
                rect.bottom += AutoSizeUtils.dp2px(context, bottomExpandSize);
                parentView.setTouchDelegate(new TouchDelegate(rect, parentView));
            });
        }
    }

}
