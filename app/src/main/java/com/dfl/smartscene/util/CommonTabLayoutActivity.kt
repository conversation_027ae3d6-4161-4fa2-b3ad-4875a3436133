//package com.dfl.android.animation.tabLayout
//
//import android.os.Bundle
//import androidx.appcompat.app.AppCompatActivity
//import androidx.fragment.app.Fragment
//import androidx.viewpager2.adapter.FragmentStateAdapter
//import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
//import com.dfl.android.animation.databinding.ActivityCommonTabLayoutBinding
//import com.dfl.android.animation.tabLayout.fragment.TabFragment
//import com.dfl.android.animationlib.tablayout.CommonTabLayout
//
//class CommonTabLayoutActivity : AppCompatActivity() {
//
//    private val mTab: List<String> = mutableListOf(
//    "氛围灯",
//    "闪光灯",
//    "霓虹灯",
//    "白织灯",
//    "小雨灯",
//    "猫猫灯",
//    "旋转风扇灯")
//
//    override fun onCreate(savedInstanceState: Bundle?) {
//        super.onCreate(savedInstanceState)
//        val binding = ActivityCommonTabLayoutBinding.inflate(layoutInflater)
//        setContentView(binding.root)
//
//        val fragments: MutableList<Fragment> = ArrayList()
//        for (i in mTab.indices) {
//            fragments.add(TabFragment.newInstance(mTab[i].toString()))
//        }
//
//        binding.ctlTab.setTabsData(mTab)
//
//        binding.vp.adapter = object : FragmentStateAdapter(supportFragmentManager, lifecycle) {
//            override fun createFragment(position: Int): Fragment {
//                return fragments[position]
//            }
//
//            override fun getItemCount(): Int {
//                return fragments.size
//            }
//        }
//        binding.vp.setCurrentItem(2,false)
//        binding.ctlTab.setCurrentTab(2,false)
//
//
//
//        binding.ctlTab.listener = object: CommonTabLayout.OnTabSelectedListener{
//            override fun onTabClicked(position: Int) {
//
//            }
//
//            override fun onTabSelected(position: Int, isFromUser: Boolean) {
//                if(isFromUser){
//                    binding.vp.setCurrentItem(position,true)
//                }
//            }
//
//            override fun onTabUnSelected(lastPosition: Int) {
//
//            }
//
//            override fun onInitViewComplete(isComplete: Boolean?) {
//
//            }
//
//        }
//
//        binding.vp.registerOnPageChangeCallback(object : OnPageChangeCallback() {
//            override fun onPageScrolled(
//                position: Int,
//                positionOffset: Float,
//                positionOffsetPixels: Int
//            ) {
//                super.onPageScrolled(position, positionOffset, positionOffsetPixels)
//
//            }
//
//            override fun onPageSelected(position: Int) {
//                super.onPageSelected(position)
//                binding.ctlTab.setCurrentTab(position,true)
//            }
//        })
//
//        binding.clearBt.setOnClickListener {
//            binding.ctlTab.clearCheckStatus()
//        }
//
//    }
//}