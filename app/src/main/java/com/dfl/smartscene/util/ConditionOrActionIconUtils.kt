package com.dfl.smartscene.util

import com.dfl.android.common.global.SkillsListConstant
import com.dfl.smartscene.R
import com.iauto.scenarioadapter.ScenarioInfo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/11/22
 * desc :条件动作图标帮助类
 * version: 1.0
 */ // 需要把那些新增的能力id 添加上 ，等ui出图再添加
object ConditionOrActionIconUtils {
    private val mTriggerIconMaps = HashMap<Int, Int>() //触发条件图标集合
    private val mStatusIconMaps = HashMap<Int, Int>() //状态条件图标集合
    private val mActionIconMaps = HashMap<Int, Int>() //动作图标集合
    private var mActionBgMaps = HashMap<Int, Int>() //动作背景集合

    fun initSceneConditionAndActionIcon() {
        CoroutineScope(Dispatchers.IO).launch {
            initAddTriggerConditionIconData()  //触发二级，用于简图栏
            initAddStatusConditionIconData()  //状态二级，用于简图栏
            initAddActionIconData()           //动作二级，用于简图栏
            initAddActionBgData()            //动作能用
        }
    }

    /**
     *初始化添加触发条件图标
     */
    private fun initAddTriggerConditionIconData() {
        //时间
        mTriggerIconMaps[SkillsListConstant.SKILLS_ID_TRIGGER_TIME_TIME_POINT] = R.drawable.scene_icon_time_system_clock
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_TIME_DAY_STATUS] =
            R.drawable.scene_icon_me_trigger_time_sunrise_sunset

        //环境
        mTriggerIconMaps[SkillsListConstant.SKILLS_ID_TRIGGER_ENVIRONMENT_INNER_TEMPERATURE_HIGHER] =
            R.drawable.scene_icon_me_trigger_environment_interior_temperature
        mTriggerIconMaps[SkillsListConstant.SKILLS_ID_TRIGGER_ENVIRONMENT_INNER_TEMPERATURE_LOW] =
            R.drawable.scene_icon_me_trigger_environment_interior_temperature
        mTriggerIconMaps[SkillsListConstant.SKILLS_ID_TRIGGER_ENVIRONMENT_OUTSIDE_TEMPERATURE_HIGHER] =
            R.drawable.scene_icon_me_trigger_environment_outside_temperature
        mTriggerIconMaps[SkillsListConstant.SKILLS_ID_TRIGGER_ENVIRONMENT_OUTSIDE_TEMPERATURE_LOW] =
            R.drawable.scene_icon_me_trigger_environment_outside_temperature

        //位置
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_LOCATION_VEHICLE_ARRIVAL] =
            R.drawable.scene_icon_me_trigger_location_arrival
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_LOCATION_VEHICLE_DEPARTURE] =
            R.drawable.scene_icon_me_trigger_location_departure

        //驾驶
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_BATTERY_LIFE_MORE] =
            R.drawable.scene_icon_me_trigger_drive_mileage
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_BATTERY_LIFE_LESS] =
            R.drawable.scene_icon_me_trigger_drive_mileage
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_BATTERY_POWER_MORE] =
            R.drawable.scene_icon_me_trigger_drive_battery
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_BATTERY_POWER_LESS] =
            R.drawable.scene_icon_me_trigger_drive_battery
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_SPEED_MORE] =
            R.drawable.scene_icon_me_trigger_drive_speed
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_SPEED_LESS] =
            R.drawable.scene_icon_me_trigger_drive_speed
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_TIME_MORE] =
            R.drawable.scene_icon_me_trigger_drive_time
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_TIME_LESS] =
            R.drawable.scene_icon_me_trigger_drive_time
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_DISTANCE_MORE] =
            R.drawable.scene_icon_me_trigger_drive_distance
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_DISTANCE_LESS] =
            R.drawable.scene_icon_me_trigger_drive_distance
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_GEAR] = R.drawable.scene_icon_me_trigger_drive_gear

        //座椅
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_SEAT_MAIN_DRIVER_TRIGGER_STATE] =
            R.drawable.scene_icon_me_trigger_seat_main_seat
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_SEAT_COPILOT_TRIGGER_STATE] =
            R.drawable.scene_icon_me_trigger_seat_copilot_seat
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_SEAT_MAIN_DRIVER_TRIGGER_SAFE_BELT] =
            R.drawable.scene_icon_me_trigger_seat_main_safe_belt
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_SEAT_COPILOT_TRIGGER_SAFE_BELT] =
            R.drawable.scene_icon_me_trigger_seat_copilot_safe_belt
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_SEAT_BACK_LEFT_SAFE_BELT] =
            R.drawable.scene_icon_me_trigger_seat_left_seat_belt
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_SEAT_BACK_MIDDLE_SAFE_BELT] =
            R.drawable.scene_icon_me_trigger_seat_middle_seat_belt
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_SEAT_BACK_RIGHT_SAFE_BELT] =
            R.drawable.scene_icon_me_trigger_seat_right_safe_belt

        //门窗
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_DOOR_ALL_DOOR_LOCK] =
            R.drawable.scene_icon_me_status_door_all_door_lock
        // 注：门窗的触发条件是4个公用1个ID，需要详细判断才行
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_DOOR_ALL_STATE] =
            R.drawable.scene_icon_me_trigger_door_upperleft_door
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_DOOR_LEFT_CHILD_LOCK] =
            R.drawable.scene_icon_me_action_door_children_lock_l
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_DOOR_RIGHT_CHILD_LOCK] =
            R.drawable.scene_icon_me_action_door_children_lock_r

        //灯光
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_FAR_LIGHT] = R.drawable.scene_icon_me_action_light_highbeam
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_NEAR_LIGHT] = R.drawable.scene_icon_me_action_light_lowbeam
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_FOG_LIGHT] = R.drawable.scene_icon_me_action_light_fog

        //充放电
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_CHARGE] = R.drawable.scene_icon_me_trigger_charge
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_CHARGE_END_TIME] =
            R.drawable.scene_icon_me_trigger_charge_end_time
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_DISCHARGE] = R.drawable.scene_icon_me_trigger_discharge

        //座舱感知
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_OMS_POSITION_CHILDREN] =
            R.drawable.scene_icon_me_status_cabin_perception_child_testing
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_OMS_POSITION_SOMEONE] =
            R.drawable.scene_icon_me_status_cabin_perception_distribution_detection
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_OMS_POSITION_GENDER] =
            R.drawable.scene_icon_me_status_cabinperception_gender_detection
        mTriggerIconMaps[SkillsListConstant.SKILL_ID_TRIGGER_OMS_POSITION_BEHAVIOR] =
            R.drawable.scene_icon_me_status_cabin_perception_behavior_detection
    }

    /**
     * 初始化添加状态条件图标
     */
    private fun initAddStatusConditionIconData() {
        //时间
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_TIME_INTERVAL_ID] =
            R.drawable.scene_icon_me_status_time_interval
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_TIME_WORKDAY_ID] = R.drawable.scene_icon_me_status_time_cycle
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_TIME_HOLIDAY_ID] = R.drawable.scene_icon_me_status_time_cycle
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_ID] =
            R.drawable.scene_icon_me_status_time_cycle
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_TIME_SOME_DAY_ID] =
            R.drawable.scene_icon_me_status_time_cycle

        //环境
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_ENVIRONMENT_AIR_QUALITY] =
            R.drawable.scene_icon_me_status_environment_air_quality
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_ENVIRONMENT_WEATHER] =
            R.drawable.scene_icon_me_status_environment_weather
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_ENVIRONMENT_CAR_OUTSIDE_WEATHER_RAIN] =
            R.drawable.scene_icon_me_status_environment_outside_weather
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_ENVIRONMENT_CAR_OUTSIDE_WEATHER_NO_RAIN] =
            R.drawable.scene_icon_me_status_environment_outside_weather
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_ENVIRONMENT_HUMIDITY_IN_CAR_LESS] =
            R.drawable.scene_icon_me_status_environment_humidity_in_car
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_ENVIRONMENT_HUMIDITY_IN_CAR_MORE] =
            R.drawable.scene_icon_me_status_environment_humidity_in_car

        //位置
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_LOCATION_LOCATED_AT] =
            R.drawable.scene_icon_me_status_location_position
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_LOCATION_LOCATED_AT_NOT] =
            R.drawable.scene_icon_me_status_location_noposition

        //导航
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_NAVIGATION_EXPECTED_ARRIVAL_TIME] =
            R.drawable.scene_icon_me_status_navigation_expected_arrival_distance_time
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_NAVIGATION_EXPECTED_ARRIVAL_DISTANCE] =
            R.drawable.scene_icon_me_status_navigation_expected_arrival_distance_m

        //门窗
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_DOOR_ALL_DOOR_LOCK] =
            R.drawable.scene_icon_me_status_door_all_door_lock
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_DOOR_DOOR_BACK] =
            R.drawable.scene_icon_me_action_door_back_door
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_DOOR_ELECTRIC_DOOR_BACK] =
            R.drawable.scene_icon_me_action_door_back_door
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_DOOR_LEFT_CHILD_LOCK] =
            R.drawable.scene_icon_me_action_door_children_lock_l
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_DOOR_RIGHT_CHILD_LOCK] =
            R.drawable.scene_icon_me_action_door_children_lock_r

        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_DOOR_WINDOW_DRIVER_MAIN] =
            R.drawable.scene_icon_me_status_door_main_door_window
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_DOOR_WINDOW_DRIVER_CHIEF] =
            R.drawable.scene_icon_me_status_door_copilot_door_window
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_DOOR_WINDOW_BACK_LEFT] =
            R.drawable.scene_icon_me_status_door_main_door_window
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_DOOR_WINDOW_BACK_RIGHT] =
            R.drawable.scene_icon_me_status_door_copilot_door_window

        //座椅
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_SEAT_MAIN_DRIVER_STATUS_STATE] =
            R.drawable.scene_icon_me_trigger_seat_main_seat
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_SEAT_COPILOT_STATUS_STATE] =
            R.drawable.scene_icon_me_trigger_seat_copilot_seat
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_SEAT_MAIN_DRIVER_STATUS_SAFE_BELT] =
            R.drawable.scene_icon_me_trigger_seat_main_safe_belt
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_SEAT_COPILOT_STATUS_SAFE_BELT] =
            R.drawable.scene_icon_me_trigger_seat_copilot_safe_belt
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_SEAT_BACK_LEFT_STATUS_SAFE_BELT] =
            R.drawable.scene_icon_me_trigger_seat_left_seat_belt
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_SEAT_BACK_MIDDLE_STATUS_SAFE_BELT] =
            R.drawable.scene_icon_me_trigger_seat_middle_seat_belt
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_SEAT_BACK_RIGHT_STATUS_SAFE_BELT] =
            R.drawable.scene_icon_me_trigger_seat_right_safe_belt

        //连接
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_LINK_BLUETOOTH_CONNECTION] =
            R.drawable.scene_icon_me_status_link_bluetooth
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_LINK_BLUETOOTH_SWITCH] =
            R.drawable.scene_icon_me_status_link_bluetooth
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_LINK_HOTSPOT_CONNECTION] =
            R.drawable.scene_icon_me_status_link_hot_point
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_LINK_HOTSPOT_SWITCH] =
            R.drawable.scene_icon_me_status_link_hot_point
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_LINK_WLAN_CONNECTION] =
            R.drawable.scene_icon_me_status_link_wifi
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_LINK_WLAN_SWITCH] =
            R.drawable.scene_icon_me_status_link_wifi
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_LINK_MOBILE_SWITCH] =
            R.drawable.scene_icon_me_status_link_4g

        //智能设备
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_SMART_DEVICE_AIR_QUALITY] =
            R.drawable.scene_icon_me_status_smart_device_air_quality
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_SMART_DEVICE_RIDING_STATUS] =
            R.drawable.scene_icon_me_status_smart_device_riding_status

        //胎压
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_OTHER_TIRE_PRESSURE_LEFT_FRONT_WHEEL] =
            R.drawable.scene_icon_me_status_tire_upperleft_tire
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_OTHER_TIRE_PRESSURE_RIGHT_FRONT_WHEEL] =
            R.drawable.scene_icon_me_status_tire_upperright_tire
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_OTHER_TIRE_PRESSURE_LEFT_AFTER_WHEEL] =
            R.drawable.scene_icon_me_status_tire_lowerleft_tire
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_OTHER_TIRE_PRESSURE_RIGHT_AFTER_WHEEL] =
            R.drawable.scene_icon_me_status_tire_lowerright_tire

        //座舱感知
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_OMS_POSITION_CHILDREN] =
            R.drawable.scene_icon_me_status_cabin_perception_child_testing
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_OMS_POSITION_SOMEONE] =
            R.drawable.scene_icon_me_status_cabin_perception_distribution_detection
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_OMS_POSITION_GENDER] =
            R.drawable.scene_icon_me_status_cabinperception_gender_detection
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_OMS_POSITION_BEHAVIOR] =
            R.drawable.scene_icon_me_status_cabin_perception_behavior_detection

        //灯光
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_FAR_LIGHT] = R.drawable.scene_icon_me_action_light_highbeam
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_NEAR_LIGHT] = R.drawable.scene_icon_me_action_light_lowbeam
        mStatusIconMaps[SkillsListConstant.SKILL_ID_STATUS_FOG_LIGHT] = R.drawable.scene_icon_me_action_light_fog
    }

    /**
     * 初始化添加动作图标
     */
    private fun initAddActionIconData() { //推荐
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_RECOMMEND_DELAY_ID] = R.drawable.scene_icon_me_action_delay

        //空调
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_OPEN] =
            R.drawable.scene_icon_me_action_air_switch
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_TEMPERATURE] =
            R.drawable.scene_icon_me_action_air_temperature
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_TEMPERATURE_TWO_LEFT] =
            R.drawable.scene_icon_me_action_air_temperature
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_TEMPERATURE_TWO_RIGHT] =
            R.drawable.scene_icon_me_action_air_temperature
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_AIR_VOLUME] =
            R.drawable.scene_icon_me_action_air_volume
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_BLOW_MODE] =
            R.drawable.scene_icon_me_action_air_blow_mode
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_INNER_LOOP] =
            R.drawable.scene_icon_me_action_air_loop_mode
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_AUTO_MODE_OPEN] =
            R.drawable.scene_icon_me_action_air_auto
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_COOLING_HEATING_OPEN] =
            R.drawable.scene_icon_me_action_air_cool_heat
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_BLOWER_OPEN] =
            R.drawable.scene_icon_me_action_air_blower
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_FRONT_DEFOG_OPEN] =
            R.drawable.scene_icon_me_action_air_front_defog
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_AFTER_DEFOG_OPEN] =
            R.drawable.scene_icon_me_action_air_after_defog
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_MAX_COOL] = R.drawable.scene_icon_me_action_air_max_cool
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_MAX_HEAT] = R.drawable.scene_icon_me_action_air_max_heat
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_SYNC] = R.drawable.scene_icon_me_action_air_synchronous
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_ENERGY_CONSERVATION] =
            R.drawable.scene_icon_me_action_air_energy_saving_mode
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_SELF_DESICCATION] =
            R.drawable.scene_icon_me_action_air_self_drying_mode

        //门窗
        //mActionIconMaps[SkillsListConstant.BACK_DOOR_NORMAL_LOCK] = R.drawable.scene_icon_back_door
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_BACK_DOOR_ELECTRIC_LOCK] =
            R.drawable.scene_icon_me_action_door_back_door
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_BACK_DOOR_NORMAL_LOCK] =
            R.drawable.scene_icon_me_action_door_back_door
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_LEFT_CHILD_LOCK] =
            R.drawable.scene_icon_me_action_door_children_lock_l
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_RIGHT_CHILD_LOCK] =
            R.drawable.scene_icon_me_action_door_children_lock_r
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_ALL_OPEN] =
            R.drawable.scene_icon_me_action_door_window_all
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_VENTILATE_OPEN] =
            R.drawable.scene_icon_me_action_door_window_freshleft
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_IN_FRESH_AIR] =
            R.drawable.scene_icon_me_action_door_window_ventilation
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_DRIVER_MAIN_PERCENT] =
            R.drawable.scene_icon_me_status_door_main_door_window
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_DRIVER_CHIEF_PERCENT] =
            R.drawable.scene_icon_me_status_door_copilot_door_window
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_BACK_LEFT_PERCENT] =
            R.drawable.scene_icon_me_status_door_main_door_window
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_BACK_RIGHT_PERCENT] =
            R.drawable.scene_icon_me_status_door_copilot_door_window
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_SKYLIGHT] =
            R.drawable.scene_icon_me_trigger_door_skylight
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_ELECTRIC_SUNSHADE_CURTAIN] =
            R.drawable.scene_icon_me_trigger_door_sunrise_electric_sunshade_curtain

        //灯光
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_LAMP_LANGUAGE_MODE_ID] =
            R.drawable.scene_icon_me_action_light_language_mode
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_SCENE_EFFECT] =
            R.drawable.scene_icon_me_action_light_lamp_effect
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_SOA] =
            R.drawable.scene_icon_me_action_light_soa_language_mode
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_FOG_LIGHT_ID] =
            R.drawable.scene_icon_me_action_light_fog_lamp
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_STAR_LIGHT_STATUS] =
            R.drawable.scene_icon_me_action_light_sceneslight
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_EXTERIOR_LIGHT_MUSIC_EFFECT] =
            R.drawable.scene_icon_me_action_light_headlight_music_rhythm
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_EXTERIOR_LIGHT_HEIGHT] =
            R.drawable.scene_icon_me_action_light_headlight_height
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_EXTERIOR_LIGHT_MODE_CONTROL] =
            R.drawable.scene_icon_me_action_light_headlight_control

        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_SWITCH_ID] =
            R.drawable.scene_icon_me_action_light_atmosphere_switch
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_MODE_ID] =
            R.drawable.scene_icon_me_action_light_atmosphere_mode
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_LIGHT_ID] =
            R.drawable.scene_icon_me_action_light_atmosphere_light
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_COLOR_ID] =
            R.drawable.scene_icon_me_action_light_atmosphere_color
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_COLOR_THEME_ID] =
            R.drawable.scene_icon_me_action_light_atmosphere_color
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_SOA] =
            R.drawable.scene_icon_me_action_light_atmosphere_soa_language_mode
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_SCENE_EFFECT_ID] =
            R.drawable.scene_icon_me_action_light_atmosphere_lamp_effect

        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_READ_LIGHT_SWITCH] =
            R.drawable.scene_icon_me_action_light_read_lamp_switch
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_READ_LIGHT_MODE] =
            R.drawable.scene_icon_me_action_light_read_lamp_mode
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_READ_LIGHT_CUSTOM] =
            R.drawable.scene_icon_me_action_light_read_lamp_mode

        //座椅
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_MAIN_DRIVER_SEAT_MEMORY_SETTING] =
            R.drawable.scene_icon_me_action_seat_main_memory
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_COPILOT_SEAT_MEMORY_SETTING] =
            R.drawable.scene_icon_me_action_seat_copilot_memory
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_MAIN_DRIVER_SEAT_AIR_SETTING] =
            R.drawable.scene_icon_me_action_seat_main_air
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_COPILOT_SEAT_AIR_SETTING] =
            R.drawable.scene_icon_me_action_seat_copilot_air
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_AIR] =
            R.drawable.scene_icon_me_action_seat_left_air
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_AIR] =
            R.drawable.scene_icon_me_action_seat_right_air
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_MAIN_DRIVER_SEAT_HEATING_SETTING] =
            R.drawable.scene_icon_me_action_seat_main_heating
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_COPILOT_SEAT_HEATING_SETTING] =
            R.drawable.scene_icon_me_action_seat_copilot_heating
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_HEATING] =
            R.drawable.scene_icon_me_action_seat_left_heating
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_HEATING] =
            R.drawable.scene_icon_me_action_seat_right_heating
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_LEFT_SEAT_MASSAGE_SWITCH] =
            R.drawable.scene_icon_me_action_seat_right_massage_strength
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_LEFT_SEAT_MASSAGE_INTENSITY] =
            R.drawable.scene_icon_me_action_seat_right_massage_strength
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_RIGHT_SEAT_MASSAGE_SWITCH] =
            R.drawable.scene_icon_me_action_seat_left_massage_strength
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_RIGHT_SEAT_MASSAGE_INTENSITY] =
            R.drawable.scene_icon_me_action_seat_left_massage_strength
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_MASSAGE_OFF] =
            R.drawable.scene_icon_me_action_seat_left_massage
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_MASSAGE_INTENSITY] =
            R.drawable.scene_icon_me_action_seat_left_massage
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_MASSAGE_OFF] =
            R.drawable.scene_icon_me_action_seat_right_massage
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_MASSAGE_INTENSITY] =
            R.drawable.scene_icon_me_action_seat_right_massage
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_RIGHT_SEAT_MASSAGE_MODE] =
            R.drawable.scene_icon_me_action_seat_left_massage_mode
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_LEFT_SEAT_MASSAGE_MODE] =
            R.drawable.scene_icon_me_action_seat_right_massage_mode
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_MASSAGE_MODE] =
            R.drawable.scene_icon_me_action_seat_back_left_massage_mode
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_MASSAGE_MODE] =
            R.drawable.scene_icon_me_action_seat_back_right_massage_mode
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_LEFT_SEAT_RESET] =
            R.drawable.scene_icon_me_action_seat_reset_left
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_RIGHT_SEAT_RESET] =
            R.drawable.scene_icon_me_action_seat_reset_right
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_AI_BACK_SEAT] =
            R.drawable.scene_icon_me_action_seat_exclusive_enjoyment
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_AI_BACK_LEFT_SEAT_FOLD] =
            R.drawable.scene_icon_me_action_seat_fold_left
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_AI_BACK_RIGHT_SEAT_FOLD] =
            R.drawable.scene_icon_me_action_seat_fold_right
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_AI_ZERO_G_SEAT] =
            R.drawable.scene_icon_me_trigger_seat_main_seat

        //冰箱
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_FRIDGE_SWITCH] =
            R.drawable.scene_icon_me_action_icebox_switch
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_FRIDGE_MODE] = R.drawable.scene_icon_me_action_air_cool_heat
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_FRIDGE_COOLING_TEMP] =
            R.drawable.scene_icon_me_action_icebox_cold
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_FRIDGE_HEATING_TEMP] =
            R.drawable.scene_icon_me_action_icebox_hot
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_FRIDGE_CONVENIENCE_MODE] =
            R.drawable.scene_icon_me_action_icebox_quick
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_FRIDGE_DEPARTURE_WORKING_TIME] =
            R.drawable.scene_icon_me_action_icebox_open
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_FRIDGE_DEPARTURE_WORKING_OFF] =
            R.drawable.scene_icon_me_action_icebox_close
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_FRIDGE_ECO_MODE] =
            R.drawable.scene_icon_me_action_air_energy_saving_mode
        //驾驶
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_DRIVE_MODE] =
            R.drawable.scene_icon_me_action_drive_driving_mode
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_WIPER_SENSITIVITY] =
            R.drawable.scene_icon_me_action_drive_wiper
        //应用-导航
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_NAV_HOME_AND_COMPANY] =
            R.drawable.scene_icon_me_action_navigation_initiate_navigation
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_NAV_GO_CHARGE] =
            R.drawable.scene_icon_me_action_navigation_initiate_navigation
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_NAV_GO_SOME_WHERE] =
            R.drawable.scene_icon_me_action_navigation_initiate_navigation
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_NAV_GO_SELECT_AND_MID] =
            R.drawable.scene_icon_me_action_navigation_initiate_navigation
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_NAV_ROUTE] =
            R.drawable.scene_icon_me_action_navigation_route_preference

        //应用  -播报
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_APP_VPA_REPLY] =
            R.drawable.scene_icon_me_action_apps_xiaoni_reply
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_APP_WEATHER_REPORT] =
            R.drawable.scene_icon_me_action_xiaochen_weather
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_APP_TRAVEL_REPORT] =
            R.drawable.scene_icon_me_action_trip_broadcast

        //应用-多媒体
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_OPEN_MEDIA] =
            R.drawable.scene_icon_me_action_media_open_media
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_MEDIA_CLOSE] =
            R.drawable.scene_icon_me_action_media_close_media
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_MODE] =
            R.drawable.scene_icon_me_action_media_play_mode
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_MEDIA_CONTROL] =
            R.drawable.scene_icon_me_action_media_play_control
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_MEDIA_CONTROL2] =
            R.drawable.scene_icon_me_action_media_play_control

        //播放qq音乐
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_MEDIA_QQ_SONG_STYLE] =
            R.drawable.scene_icon_me_action_media_open_qq_media
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_LIST] =
            R.drawable.scene_icon_me_action_media_open_qq_media
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_MEDIA_MOOD] =
            R.drawable.scene_icon_me_action_media_open_qq_media
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_MEDIA_YEARS] =
            R.drawable.scene_icon_me_action_media_open_qq_media
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_SINGER] =
            R.drawable.scene_icon_me_action_media_open_qq_media
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_SONG_APPOINT] =
            R.drawable.scene_icon_me_action_media_open_qq_media

        //应用-消息中心
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_APP_NOTIFY] =
            R.drawable.scene_icon_me_action_msg_text_short_prompt

        //应用-声音中心
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_APP_PLAY_PRANK_MUSIC] =
            R.drawable.scene_icon_me_action_soundspace_play_prank_music
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_APP_VOICE_VOLUME] =
            R.drawable.scene_icon_me_action_soundspace_voice_volume
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_APP_SOUND_EFFECT] =
            R.drawable.scene_icon_me_action_soundspace_sound_effect
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_APP_VOICE_SWITCH] =
            R.drawable.scene_icon_me_action_soundspace_acoustic_switch
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_APP_USE_VOICE] =
            R.drawable.scene_icon_me_action_soundspace_acoustic_mode
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_APP_OUT_SPEAKER] =
            R.drawable.scene_icon_me_action_apps_loudspeaker_shouting

        //应用-能源中心
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_APP_PREHEAT_SWITCH] =
            R.drawable.scene_icon_me_action_energycenter_preheat_switch
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_APP_DISCHARGE_SWITCH] =
            R.drawable.scene_icon_me_action_energycenter_discharge_switch
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_APP_ENDURANCE_MODE] =
            R.drawable.scene_icon_me_action_apps_endurance_mode

        //应用-应用操作
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_APP_OPEN_APP] =
            R.drawable.scene_icon_me_action_apply_open_apply
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_APP_CLOSE_APP] =
            R.drawable.scene_icon_me_action_apply_close_apply

        //系统-显示
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_CENTRAL_SCREEN_BRIGHTNESS] =
            R.drawable.scene_icon_me_action_system_screen_light
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_SCREEN_MODE] =
            R.drawable.scene_icon_me_action_system_screen_mode

        //系统-声音
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_CALL] =
            R.drawable.scene_icon_me_action_system_call_volume
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_ASSISTANT] =
            R.drawable.scene_icon_me_action_system_voice_assistant_volume
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_NAVI] =
            R.drawable.scene_icon_me_action_system_navi_volume
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_MEDIA] =
            R.drawable.scene_icon_me_action_system_media_volume
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_BLUETOOTH] =
            R.drawable.scene_icon_me_action_system_bluetooth_volume
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_MUTE] =
            R.drawable.scene_icon_me_action_system_mute_solid
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_CAT_OUT_VOLUME] =
            R.drawable.scene_icon_me_action_system_external_speaker_volume
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_RING_VOLUME] =
            R.drawable.scene_icon_me_action_system_ring_volume

        //系统-连接
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_MOBILE] = R.drawable.scene_icon_me_action_system_4g
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_BLUETOOTH_ID] =
            R.drawable.scene_icon_me_action_system_bluetooth
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_HOT_SPOT_ID] =
            R.drawable.scene_icon_me_action_system_hotspot
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_WLAN_ID] = R.drawable.scene_icon_me_action_system_wifi

        //智能设备

        //智能设备
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_AROMA_DIFFUSER_SWITCH_ID] =
            R.drawable.scene_icon_me_action_smart_aroma_diffuser_switch
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_FRAGRANCE_STALL_ID] =
            R.drawable.scene_icon_me_action_smart_aroma_diffuser_mode
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_FRAGRANCE_DURATION_ID] =
            R.drawable.scene_icon_me_action_smart_aroma_diffuser_concentration
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_FRAGRANCE_MODE_ID] =
            R.drawable.scene_icon_me_action_smart_aroma_diffuser_duration
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_AIR_PURIFIER_ID] =
            R.drawable.scene_icon_me_status_smart_device_air_quality
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_AIR_QUALITY_SYNC_ID] =
            R.drawable.scene_icon_me_status_smart_device_air_quality
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_CHILD_SEAT_BLOW_ID] =
            R.drawable.scene_icon_me_status_smart_device_children_ventilate
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_CHILD_SEAT_HEAT_ID] =
            R.drawable.scene_icon_me_status_smart_device_children_heating
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_NECK_PILLOW_SWITCH_ID] =
            R.drawable.scene_icon_me_action_smart_neck_pillow
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_NECK_PILLOW_HEAT_ID] =
            R.drawable.scene_icon_me_action_smart_neck_pillow
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_NECK_PILLOW_MASSAGE_STRENGTH_ID] =
            R.drawable.scene_icon_me_action_smart_neck_pillow
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_NECK_PILLOW_MASSAGE_TIME_ID] =
            R.drawable.scene_icon_me_action_smart_neck_pillow
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_NECK_PILLOW_MASSAGE_MODE_ID] =
            R.drawable.scene_icon_me_action_smart_neck_pillow
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_CUSHION_SWITCH_ID] =
            R.drawable.scene_icon_me_action_smart_massage_cushion
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_CUSHION_HEAT_ID] =
            R.drawable.scene_icon_me_action_smart_massage_cushion
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_PART_ID] =
            R.drawable.scene_icon_me_action_smart_massage_cushion
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_CUSHION_STRENGTH_ID] =
            R.drawable.scene_icon_me_action_smart_massage_cushion
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_CUSHION_TIME_ID] =
            R.drawable.scene_icon_me_action_smart_massage_cushion
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_SWITCH] =
            R.drawable.scene_icon_me_action_smart_colorful_ambient_light_switch
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_BRIGHTNESS] =
            R.drawable.scene_icon_me_action_smart_colorful_ambient_light_brightnes
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_MODE] =
            R.drawable.scene_icon_me_action_smart_colorful_ambient_light_mode
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_COLOR_THEME] =
            R.drawable.scene_icon_me_action_smart_colorful_ambient_light_color
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_COLOR_CUSTOMIZE] =
            R.drawable.scene_icon_me_action_smart_colorful_ambient_light_color
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_PLASMA_FRAGRANCE_SWITCH] =
            R.drawable.scene_icon_me_action_smart_aroma_diffuser_switch
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_PLASMA_FRAGRANCE_CONCENTRATION] =
            R.drawable.scene_icon_me_action_smart_aroma_diffuser_concentration
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_PLASMA_FRAGRANCE_MODE] =
            R.drawable.scene_icon_me_action_smart_aroma_diffuser_mode
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_PLASMA_FRAGRANCE_FLAVOR] =
            R.drawable.scene_icon_me_action_smart_aroma_diffuser_flavor

        //其他
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_OTHER_REARVIEW_MIRROR_SWITCH_ID] =
            R.drawable.scene_icon_me_action_others_rearview_mirror_switch
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_OTHER_REARVIEW_MIRROR_HEATING_OPEN_ID] =
            R.drawable.scene_icon_me_action_others_rearview_mirror_heating
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_OTHER_SCREEN_PROTECT] =
            R.drawable.scene_icon_me_action_delay_screen_saver
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_OTHER_SCREEN_OFF] =
            R.drawable.scene_icon_me_action_others_extinguish
        mActionIconMaps[SkillsListConstant.SKILL_ID_ACTION_OTHER_WIRELESS_CHARGER] =
            R.drawable.scene_icon_me_action_others_wirelesscharging
    }

    /**
     * 初始化添加动作背景图片
     */
    private fun initAddActionBgData() {

        //空调
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_OPEN] =
            R.drawable.scene_img_me_action_air_switch
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_TEMPERATURE] =
            R.drawable.scene_img_me_action_air_temperature
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_TEMPERATURE_TWO_LEFT] =
            R.drawable.scene_img_me_action_air_temperature
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_TEMPERATURE_TWO_RIGHT] =
            R.drawable.scene_img_me_action_air_temperature
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_AIR_VOLUME] =
            R.drawable.scene_img_me_action_air_volume
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_BLOW_MODE] =
            R.drawable.scene_img_me_action_air_blow_mode
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_INNER_LOOP] =
            R.drawable.scene_img_me_action_air_loop_mode
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_AUTO_MODE_OPEN] =
            R.drawable.scene_img_me_action_air_auto
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_COOLING_HEATING_OPEN] =
            R.drawable.scene_img_me_action_air_cool_heat
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_BLOWER_OPEN] =
            R.drawable.scene_img_me_action_air_blower
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_FRONT_DEFOG_OPEN] =
            R.drawable.scene_img_me_action_air_front_defog
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_AFTER_DEFOG_OPEN] =
            R.drawable.scene_img_me_action_air_after_defog
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_MAX_COOL] = R.drawable.scene_img_me_action_air_max_cool
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_MAX_HEAT] = R.drawable.scene_img_me_action_air_max_heat
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_SYNC] = R.drawable.scene_img_me_action_air_synchronous
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_ENERGY_CONSERVATION] =
            R.drawable.scene_img_me_action_air_energy_saving_mode
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_AIR_SELF_DESICCATION] =
            R.drawable.scene_img_me_action_air_self_drying_mode

        //门窗
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_BACK_DOOR_ELECTRIC_LOCK] =
            R.drawable.scene_img_me_action_door_back_door
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_BACK_DOOR_NORMAL_LOCK] =
            R.drawable.scene_img_me_action_door_back_door
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_LEFT_CHILD_LOCK] =
            R.drawable.scene_img_me_action_door_children_lock_l
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_RIGHT_CHILD_LOCK] =
            R.drawable.scene_img_me_action_door_children_lock_r
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_ALL_OPEN] =
            R.drawable.scene_img_me_action_door_window_all
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_VENTILATE_OPEN] =
            R.drawable.scene_img_me_action_door_window_freshleft
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_IN_FRESH_AIR] =
            R.drawable.scene_img_me_action_door_window_ventilation
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_DRIVER_MAIN_PERCENT] =
            R.drawable.scene_img_me_status_door_main_door_window
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_DRIVER_CHIEF_PERCENT] =
            R.drawable.scene_img_me_status_door_copilot_door_window
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_BACK_LEFT_PERCENT] =
            R.drawable.scene_img_me_status_door_main_door_window
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_BACK_RIGHT_PERCENT] =
            R.drawable.scene_img_me_status_door_copilot_door_window
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_SKYLIGHT] =
            R.drawable.scene_img_me_action_door_skylight
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_ELECTRIC_SUNSHADE_CURTAIN] =
            R.drawable.scene_img_me_action_door_sunrise_electric_sunshade_curtain

        //车灯
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_LAMP_LANGUAGE_MODE_ID] =
            R.drawable.scene_img_me_action_light_language_mode
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_SOA] =
            R.drawable.scene_img_me_action_light_soa_language_mode
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_SCENE_EFFECT] =
            R.drawable.scene_img_me_action_light_lamp_effect
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_FOG_LIGHT_ID] =
            R.drawable.scene_img_me_action_light_fog_lamp
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_STAR_LIGHT_STATUS] =
            R.drawable.scene_img_me_action_light_lamp_sceneslight
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_SWITCH_ID] =
            R.drawable.scene_img_me_action_light_atmosphere_switch
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_MODE_ID] =
            R.drawable.scene_img_me_action_light_atmosphere_mode
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_LIGHT_ID] =
            R.drawable.scene_img_me_action_light_atmosphere_light
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_COLOR_ID] =
            R.drawable.scene_img_me_action_light_atmosphere_color
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_COLOR_THEME_ID] =
            R.drawable.scene_img_me_action_light_atmosphere_color
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_SOA] =
            R.drawable.scene_img_me_action_light_atmosphere_soa_language_mode
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_SCENE_EFFECT_ID] =
            R.drawable.scene_img_me_action_light_atmosphere_lamp_effect
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_READ_LIGHT_SWITCH] =
            R.drawable.scene_img_me_action_light_read_lamp_switch
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_READ_LIGHT_MODE] =
            R.drawable.scene_img_me_action_light_read_lamp_mode
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_LIGHT_READ_LIGHT_CUSTOM] =
            R.drawable.scene_img_me_action_light_read_lamp_mode
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_EXTERIOR_LIGHT_MUSIC_EFFECT] =
            R.drawable.scene_img_me_action_light_headlight_music_rhythm
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_EXTERIOR_LIGHT_MODE_CONTROL] =
            R.drawable.scene_img_me_action_light_headlight_control
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_EXTERIOR_LIGHT_HEIGHT] =
            R.drawable.scene_img_me_action_light_headlight_height

        //座椅
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_MAIN_DRIVER_SEAT_MEMORY_SETTING] =
            R.drawable.scene_img_me_action_seat_main_memory
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_COPILOT_SEAT_MEMORY_SETTING] =
            R.drawable.scene_img_me_action_seat_copilot_memory
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_MAIN_DRIVER_SEAT_AIR_SETTING] =
            R.drawable.scene_img_me_action_seat_main_air
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_COPILOT_SEAT_AIR_SETTING] =
            R.drawable.scene_img_me_action_seat_copilot_air
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_AIR] =
            R.drawable.scene_img_me_action_seat_left_air
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_AIR] =
            R.drawable.scene_img_me_action_seat_right_air
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_MAIN_DRIVER_SEAT_HEATING_SETTING] =
            R.drawable.scene_img_me_action_seat_main_heating
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_COPILOT_SEAT_HEATING_SETTING] =
            R.drawable.scene_img_me_action_seat_copilot_heating
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_HEATING] =
            R.drawable.scene_img_me_action_seat_left_heating
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_HEATING] =
            R.drawable.scene_img_me_action_seat_right_heating
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_LEFT_SEAT_MASSAGE_SWITCH] =
            R.drawable.scene_img_me_action_seat_right_massage_strength
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_LEFT_SEAT_MASSAGE_INTENSITY] =
            R.drawable.scene_img_me_action_seat_right_massage_strength
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_RIGHT_SEAT_MASSAGE_SWITCH] =
            R.drawable.scene_img_me_action_seat_left_massage_strength
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_RIGHT_SEAT_MASSAGE_INTENSITY] =
            R.drawable.scene_img_me_action_seat_left_massage_strength
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_MASSAGE_OFF] =
            R.drawable.scene_img_me_action_seat_left_massage
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_MASSAGE_INTENSITY] =
            R.drawable.scene_img_me_action_seat_left_massage
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_MASSAGE_OFF] =
            R.drawable.scene_img_me_action_seat_right_massage
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_MASSAGE_INTENSITY] =
            R.drawable.scene_img_me_action_seat_right_massage
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_RIGHT_SEAT_MASSAGE_MODE] =
            R.drawable.scene_img_me_action_seat_left_massage_mode
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_LEFT_SEAT_MASSAGE_MODE] =
            R.drawable.scene_img_me_action_seat_right_massage_mode
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_MASSAGE_MODE] =
            R.drawable.scene_img_me_action_seat_back_left_massage_mode
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_MASSAGE_MODE] =
            R.drawable.scene_img_me_action_seat_back_right_massage_mode
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_LEFT_SEAT_RESET] =
            R.drawable.scene_img_me_action_seat_reset_left
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_RIGHT_SEAT_RESET] =
            R.drawable.scene_img_me_action_seat_reset_right
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_AI_BACK_SEAT] =
            R.drawable.scene_img_me_action_seat_exclusive_enjoyment
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_AI_BACK_LEFT_SEAT_FOLD] =
            R.drawable.scene_img_me_action_seat_fold_left
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_AI_BACK_RIGHT_SEAT_FOLD] =
            R.drawable.scene_img_me_action_seat_fold_right
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SEAT_AI_ZERO_G_SEAT] =
            R.drawable.scene_img_me_action_seat_zero_g

        //驾驶
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_DRIVE_MODE] = R.drawable.scene_img_me_action_drive_driving_mode
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_WIPER_SENSITIVITY] = R.drawable.scene_img_me_action_drive_wiper

        //冰箱
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_FRIDGE_SWITCH] = R.drawable.scene_img_me_action_icebox_switch
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_FRIDGE_MODE] = R.drawable.scene_img_me_action_air_cool_heat
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_FRIDGE_COOLING_TEMP] =
            R.drawable.scene_img_me_action_icebox_cold
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_FRIDGE_HEATING_TEMP] =
            R.drawable.scene_img_me_action_icebox_hot
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_FRIDGE_CONVENIENCE_MODE] =
            R.drawable.scene_img_me_action_icebox_quick
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_FRIDGE_DEPARTURE_WORKING_TIME] =
            R.drawable.scene_img_me_action_icebox_open
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_FRIDGE_DEPARTURE_WORKING_OFF] =
            R.drawable.scene_img_me_action_icebox_close
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_FRIDGE_ECO_MODE] =
            R.drawable.scene_img_me_action_air_energy_saving_mode

        //应用-导航
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_NAV_HOME_AND_COMPANY] =
            R.drawable.scene_img_me_action_apps_navigation_initiate_navigation
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_NAV_GO_CHARGE] =
            R.drawable.scene_img_me_action_apps_navigation_initiate_navigation
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_NAV_GO_SOME_WHERE] =
            R.drawable.scene_img_me_action_apps_navigation_initiate_navigation
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_NAV_GO_SELECT_AND_MID] =
            R.drawable.scene_img_me_action_apps_navigation_initiate_navigation
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_NAV_ROUTE] =
            R.drawable.scene_img_me_action_navigation_route_preference

        //应用-多媒体
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_OPEN_MEDIA] =
            R.drawable.scene_img_me_action_apps_media_open_media
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_MEDIA_CLOSE] =
            R.drawable.scene_img_me_action_apps_media_close_media
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_MODE] =
            R.drawable.scene_img_me_action_media_play_mode
        //播放控制


        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_MEDIA_CONTROL] =
            R.drawable.scene_img_me_action_media_play_control
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_MEDIA_CONTROL2] =
            R.drawable.scene_img_me_action_media_play_control
        //播放qq音乐
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_MEDIA_QQ_SONG_STYLE] =
            R.drawable.scene_img_me_action_media_open_qq_media
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_LIST] =
            R.drawable.scene_img_me_action_media_open_qq_media
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_MEDIA_MOOD] =
            R.drawable.scene_img_me_action_media_open_qq_media
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_MEDIA_YEARS] =
            R.drawable.scene_img_me_action_media_open_qq_media
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_SINGER] =
            R.drawable.scene_img_me_action_media_open_qq_media
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_SONG_APPOINT] =
            R.drawable.scene_img_me_action_media_open_qq_media

        //应用-语音播报
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_APP_VPA_REPLY] =
            R.drawable.scene_img_me_action_apps_xiaoni_reply
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_APP_WEATHER_REPORT] =
            R.drawable.scene_img_me_action_xiaochen_weather
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_APP_TRAVEL_REPORT] =
            R.drawable.scene_img_me_action_trip_broadcast

        //应用-消息中心
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_APP_NOTIFY] =
            R.drawable.scene_img_me_action_apps_msg_text_short_prompt

        //应用-声音中心
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_APP_PLAY_PRANK_MUSIC] =
            R.drawable.scene_img_me_action_soundspace_play_prank_music
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_APP_VOICE_VOLUME] =
            R.drawable.scene_img_me_action_soundspace_voice_volume
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_APP_SOUND_EFFECT] =
            R.drawable.scene_img_me_action_soundspace_sound_effect
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_APP_USE_VOICE] =
            R.drawable.scene_img_me_action_soundspace_acoustic_mode
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_APP_VOICE_SWITCH] =
            R.drawable.scene_img_me_action_soundspace_acoustic_switch
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_APP_OUT_SPEAKER] =
            R.drawable.scene_img_me_action_apps_loudspeaker_shouting

        //应用-能源中心
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_APP_PREHEAT_SWITCH] =
            R.drawable.scene_img_me_action_energycenter_preheat_switch
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_APP_DISCHARGE_SWITCH] =
            R.drawable.scene_img_me_action_energycenter_discharge_switch
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_APP_ENDURANCE_MODE] =
            R.drawable.scene_img_me_action_apps_endurance_mode

        //应用-应用操作
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_APP_OPEN_APP] = R.drawable.scene_img_me_action_apply_open_apply
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_APP_CLOSE_APP] =
            R.drawable.scene_img_me_action_apply_close_apply

        //系统设置
        //系统设置-显示
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_CENTRAL_SCREEN_BRIGHTNESS] =
            R.drawable.scene_img_me_action_system_screen_light
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_SCREEN_MODE] =
            R.drawable.scene_img_me_action_system_screen_mode
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_OTHER_SCREEN_PROTECT] =
            R.drawable.scene_img_me_action_system_screen_mode
        //系统设置-声音
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_CALL] =
            R.drawable.scene_img_me_action_system_call_volume
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_NAVI] =
            R.drawable.scene_img_me_action_system_navi_volume
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_ASSISTANT] =
            R.drawable.scene_img_me_action_system_voice_assistant_volume
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_BLUETOOTH] =
            R.drawable.scene_img_me_action_system_bluetooth_volume
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_MEDIA] =
            R.drawable.scene_img_me_action_system_media_volume
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_CAT_OUT_VOLUME] =
            R.drawable.scene_img_me_action_system_external_speaker_volume
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_RING_VOLUME] =
            R.drawable.scene_img_me_action_system_ring_volume
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_MUTE] = R.drawable.scene_img_me_action_system_mute_solid
        //系统设置-连接
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_BLUETOOTH_ID] =
            R.drawable.scene_img_me_action_system_bluetooth
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_HOT_SPOT_ID] =
            R.drawable.scene_img_me_action_system_hotspot
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_WLAN_ID] = R.drawable.scene_img_me_action_system_wifi
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SYSTEM_MOBILE] = R.drawable.scene_img_me_action_system_4g

        //智能设备
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_AROMA_DIFFUSER_SWITCH_ID] =
            R.drawable.scene_img_me_action_smart_aroma_diffuser_switch
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_FRAGRANCE_STALL_ID] =
            R.drawable.scene_img_me_action_smart_aroma_diffuser_concentration
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_FRAGRANCE_DURATION_ID] =
            R.drawable.scene_img_me_action_smart_aroma_diffuser_duration
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_FRAGRANCE_MODE_ID] =
            R.drawable.scene_img_me_action_smart_aroma_diffuser_mode

        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_AIR_PURIFIER_ID] =
            R.drawable.scene_img_me_status_smart_device_air_quality
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_AIR_QUALITY_SYNC_ID] =
            R.drawable.scene_img_me_status_smart_device_air_quality

        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_CHILD_SEAT_BLOW_ID] =
            R.drawable.scene_img_me_status_smart_device_children_ventilate
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_CHILD_SEAT_HEAT_ID] =
            R.drawable.scene_img_me_status_smart_device_children_heating

        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_NECK_PILLOW_SWITCH_ID] =
            R.drawable.scene_img_me_action_smart_neck_pillow
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_NECK_PILLOW_HEAT_ID] =
            R.drawable.scene_img_me_action_smart_neck_pillow
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_NECK_PILLOW_MASSAGE_STRENGTH_ID] =
            R.drawable.scene_img_me_action_smart_neck_pillow
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_NECK_PILLOW_MASSAGE_TIME_ID] =
            R.drawable.scene_img_me_action_smart_neck_pillow
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_NECK_PILLOW_MASSAGE_MODE_ID] =
            R.drawable.scene_img_me_action_smart_neck_pillow

        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_CUSHION_SWITCH_ID] =
            R.drawable.scene_img_me_action_smart_massage_cushion
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_CUSHION_HEAT_ID] =
            R.drawable.scene_img_me_action_smart_massage_cushion
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_PART_ID] =
            R.drawable.scene_img_me_action_smart_massage_cushion
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_CUSHION_STRENGTH_ID] =
            R.drawable.scene_img_me_action_smart_massage_cushion
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_CUSHION_TIME_ID] =
            R.drawable.scene_img_me_action_smart_massage_cushion

        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_SWITCH] =
            R.drawable.scene_img_me_action_smart_colorful_ambient_light_switch
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_BRIGHTNESS] =
            R.drawable.scene_img_me_action_smart_colorful_ambient_light_brightness
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_MODE] =
            R.drawable.scene_img_me_action_smart_colorful_ambient_light_mode
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_COLOR_THEME] =
            R.drawable.scene_img_me_action_smart_colorful_ambient_light_color_theme
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_COLOR_CUSTOMIZE] =
            R.drawable.scene_img_me_action_smart_colorful_ambient_light_color_theme

        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_PLASMA_FRAGRANCE_SWITCH] =
            R.drawable.scene_img_me_action_smart_aroma_diffuser_switch
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_PLASMA_FRAGRANCE_CONCENTRATION] =
            R.drawable.scene_img_me_action_smart_aroma_diffuser_concentration
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_PLASMA_FRAGRANCE_MODE] =
            R.drawable.scene_img_me_action_smart_aroma_diffuser_mode
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_SMART_PLASMA_FRAGRANCE_FLAVOR] =
            R.drawable.scene_img_me_action_smart_aroma_diffuser_flavor

        //其他
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_OTHER_REARVIEW_MIRROR_SWITCH_ID] =
            R.drawable.scene_img_me_action_others_rearview_mirror_switch
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_OTHER_REARVIEW_MIRROR_HEATING_OPEN_ID] =
            R.drawable.scene_img_me_action_others_rearview_mirror_heating
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_RECOMMEND_DELAY_ID] = R.drawable.scene_img_me_action_delay
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_OTHER_SCREEN_PROTECT] =
            R.drawable.scene_img_me_action_delay_screen_saver
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_OTHER_SCREEN_OFF] =
            R.drawable.scene_img_me_action_others_extinguish
        mActionBgMaps[SkillsListConstant.SKILL_ID_ACTION_OTHER_WIRELESS_CHARGER] =
            R.drawable.scene_img_me_action_others_wirelesscharging

    }

    //门窗的触发条件是4个公用1个ID
    fun getTriggerConditionIcon(
        id: Int, condition: ScenarioInfo.Condition? = null
    ): Int {
        if (condition != null) { //判断是否是门窗ID
            if (condition.skillId == SkillsListConstant.SKILL_ID_TRIGGER_DOOR_ALL_STATE) {
                val resId = when (condition.input[0].value) {
                    "1" -> {
                        R.drawable.scene_icon_me_trigger_door_upperleft_door
                    }

                    "2" -> {
                        R.drawable.scene_icon_me_trigger_door_upperright_door
                    }

                    "3" -> {
                        R.drawable.scene_icon_me_trigger_door_lowerleft_door
                    }

                    else -> {
                        R.drawable.scene_icon_me_trigger_door_lowerright_door
                    }
                }
                return resId
            }
        }
        return mTriggerIconMaps[id] ?: R.drawable.scene_icon_common_skill_default
    }


    fun getStatusConditionIcon(id: Int): Int {
        return mStatusIconMaps[id] ?: R.drawable.scene_icon_common_skill_default
    }

    fun getActionBgImg(key: Int): Int {
        //return mActionBgMaps[key] ?: R.drawable.scene_selector_edit_action_item_bg
        return mActionBgMaps[key] ?: R.drawable.scene_shape_detail_action_item_bg
    }

    fun getActionIcon(id: Int): Int { //动作有个氛围灯是公用同一个ID的，需要在获取的时候做判断
        return mActionIconMaps[id] ?: R.drawable.scene_icon_common_skill_default
    }

}



