package com.dfl.smartscene.util;

import android.content.Context;

import com.dfl.android.common.util.GsonUtils;
import com.dfl.smartscene.bean.main.MySceneBean;
import com.dfl.smartscene.bean.main.SceneSaveLocalDataBean;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.tencent.mmkv.MMKV;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * author : wswang
 * e-mail : <EMAIL>
 * time   : 2022/08/04
 * desc   :缓存场景相关本地数据
 * version: 1.0
 */
public class MMKVUtils {
    /**
     * 场景编辑的缓存数据，用以下一次编辑时能还原上次编辑的场景数据，新建场景草稿
     */
    private static final String SCENE_EDIT_DATA = "SCENE_EDIT_DATA";
    /**
     * 社区场景下载过来的原始数据 ，用于重新编辑
     */
    private static final String SCENE_DOWNLOAD_ORIGINAL_DATA = "SCENE_DOWNLOAD_ORIGINAL_DATA";
    /**
     * 场景列表顺序的缓存，用以呈现上一次车机关机前场景的顺序
     */
    private static final String SCENE_SAVE_SEQUENCE = "SCENE_SAVE_SEQUENCE";
    /**
     * 创建场景ID的序号，从1开始累加
     */
    private static final String SCENE_ID_NO = "SCENE_ID_NO";
    /**
     * 车辆配置信息
     */
    private static final String VEHICLE_CONFIG = "vehicle_config";
    /**
     * 缓存发现场景列表数据信息
     */
    private static final String SCENE_DISCOVER_LIST = "scene_discover_list";
    /**
     * 没有初始化过社区场景页，我的场景页又上传场景成功时 ，需要社区的最新tag页闪烁
     */
    private static final String ME_UPLOADSUCCESS_2_NEWLASTTAG_SHOWLOTTIEANIMATION = "Me_UploadSuccess_2_NewLastTag_ShowLottieAnimation";
    /** 车控临停信号同时是场景禁用状态记录 true进入临停，false退出 */
    private static final String CAR_CONTROL_DISABLE_SCENES = "car_control_disable_scenes";
    /** 自在进行中信号 true自在进行中，false退出 */
    private static final String COMFORT_DISABLE_SCENES = "comfort_disable_scenes";

    private MMKV mmkv;

    private MMKVUtils() {

    }

    public static MMKVUtils getInstance() {
        return InstanceHolder.instance;
    }

    public void init(Context context) {
        MMKV.initialize(context.getApplicationContext());
        mmkv = MMKV.defaultMMKV();
    }

    /** 保存草稿场景编辑的缓存数据 */
    public void saveSceneEditData(boolean isAddScene, String sceneId,
                                  MySceneBean sceneEditBean) {
        if (mmkv != null) {
            if (isAddScene) {
                mmkv.encode(SCENE_EDIT_DATA, sceneEditBean);
            } else {
                if (sceneId != null)
                    mmkv.encode(sceneId, sceneEditBean);
            }
        }
    }

    /** 删除草稿场景编辑的缓存数据 */
    public void deleteSceneEditData(boolean isAddScene, String sceneId) {
        if (mmkv != null) {
            if (isAddScene) {
                mmkv.removeValueForKey(SCENE_EDIT_DATA);
            } else {
                if (sceneId != null)
                    mmkv.removeValueForKey(sceneId);
            }
        }
    }

    /** 获取草稿场景编辑的缓存数据 */
    public MySceneBean getSceneEditData(boolean isAddScene, String sceneId) {
        if (mmkv != null) {
            if (isAddScene) {
                return mmkv.decodeParcelable(SCENE_EDIT_DATA, MySceneBean.class);
            } else {
                if (sceneId != null)
                    return mmkv.decodeParcelable(sceneId, MySceneBean.class);
            }
        }
        return null;
    }

    /** 保存社区场景下载过来的原始数据 */
    public void saveDownloadOriginalSceneEditBean(MySceneBean sceneEditBean) {
        if (mmkv != null) {
            mmkv.encode(SCENE_DOWNLOAD_ORIGINAL_DATA, sceneEditBean);
        }
    }

    /** 获取社区场景下载过来的原始数据 */
    public MySceneBean getDownloadOriginalSceneEditBean() {
        if (mmkv != null) {
            return mmkv.decodeParcelable(SCENE_DOWNLOAD_ORIGINAL_DATA, MySceneBean.class);
        }
        return null;
    }

    /** 保存场景列表顺序的缓存 */
    public void saveSceneSequence(List<SceneSaveLocalDataBean> sceneIdList) {
        if (mmkv != null) {
            Gson gson = new Gson();
            String json = gson.toJson(sceneIdList);
            mmkv.encode(SCENE_SAVE_SEQUENCE, json);
        }
    }

    /**
     * 获取场景列表顺序
     *
     * @return 我的场景列表数据
     */
    public List<SceneSaveLocalDataBean> getSceneSequence() {
        if (mmkv != null) {
            String json = mmkv.decodeString(SCENE_SAVE_SEQUENCE, "");
            if (json.isEmpty()) {
                return new ArrayList<>();
            }
            Gson gson = new Gson();
            Type type = new TypeToken<ArrayList<SceneSaveLocalDataBean>>() {
            }.getType();
            return gson.fromJson(json, type);
        }
        return new ArrayList<>();
    }

    /** 清空场景列表顺序的缓存 */
    public void clearLocalSceneData() {
        if (mmkv != null) {
            mmkv.removeValueForKey(SCENE_SAVE_SEQUENCE);
        }
    }

    /**
     * 将场景ID的序号增加1
     */
    public void addSceneIdNo() {
        mmkv.putInt(SCENE_ID_NO, getSceneIdNo() + 1);
    }

    /**
     * 获取上一次生成场景ID的序号，从1开始计数
     *
     * @return 当前已保存场景ID的序号
     */
    public int getSceneIdNo() {
        return mmkv.getInt(SCENE_ID_NO, 0);
    }

    public void saveVehicleConfig(String vehicleConfig) {
        mmkv.putString(VEHICLE_CONFIG, vehicleConfig);
    }

    public String getVehicleConfig() {
        return mmkv.getString(VEHICLE_CONFIG, "");
    }

    /** 保存发现场景列表数据信息 */
    public void saveDiscoverSceneData(List<String> list) {
        String json = GsonUtils.toJson(list);
        mmkv.putString(SCENE_DISCOVER_LIST, json);
    }

    /** 获取发现场景列表数据信息 */
    public List<String> getDiscoverSceneData() {
        String json = mmkv.getString(SCENE_DISCOVER_LIST, "");
        if (json == null || json.isEmpty())
            return null;
        return Arrays.asList(GsonUtils.fromJson(json, String[].class));
    }

    //======社区场景========

    /** 保存是否 没有初始化过社区场景页 */
    public void saveMeUploadShowLottieAnimation(boolean isMeUploadShowLottieAnimation) {
        mmkv.putBoolean(ME_UPLOADSUCCESS_2_NEWLASTTAG_SHOWLOTTIEANIMATION, isMeUploadShowLottieAnimation);
    }

    /** 获取是否 没有初始化过社区场景页 */
    public boolean getMeUploadShowLottieAnimation() {
        return mmkv.getBoolean(ME_UPLOADSUCCESS_2_NEWLASTTAG_SHOWLOTTIEANIMATION, false);
    }

    /**
     * 保存车控临停信号
     *
     * @param isCarControlDisableScenes true进入 false退出
     */
    public void saveCarControlDisableScenes(boolean isCarControlDisableScenes) {
        mmkv.putBoolean(CAR_CONTROL_DISABLE_SCENES, isCarControlDisableScenes);
    }

    /**
     * 获取车控临停信号
     *
     * @return true进入 false退出,默认false
     */
    public boolean getCarControlDisableScenes() {
        return mmkv.getBoolean(CAR_CONTROL_DISABLE_SCENES, false);
    }

    /**
     * 保存自在进行信号
     *
     * @param isComfortDisableScenes true进入 false退出
     */
    public void saveComfortDisableScenes(boolean isComfortDisableScenes) {
        mmkv.putBoolean(COMFORT_DISABLE_SCENES, isComfortDisableScenes);
    }

    /**
     * 获取自在进行信号
     *
     * @return true进入 false退出,默认false
     */
    public boolean getComfortDisableScenes() {
        return mmkv.getBoolean(COMFORT_DISABLE_SCENES, false);
    }

    private static final class InstanceHolder {
        private static final MMKVUtils instance = new MMKVUtils();
    }


}
