package com.dfl.android.animationlib.tablayout

import android.animation.Animator
import android.animation.AnimatorInflater
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.app.UiModeManager
import android.content.Context
import android.graphics.Canvas
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.GradientDrawable
import android.text.TextUtils
import android.util.AttributeSet
import android.view.Gravity
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import android.view.animation.PathInterpolator
import android.widget.TextView
import androidx.core.view.ViewCompat
import androidx.core.view.isGone
import com.dfl.android.animationlib.R
import com.dfl.android.animationlib.tools.LogUtils
import com.dfl.android.animationlib.tools.LogUtils.logI
import com.dfl.android.animationlib.tools.SkinExecutor
import com.dfl.common.nightmode.res.SkinCompatVectorResources
import com.dfl.common.nightmode.widget.SkinCompatSupportable
import kotlin.math.abs

/**
 * @description 通用tabLayout
 * <AUTHOR>
 * @createTime 2025/5/12
 */
class CommonTabLayout : ViewGroup, SkinCompatSupportable, SkinExecutor.Listener {

    private val TAG = "CommonTabLayoutAnima-" + hashCode()

    private var mContext: Context? = null
    private var textColor = 0
    private var textColorSelected = 0 // textColor颜色值
    private var textColorId = 0
    private var textColorSelectedId = 0 // textColor颜色值

    private var lastSelectedPosition = -1 // 上一个选中的Tab位置
    var selectedIndex: Int = 0 // 当前选中的Tab位置
        private set
    private var slidingIndicator = GradientDrawable() // 指示器，背景滑块
    var listener: OnTabSelectedListener? = null // Tab选中回调接口
    private var isOnlyCallBack = false // 点击时是否只回调，不执行切换动画

    private var scaledEdgeSlop = 0

    private var indicatorAnimator: ValueAnimator? = null


    var indicatorColor = 0
    var indicatorRadius = 0
    var indicatorWidth = 0
    var indicatorHeight = 0
    var indicatorMargin = 0
    var titleSize = 0
    var tabHeight = 0
    var tabPadding = 0
    var tabMargin = 0

    //tab宽度是否相等
    var tabIsEqual = false


    private var indicatorOffsetX = 0f

    private var isAllowDrawIndicator = true

    private var isStartDraw = false

    private val skinExecutor = SkinExecutor()

    private var isLayoutCompleted = false

    private val lastSelectedPositionScale = 0.95f

    constructor(context: Context) : super(context) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        initRes(attrs, context)
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        initRes(attrs, context)
        init(context)
    }


    private fun init(context: Context) {
        setWillNotDraw(false) //重写onDraw方法,需要调用这个方法来清除flag

        // 获取系统定义的最小拖动距离
        scaledEdgeSlop = ViewConfiguration.get(context).scaledEdgeSlop
        logI(TAG, "init---scaledEdgeSlop=scaledEdgeSlop")

        slidingIndicator.setColor(indicatorColor)
        slidingIndicator.cornerRadius = indicatorRadius.toFloat()
        slidingIndicator.setBounds(0, 0, indicatorWidth, indicatorHeight)

        listener?.onInitViewComplete(false)

    }

    @SuppressLint("ResourceType")
    private fun initRes(attrs: AttributeSet?, context: Context) {
        mContext = context

        val ta = context.obtainStyledAttributes(attrs, R.styleable.CommonTabLayout)

        val hasType = ta.hasValue(R.styleable.CommonTabLayout_tab_layout_type) //方向，左右还是上下
        val hasTextColor =
            ta.hasValue(R.styleable.CommonTabLayout_tab_unselect_title_color) //选中item背景
        val hasTextColorSelected =
            ta.hasValue(R.styleable.CommonTabLayout_tab_select_title_color) //item图标个数

        if (hasType) {
            ta.getInt(R.styleable.CommonTabLayout_tab_layout_type, 0)
        } else {
            0
        }

        if (hasTextColor) {
            textColorId = ta.getResourceId(R.styleable.CommonTabLayout_tab_unselect_title_color, 0)
            textColor = ta.getColor(
                R.styleable.CommonTabLayout_tab_unselect_title_color, resources.getColor(
                    R.color.nissan_custom_hor_tab_tittle
                )
            )
        } else {
            textColorId = R.color.nissan_custom_hor_tab_tittle
            textColor = resources.getColor(R.color.nissan_custom_hor_tab_tittle)
        }

        if (hasTextColorSelected) {
            textColorSelectedId =
                ta.getResourceId(R.styleable.CommonTabLayout_tab_select_title_color, 0)
            textColorSelected = ta.getColor(
                R.styleable.CommonTabLayout_tab_select_title_color, resources.getColor(
                    R.color.nissan_custom_hor_select_tab_tittle
                )
            )
        } else {
            textColorSelectedId = R.color.nissan_custom_hor_select_tab_tittle
            textColorSelected = resources.getColor(R.color.nissan_custom_hor_select_tab_tittle)
        }


        indicatorColor = ta.getColor(
            R.styleable.CommonTabLayout_tab_indicator_color,
            R.color.nissan_switch_bg_open
        )
        indicatorRadius =
            ta.getDimensionPixelSize(R.styleable.CommonTabLayout_tab_indicator_radius, 2)
        indicatorWidth =
            ta.getDimensionPixelSize(R.styleable.CommonTabLayout_tab_indicator_width, 45)
        indicatorHeight =
            ta.getDimensionPixelSize(R.styleable.CommonTabLayout_tab_indicator_height, 4)
        indicatorMargin =
            ta.getDimensionPixelSize(R.styleable.CommonTabLayout_tab_indicator_margin, 13)
        titleSize = ta.getDimensionPixelSize(R.styleable.CommonTabLayout_tab_font_size, 30)
        tabHeight = ta.getDimensionPixelSize(R.styleable.CommonTabLayout_tab_height, 84)
        tabPadding = ta.getDimensionPixelSize(R.styleable.CommonTabLayout_tab_padding, 32)
        tabMargin = ta.getDimensionPixelSize(R.styleable.CommonTabLayout_tab_margin, 0)
        tabIsEqual = ta.getBoolean(R.styleable.CommonTabLayout_tab_is_equal, false)


        LogUtils.logI(
            TAG,
            "initRes---tabHeight=$tabHeight, tabIsEqual=$tabIsEqual, indicatorWidth=$indicatorWidth, indicatorHeight=$indicatorHeight, indicatorColor=${indicatorColor}"
        )
        ta.recycle()
    }

    /**
     * 实际上真正添加item的地方，要等到OnMeasure之后
     *
     * @param items 列表数据Object
     */
    fun setTabsData(items: List<String>) {
        if (childCount != 0) {
            removeAllViews()
        }
        for (i in items.indices) {

            //根据类型实例化
            val textView = createTextView(items[i], Gravity.CENTER)
            textView.tag = i
//            val paint: Paint = textView.paint
//            val tabWidth = paint.measureText(textView.text.toString()).toInt() + tabPadding *2
            textView.maxLines = 1
            textView.includeFontPadding = false
            textView.ellipsize = TextUtils.TruncateAt.END
            val layoutParams =
                MarginLayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.MATCH_PARENT)

            textView.setOnClickListener {
                onTabClicked(i, true, true)
            }
            addView(textView, i, layoutParams)
        }
        requestLayout()
        LogUtils.logI(TAG, "setTabsData---finish")
    }


    //放大动画
    private fun zoomInAnimation(view: View?) {
        if (view == null) {
            logI(TAG, "zoomInAnimation---view is null")
            return
        }
        logI(TAG, "zoomInAnimation---")
        ViewCompat.animate(view)
            .scaleX(1.02f)
            .scaleY(1.02f)
            .setDuration(167)
            .setInterpolator(PathInterpolator(0.0f, 0.0f, 1.0f, 1.0f))
            .start()
    }

    //缩小动画
    private fun zoomOutAnimation(view: View?) {
        if (view == null) {
            logI(TAG, "zoomOutAnimation---view is null")
            return
        }
        logI(TAG, "zoomOutAnimation---")
        ViewCompat.animate(view)
            .scaleX(1f)
            .scaleY(1f)
            .setDuration(133)
            .setInterpolator(PathInterpolator(0.0f, 0.0f, 1.0f, 1.0f))
            .start()
    }

    private fun startTextScale(tabView: View?) {
        logI(TAG, "startTextScale---")
        tabView?.let { toggleTouchAnimation(it) }
    }

    /**
     * 创建纯文本TextView作为Tab项
     *
     * @param title 文本内容
     * @return TextView
     */
    @SuppressLint("ResourceType")
    private fun createTextView(title: String, gravity: Int): TextView {
        val textView = TextView(context)
        textView.id = generateViewId()
        textView.text = title
        textView.gravity = gravity
        textView.textSize = titleSize.toFloat() // 设置文字大小
        textView.setTextColor(textColor) // 未选中文字颜色
        textView.stateListAnimator = AnimatorInflater.loadStateListAnimator(
            context,
            R.animator.tab_switch_image_change
        )
        if (tabPadding > 0) {
            textView.setPadding(tabPadding, 0, tabPadding, 0)
        }
        LogUtils.logI(
            TAG,
            "createTextView---textSize=$titleSize, textColor=${textColor}, $tabPadding=$tabPadding"
        )
        return textView
    }

    /**
     * tab🈶选中到未选中到动画
     */
    private fun unSelectTabAnim(position: Int, hasAnima: Boolean = true) {
        LogUtils.logI(TAG,"unSelectTabAnim---position=$position, hasAnima=$hasAnima")
        if (isPositionValid(position)) {

            if (hasAnima) {
                val view = getChildAt(position)
                if (view is TextView) {
                    view.setTextColor(textColor)

                    val animator = ValueAnimator.ofFloat(1f, lastSelectedPositionScale)
                    animator.setDuration(67) // 放大时间
                    animator.interpolator = PathInterpolator(0.00f, 0.00f, 1.00f, 1.00f)
                    animator.addUpdateListener { animation: ValueAnimator ->
                        val scale = animation.animatedValue as Float
                        view.scaleX = scale
                        view.scaleY = scale
                    }
                    animator.start()
                }
            } else {
                updateLastPositionTabViewStatusNoAnima(position, lastSelectedPositionScale)
            }

            listener?.onTabUnSelected(lastSelectedPosition)
        } else {
            LogUtils.logI(
                TAG,
                "unSelectTabAnim---position=$position, selectedIndex=$selectedIndex, childCount=$childCount"
            )
        }
        updateTabViewSelectedStatus(selectedIndex)
    }

    private fun updateLastPositionTabViewStatusNoAnima(position: Int, scale: Float) {
        if (isPositionValid(position)) {
            val view = getChildAt(position)
            if (view is TextView) {
                view.setTextColor(
                    textColor
                )
            }
            view.scaleX = scale
            view.scaleY = scale
        }
    }

    private fun updateTabViewSelectedStatus(position: Int) {
        if (isPositionValid(position)) {
            val view = getChildAt(position)
            view.isSelected = true
            if (view is TextView) {
                view.setTextColor(
                    textColorSelected
                )
            }
        }
    }

    private var lastClickTime = 0L
    private val CLICK_INTERVAL = 100

    /**
     * 处理Tab点击事件
     *
     * @param position 点击的位置
     * @param isTouch
     */
    private fun onTabClicked(position: Int, isManual: Boolean, isTouch: Boolean) {
        val currentTimeMillis = System.currentTimeMillis()
        val time = abs(currentTimeMillis - lastClickTime)
        LogUtils.logI(TAG, "onTabClicked---position=$position, time=$time")
        if (time < CLICK_INTERVAL) {
            return
        }
        lastClickTime = currentTimeMillis
        logI(
            TAG,
            "onTabClicked---position=$position,selectedPosition=$selectedIndex, isManual=$isManual, isTouch=$isTouch, isSwitchTab=$isOnlyCallBack"
        )

        if (isPositionValid(position)) {
            zoomOutAnimation(getTabView(position))
            if (isOnlyCallBack) {
                listener?.onTabClicked(position)
            } else {
                setCurrentTab(position, isFromUser = true)
            }

        }
    }

    private fun cancelIndicatorAnima() {
        if (indicatorAnimator != null) {
            indicatorAnimator!!.cancel()
            indicatorAnimator = null
        }
    }

    /**
     * 选中item的动效
     *
     * @param view 选中item的View
     */
    private fun toggleTouchAnimation(view: View) {
        ViewCompat.animate(view)
            .scaleX(1.02f)
            .scaleY(1.02f)
            .setDuration(167)
            .setInterpolator(PathInterpolator(0.0f, 0.0f, 1.0f, 1.0f))
            .withEndAction {
                ViewCompat.animate(view)
                    .scaleX(1f)
                    .scaleY(1f)
                    .setDuration(133)
                    .setInterpolator(PathInterpolator(0.0f, 0.0f, 1.0f, 1.0f))
                    .start()
            }
            .start()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {

        val widthSize = MeasureSpec.getSize(widthMeasureSpec)
        val heightSize = MeasureSpec.getSize(heightMeasureSpec)

        if (tabIsEqual) {
            super.onMeasure(widthMeasureSpec, tabHeight)
        } else {
            var totalWidth = 0

            val widthMode = MeasureSpec.getMode(widthMeasureSpec)
            val heightMode = MeasureSpec.getMode(heightMeasureSpec)
            val parentWidth = MeasureSpec.getSize(widthMeasureSpec)
            val parentHeight = MeasureSpec.getSize(heightMeasureSpec)


            measureChildren(widthMeasureSpec, heightMeasureSpec)

            // 遍历所有子 View
            for (i in 0 until childCount) {
                val child = getChildAt(i)
                if (child.visibility == GONE) continue

                // 获取子 View 的测量宽高
                val childWidth = child.measuredWidth

                totalWidth += childWidth

                if (i in 1 until (childCount - 1)) {
                    if (tabMargin > 0) {
                        totalWidth + tabMargin
                    }
                }
            }


            // 处理父容器的 padding
            totalWidth += paddingLeft + paddingRight
            val totalHeight = paddingTop + paddingBottom + tabHeight


            // 根据父容器的 MeasureSpec 确定最终尺寸
            val finalWidth = resolveSize(totalWidth, widthMeasureSpec)
            val finalHeight = resolveSize(totalHeight, heightMeasureSpec)

            setMeasuredDimension(finalWidth, finalHeight)

            LogUtils.logI(
                TAG,
                "onMeasure----finalWidth=$finalWidth, finalHeight=$finalHeight, totalHeight=$totalHeight, totalWidth=$totalWidth"
            )
        }
    }

    override fun generateLayoutParams(attrs: AttributeSet?): LayoutParams {
        return MarginLayoutParams(context, attrs)
    }

    override fun generateLayoutParams(p: LayoutParams?): LayoutParams {
        return MarginLayoutParams(p)
    }

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        LogUtils.logI(TAG, "onLayout---changed=$changed, left=$l, top=$t, r=$r, b=$b")
        if (childCount > 0) {
            var childLeft = paddingLeft
            for (i in 0 until childCount) {
                val view = getChildAt(i)
                if (view.isGone) {
                    view.visibility = View.VISIBLE
                }
                val childRight = childLeft + view.measuredWidth
                view.layout(childLeft, paddingTop, childRight, paddingTop + view.measuredHeight)

                LogUtils.logI(
                    TAG,
                    "onLayout----i=$i, childLeft=$childLeft, childRight=$childRight, measuredWidth=${view.measuredWidth}, measuredHeight=${view.measuredHeight}, [${view.left}, ${view.top}, ${view.right}, ${view.bottom}]"
                )

                childLeft = childRight
                if (i in 1 until childCount - 1) {
                    childLeft += tabMargin
                }
            }

            if (!isLayoutCompleted) {
                isLayoutCompleted = true
                LogUtils.logI(TAG, "onLayout----isLayoutCompleted=$isLayoutCompleted")
                listener?.onInitViewComplete(true)
            }
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (!isStartDraw) {
            isStartDraw = true

            if (isPositionValid(selectedIndex)) {
                selectedIndex = 0
            }
            val view = getChildAt(selectedIndex)
            indicatorOffsetX =
                view.left.toFloat() + view.width / 2f - slidingIndicator.bounds.width() / 2f
            LogUtils.logI(
                TAG,
                "onDraw---position=$selectedIndex, indicatorOffsetX=$indicatorOffsetX, width=$width, height=$height, isAllowDrawIndicator=$isAllowDrawIndicator"
            )
//            if (isPositionValid(selectedIndex))
//                updateUnselectedItem(lastSelectedPosition)
//            }
        }
        canvas.let {
            if (isAllowDrawIndicator) {
                drawIndicator(it)
            }
        }
    }


    private fun drawIndicator(canvas: Canvas) {

        if (indicatorOffsetX < 0 || indicatorOffsetX > (width - indicatorWidth / 2)) {
            LogUtils.logI(TAG, "drawIndicator---indicatorOffsetX=$indicatorOffsetX")
        } else {
            canvas.save()
            canvas.translate(
                indicatorOffsetX,
                (height - indicatorMargin - slidingIndicator.bounds.height() / 2f)
            )
            slidingIndicator.draw(canvas)
            canvas.restore()
        }
    }

    /**
     * 移动指示器的动画
     *
     * @param newPosition 位置索引
     */
    private fun moveIndicator(newPosition: Int, hasAnima: Boolean = true) {
        logI(
            TAG,
            "moveIndicator---childCount=$childCount, newPosition=$newPosition, hasAnima=$hasAnima"
        )
        if (isPositionValid(newPosition)) {
            cancelIndicatorAnima()

            val selectedTab = getChildAt(newPosition)

            var to = (selectedTab.left + selectedTab.width / 2) - indicatorWidth / 2f

            LogUtils.logI(TAG, "moveIndicator---to=$to, width=${selectedTab.width}")
            if (to < 0) {
                to = 0f
            }

            if (hasAnima) {
                indicatorAnimator = ValueAnimator.ofFloat(
                    indicatorOffsetX,
                    to
                ) //位置要减去padding的宽度

                indicatorAnimator?.addUpdateListener { animation: ValueAnimator ->
                    indicatorOffsetX = animation.animatedValue as Float
                    postInvalidate()
                }
                indicatorAnimator?.addListener(object : Animator.AnimatorListener {
                    override fun onAnimationStart(animation: Animator) {
                    }

                    override fun onAnimationEnd(animation: Animator) {
                        logI(TAG, "moveIndicator---onAnimationEnd")
                        indicatorAnimator = null
                    }

                    override fun onAnimationCancel(animation: Animator) {
                        indicatorOffsetX = to
                        postInvalidate()
                        indicatorAnimator = null
                        logI(TAG, "moveIndicator---onAnimationCancel")

                    }

                    override fun onAnimationRepeat(animation: Animator) {
                    }
                })
                indicatorAnimator?.interpolator = PathInterpolator(0.22f, 1.00f, 0.36f, 1.00f)
                indicatorAnimator?.setDuration(500) // 动画时长400ms
                indicatorAnimator?.start()
            } else {
                indicatorOffsetX = to
                postInvalidate()
            }
        }

    }


    /**
     * 判断是否有tab不选中状态
     *
     * @param s
     */
    fun setIsSwitchTab(s: Boolean) {
        isOnlyCallBack = s
    }

    /**
     * 适配白天黑夜模式
     */
    private fun adapterSkinColor() {
        val isNightMode = isNightMode

        logI(
            TAG,
            "adapterSkinColor----isNightMode=$isNightMode"
        )


        if (textColorId != 0) {
            val drawableBackgroundBaseColor =
                SkinCompatVectorResources.getDrawableCompat(context, textColorId)
            if (drawableBackgroundBaseColor is ColorDrawable) {
                textColor = drawableBackgroundBaseColor.color
            }
        }

        if (textColorSelectedId != 0) {
            val drawableBackgroundBaseColor1 =
                SkinCompatVectorResources.getDrawableCompat(context, textColorSelectedId)
            if (drawableBackgroundBaseColor1 is ColorDrawable) {
                textColorSelected = drawableBackgroundBaseColor1.color
            }
        }

        if (childCount > 0) {
            for (i in 0 until childCount) {
                val textView = getChildAt(i) as TextView
                textView?.let {
                    if (i == selectedIndex) {
                        textView.setTextColor(textColorSelected)
                    } else {
                        textView.setTextColor(textColor)
                    }
                }
            }
        }
        postInvalidate()
    }

    private val isNightMode: Boolean
        /**
         * 判断是否是黑夜模式
         *
         * @return true:是黑夜模式，false:不是黑夜模式
         */
        get() {
            val uiManager = mContext!!.getSystemService(Context.UI_MODE_SERVICE) as UiModeManager
            return uiManager.nightMode == UiModeManager.MODE_NIGHT_YES
        }


    /**
     * 外部回调接口定义
     * 选中事件的外部回调接口定义
     * 参数说明:
     * position选中下标，从0开始；
     * viewId选中的ViewId，用于取得VIew进行额外处理；
     * isManual为区分是否是手动点击回调还是代码设置点击回调
     */
    interface OnTabSelectedListener {

        fun onTabClicked(position: Int)

        //从未选中态变为选中态的回调
        fun onTabSelected(position: Int, isFromUser: Boolean)

        //从选中态变为未选中态的回调
        fun onTabUnSelected(lastPosition: Int)

        fun onInitViewComplete(isComplete: Boolean?)
    }

    /**
     * vp主动滚动切换时控制tab切换
     *
     * @param index
     */
    fun setCurrentTab(index: Int, hasAnima: Boolean = true, isFromUser: Boolean = false) {
        LogUtils.logI(
            TAG,
            "setCurrentTab---selectedIndex=$selectedIndex, index=$index, hasAnima=$hasAnima, isLayoutCompleted=$isLayoutCompleted, isFromUser=$isFromUser"
        )
        if (!isPositionValid(index)) {
            LogUtils.logI(
                TAG, "setCurrentTab---index is not Valid!!"
            )
            return
        }

        if (selectedIndex != index) {
            lastSelectedPosition = selectedIndex
            selectedIndex = index
            isAllowDrawIndicator = true

            if (isStartDraw) {
                if (hasAnima) {
                    getTabView(selectedIndex)?.let {
                        toggleTouchAnimation(it)
                    }
                }
                moveIndicator(selectedIndex, hasAnima)
                unSelectTabAnim(lastSelectedPosition, hasAnima)
                postInvalidate()

                listener?.onTabSelected(selectedIndex, isFromUser)
            }
        } else {
            if(!isAllowDrawIndicator){
                listener?.onTabSelected(selectedIndex, isFromUser)
            }
            isAllowDrawIndicator = true
            postInvalidate()
            updateTabViewSelectedStatus(selectedIndex)
            updateLastPositionTabViewStatusNoAnima(lastSelectedPosition, lastSelectedPositionScale)

        }
    }

    /**
     * 外部接口，根据position获取View
     *
     * @return 对应位置的点击View
     */
    fun getTabView(position: Int): View? {
        if (childCount == 0) {
            return null
        }
        if (isPositionValid(position)) {
            return getChildAt(position)
        } else {
            return null
        }
    }

    private fun isPositionValid(position: Int): Boolean {
        if (childCount == 0) {
            return false
        }
        return position in 0 until childCount
    }

    /**
     * 清除选中状态
     */
    fun clearCheckStatus() {
        LogUtils.logI(TAG, "clearCheckStatus---selectedIndex=$selectedIndex, lastSelectedPosition=$lastSelectedPosition")
        isAllowDrawIndicator = false
        postInvalidate()

        if (isPositionValid(selectedIndex)) {
            updateUnselectedItem(selectedIndex)
        }
//        updateLastPositionTabViewStatusNoAnima(lastSelectedPosition,1.0f)
    }

    /**
     * 更新上一个选中的tab变成未选中状态
     *
     * @param position
     */
    private fun updateUnselectedItem(position: Int) {
        val view = getChildAt(position)
        view.isSelected = false
        if (view is TextView) {
            view.setTextColor(textColor)
        }
        if (listener != null) { //上一个TAB变成未选中时返回回调
            listener!!.onTabUnSelected(position)
        }
    }

    private fun reSetSelectPosition(position: Int) {
        if (isPositionValid(position)) {

            lastSelectedPosition = selectedIndex
            selectedIndex = position

            if (lastSelectedPosition != selectedIndex) {
                if (isOnlyCallBack) {
                    getTabView(position)?.let { toggleTouchAnimation(it) }
                }
                unSelectTabAnim(lastSelectedPosition)
            }
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        LogUtils.logI(TAG, "onAttachedToWindow-----")
        skinExecutor.register(this)
        skinExecutor.startAdapterSkinTask(this)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        skinExecutor.unregister()
    }


    override fun getAdapterSkinTask(): Runnable {
        return Runnable {
            try {
                adapterSkinColor()
            } catch (e: Exception) {
                logI(TAG, "getAdapterSkinTask---e=" + e.message)
            }
        }
    }

    override fun applySkin() {
        logI(TAG, "applySkin----")
        skinExecutor.startAdapterSkinTask(this)
    }

}
