package com.dfl.smartscene.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.android.common.global.GlobalConstant
import com.dfl.smartscene.soa.SceneManager

/**
 * author:zhushi<PERSON>
 * e-mail:<EMAIL>
 * time: 2023/05/29
 * desc: 恢复出厂设置的receiver,动态注册
 * version:1.0
 */
class FactoryResetReceiver : BroadcastReceiver() {
    companion object {
        const val TAG = GlobalConstant.GLOBAL_TAG.plus("FactoryResetReceiver")
        const val RESET_ACTION = "com.dfl.systemsettings.action.ACTION_FACTORY_RESET"
        const val RESET_ACTION_OFFLINE_MAP_DATA = "com.dfl.systemsettings.action.ACTION_FACTORY_RESET_WITH_PRESERVE_OFFLINE_MAP_DATA"
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        CommonLogUtils.logI(TAG, "恢复出厂设置广播:${intent?.action}")
        if (RESET_ACTION == intent?.action || RESET_ACTION_OFFLINE_MAP_DATA == intent?.action) {
            //删除所有的用户场景
            SceneManager.deleteAllUserScene()
        }
    }

    fun registerReceiver(context: Context?) {
        val filter = IntentFilter()
        filter.addAction(RESET_ACTION)
        filter.addAction(RESET_ACTION_OFFLINE_MAP_DATA)
        filter.addCategory(Intent.CATEGORY_DEFAULT)
        context?.registerReceiver(this, filter)
        CommonLogUtils.logI(TAG, "恢复出厂设置广播注册成功")
    }
}