package com.dfl.smartscene;

import com.dfl.smartscene.INewScenePatternServiceCallback;
import com.dfl.smartscene.INewSceneRequestServiceCallback;

interface INewScenePatternService {

    /**
     * 注册监听
	 * 
     */
    oneway void registerScenePatternCallback(INewScenePatternServiceCallback callback);

    /**
     * 反注册监听
	 * 
     */
    oneway void unregisterScenePatternCallback(INewScenePatternServiceCallback callback);

    /**
     * 请求智慧场景
	 * 
     */
    oneway void reqScenePattern(INewSceneRequestServiceCallback callback);

    /**
     * 执行智慧场景
	 * 
     */
    boolean setScenePatternRunning(String id,int action);

    /**
     * 获取状态
	 * 
     */
    int getSceneStatus();

    /**
     * 获取智慧场景是否可用
	 * 
     */
    boolean getSceneEnabled(String id);

    /**
     * 表示H5链接彩蛋内容
	 * 
     */
    boolean reqSurpriseEgg(int keyCode);
}