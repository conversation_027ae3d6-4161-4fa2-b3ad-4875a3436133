<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="32px"
    android:height="32px"
    android:viewportWidth="32"
    android:viewportHeight="32">
    <path
        android:fillAlpha="0"
        android:fillColor="#D8D8D8"
        android:fillType="evenOdd"
        android:pathData="M0.5,31.5l31,-0l0,-31l-31,-0z"
        android:strokeWidth="1"
        android:strokeAlpha="0"
        android:strokeColor="#979797" />

    <path
        android:fillType="evenOdd"
        android:pathData="M16,16m-15,-0a15,15 0,1 0,30 -0a15,15 0,1 0,-30 -0">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="23.332838"
                android:endY="31"
                android:startX="11.015255"
                android:startY="3.9945152"
                android:type="linear">
                <item
                    android:color="#FFFEFEFE"
                    android:offset="0" />
                <item
                    android:color="#FFB4B9C5"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>

<!--    <path-->
<!--        android:name="round"-->
<!--        android:fillType="evenOdd"-->
<!--        android:pathData="M16,16m-15,-0a15,15 0,1 0,30 -0a15,15 0,1 0,-30 -0"-->
<!--        android:strokeWidth="2"-->
<!--        android:strokeColor="#2D39FD">-->
<!--    </path>-->
</vector>
