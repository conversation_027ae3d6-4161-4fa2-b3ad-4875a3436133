<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="156dp"
    android:height="116dp"
    android:viewportWidth="156"
    android:viewportHeight="116">
  <path
      android:pathData="M21.21,44.58C21.21,41.94 22.17,39.38 23.91,37.39L40.12,18.81C42.2,16.42 45.21,15.05 48.37,15.05H107.47C110.69,15.05 113.74,16.47 115.82,18.92L131.51,37.41C133.18,39.39 134.11,41.9 134.11,44.5V89.63C134.11,95.68 129.2,100.58 123.16,100.58H32.16C26.11,100.58 21.21,95.68 21.21,89.63V44.58Z"
      android:fillColor="#ffffff"
      android:fillAlpha="0.45"/>
  <path
      android:pathData="M41.6,32.33C41.6,29.31 44.05,26.86 47.08,26.86L108.24,26.86C111.26,26.86 113.71,29.31 113.71,32.33V85.48C113.71,88.5 111.26,90.95 108.24,90.95H47.08C44.05,90.95 41.6,88.5 41.6,85.48V32.33Z"
      android:fillColor="#F5A161"/>
  <path
      android:pathData="M26.69,45.16C26.69,40.63 30.36,36.95 34.9,36.95L120.42,36.95C124.96,36.95 128.63,40.63 128.63,45.16V86.9C128.63,91.43 124.96,95.11 120.42,95.11H34.9C30.36,95.11 26.69,91.43 26.69,86.9V45.16Z"
      android:fillColor="#ffffff"
      android:fillAlpha="0.6"/>
  <path
      android:pathData="M26.69,45.16C26.69,40.63 30.36,36.95 34.9,36.95L120.42,36.95C124.96,36.95 128.63,40.63 128.63,45.16V86.9C128.63,91.43 124.96,95.11 120.42,95.11H34.9C30.36,95.11 26.69,91.43 26.69,86.9V45.16Z"
      android:fillAlpha="0.5">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="77.66"
          android:startY="39.94"
          android:endX="77.66"
          android:endY="95.11"
          android:type="linear">
        <item android:offset="0" android:color="#FFEFAD71"/>
        <item android:offset="1" android:color="#FFF9CDB2"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M34.9,37.29H120.43C124.77,37.3 128.29,40.82 128.29,45.16V86.9C128.29,91.25 124.77,94.77 120.43,94.77H34.9C30.55,94.77 27.03,91.25 27.03,86.9V45.16C27.03,40.95 30.34,37.52 34.49,37.31L34.9,37.29Z"
      android:strokeAlpha="0.6"
      android:strokeWidth="0.684211"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="77.66"
          android:startY="36.95"
          android:endX="77.66"
          android:endY="95.11"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#33FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M67.1,47.26C67.1,45.65 68.41,44.34 70.01,44.34L85.58,44.34C87.19,44.34 88.49,45.65 88.49,47.26C88.49,48.87 87.19,50.17 85.58,50.17H70.01C68.41,50.17 67.1,48.87 67.1,47.26Z"
      android:fillColor="#ffffff"
      android:fillAlpha="0.8"/>
</vector>
