<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle">
    <!-- "rectangle" | "oval" | "line" | "ring" 矩形（rectangle）、椭圆形(oval)、线性形状(line)、环形(ring)-->
    <!--solid 填充颜色-->
    <solid android:color="@color/scene_color_card_bg_1" />
    <!-- corners 圆角-->
    <corners android:radius="@dimen/px_12" />
    <!--    <corners-->
    <!--        android:topLeftRadius="20dp"-->
    <!--        android:topRightRadius="20dp"-->
    <!--        android:bottomLeftRadius="0dp"-->
    <!--        android:bottomRightRadius="0dp"/>-->
    <!--stroke 边框-->
    <!--    <stroke-->
    <!--         android:width="2dp"-->
    <!--         android:color="@color/scene_primary_color" />-->
</shape>