<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle">
    <!-- "rectangle" | "oval" | "line" | "ring" 矩形（rectangle）、椭圆形(oval)、线性形状(line)、环形(ring)-->
    <!--solid 填充颜色-->
    <!--    <solid android:color="#12FFFF00" />-->
    <!-- 渐变色   https://blog.csdn.net/g984160547/article/details/131397307-->
    <gradient
        android:angle="270"
        android:endColor="@color/scene_color_mask_community_next_end"
        android:startColor="@color/scene_color_mask_community_next_start"
        android:type="linear" />
    <!-- corners 圆角-->
    <!--<corners android:radius="3dp" />-->
    <!--    <corners-->
    <!--        android:bottomLeftRadius="14dp"-->
    <!--        android:bottomRightRadius="0dp"-->
    <!--        android:topLeftRadius="14dp"-->
    <!--        android:topRightRadius="0dp" />-->
    <!--stroke 边框-->
    <!-- <stroke
         android:width="2dp"
         android:color="@color/bisque" />-->
</shape>