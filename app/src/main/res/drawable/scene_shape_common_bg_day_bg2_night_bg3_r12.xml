<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle">
    <!-- "rectangle" | "oval" | "line" | "ring" 矩形（rectangle）、椭圆形(oval)、线性形状(line)、环形(ring)-->
    <!--solid 填充颜色-->
    <solid android:color="@color/scene_color_day_bg2_night_bg3" />
    <!-- 渐变色   https://blog.csdn.net/g984160547/article/details/131397307-->
    <!--    <gradient-->
    <!--        android:angle="0"-->
    <!--        android:endColor="#00D1BEA5"-->
    <!--        android:startColor="#FFEEE0D1"-->
    <!--        android:type="linear" />-->
    <!-- corners 圆角-->
    <corners android:radius="@dimen/px_12" />
    <!--    <corners-->
    <!--        android:bottomLeftRadius="14dp"-->
    <!--        android:bottomRightRadius="0dp"-->
    <!--        android:topLeftRadius="14dp"-->
    <!--        android:topRightRadius="0dp" />-->
    <!--stroke 边框-->
    <!-- <stroke
         android:width="2dp"
         android:color="@color/bisque" />-->
</shape>