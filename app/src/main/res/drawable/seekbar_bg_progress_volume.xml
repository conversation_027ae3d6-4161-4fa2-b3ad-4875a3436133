<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!--定义seekbar滑动条的底色-->
    <item android:id="@android:id/background"
        android:width="@dimen/x_px_420"
        android:height="8px">
        <shape>
            <corners android:radius="4px" />
            <gradient
                android:angle="270"
                android:centerColor="#80616B80"
                android:centerY="0.75"
                android:endColor="#80616B80"
                android:startColor="#80616B80" />
        </shape>

    </item>
    <!--定义seekbar滑动条进度颜色-->
    <item android:id="@android:id/progress"
        android:width="@dimen/x_px_420"
        android:height="8px">
        <clip android:drawable="@drawable/ic_volume_seekbar_progress">
        </clip>
    </item>
</layer-list>