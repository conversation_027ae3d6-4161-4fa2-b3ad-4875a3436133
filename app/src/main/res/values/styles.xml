<?xml version="1.0" encoding="utf-8"?><!--针对窗体元素级别的，改变指定控件或者Layout的样式-->
<resources>
<!--ccs5.0-->
    <style name="style_scene_message_toast">
        <item name="android:windowEnterAnimation">@anim/scene_message_toast_push_up_in</item>
        <item name="android:windowExitAnimation">@anim/scene_message_toast_push_up_out</item>
    </style>


    <!--RadioGroup里button的样式，新版ui已废弃再次使用以设计稿为准-->
    <style name="RadioButtonSwitchTheme">
        <item name="android:layout_width">410dp</item>
        <item name="android:layout_height">96dp</item>
        <!--<item name="android:background">@drawable/scene_selector_radio_button_bg</item>-->
        <item name="android:gravity">center</item>
        <item name="android:layout_margin">4dp</item>
        <item name="android:textColor">@color/scene_selector_radio_text_color</item>
        <item name="android:textSize">@dimen/scene_text_size_h3</item>
        <item name="android:button">@null</item>
    </style>

    <!--组件库分段选择器style-->
    <style name="TabSwitchViewHorizontalTheme" parent="tab_switch_view_horizontal_style">
        <item name="android:layout_width">844dp</item>
        <item name="android:layout_height">112dp</item>
        <item name="itemWidth">@null</item>
        <item name="android:background">@drawable/scene_style_tab_switch_normal_bg</item>
    </style>


    <!--WheelView选中位置白带样式-->
    <style name="WheelViewSelectViewTheme">
        <item name="android:layout_width">740dp</item>
        <item name="android:layout_height">92dp</item>
        <item name="android:background">@drawable/scene_shape_wheel_view_line_divider</item>
    </style>

    <!--不带WheelView区域LinearLayout的样式-->
    <style name="WheelViewLinearLayoutTheme">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">260dp</item>
        <item name="android:layout_marginTop">20dp</item>
        <item name="android:layout_marginBottom">232dp</item>
        <item name="android:background">@color/transparent</item>
        <item name="android:gravity">center</item>
        <item name="android:orientation">horizontal</item>
    </style>

    <!--带tab的WheelView区域LinearLayout的样式-->
    <style name="WheelViewWithTabLinearLayoutTheme">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">300dp</item>
        <item name="android:layout_marginTop">58dp</item>
        <item name="android:layout_marginBottom">58dp</item>
        <item name="android:background">@color/transparent</item>
        <item name="android:gravity">center</item>
        <item name="android:orientation">horizontal</item>
    </style>

    <!--WheelView样式 ，需要根据实际设置宽度-->
    <style name="WheelViewStyleWidth90Theme">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:minWidth">60dp</item>
        <item name="android:layout_height">100dp</item>
        <item name="android:background">@color/transparent</item>
    </style>
    <!--wheelView 5项选择-->
    <style name="WheelViewFiveItemStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">428dp</item>
        <item name="android:minWidth">390dp</item>
        <item name="android:background">@color/transparent</item>
        <item name="wheelview_centerBackgroundColor">@color/transparent</item>
        <item name="wheelview_centerTextSize">@dimen/scene_text_size_h3</item>
        <item name="wheelview_gravity">center</item>
        <item name="wheelview_isCenterLabel">true</item>
        <item name="wheelview_isEnableCurve">false</item>
        <item name="wheelview_isLoop">true</item>
        <item name="wheelview_lineSpacingMultiplier">1.0</item>
        <item name="wheelview_maskId">@drawable/scene_layer_common_pop_wheel_mask</item>
        <item name="wheelview_secondTextSize">@dimen/scene_text_size_h4</item>
        <item name="wheelview_textColorCenter">@color/scene_primary_color_highlight</item>
        <item name="wheelview_textColorOut">@color/scene_color_text_4</item>
        <item name="wheelview_textLabelGap">16dp</item>
        <item name="wheelview_textLabelSize">24sp</item>
    </style>
    <!--WheelView 内旁边文字样式 -->
    <style name="WheelViewTextTheme">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">@dimen/scene_height_text_size_h3</item>
        <!--<item name="android:layout_marginTop">5dp</item>-->
        <!--<item name="android:layout_marginLeft">10dp</item>-->
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/scene_primary_color_highlight</item>
        <item name="android:textSize">@dimen/scene_text_size_h3</item>
    </style>

    <!--选择框 带icon的linearlayout样式 ll作为按钮主体-->
    <style name="SelectorLinearLayoutTheme">
        <item name="android:layout_width">@dimen/scene_width_big_selector_checkbox</item>
        <item name="android:layout_height">@dimen/scene_height_selector_checkbox</item>
        <item name="android:background">@drawable/scene_selector_edit_checkbox_bg</item>
        <item name="android:gravity">center</item>
    </style>

    <!--选择框 带icon的CheckTextView样式 CheckTextView作为按钮主体-->
    <style name="SelectorCheckTextViewTheme">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">@dimen/scene_height_selector_checkbox</item>
        <item name="android:gravity">center</item>
        <item name="android:textAlignment">center</item>
        <item name="android:textColor">@color/scene_selector_radio_color_black_checkout</item>
        <item name="android:textSize">@dimen/scene_text_size_h3</item>
    </style>

    <!--选择框 带icon的CheckTextView样式  3个选项 每个选项3个字 ，其他字数需要修改drawablePadding paddingStart-->
    <style name="SelectorCheckTextViewTheme.Item3Theme" parent="SelectorCheckTextViewTheme">
        <item name="android:layout_width">324dp</item>
        <item name="android:drawablePadding">-60dp</item>
        <item name="android:paddingStart">77dp</item>
        <item name="android:background">@drawable/scene_selector_edit_checkbox_bg</item>
    </style>

    <!--选择框 带icon的CheckTextView样式  4个选项 每个选项2个字 ，其他字数需要修改drawablePadding paddingStart-->
    <style name="SelectorCheckTextViewTheme.Item4Theme" parent="SelectorCheckTextViewTheme">
        <item name="android:layout_width">@dimen/scene_width_selector_checkbox_bigger</item>
        <item name="android:drawablePadding">-130dp</item>
        <item name="android:paddingStart">130dp</item>
        <item name="android:background">@drawable/scene_selector_edit_checkbox_bg</item>
    </style>

    <!--不带icon的浅灰色边框 大按钮的样式 例取消按钮-->
    <style name="SecondaryBigBtnColorStyle" parent="@style/example_nissan_button_style_third_text_logo">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:minWidth">@dimen/scene_width_big_button_without_icon</item>
        <item name="android:layout_height">@dimen/scene_height_big_button</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">@dimen/scene_text_size_h4</item>
        <item name="android:textAlignment">center</item>
        <item name="textSize">@dimen/scene_text_size_h4</item>
        <item name="useSizeUnitPX">true</item>
        <item name="backgroundCornerDegree">12</item>
    </style>
    <!--不带icon的浅灰色边框 大按钮的样式 例取消按钮   P42R-->
    <style name="SecondaryBigBtnColorStyleP42R" parent="@style/example_nissan_button_style_third_text_logo_p42r">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:minWidth">@dimen/scene_width_big_button_without_icon_p42r</item>
        <item name="android:layout_height">@dimen/scene_height_big_button_p42r</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">@dimen/scene_text_size_h8</item>
        <item name="android:textAlignment">center</item>
        <item name="textSize">@dimen/scene_text_size_h8</item>
        <item name="useSizeUnitPX">true</item>
        <item name="backgroundCornerDegree">24</item>
    </style>

    <style name="example_nissan_button_style_third_text_logo_p42r">
        <item name="backgroundBorderWidth">0dp</item>
        <item name="backgroundBaseColor">@color/scene_neutral_btn_1</item>
        <item name="backgroundBaseColorClicked">@color/scene_neutral_btn_1</item>
        <item name="textColor">@color/scene_primary_text_normal</item>
        <item name="textColorClicked">@color/scene_primary_text_normal</item>
        <item name="textDisableColor">@color/scene_primary_text_normal</item>
        <item name="buttonMotionStyle">nissan_lk1a</item>
        <item name="backgroundBorderColor">@color/scene_neutral_btn_1</item>
        <item name="backgroundBorderColorClicked">@color/scene_neutral_btn_1</item>
        <item name="backgroundDisableColor">@color/scene_neutral_btn_1</item>
        <!--        &lt;!&ndash;        值越小越不透明，越没有透明效果&ndash;&gt;-->
        <item name="disableMaskColorAlpha">@integer/bt_disable_alpha_3</item>

        <!--        以下4个参数和分辨率有些关系 -->
        <item name="textSize">21px</item>
        <item name="backgroundCornerDegree">15</item>
        <item name="imgWidth">30dp</item>
        <item name="spaceWidth">15dp</item>

    </style>
    <!--不带icon的深灰色背景 大按钮的样式 例确认按钮-->
    <style name="PrimaryBigBtnColorStyleP42R" parent="@style/example_nissan_button_style_second_text_logo">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:minWidth">@dimen/scene_width_big_button_without_icon_p42r</item>
        <item name="android:layout_height">@dimen/px_76</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">@dimen/scene_text_size_p42r_h3</item>
        <item name="android:textAlignment">center</item>
        <item name="textSize">@dimen/scene_text_size_p42r_h3</item>
        <item name="useSizeUnitPX">true</item>
        <item name="backgroundCornerDegree">12</item>
        <item name="textDisableColor">@color/scene_color_text_second_bt_disable</item>
    </style>


    <!--不带icon的深灰色背景 大按钮的样式 例确认按钮-->
    <style name="PrimaryBigBtnColorStyle" parent="@style/example_nissan_button_style_second_text_logo">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:minWidth">@dimen/scene_width_big_button_without_icon</item>
        <item name="android:layout_height">@dimen/scene_height_big_button</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">@dimen/scene_text_size_h4</item>
        <item name="android:textAlignment">center</item>
        <item name="textSize">@dimen/scene_text_size_h4</item>
        <item name="useSizeUnitPX">true</item>
        <item name="backgroundCornerDegree">24</item>
        <item name="textDisableColor">@color/scene_color_text_second_bt_disable</item>
    </style>
    <!--不带icon的选中按钮样式 大按钮的样式 例确认按钮-->
    <style name="SelectBigBtnColorStyle" parent="@style/example_nissan_button_style_option">
        <item name="android:gravity">center</item>
        <item name="android:textAlignment">center</item>
        <item name="textSize">@dimen/scene_text_size_h4</item>
        <item name="textColor">@color/scene_color_text_2</item>
        <item name="useSizeUnitPX">true</item>
        <item name="backgroundCornerDegree">24</item>
    </style>
    <!--不带icon的警示背景 大按钮样式-->
    <style name="WarningBigBtnColorStyle" parent="@style/example_nissan_button_style_warning_text">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:minWidth">@dimen/scene_width_big_button_without_icon</item>
        <item name="android:layout_height">@dimen/scene_height_big_button</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">@dimen/scene_text_size_h4</item>
        <item name="android:textAlignment">center</item>
        <item name="textSize">@dimen/scene_text_size_h4</item>
        <item name="useSizeUnitPX">true</item>
        <item name="backgroundCornerDegree">24</item>
    </style>

    <style name="WarningBigBtnColorStyleP42R" parent="@style/example_nissan_button_style_warning_text">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:minWidth">@dimen/scene_width_big_button_without_icon_p42r</item>
        <item name="android:layout_height">@dimen/px_76</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">@dimen/scene_text_size_p42r_h3</item>
        <item name="android:textAlignment">center</item>
        <item name="textSize">@dimen/scene_text_size_p42r_h3</item>
        <item name="useSizeUnitPX">true</item>
        <item name="backgroundCornerDegree">12</item>
        <item name="textColor">@color/scene_primary_text_normal</item>
        <item name="textColorClicked">@color/scene_primary_text_normal</item>
    </style>

    <!--带icon的浅灰色边框 大按钮的样式 -->
    <style name="SecondaryBigBtnColorStyle.WithIconStyle" parent="SecondaryBigBtnColorStyle">
        <item name="spaceWidth">24dp</item>
        <item name="imgWidth">48dp</item>
        <item name="imgHight">48dp</item>
    </style>
    <!--带icon的浅灰色边框 大按钮的样式P42R-->
    <style name="SecondaryBigBtnColorStyle.WithIconStyleP42R" parent="SecondaryBigBtnColorStyleP42R">
        <item name="spaceWidth">12dp</item>
        <item name="imgWidth">28dp</item>
        <item name="imgHight">28dp</item>
    </style>

    <!--带icon的浅灰色边框 场景卡片上按钮的样式 -->
    <style name="SecondaryBigBtnColorStyle.CardWithIconStyle" parent="SecondaryBigBtnColorStyle">
        <item name="spaceWidth">20dp</item>
        <item name="imgWidth">36dp</item>
        <item name="imgHight">36dp</item>
        <item name="backgroundBaseColor">@color/scene_color_day_bg2_night_btn2</item>
        <item name="backgroundCornerDegree">10</item>
        <item name="textSize">@dimen/scene_text_size_h6</item>
        <item name="android:layout_width">180dp</item>
        <item name="android:layout_height">60dp</item>
    </style>

    <!--带icon的深灰色背景 大按钮的样式-->
    <style name="PrimaryBigBtnColorStyle.WithIconStyle" parent="PrimaryBigBtnColorStyle">
        <item name="spaceWidth">24dp</item>
        <item name="android:paddingHorizontal">37dp</item>
        <item name="imgWidth">48dp</item>
        <item name="imgHight">48dp</item>
    </style>
    <!--带icon的深灰色背景 大按钮的样式-->
    <style name="SelectBigBtnColorStyle.WithIconStyle" parent="SelectBigBtnColorStyle">
        <item name="spaceWidth">10dp</item>
        <item name="imgWidth">48dp</item>
        <item name="imgHight">48dp</item>
    </style>
    <!--关注按钮 小按钮-->
    <style name="PrimaryBigBtnColorStyle.SubScribeBtnStyle" parent="PrimaryBigBtnColorStyle">
        <item name="android:layout_height">@dimen/scene_height_small_button</item>
        <item name="android:layout_width">190dp</item>
        <item name="text">@string/scene_text_user_center_subscribes_desc</item>
        <item name="backgroundCornerDegree">16</item>
        <item name="textSize">@dimen/scene_text_size_h5</item>
    </style>
    <!--已关注按钮 小按钮-->
    <style name="SecondaryBigBtnColorStyle.SubScribedBtnStyle" parent="SecondaryBigBtnColorStyle">
        <item name="android:layout_height">@dimen/scene_height_small_button</item>
        <item name="android:layout_width">190dp</item>
        <item name="text">@string/scene_text_user_center_subscribed</item>
        <item name="backgroundCornerDegree">16</item>
        <item name="textSize">@dimen/scene_text_size_h5</item>
    </style>

    <!--小对话框宽高背景,例如添加动作三级弹框,高度需要自己设置;距离顶部状态栏高度后居中布局;gravity=center距离顶部38dp-->
    <style name="SmallDialogTheme">
        <item name="android:layout_width">@dimen/scene_width_small_dialog</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:minHeight">640dp</item>
        <item name="android:layout_marginTop">76dp</item>
        <item name="android:maxHeight">1010dp</item>
        <item name="android:background">@drawable/scene_shape_common_medal_bg_radius_large</item>
        <item name="android:clickable">true</item>
        <item name="android:soundEffectsEnabled">false</item>
    </style>
    <!--中弹框距离顶部固定间距,不需要居中布局-->
    <style name="MiddleDialogTheme">
        <item name="android:layout_width">@dimen/scene_width_middle_dialog</item>
        <item name="android:layout_height">@dimen/scene_height_middle_dialog</item>
        <item name="android:layout_marginTop">198dp</item>
        <item name="android:background">@drawable/scene_shape_common_medal_bg_radius_large</item>
        <item name="android:clickable">true</item>
        <item name="android:soundEffectsEnabled">false</item>
    </style>
    <!--大弹框距离顶部固定间距,不需要居中布局-->
    <style name="BigDialogTheme">
        <item name="android:layout_width">@dimen/scene_width_big_dialog</item>
        <item name="android:layout_height">@dimen/scene_height_big_dialog</item>
        <item name="android:layout_marginTop">144dp</item>
        <item name="android:background">@drawable/scene_shape_common_medal_bg_radius_large</item>
        <item name="android:clickable">true</item>
        <item name="android:soundEffectsEnabled">false</item>
    </style>



    <!--小对话框宽高背景,例如添加动作三级弹框,高度需要自己设置;距离顶部状态栏高度后居中布局;gravity=center距离顶部38dp-->
    <style name="SmallDialogThemeP42R">
        <item name="android:layout_width">@dimen/scene_width_small_dialog_p42r</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:minHeight">@dimen/px_284</item>
        <item name="android:layout_marginTop">@dimen/px_72</item>
        <item name="android:maxHeight">@dimen/px_576</item>
        <item name="android:background">@drawable/scene_shape_common_medal_bg_radius_small</item>
        <item name="android:clickable">true</item>
        <item name="android:soundEffectsEnabled">false</item>
    </style>
    <!--中弹框距离顶部固定间距,不需要居中布局-->
    <style name="MiddleDialogThemeP42R">
        <item name="android:layout_width">@dimen/scene_width_middle_dialog_p42r</item>
        <item name="android:layout_height">@dimen/scene_height_middle_dialog_p42r</item>
        <item name="android:layout_marginTop">@dimen/px_72</item>
        <item name="android:background">@drawable/scene_shape_common_medal_bg_radius_small</item>
        <item name="android:clickable">true</item>
        <item name="android:soundEffectsEnabled">false</item>
    </style>
    <!--大弹框距离顶部固定间距,不需要居中布局-->
    <style name="BigDialogThemeP42R">
        <item name="android:layout_width">@dimen/scene_width_big_dialog_p42r</item>
        <item name="android:layout_height">@dimen/scene_height_big_dialog_p42r</item>
        <item name="android:layout_marginTop">@dimen/px_72</item>
        <item name="android:background">@drawable/scene_shape_common_medal_bg_radius_small</item>
        <item name="android:clickable">true</item>
        <item name="android:soundEffectsEnabled">false</item>
    </style>

    <!--小对话框 的 一级标题-->
    <style name="SmallDialogTitleThemeP42R">
        <item name="android:textFontWeight">500</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/px_104</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/scene_color_text_1</item>
        <item name="android:textSize">@dimen/scene_text_size_p42r_h3</item>
    </style>


    <!--小对话框 的 一级标题-->
    <style name="SmallDialogTitleTheme">
        <item name="android:textFontWeight">500</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">148dp</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/scene_color_text_1</item>
        <item name="android:textSize">@dimen/scene_text_size_h3</item>
    </style>

    <!--小对话框 的 二级标题，注意显示时要把一级标题居中位置上移14dp-->
    <style name="SmallDialogSubTitleTheme">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/scene_height_text_size_h5</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_marginBottom">18dp</item>
        <item name="android:textColor">@color/scene_color_text_3</item>
        <item name="android:textSize">@dimen/scene_text_size_h5</item>
    </style>

    <!--小对话框 的 二级标题，注意显示时要把一级标题居中位置上移14dp-->
    <style name="SmallDialogSubTitleThemeP42R">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/px_36</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/scene_color_text_3</item>
        <item name="android:textSize">@dimen/scene_text_size_h7</item>
    </style>

    <style name="HelpDialogTabTheme">
        <item name="android:textSize">@dimen/dp_40</item>
    </style>

    <style name="SmartRefreshClassicsTheme">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="srlClassicsSpinnerStyle">Translate</item>
        <item name="srlDrawableArrow">@drawable/scene_icon_common_refresh_down</item>
        <item name="srlDrawableArrowSize">@dimen/scene_text_size_h5</item>
        <item name="srlDrawableMarginRight">18dp</item>
        <item name="srlDrawableProgress">@drawable/scene_img_common_refresh_loading</item>f
        <item name="srlDrawableProgressSize">@dimen/scene_text_size_h5</item>
        <item name="srlEnableLastTime">false</item>
        <item name="srlTextFailed">@string/scene_text_srl_refresh_fail</item>
        <item name="srlTextFinish">@string/scene_text_srl_refresh_complete</item>
        <item name="srlTextPulling">@string/scene_text_srl_pull_refresh</item>
        <item name="srlTextRefreshing">@string/scene_text_srl_refreshing</item>
        <item name="srlTextRelease">@string/scene_text_srl_release_refresh_now</item>
        <item name="srlTextSizeTitle">@dimen/scene_text_size_p42r_h6</item>
        <!--文字和图片颜色-->
        <item name="srlAccentColor">@color/scene_color_text_3</item>
        <!--背景色-->
        <item name="srlPrimaryColor">@color/transparent</item>
        <!--添加居中对齐-->
        <item name="android:gravity">center</item>
    </style>

    <style name="SmartRefreshClassicsTheme_4Banner" parent="SmartRefreshClassicsTheme">
        <!--文字和图片颜色-->
        <item name="srlAccentColor">@color/scene_color_banner_header_bg_1</item>
        <item name="srlDrawableArrow">@drawable/scene_icon_common_refresh_down_4_banner</item>
        <item name="srlDrawableProgress">@drawable/scene_img_common_refresh_loading</item>
    </style>

    <style name="SmartRefreshClassicsTheme.FooterTheme" parent="SmartRefreshClassicsTheme">
        <item name="srlTextFailed">@string/scene_text_srl_load_fail</item>
        <item name="srlTextFinish">@string/scene_text_srl_load_complete</item>
        <item name="srlTextPulling">@string/scene_text_srl_slide_refresh</item>
        <item name="srlTextRefreshing">@string/scene_text_srl_loading</item>
        <item name="srlTextNothing">@string/scene_text_discover_scene_no_data_tips</item>
    </style>

    <style name="SmartRefreshLayoutTheme">
        <item name="srlHeaderHeight">134dp</item>
        <item name="srlFooterHeight">204dp</item>
    </style>

    <style name="ShakingEditViewTheme">
        <item name="textSize">@dimen/scene_text_size_h5</item>
        <item name="textColor">@color/scene_color_text_1</item>
        <item name="textColorHint">@color/scene_color_text_3</item>
        <item name="cursorColor">@color/scene_primary_color</item>
    </style>

    <style name="ShakingEditViewTheme.WithTip" parent="ShakingEditViewTheme">
        <item name="tipSize">@dimen/scene_text_size_h5</item>
        <item name="tipColor">@color/scene_color_text_4</item>
        <item name="tipHeight">54dp</item>
    </style>

    <style name="popupAnimationTheme">
        <item name="android:windowEnterAnimation">@anim/scene_popup_enter</item>
    </style>

    <style name="OverScrollViewTheme1A">
        <!-- 是否启用滑动到顶/底部视图自动回弹 0启用 1不启用-->
        <item name="enableAuthReound">1</item>
        <!-- 是否启用右侧的自定义滚动条 0启用 1不启用-->
        <item name="enableCustomBar">1</item>
        <!-- 渐消类型 0实底背景渐消 1无背景纯文本类-->
        <item name="fadeType">0</item>
        <!-- 渐消遮罩的长度类型 0为1% 1为3%-->
        <item name="fadeLengthType">0</item>
        <item name="android:fillViewport">true</item>
    </style>

    <style name="OverScrollViewTheme3B" parent="OverScrollViewTheme1A">
        <!-- 渐消类型 0实底背景渐消 1无背景纯文本类-->
        <item name="fadeType">1</item>
        <!-- 渐消遮罩的长度类型 0为1% 1为3%-->
        <item name="fadeLengthType">1</item>
    </style>

    <style name="FadeRecyclerViewTheme1A">
        <!-- 渐消类型 0实底背景渐消 1无背景纯文本类-->
        <item name="recyclerFadeType">0</item>
        <!-- 渐消遮罩的长度类型 0为1% 1为3%-->
        <item name="recyclerFadeLengthType">0</item>
    </style>

    <style name="FadeRecyclerViewTheme3B">
        <!-- 渐消类型 0实底背景渐消 1无背景纯文本类-->
        <item name="recyclerFadeType">1</item>
        <!-- 渐消遮罩的长度类型 0为1% 1为3%-->
        <item name="recyclerFadeLengthType">1</item>
    </style>

    <style name="FadeRecyclerViewTheme3A">
        <!-- 渐消类型 0实底背景渐消 1无背景纯文本类-->
        <item name="recyclerFadeType">0</item>
        <!-- 渐消遮罩的长度类型 0为1% 1为3%-->
        <item name="recyclerFadeLengthType">1</item>
    </style>

    <style name="ScaleSwitchCompatTheme" parent="example_nissan_switch_style2">
        <item name="trackCornerDegree">16</item>
    </style>
    <!--OverScrollView待滚动条1%实底背景渐消-->
    <style name="OverScrollViewTheme1A.WithScrollBar" parent="OverScrollViewTheme1A">
        <!-- 是否启用滑动到顶/底部视图自动回弹 0启用 1不启用-->
        <item name="enableAuthReound">0</item>
        <!-- 是否启用右侧的自定义滚动条 0启用 1不启用-->
        <item name="enableCustomBar">0</item>
        <!-- 右侧的自定义滚动条尺寸类型 0大尺寸 1小尺寸-->
        <item name="customBarWidthType">0</item>
        <!-- 右侧的自定义滚动条的颜色-->
        <item name="customBarColor">@color/nissan_over_scroll_custom_bar_bg</item>
        <!-- 渐消类型 0实底背景渐消 1无背景纯文本类-->
        <item name="fadeType">0</item>
        <!-- 渐消遮罩的长度类型 0为1% 1为3%-->
        <item name="fadeLengthType">0</item>
    </style>

</resources>