[{"scenario_id": "OEM_0001", "name": "上班模式", "desc": "开启导航至公司，播放音频节目并调节车辆设定。", "version": 1, "auto_execute": 1, "second_ask": 1, "timestamp": 0, "trigger_con": {"id": 1, "desc": "主驾安全带系上/解开", "skill_id": "0x41b", "class_name": "座椅", "args": [{"value": "0x2", "type": 7, "name": "beltState", "desc": "主驾安全带 系上"}]}, "conditions": [{"id": 1, "desc": "时间范围 7:00-10:00", "skill_id": "0xFF05", "class_name": "时间", "args": [{"value": "7", "type": 7, "name": "start_hour", "desc": ""}, {"value": "0", "type": 7, "name": "start_minute", "desc": ""}, {"value": "0", "type": 7, "name": "start_second", "desc": ""}, {"value": "10", "type": 7, "name": "stop_hour", "desc": ""}, {"value": "0", "type": 7, "name": "stop_minute", "desc": ""}, {"value": "0", "type": 7, "name": "stop_second", "desc": ""}]}], "sequence": {"items": [{"id": 0, "type": 1, "item": {"desc": "开启自动导航模式", "skill_id": "0x208", "class_name": "大灯灯语", "args": [{"value": "9", "type": 3, "name": "lampEffect", "desc": ""}]}}]}}, {"scenario_id": "OEM_0002", "name": "回家模式", "desc": "自动按照地图规划回家路线，语音协助直达", "version": 0, "auto_execute": 1, "second_ask": 1, "timestamp": 0, "trigger_con": {"id": 0, "desc": "当方向盘星号键短按回家", "skill_id": "0x14", "class_name": "距离家", "args": [{"value": "100", "type": 1, "name": "longitude", "desc": ""}, {"value": "20", "type": 1, "name": "latitude", "desc": ""}, {"value": "100", "type": 1, "name": "distance", "desc": ""}]}, "conditions": [{"id": 0, "desc": "当方向盘星号键短按回家", "skill_id": "0x42", "class_name": "天气", "args": [{"value": "2", "type": 1, "name": "status", "desc": ""}]}], "sequence": {"items": [{"id": 0, "type": 1, "item": {"desc": "开启自动导航模式", "skill_id": "0xb01", "class_name": "香薰机开关", "args": [{"value": "1", "type": 1, "name": "status", "desc": ""}]}}]}}, {"scenario_id": "OEM_0003", "name": "生日惊喜模式", "desc": "智能根据设置日期语音发出祝福", "version": 0, "auto_execute": 1, "second_ask": 1, "timestamp": 0, "trigger_con": {"id": 0, "desc": "当日期达到设定的生日日期", "skill_id": "0xFF06", "class_name": "时间点", "args": [{"value": "12", "type": 1, "name": "hour", "desc": ""}, {"value": "12", "type": 1, "name": "minute", "desc": ""}, {"value": "0", "type": 1, "name": "second", "desc": ""}]}, "conditions": [{"id": 0, "desc": "当日期达到设定的生日日期", "skill_id": "0x413", "class_name": "座椅", "args": [{"value": "1", "type": 1, "name": "isOccupied", "desc": ""}]}], "sequence": {"items": [{"id": 0, "type": 1, "item": {"desc": "开启语音对话", "skill_id": "0xb01", "class_name": "香薰机开关", "args": [{"value": "1", "type": 1, "name": "status", "desc": ""}]}}]}}, {"scenario_id": "OEM_0004", "name": "疲劳模式", "desc": "驾驶员疲劳状态下，协助安全驾驶", "version": 0, "auto_execute": 1, "second_ask": 1, "timestamp": 0, "trigger_con": {"id": 0, "desc": "当启动疲劳模式", "skill_id": "0x414", "class_name": "座椅", "args": [{"value": "1", "type": 1, "name": "isOccupied", "desc": ""}]}, "conditions": [{"id": 0, "desc": "当启动疲劳模式", "skill_id": "0x413", "class_name": "座椅", "args": [{"value": "2", "type": 1, "name": "isOccupied", "desc": ""}]}], "sequence": {"items": [{"id": 0, "type": 1, "item": {"desc": "辅助驾驶，并发出提示", "skill_id": "0x206", "class_name": "雾灯", "args": [{"value": "1", "type": 1, "name": "lampSts", "desc": ""}]}}]}}, {"scenario_id": "OEM_0005", "name": "节能模式", "desc": "开启节能模式，车速及耗能设备设置将收到限制提示", "version": 0, "auto_execute": 1, "second_ask": 1, "timestamp": 0, "trigger_con": {"id": 0, "desc": "续航里程 大于100km", "skill_id": "0x839", "class_name": "续航里程", "args": [{"value": "0", "type": 1, "name": "distanceToEmpty", "desc": ""}]}, "conditions": [{"id": 0, "desc": "左前车轮 胎压报警", "skill_id": "0x519", "class_name": "左前车轮", "args": [{"value": "2", "type": 1, "name": "starKeyState", "desc": ""}]}], "sequence": {"items": [{"id": 0, "type": 1, "item": {"desc": "打开", "skill_id": "0x203", "class_name": "阅读灯模式", "args": [{"value": "0", "type": 1, "name": "lampMode", "desc": ""}]}}]}}, {"scenario_id": "OEM_0006", "name": "亲子模式", "desc": "接送孩子上下学轻松一键智能导航", "version": 0, "auto_execute": 1, "second_ask": 1, "timestamp": 0, "trigger_con": {"id": 0, "desc": "车内温度 大于30℃", "skill_id": "0x126", "class_name": "车内温度", "args": [{"value": "30", "type": 1, "name": "InsideTemp", "desc": ""}]}, "conditions": [{"id": 0, "desc": "全车门锁 解锁", "skill_id": "0x602", "class_name": "门锁", "args": [{"value": "0", "type": 1, "name": "status", "desc": ""}]}], "sequence": {"items": [{"id": 0, "type": 1, "item": {"desc": "内循环", "skill_id": "0x109", "class_name": "循环模式", "args": [{"value": "0", "type": 3, "name": "recirc", "desc": ""}]}}]}}, {"scenario_id": "OEM_0007", "name": "洗车房模式", "desc": "洗车情况下自动检索门窗锁是否关闭", "version": 0, "auto_execute": 1, "second_ask": 1, "timestamp": 0, "trigger_con": {"id": 1, "desc": "挡位 P挡", "skill_id": "0x87f", "class_name": "挡位", "args": [{"value": "0", "type": 1, "name": "gearState", "desc": ""}]}, "conditions": [{"id": 1, "desc": "天气 雨天", "skill_id": "0x42", "class_name": "天气", "args": [{"value": "2", "type": 1, "name": "status", "desc": ""}]}], "sequence": {"items": [{"id": 1, "type": 1, "item": {"desc": "打开", "skill_id": "0x10e", "class_name": "制冷制热", "args": [{"value": "0", "type": 1, "name": "ACSwstate", "desc": ""}]}}]}}, {"scenario_id": "OEM_0008", "name": "雨雪模式", "desc": "雨雪天气情况下，智能语音提示协助驾驶", "version": 0, "auto_execute": 1, "second_ask": 1, "timestamp": 0, "trigger_con": {"id": 0, "desc": "主驾车门 打开", "skill_id": "0x605", "class_name": "主驾车门", "args": [{"value": "1", "type": 1, "name": "doorID", "desc": ""}, {"value": "1", "type": 1, "name": "status", "desc": ""}]}, "conditions": [{"id": 0, "desc": "天气 雪天", "skill_id": "0x42", "class_name": "天气", "args": [{"value": "5", "type": 1, "name": "status", "desc": ""}]}], "sequence": {"items": [{"id": 0, "type": 1, "item": {"desc": "1挡", "skill_id": "0x103", "class_name": "空调风量", "args": [{"value": "1", "type": 1, "name": "speedgear", "desc": ""}]}}]}}, {"scenario_id": "OEM_0009", "name": "接送女王模式", "desc": "开启后智能导航协助驾驶", "version": 0, "auto_execute": 1, "second_ask": 1, "timestamp": 0, "trigger_con": {"id": 0, "desc": "副驾车门", "skill_id": "0x605", "class_name": "车门", "args": [{"value": "2", "type": 1, "name": "doorID", "desc": ""}, {"value": "1", "type": 1, "name": "status", "desc": ""}]}, "conditions": [{"id": 0, "desc": "副驾座椅 有人", "skill_id": "0x415", "class_name": "座椅", "args": [{"value": "2", "type": 1, "name": "isOccupied", "desc": ""}]}], "sequence": {"items": [{"id": 0, "type": 1, "item": {"desc": "强", "skill_id": "0xb02", "class_name": "散香挡位", "args": [{"value": "3", "type": 1, "name": "aromatherapyIntensity", "desc": ""}]}}]}}]