<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/viewpager_scene_small_video"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <com.dfl.smartscene.ccs.view.commonview.CardContainerMark
        android:id="@+id/mark_viewpager_small_view"
        android:layout_width="@dimen/x_px_100"
        android:layout_height="@dimen/y_px_16"
        android:layout_gravity="center_horizontal|bottom"
        android:layout_marginBottom="@dimen/y_px_28" />

    <View
        android:id="@+id/view_slide_left"
        android:layout_gravity="left|bottom"
        android:layout_marginBottom="@dimen/y_px_28"
        android:contentDescription="@string/string_slide_left"
        android:layout_width="1px"
        android:layout_height="1px"/>
    <View
        android:id="@+id/view_slide_right"
        android:layout_gravity="left|bottom"
        android:layout_marginBottom="@dimen/y_px_20"
        android:contentDescription="@string/string_slide_right"
        android:layout_width="1px"
        android:layout_height="1px"/>

    <!--    <com.iauto.uicontrol.GestureBoard-->
    <!--        android:id="@+id/view_gesture"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="match_parent"/>-->


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_scene_small_video_part_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone">

        <FrameLayout
            android:id="@+id/textview_scene_small_video_pause"
            android:layout_width="@dimen/x_px_184"
            android:layout_height="@dimen/y_px_64"
            android:layout_marginStart="@dimen/x_px_144"
            android:layout_marginBottom="@dimen/y_px_48"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/bg_play_relax_pattern_btn"
                android:gravity="left|center_vertical"
                android:includeFontPadding="false"
                android:paddingStart="@dimen/x_px_90"
                android:text="暂停"
                android:textColor="@color/white"
                android:textSize="@dimen/y_px_text_body"
                tools:ignore="RtlHardcoded,RtlSymmetry" />

            <com.dfl.dflcommonlibs.uimodeutil.UIImageView
                android:layout_width="@dimen/x_px_32"
                android:layout_height="@dimen/y_px_32"
                android:layout_marginStart="@dimen/x_px_50"
                android:layout_marginTop="@dimen/y_px_16"
                android:background="@drawable/icon_full_view_pause" />

        </FrameLayout>

        <FrameLayout
            android:id="@+id/textview_scene_small_video_resume"
            android:layout_width="@dimen/x_px_184"
            android:layout_height="@dimen/y_px_64"
            android:layout_marginStart="@dimen/x_px_144"
            android:layout_marginBottom="@dimen/y_px_48"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent">

            <com.dfl.dflcommonlibs.uimodeutil.UITextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/bg_play_relax_pattern_btn"
                android:gravity="left|center_vertical"
                android:includeFontPadding="false"
                android:paddingStart="@dimen/x_px_90"
                android:text="继续"
                android:textColor="@color/white"
                android:textSize="@dimen/y_px_text_body"
                tools:ignore="RtlHardcoded,RtlSymmetry" />

            <com.dfl.dflcommonlibs.uimodeutil.UIImageView
                android:layout_width="@dimen/x_px_32"
                android:layout_height="@dimen/y_px_32"
                android:layout_marginStart="@dimen/x_px_50"
                android:layout_marginTop="@dimen/y_px_16"
                android:background="@drawable/icon_full_view_resume" />

        </FrameLayout>
        <FrameLayout
            android:id="@+id/textview_scene_small_video_stop"
            android:layout_width="@dimen/x_px_184"
            android:layout_height="@dimen/y_px_64"
            android:layout_marginStart="@dimen/x_px_360"
            android:layout_marginBottom="@dimen/y_px_48"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent">

            <com.dfl.dflcommonlibs.uimodeutil.UITextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/bg_play_relax_pattern_btn"
                android:gravity="left|center_vertical"
                android:includeFontPadding="false"
                android:paddingStart="@dimen/x_px_90"
                android:text="结束"
                android:textColor="@color/white"
                android:textSize="@dimen/y_px_text_body"
                tools:ignore="RtlHardcoded,RtlSymmetry" />

            <com.dfl.dflcommonlibs.uimodeutil.UIImageView
                android:layout_width="@dimen/x_px_24"
                android:layout_height="@dimen/y_px_24"
                android:scaleType="centerInside"
                android:layout_marginStart="@dimen/x_px_54"
                android:layout_marginTop="@dimen/y_px_20"
                android:background="@drawable/icon_full_view_end" />

        </FrameLayout>


        <com.dfl.dflcommonlibs.uimodeutil.UITextView
            android:id="@+id/textview_scene_small_video_time"
            android:layout_width="@dimen/x_px_184"
            android:layout_height="@dimen/y_px_64"
            android:layout_marginTop="@dimen/y_px_48"
            android:layout_marginStart="@dimen/x_px_48"
            android:background="@drawable/icon_full_view_clock"
            android:gravity="left|center_vertical"
            android:includeFontPadding="false"
            android:paddingStart="@dimen/x_px_85"
            android:textColor="@color/color_white"
            android:textSize="@dimen/font_size_28"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="RtlHardcoded,RtlSymmetry" />

        <com.dfl.dflcommonlibs.uimodeutil.UIImageView
            android:id="@+id/imageview_scene_full_expand"
            android:layout_width="@dimen/x_px_64"
            android:layout_height="@dimen/y_px_64"
            android:layout_marginStart="@dimen/x_px_48"
            android:layout_marginBottom="@dimen/y_px_48"
            android:background="@drawable/icon_full_view_zoom_out"
            android:visibility="gone"
            android:contentDescription="@string/string_expand_contentDescription"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent" />

        <com.dfl.dflcommonlibs.uimodeutil.UIImageView
            android:id="@+id/imageview_scene_full_fold"
            android:layout_width="@dimen/x_px_64"
            android:layout_height="@dimen/y_px_64"
            android:layout_marginStart="@dimen/x_px_48"
            android:layout_marginBottom="@dimen/y_px_48"
            android:background="@drawable/icon_full_view_zoom_in"
            android:contentDescription="@string/string_narrow_contentDescription"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>


    <LinearLayout
        android:id="@+id/linear_layout_volume_layout"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/y_px_144"
        android:layout_gravity="end|bottom"
        android:layout_marginEnd="@dimen/x_px_410"
        android:layout_marginBottom="@dimen/y_px_200"
        android:background="@drawable/ic_volume_layout_bg"
        android:gravity="center"
        android:tag="background:drawable:ic_volume_layout_bg"
        android:visibility="gone">

        <com.dfl.dflcommonlibs.uimodeutil.UIImageView
            android:layout_width="@dimen/x_px_26"
            android:layout_height="@dimen/y_px_26"
            android:layout_marginStart="@dimen/x_px_68"
            android:background="@drawable/ic_volume_reduce" />

        <com.dfl.smartscene.ccs.base.CustomSeekbar
            android:id="@+id/seekbar_scene_small_volume"
            android:layout_width="@dimen/x_px_472"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/x_px_24"
            android:background="@null"
            android:max="40"
            android:maxHeight="@dimen/x_px_8"
            android:minHeight="@dimen/x_px_8"
            android:progress="0"
            android:progressDrawable="@drawable/seekbar_bg_progress_volume"
            android:splitTrack="false"
            android:thumb="@drawable/ic_volume_seekbar_thumb" />

        <com.dfl.dflcommonlibs.uimodeutil.UIImageView
            android:layout_width="@dimen/x_px_40"
            android:layout_height="@dimen/y_px_40"
            android:layout_marginEnd="@dimen/x_px_60"
            android:background="@drawable/ic_volume_add" />

    </LinearLayout>

</FrameLayout>