<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/x_px_610"
    android:layout_height="@dimen/y_px_500"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <View
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:background="@drawable/drawable_scene_img_chare_set_lujing2"
        app:layout_constraintTop_toTopOf="@+id/button_layout_seat_adjust_driver_whole_right"
        app:layout_constraintBottom_toBottomOf="@+id/button_layout_seat_adjust_driver_whole_right"
        app:layout_constraintStart_toEndOf="@+id/button_layout_seat_adjust_driver_whole_left"
        app:layout_constraintEnd_toStartOf="@+id/button_layout_seat_adjust_driver_whole_right"/>

    <View
        android:layout_width="@dimen/x_px_1"
        android:layout_height="0dp"
        android:background="@drawable/drawable_scene_img_chare_set_lujing1"
        app:layout_constraintStart_toStartOf="@+id/button_layout_seat_adjust_driver_whole_up"
        app:layout_constraintEnd_toEndOf="@+id/button_layout_seat_adjust_driver_whole_up"
        app:layout_constraintTop_toBottomOf="@+id/button_layout_seat_adjust_driver_whole_up"
        app:layout_constraintBottom_toTopOf="@+id/button_layout_seat_adjust_driver_whole_down"/>

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/drawable_scene_img_chare_set_lujing3"
        app:layout_constraintStart_toStartOf="@+id/button_layout_seat_adjust_driver_back_rear"
        app:layout_constraintEnd_toStartOf="@+id/button_layout_seat_adjust_driver_back_front"
        app:layout_constraintTop_toTopOf="@+id/button_layout_seat_adjust_driver_back_front"
        app:layout_constraintBottom_toTopOf="@+id/button_layout_seat_adjust_driver_back_rear"
        android:layout_marginStart="@dimen/x_px_25"
        android:layout_marginTop="@dimen/y_px_25"/>

    <FrameLayout
        android:id="@+id/framelayout_layout_seat_adjust_driver_back_layout"
        android:layout_width="@dimen/x_px_550"
        android:layout_height="@dimen/y_px_400"

        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.iauto.uicontrol.ImageBase
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/drawable_scene_img_chare_set_shadow"
            android:layout_gravity="center|bottom"/>

        <com.iauto.uicontrol.ImageBase
            android:id="@+id/iv_back_view"
            android:layout_width="@dimen/x_px_400"
            android:layout_height="@dimen/y_px_400"
            android:layout_gravity="center"
            android:scaleType="center"
            android:src="@drawable/bg_set_backrestleft_n"
            android:transformPivotX="@dimen/x_px_143"
            android:transformPivotY="@dimen/y_px_293"
            tools:ignore="ContentDescription" />

        <com.iauto.uicontrol.ImageBase
            android:id="@+id/iv_cushion_view"
            android:layout_width="@dimen/x_px_400"
            android:layout_height="@dimen/y_px_400"
            android:layout_gravity="center"
            android:scaleType="center"
            android:src="@drawable/bg_set_backcushleft_n"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:ignore="ContentDescription" />
    </FrameLayout>


    <com.iauto.uicontrol.ButtonBase
        android:id="@+id/button_layout_seat_adjust_driver_back_front"
        android:layout_width="@dimen/x_px_55"
        android:layout_height="@dimen/y_px_55"
        android:layout_marginStart="@dimen/x_px_400"
        android:layout_marginTop="@dimen/y_px_70"
        android:background="@drawable/drawable_scene_btn_right"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.iauto.uicontrol.ButtonBase
        android:id="@+id/button_layout_seat_adjust_driver_back_rear"
        android:layout_width="@dimen/x_px_55"
        android:layout_height="@dimen/y_px_55"
        android:layout_marginStart="@dimen/x_px_50"
        android:layout_marginTop="@dimen/y_px_220"
        android:background="@drawable/drawable_scene_btn_down"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.iauto.uicontrol.ButtonBase
        android:id="@+id/button_layout_seat_adjust_driver_whole_right"
        android:layout_width="@dimen/x_px_55"
        android:layout_height="@dimen/y_px_55"
        android:layout_marginStart="@dimen/x_px_500"
        android:layout_marginTop="@dimen/y_px_280"
        android:background="@drawable/drawable_scene_btn_right"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.iauto.uicontrol.ButtonBase
        android:id="@+id/button_layout_seat_adjust_driver_whole_left"
        android:layout_width="@dimen/x_px_55"
        android:layout_height="@dimen/y_px_55"
        android:layout_marginStart="@dimen/x_px_120"
        android:layout_marginTop="@dimen/y_px_280"
        android:background="@drawable/drawable_scene_btn_lift"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.iauto.uicontrol.ButtonBase
        android:id="@+id/button_layout_seat_adjust_driver_whole_up"
        android:layout_width="@dimen/x_px_55"
        android:layout_height="@dimen/y_px_55"
        android:layout_marginStart="@dimen/x_px_350"
        android:layout_marginTop="@dimen/y_px_120"
        android:background="@drawable/drawable_scene_btn_up"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.iauto.uicontrol.ButtonBase
        android:id="@+id/button_layout_seat_adjust_driver_whole_down"
        android:layout_width="@dimen/x_px_55"
        android:layout_height="@dimen/y_px_55"
        android:layout_marginStart="@dimen/x_px_350"
        android:layout_marginTop="@dimen/y_px_400"
        android:background="@drawable/drawable_scene_btn_down"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>