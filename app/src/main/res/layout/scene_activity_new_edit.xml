<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="vm"
            type="com.dfl.smartscene.ui.edit.SceneEditViewModel" />

        <import type="android.view.View" />
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- 顶部-->
        <RelativeLayout
            android:id="@+id/ll_back_edit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/px_56"
            android:layout_marginTop="@dimen/px_92"
            android:minHeight="@dimen/px_52"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="@dimen/dp_40"
                android:layout_height="@dimen/dp_40"
                android:layout_marginEnd="@dimen/px_20"
                android:layout_marginTop="@dimen/px_6"
                android:scaleType="center"
                android:src="@drawable/scene_icon_common_arrow_left"
                tools:ignore="RtlSymmetry" />
            <TextView
                android:id="@+id/tv_scene_name"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/px_52"
                android:layout_marginStart="@dimen/px_20"
                android:layout_toEndOf="@id/iv_back"
                android:gravity="center"
                android:textColor="@color/scene_color_text_1"
                android:textFontWeight="400"
                android:textSize="@dimen/scene_text_size_h4_p42r"
                tools:text="场景一" />

            <ImageView
                android:id="@+id/tv_edit_name"
                android:layout_width="@dimen/dp_40"
                android:layout_height="@dimen/dp_40"
                android:layout_marginStart="@dimen/px_20"
                android:layout_marginTop="@dimen/px_6"
                android:layout_toEndOf="@id/tv_scene_name"
                android:src="@drawable/scene_icon_community_edit" />

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_below="@id/tv_scene_name"
                android:layout_alignStart="@id/iv_back"
                android:layout_marginTop="12dp"
                tools:visibility="gone"
                android:src="@drawable/scene_icon_community_success"
                android:visibility="@{vm.isDownLoadSceneLiveData()? View.VISIBLE:View.GONE,default=visible}" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:layout_below="@id/tv_scene_name"
                android:layout_marginStart="68dp"
                android:layout_marginTop="12dp"
                android:layout_marginBottom="50dp"
                android:gravity="center"
                android:text="@string/scene_text_community_downloaded_save"
                android:textColor="@color/scene_color_text_3"
                android:textFontWeight="400"
                android:textSize="@dimen/scene_text_size_h5"
                tools:visibility="gone"
                android:visibility="@{vm.isDownLoadSceneLiveData()? View.VISIBLE:View.GONE,default=visible}" />

            <TextView
                android:id="@+id/btn_reset"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/px_52"
                android:layout_alignParentEnd="true"
                android:paddingEnd="@dimen/px_24"
                android:enabled="@{vm.btnReSetIsEnAbleLiveData}"
                android:gravity="center"
                android:text="@string/scene_text_common_clean_up"
                android:textColor="@color/scene_primary_color_highlight"
                android:textSize="@dimen/scene_text_size_p42r_h3" />


        </RelativeLayout>

        <!--  内容-->
        <com.dfl.android.animationlib.scrollView.OverScrollView
            android:id="@+id/sl_edit_content"
            style="@style/OverScrollViewTheme1A"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ll_back_edit">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/csl_edit_content"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <!--小图标-->
<!--                <androidx.recyclerview.widget.RecyclerView-->
<!--                    android:id="@+id/rv_action_icon_edit"-->
<!--                    android:layout_width="0dp"-->
<!--                    android:layout_height="@dimen/px_120"-->
<!--                    android:layout_marginHorizontal="76dp"-->
<!--                    android:layout_marginTop="56dp"-->
<!--                    android:background="@drawable/scene_shape_detail_condition_able_r24"-->
<!--                    android:paddingHorizontal="48dp"-->
<!--                    android:visibility="gone"-->
<!--                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"-->
<!--                    app:layout_constraintEnd_toEndOf="parent"-->
<!--                    app:layout_constraintStart_toStartOf="parent"-->
<!--                    app:layout_constraintTop_toBottomOf="@id/tv_scene_name"-->
<!--                    app:spanCount="25" />-->

                <!--条件-->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/csl_edit_condition_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/px_33"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/tv_meet_all_conditions_desc"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/px_46"
                        android:layout_marginStart="@dimen/px_64"
                        android:gravity="center"
                        android:text="@string/scene_text_edit_meet_all_conditions"
                        android:textColor="@color/scene_color_text_2"
                        android:textFontWeight="400"
                        android:textSize="@dimen/scene_text_size_p42r_h3"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <!-- 条件列表-->
                <com.dfl.smartscene.widget.recycleview.HeightRecycleView
                    android:id="@+id/rv_edit_scene_condition"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/px_64"
                    android:layout_marginTop="@dimen/px_20"
                    android:minHeight="@dimen/px_100"
                    android:overScrollMode="never"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/csl_edit_condition_title"
                    app:spanCount="3" />

                <!-- 动作-->
                <TextView
                    android:id="@+id/tv_edit_scene_action_name"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/px_37"
                    android:layout_marginStart="@dimen/px_64"
                    android:gravity="center"
                    android:text="@string/scene_text_edit_scene_action"
                    android:textColor="@color/scene_color_text_2"
                    android:textSize="@dimen/scene_text_size_p42r_h3"
                    android:layout_marginTop="@dimen/px_10"
                    app:layout_constraintStart_toStartOf="@id/csl_edit_condition_title"
                    app:layout_constraintTop_toBottomOf="@id/rv_edit_scene_condition" />

                <!--动作列表-->
                <com.dfl.smartscene.widget.recycleview.HeightRecycleView
                    android:id="@+id/rv_edit_scene_action"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/px_64"
                    android:layout_marginTop="@dimen/px_20"
                    android:minHeight="@dimen/px_162"
                    android:overScrollMode="never"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:layout_constraintEnd_toEndOf="@id/csl_edit_condition_title"
                    app:layout_constraintStart_toStartOf="@id/csl_edit_condition_title"
                    app:layout_constraintTop_toBottomOf="@id/tv_edit_scene_action_name"
                    app:spanCount="6" />

                <!--自动执行开关+执行前询问-->
                <LinearLayout
                    android:id="@+id/ll_edit_scene_auto_execute_set"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/px_64"
                    android:layout_marginTop="@dimen/px_20"
                    android:orientation="vertical"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="@+id/csl_edit_condition_title"
                    app:layout_constraintTop_toBottomOf="@id/rv_edit_scene_action">

                    <TextView
                        android:id="@+id/tv_edit_scene_auto_execute_set"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/px_37"
                        android:text="@string/scene_text_edit_scene_auto_execute_set"
                        android:textColor="@color/scene_color_text_2"
                        android:textSize="@dimen/scene_text_size_p42r_h3" />
                    <!-- 注意：ll_condition_switch_scene 一定要隐藏显示-->
                    <LinearLayout
                        android:id="@+id/ll_condition_switch_scene"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/px_100"
                        android:layout_marginTop="@dimen/px_30"
                        android:orientation="horizontal"
                        app:layout_constraintStart_toStartOf="@+id/tv_edit_scene_auto_execute_set"
                        app:layout_constraintTop_toBottomOf="@+id/tv_edit_scene_auto_execute_set">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/cl_edit_auto_run_hot_area"
                            android:layout_width="@dimen/px_581"
                            android:layout_height="@dimen/px_100"
                            android:background="@drawable/scene_shape_detail_condition_able_r16">

                            <RadioButton
                                android:id="@+id/cb_auto_run"
                                android:layout_width="@dimen/px_40"
                                android:layout_height="@dimen/px_40"
                                android:layout_marginLeft="@dimen/px_48"
                                android:button="@drawable/state_complex_btn_p42r"
                                android:clickable="false"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />
                            <!--                        android:checked="@{vm.scenarioBeanLiveData.isAutoRun()}"-->

                            <TextView
                                android:id="@+id/textView3"
                                android:layout_width="wrap_content"
                                android:layout_height="@dimen/px_46"
                                android:layout_marginLeft="@dimen/px_24"
                                android:gravity="center"
                                android:text="@string/scene_text_common_automatic_execution"
                                android:textColor="@color/scene_color_text_1"
                                android:textSize="@dimen/scene_text_size_p42r_h3"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toEndOf="@+id/cb_auto_run"
                                app:layout_constraintTop_toTopOf="parent" />
                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/cl_edit_ask_hot_area"
                            android:layout_width="@dimen/px_581"
                            android:layout_height="@dimen/px_100"
                            android:layout_marginLeft="@dimen/px_24"
                            android:background="@drawable/scene_shape_detail_condition_able_r16">

                            <RadioButton
                                android:id="@+id/cb_ask_before_run"
                                android:layout_width="@dimen/px_40"
                                android:layout_height="@dimen/px_40"
                                android:layout_marginLeft="@dimen/px_48"
                                android:button="@drawable/state_complex_btn_p42r"
                                android:clickable="false"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />
                            <!--                        android:checked="@{vm.scenarioBeanLiveData.isAutoRun()}"-->
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="@dimen/px_46"
                                android:layout_marginLeft="24dp"
                                android:gravity="center"
                                android:text="@string/scene_button_ask_before_start_scene"
                                android:textColor="@color/scene_color_text_1"
                                android:textSize="@dimen/scene_text_size_p42r_h3"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toEndOf="@+id/cb_ask_before_run"
                                app:layout_constraintTop_toTopOf="parent" />
                        </androidx.constraintlayout.widget.ConstraintLayout>
                    </LinearLayout>
                </LinearLayout>

                <!--触发频率-->
                <LinearLayout
                    android:id="@+id/ll_edit_scene_auto_execute_frequency"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/px_64"
                    android:layout_marginTop="@dimen/px_40"
                    android:orientation="vertical"
                    android:paddingBottom="@dimen/px_20"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="@+id/csl_edit_condition_title"
                    app:layout_constraintTop_toBottomOf="@id/ll_edit_scene_auto_execute_set">

                    <TextView
                        android:id="@+id/tv_edit_scene_execute_frequency"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/px_37"
                        android:text="@string/scene_text_edit_scene_execute_frequency"
                        android:textColor="@color/scene_color_text_2"
                        android:textSize="@dimen/scene_text_size_p42r_h3"
                        app:layout_constraintStart_toStartOf="@+id/csl_edit_condition_title"
                        app:layout_constraintTop_toBottomOf="@id/ll_edit_scene_auto_execute_set" />

                    <com.dfl.android.animationlib.TabSwitchView
                        android:id="@+id/tab_switch_view_execute_frequency"
                        style="@style/TabSwitchViewHorizontalTheme"
                        android:layout_width="@dimen/px_773"
                        android:layout_marginTop="@dimen/px_20"
                        app:itemTotality="3"
                        app:textSize="@dimen/scene_text_size_p42r_h3"
                        app:itemSelectedBg="@drawable/nissan_tab_selected_bg_p42r"
                        app:layout_constraintStart_toStartOf="@+id/tv_edit_scene_execute_frequency"
                        app:layout_constraintTop_toBottomOf="@+id/tv_edit_scene_execute_frequency" />
                </LinearLayout>
                <!-- 下载进入时的底部加长-->
                <View
                    android:layout_width="0dp"
                    android:layout_height="204dp"
                    android:visibility="@{vm.isDownLoadSceneLiveData()? View.VISIBLE:View.GONE,default=gone}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/ll_edit_scene_auto_execute_frequency" />


                <com.dfl.android.animationlib.ScaleImageButton
                    android:id="@+id/btn_save"
                    style="@style/PrimaryBigBtnColorStyleP42R"
                    android:layout_marginBottom="@dimen/px_30"
                    android:minWidth="@dimen/px_292"
                    android:visibility="@{vm.isDownLoadSceneLiveData()? View.GONE:View.VISIBLE,default=visible}"
                    app:enable="@{vm.sceneEditReadyLiveData}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/btn_try_run"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/ll_edit_scene_auto_execute_frequency"
                    app:text="@string/scene_button_common_finish" />

                <com.dfl.android.animationlib.ScaleImageButton
                    android:id="@+id/btn_try_run"
                    style="@style/SecondaryBigBtnColorStyle"
                    android:layout_marginStart="@dimen/px_32"
                    android:minWidth="@dimen/px_292"
                    android:layout_height="@dimen/px_76"
                    android:visibility="@{vm.isDownLoadSceneLiveData()? View.GONE:View.VISIBLE,default=visible}"
                    app:enable="@{vm.tryStartSceneReadyLiveData}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/btn_save"
                    app:layout_constraintTop_toTopOf="@+id/btn_save"
                    app:text="@string/scene_text_scene_detail_try" />

                <com.airbnb.lottie.LottieAnimationView
                    android:id="@+id/lottie_try_run"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="@+id/btn_try_run"
                    app:layout_constraintEnd_toEndOf="@+id/btn_try_run"
                    app:layout_constraintStart_toStartOf="@+id/btn_try_run"
                    app:layout_constraintTop_toTopOf="@+id/btn_try_run"
                    app:lottie_loop="true" />


            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.dfl.android.animationlib.scrollView.OverScrollView>


        <!--底部-->
        <View
            android:id="@+id/view_bottom"
            android:layout_width="0dp"
            android:layout_height="240dp"
            android:background="@drawable/scene_shape_community_bottom_bg"
            android:visibility="@{vm.isDownLoadSceneLiveData()? View.VISIBLE:View.GONE,default=gone}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />


        <!--保持同一宽度-->
        <com.dfl.android.animationlib.ScaleImageButton
            android:id="@+id/btn_save2"
            style="@style/PrimaryBigBtnColorStyle"
            android:layout_marginBottom="56dp"
            android:enabled="@{vm.sceneEditReadyLiveData}"
            android:visibility="@{vm.isDownLoadSceneLiveData()? View.VISIBLE:View.GONE,default=gone}"
            app:backgroundDisableColor="@color/scene_color_bg_disable_community_next"
            app:layout_constraintBottom_toBottomOf="@id/view_bottom"
            app:layout_constraintEnd_toStartOf="@id/btn_try_run2"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="@id/view_bottom"
            app:text="@string/scene_text_community_save" />

        <com.dfl.android.animationlib.ScaleImageButton
            android:id="@+id/btn_try_run2"
            style="@style/SecondaryBigBtnColorStyle"
            android:layout_marginStart="64dp"
            android:layout_marginBottom="56dp"
            android:visibility="@{vm.isDownLoadSceneLiveData()? View.VISIBLE:View.GONE,default=gone}"
            app:backgroundBaseColor="@color/scene_color_bg_btn_try2"
            app:enable="@{vm.tryStartSceneReadyLiveData}"
            app:layout_constraintBottom_toBottomOf="@id/view_bottom"
            app:layout_constraintEnd_toEndOf="@id/view_bottom"
            app:layout_constraintStart_toEndOf="@id/btn_save2"
            app:text="@string/scene_text_scene_detail_try" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lottie_try_run2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@+id/btn_try_run2"
            app:layout_constraintEnd_toEndOf="@+id/btn_try_run2"
            app:layout_constraintStart_toStartOf="@+id/btn_try_run2"
            app:layout_constraintTop_toTopOf="@+id/btn_try_run2"
            app:lottie_loop="true" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</layout>