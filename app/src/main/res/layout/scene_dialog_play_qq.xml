<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!--        尺寸验证-->
        <!--        android:layout_width="@dimen/px_892"-->
        <androidx.constraintlayout.widget.ConstraintLayout
            style="@style/SmallDialogTheme"
            android:layout_marginTop="76dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_title"
                style="@style/SmallDialogTitleTheme"
                android:text="@string/scene_text_action_play_qq"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_desc"
                style="@style/SmallDialogSubTitleTheme"
                app:layout_constraintBottom_toBottomOf="@id/tv_title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="388dp"
                android:layout_marginBottom="212dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_title">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_check"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:overScrollMode="never" />
            </RelativeLayout>

            <include
                android:id="@+id/include_dialog_bottom"
                layout="@layout/scene_layout_dialog_bottom_button"
                android:layout_width="match_parent"
                android:layout_height="@dimen/scene_height_dialog_bottom"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>