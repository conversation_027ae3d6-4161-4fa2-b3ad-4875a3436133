<?xml version="1.0" encoding="utf-8" standalone="no"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/constraint_layout_item"
    android:background="@drawable/drawable_bg_scene_editor_operation"
    android:layout_height="@dimen/y_px_80"
    android:layout_width="@dimen/x_px_660"
    android:paddingLeft="@dimen/x_px_20"
    android:tag="background:drawable:drawable_bg_scene_editor_operation">

    <com.dfl.dflcommonlibs.uimodeutil.UIImageView
        android:id="@+id/imageview_scene_editor_item_icon"
        android:layout_height="@dimen/y_px_40"
        android:layout_width="@dimen/x_px_40"
        android:scaleType="fitXY"
        android:src="@drawable/icon_scene_editor_operation_default"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:ellipsize="end"
        android:id="@+id/textview_item_scene_editor_desc"
        android:includeFontPadding="false"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/x_px_12"
        android:layout_width="@dimen/x_px_500"
        android:maxLines="1"
        android:textColor="@color/color_text_first_label"
        android:textSize="@dimen/y_px_text_body"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/imageview_scene_editor_item_icon"
        app:layout_constraintTop_toTopOf="parent" />

    <com.dfl.dflcommonlibs.uimodeutil.UIImageView
        android:id="@+id/imagebutton_item_scene_editor_delete"
        android:layout_height="@dimen/y_px_72"
        android:layout_width="@dimen/x_px_72"
        android:scaleType="fitXY"
        android:src="@drawable/icon_scene_editor_operation_delete"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>