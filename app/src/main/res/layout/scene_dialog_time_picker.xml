<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/SmallDialogThemeP42R"
        android:layout_height="@dimen/px_490"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_picker_title"
            style="@style/SmallDialogTitleThemeP42R"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="时间点" />

        <!--        两条灰线-->
        <View
            android:id="@+id/view2"
            style="@style/WheelViewSelectViewTheme"
            android:layout_width="@dimen/px_616"
            android:layout_height="@dimen/px_94"
            app:layout_constraintBottom_toBottomOf="@+id/wv_hour_options"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/wv_hour_options" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_begin="@dimen/px_120" />

        <TextView
            android:id="@+id/tv_prefix"
            style="@style/WheelViewTextTheme"
            android:text="@string/scene_text_common_less_than"
            android:visibility="gone"
            android:textFontWeight="400"
            android:textSize="@dimen/scene_text_size_p42r_h3"
            app:layout_constraintBottom_toBottomOf="@id/wv_hour_options"
            app:layout_constraintStart_toStartOf="@id/guideline1"
            app:layout_constraintTop_toTopOf="@id/wv_hour_options"
            tools:text="前缀" />

        <com.dfl.smartscene.widget.wheel.WheelView
            android:id="@+id/wv_hour_options"
            style="@style/WheelViewFiveItemStyle"
            android:layout_width="@dimen/px_206"
            app:layout_constraintBottom_toTopOf="@id/include_dialog_bottom"
            app:layout_constraintEnd_toStartOf="@+id/wv_minute_options"
            app:layout_constraintStart_toStartOf="@id/guideline1"
            app:layout_constraintTop_toBottomOf="@id/tv_picker_title" />

        <TextView
            android:id="@+id/tv_point_dialog"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/scene_height_text_size_h3"
            android:gravity="center"
            android:text="@string/scene_text_common_colon"
            android:textColor="@color/scene_primary_color_highlight"
            android:textSize="@dimen/scene_text_size_h3"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@id/wv_hour_options"
            app:layout_constraintEnd_toStartOf="@id/wv_minute_options"
            app:layout_constraintStart_toEndOf="@id/wv_hour_options"
            app:layout_constraintTop_toTopOf="@id/wv_hour_options" />

        <com.dfl.smartscene.widget.wheel.WheelView
            android:id="@+id/wv_minute_options"
            style="@style/WheelViewFiveItemStyle"
            android:layout_width="@dimen/px_206"
            app:layout_constraintBottom_toBottomOf="@id/wv_hour_options"
            app:layout_constraintEnd_toStartOf="@id/wv_second_options"
            app:layout_constraintStart_toEndOf="@+id/wv_hour_options"
            app:layout_constraintTop_toTopOf="@id/wv_hour_options" />

        <com.dfl.smartscene.widget.wheel.WheelView
            android:id="@+id/wv_second_options"
            style="@style/WheelViewFiveItemStyle"
            android:layout_width="@dimen/px_206"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/wv_hour_options"
            app:layout_constraintEnd_toEndOf="@id/guideline2"
            app:layout_constraintStart_toEndOf="@+id/wv_minute_options"
            app:layout_constraintTop_toTopOf="@id/wv_hour_options" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_end="@dimen/px_134" />
        <!--<LinearLayout-->
        <!--    android:id="@+id/ll_time_picker_content"-->
        <!--    style="@style/WheelViewLinearLayoutTheme"-->
        <!--    android:layout_marginTop="16dp"-->
        <!--    android:gravity="center"-->
        <!--    app:layout_constraintEnd_toEndOf="parent"-->
        <!--    app:layout_constraintStart_toStartOf="parent"-->
        <!--    app:layout_constraintTop_toBottomOf="@+id/tv_picker_title">-->

        <!--    <TextView-->
        <!--        android:id="@+id/tv_prefix"-->
        <!--        style="@style/WheelViewTextTheme"-->
        <!--        android:layout_marginEnd="120dp"-->
        <!--        android:text="@string/scene_text_common_less_than"-->
        <!--        android:visibility="gone"-->
        <!--        tools:text="前缀" />-->

        <!--    <com.dfl.smartscene.widget.wheel.WheelView-->
        <!--        android:id="@+id/wv_hour_options"-->
        <!--        style="@style/WheelViewStyleWidth90Theme"-->
        <!--        android:layout_width="60dp"-->
        <!--        android:layout_marginRight="10dp" />-->


        <!--    <TextView-->
        <!--        android:id="@+id/tv_hour_dialog"-->
        <!--        style="@style/WheelViewTextTheme"-->
        <!--        android:layout_marginEnd="170dp"-->
        <!--        android:text="@string/scene_text_common_hour"-->
        <!--        android:visibility="gone" />-->

        <!--    <TextView-->
        <!--        android:id="@+id/tv_point_dialog"-->
        <!--        android:layout_width="96dp"-->
        <!--        android:layout_height="96dp"-->
        <!--        android:layout_marginHorizontal="68dp"-->
        <!--        android:gravity="center"-->
        <!--        android:text=":"-->
        <!--        android:textColor="@color/scene_primary_color"-->
        <!--        android:textSize="@dimen/scene_text_size_h2"-->
        <!--        android:visibility="visible" />-->

        <!--    <TextView-->
        <!--        android:id="@+id/tv_prefix3"-->
        <!--        style="@style/WheelViewTextTheme"-->
        <!--        android:textColor="@color/transparent"-->
        <!--        android:visibility="gone"-->
        <!--        tools:text="少" />-->

        <!--    <com.dfl.smartscene.widget.wheel.WheelView-->
        <!--        android:id="@+id/wv_minute_options"-->
        <!--        style="@style/WheelViewStyleWidth90Theme"-->
        <!--        android:layout_width="60dp" />-->

        <!--    <TextView-->
        <!--        android:id="@+id/tv_minute_dialog"-->
        <!--        style="@style/WheelViewTextTheme"-->
        <!--        android:text="@string/scene_text_common_minute"-->
        <!--        android:visibility="gone" />-->

        <!--    <com.dfl.smartscene.widget.wheel.WheelView-->
        <!--        android:id="@+id/wv_second_options"-->
        <!--        style="@style/WheelViewStyleWidth90Theme"-->
        <!--        android:layout_width="60dp"-->
        <!--        android:layout_marginStart="150dp"-->
        <!--        android:visibility="gone" />-->

        <!--    <TextView-->
        <!--        android:id="@+id/tv_second_dialog"-->
        <!--        style="@style/WheelViewTextTheme"-->
        <!--        android:text="@string/scene_text_common_second"-->
        <!--        android:visibility="gone" />-->

        <!--    <TextView-->
        <!--        android:id="@+id/tv_prefix2"-->
        <!--        style="@style/WheelViewTextTheme"-->
        <!--        android:layout_marginStart="120dp"-->
        <!--        android:textColor="@color/transparent"-->
        <!--        android:visibility="gone"-->
        <!--        tools:text="少" />-->
        <!--</LinearLayout>-->

        <include
            android:layout_marginTop="@dimen/px_28"
            android:id="@+id/include_dialog_bottom"
            layout="@layout/scene_layout_dialog_bottom_button"
            android:layout_width="match_parent"
            android:layout_height="@dimen/scene_height_dialog_bottom_p42r"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>