<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/SmallDialogTheme"
        android:layout_height="824dp"
        android:layout_marginTop="76dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_date_title"
            style="@style/SmallDialogTitleTheme"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="重复" />

        <com.dfl.android.animationlib.TabSwitchView
            android:id="@+id/tab_switch_view_condition"
            style="@style/TabSwitchViewHorizontalTheme"
            app:itemTotality="3"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_date_title" />

        <LinearLayout
            android:id="@+id/ll_content_recommend_week"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="visible"
            app:layout_constraintBottom_toTopOf="@id/include_dialog_bottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tab_switch_view_condition">
            <!--推荐-->
            <LinearLayout
                android:id="@+id/ll_center_date_setting"
                android:layout_width="836dp"
                android:layout_height="313dp"
                android:background="@drawable/scene_icon_bg_center_date_setting"
                android:gravity="center"
                android:orientation="horizontal"
                android:visibility="gone">


                <RadioButton
                    android:id="@+id/ctv_every_day"
                    android:layout_width="wrap_content"
                    android:layout_height="54dp"
                    android:button="@drawable/scene_selector_me_radio_check_style"
                    android:checked="true"
                    android:gravity="center"
                    android:text="@string/scene_text_edit_condition_time_every_day"
                    android:textAlignment="center"
                    android:textColor="@color/scene_color_text_1"
                    android:textSize="@dimen/scene_text_size_h4" />

                <RadioButton
                    android:id="@+id/ctv_legal_working_day"
                    android:layout_width="wrap_content"
                    android:layout_height="54dp"
                    android:layout_marginHorizontal="56dp"
                    android:button="@drawable/scene_selector_me_radio_check_style"
                    android:gravity="center"
                    android:text="@string/scene_text_edit_condition_time_legal_working_day"
                    android:textAlignment="center"
                    android:textColor="@color/scene_color_text_1"
                    android:textSize="@dimen/scene_text_size_h4" />

                <RadioButton
                    android:id="@+id/ctv_statutory_holidays"
                    android:layout_width="wrap_content"
                    android:layout_height="54dp"
                    android:button="@drawable/scene_selector_me_radio_check_style"
                    android:gravity="center"
                    android:text="@string/scene_text_edit_condition_time_statutory_holidays"
                    android:textAlignment="center"
                    android:textColor="@color/scene_color_text_1"
                    android:textSize="@dimen/scene_text_size_h4" />

            </LinearLayout>
            <!--每周-->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_date_which_day"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="68dp"
                android:layout_marginTop="24dp"
                android:overScrollMode="never"
                android:visibility="gone" />
        </LinearLayout>

        <!--指定日期-->
        <!-- WheelView选中位置白带样式-->
        <View
            android:id="@+id/v_appoint_hour"
            style="@style/WheelViewSelectViewTheme"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/ll_appoint_hour"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/ll_appoint_hour" />

        <LinearLayout
            android:id="@+id/ll_appoint_hour"
            style="@style/WheelViewLinearLayoutTheme"
            android:layout_marginStart="50dp"
            android:layout_marginTop="0dp"
            android:layout_marginBottom="0dp"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/include_dialog_bottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tab_switch_view_condition">

            <com.dfl.smartscene.widget.wheel.WheelView
                android:id="@+id/wheel_date_year"
                style="@style/WheelViewStyleWidth90Theme"
                android:layout_width="94dp" />

            <TextView
                style="@style/WheelViewTextTheme"
                android:layout_marginStart="20dp"
                android:text="@string/scene_text_common_year"
                android:textSize="@dimen/scene_text_size_h7" />

            <com.dfl.smartscene.widget.wheel.WheelView
                android:id="@+id/wheel_date_month"
                style="@style/WheelViewStyleWidth90Theme"
                android:layout_width="70dp"
                android:layout_marginStart="120dp"
                android:minWidth="70dp" />

            <TextView
                style="@style/WheelViewTextTheme"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="120dp"
                android:text="@string/scene_text_common_month"
                android:textSize="@dimen/scene_text_size_h7" />

            <com.dfl.smartscene.widget.wheel.WheelView
                android:id="@+id/wheel_date_day"
                style="@style/WheelViewStyleWidth90Theme"
                android:layout_width="70dp"
                android:minWidth="70dp" />

            <TextView
                style="@style/WheelViewTextTheme"
                android:layout_marginStart="20dp"
                android:text="@string/scene_text_common_day"
                android:textSize="@dimen/scene_text_size_h7" />
        </LinearLayout>
        <!--滚轮渐变蒙层-->
        <View
            android:id="@+id/date_wheel_mask"
            android:layout_width="844dp"
            android:layout_height="0dp"
            android:layout_marginTop="30dp"
            android:layout_marginBottom="30dp"
            android:background="@drawable/scene_layer_common_pop_wheel_mask"
            android:foregroundGravity="center_horizontal"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/include_dialog_bottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tab_switch_view_condition" />

        <include
            android:id="@+id/include_dialog_bottom"
            layout="@layout/scene_layout_dialog_bottom_button"
            android:layout_width="match_parent"
            android:layout_height="@dimen/scene_height_dialog_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>