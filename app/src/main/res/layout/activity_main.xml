<?xml version="1.0" encoding="utf-8" standalone="no"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_content"
    android:layout_height="match_parent"
    android:layout_width="match_parent"
    android:tag="background:drawable:bg_main_new"
    android:background="@drawable/bg_main_new"
    tools:context=".ccs.MainActivity">

    <fragment
        android:id="@+id/fragment_main_nav_host"
        android:layout_height="match_parent"
        android:layout_width="match_parent"
        android:name="com.dfl.smartscene.ccs.base.MainNavHostFragment"
        app:navGraph="@navigation/main_graph" />

    <View
        android:id="@+id/button_debug"
        android:layout_height="@dimen/y_px_50"
        android:layout_marginTop="@dimen/y_px_88"
        android:layout_width="@dimen/x_px_50"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <TextView
        android:id="@+id/textview_package_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/colorBlue"
        android:textSize="@dimen/y_px_text_title"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>