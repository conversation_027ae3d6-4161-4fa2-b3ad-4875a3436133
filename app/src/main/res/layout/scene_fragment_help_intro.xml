<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingStart="@dimen/px_63"
        android:layout_gravity="center"
        android:background="@drawable/scene_shape_common_medal_bg_radius_large">
        <!--左内容-->
        <ImageView
            android:id="@+id/iv_help_intro_left"
            android:layout_width="@dimen/px_270"
            android:layout_height="@dimen/px_206"
            android:layout_marginTop="@dimen/px_15"
            android:background="@drawable/scene_img_common_help_intro_left"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_help_intro_left"
            android:layout_width="@dimen/px_390"
            android:layout_height="@dimen/px_96"
            android:layout_marginTop="@dimen/px_24"
            android:text="@string/scene_text_help_intro_left"
            android:textColor="@color/scene_color_text_3"
            android:textSize="@dimen/scene_text_size_p42r_h6"
            android:textStyle="normal"
            app:layout_constraintStart_toStartOf="@+id/iv_help_intro_left"
            app:layout_constraintTop_toBottomOf="@+id/iv_help_intro_left" />

        <!--中内容-->
        <TextView
            android:id="@+id/tv_help_intro_middle"
            android:layout_width="@dimen/px_390"
            android:layout_height="@dimen/px_96"
            android:layout_marginTop="@dimen/px_24"
            android:maxLines="2"
            android:text="@string/scene_text_help_intro_middle"
            android:textColor="@color/scene_color_text_3"
            android:textSize="@dimen/scene_text_size_p42r_h6"
            android:textStyle="normal"
            app:layout_constraintStart_toStartOf="@+id/iv_help_intro_middle"
            app:layout_constraintTop_toBottomOf="@+id/iv_help_intro_middle" />

        <!--左内容-->
        <ImageView
            android:id="@+id/iv_help_intro_middle"
            android:layout_width="@dimen/px_270"
            android:layout_height="@dimen/px_206"
            android:layout_marginLeft="@dimen/px_186"
            android:background="@drawable/scene_img_common_help_intro_center"
            app:layout_constraintStart_toEndOf="@+id/iv_help_intro_left"
            app:layout_constraintTop_toTopOf="@+id/iv_help_intro_left" />

        <ImageView
            android:id="@+id/iv_help_intro_right"
            android:layout_width="@dimen/px_270"
            android:layout_height="@dimen/px_206"
            android:layout_marginStart="@dimen/px_186"
            android:background="@drawable/scene_img_common_help_intro_right"
            app:layout_constraintStart_toEndOf="@+id/iv_help_intro_middle"
            app:layout_constraintTop_toTopOf="@+id/iv_help_intro_left" />

        <TextView
            android:id="@+id/tv_help_intro_right"
            android:layout_width="@dimen/px_390"
            android:layout_height="@dimen/px_96"
            android:layout_marginTop="@dimen/px_24"
            android:maxLines="2"
            android:text="@string/scene_text_help_intro_right"
            android:textColor="@color/scene_color_text_3"
            android:textSize="@dimen/scene_text_size_p42r_h6"
            android:textStyle="normal"
            app:layout_constraintStart_toStartOf="@+id/iv_help_intro_right"
            app:layout_constraintTop_toBottomOf="@+id/iv_help_intro_right" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>