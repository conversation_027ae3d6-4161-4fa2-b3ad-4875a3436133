<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/x_px_610"
    android:layout_height="@dimen/y_px_500"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <View
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:background="@drawable/drawable_scene_img_chare_set_lujing2"
        app:layout_constraintBottom_toBottomOf="@+id/button_layout_seat_adjust_driver_whole_right"
        app:layout_constraintEnd_toStartOf="@+id/button_layout_seat_adjust_driver_whole_right"
        app:layout_constraintStart_toEndOf="@+id/button_layout_seat_adjust_driver_whole_left"
        app:layout_constraintTop_toTopOf="@+id/button_layout_seat_adjust_driver_whole_right" />

    <View
        android:layout_width="@dimen/x_px_1"
        android:layout_height="0dp"
        android:background="@drawable/drawable_scene_img_chare_set_lujing1"
        app:layout_constraintBottom_toTopOf="@+id/button_layout_seat_adjust_driver_whole_down"
        app:layout_constraintEnd_toEndOf="@+id/button_layout_seat_adjust_driver_whole_up"
        app:layout_constraintStart_toStartOf="@+id/button_layout_seat_adjust_driver_whole_up"
        app:layout_constraintTop_toBottomOf="@+id/button_layout_seat_adjust_driver_whole_up" />

    <View
        android:layout_width="0dp"
        android:layout_height="@dimen/y_px_39"
        android:layout_marginEnd="@dimen/x_px_25"
        android:rotation="10"
        android:background="@drawable/drawable_scene_img_chare_set_lujing4"
        app:layout_constraintEnd_toEndOf="@+id/button_layout_seat_adjust_driver_back_front"
        app:layout_constraintStart_toEndOf="@+id/button_layout_seat_adjust_driver_back_rear"
        app:layout_constraintTop_toTopOf="@+id/button_layout_seat_adjust_driver_back_rear" />

    <FrameLayout
        android:id="@+id/framelayout_layout_seat_adjust_driver_back_layout"
        android:layout_width="@dimen/x_px_550"
        android:layout_height="@dimen/y_px_400"

        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.iauto.uicontrol.ImageBase
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center|bottom"
            android:background="@drawable/drawable_scene_img_chare_set_shadow" />

        <com.iauto.uicontrol.ImageBase
            android:id="@+id/iv_back_view"
            android:layout_width="@dimen/x_px_400"
            android:layout_height="@dimen/y_px_400"
            android:layout_gravity="center"
            android:scaleType="center"
            android:src="@drawable/bg_set_backrestright_n"
            android:transformPivotX="@dimen/x_px_257"
            android:transformPivotY="@dimen/y_px_293"
            tools:ignore="ContentDescription" />

        <com.iauto.uicontrol.ImageBase
            android:id="@+id/iv_cushion_view"
            android:layout_width="@dimen/x_px_400"
            android:layout_height="@dimen/y_px_400"
            android:layout_gravity="center"
            android:scaleType="center"
            android:src="@drawable/bg_set_backcushright_n"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:ignore="ContentDescription" />
    </FrameLayout>


    <com.iauto.uicontrol.ButtonBase
        android:id="@+id/button_layout_seat_adjust_driver_back_front"
        android:layout_width="@dimen/x_px_55"
        android:layout_height="@dimen/y_px_55"
        android:layout_marginStart="@dimen/x_px_480"
        android:layout_marginTop="@dimen/y_px_120"
        android:background="@drawable/drawable_scene_btn_down"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.iauto.uicontrol.ButtonBase
        android:id="@+id/button_layout_seat_adjust_driver_back_rear"
        android:layout_width="@dimen/x_px_55"
        android:layout_height="@dimen/y_px_55"
        android:layout_marginStart="@dimen/x_px_200"
        android:layout_marginTop="@dimen/y_px_80"
        android:background="@drawable/drawable_scene_btn_lift"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.iauto.uicontrol.ButtonBase
        android:id="@+id/button_layout_seat_adjust_driver_whole_right"
        android:layout_width="@dimen/x_px_55"
        android:layout_height="@dimen/y_px_55"
        android:layout_marginStart="@dimen/x_px_450"
        android:layout_marginTop="@dimen/y_px_280"
        android:background="@drawable/drawable_scene_btn_right"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.iauto.uicontrol.ButtonBase
        android:id="@+id/button_layout_seat_adjust_driver_whole_left"
        android:layout_width="@dimen/x_px_55"
        android:layout_height="@dimen/y_px_55"
        android:layout_marginStart="@dimen/x_px_70"
        android:layout_marginTop="@dimen/y_px_280"
        android:background="@drawable/drawable_scene_btn_lift"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <com.iauto.uicontrol.ButtonBase
        android:id="@+id/button_layout_seat_adjust_driver_whole_up"
        android:layout_width="@dimen/x_px_55"
        android:layout_height="@dimen/y_px_55"
        android:layout_marginStart="@dimen/x_px_200"
        android:layout_marginTop="@dimen/y_px_150"
        android:background="@drawable/drawable_scene_btn_up"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.iauto.uicontrol.ButtonBase
        android:id="@+id/button_layout_seat_adjust_driver_whole_down"
        android:layout_width="@dimen/x_px_55"
        android:layout_height="@dimen/y_px_55"
        android:layout_marginStart="@dimen/x_px_200"
        android:layout_marginTop="@dimen/y_px_380"
        android:background="@drawable/drawable_scene_btn_down"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>