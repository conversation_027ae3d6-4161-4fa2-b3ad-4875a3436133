<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="1336dp"
    android:layout_height="176dp">

    <ImageView
        android:id="@+id/iv_location"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginStart="48dp"
        android:scaleType="center"
        android:src="@drawable/scene_icon_me_action_navigation_gowhere_list"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_address_item"
        android:layout_width="0dp"
        android:layout_height="54dp"
        android:layout_marginHorizontal="36dp"
        android:gravity="center_vertical"
        android:textColor="@color/scene_color_text_1"
        android:textSize="@dimen/scene_text_size_h4"
        app:layout_constraintBottom_toTopOf="@id/address_detail_item"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_location"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="浙江省杭州市上城区" />

    <TextView
        android:id="@+id/address_detail_item"
        android:layout_width="0dp"
        android:layout_height="42dp"
        android:layout_marginTop="2dp"
        android:textColor="@color/scene_color_text_3"
        android:textSize="@dimen/scene_text_size_h6"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/tv_address_item"
        app:layout_constraintStart_toStartOf="@id/tv_address_item"
        app:layout_constraintTop_toBottomOf="@id/tv_address_item"
        tools:text="宁夏回族自治区吴忠市同心路" />

    <View
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:background="#14000000"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>


