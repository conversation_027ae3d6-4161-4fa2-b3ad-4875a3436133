<?xml version="1.0" encoding="utf-8" standalone="no"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="match_parent"
    android:layout_width="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_delay_time_picker_value"
            android:layout_width="@dimen/x_px_262"
            android:layout_height="@dimen/y_px_200"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/layout_delay_time_picker_util"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_chainStyle="packed">
            <com.dfl.dflcommonlibs.uimodeutil.UIImageView
                android:layout_width="match_parent"
                android:layout_height="@dimen/y_px_86"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:background="@drawable/number_picaker_system_popup_selectbg_s"
                />

            <com.dfl.smartscene.ccs.view.weight.CustomNumberPicker
                android:id="@+id/numberpicker_item_operation_delay_time_value"
                android:layout_height="match_parent"
                android:layout_width="@dimen/x_px_100"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_delay_time_picker_util"
            android:layout_width="@dimen/x_px_262"
            android:layout_height="@dimen/y_px_200"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/layout_delay_time_picker_value"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">
            <com.dfl.dflcommonlibs.uimodeutil.UIImageView
                android:layout_width="match_parent"
                android:layout_height="@dimen/y_px_86"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:background="@drawable/number_picaker_system_popup_selectbg_s"
                />
            <com.dfl.smartscene.ccs.view.weight.CustomNumberPicker
                android:id="@+id/numberpicker_item_operation_delay_time_unit"
                android:layout_height="match_parent"
                android:layout_width="@dimen/x_px_100"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:id="@+id/textview_item_operation_delay_time_action_tip_start"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginStart="@dimen/x_px_50"
        android:text="@string/string_item_operation_delay_time_action_tip_start"
        android:textColor="@color/color_text_indicator"
        android:textSize="@dimen/x_px_28" />

<!--    <com.dfl.newscenepattern.view.weight.CustomNumberPicker-->
<!--        android:id="@+id/numberpicker_item_operation_delay_time_value"-->
<!--        android:layout_height="@dimen/y_px_200"-->
<!--        android:layout_marginStart="@dimen/x_px_100"-->
<!--        android:layout_width="@dimen/x_px_100" />-->

<!--    <com.dfl.newscenepattern.view.weight.CustomNumberPicker-->
<!--        android:id="@+id/numberpicker_item_operation_delay_time_unit"-->
<!--        android:layout_height="@dimen/y_px_200"-->
<!--        android:layout_width="@dimen/x_px_100" />-->

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:id="@+id/textview_item_operation_delay_time_action_tip_end"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/x_px_100"
        android:layout_width="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:text="@string/string_item_operation_delay_time_action_tip_end"
        android:textColor="@color/color_text_indicator"
        android:textSize="@dimen/x_px_28" />

</androidx.constraintlayout.widget.ConstraintLayout>