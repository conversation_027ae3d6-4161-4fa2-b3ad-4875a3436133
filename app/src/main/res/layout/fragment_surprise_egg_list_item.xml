<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <variable name="vm" type="com.dfl.smartscene.ccs.viewmodel.SurpriseEggListItemVM"/>
    </data>
    <com.iauto.uicontrol.ListItemBase
        android:layout_marginStart = "0px"
        android:layout_marginTop = "0px"
        android:layout_height = "@dimen/y_px_194"
        android:layout_width = "@dimen/x_px_439">
        <com.iauto.uicontrol.ListItemUnit
            android:layout_marginStart="0px"
            android:layout_marginTop="0px"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:ccSelected="@{vm.isSel.booleanValue()}"
            app:isBtnSelStateBgAnim="true"
            app:isBtnScaleAnim="true"
            app:btnScaleValue="0.95"
            app:onCcClick="onCCMethod_SurpriseEggItemClick"
            android:contentDescription="@{vm.name}"
            >

            <com.iauto.uicontrol.CircleImage
                android:id="@+id/surprise_egg_item_image"
                android:layout_marginStart="0px"
                android:layout_marginTop="0px"
                android:layout_height = "@dimen/y_px_194"
                android:layout_width = "@dimen/x_px_439"
                app:imageType="Rounde"
                app:roundeRadius="16"
                app:hasAlphaMask = "true"
                >

            </com.iauto.uicontrol.CircleImage>

            <TextView
                android:id="@+id/surprise_egg_item_text"
                android:layout_marginStart="32px"
                android:layout_marginTop="@dimen/y_px_126"
                android:layout_width="@dimen/x_px_297"
                android:layout_height="@dimen/y_px_42"
                android:maxLines="1"
                android:ellipsize="end"
                android:textSize="@dimen/font_size_34"
                android:textColor="@color/ColorId_screen_white_n"
                app:hasAlphaMask = "true"
                android:text="@{vm.name}"
                android:textStyle="bold"
                >
            </TextView>

            <com.iauto.uicontrol.ImageBase
                android:layout_marginStart="361px"
                android:layout_marginTop="@dimen/y_px_126"
                android:layout_width="@dimen/x_px_46"
                android:layout_height="@dimen/y_px_46"
                app:imageString="@drawable/drawable_item_suprise_egg_excute"
                />

            <com.iauto.uicontrol.ImageBase
                android:layout_width="@dimen/x_px_40"
                android:layout_height="@dimen/y_px_40"
                android:visibility="@{vm.editVisibility.intValue()}"
                app:imageString="@drawable/ic40_setting_unselected">
            </com.iauto.uicontrol.ImageBase>
            <com.iauto.uicontrol.ImageBase
                android:tag = "btnSelStateBg"
                android:layout_width="@dimen/x_px_40"
                android:layout_height="@dimen/y_px_40"
                app:imageString="@drawable/ic40_setting_topicselection">
            </com.iauto.uicontrol.ImageBase>

        </com.iauto.uicontrol.ListItemUnit>
    </com.iauto.uicontrol.ListItemBase>
</layout>