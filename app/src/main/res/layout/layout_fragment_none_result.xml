<?xml version="1.0" encoding="utf-8" standalone="no"?><!--推荐页面：暂无相关结果的提示-->
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/column_result_fragment_none_result"
    android:layout_height="match_parent"
    android:layout_width="match_parent"
    android:visibility="gone"
    tools:ignore="MissingConstraints">

    <com.dfl.dflcommonlibs.uimodeutil.UIImageView
        android:id="@+id/fragment_refresh_btn"
        android:layout_width="@dimen/x_px_384"
        android:layout_height="@dimen/x_px_192"
        android:src="@drawable/bg_icon_no_data"
        android:layout_marginTop="@dimen/y_px_66"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:id="@+id/tv_retry_network"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/y_px_46"
        android:layout_marginTop="@dimen/y_px_8"
        android:text="数据加载失败，请重试"
        android:textColor="@color/color_text_secondary_label"
        android:textSize="@dimen/font_size_28"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/fragment_refresh_btn" />

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:id="@+id/text_view_network_retry"
        android:layout_width="@dimen/x_px_184"
        android:layout_height="@dimen/y_px_54"
        android:background="@drawable/bg_button_positive_white"
        android:text="重试"
        android:textColor="@color/white"
        android:gravity="center"
        android:textSize="@dimen/font_size_26"
        android:layout_marginTop="@dimen/y_px_51"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_retry_network"/>

</androidx.constraintlayout.widget.ConstraintLayout>