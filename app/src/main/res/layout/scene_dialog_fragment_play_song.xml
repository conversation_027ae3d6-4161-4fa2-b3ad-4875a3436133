<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/SmallDialogTheme"
        android:layout_marginTop="76dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <TextView
            android:id="@+id/tv_title"
            style="@style/SmallDialogTitleTheme"
            android:text="@string/scene_text_action_play_qq"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_play_song_title_desc"
            style="@style/SmallDialogSubTitleTheme"
            android:text="@string/scene_text_action_only_qq"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@+id/tv_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_play_song"
            android:layout_width="@dimen/px_520"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/px_156"
            android:layout_marginTop="@dimen/px_64"
            android:background="@color/transparent"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_play_song_title_desc"
            app:spanCount="2" />

        <include
            android:id="@+id/include_dialog_bottom"
            layout="@layout/scene_layout_dialog_bottom_button"
            android:layout_width="match_parent"
            android:layout_height="@dimen/scene_height_dialog_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>