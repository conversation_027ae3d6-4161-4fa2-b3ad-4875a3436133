<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/csl_buy_smart_device_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/SmallDialogTheme"
        android:layout_width="@dimen/px_1200"
        android:layout_height="@dimen/px_752"
        android:layout_marginTop="76dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_close_buy"
            android:layout_width="@dimen/px_40"
            android:layout_height="@dimen/px_40"
            android:layout_marginStart="@dimen/px_40"
            android:layout_marginTop="@dimen/px_36"
            android:src="@drawable/scene_icon_common_close"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_buy_tips"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/px_112"
            android:gravity="center"
            android:text="@string/scene_text_scan_code_buy"
            android:textColor="@color/scene_color_text_1"
            android:textFontWeight="400"
            android:textSize="@dimen/px_36"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_qrcode_buy"
            android:layout_width="@dimen/px_268"
            android:layout_height="@dimen/px_268"
            android:layout_marginTop="@dimen/px_72"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_buy_tips"
            tools:srcCompat="@drawable/scene_img_me_action_device_buy_qr" />

        <TextView
            android:id="@+id/tv_scan_code_use_tips"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/px_32"
            android:layout_marginTop="@dimen/px_5"
            android:elegantTextHeight="false"
            android:gravity="center"
            android:text="@string/scene_text_scan_code_use_tips"
            android:textColor="@color/scene_color_text_3"
            android:textSize="@dimen/px_22"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_qrcode_buy" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="@dimen/px_40"
            android:layout_marginTop="@dimen/px_38"
            android:gravity="center"
            android:text="@string/scene_text_buy_bind_device_tips"
            android:textColor="@color/scene_color_text_3"
            android:textSize="@dimen/px_28"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_scan_code_use_tips" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>