<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/SmallDialogTheme"
        android:layout_height="628dp"
        android:layout_marginTop="76dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_volume_title"
            style="@style/SmallDialogTitleTheme"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="空调风量" />

        <TextView
            android:id="@+id/tv_volume_title_desc"
            style="@style/SmallDialogSubTitleTheme"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/tv_volume_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="设置车窗关闭度" />

        <com.dfl.smartscene.widget.indicatorseekbar.IndicatorStayLayout
            android:id="@+id/indicator_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toTopOf="@+id/include_dialog_bottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_volume_title">

            <com.dfl.smartscene.widget.indicatorseekbar.IndicatorSeekBar
                android:id="@+id/indicator_seek_bar"
                android:layout_width="784dp"
                android:layout_height="wrap_content"
                android:paddingHorizontal="36dp"
                app:isb_indicator_content_layout="@layout/scene_widget_indicator"
                app:isb_indicator_text_color="@color/scene_color_text_1"
                app:isb_indicator_text_size="@dimen/scene_text_size_h5"
                app:isb_max="7"
                app:isb_min="1"
                app:isb_progress="0"
                app:isb_show_indicator="custom"
                app:isb_show_tick_marks_type="oval"
                app:isb_thumb_color="@color/transparent"
                app:isb_thumb_drawable="@drawable/scene_icon_wight_indicator_thumb"
                app:isb_thumb_hide="false"
                app:isb_thumb_size="72dp"
                app:isb_tick_marks_color="@color/scene_color_text_normal_highlight_primary"
                app:isb_tick_marks_ends_hide="true"
                app:isb_ticks_count="8"
                app:isb_track_background_color="@color/scene_color_text_disable_secondary"
                app:isb_track_background_size="20dp"
                app:isb_track_progress_color="@color/scene_primary_color"
                app:isb_track_progress_size="20dp"
                app:isb_track_rounded_corners="true" />
        </com.dfl.smartscene.widget.indicatorseekbar.IndicatorStayLayout>


        <ImageView
            android:id="@+id/iv_left_icon"
            android:layout_width="@dimen/scene_icon_size_indicator_dialog_left"
            android:layout_height="@dimen/scene_icon_size_indicator_dialog_left"
            android:layout_marginEnd="12dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/iv_right_icon"
            app:layout_constraintEnd_toStartOf="@+id/indicator_layout"
            app:layout_constraintTop_toTopOf="@+id/iv_right_icon"
            tools:src="@drawable/scene_icon_me_action_seat_copilot_air" />

        <ImageView
            android:id="@+id/iv_right_icon"
            android:layout_width="@dimen/scene_icon_size_normal_icon"
            android:layout_height="@dimen/scene_icon_size_normal_icon"
            android:layout_marginStart="12dp"
            android:layout_marginBottom="12dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/indicator_layout"
            app:layout_constraintStart_toEndOf="@+id/indicator_layout"
            tools:src="@drawable/scene_icon_me_action_seat_copilot_air" />

        <include
            android:id="@+id/include_dialog_bottom"
            layout="@layout/scene_layout_dialog_bottom_button"
            android:layout_width="match_parent"
            android:layout_height="@dimen/scene_height_dialog_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>