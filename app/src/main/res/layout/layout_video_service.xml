<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:cardElevation = "0dp"
    app:cardMaxElevation = "0dp"
    app:cardCornerRadius="@dimen/x_px_20"
    app:cardBackgroundColor="@color/transparent">

    <include layout="@layout/fragment_scene_small_view"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_video_service_prepare"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include
            layout="@layout/layout_content_top"
            tools:layout_editor_absoluteX="0dp"
            tools:layout_editor_absoluteY="0dp" />

        <FrameLayout
            android:id="@+id/textview_scene_small_video_start"
            android:layout_width="@dimen/x_px_184"
            android:layout_height="@dimen/y_px_64"
            android:layout_marginTop="@dimen/y_px_442"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent">

            <com.dfl.dflcommonlibs.uimodeutil.UITextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/bg_play_relax_pattern_btn"
                android:gravity="left|center_vertical"
                android:includeFontPadding="false"
                android:paddingStart="@dimen/x_px_90"
                android:text="开始"
                android:textColor="@color/white"
                android:textSize="@dimen/y_px_text_body"/>

            <com.dfl.dflcommonlibs.uimodeutil.UIImageView
                android:layout_width="@dimen/x_px_32"
                android:layout_height="@dimen/y_px_32"
                android:layout_marginStart="@dimen/x_px_50"
                android:layout_marginTop="@dimen/y_px_16"
                android:background="@drawable/icon_full_view_resume" />

        </FrameLayout>

        <ImageView
            android:src="@drawable/icon_number_picker"
            android:layout_width="@dimen/x_px_252"
            android:layout_height="@dimen/x_px_252"
            android:layout_marginTop="@dimen/x_px_130"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"/>

        <com.iauto.uicontrol.ANumberPicker
            android:id="@+id/time_picker_video_service"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="@dimen/y_px_173"
            android:layout_width="@dimen/x_px_128"
            android:layout_height="@dimen/y_px_144"
            app:textAlignType = "Center"
            app:lines="3"
            app:midTextColor="@color/white"
            app:sideTextColor="@color/white"
            app:midTextSize="@integer/x_int_28"
            app:sideTextSize="@integer/x_int_24"/>


    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>