<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="90dp"
    android:gravity="center_vertical">

    <ImageView
        android:layout_width="@dimen/scene_icon_size_normal_icon"
        android:layout_height="@dimen/scene_icon_size_normal_icon"
        android:src="@drawable/scene_icon_common_search" />

    <TextView
        android:id="@+id/tv_search_lenovo"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/scene_height_text_size_h5"
        android:layout_marginStart="24dp"
        android:textColor="@color/scene_color_text_1"
        android:textSize="@dimen/scene_text_size_h5"
        tools:text="轻松轻松" />
</LinearLayout>