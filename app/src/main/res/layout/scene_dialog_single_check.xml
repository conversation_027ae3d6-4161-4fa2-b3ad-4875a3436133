<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/SmallDialogTheme"
        android:layout_marginTop="76dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <TextView
            android:id="@+id/tv_single_switch_title"
            style="@style/SmallDialogTitleTheme"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="左前车轮" />

        <TextView
            android:id="@+id/tv_single_switch_title_desc"
            style="@style/SmallDialogSubTitleTheme"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@id/tv_single_switch_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="----标题描述----" />

        <LinearLayout
            android:id="@+id/ll_open"
            style="@style/SelectorLinearLayoutTheme"
            android:layout_marginTop="72dp"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_single_switch_title">

            <ImageView
                android:id="@+id/imv_open"
                android:layout_width="@dimen/scene_icon_size_normal_icon"
                android:layout_height="@dimen/scene_icon_size_normal_icon"
                android:layout_marginEnd="@dimen/scene_margin_end_selector_checkbox_icon"
                android:visibility="gone"
                tools:src="@drawable/scene_icon_me_action_door_back_door" />

            <CheckedTextView
                android:id="@+id/ctv_single_open"
                style="@style/SelectorCheckTextViewTheme"
                android:checked="true"
                android:text="@string/scene_text_common_open" />
        </LinearLayout>

        <include
            android:id="@+id/include_dialog_bottom"
            layout="@layout/scene_layout_dialog_bottom_button"
            android:layout_width="match_parent"
            android:layout_height="@dimen/scene_height_dialog_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>