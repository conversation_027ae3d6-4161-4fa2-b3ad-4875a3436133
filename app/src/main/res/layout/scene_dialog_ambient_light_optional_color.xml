<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/SmallDialogThemeP42R"
        android:layout_height="700dp"
        android:maxHeight="700dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_light_color_optional_title"
            style="@style/SmallDialogTitleThemeP42R"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@string/scene_text_action_optional" />

        <include
            android:id="@+id/include_dialog_bottom"
            layout="@layout/scene_layout_dialog_bottom_button"
            android:layout_width="match_parent"
            android:layout_height="@dimen/scene_height_dialog_bottom_p42r"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/ll_content_light"
            style="@style/WheelViewLinearLayoutTheme"
            android:layout_width="0dp"
            android:layout_height="@dimen/px_433"
            android:layout_marginTop="@dimen/px_8"
            android:layout_marginBottom="@dimen/px_28"
            android:paddingHorizontal="@dimen/px_64"
            app:layout_constraintBottom_toTopOf="@id/include_dialog_bottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_light_color_optional_title">

            <com.dfl.smartscene.widget.SwatchesView
                android:id="@+id/swatches_view"
                android:layout_width="@dimen/px_616"
                android:layout_height="432dp"
                android:background="@drawable/scene_shape_swatches_view_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.dfl.smartscene.widget.seekbar.colorseekbar.ColorSeekBar
                android:id="@+id/seekbar_ambient_light"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginHorizontal="10dp"
                android:background="@color/scene_color_medal_bg"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>