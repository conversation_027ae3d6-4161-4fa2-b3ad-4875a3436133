<?xml version="1.0" encoding="utf-8" standalone="no"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/x_px_456"
    android:layout_height="@dimen/y_px_200">

    <ImageView
        android:id="@+id/item_mine_scene_bg"
        android:scaleType="fitXY"
        android:src="@drawable/scene_bg_my_card_1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <ImageView
        android:id="@+id/imageview_item_my_scene_icon"
        android:layout_width="@dimen/x_px_60"
        android:layout_height="@dimen/y_px_60"
        android:layout_marginBottom="@dimen/x_px_24"
        android:layout_marginRight="@dimen/x_px_30"
        android:scaleType="fitXY"
        android:src="@drawable/icon_edit_scene"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <com.dfl.dflcommonlibs.uimodeutil.UIImageView
        android:id="@+id/image_item_my_scene_delete"
        android:layout_height="@dimen/x_px_50"
        android:layout_width="@dimen/x_px_50"
        android:layout_marginTop="@dimen/x_px_24"
        android:layout_marginRight="@dimen/x_px_35"
        android:scaleType="fitXY"
        android:src="@drawable/icon_delete"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>
    
    <ImageButton
        android:id="@+id/imageButton_my_scene_delete"
        android:layout_width="@dimen/x_px_136"
        android:layout_height="@dimen/x_px_54"
        android:layout_marginTop="@dimen/x_px_20"
        android:layout_marginRight="@dimen/x_px_25"
        android:background="@drawable/bg_my_scene_delete"
        android:visibility="invisible"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:id="@+id/textview_my_scene_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/x_px_24"
        android:layout_marginTop="@dimen/x_px_24"
        android:lines="1"
        android:maxEms="6"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:textColor="#FFFFFFFF"
        android:textSize="@dimen/program_time_width"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageButton
        android:id="@+id/imagebutton_my_scene_excute"
        android:layout_width="@dimen/x_px_64"
        android:layout_height="@dimen/y_px_64"
        android:layout_marginLeft="@dimen/x_px_24"
        android:layout_marginBottom="@dimen/x_px_24"
        android:background="@drawable/drawable_item_my_scene_execute"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <com.dfl.dflcommonlibs.uimodeutil.UICheckBox
        android:id="@+id/checkbox_item_my_scene"
        android:layout_width="@dimen/x_px_40"
        android:layout_height="@dimen/y_px_40"
        android:button="@drawable/selector_checkbox_scene_select"
        android:enabled="false"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>