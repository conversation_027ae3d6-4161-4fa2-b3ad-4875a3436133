<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <variable name="vm" type="com.dfl.smartscene.ccs.viewmodel.SurpriseEggPlayVM"/>
    </data>
    <RelativeLayout
        xmlns:app = "http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        tools:context=".SurpriseEggPlayFragment"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="0px"
        android:layout_marginTop="0px">

        <WebView
            android:id="@+id/surprise_egg_web_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
        <com.iauto.uicontrol.ButtonView
            android:id="@+id/button_base_close"
            android:layout_width="@dimen/x_px_72"
            android:layout_height="@dimen/y_px_72"
            android:layout_marginStart="@dimen/x_px_24"
            android:layout_marginTop="@dimen/y_px_8"
            android:contentDescription="@string/string_close_contentDescription"
            android:paddingStart="@dimen/x_px_16"
            android:paddingTop="@dimen/y_px_16"
            android:paddingEnd="@dimen/x_px_16"
            android:paddingBottom="@dimen/y_px_16"
            app:hasAlphaMask="true"
            app:isBtnScaleAnim="true"
            app:btnScaleValue="0.95"
            app:leftImageHeight="@integer/y_int_40"
            app:leftImageId="@drawable/ic48_system_closebig_n"
            app:leftImageWidth="@integer/x_int_40"
            >
        </com.iauto.uicontrol.ButtonView>
    </RelativeLayout>
</layout>