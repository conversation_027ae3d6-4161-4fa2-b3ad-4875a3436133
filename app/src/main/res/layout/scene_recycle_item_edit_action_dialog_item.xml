<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_action_item"
    android:layout_width="@dimen/px_190"
    android:layout_height="@dimen/px_162"
    android:layout_marginEnd="@dimen/px_20"
    android:layout_marginBottom="@dimen/px_20"
    android:background="@drawable/scene_shape_white_pop_r12"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_action_reflex_icon"
        android:layout_width="@dimen/px_60"
        android:layout_height="@dimen/px_60"
        android:layout_marginTop="@dimen/px_23"
        android:scaleType="center"
        tools:src="@drawable/scene_icon_me_action_delay" />

    <TextView
        android:id="@+id/tv_action_reflex_name"
        android:layout_width="@dimen/px_190"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="2"
        android:minHeight="@dimen/px_37"
        android:textColor="@color/scene_color_text_2"
        android:textSize="@dimen/scene_text_size_h7"
        tools:text="氛围灯场景灯返回啊哈" />
</LinearLayout>
