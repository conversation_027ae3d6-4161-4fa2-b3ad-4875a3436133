<?xml version="1.0" encoding="utf-8" standalone="no"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_height="match_parent"
    android:layout_width="match_parent">

    <com.dfl.smartscene.ccs.view.weight.SeatAdjustDriverLayout
        android:id="@+id/seat_adjust_driver_layout_item_operation_seat_adjust_layout_driver"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        android:visibility="gone" />

    <!--    <com.dfl.newscenepattern.view.weight.SeatAdjustRelativeLayout-->
    <!--        android:id="@+id/seat_adjust_relative_layout_item_operation_seat_adjust_layout_driver"-->
    <!--        android:layout_width="@dimen/x_px_400"-->
    <!--        android:layout_height="@dimen/y_px_480"-->
    <!--        android:layout_marginStart="@dimen/x_px_100"-->
    <!--        android:tag="driver"-->
    <!--        android:visibility="gone" />-->

    <com.dfl.smartscene.ccs.view.weight.SeatBackrestRelativeLayout
        android:id="@+id/seat_back_rest_layout_item_operation_seat_adjust_layout_driver"
        android:layout_height="@dimen/y_px_480"
        android:layout_marginStart="@dimen/x_px_100"
        android:layout_width="@dimen/x_px_400"
        android:tag="driver"
        android:visibility="gone" />

    <com.dfl.smartscene.ccs.view.weight.SeatAdjustCopilotLayout
        android:id="@+id/seat_adjust_copilot_layout_item_operation_seat_adjust_layout_copilot"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        android:visibility="gone" />

    <!--    <com.dfl.newscenepattern.view.weight.SeatAdjustRelativeLayout-->
    <!--        android:id="@+id/seat_adjust_relative_layout_item_operation_seat_adjust_layout_copilot"-->
    <!--        android:layout_width="@dimen/x_px_400"-->
    <!--        android:layout_height="@dimen/y_px_480"-->
    <!--        android:layout_marginStart="@dimen/x_px_100"-->
    <!--        android:tag="copilot"-->
    <!--        android:visibility="gone" />-->

    <com.dfl.smartscene.ccs.view.weight.SeatBackrestRelativeLayout
        android:id="@+id/seat_back_rest_layout_item_operation_seat_adjust_layout_copilot"
        android:layout_height="@dimen/y_px_480"
        android:layout_marginStart="@dimen/x_px_100"
        android:layout_width="@dimen/x_px_400"
        android:tag="copilot"
        android:visibility="gone" />

    <com.dfl.smartscene.ccs.view.weight.CustomRadioGroup
        android:id="@+id/custom_radio_group_item_operation_seat_adjust_layout"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_height="@dimen/x_px_56"
        android:layout_marginEnd="@dimen/x_px_148"
        android:layout_width="@dimen/x_px_224"
        android:visibility="invisible" />

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:id="@+id/textview_item_operation_seat_adjust_driver_title"
        android:layout_above="@id/custom_radio_group_item_operation_seat_adjust_layout"
        android:layout_alignParentEnd="true"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/y_px_32"
        android:layout_marginEnd="@dimen/x_px_228"
        android:layout_width="wrap_content"
        android:text="@string/string_item_operation_seat_memory_title"
        android:textColor="@color/color_text_tertiary_label"
        android:textSize="@dimen/font_size_32"
        android:visibility="invisible" />


    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:id="@+id/textview_item_operation_seat_adjust_driver_hint"
        android:layout_alignParentEnd="true"
        android:layout_below="@+id/custom_radio_group_item_operation_seat_adjust_layout"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/x_px_96"
        android:layout_marginTop="@dimen/y_px_122"
        android:layout_width="wrap_content"
        android:text="@string/string_item_operation_seat_memory_hint"
        android:textColor="@color/color_text_tertiary_label"
        android:textSize="@dimen/font_size_28"
        android:visibility="gone" />

</RelativeLayout>