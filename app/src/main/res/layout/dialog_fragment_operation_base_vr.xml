<?xml version="1.0" encoding="utf-8" standalone="no"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_height="wrap_content"
    android:layout_width="wrap_content"
    >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:background="@drawable/bg_common_dialog_small"
        android:layout_height="@dimen/y_px_502"
        android:layout_width="@dimen/x_px_722"
        android:tag="background:drawable:bg_common_dialog_small"
        android:paddingTop="@dimen/y_px_16"
        android:paddingLeft="@dimen/x_px_16"
        android:paddingRight="@dimen/x_px_16"
        android:paddingBottom="@dimen/y_px_16">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_top_bar"
            android:layout_height="@dimen/y_px_80"
            android:layout_width="match_parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.dfl.dflcommonlibs.uimodeutil.UITextView
                android:id="@+id/button_operation_base_dialog_title"
                android:layout_height="wrap_content"
                android:layout_width="wrap_content"
                android:textColor="@color/color_text_first_label"
                android:textSize="@dimen/x_px_32"
                android:layout_marginTop="@dimen/y_px_40"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />



        </androidx.constraintlayout.widget.ConstraintLayout>


        <FrameLayout
            android:id="@+id/layout_operation_dialog_content"
            android:layout_height="@dimen/y_px_222"
            android:layout_marginTop="@dimen/y_px_24"
            android:layout_width="match_parent"
            app:layout_constraintTop_toBottomOf="@id/layout_top_bar"
            android:clipChildren="false"
            android:clipToPadding="false"/>

        <com.dfl.dflcommonlibs.uimodeutil.UITextView
            android:gravity="center"
            android:id="@+id/textview_dialog_operation_base_bottom_desc"
            android:includeFontPadding="false"
            android:layout_height="@dimen/y_px_42"
            android:layout_marginTop="@dimen/y_px_16"
            android:layout_width="match_parent"
            android:textColor="@color/color_text_secondary_label"
            android:textSize="@dimen/y_px_text_body"
            app:layout_constraintTop_toBottomOf="@id/layout_top_bar" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:background="@drawable/bg_button_positive"
            android:id="@+id/button_operation_base_dialog_positive"
            android:layout_height="@dimen/y_px_92"
            android:layout_marginBottom="@dimen/y_px_40"
            android:layout_marginLeft="@dimen/x_px_110"
            android:layout_width="@dimen/x_px_222"
            android:tag="background:drawable:bg_button_positive"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent">

            <com.dfl.dflcommonlibs.uimodeutil.UITextView
                android:includeFontPadding="false"
                android:layout_height="wrap_content"
                android:layout_width="wrap_content"
                android:text="确定"
                android:textColor="@color/color_white"
                android:textSize="@dimen/y_px_text_body"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:background="@drawable/bg_button_negative_new"
            android:id="@+id/imageview_operation_base_dialog_back"
            android:layout_height="@dimen/y_px_92"
            android:layout_marginBottom="@dimen/y_px_40"
            android:layout_marginRight="@dimen/x_px_110"
            android:layout_width="@dimen/x_px_222"
            android:tag="background:drawable:bg_button_negative_new"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent">

            <com.dfl.dflcommonlibs.uimodeutil.UITextView
                android:includeFontPadding="false"
                android:layout_height="wrap_content"
                android:layout_width="wrap_content"
                android:text="取消"
                android:textColor="@color/color_dialog_btn_text_cancel"
                android:textSize="@dimen/y_px_text_body"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


</FrameLayout>