<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <!--加载画面-->
    <include
        layout="@layout/scene_layout_comment_req_res"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <!--返回键-->
    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/scene_icon_size_normal_icon"
        android:layout_height="@dimen/scene_icon_size_normal_icon"
        android:layout_marginStart="76dp"
        android:src="@drawable/scene_icon_common_arrow_left"
        app:layout_constraintBottom_toBottomOf="@id/view_edit"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/view_edit" />

    <!--输入框-->
    <View
        android:id="@+id/view_edit"
        android:layout_width="2180dp"
        android:layout_height="96dp"
        android:layout_marginStart="48dp"
        android:layout_marginTop="152dp"
        android:background="@drawable/scene_shape_common_bg_2_radius_large"
        app:layout_constraintStart_toEndOf="@id/iv_back"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:layout_width="@dimen/scene_icon_size_small_icon"
        android:layout_height="@dimen/scene_icon_size_small_icon"
        android:layout_marginStart="48dp"
        android:src="@drawable/scene_icon_common_search"
        app:layout_constraintBottom_toBottomOf="@id/view_edit"
        app:layout_constraintStart_toStartOf="@id/view_edit"
        app:layout_constraintTop_toTopOf="@id/view_edit" />

    <com.dfl.smartscene.widget.ClearEditText
        android:id="@+id/et_search"
        android:layout_width="0dp"
        android:layout_height="@dimen/scene_height_text_size_h5"
        android:layout_marginStart="108dp"
        android:layout_marginEnd="48dp"
        android:background="@null"
        android:cursorVisible="true"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:hint="@string/scene_text_community_search_hint"
        android:imeOptions="actionSearch"
        android:inputType="text"
        android:maxLength="30"
        android:maxLines="1"
        android:textColor="@color/scene_color_text_1"
        android:textColorHint="@color/scene_color_text_3"
        android:textCursorDrawable="@drawable/scene_shape_edit_cursor_bg"
        android:textSize="@dimen/scene_text_size_h5"
        app:layout_constraintBottom_toBottomOf="@id/view_edit"
        app:layout_constraintEnd_toEndOf="@id/view_edit"
        app:layout_constraintStart_toStartOf="@id/view_edit"
        app:layout_constraintTop_toTopOf="@id/view_edit" />

    <TextView
        android:id="@+id/tv_search"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/scene_height_text_size_h3"
        android:layout_marginStart="52dp"
        android:enabled="false"
        android:gravity="center"
        android:text="@string/scene_button_community_search"
        android:textColor="@color/scene_selector_search_tv_color"
        android:textSize="@dimen/scene_text_size_h3"
        app:layout_constraintBottom_toBottomOf="@id/view_edit"
        app:layout_constraintStart_toEndOf="@id/view_edit"
        app:layout_constraintTop_toTopOf="@id/view_edit" />

    <!--最近搜索-->
    <TextView
        android:id="@+id/tv_recent_search_desc"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/scene_height_text_size_h3"
        android:layout_marginTop="64dp"
        android:gravity="center"
        android:text="@string/scene_text_search_recent_search"
        android:textAlignment="center"
        android:textColor="@color/scene_color_text_1"
        android:textSize="@dimen/scene_text_size_h3"
        app:layout_constraintStart_toStartOf="@id/iv_back"
        app:layout_constraintTop_toBottomOf="@id/view_edit" />

    <ImageView
        android:id="@+id/iv_clear_recent_search"
        android:layout_width="@dimen/scene_icon_size_normal_icon"
        android:layout_height="@dimen/scene_icon_size_normal_icon"
        android:layout_marginEnd="76dp"
        android:src="@drawable/scene_icon_common_clear_text3"
        app:layout_constraintBottom_toBottomOf="@id/tv_recent_search_desc"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_recent_search_desc" />

    <com.dfl.smartscene.widget.flodlayout.FoldLayout
        android:id="@+id/fold_recent_search"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="44dp"
        android:clipChildren="false"
        app:flow_equally="false"
        app:flow_fold="true"
        app:flow_foldLines="1"
        app:flow_horizontalSpacing="40dp"
        app:flow_verticalSpacing="40dp"
        app:layout_constraintEnd_toEndOf="@id/tv_search"
        app:layout_constraintStart_toStartOf="@id/tv_recent_search_desc"
        app:layout_constraintTop_toBottomOf="@id/tv_recent_search_desc" />

    <!--搜索联想-->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_lenovo"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="65dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/tv_search"
        app:layout_constraintStart_toStartOf="@id/iv_back"
        app:layout_constraintTop_toBottomOf="@id/view_edit" />

    <!--搜索结果-->
    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:id="@+id/sl_update"
        style="@style/SmartRefreshLayoutTheme"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="72dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/tv_search"
        app:layout_constraintStart_toStartOf="@id/iv_back"
        app:layout_constraintTop_toBottomOf="@id/view_edit">

        <com.dfl.smartscene.widget.ClassicsHeader4All
            android:id="@+id/header"
            style="@style/SmartRefreshClassicsTheme" />

        <com.dfl.android.animationlib.scrollView.FadeRecyclerView
            android:id="@+id/rv_search"
            style="@style/FadeRecyclerViewTheme1A"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never" />

        <com.dfl.smartscene.widget.ClassicsFooter4All
            android:id="@+id/classicsFooter"
            style="@style/SmartRefreshClassicsTheme.FooterTheme" />
    </com.scwang.smartrefresh.layout.SmartRefreshLayout>

    <!--搜索结果为空-->
    <ImageView
        android:id="@+id/iv_search_result_empty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="352dp"
        android:src="@drawable/scene_img_community_search_no_data"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/view_edit" />

    <TextView
        android:id="@+id/tv_search_result_empty"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/scene_height_text_size_h4"
        android:layout_marginTop="28dp"
        android:text="@string/scene_text_search_result_empty"
        android:textColor="@color/scene_color_text_3"
        android:textSize="@dimen/scene_text_size_h4"
        app:layout_constraintEnd_toEndOf="@id/iv_search_result_empty"
        app:layout_constraintStart_toStartOf="@id/iv_search_result_empty"
        app:layout_constraintTop_toBottomOf="@id/iv_search_result_empty" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_search_result_empty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tv_search_result_empty,iv_search_result_empty" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_recent_search"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="tv_recent_search_desc,fold_recent_search,iv_clear_recent_search" />
</androidx.constraintlayout.widget.ConstraintLayout>