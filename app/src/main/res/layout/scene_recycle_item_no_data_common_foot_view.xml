<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_me_beian_foot"
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            android:drawableEnd="@drawable/scene_icon_common_arrow_right"
            android:drawablePadding="24dp"
            android:gravity="center"
            android:text="@string/scene_software_record_number"
            android:textColor="@color/scene_color_text_3"
            android:textSize="@dimen/scene_text_size_h6"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>