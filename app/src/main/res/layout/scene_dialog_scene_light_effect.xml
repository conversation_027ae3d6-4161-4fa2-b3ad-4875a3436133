<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <!--新版ui已废弃，再次使用以设计稿为准-->
    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/SmallDialogTheme"
        android:layout_marginTop="76dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_light_effect_title"
            style="@style/SmallDialogTitleTheme"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="场景灯效" />

        <RadioGroup
            android:id="@+id/rg_effect"
            android:layout_width="@dimen/px_340"
            android:layout_height="@dimen/px_64"
            android:layout_marginTop="5dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_light_effect_title">

            <RadioButton
                android:id="@+id/rb_drive_mode_effect"
                style="@style/RadioButtonSwitchTheme"
                android:layout_width="@dimen/px_168"
                android:layout_height="@dimen/px_64"
                android:checked="true"
                android:text="@string/scene_text_action_drive_mode" />

            <RadioButton
                android:id="@+id/rb_air_mode_effect"
                style="@style/RadioButtonSwitchTheme"
                android:layout_width="@dimen/px_168"
                android:layout_height="@dimen/px_64"
                android:text="@string/scene_text_action_air_mode" />
        </RadioGroup>


        <LinearLayout
            android:id="@+id/ll_content_light"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/px_160"
            android:layout_marginTop="@dimen/px_50"
            android:background="@color/transparent"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/rg_effect">

            <CheckedTextView
                android:id="@+id/ctv_open_effect"
                style="@style/SelectorCheckTextViewTheme"
                android:layout_width="@dimen/scene_min_width_selector_checkbox"
                android:background="@drawable/scene_selector_edit_checkbox_bg"
                android:checked="true"
                android:text="@string/scene_text_common_open" />


            <CheckedTextView
                android:id="@+id/ctv_close_effect"
                style="@style/SelectorCheckTextViewTheme"
                android:layout_width="@dimen/scene_min_width_selector_checkbox"
                android:layout_marginStart="84dp"
                android:background="@drawable/scene_selector_edit_checkbox_bg"
                android:text="@string/scene_text_common_close" />
        </LinearLayout>

        <include
            android:id="@+id/include_dialog_bottom"
            layout="@layout/scene_layout_dialog_bottom_button"
            android:layout_width="match_parent"
            android:layout_height="@dimen/scene_height_dialog_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_content_light" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>