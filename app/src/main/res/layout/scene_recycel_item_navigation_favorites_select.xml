<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="isSelected"
            type="Boolean" />

        <variable
            name="bean"
            type="com.dfl.soacenter.entity.RoutePoi" />

        <import type="android.view.View" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/px_544"
        android:layout_height="@dimen/px_148"
        android:layout_marginEnd="@dimen/px_32"
        android:layout_marginBottom="@dimen/px_32"
        android:background="@drawable/scene_shape_common_bg_day_bg2_night_medal_component_r24">

        <ImageView
            android:id="@+id/iv_location"
            android:layout_width="@dimen/px_36"
            android:layout_height="@dimen/px_36"
            android:layout_marginStart="@dimen/px_48"
            android:scaleType="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_location_name"
            android:layout_width="0dp"
            android:layout_height="@dimen/px_46"
            android:layout_marginStart="@dimen/px_28"
            android:layout_marginEnd="@dimen/px_48"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:lines="1"
            android:text="@{bean.name}"
            android:textSize="@dimen/scene_text_size_p42r_h3"
            android:textColor="@color/scene_color_text_1"
            app:layout_constraintBottom_toTopOf="@id/tv_location_address"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_location"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="浙江省杭州市上城区" />

        <TextView
            android:id="@+id/tv_location_address"
            android:layout_width="0dp"
            android:layout_height="@dimen/px_40"
            android:layout_marginTop="2dp"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:lines="1"
            android:text="@{bean.address}"
            android:textColor="@{isSelected?@color/scene_primary_color_highlight:@color/scene_color_text_3, default = @color/scene_color_text_3 }"
            android:textSize="@dimen/scene_text_size_p42r_h6"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/tv_location_name"
            app:layout_constraintStart_toStartOf="@id/tv_location_name"
            app:layout_constraintTop_toBottomOf="@id/tv_location_name"
            tools:text="宁夏回族自治区吴忠市同心路" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>