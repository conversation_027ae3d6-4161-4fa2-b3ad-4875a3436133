<?xml version="1.0" encoding="utf-8" standalone="no"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="@dimen/y_px_100"
    android:id="@+id/ccs_item_user_day"
    android:layout_width="@dimen/x_px_170"
    android:tag="background:drawable:bg_item_user_day">

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:id="@+id/textview_item_user_day_title"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        android:textColor="@color/color_text_first_label"
        android:textSize="@dimen/y_px_text_subtitle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.dfl.dflcommonlibs.uimodeutil.UICheckBox
        android:id="@+id/checkbox_item_user_day"
        android:layout_width="@dimen/x_px_48"
        android:layout_height="@dimen/y_px_48"
        android:button="@drawable/selector_checkbox_state"
        android:clickable="false"
        android:enabled="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>