<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="isSelected"
            type="Boolean" />

        <variable
            name="bean"
            type="com.dfl.soacenter.entity.RoutePoi" />

        <import type="android.view.View" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="176dp"
        android:background="@color/transparent">

        <ImageView
            android:id="@+id/iv_location"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:layout_marginStart="48dp"
            android:scaleType="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_location_name"
            android:layout_width="0dp"
            android:layout_height="54dp"
            android:layout_marginHorizontal="36dp"
            android:gravity="center_vertical"
            android:text="@{bean.name}"
            android:textColor="@{isSelected?@color/scene_primary_color_highlight:@color/scene_color_text_1, default = @color/scene_color_text_1 }"
            android:textSize="@dimen/scene_text_size_h3"
            app:layout_constraintBottom_toTopOf="@id/tv_location_address"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_location"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="浙江省杭州市上城区" />

        <TextView
            android:id="@+id/tv_location_address"
            android:layout_width="0dp"
            android:layout_height="42dp"
            android:layout_marginTop="2dp"
            android:text="@{bean.address}"
            android:textColor="@{isSelected?@color/scene_primary_color_highlight:@color/scene_color_text_3, default = @color/scene_color_text_3 }"
            android:textSize="@dimen/scene_text_size_h5"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/tv_location_name"
            app:layout_constraintStart_toStartOf="@id/tv_location_name"
            app:layout_constraintTop_toBottomOf="@id/tv_location_name"
            tools:text="宁夏回族自治区吴忠市同心路" />

        <View
            android:id="@+id/view_line"
            android:layout_width="0dp"
            android:layout_height="2dp"
            android:layout_marginHorizontal="48dp"
            android:background="@color/scene_color_divider"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>