<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable name="vm" type="com.dfl.smartscene.ccs.viewmodel.RelieveStressPatternVM"/>
    </data>
    <RelativeLayout xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="0px"
        android:layout_marginTop="0px">
        <com.iauto.uicontrol.GestureBoard
            android:id="@+id/gesture_board_relieve_stress_full_start"
            android:layout_marginStart="0px"
            android:layout_marginTop="0px"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clickable="false"
            >
        </com.iauto.uicontrol.GestureBoard>
        <com.iauto.uicontrol.ViewPagerList
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:id="@+id/background_view_full_paper"
            />

        <com.iauto.uicontrol.TextBase
            android:id="@+id/text_relieve_stress_full_time"
            android:visibility="@{vm.relieveStressTimerVisible.intValue()}"
            android:layout_marginStart="@dimen/relieve_stress_full_time_x"
            android:layout_marginTop="@dimen/relieve_stress_full_time_y"
            android:layout_width="@dimen/x_px_300"
            android:layout_height="@dimen/y_px_120"
            android:textColor="@color/color_text_text"
            android:textSize="@dimen/font_size_80"
            android:text="@{vm.relieveStressDspTimer.toString()}"
            android:gravity="center_vertical|center_horizontal"/>

        <com.iauto.uicontrol.ButtonView
            android:id="@+id/button_relieve_stress_full_stop"
            android:visibility="@{vm.relieveStressCtrlViewVisibleInPause.intValue()}"
            android:layout_width="@dimen/x_px_232"
            android:layout_height="@dimen/x_px_96"
            android:layout_marginTop="@dimen/relieve_stress_full_stop_y"
            android:layout_marginStart="@dimen/relieve_stress_full_stop_x"
            app:onCcClick="onCCMethod_RelievePatternStatusChangeClick"
            app:imageString="@drawable/btnbg_navi_switch_off_n"
            app:hasAlphaMask="true"
            app:leftImageId="@drawable/ic48_system_stop_n"
            app:leftImageWidth="@integer/x_int_32"
            app:leftImageHeight="@integer/y_int_32"
            android:drawablePadding="@dimen/x_px_8"
            android:paddingStart="@dimen/x_px_68"
            android:paddingEnd="@dimen/x_px_68"
            android:paddingTop="@dimen/y_px_27"
            android:paddingBottom="@dimen/y_px_27"
            android:gravity="center"
            android:textSize="@dimen/font_size_28"
            android:textColor="@color/color_text_button"
            app:textString="@string/string_relieve_stress_stop"
            android:contentDescription="@string/string_relieve_stress_stop">
        </com.iauto.uicontrol.ButtonView>

        <com.iauto.uicontrol.ButtonView
            android:id="@+id/button_relieve_stress_full_play"
            android:visibility="@{vm.relieveStressCtrlViewVisibleInPause.intValue()}"
            android:layout_width="@dimen/x_px_232"
            android:layout_height="@dimen/x_px_96"
            android:layout_marginTop="@dimen/relieve_stress_full_play_y"
            android:layout_marginStart="@dimen/relieve_stress_full_play_x"
            app:onCcClick="onCCMethod_RelievePatternStatusChangeClick"
            app:imageString="@drawable/btnbg_navi_switch_off_n"
            app:hasAlphaMask="true"
            app:leftImageId="@drawable/ic48_system_play_n"
            app:leftImageWidth="@integer/x_int_32"
            app:leftImageHeight="@integer/y_int_32"
            android:drawablePadding="@dimen/x_px_8"
            android:paddingLeft="@dimen/x_px_68"
            android:paddingRight="@dimen/x_px_68"
            android:paddingTop="@dimen/y_px_27"
            android:paddingBottom="@dimen/y_px_27"
            android:gravity="center"
            android:textSize="@dimen/font_size_28"
            android:textColor="@color/color_text_button"
            app:textString="@string/string_relieve_stress_play"
            android:contentDescription="@string/string_relieve_stress_play">
        </com.iauto.uicontrol.ButtonView>

        <com.iauto.uicontrol.ButtonView
            android:id="@+id/button_relieve_stress_full_pause"
            android:visibility="@{vm.relieveStressCtrlViewVisibleInPlay.intValue()}"
            android:layout_width="@dimen/x_px_232"
            android:layout_height="@dimen/x_px_96"
            android:layout_marginTop="@dimen/relieve_stress_full_pause_y"
            android:layout_marginStart="@dimen/relieve_stress_full_pause_x"
            app:onCcClick="onCCMethod_RelievePatternStatusChangeClick"
            app:imageString="@drawable/btnbg_navi_switch_off_n"
            app:hasAlphaMask="true"
            app:leftImageId="@drawable/ic48_system_suspend_n"
            app:leftImageWidth="@integer/x_int_32"
            app:leftImageHeight="@integer/y_int_32"
            android:drawablePadding="@dimen/x_px_8"
            android:paddingLeft="@dimen/x_px_68"
            android:paddingRight="@dimen/x_px_68"
            android:paddingTop="@dimen/y_px_27"
            android:paddingBottom="@dimen/y_px_27"
            android:gravity="center"
            android:textSize="@dimen/font_size_28"
            android:textColor="@color/color_text_button"
            app:textString="@string/string_relieve_stress_pause"
            android:contentDescription="@string/string_relieve_stress_pause">
        </com.iauto.uicontrol.ButtonView>

        <com.iauto.uicontrol.ButtonView
            android:id="@+id/button_relieve_stress_full_narrow"
            app:onCcClick="onCCMethod_RelievePatternStatusChangeClick"
            android:visibility="@{vm.relieveStressNarrowVisible.intValue()}"
            android:layout_marginTop="@dimen/relieve_stress_full_narrow_y"
            android:layout_marginStart="@dimen/relieve_stress_full_narrow_x"
            android:layout_width="@dimen/x_px_112"
            android:layout_height="@dimen/y_px_112"
            app:leftImageId="@drawable/ic48_system_putaway_n"
            app:leftImageHeight="@integer/x_int_40"
            app:leftImageWidth="@integer/y_int_40"
            android:paddingLeft="@dimen/x_px_36"
            android:paddingRight="@dimen/x_px_36"
            android:paddingTop="@dimen/y_px_36"
            android:paddingBottom="@dimen/y_px_36"
            android:contentDescription="@string/string_narrow_contentDescription"
            app:hasAlphaMask="true"
            app:isBtnScaleAnim="true"
            app:btnScaleValue="0.95"
            app:imageString="@drawable/btn112_com_circular2_n"
            app:coverImageId="@drawable/com_112x112"/>

        <RelativeLayout
            android:layout_marginStart="@dimen/relieve_stress_full_volume_x"
            android:layout_marginTop="@dimen/relieve_stress_full_volume_y"
            android:layout_width="@dimen/x_px_672"
            android:layout_height="@dimen/y_px_144"
            android:visibility="@{safeUnbox(vm.SettingVolumeViewVisible)}"
            >
            <com.iauto.uicontrol.ImageBase
                android:layout_marginStart="@dimen/x_px_6"
                android:layout_width="@dimen/x_px_666"
                android:layout_height="match_parent"
                app:imageString="@drawable/com_dialogs2"
                >
            </com.iauto.uicontrol.ImageBase>
            <com.iauto.uicontrol.ImageBase
                android:layout_marginStart="@dimen/x_px_74"
                android:layout_marginTop="@dimen/y_px_59"
                android:layout_width="@dimen/x_px_26"
                android:layout_height="@dimen/y_px_26"
                app:imageString="@drawable/ic48_system_soundeffect2_n"
                >
            </com.iauto.uicontrol.ImageBase>
            <com.iauto.uicontrol.ImageBase
                android:layout_marginStart="@dimen/x_px_572"
                android:layout_marginTop="@dimen/y_px_52"
                android:layout_width="@dimen/x_px_40"
                android:layout_height="@dimen/y_px_40"
                app:imageString="@drawable/ic48_system_soundeffect_n"
                >
            </com.iauto.uicontrol.ImageBase>
            <com.iauto.uicontrol.ProgressBar
                android:layout_marginStart="@dimen/x_px_94"
                android:layout_marginTop="@dimen/y_px_20"
                android:layout_width="@dimen/x_px_484"
                android:layout_height="@dimen/y_px_104"
                app:curPercentValue="@{safeUnbox(vm.VolumeValue)}"
                app:totalValue="40"
                app:initialValue="0"
                app:isToneDown="false"
                >
                <com.iauto.uicontrol.ImageBase
                    android:layout_marginStart="@dimen/x_px_30"
                    android:layout_marginTop="@dimen/y_px_48"
                    android:layout_width="@dimen/x_px_424"
                    android:layout_height="@dimen/y_px_8"
                    android:tag="Background"
                    app:imageString="@drawable/bg_setting_progressbg"
                    >
                </com.iauto.uicontrol.ImageBase>
                <com.iauto.uicontrol.ImageBase
                    android:layout_marginStart="@dimen/x_px_30"
                    android:layout_marginTop="@dimen/y_px_48"
                    android:layout_width="@dimen/x_px_424"
                    android:layout_height="@dimen/y_px_8"
                    android:tag="Progress"
                    app:imageString="@drawable/bg_setting_progress"
                    >
                </com.iauto.uicontrol.ImageBase>
                <com.iauto.uicontrol.ImageBase
                    android:layout_marginStart="@dimen/x_px_190"
                    android:layout_marginTop="@dimen/y_px_22"
                    android:layout_width="@dimen/x_px_60"
                    android:layout_height="@dimen/y_px_60"
                    android:tag="Block"
                    app:imageString="@drawable/ic60_setting_slider"
                    >
                </com.iauto.uicontrol.ImageBase>
            </com.iauto.uicontrol.ProgressBar>
        </RelativeLayout>
    </RelativeLayout>
</layout>