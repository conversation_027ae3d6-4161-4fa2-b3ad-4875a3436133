<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/scene_img_community_user_center_bg">
    <!--加载画面-->
    <include
        layout="@layout/scene_layout_comment_req_res"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <!-- 顶部返回    -->
    <ImageView
        android:id="@+id/iv_back_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="76dp"
        android:src="@drawable/scene_icon_common_arrow_left"
        app:layout_constraintBottom_toBottomOf="@+id/tv_back_desc"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_back_desc" />

    <TextView
        android:id="@+id/tv_back_desc"
        android:layout_width="wrap_content"
        android:layout_height="144dp"
        android:layout_marginStart="32dp"
        android:layout_marginTop="@dimen/scene_height_top_status_bar"
        android:gravity="center"
        android:text="@string/scene_text_user_center_my_account_desc"
        android:textColor="@color/scene_color_text_1"
        android:textSize="@dimen/scene_text_size_h3"
        app:layout_constraintStart_toEndOf="@id/iv_back_icon"
        app:layout_constraintTop_toTopOf="parent" />

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:id="@+id/sl_update"
        style="@style/SmartRefreshLayoutTheme"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="76dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_back_desc"
        app:srlEnableNestedScrolling="true">

        <com.dfl.smartscene.widget.ClassicsHeader4All
            android:id="@+id/header"
            style="@style/SmartRefreshClassicsTheme" />

        <com.dfl.android.animationlib.scrollView.bar.nestedscrollview.NestedScrollViewFrameLayout
            style="@style/OverScrollViewTheme1A"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never">

            <com.dfl.android.animationlib.scrollView.bar.nestedscrollview.NothingNestedScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/coordinator_userinfo"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <!--用户头像-->
                    <ImageView
                        android:id="@+id/iv_user_avatar"
                        android:layout_width="180dp"
                        android:layout_height="180dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <!--用户名-->
                    <TextView
                        android:id="@+id/tv_user_name"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/scene_height_text_size_h1"
                        android:layout_marginTop="20dp"
                        android:gravity="center"
                        android:textColor="@color/scene_color_text_1"
                        android:textSize="@dimen/scene_text_size_h1"
                        app:layout_constraintEnd_toEndOf="@+id/iv_user_avatar"
                        app:layout_constraintStart_toStartOf="@+id/iv_user_avatar"
                        app:layout_constraintTop_toBottomOf="@+id/iv_user_avatar"
                        tools:text="用户名" />

                    <!--用户信息-->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_userinfo_list"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:layout_marginEnd="-80dp"
                        android:overScrollMode="never"
                        app:layout_constraintEnd_toEndOf="@+id/iv_user_avatar"
                        app:layout_constraintStart_toStartOf="@+id/iv_user_avatar"
                        app:layout_constraintTop_toBottomOf="@+id/tv_user_name" />
                    <!--关注按钮-->
                    <com.dfl.android.animationlib.ScaleImageButton
                        android:id="@+id/tv_community_subscribe"
                        style="@style/PrimaryBigBtnColorStyle.SubScribeBtnStyle"
                        android:layout_marginTop="20dp"
                        app:layout_constraintEnd_toEndOf="@+id/iv_user_avatar"
                        app:layout_constraintStart_toStartOf="@+id/iv_user_avatar"
                        app:layout_constraintTop_toBottomOf="@+id/rv_userinfo_list" />
                    <!--已关注按钮-->
                    <com.dfl.android.animationlib.ScaleImageButton
                        android:id="@+id/tv_community_subscribed"
                        style="@style/SecondaryBigBtnColorStyle.SubScribedBtnStyle"
                        android:layout_marginTop="20dp"
                        android:visibility="invisible"
                        app:layout_constraintEnd_toEndOf="@+id/iv_user_avatar"
                        app:layout_constraintStart_toStartOf="@+id/iv_user_avatar"
                        app:layout_constraintTop_toBottomOf="@+id/rv_userinfo_list" />

                    <!--发布场景-->
                    <TextView
                        android:id="@+id/tv_user_uploads_num"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/scene_height_text_size_h4"
                        android:layout_marginTop="60dp"
                        android:text="@string/scene_text_user_center_my_uploads_num"
                        android:textColor="@color/scene_color_text_3"
                        android:textSize="@dimen/scene_text_size_h4"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_community_subscribe" />

                    <!--发布场景列表-->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_user_uploads_list"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="32dp"
                        android:nestedScrollingEnabled="false"
                        android:overScrollMode="never"
                        app:layout_constraintTop_toBottomOf="@+id/tv_user_uploads_num" />

                    <!--发布场景列表为空-->
                    <ImageView
                        android:id="@+id/iv_upload_empty"
                        android:layout_width="@dimen/scene_width_default_icon"
                        android:layout_height="@dimen/scene_height_default_icon"
                        android:layout_marginTop="96dp"
                        android:src="@drawable/scene_img_community_usercenter_empty_list"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_user_uploads_num" />

                    <TextView
                        android:id="@+id/tv_empty_upload_desc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:text="@string/scene_text_user_center_empty_upload_desc"
                        android:textColor="@color/scene_color_text_3"
                        android:textSize="@dimen/scene_text_size_h4"
                        app:layout_constraintEnd_toEndOf="@+id/iv_upload_empty"
                        app:layout_constraintStart_toStartOf="@id/iv_upload_empty"
                        app:layout_constraintTop_toBottomOf="@id/iv_upload_empty"
                        tools:text="@string/scene_text_user_center_empty_upload_desc" />
                    <!--不再关注菜单 放在这防止被其他组件覆盖-->
                    <LinearLayout
                        android:id="@+id/view_pop"
                        android:layout_width="313dp"
                        android:layout_height="258dp"
                        android:layout_marginTop="16dp"
                        android:background="@drawable/scene_shape_community_bg_4_r24"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:paddingHorizontal="32dp"
                        android:paddingVertical="20dp"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="@id/tv_community_subscribe"
                        app:layout_constraintStart_toStartOf="@id/tv_community_subscribe"
                        app:layout_constraintTop_toBottomOf="@id/tv_community_subscribe">

                        <TextView
                            android:id="@+id/tv_no_longer_sub"
                            android:layout_width="match_parent"
                            android:layout_height="108dp"
                            android:gravity="center"
                            android:text="@string/scene_text_community_no_longer_subscribes"
                            android:textColor="@color/scene_color_text_1"
                            android:textSize="@dimen/scene_text_size_h4"
                            tools:ignore="SpUsage" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="2dp"
                            android:background="@color/scene_color_divider" />

                        <TextView
                            android:id="@+id/tv_no_longer_sub_cancel"
                            android:layout_width="match_parent"
                            android:layout_height="108dp"
                            android:gravity="center"
                            android:text="@string/scene_text_common_cancel"
                            android:textColor="@color/scene_color_text_1"
                            android:textSize="@dimen/scene_text_size_h4"
                            tools:ignore="SpUsage" />
                    </LinearLayout>

                    <androidx.constraintlayout.widget.Group
                        android:id="@+id/group_empty_upload_show"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        app:constraint_referenced_ids="tv_empty_upload_desc,iv_upload_empty" />

                </androidx.constraintlayout.widget.ConstraintLayout>
            </com.dfl.android.animationlib.scrollView.bar.nestedscrollview.NothingNestedScrollView>
        </com.dfl.android.animationlib.scrollView.bar.nestedscrollview.NestedScrollViewFrameLayout>

        <com.dfl.smartscene.widget.ClassicsFooter4All
            android:id="@+id/classicsFooter"
            style="@style/SmartRefreshClassicsTheme.FooterTheme" />
    </com.scwang.smartrefresh.layout.SmartRefreshLayout>

</androidx.constraintlayout.widget.ConstraintLayout>