<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.iauto.uicontrol.ImageBase
            android:layout_width="@dimen/x_px_1488"
            android:layout_height="@dimen/y_px_612"
            android:layout_centerHorizontal="true"
            app:imageString="@drawable/com_boder"/>

        <RelativeLayout
            android:id="@+id/fragment_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="0px" />

        <com.iauto.uicontrol.ButtonView
            android:id="@+id/button_base_close"
            android:layout_width="@dimen/x_px_72"
            android:layout_height="@dimen/y_px_72"
            android:layout_marginStart="@dimen/x_px_24"
            android:layout_marginTop="@dimen/y_px_8"
            android:contentDescription="@string/string_close_contentDescription"
            android:paddingStart="@dimen/x_px_16"
            android:paddingTop="@dimen/y_px_16"
            android:paddingEnd="@dimen/x_px_16"
            android:paddingBottom="@dimen/y_px_16"
            app:hasAlphaMask="true"
            app:isBtnScaleAnim="true"
            app:btnScaleValue="0.95"
            app:leftImageHeight="@integer/y_int_40"
            app:leftImageId="@drawable/ic48_system_closebig_n"
            app:leftImageWidth="@integer/x_int_40"
            >
        </com.iauto.uicontrol.ButtonView>

        <com.iauto.uicontrol.ButtonView
            android:id="@+id/button_surprise_egg_list_back"
            android:layout_width="@dimen/x_px_72"
            android:layout_height="@dimen/y_px_72"
            android:layout_marginStart="@dimen/x_px_152"
            android:layout_marginTop="@dimen/y_px_8"
            android:contentDescription="@string/string_back_contentDescription"
            android:paddingStart="@dimen/x_px_16"
            android:paddingTop="@dimen/y_px_16"
            android:paddingEnd="@dimen/x_px_16"
            android:paddingBottom="@dimen/y_px_16"
            app:hasAlphaMask="true"
            app:isBtnScaleAnim="true"
            app:btnScaleValue="0.95"
            app:leftImageHeight="@integer/y_int_40"
            app:leftImageId="@drawable/ic48_system_back_n"
            app:leftImageWidth="@integer/x_int_40">
        </com.iauto.uicontrol.ButtonView>

    </RelativeLayout>
</layout>