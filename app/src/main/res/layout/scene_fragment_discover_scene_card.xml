<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/srl_discover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/px_36"
            android:layout_marginTop="@dimen/px_36"
            android:layout_marginEnd="@dimen/px_36">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_card_list"
                style="@style/ScrollBarStyle"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="@dimen/px_52" />

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


</layout>