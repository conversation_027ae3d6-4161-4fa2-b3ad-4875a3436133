<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/SmallDialogTheme"
        android:layout_marginTop="76dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_title"
            style="@style/SmallDialogTitleTheme"
            android:text="@string/scene_text_action_initiate_navigation"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_navigation_action_title_desc"
            style="@style/SmallDialogSubTitleTheme"
            android:text="@string/scene_text_start_navi_title_desc"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@id/tv_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />


        <RelativeLayout
            android:id="@+id/rl_ctv_go_home"
            android:layout_width="406dp"
            android:layout_height="112dp"
            android:layout_marginStart="64dp"
            android:layout_marginTop="34dp"
            android:background="@drawable/scene_shape_common_bg_2_radius_large"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title">

            <RadioButton
                android:id="@+id/ctv_go_home"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="48dp"
                android:button="@drawable/scene_selector_me_radio_check_style"
                android:clickable="false"
                android:paddingHorizontal="20dp"
                android:text="@string/scene_text_action_go_home"
                android:textColor="@color/scene_color_text_1"
                android:textSize="@dimen/scene_text_size_h4" />
        </RelativeLayout>


        <RelativeLayout
            android:id="@+id/rl_ctv_go_company"
            android:layout_width="406dp"
            android:layout_height="112dp"
            android:layout_marginStart="32dp"
            android:background="@drawable/scene_shape_common_bg_2_radius_large"
            app:layout_constraintStart_toEndOf="@id/rl_ctv_go_home"
            app:layout_constraintTop_toTopOf="@id/rl_ctv_go_home">

            <RadioButton
                android:id="@+id/ctv_go_company"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="48dp"
                android:button="@drawable/scene_selector_me_radio_check_style"
                android:clickable="false"
                android:paddingHorizontal="20dp"
                android:text="@string/scene_text_action_back_to_company"
                android:textColor="@color/scene_color_text_1"
                android:textSize="@dimen/scene_text_size_h4" />
        </RelativeLayout>


        <RelativeLayout
            android:id="@+id/rl_ctv_go_some_where"
            android:layout_width="0dp"
            android:layout_height="112dp"
            android:layout_marginTop="32dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/scene_shape_common_bg_2_radius_large"
            app:layout_constraintBottom_toTopOf="@id/include_dialog_bottom"
            app:layout_constraintEnd_toEndOf="@id/rl_ctv_go_company"
            app:layout_constraintStart_toStartOf="@id/rl_ctv_go_home"
            app:layout_constraintTop_toBottomOf="@id/rl_ctv_go_home">

            <RadioButton
                android:id="@+id/ctv_go_some_where"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="48dp"
                android:button="@drawable/scene_selector_me_radio_check_style"
                android:clickable="false"
                android:paddingHorizontal="20dp"
                android:text="@string/scene_text_action_go_somewhere"
                android:textColor="@color/scene_color_text_1"
                android:textSize="@dimen/scene_text_size_h4" />

            <View
                android:id="@+id/view_more"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_alignParentTop="true"
                android:layout_alignParentEnd="true"
                android:layout_margin="16dp"
                android:background="@drawable/scene_icon_radio_check_more" />

        </RelativeLayout>


        <include
            android:id="@+id/include_dialog_bottom"
            layout="@layout/scene_layout_dialog_bottom_button"
            android:layout_width="match_parent"
            android:layout_height="@dimen/scene_height_dialog_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>