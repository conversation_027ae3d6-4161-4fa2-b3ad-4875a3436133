<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/SmallDialogThemeP42R"
        android:layout_height="@dimen/px_502"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_title"
            style="@style/SmallDialogTitleThemeP42R"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="标题" />

        <TextView
            android:id="@+id/tv_desc"
            style="@style/SmallDialogSubTitleThemeP42R"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/tv_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="=====标题描述=====" />
        <!--        两条灰线-->
        <View
            android:id="@+id/view2"
            style="@style/WheelViewSelectViewTheme"
            android:layout_width="@dimen/px_616"
            android:layout_height="@dimen/px_94"
            app:layout_constraintBottom_toBottomOf="@id/wheel_left"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/wheel_left" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_begin="@dimen/px_180" />
        <!-- 3d滚轮设置高度不起作用会根据设置到itemHeight重绘,例如itemHeight=96,整体高度为244,手动设置间距-->
        <com.dfl.smartscene.widget.wheel.WheelView
            android:id="@+id/wheel_left"
            style="@style/WheelViewFiveItemStyle"
            android:layout_width="@dimen/px_313"
            android:gravity="center"
            app:layout_constraintBottom_toTopOf="@+id/include_dialog_bottom"
            app:layout_constraintEnd_toStartOf="@id/wheel_right"
            app:layout_constraintStart_toStartOf="@id/guideline1"
            app:layout_constraintTop_toBottomOf="@+id/tv_title" />

        <com.dfl.smartscene.widget.wheel.WheelView
            android:id="@+id/wheel_right"
            style="@style/WheelViewFiveItemStyle"
            android:layout_width="@dimen/px_313"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="@id/wheel_left"
            app:layout_constraintEnd_toEndOf="@id/guideline2"
            app:layout_constraintStart_toEndOf="@id/wheel_left"
            app:layout_constraintTop_toTopOf="@id/wheel_left" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_end="@dimen/px_169" />

        <include
            android:id="@+id/include_dialog_bottom"
            layout="@layout/scene_layout_dialog_bottom_button"
            android:layout_width="match_parent"
            android:layout_height="@dimen/scene_height_dialog_bottom_p42r"
            android:layout_marginTop="@dimen/px_28"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>