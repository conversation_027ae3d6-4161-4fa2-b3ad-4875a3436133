<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="@dimen/y_px_104"
    android:layout_width="@dimen/x_px_210"
    android:clipChildren="false"
    android:clipToPadding="false">

    <com.dfl.dflcommonlibs.uimodeutil.UIImageView
        android:id="@+id/imageview_item_ope_recycler_shade"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:src="@drawable/bg_select_route"
        />

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:id="@+id/textview_item_ope_recycler"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        android:textColor="@color/color_text_first_label"
        android:textSize="@dimen/y_px_text_subtitle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>