<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/px_278"
    android:layout_height="@dimen/px_162"
    android:layout_marginHorizontal="@dimen/px_24"
    android:background="@drawable/scene_shape_detail_condition_able_r16">

    <!--卡片背景-->
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_detail_action_item"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <TextView
        android:id="@+id/tv_detail_action_item_serial_no"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/px_46"
        android:layout_marginStart="@dimen/px_18"
        android:layout_marginTop="@dimen/px_15"
        android:gravity="center"
        android:textColor="@color/scene_color_text_4"
        android:textSize="@dimen/scene_text_size_p42r_h3"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="1" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/btn_action_item_remove"
        android:layout_width="@dimen/px_42"
        android:layout_height="@dimen/px_42"
        android:layout_marginTop="@dimen/px_15"
        android:layout_marginEnd="@dimen/px_16"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/scene_icon_me_condition_delete" />

    <TextView
        android:id="@+id/tv_detail_action_item_name"
        android:layout_width="0dp"
        android:layout_height="@dimen/px_40"
        android:layout_marginStart="@dimen/px_18"
        android:ellipsize="end"
        android:gravity="start|center_vertical"
        android:lines="1"
        android:maxLines="1"
        android:textColor="@color/scene_color_text_1"
        android:textSize="@dimen/scene_text_size_p42r_h3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_detail_action_item_serial_no"
        tools:text="大灯打招呼灯语" />

    <TextView
        android:id="@+id/tv_detail_action_item_desc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/px_18"
        android:layout_marginTop="@dimen/px_8"
        android:layout_marginEnd="@dimen/px_68"
        android:ellipsize="end"
        android:gravity="center_vertical|start"
        android:maxLength="20"
        android:maxLines="1"
        android:textColor="@color/scene_color_text_3"
        android:textSize="@dimen/scene_text_size_p42r_h6"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_detail_action_item_name"
        tools:text="智能推荐智能推荐智能推荐智能推荐智能推荐" />

    <!--    颜色块放在中间就行了-->
    <View
        android:id="@+id/v_ambient_light_bg"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginStart="15dp"
        android:background="@color/scene_mean_color_success"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tv_detail_action_item_desc"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_detail_action_item_desc" />

    <TextView
        android:id="@+id/iv_condition_action_no_active"
        android:layout_width="205dp"
        android:layout_height="48dp"
        android:background="@drawable/scene_shape_detail_adapter_no_active"
        android:gravity="center"
        android:text="@string/scene_text_condition_action_no_active"
        android:visibility="gone"
        android:textColor="@color/scene_color_text_4"
        android:textSize="@dimen/scene_text_size_h7"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>