<?xml version="1.0" encoding="utf-8"?><!--发起导航和位置合并在一个xml-->
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/BigDialogThemeP42R"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_title"
            style="@style/SmallDialogTitleThemeP42R"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="标题" />

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/px_40"
            android:layout_height="@dimen/px_40"
            android:layout_marginStart="@dimen/px_60"
            android:layout_marginTop="@dimen/px_32"
            android:scaleType="center"
            android:src="@drawable/scene_icon_common_arrow_left"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:ignore="RtlSymmetry" />

        <TextView
            android:id="@+id/txt_radius"
            android:layout_width="@dimen/px_248"
            android:layout_height="@dimen/px_68"
            android:gravity="center"
            android:background="@drawable/scene_shape_common_bg_2_r12"
            android:textColor="@color/scene_color_text_1"
            android:textFontWeight="400"
            android:textSize="@dimen/scene_text_size_h7"
            android:visibility="gone"
            android:layout_marginEnd="@dimen/px_18"
            app:layout_constraintBottom_toBottomOf="@id/tv_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_title"
            tools:text="范围半径  100m" />
        <View
            android:id="@+id/view_more"
            android:layout_width="@dimen/px_24"
            android:layout_height="@dimen/px_24"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_margin="@dimen/px_12"
            app:layout_constraintTop_toTopOf="@+id/txt_radius"
            app:layout_constraintEnd_toEndOf="@+id/txt_radius"
            android:background="@drawable/scene_icon_radio_check_more"
            android:visibility="gone" />

        <!--        目标地-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_end_address"
            android:layout_width="@dimen/px_164"
            android:layout_height="@dimen/px_68"
            android:layout_marginHorizontal="@dimen/px_16"
            android:background="@drawable/scene_shape_common_bg_2_r12"
            android:layout_marginTop="@dimen/px_16"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_title">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_close_end_address"
                android:layout_width="@dimen/px_40"
                android:layout_height="@dimen/px_40"
                android:layout_marginStart="@dimen/px_40"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/ll_end_address"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/scene_icon_common_clear_text1_2a" />

            <LinearLayout
                android:id="@+id/ll_end_address"
                android:layout_width="@dimen/px_164"
                android:layout_height="@dimen/px_68"
                android:background="@drawable/scene_shape_common_bg_2_r12"
                android:gravity="center_vertical"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/iv_input_end"
                    android:layout_width="@dimen/px_32"
                    android:layout_height="@dimen/px_32"
                    android:layout_marginStart="@dimen/px_34"
                    android:src="@drawable/scene_icon_commom_navigation_search"
                    android:visibility="visible" />

                <com.dfl.smartscene.widget.ClearEditText
                    android:id="@+id/et_edit_input_end"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/px_68"
                    android:background="@null"
                    android:cursorVisible="true"
                    android:hint="@string/scene_text_select_points"
                    android:paddingStart="@dimen/px_16"
                    android:gravity="center_vertical"
                    android:singleLine="true"
                    android:textColor="@color/scene_color_text_1"
                    android:textColorHint="@color/scene_color_text_1"
                    android:textCursorDrawable="@drawable/scene_shape_edit_cursor_bg"
                    android:textSize="@dimen/scene_text_size_h7" />

            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--        途经点按钮-->
        <TextView
            android:id="@+id/tv_mid_address"
            android:layout_width="1028dp"
            android:layout_height="@dimen/px_76"
            android:layout_marginTop="@dimen/px_20"
            android:background="@drawable/scene_shape_common_bg_2_r12"
            android:drawableStart="@drawable/scene_icon_me_action_navigation_gowhere_wayside"
            android:drawablePadding="@dimen/px_20"
            android:paddingVertical="@dimen/px_18"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="@dimen/px_36"
            android:text="@string/scene_edit_action_add_wayside_address"
            android:textColor="@color/scene_primary_color_highlight"
            android:textSize="@dimen/scene_text_size_p42r_h6"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@id/cl_end_address"
            app:layout_constraintStart_toStartOf="@id/cl_end_address"
            app:layout_constraintTop_toBottomOf="@id/cl_end_address" />

        <!--        途经点-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_mid_address"
            android:layout_width="1028dp"
            android:layout_height="@dimen/px_76"
            android:layout_marginTop="@dimen/px_21"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@id/cl_end_address"
            app:layout_constraintStart_toStartOf="@id/cl_end_address"
            app:layout_constraintTop_toBottomOf="@id/cl_end_address">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_wayside_address_close"
                android:layout_width="@dimen/px_40"
                android:layout_height="@dimen/px_40"
                android:layout_marginStart="@dimen/px_40"
                android:src="@drawable/scene_icon_common_clear_text1_2a"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
<!--            @drawable/scene_shape_common_bg_2_r12-->
            <LinearLayout
                android:layout_width="944dp"
                android:layout_height="@dimen/px_76"
                android:layout_marginEnd="@dimen/px_40"
                android:background="@drawable/scene_shape_common_bg_2_r12"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="@dimen/px_36"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/iv_wayside_address_close"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:layout_width="@dimen/px_32"
                    android:layout_height="@dimen/px_32"
                    android:src="@drawable/scene_icon_me_action_navigation_gowhere_wayside" />

                <com.dfl.smartscene.widget.ClearEditText
                    android:id="@+id/et_edit_input_mid"
                    android:layout_width="944dp"
                    android:layout_height="match_parent"
                    android:background="@null"
                    android:cursorVisible="true"
                    android:hint="@string/scene_edit_input_hint_wayside_address"
                    android:maxLength="30"
                    android:paddingStart="20dp"
                    android:paddingEnd="@dimen/px_110"
                    android:singleLine="true"
                    android:textColor="@color/scene_color_text_1"
                    android:textColorHint="@color/scene_color_text_3"
                    android:textCursorDrawable="@drawable/scene_shape_edit_cursor_bg"
                    android:textSize="@dimen/scene_text_size_p42r_h6" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--        收藏夹文字-->
        <LinearLayout
            android:id="@+id/ll_txt_favorites"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="60dp"
            app:layout_constraintStart_toStartOf="@id/cl_end_address"
            app:layout_constraintTop_toBottomOf="@id/cl_end_address">

            <TextView
                android:id="@+id/txt_favorites"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:layout_marginBottom="28dp"
                android:gravity="center"
                android:text="@string/scene_text_favorites"
                android:textColor="@color/scene_color_text_3"
                android:textSize="@dimen/scene_text_size_h5"
                android:visibility="gone" />

        </LinearLayout>

        <!--        收藏夹列表-->
        <com.dfl.android.animationlib.scrollView.FadeRecyclerView
            android:id="@+id/rv_favorites"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginHorizontal="@dimen/px_46"
            android:overScrollMode="never"
            app:layout_constraintBottom_toTopOf="@id/include_dialog_bottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />
        <!--        搜索列表-->
        <com.dfl.android.animationlib.scrollView.FadeRecyclerView
            android:id="@+id/rv_search"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="294dp"
            android:layout_marginBottom="16dp"
            android:background="@color/scene_color_medal_bg"
            android:overScrollMode="never"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/include_dialog_bottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/cl_end_address"
            app:layout_constraintTop_toTopOf="parent" />

        <include
            android:layout_marginTop="@dimen/px_8"
            android:id="@+id/include_dialog_bottom"
            layout="@layout/scene_layout_dialog_bottom_flat"
            android:layout_width="match_parent"
            android:layout_height="@dimen/scene_height_dialog_bottom_p42r"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>