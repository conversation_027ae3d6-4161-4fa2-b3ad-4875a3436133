<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="1336dp"
        android:layout_height="136dp"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="135dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_song_or_musician_name"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="48dp"
                android:ellipsize="end"
                android:gravity="start|center_vertical"
                android:singleLine="true"
                android:textColor="@color/scene_color_text_1"
                android:textSize="@dimen/scene_text_size_h4"
                tools:text="广东省\t广州市花都区" />

            <TextView
                android:id="@+id/tv_song_detail_item"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="20dp"
                android:ellipsize="end"
                android:gravity="start|center_vertical"
                android:singleLine="true"
                android:textColor="@color/scene_color_text_3"
                android:textSize="@dimen/scene_text_size_h6"
                tools:text="风神大道100086号" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginHorizontal="39dp"
            android:background="#14000000" />
    </LinearLayout>

</layout>