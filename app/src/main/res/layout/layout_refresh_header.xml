<?xml version="1.0" encoding="utf-8" standalone="no"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingTop="@dimen/y_px_24"
    android:paddingBottom="@dimen/y_px_24"
    android:gravity="center">

    <include
        layout="@layout/layout_small_loading_anim"
        android:layout_width="@dimen/x_px_48"
        android:layout_height="@dimen/y_px_48" />

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:id="@+id/memo_text_view"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/y_px_48"
        android:gravity="center"
        android:text="正在加载..."
        android:textColor="@color/color_text_unselected"
        android:textSize="@dimen/font_size_24"
        android:layout_marginLeft="@dimen/x_px_10" />

</LinearLayout>