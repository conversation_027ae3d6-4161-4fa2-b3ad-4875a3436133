<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <variable name="vm" type="com.dfl.smartscene.ccs.viewmodel.SurpriseEggPlayVM"/>
    </data>
    <RelativeLayout
        xmlns:app = "http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        tools:context=".SurpriseEggPlayView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="0px"
        android:layout_marginTop="0px"
        android:id="@+id/layout_webview">

        <com.iauto.uicontrol.ButtonView
            android:id="@+id/button_base_close"
            android:layout_width="@dimen/x_px_64"
            android:layout_height="@dimen/y_px_64"
            android:layout_marginStart="@dimen/x_px_64"
            android:layout_marginTop="@dimen/y_px_608"
            android:contentDescription="@string/string_narrow_contentDescription_egg"
            app:hasAlphaMask="true"
            app:isBtnScaleAnim="true"
            app:btnScaleValue="0.95"
            app:imageString="@drawable/icon_close_surprise_egg_play"
            app:onCcClick="onCCMethod_Close"
            >
        </com.iauto.uicontrol.ButtonView>
    </RelativeLayout>
</layout>