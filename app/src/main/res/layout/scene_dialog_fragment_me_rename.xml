<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_dialog_container"
        style="@style/SmallDialogThemeP42R"
        android:layout_height="@dimen/px_264"
        android:layout_marginTop="@dimen/px_211"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_rename_title"
            style="@style/SmallDialogTitleThemeP42R"
            android:text="@string/scene_text_new_edit_title_scene_name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_rename_close"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginStart="@dimen/px_60"
            android:src="@drawable/scene_icon_common_close"
            app:layout_constraintBottom_toBottomOf="@id/tv_rename_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_rename_title" />

        <!--        <com.dfl.android.animationlib.ShakingEditView-->
        <!--            android:id="@+id/edt_input_area"-->
        <!--            style="@style/ShakingEditViewTheme"-->
        <!--            android:layout_width="844dp"-->
        <!--            android:layout_height="192dp"-->
        <!--            android:layout_marginTop="16dp"-->
        <!--            app:hint="@string/scene_text_edit_scene_name_hint"-->
        <!--            app:layout_constraintEnd_toEndOf="parent"-->
        <!--            app:layout_constraintStart_toStartOf="parent"-->
        <!--            app:layout_constraintTop_toBottomOf="@+id/tv_rename_title"-->
        <!--            app:tipText="@string/scene_edit_input_hint_short_text_tips" />-->

        <com.dfl.smartscene.widget.ClearEditText
            android:id="@+id/edt_input_area"
            android:layout_width="@dimen/px_616"
            android:layout_height="@dimen/px_96"
            android:layout_marginTop="@dimen/px_12"
            android:background="@drawable/scene_shape_edit_address_bg_r12"
            android:cursorVisible="true"
            android:hint="@string/scene_text_edit_scene_name_hint"
            android:maxLength="10"
            android:paddingHorizontal="@dimen/px_36"
            android:singleLine="true"
            android:textColor="@color/scene_color_text_1"
            android:textColorHint="@color/scene_color_text_3"
            android:textCursorDrawable="@drawable/scene_shape_edit_cursor_bg"
            android:textSize="@dimen/scene_text_size_p42r_h6"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_rename_title" />

        <TextView
            android:id="@+id/tv_check_msg"
            android:layout_width="0dp"
            android:layout_height="@dimen/px_34"
            android:layout_marginTop="@dimen/px_4"
            android:gravity="center_vertical|start"
            android:textColor="@color/scene_color_text_3"
            android:textSize="@dimen/scene_text_size_h8"
            app:layout_constraintEnd_toEndOf="@id/edt_input_area"
            app:layout_constraintStart_toStartOf="@id/edt_input_area"
            app:layout_constraintTop_toBottomOf="@id/edt_input_area"
            tools:text="@string/scene_edit_input_hint_short_text_tips" />


        <!--        <include-->
        <!--            android:id="@+id/include_dialog_bottom"-->
        <!--            layout="@layout/scene_layout_dialog_bottom_button"-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="@dimen/scene_height_dialog_bottom"-->
        <!--            app:layout_constraintBottom_toBottomOf="parent"-->
        <!--            app:layout_constraintEnd_toEndOf="parent"-->
        <!--            app:layout_constraintStart_toStartOf="parent" />-->

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>