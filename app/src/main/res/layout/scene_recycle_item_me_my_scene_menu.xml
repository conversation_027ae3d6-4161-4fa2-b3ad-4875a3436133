<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/ll_menu_item_container"
        android:layout_width="match_parent"
        android:layout_height="@dimen/px_48"
        android:orientation="horizontal"
        android:gravity="center">

        <ImageView
            android:id="@+id/iv_menu_icon"
            android:layout_width="@dimen/px_28"
            android:layout_height="@dimen/px_28"
            android:src="@drawable/scene_img_main_garbagecan"
            android:layout_marginEnd="@dimen/px_12"
            android:scaleType="centerInside" />

        <TextView
            android:id="@+id/tv_my_scene_menu_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/scene_color_text_1"
            android:textSize="@dimen/scene_text_size_h8"
            tools:text="删除" />

    </LinearLayout>

</LinearLayout>