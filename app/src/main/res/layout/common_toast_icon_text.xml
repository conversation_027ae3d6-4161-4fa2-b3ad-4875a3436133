<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/com_toastbg"
    android:gravity="center_vertical"
    android:paddingStart="@dimen/x_px_94"
    android:paddingEnd="@dimen/x_px_94">

    <com.iauto.uicontrol.ImageBase
        android:id="@+id/toast_icon"
        android:layout_width="@dimen/x_px_48"
        android:layout_height="@dimen/y_px_48" />

    <com.iauto.uicontrol.TextBase
        android:id="@+id/message"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/y_px_42"
        android:layout_marginStart="@dimen/x_px_24"
        android:gravity="start"
        android:textColor="@color/scene_primary_text_normal"
        android:textSize="@dimen/font_size_28" />

</LinearLayout>