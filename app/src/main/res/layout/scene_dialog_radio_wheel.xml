<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            style="@style/SmallDialogTheme"
            android:layout_marginTop="76dp"
            android:minHeight="824dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:id="@+id/ll_top_title"
                style="@style/SmallDialogTitleTheme"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tv_title"
                    style="@style/SmallDialogTitleTheme"
                    android:layout_height="wrap_content"
                    tools:text="XXXX" />

                <TextView
                    android:id="@+id/tv_desc"
                    style="@style/SmallDialogSubTitleTheme"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    tools:text="=====标题描述=====" />
            </LinearLayout>


            <com.dfl.android.animationlib.TabSwitchView
                android:id="@+id/tab_switch_view_condition"
                style="@style/TabSwitchViewHorizontalTheme"
                app:itemTotality="2"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ll_top_title" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/ll_range_wheel"
                style="@style/WheelViewWithTabLinearLayoutTheme"
                android:layout_height="300dp"
                android:layout_marginTop="32dp"
                android:layout_marginBottom="28dp"
                app:layout_constraintBottom_toTopOf="@+id/include_dialog_bottom"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tab_switch_view_condition">

                <View
                    style="@style/WheelViewSelectViewTheme"
                    android:foregroundGravity="center_vertical"
                    app:layout_constraintBottom_toBottomOf="@+id/ll_range_wheel"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/ll_range_wheel" />


                <com.dfl.smartscene.widget.wheel.WheelView
                    android:id="@+id/wheel"
                    style="@style/WheelViewStyleWidth90Theme"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_unit"
                    style="@style/WheelViewTextTheme"
                    android:layout_marginStart="180dp"
                    android:text="@string/scene_text_common_temperature_unit"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/wheel"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/wheel" />

            </androidx.constraintlayout.widget.ConstraintLayout>
            <!--滚轮渐变蒙层-->
            <View
                android:id="@+id/date_wheel_mask"
                android:layout_width="844dp"
                android:layout_height="300dp"
                android:layout_marginTop="32dp"
                android:layout_marginBottom="28dp"
                android:background="@drawable/scene_layer_common_pop_wheel_mask"
                android:foregroundGravity="center_horizontal"
                app:layout_constraintBottom_toTopOf="@id/include_dialog_bottom"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tab_switch_view_condition" />

            <include
                android:id="@+id/include_dialog_bottom"
                layout="@layout/scene_layout_dialog_bottom_button"
                android:layout_width="match_parent"
                android:layout_height="@dimen/scene_height_dialog_bottom"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>