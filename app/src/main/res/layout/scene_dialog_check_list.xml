<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/SmallDialogTheme"
        android:layout_height="748dp"
        android:layout_marginTop="76dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_check_list_title"
            style="@style/SmallDialogTitleTheme"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="挡位" />

        <TextView
            android:id="@+id/tv_desc"
            style="@style/SmallDialogSubTitleTheme"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/tv_check_list_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="----标题描述----" />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_check_list"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="212dp"
            android:overScrollMode="never"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_check_list_title" />

        <include
            android:id="@+id/include_dialog_bottom"
            layout="@layout/scene_layout_dialog_bottom_button"
            android:layout_width="match_parent"
            android:layout_height="@dimen/scene_height_dialog_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>