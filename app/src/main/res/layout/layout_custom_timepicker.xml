<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <com.iauto.uicontrol.ImageBase
        android:id="@+id/numberpicker_layout_custom_number_compare_condition_time_bg_1"
        android:layout_width="@dimen/x_px_280"
        android:layout_height="@dimen/y_px_86"
        android:background="@drawable/number_picaker_system_popup_selectbg_s"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintEnd_toStartOf="@id/numberpicker_layout_custom_number_compare_condition_time_bg_2"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <com.iauto.uicontrol.ImageBase
        android:id="@+id/numberpicker_layout_custom_number_compare_condition_time_bg_2"
        android:layout_width="@dimen/x_px_280"
        android:layout_height="@dimen/y_px_86"
        android:background="@drawable/number_picaker_system_popup_selectbg_s"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/numberpicker_layout_custom_number_compare_condition_time_bg_1"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />


    <com.dfl.smartscene.ccs.view.weight.CustomNumberPicker
        android:id="@+id/numberpicker_layout_custom_state_time_clock_hour_first"
        android:layout_width="@dimen/x_px_100"
        android:layout_height="@dimen/y_px_270"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toStartOf="@id/textview_layout_custom_state_time_clock_value_unit_1"
        android:descendantFocusability="blocksDescendants"
        app:layout_constraintStart_toStartOf="@id/numberpicker_layout_custom_number_compare_condition_time_bg_1" />

    <com.iauto.uicontrol.TextBase
        android:id="@+id/textview_layout_custom_state_time_clock_value_unit_1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="10dp"
        android:gravity="center"
        android:text="@string/string_layout_custom_state_time_clock_hour"
        android:textColor="@color/color_text_tertiary_label"
        android:textSize="@dimen/font_size_28"
        app:layout_constraintBottom_toBottomOf="@id/numberpicker_layout_custom_state_time_clock_hour_first"
        app:layout_constraintStart_toEndOf="@id/numberpicker_layout_custom_state_time_clock_hour_first"
        app:layout_constraintTop_toTopOf="@id/numberpicker_layout_custom_state_time_clock_hour_first"
        app:layout_constraintEnd_toEndOf="@id/numberpicker_layout_custom_number_compare_condition_time_bg_1"/>

    <com.dfl.smartscene.ccs.view.weight.CustomNumberPicker
        android:id="@+id/numberpicker_layout_custom_state_time_clock_minute_first"
        android:layout_width="@dimen/x_px_100"
        android:layout_height="@dimen/y_px_270"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toStartOf="@id/textview_layout_custom_state_time_clock_value_unit_2"
        android:descendantFocusability="blocksDescendants"
        app:layout_constraintStart_toStartOf="@id/numberpicker_layout_custom_number_compare_condition_time_bg_2" />

    <com.iauto.uicontrol.TextBase
        android:id="@+id/textview_layout_custom_state_time_clock_value_unit_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/string_layout_custom_state_time_clock_minute"
        android:textColor="@color/color_text_tertiary_label"
        android:textSize="@dimen/font_size_28"
        app:layout_constraintBottom_toBottomOf="@id/numberpicker_layout_custom_state_time_clock_hour_first"
        app:layout_constraintStart_toEndOf="@id/numberpicker_layout_custom_state_time_clock_minute_first"
        app:layout_constraintTop_toTopOf="@id/numberpicker_layout_custom_state_time_clock_hour_first"
        app:layout_constraintEnd_toEndOf="@id/numberpicker_layout_custom_number_compare_condition_time_bg_2"/>

</merge>