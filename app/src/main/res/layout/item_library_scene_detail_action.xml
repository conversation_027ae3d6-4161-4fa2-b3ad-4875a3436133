<?xml version="1.0" encoding="utf-8" standalone="no"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="@dimen/y_px_40"
    android:layout_width="@dimen/x_px_475">

    <com.dfl.dflcommonlibs.uimodeutil.UIImageView
        android:id="@+id/imageview_item_library_scene_detail_action_icon"
        android:layout_height="@dimen/y_px_40"
        android:layout_width="@dimen/x_px_40"
        android:scaleType="fitXY"
        android:src="@mipmap/icon_item_my_scene_default"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:ellipsize="end"
        android:id="@+id/textview_item_library_scene_detail_action_title"
        android:includeFontPadding="false"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/x_px_50"
        android:layout_width="@dimen/x_px_400"
        android:maxLines="1"
        android:textColor="@color/color_text_first_label"
        android:textSize="@dimen/y_px_text_body"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>