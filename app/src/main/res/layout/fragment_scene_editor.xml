<?xml version="1.0" encoding="utf-8" standalone="no"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.dfl.smartscene.ccs.fragment.VrOperationTextLayout
        android:id="@+id/vr_operation_text_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

    <com.dfl.smartscene.ccs.view.fragment.SceneNameTextLayout
        android:id="@+id/scene_name_text_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

    <com.dfl.smartscene.ccs.fragment.EditTextLayout
        android:id="@+id/scene_editor_title_text_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <include layout="@layout/layout_content_top" />

        <com.dfl.dflcommonlibs.uimodeutil.UITextView
            android:id="@+id/textview_scene_editor_clear"
            android:layout_width="@dimen/x_px_218"
            android:layout_height="@dimen/y_px_54"
            android:layout_marginRight="@dimen/x_px_174"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/string_scene_editor_fragment_clear"
            android:textColor="@color/color_text_indicator_alpha"
            android:enabled="false"
            android:textSize="@dimen/y_px_text_title"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <com.dfl.dflcommonlibs.uimodeutil.UITextView
            android:id="@+id/textview_scene_edoitor_restore"
            android:layout_width="@dimen/x_px_218"
            android:layout_height="@dimen/y_px_54"
            android:layout_marginRight="@dimen/x_px_174"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/string_scene_editor_fragment_clear"
            android:textColor="@color/color_text_indicator_alpha"
            android:enabled="false"
            android:textSize="@dimen/y_px_text_title"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.dfl.dflcommonlibs.uimodeutil.UITextView
            android:id="@+id/textview_scene_editor_complete"
            android:layout_width="@dimen/x_px_148"
            android:layout_height="@dimen/y_px_54"
            android:layout_marginRight="@dimen/x_px_24"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/string_scene_editor_fragment_complete"
            android:textColor="@color/color_text_indicator"
            android:textSize="@dimen/y_px_text_title"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/layout_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="none">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="@dimen/y_px_24">

            <View
                android:id="@+id/view_open_switch"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_marginTop="20px"
                android:layout_marginLeft="10px"
                android:contentDescription="@string/string_advanced_set_open"
                android:layout_width="1px"
                android:layout_height="1px"/>
            <View
                android:id="@+id/view_close_switch"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_marginTop="25px"
                android:layout_marginLeft="10px"
                android:contentDescription="@string/string_advanced_set_close"
                android:layout_width="1px"
                android:layout_height="1px"/>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/textview_scene_editor_bar"
                android:background="@drawable/drawable_bg_scene_editor_operation"
                android:layout_height="@dimen/y_px_80"
                android:layout_width="@dimen/x_px_660"
                android:paddingLeft="@dimen/x_px_20"
                android:layout_marginLeft="@dimen/x_px_72"
                android:layout_marginTop="@dimen/y_px_20"
                android:tag="background:drawable:drawable_bg_scene_editor_operation"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="MissingConstraints">

            <com.dfl.dflcommonlibs.uimodeutil.UIImageView
                android:id="@+id/imageview_scene_editor_icon"
                android:layout_width="@dimen/x_px_44"
                android:layout_height="@dimen/y_px_44"
                android:scaleType="fitXY"
                android:src="@drawable/icon_edit_scene_64x64"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.dfl.dflcommonlibs.uimodeutil.UITextView
                android:id="@+id/textview_scene_editor_name"
                android:layout_width="@dimen/x_px_500"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/x_px_16"
                android:background="@null"
                android:gravity="center_vertical"
                android:includeFontPadding="false"
                android:maxLength="15"
                android:textColor="@color/color_text_first_label"
                android:textSize="@dimen/y_px_text_subtitle"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/imageview_scene_editor_icon" />

                <com.dfl.dflcommonlibs.uimodeutil.UIImageView
                    android:id="@+id/imageview_corner_icon"
                    android:layout_width="@dimen/x_px_20"
                    android:layout_height="@dimen/x_px_20"
                    android:layout_marginLeft="@dimen/x_px_45"
                    android:layout_marginTop="@dimen/x_px_45"
                    android:src="@drawable/icon_corner"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintLeft_toRightOf="@+id/textview_scene_editor_name" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.dfl.smartscene.ccs.view.weight.CustomSwitch
                android:id="@+id/switch_scene_editor_automeet"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/x_px_63"
                android:textOff=""
                android:textOn=""
                android:thumb="@drawable/selector_switch_thumb_default"
                android:track="@drawable/selector_switch_track_default"
                app:layout_constraintBottom_toBottomOf="@+id/textview_scene_editor_bar"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/textview_scene_editor_bar" />

            <com.dfl.dflcommonlibs.uimodeutil.UITextView
                android:layout_width="@dimen/x_px_137"
                android:layout_height="@dimen/y_px_48"
                android:layout_marginRight="@dimen/x_px_200"
                android:gravity="center_vertical"
                android:includeFontPadding="false"
                android:text="@string/string_scene_editor_fragment_auto_meet"
                android:textColor="@color/color_text_first_label"
                android:textSize="@dimen/y_px_text_subtitle"
                app:layout_constraintBottom_toBottomOf="@+id/textview_scene_editor_bar"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/textview_scene_editor_bar" />

            <com.dfl.dflcommonlibs.uimodeutil.UIImageView
                android:id="@+id/divide_scene_editor_title"
                android:layout_width="@dimen/x_px_1360"
                android:layout_height="@dimen/y_px_1"
                android:layout_marginTop="@dimen/y_px_124"
                android:background="@color/color_divide"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.dfl.dflcommonlibs.uimodeutil.UITextView
                android:id="@+id/textview_scene_editor_vr_title"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/y_px_48"
                android:layout_marginLeft="@dimen/x_px_72"
                android:layout_marginTop="@dimen/y_px_28"
                android:gravity="center_vertical"
                android:includeFontPadding="false"
                android:text="@string/string_scene_editor_vr_title"
                android:textColor="@color/color_text_first_label"
                android:textSize="@dimen/y_px_text_subtitle"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/divide_scene_editor_title" />

            <com.dfl.dflcommonlibs.uimodeutil.UITextView
                android:id="@+id/textview_scene_editor_vr_desc"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/y_px_42"
                android:layout_marginLeft="@dimen/y_px_140"
                android:layout_marginTop="@dimen/y_px_33"
                android:gravity="center_vertical"
                android:includeFontPadding="false"
                android:text="@string/string_scene_editor_vr_desc"
                android:textColor="@color/color_text_tertiary_label"
                android:textSize="@dimen/y_px_text_subbody"
                app:layout_constraintLeft_toLeftOf="@+id/textview_scene_editor_vr_title"
                app:layout_constraintTop_toBottomOf="@+id/divide_scene_editor_title" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerview_scene_editor_vr"
                android:layout_width="@dimen/x_px_1380"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="true"
                android:layout_marginLeft="@dimen/x_px_72"
                android:layout_marginTop="@dimen/y_px_20"
                android:layout_marginRight="@dimen/x_px_66"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textview_scene_editor_vr_desc" />

            <com.dfl.dflcommonlibs.uimodeutil.UIImageView
                android:id="@+id/divide_scene_editor_vr"
                android:layout_width="@dimen/x_px_1360"
                android:layout_height="@dimen/y_px_1"
                android:layout_marginTop="@dimen/y_px_33"
                android:background="@color/color_divide"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/recyclerview_scene_editor_vr" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_scene_editor_condition_state"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toBottomOf="@+id/divide_scene_editor_vr">

                <com.dfl.dflcommonlibs.uimodeutil.UITextView
                    android:id="@+id/textview_scene_editor_condition_title"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/y_px_48"
                    android:layout_marginLeft="@dimen/x_px_72"
                    android:layout_marginTop="@dimen/y_px_28"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:text="@string/string_scene_editor_fragment_condition_title"
                    android:textColor="@color/color_text_first_label"
                    android:textSize="@dimen/y_px_text_subtitle"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


                <com.dfl.dflcommonlibs.uimodeutil.UITextView
                    android:id="@+id/textview_scene_editor_condition_desc"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/y_px_42"
                    android:layout_marginLeft="@dimen/y_px_175"
                    android:layout_marginTop="@dimen/y_px_33"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:text="@string/string_scene_editor_condition_desc"
                    android:textColor="@color/color_text_tertiary_label"
                    android:textSize="@dimen/y_px_text_subbody"
                    app:layout_constraintLeft_toLeftOf="@+id/textview_scene_editor_condition_title"
                    app:layout_constraintTop_toTopOf="parent"/>

                <include
                    android:id="@+id/item_scene_editor_condition"
                    layout="@layout/item_scene_editor_operation"
                    android:layout_width="@dimen/x_px_660"
                    android:layout_height="@dimen/y_px_80"
                    android:layout_marginStart="@dimen/x_px_72"
                    android:layout_marginTop="@dimen/y_px_20"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textview_scene_editor_condition_desc" />

                <com.dfl.dflcommonlibs.uimodeutil.UIImageView
                    android:id="@+id/divide_scene_editor_condition"
                    android:layout_width="@dimen/x_px_1360"
                    android:layout_height="@dimen/y_px_1"
                    android:layout_marginTop="@dimen/y_px_214"
                    android:background="@color/color_divide"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.dfl.dflcommonlibs.uimodeutil.UITextView
                    android:id="@+id/textview_scene_editor_state_title"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/y_px_48"
                    android:layout_marginLeft="@dimen/x_px_72"
                    android:layout_marginTop="@dimen/y_px_28"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:text="@string/string_scene_editor_state_title"
                    android:textColor="@color/color_text_first_label"
                    android:textSize="@dimen/y_px_text_subtitle"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divide_scene_editor_condition" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerview_scene_editor_state"
                    android:layout_width="@dimen/x_px_1380"
                    android:layout_height="match_parent"
                    android:nestedScrollingEnabled="true"
                    android:layout_marginLeft="@dimen/x_px_72"
                    android:layout_marginTop="@dimen/y_px_28"
                    android:layout_marginRight="@dimen/x_px_66"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textview_scene_editor_state_title" />

                <com.dfl.dflcommonlibs.uimodeutil.UIImageView
                    android:id="@+id/divide_scene_editor_state"
                    android:layout_width="@dimen/x_px_1360"
                    android:layout_height="@dimen/y_px_1"
                    android:layout_marginTop="@dimen/y_px_40"
                    android:background="@color/color_divide"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/recyclerview_scene_editor_state" />

                <com.dfl.dflcommonlibs.uimodeutil.UITextView
                    android:id="@+id/textview_scene_editor_time_state_title"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/y_px_48"
                    android:layout_marginLeft="@dimen/x_px_72"
                    android:layout_marginTop="@dimen/y_px_28"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:text="@string/string_scene_editor_time_state"
                    android:textColor="@color/color_text_first_label"
                    android:textSize="@dimen/y_px_text_subtitle"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divide_scene_editor_state" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerview_scene_editor_time_state"
                    android:layout_width="@dimen/x_px_1380"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="true"
                    android:layout_marginLeft="@dimen/x_px_72"
                    android:layout_marginTop="@dimen/y_px_28"
                    android:layout_marginRight="@dimen/x_px_66"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textview_scene_editor_time_state_title" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/y_px_104"
                    android:layout_marginTop="@dimen/y_px_10"
                    app:layout_constraintTop_toBottomOf="@+id/recyclerview_scene_editor_time_state">

                    <com.dfl.smartscene.ccs.view.weight.CustomSwitch
                        android:id="@+id/switch_scene_editor_auto_notify"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/x_px_380"
                        android:thumb="@drawable/selector_switch_thumb_default"
                        android:track="@drawable/selector_switch_track_default"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.dfl.dflcommonlibs.uimodeutil.UITextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"

                        android:layout_marginStart="@dimen/x_px_72"
                        android:includeFontPadding="false"
                        android:text="@string/string_scene_editor_auto_notify"
                        android:textColor="@color/color_text_first_label"
                        android:textSize="@dimen/y_px_text_subtitle"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                    <View
                        android:id="@+id/view_open_auto_switch"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="20px"
                        android:layout_marginLeft="10px"
                        android:contentDescription="@string/string_auto_execute_open"
                        android:layout_width="1px"
                        android:layout_height="1px"/>
                    <View
                        android:id="@+id/view_close_auto_switch"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="25px"
                        android:layout_marginLeft="10px"
                        android:contentDescription="@string/string_auto_execute_close"
                        android:layout_width="1px"
                        android:layout_height="1px"/>


                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.dfl.dflcommonlibs.uimodeutil.UIImageView
                    android:id="@+id/divide_scene_editor_auto_notify"
                    android:layout_width="@dimen/x_px_1360"
                    android:layout_height="@dimen/y_px_1"
                    android:layout_marginTop="@dimen/y_px_40"
                    android:background="@color/color_divide"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent" />


            </androidx.constraintlayout.widget.ConstraintLayout>


            <com.dfl.dflcommonlibs.uimodeutil.UITextView
                android:id="@+id/textview_scene_editor_action_title"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/y_px_48"
                android:layout_marginLeft="@dimen/x_px_72"
                android:layout_marginTop="@dimen/y_px_28"
                android:gravity="center_vertical"
                android:includeFontPadding="false"
                android:text="@string/string_scene_editor_action_title"
                android:textColor="@color/color_text_first_label"
                android:textSize="@dimen/y_px_text_subtitle"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layout_scene_editor_condition_state" />

            <com.dfl.dflcommonlibs.uimodeutil.UITextView
                android:id="@+id/textview_scene_editor_action_desc"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/y_px_42"
                android:layout_marginLeft="@dimen/y_px_140"
                android:layout_marginTop="@dimen/y_px_33"
                android:gravity="center_vertical"
                android:includeFontPadding="false"
                android:text="@string/string_scene_editor_action_desc"
                android:textColor="@color/color_text_tertiary_label"
                android:textSize="@dimen/y_px_text_subbody"
                app:layout_constraintLeft_toLeftOf="@+id/textview_scene_editor_action_title"
                app:layout_constraintTop_toBottomOf="@+id/layout_scene_editor_condition_state" />


            <RelativeLayout
                android:id="@+id/relativelayout_scene_editor_action"
                android:layout_width="match_parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textview_scene_editor_action_desc"
                android:layout_height="wrap_content">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerview_scene_editor_action"
                    android:layout_width="@dimen/x_px_1380"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/x_px_72"
                    android:layout_marginTop="@dimen/y_px_20"
                    android:nestedScrollingEnabled="true"
                    android:paddingTop="-100px"
                    android:paddingBottom="20px" />

            </RelativeLayout>
            <com.dfl.dflcommonlibs.uimodeutil.UITextView
                android:id="@+id/textview_scene_editor_buy_notice"
                android:layout_width="match_parent"
                android:layout_height="@dimen/y_px_40"
                android:gravity="center"
                android:layout_marginTop="@dimen/x_px_20"
                app:layout_constraintTop_toBottomOf="@+id/relativelayout_scene_editor_action"
                android:visibility="gone"
                android:textSize="@dimen/y_px_text_subbody"
                android:includeFontPadding="false"
                android:textColor="@color/color_text_secondary_label"
                android:ellipsize="end"/>
            <View
                android:id="@+id/view_buy_notice"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintBottom_toTopOf="@+id/textview_scene_editor_buy_notice"
                android:layout_marginBottom="5px"
                android:contentDescription="前往购买"
                android:visibility="gone"
                android:layout_width="1px"
                android:layout_height="1px"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>