<?xml version="1.0" encoding="utf-8" standalone="no"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="center"
    android:layout_height="match_parent"
    android:layout_width="match_parent"
    android:orientation="vertical">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerview_item_operation_seat_memory_container"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_width="match_parent" />

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:gravity="center"
        android:hint="@string/string_item_operation_seat_memory_hint"
        android:id="@+id/textview_item_operation_seat_memory_hint"
        android:layout_height="@dimen/y_px_100"
        android:layout_width="wrap_content" />

</LinearLayout>