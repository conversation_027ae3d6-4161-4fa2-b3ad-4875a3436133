<?xml version="1.0" encoding="utf-8" standalone="no"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="@dimen/y_px_50"
    android:layout_width="@dimen/x_px_324">

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:id="@+id/textview_item_operation_menu_child_title"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        android:textColor="@color/color_text_secondary_label"
        android:textSize="@dimen/y_px_text_subtitle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>