<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable name="vm" type="com.dfl.smartscene.ccs.viewmodel.RelieveStressPatternVM"/>
    </data>
    <RelativeLayout xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="@dimen/x_px_1488"
        android:layout_height="@dimen/y_px_612"
        android:layout_marginStart="0px"
        android:layout_marginTop="0px"
        tools:context=".RelieveStressPatternFragment">
        <com.iauto.uicontrol.GestureBoard
            android:id="@+id/gesture_board_relieve_stress_start"
            android:layout_marginStart="0px"
            android:layout_marginTop="0px"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clickable="false"
            >
        </com.iauto.uicontrol.GestureBoard>
        <com.iauto.uicontrol.ViewPagerList
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:id="@+id/background_view_paper"
            />
        <com.iauto.uicontrol.PageIndicator
            android:id="@+id/staticPageIndicator"
            android:layout_width="@dimen/x_px_700"
            android:layout_height="@dimen/y_px_8"
            android:layout_marginTop="@dimen/y_px_592"
            android:layout_centerHorizontal="true"
            app:totalNum="3"
            app:itemMargin="20"
            >
            <com.iauto.uicontrol.ImageBase
                android:tag="dot_on"
                android:layout_width="@dimen/x_px_8"
                android:layout_height="@dimen/y_px_8"
                app:imageString="@drawable/com_dot_on"/>
            <com.iauto.uicontrol.ImageBase
                android:tag="dot_off"
                android:layout_width="@dimen/x_px_8"
                android:layout_height="@dimen/y_px_8"
                app:imageString="@drawable/com_dot_off"/>
            <com.iauto.uicontrol.ImageBase
                android:tag="dot_off"
                android:layout_width="@dimen/x_px_8"
                android:layout_height="@dimen/y_px_8"
                app:imageString="@drawable/com_dot_off"/>
        </com.iauto.uicontrol.PageIndicator>

        <com.iauto.uicontrol.ButtonView
            android:id="@+id/button_relieve_stress_start"
            android:visibility="@{vm.relieveStressCtrlViewVisibleInStop.intValue()}"
            android:layout_width="@dimen/x_px_232"
            android:layout_height="@dimen/x_px_96"
            android:layout_marginTop="@dimen/y_px_213"
            android:layout_marginStart="@dimen/x_px_628"
            app:onCcClick="onCCMethod_RelievePatternStatusChangeClick"
            app:imageString="@drawable/btnbg_navi_switch_off_n"
            app:hasAlphaMask="true"
            app:leftImageId="@drawable/ic48_system_play_n"
            android:drawablePadding="@dimen/x_px_8"
            app:leftImageWidth="@integer/x_int_32"
            app:leftImageHeight="@integer/y_int_32"
            android:paddingLeft="@dimen/x_px_68"
            android:paddingRight="@dimen/x_px_68"
            android:paddingTop="@dimen/y_px_27"
            android:paddingBottom="@dimen/y_px_27"
            android:gravity="center"
            android:textSize="@dimen/font_size_28"
            android:textColor="@color/color_text_button"
            android:contentDescription="@string/string_relieve_stress_start"
            app:textString="@string/string_relieve_stress_start">
        </com.iauto.uicontrol.ButtonView>

        <com.iauto.uicontrol.ButtonView
            android:id="@+id/button_relieve_stress_timer"
            android:visibility="@{vm.relieveStressCtrlViewVisibleInStop.intValue()}"
            android:layout_width="@dimen/x_px_220"
            android:layout_height="@dimen/y_px_102"
            android:layout_marginTop="@dimen/y_px_369"
            android:layout_marginStart="@dimen/x_px_634"
            app:onCcClick="onCCMethod_RelievePatternStatusChangeClick"
            app:imageString="@drawable/com_dialogs2"
            app:hasAlphaMask="true"
            android:paddingLeft="@dimen/x_px_46"
            android:paddingRight="@dimen/x_px_46"
            android:paddingTop="@dimen/y_px_30"
            android:paddingBottom="@dimen/y_px_30"
            android:gravity="center"
            android:textSize="@dimen/font_size_28"
            android:textColor="@color/color_text_explain_spec"
            android:text="@{vm.relieveStressTimerText.toString()}">
        </com.iauto.uicontrol.ButtonView>

        <com.iauto.uicontrol.ImageBase
            android:layout_width="@dimen/x_px_220"
            android:layout_height="@dimen/y_px_204"
            android:visibility="@{vm.relieveStressTimerListVisible.intValue()}"
            app:layout_constraintTop_toTopOf = "parent"
            android:layout_marginStart="@dimen/x_px_634"
            android:layout_marginTop="@dimen/y_px_318"
            app:imageString="@drawable/com_dialogs2"/>
        <com.iauto.uicontrol.ImageBase
            android:layout_width="@dimen/x_px_160"
            android:layout_height="@dimen/y_px_52"
            android:visibility="@{vm.relieveStressTimerListVisible.intValue()}"
            android:layout_marginStart="@dimen/x_px_664"
            android:layout_marginTop="@dimen/y_px_394"
            app:imageString="@drawable/bg_popup_select"/>
        <com.iauto.uicontrol.ANumberPicker
            android:id="@+id/timer_picker"
            android:visibility="@{vm.relieveStressTimerListVisible.intValue()}"
            android:layout_marginStart="@dimen/x_px_680"
            android:layout_marginTop="@dimen/y_px_348"
            android:layout_width="@dimen/x_px_128"
            android:layout_height="@dimen/y_px_144"
            app:textAlignType = "Center"
            app:lines="3"
            app:curValue="@{vm.relieveStressTimerIndex.intValue()}"
            app:midTextColor="@color/color_text_explain_spec"
            app:sideTextColor="@color/color_text_explain"
            app:midTextSize="@integer/x_int_28"
            app:sideTextSize="@integer/x_int_24"/>

        <com.iauto.uicontrol.TextBase
            android:id="@+id/text_relieve_stress_time"
            android:visibility="@{vm.relieveStressTimerVisible.intValue()}"
            android:layout_marginStart="@dimen/x_px_594"
            android:layout_marginTop="@dimen/y_px_198"
            android:layout_width="@dimen/x_px_300"
            android:layout_height="@dimen/y_px_120"
            android:textColor="@color/color_text_text"
            android:textSize="@dimen/font_size_80"
            android:text="@{vm.relieveStressDspTimer.toString()}"
            android:gravity="center_vertical|center_horizontal"/>


        <com.iauto.uicontrol.ButtonView
            android:id="@+id/button_relieve_stress_stop"
            android:visibility="@{vm.relieveStressCtrlViewVisibleInPause.intValue()}"
            android:layout_width="@dimen/x_px_232"
            android:layout_height="@dimen/x_px_96"
            android:layout_marginTop="@dimen/y_px_318"
            android:layout_marginStart="@dimen/x_px_512"
            app:onCcClick="onCCMethod_RelievePatternStatusChangeClick"
            app:imageString="@drawable/btnbg_navi_switch_off_n"
            app:hasAlphaMask="true"
            app:leftImageId="@drawable/ic48_system_stop_n"
            app:leftImageWidth="@integer/x_int_32"
            app:leftImageHeight="@integer/y_int_32"
            android:drawablePadding="@dimen/x_px_8"
            android:paddingLeft="@dimen/x_px_68"
            android:paddingRight="@dimen/x_px_68"
            android:paddingTop="@dimen/y_px_27"
            android:paddingBottom="@dimen/y_px_27"
            android:gravity="center"
            android:textSize="@dimen/font_size_28"
            android:textColor="@color/color_text_button"
            android:contentDescription="@string/string_relieve_stress_stop"
            app:textString="@string/string_relieve_stress_stop">
        </com.iauto.uicontrol.ButtonView>

        <com.iauto.uicontrol.ButtonView
            android:id="@+id/button_relieve_stress_phone_stop"
            android:visibility="@{vm.relieveStressCtrlViewVisibleInPhoneStop.intValue()}"
            android:layout_width="@dimen/x_px_232"
            android:layout_height="@dimen/x_px_96"
            android:layout_marginTop="@dimen/y_px_318"
            android:layout_centerHorizontal="true"
            app:onCcClick="onCCMethod_RelievePatternStatusChangeClick"
            app:imageString="@drawable/btnbg_navi_switch_off_n"
            app:hasAlphaMask="true"
            app:leftImageId="@drawable/ic48_system_stop_n"
            app:leftImageWidth="@integer/x_int_32"
            app:leftImageHeight="@integer/y_int_32"
            android:drawablePadding="@dimen/x_px_8"
            android:paddingLeft="@dimen/x_px_68"
            android:paddingRight="@dimen/x_px_68"
            android:paddingTop="@dimen/y_px_27"
            android:paddingBottom="@dimen/y_px_27"
            android:gravity="center"
            android:textSize="@dimen/font_size_28"
            android:textColor="@color/color_text_button"
            android:contentDescription="@string/string_relieve_stress_stop"
            app:textString="@string/string_relieve_stress_stop">
        </com.iauto.uicontrol.ButtonView>

        <com.iauto.uicontrol.ButtonView
            android:id="@+id/button_relieve_stress_play"
            android:visibility="@{vm.relieveStressCtrlViewVisibleInPause.intValue()}"
            android:layout_width="@dimen/x_px_232"
            android:layout_height="@dimen/x_px_96"
            android:layout_marginTop="@dimen/y_px_318"
            android:layout_marginStart="@dimen/x_px_744"
            app:onCcClick="onCCMethod_RelievePatternStatusChangeClick"
            app:imageString="@drawable/btnbg_navi_switch_off_n"
            app:hasAlphaMask="true"
            app:leftImageId="@drawable/ic48_system_play_n"
            app:leftImageWidth="@integer/x_int_32"
            app:leftImageHeight="@integer/y_int_32"
            android:drawablePadding="@dimen/x_px_8"
            android:paddingLeft="@dimen/x_px_68"
            android:paddingRight="@dimen/x_px_68"
            android:paddingTop="@dimen/y_px_27"
            android:paddingBottom="@dimen/y_px_27"
            android:gravity="center"
            android:textSize="@dimen/font_size_28"
            android:textColor="@color/color_text_button"
            android:contentDescription="@string/string_relieve_stress_play"
            app:textString="@string/string_relieve_stress_play">
        </com.iauto.uicontrol.ButtonView>

        <com.iauto.uicontrol.ButtonView
            android:id="@+id/button_relieve_stress_pause"
            android:layout_width="@dimen/x_px_232"
            android:layout_height="@dimen/x_px_96"
            android:layout_marginStart="@dimen/x_px_628"
            android:layout_marginTop="@dimen/y_px_318"
            android:contentDescription="@string/string_relieve_stress_pause"
            android:drawablePadding="@dimen/x_px_8"
            android:gravity="center"
            android:paddingLeft="@dimen/x_px_68"
            android:paddingTop="@dimen/y_px_27"
            android:paddingRight="@dimen/x_px_68"
            android:paddingBottom="@dimen/y_px_27"
            android:textColor="@color/color_text_button"
            android:textSize="@dimen/font_size_28"
            android:visibility="@{vm.relieveStressCtrlViewVisibleInPlay.intValue()}"
            app:hasAlphaMask="true"
            app:imageString="@drawable/btnbg_navi_switch_off_n"
            app:leftImageHeight="@integer/y_int_32"
            app:leftImageId="@drawable/ic48_system_suspend_n"
            app:leftImageWidth="@integer/x_int_32"
            app:onCcClick="onCCMethod_RelievePatternStatusChangeClick"
            app:textString="@string/string_relieve_stress_pause"></com.iauto.uicontrol.ButtonView>

        <com.iauto.uicontrol.ButtonView
            android:id="@+id/button_relieve_stress_expand"
            android:layout_marginStart="@dimen/x_px_44"
            android:layout_marginTop="@dimen/y_px_490"
            android:layout_width="@dimen/x_px_112"
            android:layout_height="@dimen/y_px_112"
            android:visibility="@{vm.relieveStressExpandVisible.intValue()}"
            app:leftImageId="@drawable/ic48_system_max_n"
            app:onCcClick="onCCMethod_RelievePatternStatusChangeClick"
            app:leftImageHeight="@integer/x_int_40"
            app:leftImageWidth="@integer/y_int_40"
            android:paddingLeft="@dimen/x_px_36"
            android:paddingRight="@dimen/x_px_36"
            android:paddingTop="@dimen/y_px_36"
            android:paddingBottom="@dimen/y_px_36"
            android:contentDescription="@string/string_expand_contentDescription"
            app:imageString="@drawable/btn112_com_circular2_n"
            app:coverImageId="@drawable/com_112x112"
            app:hasAlphaMask="true"
            app:isBtnScaleAnim="true"
            app:btnScaleValue="0.95">
    </com.iauto.uicontrol.ButtonView>

        <com.iauto.uicontrol.ImageBase
            android:layout_marginStart="@dimen/x_px_411"
            android:layout_marginTop="@dimen/y_px_450"
            android:layout_width="@dimen/x_px_666"
            android:layout_height="@dimen/y_px_144"
            android:visibility="@{safeUnbox(vm.SettingVolumeViewVisible)}"
            app:imageString="@drawable/com_dialogs2"
            >
        </com.iauto.uicontrol.ImageBase>
        <com.iauto.uicontrol.ImageBase
            android:layout_marginStart="@dimen/x_px_479"
            android:layout_marginTop="@dimen/y_px_509"
            android:layout_width="@dimen/x_px_26"
            android:layout_height="@dimen/y_px_26"
            android:visibility="@{safeUnbox(vm.SettingVolumeViewVisible)}"
            app:imageString="@drawable/ic48_system_soundeffect2_n"
            >
        </com.iauto.uicontrol.ImageBase>
        <com.iauto.uicontrol.ImageBase
            android:layout_marginStart="@dimen/x_px_977"
            android:layout_marginTop="@dimen/y_px_502"
            android:layout_width="@dimen/x_px_40"
            android:layout_height="@dimen/y_px_40"
            android:visibility="@{safeUnbox(vm.SettingVolumeViewVisible)}"
            app:imageString="@drawable/ic48_system_soundeffect_n"
            >
        </com.iauto.uicontrol.ImageBase>
        <com.iauto.uicontrol.ProgressBar
            android:layout_marginStart="@dimen/x_px_499"
            android:layout_marginTop="@dimen/y_px_470"
            android:layout_width="@dimen/x_px_484"
            android:layout_height="@dimen/y_px_104"
            app:curPercentValue="@{safeUnbox(vm.RelieveStressVolumeValue)}"
            app:totalValue="40"
            app:initialValue="0"
            app:isToneDown="false"
            android:visibility="@{safeUnbox(vm.SettingVolumeViewVisible)}"
            >
            <com.iauto.uicontrol.ImageBase
                android:layout_marginStart="@dimen/x_px_30"
                android:layout_marginTop="@dimen/y_px_48"
                android:layout_width="@dimen/x_px_424"
                android:layout_height="@dimen/y_px_8"
                android:tag="Background"
                app:imageString="@drawable/bg_setting_progressbg"
                >
            </com.iauto.uicontrol.ImageBase>
            <com.iauto.uicontrol.ImageBase
                android:layout_marginStart="@dimen/x_px_30"
                android:layout_marginTop="@dimen/y_px_48"
                android:layout_width="@dimen/x_px_424"
                android:layout_height="@dimen/y_px_8"
                android:tag="Progress"
                app:imageString="@drawable/bg_setting_progress"
                >
            </com.iauto.uicontrol.ImageBase>
            <com.iauto.uicontrol.ImageBase
                android:layout_marginStart="@dimen/x_px_190"
                android:layout_marginTop="@dimen/y_px_22"
                android:layout_width="@dimen/x_px_60"
                android:layout_height="@dimen/y_px_60"
                android:tag="Block"
                app:imageString="@drawable/ic60_setting_slider"
                >
            </com.iauto.uicontrol.ImageBase>
        </com.iauto.uicontrol.ProgressBar>


    </RelativeLayout>


</layout>