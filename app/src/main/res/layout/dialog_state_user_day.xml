<?xml version="1.0" encoding="utf-8" standalone="no"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="wrap_content"
    android:layout_width="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:background="@drawable/bg_common_dialog"
        android:layout_height="@dimen/y_px_dialog_height"
        android:layout_width="@dimen/x_px_dialog_width"
        android:tag="background:drawable:bg_common_dialog"
        android:paddingTop="@dimen/y_px_16"
        android:paddingLeft="@dimen/x_px_16"
        android:paddingRight="@dimen/x_px_16"
        android:paddingBottom="@dimen/y_px_16">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_height="@dimen/y_px_88"
            android:layout_width="match_parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageButton
                android:background="@drawable/icon_back"
                android:id="@+id/imagebutton_user_day_dialog_back"
                android:layout_height="@dimen/y_px_40"
                android:layout_marginLeft="@dimen/x_px_40"
                android:layout_width="@dimen/x_px_40"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.dfl.dflcommonlibs.uimodeutil.UITextView
                android:includeFontPadding="false"
                android:layout_height="wrap_content"
                android:layout_width="wrap_content"
                android:text="@string/string_state_user_day_title"
                android:textColor="@color/color_text_first_label"
                android:textSize="@dimen/y_px_text_title"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.dfl.dflcommonlibs.uimodeutil.UIImageView
                android:background="@color/color_divide"
                android:layout_height="@dimen/y_px_1"
                android:layout_marginLeft="@dimen/x_px_24"
                android:layout_marginRight="@dimen/x_px_24"
                android:layout_width="match_parent"
                app:layout_constraintBottom_toBottomOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerview_user_day"
            android:layout_height="match_parent"
            android:layout_marginBottom="@dimen/y_px_100"
            android:layout_marginLeft="@dimen/x_px_140"
            android:layout_marginTop="@dimen/y_px_136"
            android:layout_width="@dimen/x_px_979"
            app:layout_constraintLeft_toLeftOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:background="@drawable/bg_button_positive"
            android:id="@+id/imagebutton_user_day_dialog_confirm"
            android:layout_height="@dimen/y_px_96"
            android:layout_marginBottom="@dimen/y_px_40"
            android:layout_marginLeft="@dimen/x_px_248"
            android:layout_width="@dimen/x_px_232"
            android:tag="background:drawable:bg_button_positive"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent">

            <com.dfl.dflcommonlibs.uimodeutil.UITextView
                android:includeFontPadding="false"
                android:layout_height="wrap_content"
                android:layout_width="wrap_content"
                android:text="确定"
                android:textColor="@color/color_white"
                android:textSize="@dimen/y_px_text_body"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:background="@drawable/bg_button_negative_new"
            android:id="@+id/imagebutton_user_day_dialog_cancel"
            android:layout_height="@dimen/y_px_96"
            android:layout_marginBottom="@dimen/y_px_40"
            android:layout_marginRight="@dimen/x_px_248"
            android:layout_width="@dimen/x_px_232"
            android:tag="background:drawable:bg_button_negative_new"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent">

            <com.dfl.dflcommonlibs.uimodeutil.UITextView
                android:includeFontPadding="false"
                android:layout_height="wrap_content"
                android:layout_width="wrap_content"
                android:text="取消"
                android:textColor="@color/color_dialog_btn_text_cancel"
                android:textSize="@dimen/y_px_text_body"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>