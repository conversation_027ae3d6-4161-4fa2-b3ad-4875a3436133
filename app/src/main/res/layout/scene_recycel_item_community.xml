<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <import type="com.dfl.android.common.util.TimeUtils" />

        <import type="com.dfl.android.common.util.NumberToCNUtil" />

        <variable
            name="item"
            type="com.dfl.smartscene.bean.community.reponse.CommunitySceneInfo" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_root_card"
        android:layout_width="@dimen/px_581"
        android:layout_height="@dimen/px_196"
        android:background="@color/transparent">

        <!--圆角背景图-->
        <com.dfl.smartscene.widget.CornerImageView
            android:id="@+id/iv_bg"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/transparent"
            android:scaleType="centerCrop"
            android:src="@drawable/scene_img_community_card_loading"
            app:corner_radius="@dimen/px_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!--卡片蒙层背景-->
        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/scene_shape_community_card_mask"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!--卡片底部蒙层背景-->
        <View
            android:id="@+id/view_bottom"
            android:layout_width="0dp"
            android:layout_height="@dimen/scene_height_community_card_bottom"
            android:background="@color/transparent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <!--头像-->
        <com.dfl.smartscene.widget.CornerImageView
            android:id="@+id/iv_user_heard"
            android:layout_width="@dimen/scene_icon_size_normal_icon"
            android:layout_height="@dimen/scene_icon_size_normal_icon"
            android:layout_marginEnd="248dp"
            android:background="@color/transparent"
            android:visibility="gone"
            app:corner_radius="50dp"
            app:layout_constraintBottom_toBottomOf="@id/view_bottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/view_bottom" />

        <!--用户名-->
        <TextView
            android:id="@+id/tv_user_name"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginHorizontal="20dp"
            android:ellipsize="end"
            android:gravity="start|center_vertical"
            android:lines="1"
            android:textColor="@color/scene_color_text_1"
            android:textSize="@dimen/scene_text_size_h6"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/view_bottom"
            app:layout_constraintEnd_toEndOf="@id/view_bottom"
            app:layout_constraintStart_toEndOf="@id/iv_user_heard"
            app:layout_constraintTop_toTopOf="@id/view_bottom"
            tools:ignore="RtlHardcoded,SpUsage"
            tools:text="智慧场景助手" />

        <!--下载-->
        <com.dfl.android.animationlib.ScaleImageButton
            android:id="@+id/tv_downloads"
            style="@style/SecondaryBigBtnColorStyle.CardWithIconStyle"
            android:layout_marginStart="48dp"
            android:visibility="gone"
            app:imgSrc="@drawable/scene_icon_common_download_text1"
            app:imgSrcClicked="@drawable/scene_icon_common_download_text1"
            app:layout_constraintBottom_toBottomOf="@id/view_bottom"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/view_bottom"
            app:text='@{NumberToCNUtil.formatOverString(item.downloads)}'
            tools:text="100" />
<!--        &lt;!&ndash;点赞&ndash;&gt;-->
        <com.dfl.android.animationlib.ScaleImageButton
            android:id="@+id/tv_likes"
            style="@style/SecondaryBigBtnColorStyle.CardWithIconStyle"
            android:layout_marginStart="24dp"
            android:visibility="gone"          app:imgAutoPlay="false"
            app:imgIsAnimation="true"
            app:imgSrc="@raw/scene_likes_day"
            app:layout_constraintBottom_toBottomOf="@id/tv_downloads"
            app:layout_constraintStart_toEndOf="@id/tv_downloads"
            app:layout_constraintTop_toTopOf="@id/tv_downloads"
            app:spaceWidth="20dp"
            app:text='@{NumberToCNUtil.formatOverString(item.likes)}'
            app:textColor="@{item.isLike()? @color/scene_primary_color_highlight : @color/scene_color_text_1}"
            tools:ignore="SpUsage"
            tools:text="100" />

        <!--标题-->
        <ImageView
            android:id="@+id/img_scene_head"
            android:layout_width="@dimen/px_48"
            android:layout_height="@dimen/px_48"
            app:layout_constraintTop_toTopOf="@+id/tv_scene_name"
            app:layout_constraintBottom_toBottomOf="@id/tv_scene_name"
            app:layout_constraintStart_toStartOf="parent"
            android:src="@drawable/scene_img_default_head"
            android:layout_marginStart="@dimen/px_32"/>
        <TextView
            android:id="@+id/tv_scene_name"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/px_46"
            android:layout_marginStart="@dimen/px_18"
            android:layout_marginTop="@dimen/px_50"
            android:ellipsize="end"
            android:gravity="start|center_vertical"
            android:lines="1"
            android:textColor="@color/scene_color_text_1"
            android:textSize="@dimen/scene_text_size_p42r_h3"
            app:layout_constraintStart_toEndOf="@+id/img_scene_head"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="SpUsage"
            tools:text="亲子时刻" />

        <!--内容-->
        <TextView
            android:id="@+id/tv_scene_desc"
            android:layout_width="0dp"
            android:layout_height="@dimen/px_40"
            android:layout_marginTop="11dp"
            android:paddingHorizontal="@dimen/px_32"
            android:alpha="0.5"
            android:ellipsize="end"
            android:gravity="start|center_vertical"
            android:maxLines="1"
            android:textColor="@color/scene_color_text_2"
            android:textSize="@dimen/scene_text_size_p42r_h6"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_scene_name"
            tools:ignore="SpUsage"
            tools:text="日常导航回家路上开启音乐并打开氛围灯呼吸的模式日常导航回家路上开启轻松并打开氛围灯呼吸的模啊式" />

        <View
            android:layout_width="0dp"
            android:layout_height="2dp"
            android:visibility="gone"
            android:layout_marginHorizontal="48dp"
            android:background="@color/scene_color_day_bg2_night_btn2"
            app:layout_constraintBottom_toTopOf="@id/view_bottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <!--账号页上传时间-->
        <TextView
            android:id="@+id/tv_scene_upload_time"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/scene_height_text_size_h5"
            android:layout_marginEnd="60dp"
            android:text="@{TimeUtils.mills2HideCurYearString(item.uploadTimestamp)}"
            android:textColor="@color/scene_color_text_3"
            android:textSize="@dimen/scene_text_size_h5"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/view_bottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/view_bottom"
            tools:text="10月25号发布" />
        <!--账号页更多菜单-->
        <ImageButton
            android:id="@+id/btn_more"
            android:layout_width="@dimen/scene_icon_size_normal_icon"
            android:layout_height="@dimen/scene_icon_size_normal_icon"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="52dp"
            android:background="@color/transparent"
            android:contentDescription="@string/scene_button_common_delete"
            android:src="@drawable/scene_icon_common_card_more"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!--ui1.3取消-->
        <!--                <TextView-->
        <!--                    android:id="@+id/tv_upload_times"-->
        <!--                    android:layout_width="wrap_content"-->
        <!--                    android:layout_height="@dimen/px_32"-->
        <!--                    android:layout_marginEnd="@dimen/px_40"-->
        <!--                    android:gravity="start|center_vertical"-->
        <!--                    android:text="@{TimeUtils.mills2HideCurYearString(item.uploadTimestamp)}"-->
        <!--                    android:textColor="@color/scene_text_color_card_content"-->
        <!--                    android:textSize="@dimen/px_22"-->
        <!--                    android:visibility="gone"-->
        <!--                    app:layout_constraintBottom_toBottomOf="@id/tv_downloads"-->
        <!--                    app:layout_constraintEnd_toEndOf="parent"-->
        <!--                    app:layout_constraintTop_toTopOf="@id/tv_downloads"-->
        <!--                    tools:ignore="SpUsage"-->
        <!--                    tools:text="9月11日发布" />-->
        <!--        -->
        <!--                <TextView-->
        <!--                    android:id="@+id/tv_subscribe"-->
        <!--                    android:layout_width="@dimen/px_74"-->
        <!--                    android:layout_height="@dimen/px_34"-->
        <!--                    android:layout_marginEnd="40dp"-->
        <!--                    android:background="@drawable/scene_shape_community_subscribed"-->
        <!--                    android:gravity="center"-->
        <!--                    android:textColor="@color/scene_middle_text_color_third_comment"-->
        <!--                    android:textSize="@dimen/px_18"-->
        <!--                    android:visibility="gone"-->
        <!--                    app:layout_constraintBottom_toBottomOf="@id/view_bottom"-->
        <!--                    app:layout_constraintEnd_toEndOf="@id/view_bottom"-->
        <!--                    app:layout_constraintTop_toTopOf="@id/view_bottom"-->
        <!--                    tools:ignore="SpUsage"-->
        <!--                    tools:text="@string/scene_text_user_center_subscribed" />-->
        <!--                <View-->
        <!--                    android:id="@+id/view_subscribe"-->
        <!--                    android:layout_width="117dp"-->
        <!--                    android:layout_height="0dp"-->
        <!--                    android:visibility="gone"-->
        <!--                    app:layout_constraintBottom_toBottomOf="@id/view_bottom"-->
        <!--                    app:layout_constraintEnd_toEndOf="@id/view_bottom"-->
        <!--                    app:layout_constraintTop_toTopOf="@id/view_bottom" />-->
        <!--                &lt;!&ndash;关注小弹窗&ndash;&gt;-->
        <!--                <androidx.constraintlayout.widget.ConstraintLayout-->
        <!--                    android:id="@+id/view_pop"-->
        <!--                    android:layout_width="0dp"-->
        <!--                    android:layout_height="0dp"-->
        <!--                    android:background="@drawable/scene_shape_community_pop_big_bg"-->
        <!--                    android:visibility="gone"-->
        <!--                    app:layout_constraintBottom_toBottomOf="parent"-->
        <!--                    app:layout_constraintEnd_toEndOf="parent"-->
        <!--                    app:layout_constraintStart_toStartOf="parent"-->
        <!--                    app:layout_constraintTop_toTopOf="parent">-->

        <!--                    <LinearLayout-->
        <!--                        android:layout_width="224dp"-->
        <!--                        android:layout_height="128dp"-->
        <!--                        android:background="@drawable/scene_shape_community_e0_white_radius_12_border_gray"-->
        <!--                        android:orientation="vertical"-->
        <!--                        app:layout_constraintBottom_toBottomOf="parent"-->
        <!--                        app:layout_constraintEnd_toEndOf="parent">-->

        <!--                        <TextView-->
        <!--                            android:id="@+id/tv_no_longer_sub"-->
        <!--                            android:layout_width="match_parent"-->
        <!--                            android:layout_height="64dp"-->
        <!--                            android:gravity="center"-->
        <!--                            android:text="@string/scene_text_community_no_longer_subscribes"-->
        <!--                            android:textColor="@color/scene_icon_fill_color_select"-->
        <!--                            android:textSize="@dimen/px_24"-->
        <!--                            tools:ignore="SpUsage" />-->

        <!--                        <View-->
        <!--                            android:layout_width="match_parent"-->
        <!--                            android:layout_height="1dp"-->
        <!--                            android:layout_marginHorizontal="25dp"-->
        <!--                            android:background="@color/scene_community_recycler_item_favorites_line" />-->

        <!--                        <TextView-->
        <!--                            android:id="@+id/tv_no_longer_sub_cancel"-->
        <!--                            android:layout_width="match_parent"-->
        <!--                            android:layout_height="64dp"-->
        <!--                            android:gravity="center"-->
        <!--                            android:text="@string/scene_button_alert_cancel"-->
        <!--                            android:textColor="@color/scene_icon_fill_color_select"-->
        <!--                            android:textSize="@dimen/px_24"-->
        <!--                            tools:ignore="SpUsage" />-->
        <!--                    </LinearLayout>-->


        <!--                </androidx.constraintlayout.widget.ConstraintLayout>-->

        <!--        &lt;!&ndash;闪烁&ndash;&gt;-->
        <!--        <com.airbnb.lottie.LottieAnimationView-->
        <!--            android:id="@+id/me_lottie_view"-->
        <!--            scene_lottie_setting="@{item.isShowLottieAnimation}"-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="match_parent"-->
        <!--            android:scaleType="centerCrop"-->
        <!--            android:visibility="@{item.isShowLottieAnimation?View.VISIBLE:View.GONE,default=gone}"-->
        <!--            app:layout_constraintBottom_toBottomOf="parent"-->
        <!--            app:layout_constraintEnd_toEndOf="parent"-->
        <!--            app:layout_constraintStart_toStartOf="parent"-->
        <!--            app:layout_constraintTop_toTopOf="parent"-->
        <!--            app:lottie_loop="true" />-->


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>