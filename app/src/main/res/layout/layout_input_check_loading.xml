<?xml version="1.0" encoding="utf-8" standalone="no"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:visibility="gone">

    <include
        layout="@layout/layout_normal_loading_anim"
        android:id="@+id/image_loading"
        android:layout_width="@dimen/x_px_150"
        android:layout_height="@dimen/y_px_150"
        android:layout_marginTop="@dimen/x_px_20"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:id="@+id/loading_tv1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="处理中..."
        android:textColor="@color/color_text_unselected"
        android:textSize="@dimen/font_size_30"
        android:layout_marginTop="@dimen/y_px_10"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/image_loading" />

</androidx.constraintlayout.widget.ConstraintLayout>