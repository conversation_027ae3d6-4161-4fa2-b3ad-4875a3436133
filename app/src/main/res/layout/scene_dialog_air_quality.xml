<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/SmallDialogTheme"
        android:layout_marginTop="76dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_air_title"
            style="@style/SmallDialogTitleTheme"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="空气质量" />

        <TextView
            android:id="@+id/tv_three_check_title_desc"
            style="@style/SmallDialogSubTitleTheme"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/tv_air_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="标题描述------" />

        <LinearLayout
            android:id="@+id/ll_difference"
            style="@style/SelectorLinearLayoutTheme"
            android:layout_width="@dimen/scene_width_selector_checkbox"
            android:layout_marginStart="80dp"
            android:layout_marginTop="72dp"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_air_title">

            <ImageView
                android:id="@+id/iv_difference"
                android:layout_width="@dimen/scene_icon_size_normal_icon"
                android:layout_height="@dimen/scene_icon_size_normal_icon"
                android:layout_marginEnd="@dimen/scene_margin_end_selector_checkbox_icon"
                android:visibility="gone"
                tools:src="@drawable/scene_icon_me_action_seat_copilot_air" />

            <CheckedTextView
                android:id="@+id/ctv_difference"
                style="@style/SelectorCheckTextViewTheme"
                android:checked="true"
                tools:text="列表" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_middle"
            style="@style/SelectorLinearLayoutTheme"
            android:layout_width="@dimen/scene_width_selector_checkbox"
            android:layout_marginStart="@dimen/scene_margin_selector_checkbox"
            android:orientation="horizontal"
            app:layout_constraintStart_toEndOf="@+id/ll_difference"
            app:layout_constraintTop_toTopOf="@+id/ll_difference">

            <ImageView
                android:id="@+id/iv_middle"
                android:layout_width="@dimen/scene_icon_size_normal_icon"
                android:layout_height="@dimen/scene_icon_size_normal_icon"
                android:layout_marginEnd="@dimen/scene_margin_end_selector_checkbox_icon"
                android:visibility="gone"
                tools:src="@drawable/scene_icon_me_action_seat_copilot_air" />

            <CheckedTextView
                android:id="@+id/ctv_middle"
                style="@style/SelectorCheckTextViewTheme"
                tools:text="@string/scene_text_edit_action_middle" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_excellent"
            style="@style/SelectorLinearLayoutTheme"
            android:layout_width="@dimen/scene_width_selector_checkbox"
            android:layout_marginStart="@dimen/scene_margin_selector_checkbox"
            app:layout_constraintStart_toEndOf="@+id/ll_middle"
            app:layout_constraintTop_toTopOf="@+id/ll_middle">

            <ImageView
                android:id="@+id/iv_excellent"
                android:layout_width="@dimen/scene_icon_size_normal_icon"
                android:layout_height="@dimen/scene_icon_size_normal_icon"
                android:layout_marginEnd="@dimen/scene_margin_end_selector_checkbox_icon"
                android:visibility="gone"
                tools:src="@drawable/scene_icon_me_action_seat_copilot_air" />

            <CheckedTextView
                android:id="@+id/ctv_excellent"
                style="@style/SelectorCheckTextViewTheme"
                tools:text="@string/scene_text_edit_condition_air_excellent" />
        </LinearLayout>

        <include
            android:id="@+id/include_dialog_bottom"
            layout="@layout/scene_layout_dialog_bottom_button"
            android:layout_width="match_parent"
            android:layout_height="@dimen/scene_height_dialog_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>