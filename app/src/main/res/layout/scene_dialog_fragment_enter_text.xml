<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.overlay.apply.EnterTextDialogFragment">


    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/BigDialogThemeP42R"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_title"
            style="@style/SmallDialogTitleTheme"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:visibility="gone"
            tools:text="标题" />

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/px_40"
            android:layout_height="@dimen/px_40"
            android:layout_marginStart="@dimen/px_60"
            android:layout_marginTop="@dimen/px_32"
            android:scaleType="center"
            android:src="@drawable/scene_icon_common_arrow_left"
            android:visibility="visible"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:ignore="RtlSymmetry" />
        <LinearLayout
            android:id="@+id/ll_end_address"
            android:layout_width="1028dp"
            android:layout_height="@dimen/px_76"
            android:layout_marginStart="@dimen/px_24"
            android:layout_marginTop="16dp"
            android:background="@drawable/scene_shape_edit_address_bg"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="@dimen/px_36"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_back"
            app:layout_constraintTop_toBottomOf="@id/tv_title">

            <ImageView
                android:id="@+id/iv_input_end"
                android:layout_width="@dimen/px_32"
                android:layout_height="@dimen/px_32"
                android:src="@drawable/scene_icon_common_search"
                android:visibility="visible" />

            <com.dfl.smartscene.widget.ClearEditText
                android:id="@+id/et_edit_input"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@null"
                android:cursorVisible="true"
                android:maxLength="30"
                android:paddingStart="@dimen/px_20"
                android:paddingEnd="@dimen/px_36"
                android:selectAllOnFocus="true"
                android:singleLine="true"
                android:textColor="@color/scene_color_text_1"
                android:textColorHint="@color/scene_color_text_3"
                android:textCursorDrawable="@drawable/scene_shape_edit_cursor_bg"
                android:textSize="@dimen/scene_text_size_p42r_h6" />

        </LinearLayout>


        <include
            android:id="@+id/include_dialog_bottom"
            layout="@layout/scene_layout_dialog_bottom_flat"
            android:layout_width="match_parent"
            android:layout_height="@dimen/scene_height_dialog_bottom_p42r"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />


        <com.dfl.android.animationlib.scrollView.FadeRecyclerView
            android:id="@+id/rv_action_song"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/px_14"
            android:overScrollMode="never"
            android:visibility="visible"
            android:paddingHorizontal="@dimen/px_48"
            app:layout_constraintBottom_toTopOf="@id/include_dialog_bottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_end_address" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>