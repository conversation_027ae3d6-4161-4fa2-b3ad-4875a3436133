<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="114dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginStart="76dp"
                android:src="@drawable/scene_icon_me_action_navigation_gowhere_list" />

            <TextView
                android:id="@+id/tv_address_item"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="20dp"
                android:ellipsize="end"
                android:gravity="start|center_vertical"
                android:maxWidth="1645dp"
                android:singleLine="true"
                android:textColor="@color/scene_color_text_1"
                android:textSize="@dimen/scene_text_size_h4"
                tools:text="广东省\t广州市花都区" />

            <TextView
                android:id="@+id/address_detail_item"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="20dp"
                android:ellipsize="end"
                android:gravity="start|center_vertical"
                android:singleLine="true"
                android:textColor="@color/scene_color_text_3"
                android:textSize="@dimen/scene_text_size_h6"
                tools:text="风神大道100086号" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginHorizontal="39dp"
            android:background="#14000000" />
    </LinearLayout>

</layout>