<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable name="vm" type="com.dfl.smartscene.ccs.viewmodel.ScenePatternMenuVM"/>
    </data>
    <RelativeLayout xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="@dimen/x_px_1488"
        android:layout_height="@dimen/y_px_612"
        android:layout_marginStart="0px"
        android:layout_marginTop="0px"
        tools:context=".ScenePatternMenuFragment">
        <com.iauto.uicontrol.ImageBase
            android:layout_width="@dimen/x_px_1488"
            android:layout_height="@dimen/x_px_88"
            app:imageString = "@drawable/com_titleline_1488"/>
        <com.iauto.uicontrol.TextBase
            android:layout_width="@dimen/x_px_532"
            android:layout_height="@dimen/x_px_54"
            android:layout_marginStart="@dimen/x_px_478"
            android:layout_marginTop="@dimen/y_px_17"
            android:textColor="@color/color_text_title"
            android:textSize="@dimen/font_size_36"
            android:gravity="center_vertical|center_horizontal"
            android:text="@string/string_scene_pattern_title"/>
        <com.iauto.uicontrol.ImageBase
            android:layout_width="@dimen/x_px_1400"
            android:layout_height="@dimen/x_px_524"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/y_px_88"
            app:imageString="@drawable/visualarea"/>

        <com.iauto.uicontrol.ButtonView
            android:id="@+id/relieve_stress_button"
            android:layout_width="@dimen/x_px_320"
            android:layout_height="@dimen/x_px_188"
            android:layout_marginStart="@dimen/x_px_44"
            android:layout_marginTop="@dimen/y_px_104"
            app:isBtnScaleAnim="true"
            app:btnScaleValue="0.95"
            app:hasAlphaMask="true"
            app:onCcClick="onCCMethod_MenuClick"
            app:imageString="@drawable/bg_scenmode_diastolicmode"
            android:contentDescription="@string/string_scene_pattern_menu_relieve"/>
        <com.iauto.uicontrol.TextBase
            android:layout_width="@dimen/x_px_320"
            android:layout_height="@dimen/x_px_36"
            android:layout_marginStart="@dimen/x_px_44"
            android:layout_marginTop="@dimen/y_px_308"
            android:gravity="center_horizontal|center_vertical"
            android:textColor="@color/color_text_explain"
            android:text="@string/string_scene_pattern_menu_relieve"
            android:textSize="@dimen/font_size_24"/>
        <com.iauto.uicontrol.ButtonView
            android:id="@+id/surprise_egg_button"
            android:layout_width="@dimen/x_px_320"
            android:layout_height="@dimen/x_px_188"
            android:layout_marginStart="@dimen/x_px_404"
            android:layout_marginTop="@dimen/y_px_104"
            app:isBtnScaleAnim="true"
            app:btnScaleValue="0.95"
            app:hasAlphaMask="true"
            app:onCcClick="onCCMethod_MenuClick"
            app:imageString="@drawable/bg_scenmode_surprised"
            android:contentDescription="@string/string_scene_pattern_menu_surprise"/>
        <com.iauto.uicontrol.TextBase
            android:layout_width="@dimen/x_px_320"
            android:layout_height="@dimen/x_px_36"
            android:layout_marginStart="@dimen/x_px_404"
            android:layout_marginTop="@dimen/y_px_308"
            android:gravity="center_horizontal|center_vertical"
            android:textColor="@color/color_text_explain"
            android:text="@string/string_scene_pattern_menu_surprise"
            android:textSize="@dimen/font_size_24"/>
    </RelativeLayout>
</layout>