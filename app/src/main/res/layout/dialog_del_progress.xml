<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/x_px_1488"
    android:layout_height="@dimen/y_px_612"
    android:layout_centerInParent="true">

    <LinearLayout
        android:layout_width="@dimen/x_px_760"
        android:layout_height="@dimen/y_px_400"
        android:orientation="vertical"
        android:gravity="center_horizontal"
        android:paddingStart="@dimen/x_px_108"
        android:paddingEnd="@dimen/x_px_108"
        android:paddingTop="@dimen/x_px_68"
        android:paddingBottom="@dimen/x_px_60"
        android:background="@drawable/com_dialogs2">

        <!-- message -->
        <com.iauto.uicontrol.TextBase
            android:id="@+id/del_info_text"
            android:layout_width="@dimen/x_px_544"
            android:layout_height="@dimen/y_px_42"
            android:gravity="center"
            android:textColor="@color/color_text_popup_text"
            android:textSize="@dimen/font_size_28" />

        <com.iauto.uicontrol.ProgressBar
            android:id="@+id/del_progress_bar"
            android:layout_marginTop="@dimen/y_px_36"
            android:layout_width="@dimen/x_px_544"
            android:layout_height="@dimen/y_px_8"
            app:isControlLock="true"
            app:initialValue="0"
            >
            <com.iauto.uicontrol.ImageBase
                android:layout_width="@dimen/x_px_544"
                android:layout_height="@dimen/y_px_8"
                android:tag = "Background"
                app:imageString="@drawable/bg_setting_progressbg"></com.iauto.uicontrol.ImageBase>
            <com.iauto.uicontrol.ImageBase
                android:layout_width="@dimen/x_px_5"
                android:layout_height="@dimen/y_px_8"
                android:tag = "Progress"
                app:imageString="@drawable/bg_setting_progress"></com.iauto.uicontrol.ImageBase>
        </com.iauto.uicontrol.ProgressBar>

        <android.widget.Button
            android:id="@+id/dialog_stop_button"
            android:layout_width="@dimen/x_px_232"
            android:layout_height="@dimen/y_px_96"
            android:layout_marginTop="@dimen/y_px_60"
            android:background="@drawable/btn56_com_amber_n"
            android:textSize="@dimen/font_size_28"
            android:gravity="center"
            android:textColor="@color/color_text_popup_button"></android.widget.Button>

    </LinearLayout>
</LinearLayout>
