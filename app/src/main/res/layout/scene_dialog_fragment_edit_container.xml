<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="vm"
            type="com.dfl.smartscene.ui.edit.container.SceneEditContainerViewModel" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            style="@style/BigDialogThemeP42R"
            android:layout_gravity="center_horizontal">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="0dp"
                android:layout_height="@dimen/px_104"
                android:gravity="center"
                android:text="@{vm.titleName}"
                android:textColor="@color/scene_color_text_1"
                android:textFontWeight="500"
                android:textSize="@dimen/scene_text_size_p42r_h3"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="标题" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/btn_close"
                android:layout_width="@dimen/px_40"
                android:layout_height="@dimen/px_40"
                android:layout_marginStart="@dimen/px_60"
                android:layout_marginTop="@dimen/px_32"
                android:contentDescription="@string/scene_text_common_close"
                android:src="@drawable/scene_icon_common_close"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!--左边一级菜单-->
            <com.dfl.android.animationlib.scrollView.OverScrollView
                android:id="@+id/rv_edit_item_list"
                style="@style/OverScrollViewTheme1A"
                android:layout_width="@dimen/px_288"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/px_24"
                android:orientation="vertical"
                android:overScrollMode="never"
                android:scrollbars="none"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_title">

                <com.dfl.android.animationlib.TabSwitchView
                    android:id="@+id/tab_first_menu"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:itemWidth="@dimen/px_288"
                    app:itemHeight="@dimen/px_96"
                    android:background="@color/transparent"

                    app:iconHeight="@dimen/px_36"
                    app:iconMarginStart="@dimen/px_42"
                    app:iconWidth="@dimen/px_36"
                    app:isHorizontal="false"
                    app:itemGap="0dp"

                    app:itemSelectedBg="@drawable/scene_shape_white_bg_r12"
                    app:itemTotality="5"
                    app:textColor="@color/nissan_option_bt_normal_text"
                    app:textColorSelected="@color/nissan_option_bt_clicked_text"
                    app:textMarginStart="@dimen/px_108"
                    app:textSize="@dimen/scene_text_size_p42r_h3" />
            </com.dfl.android.animationlib.scrollView.OverScrollView>


            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/vp_fragment_container"
                viewPager_data="@{vm.categoryList}"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/px_28"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/rv_edit_item_list"
                app:layout_constraintTop_toBottomOf="@id/tv_title" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>
</layout>