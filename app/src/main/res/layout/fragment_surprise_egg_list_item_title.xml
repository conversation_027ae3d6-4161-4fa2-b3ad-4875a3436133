<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <variable name="vm" type="com.dfl.smartscene.ccs.viewmodel.SurpriseEggListItemVM"/>
    </data>
    <com.iauto.uicontrol.ListItemBase
        android:layout_height = "@dimen/x_px_64"
        android:layout_width = "@dimen/x_px_1400">
        <com.iauto.uicontrol.ListItemUnit
            android:layout_marginStart="0px"
            android:layout_marginTop="0px"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            >
            <com.iauto.uicontrol.TextBase
                android:layout_marginStart="0px"
                android:layout_marginTop="@dimen/y_px_8"
                android:layout_width="@dimen/x_px_1400"
                android:layout_height="@dimen/y_px_48"
                android:textSize="@dimen/font_size_32"
                android:gravity="start|center_vertical"
                android:textColor="@color/color_text_list_title"
                app:textString="@{vm.time}"
                >
            </com.iauto.uicontrol.TextBase>

        </com.iauto.uicontrol.ListItemUnit>
    </com.iauto.uicontrol.ListItemBase>
</layout>