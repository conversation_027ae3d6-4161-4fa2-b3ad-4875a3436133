package com.dfl.scenecontrol

import kotlinx.coroutines.*
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import org.junit.Test
import java.lang.ref.WeakReference
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2023/03/23
 * desc:
 * version:1.0
 */
class StringDemo {
    @Test
    fun testString() {
        val value = 0xa
        println(value.toString())
    }

    @Test
    fun testBase64() {
        //val timeBean = TimeBean(10,11,12)
        //val timeJson = Gson().toJson(timeBean)
        //        val base64EncodeString= Base64.encodeToString(timeJson.toByteArray(),Base64.NO_WRAP)
        //        println("base64-encode:$base64EncodeString")
        //        val base64DecodeString:String = String(timeJson.toByteArray())
        //        println("base64-decode:$base64DecodeString")
    }

    @Test
    fun mutexTest1() {
        var counter = 0
        var mutex = Mutex()
        repeat(10) {
            CoroutineScope(Dispatchers.IO).launch {
                mutex.withLock {
                    suspendCoroutine<Int> {
                        println("GlobalScope suspendCoroutine" + (counter++))
                        Thread.sleep(100L)
                        it.resume(counter)
                        println("GlobalScope suspendCoroutine" + (counter))
                    }
                    println("GlobalScope" + (counter))
                }
            }
        }
        repeat(10) {
            CoroutineScope(Dispatchers.IO).launch {
                mutex.withLock {
                    println("CoroutineScope suspendCoroutine" + (counter++))
                    Thread.sleep(100L)
                    println("CoroutineScope suspendCoroutine" + (counter))
                }
                println("CoroutineScope" + (counter))
            }
        }

        // 防止主线程直接结束
        Thread.sleep(3000)
    }

    @Test
    fun mutexTest2() {
        var counter = 0
        val mutex = Mutex()
        var weakReference: WeakReference<CancellableContinuation<Int>>? = null
        CoroutineScope(Dispatchers.IO).launch {
            try {
                withTimeout(100L) {
                    mutex.withLock {
                        suspendCancellableCoroutine<Int> {
                            weakReference = WeakReference(it)
                            println(" suspendCoroutine" + (++counter) + (weakReference == null))
                            launch {
                                delay(300L)
                                it.resume(counter)
                                println(" suspendCoroutine end" + (counter))
                            }
                        }
                        println("mutex" + (counter))
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                println("catch" + (weakReference == null))
                weakReference?.let {
                    it.get()?.cancel(Throwable("deInit called"))
                    it.clear()
                }
            }

        }
        println("main" + (counter))


        // 防止主线程直接结束
        Thread.sleep(1000)
    }
}