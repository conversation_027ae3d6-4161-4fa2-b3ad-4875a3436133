package com.dfl.smartscene

import org.junit.Test
import org.junit.Assert.*

/**
 * 测试 LanYouRequest 类的初始化
 * 用于验证 VerifyError 问题是否已解决
 */
class LanYouRequestTest {

    @Test
    fun testLanYouRequestClassLoading() {
        try {
            // 尝试加载 LanYouRequest 类
            val clazz = Class.forName("com.szlanyou.gwsdk.LanYouRequest")
            assertNotNull("LanYouRequest 类应该能够正常加载", clazz)
            
            // 检查类的构造函数
            val constructors = clazz.constructors
            assertTrue("LanYouRequest 应该有构造函数", constructors.isNotEmpty())
            
            println("LanYouRequest 类加载成功，构造函数数量: ${constructors.size}")
            
        } catch (e: ClassNotFoundException) {
            fail("无法找到 LanYouRequest 类: ${e.message}")
        } catch (e: VerifyError) {
            fail("LanYouRequest 类验证失败: ${e.message}")
        } catch (e: Exception) {
            fail("加载 LanYouRequest 类时发生异常: ${e.message}")
        }
    }
    
    @Test
    fun testLanYouRequestStaticMethods() {
        try {
            val clazz = Class.forName("com.szlanyou.gwsdk.LanYouRequest")
            
            // 检查是否有 init 静态方法
            val methods = clazz.methods
            val initMethods = methods.filter { it.name == "init" }
            assertTrue("LanYouRequest 应该有 init 方法", initMethods.isNotEmpty())
            
            // 检查是否有 deInit 静态方法
            val deInitMethods = methods.filter { it.name == "deInit" }
            assertTrue("LanYouRequest 应该有 deInit 方法", deInitMethods.isNotEmpty())
            
            println("LanYouRequest 静态方法检查通过")
            
        } catch (e: Exception) {
            fail("检查 LanYouRequest 静态方法时发生异常: ${e.message}")
        }
    }
}
