package com.dfl.smartscene

import android.app.Application
import com.dfl.smartscene.ccs.wrapper.LanYouRequestWrapper
import com.szlanyou.gwsdk.AppInfo
import com.szlanyou.gwsdk.RequestCallback
import com.szlanyou.gwsdk.RequestEnvironment
import org.junit.Test
import org.junit.Assert.*
import org.mockito.Mockito.*
import java.util.HashMap

/**
 * 测试 LanYouRequestWrapper 类的功能
 * 用于验证 VerifyError 问题的解决方案
 */
class LanYouRequestWrapperTest {

    @Test
    fun testSafeRequestWithoutInit() {
        // 重置状态
        LanYouRequestWrapper.resetState()
        
        val params = HashMap<String, Object>()
        params["api"] = "test.api"
        
        val callback = mock(RequestCallback::class.java)
        
        // 在未初始化的情况下调用 safeRequest
        val result = LanYouRequestWrapper.safeRequest(params, callback)
        
        // 应该返回 false
        assertFalse("未初始化时应该返回 false", result)
        
        // 应该调用 onFail
        verify(callback).onFail("LanYouRequest not initialized")
    }
    
    @Test
    fun testSafeRequestWithVerifyError() {
        // 重置状态
        LanYouRequestWrapper.resetState()
        
        // 模拟 VerifyError 状态
        // 这里我们无法直接设置 hasVerifyError，但可以通过其他方式测试
        
        val params = HashMap<String, Object>()
        params["api"] = "test.api"
        
        val callback = mock(RequestCallback::class.java)
        
        // 检查初始状态
        assertFalse("初始状态应该未初始化", LanYouRequestWrapper.isInitialized())
        assertFalse("初始状态应该没有 VerifyError", LanYouRequestWrapper.hasVerifyError())
    }
    
    @Test
    fun testSafeInitWithNullApplication() {
        // 重置状态
        LanYouRequestWrapper.resetState()
        
        val appInfo = mock(AppInfo::class.java)
        
        // 使用 null application 调用 safeInit
        val result = LanYouRequestWrapper.safeInit(null, appInfo, RequestEnvironment.TEST)
        
        // 应该返回 false（因为会抛出异常）
        assertFalse("使用 null application 应该返回 false", result)
    }
    
    @Test
    fun testSafeDeInitWithoutInit() {
        // 重置状态
        LanYouRequestWrapper.resetState()
        
        // 在未初始化的情况下调用 safeDeInit
        // 这应该不会抛出异常
        LanYouRequestWrapper.safeDeInit()
        
        // 状态应该保持未初始化
        assertFalse("未初始化状态下调用 deInit 后应该仍然未初始化", LanYouRequestWrapper.isInitialized())
    }
    
    @Test
    fun testResetState() {
        // 重置状态
        LanYouRequestWrapper.resetState()
        
        // 验证状态被重置
        assertFalse("重置后应该未初始化", LanYouRequestWrapper.isInitialized())
        assertFalse("重置后应该没有 VerifyError", LanYouRequestWrapper.hasVerifyError())
    }
    
    @Test
    fun testSafeRequestOverloads() {
        // 重置状态
        LanYouRequestWrapper.resetState()
        
        val params = HashMap<String, Object>()
        params["api"] = "test.api"
        
        val callback = mock(RequestCallback::class.java)
        
        // 测试不带 needSign 参数的重载方法
        val result1 = LanYouRequestWrapper.safeRequest(params, callback)
        assertFalse("未初始化时应该返回 false", result1)
        
        // 测试带 needSign 参数的方法
        val result2 = LanYouRequestWrapper.safeRequest(params, callback, true)
        assertFalse("未初始化时应该返回 false", result2)
        
        // 验证两次都调用了 onFail
        verify(callback, times(2)).onFail("LanYouRequest not initialized")
    }
}
