# Keep LanYouRequest and related classes in main dex to avoid VerifyError
com/szlanyou/gwsdk/LanYouRequest.class
com/szlanyou/gwsdk/RequestCallback.class
com/szlanyou/gwsdk/AppInfo.class
com/szlanyou/gwsdk/RequestEnvironment.class

# Keep Application classes in main dex
com/dfl/smartscene/application/SceneApplication.class
com/dfl/android/common/base/BaseApp.class

# Keep MultiDex support classes
androidx/multidex/MultiDex.class
androidx/multidex/MultiDexApplication.class

# Keep HttpWrapper to avoid issues with LanYouRequest calls
com/dfl/smartscene/ccs/wrapper/HttpWrapper.class
